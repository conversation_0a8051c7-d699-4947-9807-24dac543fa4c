{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"columnType\"];\nimport * as React from 'react';\nimport { INTERNAL_COL_DEFINE } from \"./utils/legacyUtil\";\nimport { useContext } from '@rc-component/context';\nimport TableContext from \"./context/TableContext\";\nfunction ColGroup(_ref) {\n  var colWidths = _ref.colWidths,\n    columns = _ref.columns,\n    columCount = _ref.columCount;\n  var _useContext = useContext(TableContext, ['tableLayout']),\n    tableLayout = _useContext.tableLayout;\n  var cols = [];\n  var len = columCount || columns.length;\n\n  // Only insert col with width & additional props\n  // Skip if rest col do not have any useful info\n  var mustInsert = false;\n  for (var i = len - 1; i >= 0; i -= 1) {\n    var width = colWidths[i];\n    var column = columns && columns[i];\n    var additionalProps = void 0;\n    var minWidth = void 0;\n    if (column) {\n      additionalProps = column[INTERNAL_COL_DEFINE];\n\n      // fixed will cause layout problems\n      if (tableLayout === 'auto') {\n        minWidth = column.minWidth;\n      }\n    }\n    if (width || minWidth || additionalProps || mustInsert) {\n      var _ref2 = additionalProps || {},\n        columnType = _ref2.columnType,\n        restAdditionalProps = _objectWithoutProperties(_ref2, _excluded);\n      cols.unshift(/*#__PURE__*/React.createElement(\"col\", _extends({\n        key: i,\n        style: {\n          width: width,\n          minWidth: minWidth\n        }\n      }, restAdditionalProps)));\n      mustInsert = true;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"colgroup\", null, cols);\n}\nexport default ColGroup;", "map": {"version": 3, "names": ["_extends", "_objectWithoutProperties", "_excluded", "React", "INTERNAL_COL_DEFINE", "useContext", "TableContext", "ColGroup", "_ref", "col<PERSON><PERSON><PERSON>", "columns", "columCount", "_useContext", "tableLayout", "cols", "len", "length", "mustInsert", "i", "width", "column", "additionalProps", "min<PERSON><PERSON><PERSON>", "_ref2", "columnType", "restAdditionalProps", "unshift", "createElement", "key", "style"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-table/es/ColGroup.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nvar _excluded = [\"columnType\"];\nimport * as React from 'react';\nimport { INTERNAL_COL_DEFINE } from \"./utils/legacyUtil\";\nimport { useContext } from '@rc-component/context';\nimport TableContext from \"./context/TableContext\";\nfunction ColGroup(_ref) {\n  var colWidths = _ref.colWidths,\n    columns = _ref.columns,\n    columCount = _ref.columCount;\n  var _useContext = useContext(TableContext, ['tableLayout']),\n    tableLayout = _useContext.tableLayout;\n  var cols = [];\n  var len = columCount || columns.length;\n\n  // Only insert col with width & additional props\n  // Skip if rest col do not have any useful info\n  var mustInsert = false;\n  for (var i = len - 1; i >= 0; i -= 1) {\n    var width = colWidths[i];\n    var column = columns && columns[i];\n    var additionalProps = void 0;\n    var minWidth = void 0;\n    if (column) {\n      additionalProps = column[INTERNAL_COL_DEFINE];\n\n      // fixed will cause layout problems\n      if (tableLayout === 'auto') {\n        minWidth = column.minWidth;\n      }\n    }\n    if (width || minWidth || additionalProps || mustInsert) {\n      var _ref2 = additionalProps || {},\n        columnType = _ref2.columnType,\n        restAdditionalProps = _objectWithoutProperties(_ref2, _excluded);\n      cols.unshift( /*#__PURE__*/React.createElement(\"col\", _extends({\n        key: i,\n        style: {\n          width: width,\n          minWidth: minWidth\n        }\n      }, restAdditionalProps)));\n      mustInsert = true;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"colgroup\", null, cols);\n}\nexport default ColGroup;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,IAAIC,SAAS,GAAG,CAAC,YAAY,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,QAAQ,oBAAoB;AACxD,SAASC,UAAU,QAAQ,uBAAuB;AAClD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,UAAU,GAAGH,IAAI,CAACG,UAAU;EAC9B,IAAIC,WAAW,GAAGP,UAAU,CAACC,YAAY,EAAE,CAAC,aAAa,CAAC,CAAC;IACzDO,WAAW,GAAGD,WAAW,CAACC,WAAW;EACvC,IAAIC,IAAI,GAAG,EAAE;EACb,IAAIC,GAAG,GAAGJ,UAAU,IAAID,OAAO,CAACM,MAAM;;EAEtC;EACA;EACA,IAAIC,UAAU,GAAG,KAAK;EACtB,KAAK,IAAIC,CAAC,GAAGH,GAAG,GAAG,CAAC,EAAEG,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;IACpC,IAAIC,KAAK,GAAGV,SAAS,CAACS,CAAC,CAAC;IACxB,IAAIE,MAAM,GAAGV,OAAO,IAAIA,OAAO,CAACQ,CAAC,CAAC;IAClC,IAAIG,eAAe,GAAG,KAAK,CAAC;IAC5B,IAAIC,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAIF,MAAM,EAAE;MACVC,eAAe,GAAGD,MAAM,CAAChB,mBAAmB,CAAC;;MAE7C;MACA,IAAIS,WAAW,KAAK,MAAM,EAAE;QAC1BS,QAAQ,GAAGF,MAAM,CAACE,QAAQ;MAC5B;IACF;IACA,IAAIH,KAAK,IAAIG,QAAQ,IAAID,eAAe,IAAIJ,UAAU,EAAE;MACtD,IAAIM,KAAK,GAAGF,eAAe,IAAI,CAAC,CAAC;QAC/BG,UAAU,GAAGD,KAAK,CAACC,UAAU;QAC7BC,mBAAmB,GAAGxB,wBAAwB,CAACsB,KAAK,EAAErB,SAAS,CAAC;MAClEY,IAAI,CAACY,OAAO,CAAE,aAAavB,KAAK,CAACwB,aAAa,CAAC,KAAK,EAAE3B,QAAQ,CAAC;QAC7D4B,GAAG,EAAEV,CAAC;QACNW,KAAK,EAAE;UACLV,KAAK,EAAEA,KAAK;UACZG,QAAQ,EAAEA;QACZ;MACF,CAAC,EAAEG,mBAAmB,CAAC,CAAC,CAAC;MACzBR,UAAU,GAAG,IAAI;IACnB;EACF;EACA,OAAO,aAAad,KAAK,CAACwB,aAAa,CAAC,UAAU,EAAE,IAAI,EAAEb,IAAI,CAAC;AACjE;AACA,eAAeP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}