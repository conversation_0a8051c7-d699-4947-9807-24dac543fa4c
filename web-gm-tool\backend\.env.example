# Web GM Tool Backend 环境配置

# 服务器配置
PORT=3001
HOST=0.0.0.0
NODE_ENV=development

# CORS配置
CORS_ORIGIN=http://localhost:3000

# 游戏服务器配置
GAME_HOST=localhost
GAME_PORT=8888

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log

# 安全配置
JWT_SECRET=your-jwt-secret-key
SESSION_SECRET=your-session-secret

# 数据库配置（如果需要）
# DB_HOST=localhost
# DB_PORT=3306
# DB_NAME=gm_tool
# DB_USER=root
# DB_PASSWORD=password

# Redis配置（如果需要）
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=

# 其他配置
MAX_CONNECTIONS=100
RECONNECT_INTERVAL=5000
MAX_RECONNECT_ATTEMPTS=10
