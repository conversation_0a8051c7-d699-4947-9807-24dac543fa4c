import React, { useState, useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Layout, message } from 'antd';
import { io } from 'socket.io-client';

import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import { SocketContext } from './contexts/SocketContext';
import { AuthContext } from './contexts/AuthContext';
import './App.css';

const { Content } = Layout;

function App() {
  const [socket, setSocket] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userInfo, setUserInfo] = useState(null);
  const [gameConnected, setGameConnected] = useState(false);

  useEffect(() => {
    // 创建Socket连接
    const newSocket = io(process.env.REACT_APP_BACKEND_URL || 'http://localhost:3001', {
      transports: ['websocket', 'polling']
    });

    // 连接成功
    newSocket.on('connect', () => {
      console.log('WebSocket连接成功');
      message.success('连接到服务器成功');
    });

    // 连接断开
    newSocket.on('disconnect', () => {
      console.log('WebSocket连接断开');
      message.warning('与服务器连接断开');
      setGameConnected(false);
    });

    // 登录结果
    newSocket.on('loginResult', (data) => {
      if (data.success) {
        message.success(data.message);
        // 注意：这里只是发送了登录请求，真正的认证需要等待游戏服务器响应
      } else {
        message.error(data.message);
      }
    });

    // 游戏数据包
    newSocket.on('gamePacket', (data) => {
      console.log('收到游戏数据包:', data);
      // 这里可以根据数据包内容判断登录是否成功
      // 简化处理：假设收到任何数据包都表示连接成功
      if (!isAuthenticated) {
        setIsAuthenticated(true);
        setGameConnected(true);
        message.success('登录成功！');
      }
    });

    // 游戏连接断开
    newSocket.on('gameDisconnected', (data) => {
      message.warning(data.message);
      setGameConnected(false);
    });

    // 游戏连接错误
    newSocket.on('gameError', (data) => {
      message.error(data.message);
      setGameConnected(false);
    });

    // 充值结果
    newSocket.on('rechargeResult', (data) => {
      if (data.success) {
        message.success(data.message);
      } else {
        message.error(data.message);
      }
    });

    // 账号管理结果
    newSocket.on('accountManageResult', (data) => {
      if (data.success) {
        message.success(data.message);
      } else {
        message.error(data.message);
      }
    });

    // 装备发送结果
    newSocket.on('equipmentResult', (data) => {
      if (data.success) {
        message.success(data.message);
      } else {
        message.error(data.message);
      }
    });

    // 活动结果
    newSocket.on('activityResult', (data) => {
      if (data.success) {
        message.success(data.message);
      } else {
        message.error(data.message);
      }
    });

    setSocket(newSocket);

    // 清理函数
    return () => {
      newSocket.close();
    };
  }, [isAuthenticated]);

  // 登录函数
  const handleLogin = (loginData) => {
    if (socket) {
      socket.emit('login', loginData);
      setUserInfo({
        username: loginData.username,
        gameHost: loginData.gameHost,
        gamePort: loginData.gamePort
      });
    }
  };

  // 登出函数
  const handleLogout = () => {
    if (socket) {
      socket.emit('disconnectGame');
    }
    setIsAuthenticated(false);
    setUserInfo(null);
    setGameConnected(false);
    message.info('已退出登录');
  };

  const authContextValue = {
    isAuthenticated,
    userInfo,
    gameConnected,
    login: handleLogin,
    logout: handleLogout
  };

  return (
    <div className="App">
      <AuthContext.Provider value={authContextValue}>
        <SocketContext.Provider value={socket}>
          <Layout style={{ minHeight: '100vh' }}>
            <Content>
              <Routes>
                <Route 
                  path="/login" 
                  element={
                    isAuthenticated ? 
                    <Navigate to="/dashboard" replace /> : 
                    <Login />
                  } 
                />
                <Route 
                  path="/dashboard" 
                  element={
                    isAuthenticated ? 
                    <Dashboard /> : 
                    <Navigate to="/login" replace />
                  } 
                />
                <Route 
                  path="/" 
                  element={<Navigate to={isAuthenticated ? "/dashboard" : "/login"} replace />} 
                />
              </Routes>
            </Content>
          </Layout>
        </SocketContext.Provider>
      </AuthContext.Provider>
    </div>
  );
}

export default App;
