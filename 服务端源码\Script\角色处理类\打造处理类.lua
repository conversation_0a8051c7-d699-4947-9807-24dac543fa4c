
local 打造处理类 = class()
local floor=math.floor
local random = 取随机数
local remove = table.remove
local 附加上限={
[60]={27,22}
,[80]={30,25}
,[90]={33,28}
,[100]={36,31}
,[110]={39,34}
,[120]={42,37}
,[130]={45,40}
,[140]={48,43}
,[150]={51,46}
,[160]={54,49}
}

-- local 装备属性上限 = {
-- 	[武器] = {
-- 		[1] = {{10, 45, 48},{20, 80, 104},{30, 115, 149}},
-- 		[2] = {{10, 45, 48},{20, 80, 104},{30, 115, 149}}
-- 	}
-- }

local 附加范围={"力量","敏捷","体质","耐力","魔力"}

local function 绑定等级物品()
	local n = {}
	n[1] = {"红缨枪","曲尖枪","锯齿矛","乌金三叉戟","火焰枪","墨杆金钩","玄铁矛","金蛇信","丈八点钢矛","暗夜","梨花","梨花","刑天之逆","五虎断魂","飞龙在天","天龙破城","弑皇"}
	n[2] = {"青铜斧","开山斧","双面斧","双弦钺","精钢禅钺","黄金钺","乌金鬼头镰","狂魔镰","恶龙之齿","破魄","肃魂","无敌","五丁开山","元神禁锢","护法灭魔","碧血干戚","裂天"}
	n[3] = {"青铜短剑","铁齿剑","吴越剑","青锋剑","龙泉剑","黄金剑","游龙剑","北斗七星剑","碧玉剑","鱼肠","倚天","湛卢","魏武青虹","灵犀神剑","四法青云","霜冷九州","擒龙"}
	n[4] = {"双短剑","镔铁双剑","龙凤双剑","竹节双剑","狼牙双剑","鱼骨双剑","赤焰双剑","墨玉双剑","梅花双剑","阴阳","月光双剑","灵蛇","金龙双剪","连理双树","祖龙对剑","紫电青霜","浮犀"}
	n[5] = {"五色缎带","幻彩银纱","金丝彩带","无极丝","天蚕丝带","云龙绸带","七彩罗刹","缚神绫","九天仙绫","彩虹","流云","碧波","秋水落霞","晃金仙绳","此最相思","揽月摘星","九霄"}
	n[6] = {"铁爪","天狼爪","幽冥鬼爪","青龙牙","勾魂爪","玄冰刺","青刚刺","华光刺","龙鳞刺","撕天","毒牙","胭脂","九阴勾魂","雪蚕之刺","贵霜之牙","忘川三途","离钩"}
	n[7] = {"折扇","铁骨扇","精钢扇","铁面扇","百折扇","劈水扇","神火扇","阴风扇","风云雷电","太极","玉龙","秋风","画龙点睛","秋水人家","逍遥江湖","浩气长舒","星瀚"}
	n[8] = {"细木棒","金丝魔棒","玉如意","点金棒","云龙棒","幽路引魂","满天星","水晶棒","日月光华","沧海","红莲","盘龙","降魔玉杵","青藤玉树","墨玉骷髅","丝萝乔木","醍醐"}
	n[9] = {"松木锤","镔铁锤","八棱金瓜","狼牙锤","烈焰锤","破甲战锤","震天锤","巨灵神锤","天崩地裂","八卦","鬼牙","雷神","混元金锤","九瓣莲花","鬼王蚀日","狂澜碎岳","碎寂"}
	n[10] = {"牛皮鞭","牛筋鞭","乌龙鞭","钢结鞭","蛇骨鞭","玉竹金铃","青藤柳叶鞭","雷鸣嗜血鞭","混元金钩","龙筋","百花","吹雪","游龙惊鸿","仙人指路","血之刺藤","牧云清歌","霜陨"}
	n[11] = {"黄铜圈","精钢日月圈","离情环","金刺轮","风火圈","赤炎环","蛇形月","子母双月","斜月狼牙","如意","乾坤","月光双环","别情离恨","金玉双环","九天金线","无关风月","朝夕"}
	n[12] = {"柳叶刀","苗刀","夜魔弯刀","金背大砍刀","雁翅刀","破天宝刀","狼牙刀","龙鳞宝刀","黑炎魔刀","冷月","屠龙","血刃","偃月青龙","晓风残月","斩妖泣血","业火三灾","鸣鸿"}
	n[13] = {"曲柳杖","红木杖","白椴杖","墨铁拐","玄铁牛角杖","鹰眼法杖","腾云杖","引魂杖","碧玺杖","业焰","玉辉","鹿鸣","庄周梦蝶","凤翼流珠","雪蟒霜寒","碧海潮生","弦月"}
	n[14] = {"硬木弓","铁胆弓","紫檀弓","宝雕长弓","錾金宝弓","玉腰弯弓","连珠神弓","游鱼戏珠","灵犀望月","非攻","幽篁","百鬼","冥火薄天","龙鸣寒水","太极流光","九霄风雷","若木"}
	n[15] = {"琉璃珠","水晶珠","珍宝珠","翡翠珠","莲华珠","夜灵珠","如意宝珠","沧海明珠","无量玉璧","离火","飞星","月华","回风舞雪","紫金葫芦","裂云啸日","云雷万里","赤明"}
	n[16] = {"钝铁重剑","桃印铁刃","赭石巨剑","壁玉长铗","青铜古剑","金错巨刃","惊涛雪","醉浮生","沉戟天戊","鸦九","昆吾","弦歌","墨骨枯麟","腾蛇郁刃","秋水澄流","百辟镇魂","长息"}
	n[18] = {"素纸灯","竹骨灯","红灯笼","鲤鱼灯","芙蓉花灯","如意宫灯","玲珑盏","玉兔盏","冰心盏","蟠龙","云鹤","风荷","金风玉露","凰火燎原","月露清愁","夭桃秾李","荒尘"}
	n[17] = {"油纸伞","红罗伞","紫竹伞","锦绣椎","幽兰帐","琳琅盖","孔雀羽","金刚伞","落梅伞","鬼骨","云梦","枕霞","碧火琉璃","雪羽穿云","月影星痕","浮生归梦","晴雪"}
	n[19] = {{"方巾","簪子"},{"布帽","玉钗",},{"面具","梅花簪子"},{"纶巾","珍珠头带"},{"缨络丝带","凤头钗"},{"羊角盔","媚狐头饰"},{"水晶帽","玉女发冠"},{"乾坤帽","魔女发冠"},{"黑魔冠","七彩花环"},{"白玉龙冠","凤翅金翎"},{"水晶夔帽","寒雉霜蚕"},{"翡翠曜冠","曜月嵌星"},{"金丝黑玉冠","郁金流苏簪"},{"白玉琉璃冠","玉翼附蝉翎"},{"兽鬼珐琅面","鸾羽九凤冠"},{"紫金磐龙冠","金珰紫焰冠"},{"浑天玄火盔","乾元鸣凤冕"}}
	n[20] = {"护身符","五色飞石","珍珠链",{"骷髅吊坠","苍魂珠"},{"江湖夜雨","九宫坠"},{"荧光坠子","高速之星"},{"风月宝链","八卦坠"},{"碧水青龙","鬼牙攫魂"},{"万里卷云","疾风之铃"},"七彩玲珑","黄玉琉佩","鸾飞凤舞","衔珠金凤佩","七璜珠玉佩","鎏金点翠佩","紫金碧玺佩","落霞陨星坠"}
	n[21] = {{"布裙","布衣"},{"丝绸长裙","皮衣"},{"五彩裙","鳞甲"},{"龙鳞羽衣","锁子甲"},{"天香披肩","紧身衣"},{"金缕羽衣","钢甲"},{"霓裳羽衣","夜魔披风"},{"流云素裙","龙骨甲"},{"七宝天衣","死亡斗篷"},{"飞天羽衣","神谕披风"},{"穰花翠裙","珊瑚玉衣"},{"金蚕丝裙","金蚕披风"},{"紫香金乌裙","乾坤护心甲"},{"碧霞彩云衣","蝉翼金丝甲"},{"金丝蝉翼衫","金丝鱼鳞甲"},{"五彩凤翅衣","紫金磐龙甲"},{"鎏金浣月衣","混元一气甲"}}
	n[22] = {"腰带","缎带","银腰带",{"水晶腰带","玉树腰带"},{"琥珀腰链","白面狼牙"},{"乱牙咬","魔童大牙"},{"攫魂铃","双魂引"},{"兽王腰带","百窜云"},{"八卦锻带","圣王坠"},"幻彩玉带","珠翠玉环","金蟾含珠","乾坤紫玉带","琉璃寒玉带","蝉翼鱼佩带","磐龙凤翔带","紫霄云芒带"}
	n[23] = {"布鞋","牛皮靴","马靴","侠客履","神行靴","绿靴","追星踏月","九州履","万里追云履","踏雪无痕","平步青云","追云逐电","乾坤天罡履","七星逐月靴","碧霞流云履","金丝逐日履","辟尘分光履"}
	n[24] = {"竹编护腕","皮腕","针腕","骨镯","青铜护腕","玛瑙护腕","琉璃护腕","镂空银镯","笼玉镯","嵌宝金腕","玳瑁护腕","七星宝腕","缚龙筋","凤翎护腕","织锦彩带","冰蚕丝带"}
	n[25] = {"竹编脖环","钢圈","荆棘环","骨环","青铜颈环","玛瑙石环","琉璃环","九曲环","笼玉环","嵌宝金环","玳瑁环","七星宝环","缚龙圈","鸾尾环","织锦颈圈","冰蚕丝圈"}
	n[26] = {"皮甲","皮甲","刺甲","骨排甲","青铜披甲","玛瑙软甲","琉璃罩甲","连环铠甲","笼玉甲","嵌宝金甲","玳瑁衣","七星宝甲","缚龙甲","凤凰彩衣","织锦软褡","冰蚕织甲"}
	return n
end

local function 绑定装备物品()
	local n = {}
	n[1] = "弑皇"
	n[2] = "裂天"
	n[3] = "擒龙"
	n[4] = "浮犀"
	n[5] = "九霄"
	n[6] = "离钩"
	n[7] = "星瀚"
	n[8] = "醍醐"
	n[9] = "碎寂"
	n[10] = "霜陨"
	n[11] = "朝夕"
	n[12] = "鸣鸿"
	n[13] = "弦月"
	n[14] = "若木"
	n[15] = "赤明"
	n[16] = "长息"
	n[17] = "晴雪"
	n[18] = "荒尘"
	n[19] = "浑天玄火盔"
	n[20] = "乾元鸣凤冕"
	n[21] = "落霞陨星坠"
	n[22] = "鎏金浣月衣"
	n[23] = "混元一气甲"
	n[24] = "紫霄云芒带"
	n[25] = "辟尘分光履"
	return n
end

local function 绑定元身物品()
	local n = {}
	n[1] = "枪·元身"
	n[2] = "斧·元身"
	n[3] = "剑·元身"
	n[4] = "双剑·元身"
	n[5] = "飘带·元身"
	n[6] = "爪刺·元身"
	n[7] = "扇·元身"
	n[8] = "魔棒·元身"
	n[9] = "锤·元身"
	n[10] = "长鞭·元身"
	n[11] = "双环·元身"
	n[12] = "刀·元身"
	n[13] = "长杖·元身"
	n[14] = "弓·元身"
	n[15] = "宝珠·元身"
	n[16] = "巨剑·元身"
	n[18] = "灯笼·元身"
	n[17] = "伞·元身"
	n[19] = "头盔·元身"
	n[20] = "冠冕·元身"
	n[21] = "挂坠·元身"
	n[22] = "纱衣·元身"
	n[23] = "坚甲·元身"
	n[24] = "束带·元身"
	n[25] = "鞋履·元身"
	return n
end

function 打造处理类:初始化()
 self.打造物品 = 绑定等级物品()
 self.装备物品 = 绑定装备物品()
 self.元身物品 = 绑定元身物品()
end

function 打造处理类:数据处理(连接id,序号,id,内容)
	if 序号==5000 then
		if 内容.打造方式.界面=="打造" and (内容.打造方式.选项=="强化打造装备" or  内容.打造方式.选项=="强化打造灵饰")and 玩家数据[id].角色:取任务(5)~=0 then
			常规提示(id,"#Y/你已经有一个打造任务在进行了")
			return
		end
		self:打造类型处理(连接id,序号,id,内容)
	end
end


function 打造处理类:打造类型处理(连接id,序号,id,内容)
	local 物品序号1 = 玩家数据[id].角色.数据.道具[内容.序列]
	local 物品序号2 = 玩家数据[id].角色.数据.道具[内容.序列1]
	local 道具1 = 玩家数据[id].道具.数据[物品序号1]
	local 道具2 = 玩家数据[id].道具.数据[物品序号2]
	local 道具属性 = 玩家数据[id].道具.数据
	local 打造方式界面 = 内容.打造方式.界面
	local 打造方式选项 = 内容.打造方式.选项
	if 道具1 == nil or 道具2 == nil then
		道具刷新(id)
		return
	end

	if 玩家数据[id].打造方式 == 1 then
		if 打造方式界面 == "打造" then
			if 打造方式选项 == "强化打造装备" or 打造方式选项 == "普通打造装备" then
				if 打造方式选项 == "强化打造装备" and 道具1.总类 == 66 or 道具1.总类 == 67 then
					self:布店强化打造160装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
				else
					常规提示(id,"只能打造元身")
				   return
				end
			elseif 打造方式选项 == "强化打造灵饰" or 打造方式选项 == "普通打造灵饰" then
				self:打造灵饰处理(id,物品序号1,物品序号2,道具1,道具2,道具属性,打造方式选项)
			elseif 打造方式选项 == "召唤兽装备" then
				self:打造召唤兽装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "镶嵌" then
			if 打造方式选项 == "宝石" then
				self:宝石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "精魄灵石" then
				self:灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "钟灵石" then
				self:钟灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "星辉石" then
				self:星辉石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "点化石" then
				self:宝宝点化装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "合成" then
			常规提示(id,"暂时未开放此功能")
			return
		elseif 打造方式界面 == "修理" then
			常规提示(id,"暂时未开放此功能")
			return
		elseif 打造方式界面 == "熔炼" then
			if 打造方式选项 == "熔炼装备" then
				self:熔炼装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "分解" then
			-- print(打造方式选项)
			if 打造方式选项 == "分解装备" then
				self:分解装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		end
	elseif 玩家数据[id].打造方式 == 2 then
		if 打造方式界面 == "打造" then
			if 打造方式选项 == "强化打造装备" or 打造方式选项 == "普通打造装备" then
				if 打造方式选项 == "强化打造装备" and 道具1.总类 == 66 or 道具1.总类 == 67 then
					self:打铁铺强化打造160装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
				else
					常规提示(id,"只能打造元身")
					return
				end
			elseif 打造方式选项 == "强化打造灵饰" or 打造方式选项 == "普通打造灵饰" then
				self:打造灵饰处理(id,物品序号1,物品序号2,道具1,道具2,道具属性,打造方式选项)
			elseif 打造方式选项 == "召唤兽装备" then
				self:打造召唤兽装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "镶嵌" then
			if 打造方式选项 == "宝石" then
				self:宝石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "精魄灵石" then
				self:灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "钟灵石" then
				self:钟灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "星辉石" then
				self:星辉石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			elseif 打造方式选项 == "点化石" then
				self:宝宝点化装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "合成" then
			常规提示(id,"暂时未开放此功能")
			return
		elseif 打造方式界面 == "修理" then
			常规提示(id,"暂时未开放此功能")
			return
		elseif 打造方式界面 == "熔炼" then
			if 打造方式选项 == "熔炼装备" then
				self:熔炼装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		elseif 打造方式界面 == "分解" then
			-- print(打造方式选项)
			if 打造方式选项 == "分解装备" then
				self:分解装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
			else
				常规提示(id,"暂时未开放此功能")
				return
			end
		end
	end
end



function 打造处理类:宝宝点化检测(总类1,分类1,总类2,分类2)
	if 总类1 == 2 and (分类1 == 7 or 分类1 == 8 or 分类1 == 9) and 总类2 == 5 and 分类2 == 5 then
		return true
	elseif 总类2 == 2 and (分类2 == 7 or 分类2 == 8 or 分类2 == 9) and 总类1 == 5 and 分类1 == 5 then
		return true
	else
		return false
	end
end


function 打造处理类:宝宝点化装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 点化石格子 = 0
	local 装备格子 = 0
	-- if (道具1.名称~="点化石" and 道具2.总类~=2 and (道具2.分类~=7 or 道具2.分类~=8 or 道具2.分类~=9)) or (道具2.名称~="点化石" and 道具1.总类~=2 and (道具1.分类~=7 or 道具1.分类~=8 or 道具1.分类~=9)) then
	-- 	常规提示(id,"#Y/镶嵌点化石材料不对，请仔细核对材料！")
	-- 	return
	-- end
	if (道具1.总类==5 and 道具2.总类 == 2 and (道具2.分类==7 or 道具2.分类==8 or 道具2.分类==9)) or (道具2.总类==5 and 道具1.总类 == 2 and (道具1.分类==7 or 道具1.分类==8 or 道具1.分类==9)) then
	else
		常规提示(id,"#Y/镶嵌点化石材料不对，请仔细核对材料！")
		return
	end
	if 道具1.总类 == 5 then
		点化石格子 = 物品序号1
		装备格子 = 物品序号2
	else
		点化石格子 = 物品序号2
		装备格子 = 物品序号1
	end

	--金钱
	if 道具属性[装备格子].级别限制 * 道具属性[装备格子].级别限制 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够点化的费用哦！")
		return
	end

	local 打造金钱 = 道具属性[装备格子].级别限制 * 道具属性[装备格子].级别限制 * 10
	local 附加状态 = "附加状态"
	local 追加法术技能表 = {"善恶有报","力劈华山","壁垒击破","惊心一剑","剑荡四方","水攻","烈火","雷击","落岩","奔雷咒","水漫金山","地狱烈火","泰山压顶","月光","夜舞倾城","上古灵符"}
	for i=1,#追加法术技能表 do
		if 道具属性[点化石格子].附带技能==追加法术技能表[i] then
			附加状态="追加法术"
		end
	end
	道具属性[装备格子].套装效果 = {附加状态, 道具属性[点化石格子].附带技能}
	常规提示(id,"#Y/点化召唤兽装备成功！")
	if 点化石格子 == 道具1 then
		物品序号1 = nil
		道具属性[点化石格子] = nil
	else
		道具2 = nil
		道具属性[点化石格子] = nil
	end
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"点化宝宝装备",1)
	玩家数据[id].角色:刷新信息()
	return
end


function 打造处理类:打造召唤兽装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	if (道具1.分类==10 and 道具2.分类==11) or (道具2.分类==10 and 道具1.分类==11) then

	else
		常规提示(id,"#Y/制造召唤兽装备的材料需要天眼珠和上古锻造图策")
		return
	end
	local 石头格子=0
	local 书铁格子=0

	if 道具1.分类==10 then
		书铁格子=物品序号1
		石头格子=物品序号2
	else
		书铁格子=物品序号2
		石头格子=物品序号1
	end

	--体力
	if 道具属性[书铁格子].级别限制 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[书铁格子].级别限制 * 道具属性[书铁格子].级别限制 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	if 道具属性[书铁格子].级别限制 > 道具属性[石头格子].级别限制 then
		常规提示(id,"上古锻造图策的等级不能高过天眼石的等级")
		return
	end

	local 类型序列=0

	if 道具属性[书铁格子].种类=="护腕" then
		类型序列=24
	elseif 道具属性[书铁格子].种类=="项圈" then
		类型序列=25
	else
		类型序列=26
	end

	local 打造金钱 = 道具属性[书铁格子].级别限制 * 道具属性[书铁格子].级别限制 * 10
	local 打造体力 = math.floor(道具属性[书铁格子].级别限制 / 10 + 10)
	local 临时序列 = math.floor((道具属性[书铁格子].级别限制-15)/10+1)
	local 临时名称 = self.打造物品[类型序列][临时序列]
	local 道具 = 物品类()
	道具:置对象(临时名称)
	道具.级别限制 = 道具属性[书铁格子].级别限制
	道具.识别码 =取唯一识别码(id)
	local 灵气=道具属性[石头格子].灵气
	local 等级=道具.级别限制
	if 道具属性[书铁格子].种类=="护腕" then
		道具.命中=math.floor(取随机数(等级*0.3+10,等级*0.6+20)+(取随机数(灵气*0.15,灵气*0.25)))
	elseif 道具属性[书铁格子].种类=="项圈" then
		道具.速度=math.floor(取随机数(等级*0.25+5,等级*0.35+10)+(取随机数(灵气*0.15,灵气*0.25)))
	else
		道具.防御=math.floor(取随机数(等级*0.65,等级)+(取随机数(灵气*0.15,灵气*0.25)))

	end

	local 附加范围={"伤害","灵力","敏捷","耐力","体质","力量","魔力","气血","魔法"}
	for n=1,3 do
		if 取随机数()<=20 then
			local 类型=附加范围[取随机数(1,#附加范围)]
			if 道具[类型]==nil then
				if 类型=="伤害" then
					道具[类型]=math.floor(取随机数(等级*0.3+10,等级*0.6+20)+(取随机数(灵气*0.15,灵气*0.25)))
				elseif 类型=="灵力" then
					道具[类型]=math.floor(取随机数(等级*0.3+10,等级*0.6+20)+(取随机数(灵气*0.15,灵气*0.25)))
				elseif 类型=="敏捷" or 类型=="体质" or 类型=="力量" or 类型=="耐力" or 类型=="魔力"  then
					道具[类型]=math.floor(取随机数(等级/10+2,等级/10+10)+(取随机数(灵气/20,灵气/10)))
				elseif 类型=="气血" then
					道具[类型]=math.floor(取随机数(等级*0.65+20,等级*1.05+30)+(取随机数(灵气*0.45,灵气*0.95)))
				elseif 类型=="魔法" then
					道具[类型]=math.floor(取随机数(等级*0.65+20,等级*1.05+30)+(取随机数(灵气*0.45,灵气*0.95)))
				end
			end
		end
	end

	道具属性[书铁格子]=道具
	道具属性[石头格子]=nil

	if 石头格子==物品序号1 then
		物品序号1=nil
	else
		物品序号2=nil
	end
	常规提示(id,"制造装备成功")
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造召唤兽装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造召唤兽装备",1)
	玩家数据[id].角色:刷新信息()
end


function 打造处理类:取可以镶嵌(装备,宝石)
	if 装备 == 1 and (宝石 == 2 or 宝石 == 3 or 宝石==5) then
		return true
	elseif 装备 == 2 and 宝石 == 4 then
		return true
	elseif 装备 == 3 and (宝石 == 3 or 宝石 == 5 or 宝石 == 7) then
		return true
	elseif 装备 == 4 and (宝石 == 1 or 宝石 == 2 or 宝石 == 4) then
		return true
	elseif 装备 == 5 and (宝石 == 1 or 宝石 == 6 or 宝石 == 7) then
		return true
	elseif 装备 == 6 and (宝石 == 6 or 宝石 == 7) then
		return true
	end
end

function 打造处理类:取可以镶嵌灵石(装备,宝石)
	if 装备 == 8 and 宝石==1 then  --项圈
		return true
	elseif 装备 == 9 and 宝石 == 2 then --铠甲
		return true
	elseif 装备 == 7 and 宝石==3 then  --护腕
		return true
	end
end

function 打造处理类:分解所需分解符(装备等级,装备分类)
	if 装备分类>=10 then
		--灵饰分解所需
		if 装备等级<=60 then
			return 4
		elseif 装备等级<=80 then
			return 6
		elseif 装备等级<=100 then
			return 8
		elseif 装备等级<=120 then
			return 10
		elseif 装备等级<=140 then
		    return 12
		elseif 装备等级<=160 then
			return 14
		else
			return 1000
		end
	else
		--装备分解所需
		if 装备等级<=80 then
			return 5
		elseif 装备等级<=100 then
			return 6
		elseif 装备等级<=110 then
			return 7
		elseif 装备等级<=120 then
			return 12
		elseif 装备等级<=130 then
		    return 16
		elseif 装备等级<=140 then
			return 23
		elseif 装备等级<=160 then
			return 30
		else
			return 1000
		end
	end
end
function 打造处理类:分解装备获取吸附石数量(装备等级)
	if 装备等级<=60 then
		return 1
	elseif 装备等级<=70 then
		return 取随机数(1,2)
	elseif 装备等级<=80 then
		return 取随机数(2,3)
	elseif 装备等级<=90 then
		return 取随机数(3,4)
	elseif 装备等级<=110 then
	    return 取随机数(4,5)
	elseif 装备等级<=120 then
		return 取随机数(5,6)
	elseif 装备等级<=140 then
		return 取随机数(7,8)
	elseif 装备等级<=150 then
		return 取随机数(8,9)
	end
end
function 打造处理类:分解灵饰获取星辉石(装备等级)
	if 装备等级<=60 then
		return 3
	elseif 装备等级<=80 then
		return 0
	elseif 装备等级<=100 then
		return 0
	elseif 装备等级<=120 then
		return 0
	elseif 装备等级<=140 then
	    return 0
	elseif 装备等级<=160 then
		return 0
	end
end
function 打造处理类:分解装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 装备序号 = 0
	local 分解符序号 = 0
	if 道具1.总类 == 5 then
		分解符序号 = 物品序号1
		装备序号 = 物品序号2
	else
		分解符序号 = 物品序号2
		装备序号 = 物品序号1
	end

	--分解符鉴别
	if 道具属性[分解符序号].名称~="分解符" then
		常规提示(id,"只有分解符才可以进行装备分解操作！")
		return
	end

	--级别限制
	if 道具属性[装备序号].级别限制 < 60 then
		常规提示(id,"必须60级以上的装备才能进行分解！")
		return
	end
	-- 分解符所需数量
	local 所需分解符数量 = self:分解所需分解符(道具属性[装备序号].级别限制,道具属性[装备序号].分类)
	if 道具属性[分解符序号].数量<所需分解符数量 then
		常规提示(id,"分解符数量不够,无法进行分解操作！")
		return
	end
	--体力
	if 道具属性[装备序号].级别限制 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[装备序号].级别限制 * 500 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end
	local 获取物品=nil
	local 获取数量=nil
	local 文字单位 = nil
	if 道具属性[装备序号].分类>=10 then
		获取物品 = "星辉石"
		获取数量 = self:分解灵饰获取星辉石(道具属性[装备序号].级别限制)
		文字单位 = "级"
	else
		获取物品 = "吸附石"
		获取数量 = self:分解装备获取吸附石数量(道具属性[装备序号].级别限制)
		文字单位 = "个"
	end

	local 打造金钱 = 道具属性[装备序号].级别限制 * 500
	local 打造体力 = math.floor(道具属性[装备序号].级别限制 / 10 + 10)
	local 道具等级 = 道具属性[装备序号].级别限制
	local 元身序列 = 道具属性[装备序号].元身序列

	--分解后获取物品
	if 道具等级 == 160 then
		if 取随机数()<=10 then
			道具属性[装备序号]=nil
			local 幻化名字 = self.元身物品[元身序列]
			local 道具 = 物品类()
			道具:置对象(幻化名字)
			道具属性[装备序号]=幻化处理类:分解幻化处理(id,幻化名字,元身序列)
			常规提示(id,"#Y/你获得了 #R/【"..幻化名字.."】")
		else
			获取物品 = "陨铁"
			获取数量 = 取随机数(1,4)
			文字单位 = "个"
			for n=1,获取数量 do
				玩家数据[id].道具:给予道具(id,获取物品,1)
			end
			道具属性[装备序号]=nil
			常规提示(id,"#Y/你获得了"..获取数量..文字单位.." #R/【"..获取物品.."】")
		end
	else
		道具属性[装备序号]=nil
		local 道具 = 物品类()
		道具:置对象(获取物品)
		if 获取物品=="星辉石" then
			道具.级别限制=获取数量
		else
			-- 道具.可叠加=true
			道具.数量=获取数量
			for n=1,获取数量 do
			玩家数据[id].道具:给予道具(id,获取物品,1)
			end

		end
		-- 道具属性[装备序号]=道具
		道具属性[装备序号]=nil
		常规提示(id,"#Y/你获得了"..获取数量..文字单位.." #R/【"..获取物品.."】")
	end
	if 分解符序号 == 物品序号1 then
		物品序号1 = nil
		道具属性[分解符序号].数量=道具属性[分解符序号].数量-所需分解符数量
		if 道具属性[分解符序号].数量<1 then
			道具属性[分解符序号] = nil
		end
	else
		物品序号2 = nil
		道具属性[分解符序号].数量=道具属性[分解符序号].数量-所需分解符数量
		if 道具属性[分解符序号].数量<1 then
			道具属性[分解符序号] = nil
		end
	end

	常规提示(id,"装备分解成功")
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"分解装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"分解装备",1)
	玩家数据[id].角色:刷新信息()
end

function 打造处理类:熔炼装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 装备序号 = 0
	local 石头序号 = 0
	if 道具1.总类 == 5 then
		石头序号 = 物品序号1
		装备序号 = 物品序号2
	else
		石头序号 = 物品序号2
		装备序号 = 物品序号1
	end

	if 道具属性[石头序号].名称~="钨金" then
		常规提示(id,"只有钨金才可以进行熔炼操作")
		return
	end

	if 道具属性[装备序号].级别限制 > 道具属性[石头序号].子类 then
		常规提示(id,"你的这块钨金等级太低了，熔炼不了这件装备")
		return
	end

	if 道具属性[装备序号].级别限制 < 60 then
		常规提示(id,"必须60级以上的装备才能进行熔炼！")
		return
	end

	if 道具属性[装备序号].制造者 == nil then
		常规提示(id,"必须是打造出来的装备才能进行熔炼")
		return
	end

	--体力
	if 道具属性[装备序号].级别限制 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[装备序号].级别限制 * 500 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	--耐久
	if 道具属性[装备序号].耐久 < 100 then
		常规提示(id,"#Y您装备的耐久小于100，无法进行熔炼！")
		return
	end

	local 打造金钱 = 道具属性[装备序号].级别限制 * 500
	local 打造体力 = math.floor(道具属性[装备序号].级别限制 / 10 + 10)

	if 取随机数(1,100) <= 50 then
		道具属性[装备序号].熔炼属性={}
		local rl = self:取熔炼属性(道具属性[装备序号])
		local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力"}
		for n = 1, #属性 do
			if rl[n] ~= nil then
				道具属性[装备序号].熔炼属性[属性[n]] = math.floor(rl[n])
			end
		end
		常规提示(id,"熔炼装备成功")
	else
	    常规提示(id,"#Y熔炼失败！")
	end

	if 石头序号 == 物品序号1 then
		物品序号1 = nil
		道具属性[石头序号] = nil
	else
		物品序号2 = nil
		道具属性[石头序号] = nil
	end
	道具属性[装备序号].耐久 = 道具属性[装备序号].耐久 - 5
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"熔炼装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"熔炼装备",1)
	玩家数据[id].角色:刷新信息()
end

function 打造处理类:取熔炼属性(道具)
	local 熔炼属性 = {}
	if 道具.分类 == 1 then
		if 取随机数(1,100) <= 50 then
			local 总属性编号 = {3,4} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			if x1 == 3 then
				熔炼属性[x1] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 1.3 + 6) * 0.1), (道具.级别限制 * 1.3 + 6) * 0.2)
			else
			    熔炼属性[x1] = 取随机数(-((道具.级别限制 * 1.3 + 6) * 0.1), (道具.级别限制 * 1.3 + 6) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			end
		else
			local sx = 取随机数(3,4)
			if sx == 3 then
				熔炼属性[sx] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			else
			    熔炼属性[sx] = 取随机数(-((道具.级别限制 * 1.3 + 6) * 0.1), (道具.级别限制 * 1.3 + 6) * 0.2)
			end
		end
	elseif 道具.分类 == 2 then
		熔炼属性[5] = 取随机数(-((道具.级别限制 * 1.64 + 6) * 0.1), (道具.级别限制 * 1.64 + 6) * 0.2)
	elseif 道具.分类 == 3 then
		if 取随机数(1,100) <= 50 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			熔炼属性[x1] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
			熔炼属性[x2] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
		else
			local sx = 取随机数(7,11)
			熔炼属性[sx] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
		end
	elseif 道具.分类 == 4 then
		if 取随机数(1,100) <= 50 then
			local 总属性编号 = {3,7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			if x1 == 3 then
				熔炼属性[x1] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
				熔炼属性[x2] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
			elseif x2 == 3 then
				熔炼属性[x1] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			else
				熔炼属性[x1] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
				熔炼属性[x2] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
			end
		else
			local 总属性编号 = {3,7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local sx = 总属性编号[随机编号]
			if sx == 3 then
				熔炼属性[sx] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			else
			    熔炼属性[sx] = 取随机数(-(道具.级别限制 * 0.03), 道具.级别限制 * 0.06)
			end
		end
	elseif 道具.分类 == 5 then
		if 取随机数(1,100) <= 50 then
			local 总属性编号 = {3,6} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			if x1 == 3 then
				熔炼属性[x1] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 2.73 + 13) * 0.1), (道具.级别限制 * 2.73 + 13) * 0.2)
			else
			    熔炼属性[x1] = 取随机数(-((道具.级别限制 * 2.73 + 13) * 0.1), (道具.级别限制 * 2.73 + 13) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			end
		else
			local 总属性编号 = {3,6} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local sx  = 总属性编号[随机编号]
			if sx == 3 then
				熔炼属性[sx] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			else
			    熔炼属性[sx] = 取随机数(-((道具.级别限制 * 2.73 + 13) * 0.1), (道具.级别限制 * 2.73 + 13) * 0.2)
			end
		end
	elseif 道具.分类 == 6 then
		if 取随机数(1,100) <= 50 then
			local 总属性编号 = {3,7} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			if x1 == 3 then
				熔炼属性[x1] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 0.4 + 6) * 0.1), (道具.级别限制 * 0.4 + 6) * 0.2)
			else
			    熔炼属性[x1] = 取随机数(-((道具.级别限制 * 0.4 + 6) * 0.1), (道具.级别限制 * 0.4 + 6) * 0.2)
				熔炼属性[x2] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			end
		else
			local 总属性编号 = {3,7} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local sx  = 总属性编号[随机编号]
			if sx == 3 then
				熔炼属性[sx] = 取随机数(-((道具.级别限制 * 0.64 + 7) * 0.1), (道具.级别限制 * 0.64 + 7) * 0.2)
			else
			    熔炼属性[sx] = 取随机数(-((道具.级别限制 * 0.4 + 6) * 0.1), (道具.级别限制 * 0.4 + 6) * 0.2)
			end
		end
	end
	return 熔炼属性
end

function 打造处理类:钟灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 宝石格子 = 0
	local 装备格子 = 0
	if 道具1.总类 == 5 then
		宝石格子 = 物品序号1
		装备格子 = 物品序号2
	else
		宝石格子 = 物品序号2
		装备格子 = 物品序号1
	end

	if 道具属性[宝石格子].总类 ~= 5 and 道具属性[宝石格子].分类 ~= 6 and 道具属性[宝石格子].子类 ~= 8 then
		常规提示(id,"只有钟灵石才可以镶嵌在灵饰上")
		return
	end

	if 道具属性[宝石格子].名称=="钟灵石" and 玩家数据[id].道具.数据[装备格子].灵饰 then  --处理灵饰
		if 玩家数据[id].道具.数据[装备格子].级别限制 == nil then
			玩家数据[id].道具.数据[装备格子].级别限制 = 取物品数据(玩家数据[id].道具.数据[装备格子].名称)[5]
		end
		if 玩家数据[id].道具.数据[装备格子].附加特性 == nil then
			玩家数据[id].道具.数据[装备格子].附加特性 = {}
		end
		if 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 ~= nil and 道具属性[宝石格子].级别限制 <= 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 then
			常规提示(id,"#Y/该灵饰当前需要使用#R"..(玩家数据[id].道具.数据[装备格子].附加特性.幻化等级+1).."#Y等级钟灵石进行幻化操作。")
			return
		elseif  玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 ~= nil and  道具属性[宝石格子].附加特性 ~= 玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 then
			常规提示(id,"#Y/该灵饰当前需要使用#R"..玩家数据[id].道具.数据[装备格子].附加特性.幻化类型.."#Y钟灵石进行幻化操作。")
			return
		end
		local 幻化成功 = false
		if 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 == nil then
			玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 = 1
			玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 = 道具属性[宝石格子].附加特性
			幻化成功 = true
		else
			玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 = 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 + 1
			幻化成功 = true
		end
		if 幻化成功 then
			if 宝石格子==物品序号1 then
				物品序号1=nil
				道具属性[宝石格子]=nil
			else
				物品序号2=nil
				道具属性[宝石格子]=nil
			end
			常规提示(id,"#Y/灵饰幻化特性成功！")
			道具刷新(id)
		end
		return
    end
end

function 打造处理类:星辉石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 宝石格子 = 0
	local 装备格子 = 0
	if 道具1.总类 == 5 then
		宝石格子 = 物品序号1
		装备格子 = 物品序号2
	else
		宝石格子 = 物品序号2
		装备格子 = 物品序号1
	end

	if 道具属性[宝石格子].分类 ~= 6  then
		常规提示(id,"只有宝石才可以镶嵌在装备上")
		return
	end

	if 道具属性[宝石格子].名称 == "星辉石" and 道具属性[装备格子].灵饰 then  --处理灵饰
		if 道具属性[宝石格子].级别限制 - 1 ~= 道具属性[装备格子].幻化等级 then
			常规提示(id,"该灵饰目前只能用#R/"..(玩家数据[id].道具.数据[装备格子].幻化等级+1).."#Y/级的星辉石进行强化")
			return
		elseif 道具属性[装备格子].幻化等级 >= 20 then
			常规提示(id,"#Y/灵饰最多可以强化20次")
			return
		end

		--金钱
		if 道具属性[装备格子].幻化等级 * 10000 > 玩家数据[id].角色.数据.银子 then
			常规提示(id,"#Y您的银子不够镶嵌的费用哦！")
			return
		end

		道具属性[装备格子].幻化等级 = 道具属性[装备格子].幻化等级 + 1
		local 打造金钱 = 道具属性[宝石格子].级别限制 * 10000
		for n=1,#道具属性[装备格子].幻化属性.附加 do
	        道具属性[装备格子].幻化属性.附加[n].强化=0
	        道具属性[装备格子].幻化属性.附加[n].强化=取灵饰强化(道具属性[装备格子].幻化属性.附加[n].类型,道具属性[装备格子].级别限制,道具属性[装备格子].幻化等级)



	    end
		常规提示(id,"#Y/灵饰强化成功！")

		if 宝石格子 == 物品序号1 then
			物品序号1 = nil
			道具属性[宝石格子] = nil
		else
			物品序号2 = nil
			道具属性[宝石格子] = nil
		end
		道具刷新(id)
		玩家数据[id].角色:扣除银子(math.abs(打造金钱),"镶嵌宝石",1)
		玩家数据[id].角色:刷新信息()
		return
	end
end


function 打造处理类:宝石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 宝石格子 = 0
	local 装备格子 = 0
	if 道具1.总类 == 5 then
		宝石格子 = 物品序号1
		装备格子 = 物品序号2
	else
		宝石格子 = 物品序号2
		装备格子 = 物品序号1
	end

	if 道具属性[宝石格子].分类 ~= 6  then
		常规提示(id,"只有宝石才可以镶嵌在装备上")
		return
	end

	if self:取可以镶嵌(道具属性[装备格子].分类, 道具属性[宝石格子].子类) then
		local 锻造级别 = 0
		if 道具属性[装备格子].锻炼等级 ~= nil then
			锻造级别 = 道具属性[装备格子].锻炼等级
		end

		锻造级别 = 锻造级别 + 1

		if 道具属性[装备格子].级别限制~=nil then
			锻造级别上限 = math.floor(道具属性[装备格子].级别限制/10) + 1
		end
		if (道具属性[装备格子].特效 ~= nil and 道具属性[装备格子].特效 == "精致") or (道具属性[装备格子].第二特效 ~= nil and 道具属性[装备格子].第二特效 == "精致") then
			锻造级别上限 = 锻造级别上限 + 1
		end
		if (道具属性[装备格子].特效 ~= nil and 道具属性[装备格子].特效 == "无级别限制") or (道具属性[装备格子].第二特效 ~= nil and 道具属性[装备格子].第二特效 == "无级别限制") then
			锻造级别上限 = 99
		end

		if 锻造级别>锻造级别上限 then
			常规提示(id,"该装备已达到锻造等级上限")
			return
		end

		if 道具属性[宝石格子].级别限制 ~= 锻造级别 then
			常规提示(id,"该装备目前只能用#R/"..锻造级别.."#Y/级宝石镶嵌")
			return
		end
		local 打造金钱 = 道具属性[宝石格子].级别限制 * 10000
		--金钱
		if 锻造级别 * 10000 > 玩家数据[id].角色.数据.银子 then
			常规提示(id,"#Y您的银子不够镶嵌的费用哦！")
			return
		end

		if 道具属性[装备格子].镶嵌宝石 == nil then
			道具属性[装备格子].镶嵌宝石 = {}
			道具属性[装备格子].镶嵌类型 = {}
		end

		if 道具属性[装备格子].镶嵌宝石[1] == nil then
			道具属性[装备格子].镶嵌宝石[1] = 道具属性[宝石格子].名称
		elseif 道具属性[装备格子].镶嵌宝石[2] == nil and 道具属性[装备格子].镶嵌宝石[1] ~= 道具属性[宝石格子].名称  then
			道具属性[装备格子].镶嵌宝石[2] = 道具属性[宝石格子].名称
		elseif 道具属性[装备格子].镶嵌宝石[1] ~= 道具属性[宝石格子].名称  and 道具属性[装备格子].镶嵌宝石[2] ~= 道具属性[宝石格子].名称 then
			常规提示(id,"装备最多只能镶嵌两种不同类型的宝石")
			return
		end

		道具属性[装备格子].锻炼等级 = 锻造级别
		道具属性[装备格子].镶嵌类型[锻造级别] = 道具属性[宝石格子].名称
		if 道具属性[宝石格子].子类 == 1 then
			道具属性[装备格子].气血 = (道具属性[装备格子].气血 or 0) - floor(40 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].气血 = (道具属性[装备格子].气血 or 0) + floor(40 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 2 then
			道具属性[装备格子].防御 = (道具属性[装备格子].防御 or 0) - floor(12 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].防御 = (道具属性[装备格子].防御 or 0) + floor(12 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 3 then
			道具属性[装备格子].伤害 = (道具属性[装备格子].伤害 or 0) - floor(8 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].伤害 = (道具属性[装备格子].伤害 or 0) + floor(8 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 4 then
			道具属性[装备格子].灵力 = (道具属性[装备格子].灵力 or 0) - floor(6 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].灵力 = (道具属性[装备格子].灵力 or 0) + floor(6 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 5 then
			道具属性[装备格子].命中 = (道具属性[装备格子].命中 or 0) - floor(25 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].命中 = (道具属性[装备格子].命中 or 0) + floor(25 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 6 then
			道具属性[装备格子].速度 = (道具属性[装备格子].速度 or 0) - floor(8 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].速度 = (道具属性[装备格子].速度 or 0) + floor(8 * 道具属性[宝石格子].级别限制)
		elseif 道具属性[宝石格子].子类 == 7 then
			道具属性[装备格子].躲避 = (道具属性[装备格子].躲避 or 0) - floor(20 * (道具属性[宝石格子].级别限制 - 1))
			道具属性[装备格子].躲避 = (道具属性[装备格子].躲避 or 0) + floor(20 * 道具属性[宝石格子].级别限制)
		end

		if 宝石格子 == 物品序号1 then
			物品序号1 = nil
			道具属性[宝石格子]=nil
		else
			物品序号2=nil
			道具属性[宝石格子]=nil
		end
		常规提示(id,"镶嵌装备成功")
		道具刷新(id)
		玩家数据[id].角色:扣除银子(math.abs(打造金钱),"镶嵌宝石",1)
		玩家数据[id].角色:刷新信息()
	else
		常规提示(id,"这种宝石无法镶嵌到此类装备上")
		return
	end
end

function 打造处理类:灵石镶嵌处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 宝石格子 = 0
	local 装备格子 = 0
	if 道具1.总类 == 5 then
		宝石格子 = 物品序号1
		装备格子 = 物品序号2
	else
		宝石格子 = 物品序号2
		装备格子 = 物品序号1
	end

	if 道具属性[宝石格子].分类 ~= 11  then
		常规提示(id,"只有精魄灵石才可以镶嵌在装备上")
		return
	end

	if self:取可以镶嵌灵石(道具属性[装备格子].分类, 道具属性[宝石格子].子类) then
		local 锻造级别 = 0
		if 道具属性[装备格子].锻炼等级 ~= nil then
			锻造级别 = 道具属性[装备格子].锻炼等级
		end

		锻造级别 = 锻造级别 + 1
		if 道具属性[宝石格子].级别限制 ~= 锻造级别 then
			常规提示(id,"该装备目前只能用#R/"..锻造级别.."#Y/级精魄灵石镶嵌")
			return
		end
		local 打造金钱 = 道具属性[宝石格子].级别限制 * 10000
		--金钱
		if 锻造级别 * 10000 > 玩家数据[id].角色.数据.银子 then
			常规提示(id,"#Y您的银子不够镶嵌的费用哦！")
			return
		end
		if 道具属性[装备格子].镶嵌宝石 == nil then
			道具属性[装备格子].镶嵌宝石 = {}
			道具属性[装备格子].镶嵌类型 = {}
			道具属性[装备格子].灵石属性 = {}
		end

		if 道具属性[装备格子].镶嵌宝石[1] == nil then
			道具属性[装备格子].镶嵌宝石[1] = 道具属性[宝石格子].类型
		elseif 道具属性[装备格子].镶嵌宝石[1] ~= 道具属性[宝石格子].类型  then
			常规提示(id,"BB装备只能镶嵌一种类型的宝石")
			return
		end
		道具属性[装备格子].锻炼等级 = 锻造级别
		道具属性[装备格子].镶嵌类型[锻造级别] = 道具属性[宝石格子].名称
		if 道具属性[宝石格子].类型=="伤害" then
			道具属性[装备格子].灵石属性.伤害=锻造级别*10
		elseif 道具属性[宝石格子].类型=="灵力" then
			道具属性[装备格子].灵石属性.灵力=锻造级别*4
		elseif 道具属性[宝石格子].类型=="防御" then
			道具属性[装备格子].灵石属性.防御=锻造级别*8
		elseif 道具属性[宝石格子].类型=="气血" then
			道具属性[装备格子].灵石属性.气血=锻造级别*30
		elseif 道具属性[宝石格子].类型=="速度" then
			道具属性[装备格子].灵石属性.速度=锻造级别*6
		end

		if 宝石格子 == 物品序号1 then
			物品序号1 = nil
			道具属性[宝石格子]=nil
		else
			物品序号2=nil
			道具属性[宝石格子]=nil
		end
		常规提示(id,"镶嵌召唤兽装备成功")
		道具刷新(id)
		玩家数据[id].角色:扣除银子(math.abs(打造金钱),"镶嵌精魄灵石",1)
		玩家数据[id].角色:刷新信息()
	else
		常规提示(id,"这种精魄灵石无法镶嵌到此类召唤兽装备上")
		return
	end
end

function 打造处理类:打造灵饰处理(id,物品序号1,物品序号2,道具1,道具2,道具属性,打造方式选项)
	local 灵饰书格子 = 0
	local 晶石格子 = 0
	if 道具1.名称 == "灵饰指南书" and 道具2.名称 == "元灵晶石" then
		灵饰书格子 = 物品序号1
		晶石格子 = 物品序号2
	elseif 道具1.名称 == "元灵晶石" and 道具2.名称 == "灵饰指南书" then
		灵饰书格子 = 物品序号2
		晶石格子 = 物品序号1
	end
	if 灵饰书格子 == 0 or 晶石格子 == 0 then
		常规提示(id,"打造装备需要使用灵饰指南书和元灵晶石，你这给我的是啥玩意？？？")
		return
	end

	if 道具属性[灵饰书格子].子类 > 道具属性[晶石格子].子类 then
		常规提示(id,"你的这块元灵晶石等级太低了，配不上这本制造指南书")
		return
	end

	--体力
	if 道具属性[灵饰书格子].子类 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[灵饰书格子].子类 * 道具属性[灵饰书格子].子类 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	local 打造金钱 = 道具属性[灵饰书格子].子类 * 道具属性[灵饰书格子].子类 * 10
	local 打造体力 = math.floor(道具属性[灵饰书格子].子类 / 10 + 10)

	local 临时名称 = 制造装备[道具属性[灵饰书格子].特效][道具属性[灵饰书格子].子类]
	if 打造方式选项 == "普通打造灵饰" then
		local 道具 = 物品类()
		道具:置对象(临时名称)
		道具.级别限制 = 道具属性[灵饰书格子].子类
		道具.幻化等级 = 0
		local 临时等级 = 道具属性[灵饰书格子].子类
		local 临时类型 = 道具属性[灵饰书格子].特效
		道具属性[灵饰书格子] = nil
		道具属性[灵饰书格子] = 道具
		self:打造灵饰(id,灵饰书格子,临时等级,0,临时类型,道具)
		道具属性[晶石格子] = nil
		道具属性[灵饰书格子].耐久 = 取随机数(400,600)
		道具属性[灵饰书格子].部位类型 = 临时类型
		道具.制造者 = 玩家数据[id].角色.数据.名称
		道具.灵饰 = true
		if 晶石格子 == 物品序号1 then
			物品序号1 = nil
		else
			物品序号2 = nil
		end
		常规提示(id,"制造灵饰成功")
		道具刷新(id)
	elseif 打造方式选项 == "强化打造灵饰" then
		if 玩家数据[id].角色:取任务(5)~=0 then
			常规提示(id,"#Y/你已经领取了一个强化打造任务，赶快去完成吧")
			return
		end
		任务处理类:设置打造灵饰任务(id,临时名称,道具属性[灵饰书格子].子类,道具属性[灵饰书格子].特效)
		道具属性[晶石格子] = nil
		道具属性[灵饰书格子] = nil
		物品序号2 = nil
		物品序号1 = nil
		道具刷新(id)
	end
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造灵饰",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造灵饰",1)
	玩家数据[id].角色:刷新信息()
end


function 打造处理类:打造灵饰(id,道具id,等级,强化,类型)
 	玩家数据[id].道具.数据[道具id].幻化属性 = {附加 = {},}
 	玩家数据[id].道具.数据[道具id].识别码 = 取唯一识别码(id)
 	local 临时属性 = 灵饰属性[类型].主属性[取随机数(1, #灵饰属性[类型].主属性)]
 	local 临时数值 = 灵饰属性.附加上限[等级][2]
 	local 临时下限 = 灵饰属性.附加上限[等级][1]
 	临时数值 = 取随机数(self.临时下限,self.临时数值)
 	local 下限数量 = 2
  	local 上限数量 = 4
 	if 强化 == 1 then
 		下限数量 = 2
   		临时数值 = math.floor(临时数值 * 1.1)
   		local 随机 = 取随机数()
   		if 随机 <= 10 then
	  		上限数量 = 4
	  	elseif 随机 <= 55 then
	  		上限数量 = 3
	  	else
	  		上限数量 = 2
	 	end
   	end
 	玩家数据[id].道具.数据[道具id].幻化属性.基础 = {类型 = 临时属性, 数值 = 临时数值, 强化 = 0}
 	for n=1, 取随机数(下限数量, 上限数量) do
   		临时属性 = 灵饰属性[类型].副属性[取随机数(1,#灵饰属性[类型].副属性)]
   		临时数值 = 灵饰属性.附加上限[等级][2]
   		临时下限 = 灵饰属性.附加上限[等级][1]
   		临时数值 = 取随机数(临时下限, 临时数值)
		for i=1, #玩家数据[id].道具.数据[道具id].幻化属性.附加 do
	  		if 玩家数据[id].道具.数据[道具id].幻化属性.附加[i].类型 == 临时属性 then
	   		临时数值 = 玩家数据[id].道具.数据[道具id].幻化属性.附加[i].数值
			end
		end
   		玩家数据[id].道具.数据[道具id].幻化属性.附加[n] = {类型 = 临时属性, 数值 = 临时数值, 强化 = 0}
	end
	-- 玩家数据[id].道具.数据[道具id].鉴定 = false
end

function 打造处理类:打造专用灵饰(id,道具id,等级,强化,类型)
 	玩家数据[id].道具.数据[道具id].幻化属性 = {附加 = {},}
 	玩家数据[id].道具.数据[道具id].识别码 =取唯一识别码(id)
 	local 临时属性 = 灵饰属性[类型].主属性[取随机数(1, #灵饰属性[类型].主属性)]
 	local 临时数值 = 灵饰属性.基础[临时属性][等级].b
 	local 临时下限 = 灵饰属性.基础[临时属性][等级].a
 	临时数值 = 取随机数(临时下限, 临时数值)
 	local 下限数量 = 1
  	local 上限数量 = 2
 	if 强化 == 1 then
 		下限数量 = 2
   		临时数值 = math.floor(临时数值 * 1.1)
   		if 取随机数(1,100) <= 5 then
	  		上限数量 = 4
	  	else
	  		上限数量 = 3
	 	end
   	end
 	玩家数据[id].道具.数据[道具id].幻化属性.基础 = {类型 = 临时属性, 数值 = 临时数值, 强化 = 0}
 	for n=1, 取随机数(下限数量, 上限数量) do
   		临时属性 = 灵饰属性[类型].副属性[取随机数(1, #灵饰属性[类型].副属性)]
   		临时数值 = 灵饰属性.基础[临时属性][等级].b
   		临时下限 = 灵饰属性.基础[临时属性][等级].a
   		临时数值 = 取随机数(临时下限, 临时数值)
		for i=1, #玩家数据[id].道具.数据[道具id].幻化属性.附加 do
	  		if 玩家数据[id].道具.数据[道具id].幻化属性.附加[i].类型 == 临时属性 then
	   		临时数值 = 玩家数据[id].道具.数据[道具id].幻化属性.附加[i].数值
			end
		end
   		玩家数据[id].道具.数据[道具id].幻化属性.附加[n] = {类型 = 临时属性, 数值 = 临时数值, 强化 = 0}
	end
	玩家数据[id].道具.数据[道具id].专用=id
	玩家数据[id].道具.数据[道具id].不可交易=true
end


function 打造处理类:添加强化打造灵饰(id,任务id)
    local 道具 = 物品类()
    道具:置对象(任务数据[任务id].名称)
    道具.级别限制 = 任务数据[任务id].级别
    道具.幻化等级 = 0
    local 临时id = 玩家数据[id].道具:取新编号()
    local 临时格子 = 玩家数据[id].角色:取道具格子()
    玩家数据[id].道具.数据[临时id] = 道具
    玩家数据[id].道具.数据[临时id].部位类型 = 任务数据[任务id].部位
    self:打造灵饰(id,临时id,任务数据[任务id].级别,1,任务数据[任务id].部位)
    玩家数据[id].道具.数据[临时id].灵饰 = true
    玩家数据[id].道具.数据[临时id].特效="无级别限制"
    玩家数据[id].道具.数据[临时id].耐久 = 取随机数(500,700)
    玩家数据[id].道具.数据[临时id].制造者 = 玩家数据[id].角色.数据.名称.."强化打造"
    玩家数据[id].角色.数据.道具[临时格子] = 临时id
    常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[临时id].名称)
    玩家数据[id].角色:取消任务(任务id)
    任务数据[任务id] = nil
	道具刷新(id)
end


function 打造处理类:添加强化打造装备(id,任务id,技能)
	local 道具 = 物品类()
	道具:置对象(任务数据[任务id].名称)
	道具.级别限制 = 任务数据[任务id].级别
	local dz = self:强化打造公式(任务数据[任务id].级别,任务数据[任务id].序列,技能)
	local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
	local 打造特效 = (通用特效)
	local 打造特技 = (通用特技)
	for n = 1, #属性 do
		if dz[n] ~= nil then
			if n == 12 then
				if 道具.分类 == 5 then
					table.insert(打造特效,"愤怒")
					table.insert(打造特效,"暴怒")
				end
				道具[属性[n]] = 打造特效[random(1,#打造特效)]
			elseif n == 13 then
				道具[属性[n]] = 打造特技[random(1,#打造特技)]
			else
				道具[属性[n]] = math.floor(dz[n])
			end
		end
	end
	道具.制造者 = 玩家数据[id].角色.数据.名称.."强化打造"
	-- 生产附加
	-- if 道具.分类==
	local 制造格子 = 玩家数据[id].道具:取新编号()
	玩家数据[id].道具.数据[制造格子] = 道具
	玩家数据[id].道具.数据[制造格子].五行 = 取五行()
	玩家数据[id].道具.数据[制造格子].耐久 = 取随机数(500,700)
	玩家数据[id].道具.数据[制造格子].识别码 =取唯一识别码(id)
	玩家数据[id].角色.数据.道具[玩家数据[id].角色:取道具格子()] = 制造格子
	玩家数据[id].角色:取消任务(任务id)
	任务数据[任务id] = nil
	玩家数据[id].道具.数据[制造格子].鉴定 = false
	常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[制造格子].名称)
	道具刷新(id)
end

function 打造处理类:添加强化打造160装备(id,任务id,技能)
	local 道具 = 物品类()
	道具:置对象(任务数据[任务id].名称)
	道具.级别限制 = 任务数据[任务id].级别
	道具.元身序列 = 任务数据[任务id].元身属性.元身序列
	local dz = self:强化打造160级公式(任务数据[任务id].级别,任务数据[任务id].序列,技能,任务数据[任务id].元身属性.幻化元身属性)
	local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
	local 打造特效 = (通用特效)
	local 打造特技 = (通用特技)
	local 新特效 = (通用新特效)
	for n = 1, #属性 do
		if dz[n] ~= nil then
			if n == 12 then
				if 道具.分类 == 5 then
				  table.insert(打造特效,"愤怒")
				  table.insert(打造特效,"暴怒")
				end
				if 取随机数() <= 5 then
					道具[属性[n]] = 打造特效[random(1,#打造特效)]
					道具.新特效 = 新特效[random(1,#新特效)]
					if 道具.新特效 == "魔法回复" then
						道具.新特效数值 = random(6,20)
					elseif 道具.新特效 == "格挡物理伤害" then
						道具.新特效数值 = random(20,40)
					elseif 道具.新特效 == "治疗能力" then
						道具.新特效数值 = random(10,30)
					else
					    道具.新特效数值 = string.format("%.2f",random(100,500)/100)
					end
				else
				    if 取随机数() <= 50 then
				    	道具[属性[n]] = 打造特效[random(1,#打造特效)]
				    else
				        道具.新特效 = 新特效[random(1,#新特效)]
				        if 道具.新特效 == "魔法回复" then
							道具.新特效数值 = random(6,20)
						elseif 道具.新特效 == "格挡物理伤害" then
							道具.新特效数值 = random(20,40)
						elseif 道具.新特效 == "治疗能力" then
							道具.新特效数值 = random(10,30)
						else
						    道具.新特效数值 = string.format("%.2f",random(100,500)/100)
						end
				    end
				end
			elseif n == 13 then
				道具[属性[n]] = 打造特技[random(1,#打造特技)]
			else
				道具[属性[n]] = math.floor(dz[n])
			end
		end
	end
	道具.制造者 = 玩家数据[id].角色.数据.名称.."强化打造"
	-- 生产附加
	-- if 道具.分类==
	local 制造格子 = 玩家数据[id].道具:取新编号()
	玩家数据[id].道具.数据[制造格子] = 道具
	玩家数据[id].道具.数据[制造格子].五行 = 取五行()
	玩家数据[id].道具.数据[制造格子].耐久 = 取随机数(500,700)
	玩家数据[id].道具.数据[制造格子].识别码 =取唯一识别码(id)
	玩家数据[id].角色.数据.道具[玩家数据[id].角色:取道具格子()] = 制造格子
	玩家数据[id].角色:取消任务(任务id)
	任务数据[任务id] = nil
	玩家数据[id].道具.数据[制造格子].鉴定 = false
	常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[制造格子].名称)
	道具刷新(id)
end

function 打造处理类:强化打造160级公式(元身等级,制造种类,技能,元身属性) -- 制作书等级 制作种类
	local 打造属性 = {}
	local 特效 = 取随机数()
	local 特技 = 取随机数()
	local 技能等级 = 0
	local 范围 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
	local 特效概率 = 元身属性.特效概率 or 0
	local 特技概率 = 元身属性.特技概率 or 0
	if 制造种类 < 19 then
		技能等级 = 技能[7].等级
	elseif 制造种类 == 19 or 制造种类 == 20 or 制造种类 == 22 or 制造种类 == 23  then
		技能等级 = 技能[8].等级
	elseif 制造种类 ==21 or 制造种类 ==24 or 制造种类 ==25 then
		技能等级 = 技能[9].等级
	end
	if 元身等级 == nil or 制造种类 == nil then
		return false
	end
	----------------------------摩托修改元身打造随机属性--------------------------
	if 制造种类 < 19 then -- 武器
		打造属性[1] = 取随机数(元身属性.命中*0.8, 元身属性.命中*1.1)*1
		打造属性[2] = 取随机数(元身属性.伤害*0.8, 元身属性.伤害*1.1)*1
		if 取随机数(1,100) <= 1 then
			打造属性[1] = 打造属性[1] * 1
			打造属性[2] = 打造属性[2] * 1
		end

		local 属性个数 = 0
		local 选取属性 = 0
		for n=7,#范围-2 do
			if 元身属性[范围[n]]~=nil then
				local 判断字 = 分割文本(元身属性[范围[n]],范围[n])
				if 判断字[1] == "增加" then
					属性个数 = 属性个数 + 1
					打造属性[n] = 取随机数(80, 160)
					选取属性 = n
				else
					属性个数 = 属性个数 + 1
					打造属性[n] = 取随机数(80, 160)
					选取属性 = n
				end
			end
		end

		if 属性个数 == 0 then
			if 取随机数(1,100) <= 5 + 技能等级 / 10 / 2 then
				local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
				local 随机编号 = 取随机数(1,#总属性编号)
				local x1  = 总属性编号[随机编号]
				remove(总属性编号,随机编号)
				随机编号 = 取随机数(1,#总属性编号)
				local x2  = 总属性编号[随机编号]
				打造属性[x1] = 取随机数(80, 元身等级 +10)
				打造属性[x2] = 取随机数(80, 元身等级 + 10)
			elseif 取随机数(1,100) <= 10 + 技能等级 / 10 / 2 then
				local sx = 取随机数(7,11)
				打造属性[sx] = 取随机数(1, 元身等级 * 1)
			end
		elseif 属性个数 == 1 then
			if 取随机数(1,100) <= 5 + 技能等级 / 10 / 2 then
				local 总属性编号 = {7,8,9,10,11}
				remove(总属性编号,选取属性)
				local 随机编号 = 取随机数(1,#总属性编号)
				local sx  = 总属性编号[随机编号]
				打造属性[sx] = 取随机数(80, 元身等级 +10)
			end
		end
	elseif 制造种类 == 19 or 制造种类 == 20 then -- 帽子
		打造属性[3] = 取随机数(元身属性.防御*0.8, 元身属性.防御*1.1)*1
		打造属性[4] = 取随机数(元身属性.魔法*0.8, 元身属性.魔法*1.1)*1
		if 取随机数(1,100) <= 10 then
			打造属性[3] = 打造属性[3] * 1
			打造属性[4] = 打造属性[4] * 1
		end
	elseif 制造种类 == 21 then -- 项链
		打造属性[5] = 取随机数(元身属性.灵力*1, 元身属性.灵力*1)*1
		if 取随机数(1,100) <= 10 then
			打造属性[5] = 打造属性[5] * 1
		end
	elseif 制造种类 == 22 or 制造种类 == 23 then -- 衣服
		打造属性[3] = 取随机数(元身属性.防御*1, 元身属性.防御*1)*1
		if 取随机数(1,100) <= 10 then
			打造属性[3] = 打造属性[3] * 1
		end

		local 属性个数 = 0
		local 选取属性 = 0
		for n=7,#范围-2 do
			if 元身属性[范围[n]]~=nil then
				local 判断字 = 分割文本(元身属性[范围[n]],范围[n])
				if 判断字[1] == "增加" then
					属性个数 = 属性个数 + 1
					打造属性[n] = 取随机数(80, 160)
					选取属性 = n
				else
					属性个数 = 属性个数 + 1
					打造属性[n] = 取随机数(80, 160)
					选取属性 = n
				end
			end
		end

		if 属性个数 == 0 then
			if 取随机数(1,100) <= 5 + 技能等级 / 10 / 2 then
				local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
				local 随机编号 = 取随机数(1,#总属性编号)
				local x1  = 总属性编号[随机编号]
				remove(总属性编号,随机编号)
				随机编号 = 取随机数(1,#总属性编号)
				local x2  = 总属性编号[随机编号]
				打造属性[x1] = 取随机数(1, 元身等级 * 1)
				打造属性[x2] = 取随机数(1, 元身等级 * 1)
			elseif 取随机数(1,100) <= 10 + 技能等级 / 10 / 2 then
				local sx = 取随机数(7,11)
				打造属性[sx] = 取随机数(1, 元身等级 * 1)
			end
		elseif 属性个数 == 1 then
			if 取随机数(1,100) <= 5 + 技能等级 / 10 / 2 then
				local 总属性编号 = {7,8,9,10,11}
				remove(总属性编号,选取属性)
				local 随机编号 = 取随机数(1,#总属性编号)
				local sx  = 总属性编号[随机编号]
				打造属性[sx] = 取随机数(1, 元身等级 * 1)
			end
		end
	elseif 制造种类 == 24 then -- 腰带
		打造属性[3] = 取随机数(元身属性.防御*0.8, 元身属性.防御*1.1)*1
		打造属性[6] = 取随机数(元身属性.气血*0.8, 元身属性.气血*1.1)*1
		if 取随机数(1,100) <= 10 then
			打造属性[3] = 打造属性[3] * 1
			打造属性[6] = 打造属性[6] * 1
		end
	elseif 制造种类 == 25 then -- 鞋子
		打造属性[3] = 取随机数(元身属性.防御*0.8, 元身属性.防御*1.1)*1
		打造属性[7] = 取随机数(元身属性.敏捷*0.8, 元身属性.敏捷*1.1)*1
		if 取随机数(1,100) <= 10 then
			打造属性[3] = 打造属性[3] * 1
			打造属性[7] = 打造属性[7] * 1
		end
	end
	-- if 特效 <= 5 + 特效概率 then
	-- 	打造属性[12] = 1
	-- end
	-- if 特技 <= 5 + 特技概率 then
	-- 	打造属性[13] = 1
	-- end
	return 打造属性
end


function 打造处理类:普通打造公式(制造书等级,制造种类) -- 制作书等级 制作种类
	local 打造属性 = {} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
	if 制造书等级 == nil or 制造种类 == nil then
		return false
	end
	if 制造种类 < 19 then -- 武器
		打造属性[1] = 取随机数(制造书等级 * 3.5 + 10, 制造书等级 * 4.5 + 14)
		打造属性[2] = 取随机数(制造书等级 * 3 + 10, 制造书等级 * 3.93 + 11)
		if 取随机数(1,100) <= 15 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(-8, 制造书等级 * 0.3)
			打造属性[x2] = 取随机数(-8, 制造书等级 * 0.3)
		elseif 取随机数(1,100) <= 15 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(-8, 制造书等级 * 0.3)
		end
	elseif 制造种类 == 19 then -- 帽子
		打造属性[3] = 取随机数(制造书等级 * 0.5 + 5, 制造书等级 * 0.64 + 7)
		打造属性[4] = 取随机数(制造书等级 * 1 + 5, 制造书等级 * 1.3 + 6)
	elseif 制造种类 == 20 then -- 项链
		打造属性[5] = 取随机数(制造书等级 * 1.2 + 5, 制造书等级 * 1.56 + 6)
	elseif 制造种类 == 21 then -- 衣服
		打造属性[3] = 取随机数(制造书等级 * 1.5 + 10, 制造书等级 * 2 + 8)
		if 取随机数(1,100) <= 15 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(-8, 制造书等级 * 0.3)
			打造属性[x2] = 取随机数(-8, 制造书等级 * 0.3)
		elseif 取随机数(1,100) <= 15 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(-8, 制造书等级 * 0.3)
		end
	elseif 制造种类 == 22 then -- 腰带
		打造属性[3] = 取随机数(制造书等级 * 0.5 + 5, 制造书等级 * 0.64 + 7)
		打造属性[6] = 取随机数(制造书等级 * 2 + 10, 制造书等级 * 2.6 + 13)
	elseif 制造种类 == 23 then -- 鞋子
		打造属性[3] = 取随机数(制造书等级 * 0.5 + 5, 制造书等级 * 0.64 + 7)
		打造属性[7] = 取随机数(制造书等级 * 0.3 + 5, 制造书等级 * 0.4 + 6)
	end
	local 特效 = 取随机数()
	if 特效 <= 10 then
		打造属性[12] = 1
	end
	特技 = 取随机数()
	if 特技 <= 10 then
		打造属性[13] = 1
	end
	return 打造属性
end

function 打造处理类:商城装备公式(制造书等级,制造种类,技能)
	local 打造属性 = {} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
   	if 制造书等级 == nil or 制造种类 == nil then
		return false
  	end
  	if 制造种类 < 19 then -- 武器
	   	打造属性[1] = 取随机数(制造书等级 * 4.5 + 14,制造书等级 * 4.5 + 14)*1.1
	   	打造属性[2] = 取随机数(制造书等级 * 4.12 + 11,制造书等级 * 4.12 + 11)*1.1
	  	if 取随机数(1,100) <= 100 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] =50
			打造属性[x2] =50
		end
  	elseif 制造种类 == 19 then -- 帽子
		打造属性[3] = 取随机数(制造书等级 * 0.67 + 7,制造书等级 * 0.67 + 7)*1.1
		打造属性[4] = 取随机数(制造书等级 * 1.37 + 6,制造书等级 * 1.37 + 6)*1.1
  	elseif 制造种类 == 20 then -- 项链
		打造属性[5] = 取随机数(制造书等级 * 1.64 + 6,制造书等级 * 1.64 + 6)*1.1
  	elseif 制造种类 == 21 then -- 衣服
		打造属性[3] = 取随机数(制造书等级 * 2.1 + 8,制造书等级 * 2.1 + 8)*1.1
		if 取随机数(1,100) <= 100 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] =50
			打造属性[x2] =50
		end
  	elseif 制造种类 == 22 then -- 腰带
		打造属性[3] = 取随机数(制造书等级 * 0.67 + 7,制造书等级 * 0.67 + 7)*1.1
	   	打造属性[6] = 取随机数(制造书等级 * 2.73 + 13,制造书等级 * 2.73 + 13)*1.1
  	elseif 制造种类 == 23 then -- 鞋子
		打造属性[3] = 取随机数(制造书等级 * 0.67 + 7,制造书等级 * 0.67 + 7)*1.1
		打造属性[7] = 取随机数(制造书等级 * 0.4 + 6,制造书等级 * 0.4 + 6)*1.1
  	end
  	local 特效 = 取随机数()
  	if 特效 <= 30 then
		打造属性[12] = 1
  	end
  	特技 = 取随机数()
  	if 特技 <= 30 then
		打造属性[13] = 1
  	end
  	return 打造属性
end


function 打造处理类:强化打造公式(制造书等级,制造种类,技能) -- 制作书等级 制作种类
	local 打造属性 = {}
	local 特效 = 取随机数()
	local 特技 = 取随机数()
	local 技能等级 = 0
	if 制造种类 < 19 then
		技能等级 = 技能[7].等级
	elseif 制造种类 == 19 or 制造种类 == 21 then
		技能等级 = 技能[8].等级
	elseif 制造种类 ==20 or 制造种类 ==22 or 制造种类 ==23 then
		技能等级 = 技能[9].等级
	end
	if 制造书等级 == nil or 制造种类 == nil then
		return false
	end
	if 制造种类 < 19 then -- 武器
		打造属性[1] = 取随机数(制造书等级 +650, (制造书等级 +850) )
		打造属性[2] = 取随机数(制造书等级 +600, (制造书等级 +800) )

		if 取随机数(1,100) <= 9 + 技能等级 / 10 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(制造书等级 -70, 制造书等级 -30)
			打造属性[x2] = 取随机数(制造书等级 -70, 制造书等级 -30)
		elseif 取随机数(1,100) <= 9 + 技能等级 / 10 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(制造书等级 -70, 制造书等级 -30)
		end
	elseif 制造种类 == 19 then -- 帽子
		打造属性[3] = 取随机数(制造书等级 -50, (制造书等级 +0))
		打造属性[4] = 取随机数(制造书等级 +60, (制造书等级 +150))
		-- if 取随机数(1,100) <= 10 then
		-- 	打造属性[3] = 打造属性[3] * 1.1
		-- 	打造属性[4] = 打造属性[4] * 1.1
		-- end
	elseif 制造种类 == 20 then -- 项链
		打造属性[5] = 取随机数(制造书等级 +30, (制造书等级 +70))
		-- if 取随机数(1,100) <= 10 then
		-- 	打造属性[5] = 打造属性[5] * 1.1
		-- end
	elseif 制造种类 == 21 then -- 衣服
		打造属性[3] = 取随机数(制造书等级 +50, (制造书等级 +100) )
		-- if 取随机数(1,100) <= 10 then
		-- 	打造属性[3] = 打造属性[3] * 1.1
		-- end
		if 取随机数(1,100) <= 9 + 技能等级 / 10 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(制造书等级 -70, 制造书等级 -30)
			打造属性[x2] = 取随机数(制造书等级 -70, 制造书等级 -30)
		elseif 取随机数(1,100) <= 9 + 技能等级 / 10 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(制造书等级 -70, 制造书等级 -30)
		end
	elseif 制造种类 == 22 then -- 腰带
		打造属性[3] = 取随机数(制造书等级 -55, (制造书等级 +0))
		打造属性[6] = 取随机数(制造书等级 +200, (制造书等级 +450) )
		-- if 取随机数(1,100) <= 10 then
		-- 	打造属性[3] = 打造属性[3] * 1.1
		-- 	打造属性[6] = 打造属性[6] * 1.1
		-- end
	elseif 制造种类 == 23 then -- 鞋子
		打造属性[3] = 取随机数(制造书等级 -120, (制造书等级 -100))
		打造属性[7] = 取随机数(制造书等级 -80, (制造书等级 -20) )
		-- if 取随机数(1,100) <= 10 then
		-- 	打造属性[3] = 打造属性[3] * 1.1
		-- 	打造属性[7] = 打造属性[7] * 1.1
		-- end
	end
	if 特效 <= 4 + 技能等级 / 10 then
		打造属性[12] = 1
	end
	if 特技 <= 4 + 技能等级 / 10 then
		打造属性[13] = 1
	end
	return 打造属性
end


function 打造处理类:布店装备打造处理(id,物品序号1,物品序号2,道具1,道具2,道具属性,打造方式选项)
	local 制造书序号 = 0
	local 制造铁序号 = 0

	if 道具1.名称 == "制造指南书" and 道具2.名称 == "百炼精铁" then
		制造书序号 = 物品序号1
		制造铁序号 = 物品序号2
		if 道具1.子类 > 道具2.子类 then
			常规提示(id,"你的这块精铁等级太低了，配不上这本制造指南书")
			return
		end
	elseif 道具2.名称 == "制造指南书" and 道具1.名称 == "百炼精铁" then
		制造书序号 = 物品序号2
		制造铁序号 = 物品序号1
		if 道具1.子类 < 道具2.子类 then
			常规提示(id,"你的这块精铁等级太低了，配不上这本制造指南书")
			return
		end
	end

	if 道具1.子类 == 160 and 道具2.子类 == 160 and 打造方式选项 == "普通打造装备" then
		常规提示(id,"160的装备必须使用强化打造哟！")
		return
	end

	if 制造书序号 == 0 or 制造铁序号 == 0 then
		常规提示(id,"打造装备需要使用制造指南书和百炼精铁，你这给我的是啥玩意？？？")
		return
	end

	local 临时序列 = 道具属性[制造书序号].特效
	if 临时序列 <= 18 then
		常规提示(id,"打造武器，请到铁匠铺打造哟！")
		return
	end
	--技能
	-- if 道具属性[制造书序号].子类 > 玩家数据[id].角色.数据.辅助技能[9].等级 and (临时序列 == 25 or 临时序列 == 24 or 临时序列 == 21) then
	-- 	常规提示(id,"#Y打造饰品，需要#R 炼金术 #Y/达到所打造装备的等级哟！")
	-- 	return
	-- end

	-- if 道具属性[制造书序号].子类 > 玩家数据[id].角色.数据.辅助技能[8].等级 and (临时序列 == 23 or 临时序列 == 22 or 临时序列 == 20 or 临时序列 == 19) then
	-- 	常规提示(id,"#Y打造防具，需要#R 裁缝技巧 #Y/达到所打造装备的等级哟！")
	-- 	return
	-- end

	--体力
	if 道具属性[制造书序号].子类 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[制造书序号].子类 * 道具属性[制造书序号].子类 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	--获取装备名称
	if 临时序列 == 25 then -- 鞋子
		临时序列 = 23
	elseif 临时序列 == 24 then -- 腰带
		临时序列 = 22
	elseif 临时序列 == 23 or 临时序列 == 22 then -- 男女衣服
		临时序列 = 21
	elseif 临时序列 == 21 then -- 项链
		临时序列 = 20
	elseif 临时序列 == 20 or 临时序列 == 19 then -- 男女头
		临时序列 = 19
	end

	local 临时等级 = 道具属性[制造书序号].子类 / 10
	-- 计算武器值
	if 临时序列 <= 18 and 临时等级 >= 9 then --是武器 10-12是普通光武
		if 临时等级 < 12 then
			临时等级 = 取随机数(10,12)
		elseif 临时等级 < 15 then
			临时等级 = 取随机数(13,15)
		elseif 临时等级 == 15 then
			临时等级 = 16
		end
	else
		临时等级 = 临时等级 + 1
	end
	local 临时类型=self.打造物品[临时序列][临时等级]
	if type(临时类型)=="table" then
		if 道具属性[制造书序号].特效 ==23 then
			临时类型=临时类型[2]
		elseif 道具属性[制造书序号].特效 ==22 then
			临时类型=临时类型[1]
		elseif 道具属性[制造书序号].特效 ==20 then
			临时类型=临时类型[2]
		elseif 道具属性[制造书序号].特效 ==19 then
			临时类型=临时类型[1]
		else
			临时类型=临时类型[取随机数(1,2)]
		end
	end

	local 打造金钱 = 道具属性[制造书序号].子类 * 道具属性[制造书序号].子类 * 10
	local 打造体力 = math.floor(道具属性[制造书序号].子类 / 10 + 10)

	if 打造方式选项 == "普通打造装备" then
		local 道具 = 物品类()
		道具:置对象(临时类型)
		道具.级别限制 = 道具属性[制造书序号].子类
		local dz = self:普通打造公式(道具属性[制造书序号].子类,临时序列)
		local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
		local 打造特效 = (通用特效)
		local 打造特技 = (通用特技)
		for n = 1, #属性 do
			if dz[n] ~= nil then
				if n == 12 then
					if 道具.分类 == 5 then
						table.insert(打造特效,"愤怒")
						table.insert(打造特效,"暴怒")
					end
					道具[属性[n]] = 打造特效[random(1,#打造特效)]
				elseif n == 13 then
					道具[属性[n]] = 打造特技[random(1,#打造特技)]
				else
					道具[属性[n]] = math.floor(dz[n])
				end
			end
		end

		道具.制造者 = 玩家数据[id].角色.数据.名称
		-- 生产附加
		-- if 道具.分类==
		道具属性[制造书序号] = nil
		道具属性[制造书序号] = 道具
		道具属性[制造书序号].五行 = 取五行()
		道具属性[制造书序号].耐久 = 取随机数(400,600)
		道具属性[制造书序号].识别码 = 取唯一识别码(id)
		道具属性[制造铁序号]=nil
		if 制造铁序号 == 物品序号1 then
			物品序号1 = nil
		else
			物品序号2 = nil
		end
		道具属性[制造书序号].鉴定 = false
		常规提示(id,"制造装备成功")
		道具刷新(id)
	elseif 打造方式选项 == "强化打造装备" then
		if 玩家数据[id].角色:取任务(5)~=0 then
			常规提示(id,"#Y/你已经领取了一个强化打造任务，赶快去完成吧")
			return
		end
		任务处理类:设置打造装备任务(id,临时类型,道具属性[制造书序号].子类,临时序列)
		道具属性[制造书序号] = nil
		道具属性[制造铁序号] = nil
		if 制造铁序号 == 物品序号1 then
			物品序号1 = nil
		else
			物品序号2 = nil
		end
		道具刷新(id)
	end
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造装备",1)
	玩家数据[id].角色:刷新信息()
end

function 打造处理类:布店强化打造160装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 元身 = 0
	local 战魂 = 0
	if 道具1.总类 == 66 and 道具2.总类 == 67 then
		元身 = 物品序号1
		战魂 = 物品序号2
	elseif 道具1.总类 == 67 and 道具2.总类 == 66 then
		元身 = 物品序号2
		战魂 = 物品序号1
	end

	local 临时序列 = 道具属性[元身].元身序列
	if 临时序列 <= 18 then
		常规提示(id,"打造武器，请到铁匠铺打造哟！")
		return
	end

	--技能
	-- if 道具属性[元身].级别限制 > 玩家数据[id].角色.数据.辅助技能[9].等级 and (临时序列 == 25 or 临时序列 == 24 or 临时序列 == 21) then
	-- 	常规提示(id,"#Y打造160级饰品，需要#R 炼金术 #Y/达到160级才能打造哟！")
	-- 	return
	-- end

	-- if 道具属性[元身].级别限制 > 玩家数据[id].角色.数据.辅助技能[8].等级 and (临时序列 == 23 or 临时序列 == 22 or 临时序列 == 20 or 临时序列 == 19) then
	-- 	常规提示(id,"#Y打造160级防具，需要#R 裁缝技巧 #Y/达到160级才能打造哟！")
	-- 	return
	-- end

	--体力
	if 350 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[元身].级别限制 * 道具属性[元身].级别限制 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	if 玩家数据[id].角色:取任务(5)~=0 then
		常规提示(id,"#Y/你已经领取了一个强化打造任务，赶快去完成吧")
		return
	end

	local 打造金钱 = 道具属性[元身].级别限制 * 道具属性[元身].级别限制 * 10
	local 打造体力 = 350

	local 临时类型 = self.装备物品[临时序列]

	任务处理类:设置打造160级装备任务(id,临时类型,道具属性[元身].级别限制,临时序列,道具属性[元身])
	道具属性[元身] = nil
	道具属性[战魂] = nil
	if 元身 == 物品序号1 then
		物品序号1 = nil
	else
		物品序号2 = nil
	end
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造160级装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造160级装备",1)
	玩家数据[id].角色:刷新信息()
end

function 打造处理类:打铁铺强化打造160装备处理(id,物品序号1,物品序号2,道具1,道具2,道具属性)
	local 元身 = 0
	local 战魂 = 0
	if 道具1.总类 == 66 and 道具2.总类 == 67 then
		元身 = 物品序号1
		战魂 = 物品序号2
	elseif 道具1.总类 == 67 and 道具2.总类 == 66 then
		元身 = 物品序号2
		战魂 = 物品序号1
	end

	local 临时序列 = 道具属性[元身].元身序列
	if 临时序列 > 18 then
		常规提示(id,"打造防具，请到布店打造哟！")
		return
	end
	--技能
	-- if 道具属性[元身].级别限制 > 玩家数据[id].角色.数据.辅助技能[7].等级 then
	-- 	常规提示(id,"#Y打造160级武器，需要#R 打造技巧 #Y/达到160级才能打造哟！")
	-- 	return
	-- end

	--体力
	if 350 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[元身].级别限制 * 道具属性[元身].级别限制 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	if 玩家数据[id].角色:取任务(5)~=0 then
		常规提示(id,"#Y/你已经领取了一个强化打造任务，赶快去完成吧")
		return
	end

	local 打造金钱 = 道具属性[元身].级别限制 * 道具属性[元身].级别限制 * 10
	local 打造体力 = 350

	local 临时类型 = self.装备物品[临时序列]

	任务处理类:设置打造160级装备任务(id,临时类型,道具属性[元身].级别限制,临时序列,道具属性[元身])
	道具属性[元身] = nil
	道具属性[战魂] = nil
	if 元身 == 物品序号1 then
		物品序号1 = nil
	else
		物品序号2 = nil
	end
	道具刷新(id)
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造160级装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造160级装备",1)
	玩家数据[id].角色:刷新信息()
end

function 打造处理类:打铁铺装备打造处理(id,物品序号1,物品序号2,道具1,道具2,道具属性,打造方式选项)
	local 制造书序号 = 0
	local 制造铁序号 = 0
	if 道具1.名称 == "制造指南书" and 道具2.名称 == "百炼精铁" then
		制造书序号 = 物品序号1
		制造铁序号 = 物品序号2
		if 道具1.子类 > 道具2.子类 then
			常规提示(id,"你的这块精铁等级太低了，配不上这本制造指南书")
			return
		end
	elseif 道具2.名称 == "制造指南书" and 道具1.名称 == "百炼精铁" then
		制造书序号 = 物品序号2
		制造铁序号 = 物品序号1
		if 道具1.子类 < 道具2.子类 then
			常规提示(id,"你的这块精铁等级太低了，配不上这本制造指南书")
			return
		end
	end

	if 道具1.子类 == 160 and 道具2.子类 == 160 and 打造方式选项 == "普通打造装备" then
		常规提示(id,"160的装备必须使用强化打造哟！")
		return
	end

	if (制造书序号 == 0 or 制造铁序号 == 0) then
		常规提示(id,"打造装备需要使用制造指南书和百炼精铁，你这给我的是啥玩意？？？")
		return
	end

	-- local 临时序列 = 道具属性[制造书序号].特效
	-- if 临时序列 > 18 then
	-- 	常规提示(id,"打造防具，请到布店打造哟！")
	-- 	return
	-- end
	--技能
	-- if 道具属性[制造书序号].子类 > 玩家数据[id].角色.数据.辅助技能[7].等级 then
	-- 	常规提示(id,"#Y打造武器，需要#R 打造技巧 #Y/达到所打造装备的等级哟！")
	-- 	return
	-- end

	--体力
	if 道具属性[制造书序号].子类 / 10 + 10 > 玩家数据[id].角色.数据.体力 then
		常规提示(id,"#Y您的体力好像不够了！")
		return
	end

	--金钱
	if 道具属性[制造书序号].子类 * 道具属性[制造书序号].子类 * 10 > 玩家数据[id].角色.数据.银子 then
		常规提示(id,"#Y您的银子不够打造的费用哦！")
		return
	end

	--获取装备名称
	if 临时序列 == 25 then -- 鞋子
		临时序列 = 23
	elseif 临时序列 == 24 then -- 腰带
		临时序列 = 22
	elseif 临时序列 == 23 or 临时序列 == 22 then -- 男女衣服
		临时序列 = 21
	elseif 临时序列 == 21 then -- 项链
		临时序列 = 20
	elseif 临时序列 == 20 or 临时序列 == 19 then -- 男女头
		临时序列 = 19
	end

	local 临时等级 = 道具属性[制造书序号].子类 / 10
	-- 计算武器值
	if 临时序列 <= 18 and 临时等级 >= 9 then --是武器 10-12是普通光武
		if 临时等级 < 12 then
			临时等级 = 取随机数(10,12)
		elseif 临时等级 < 15 then
			临时等级 = 取随机数(13,15)
		elseif 临时等级 == 15 then
			临时等级 = 16
		end
	else
		临时等级 = 临时等级 + 1
	end
	local 临时类型=self.打造物品[临时序列][临时等级]
	if type(临时类型)=="table" then
		if 道具属性[制造书序号].特效 ==23 then
			临时类型=临时类型[2]
		elseif 道具属性[制造书序号].特效 ==22 then
			临时类型=临时类型[1]
		elseif 道具属性[制造书序号].特效 ==20 then
			临时类型=临时类型[2]
		elseif 道具属性[制造书序号].特效 ==19 then
			临时类型=临时类型[1]
		else
			临时类型=临时类型[取随机数(1,2)]
		end
	end
	local 打造金钱 = 道具属性[制造书序号].子类 * 道具属性[制造书序号].子类 * 10
	local 打造体力 = math.floor(道具属性[制造书序号].子类 / 10 + 10)
	if 打造方式选项 == "普通打造装备" then
		local 道具 = 物品类()
		道具:置对象(临时类型)
		道具.级别限制 = 道具属性[制造书序号].子类
		local dz = self:普通打造公式(道具属性[制造书序号].子类,临时序列)
		local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
		local 打造特效 = (通用特效)
		local 打造特技 = (通用特技)
		for n = 1, #属性 do
			if dz[n] ~= nil then
				if n == 12 then
					if 道具.分类 == 5 then
					  table.insert(打造特效,"愤怒")
					  table.insert(打造特效,"暴怒")
					end
					道具[属性[n]] = 打造特效[random(1,#打造特效)]
				elseif n == 13 then
					道具[属性[n]] = 打造特技[random(1,#打造特技)]
				else
					道具[属性[n]] = math.floor(dz[n])
				end
			end
		end

		道具.制造者 = 玩家数据[id].角色.数据.名称
		-- 生产附加
		-- if 道具.分类==
		道具属性[制造书序号] = nil
		道具属性[制造书序号] = 道具
		道具属性[制造书序号].五行 = 取五行()
		道具属性[制造书序号].耐久 = 取随机数(400,600)
		道具属性[制造书序号].识别码 =取唯一识别码(id)
		道具属性[制造铁序号]=nil
		if 制造铁序号 == 物品序号1 then
			物品序号1 = nil
		else
			物品序号2 = nil
		end
		道具属性[制造书序号].鉴定 = false
		常规提示(id,"制造装备成功")
		道具刷新(id)
	elseif 打造方式选项 == "强化打造装备" then
		if 玩家数据[id].角色:取任务(5)~=0 then
			常规提示(id,"#Y/你已经领取了一个强化打造任务，赶快去完成吧")
			return
		end
		任务处理类:设置打造装备任务(id,临时类型,道具属性[制造书序号].子类,临时序列)
		道具属性[制造书序号] = nil
		道具属性[制造铁序号] = nil
		if 制造铁序号 == 物品序号1 then
			物品序号1 = nil
		else
			物品序号2 = nil
		end
		道具刷新(id)
	end
	玩家数据[id].角色:扣除银子(math.abs(打造金钱),"打造装备",1)
	玩家数据[id].角色:扣除体力(打造体力,"打造装备",1)
	玩家数据[id].角色:刷新信息()
end


function 打造处理类:更新(dt)

end


function 打造处理类:显示(x,y)

end

return 打造处理类