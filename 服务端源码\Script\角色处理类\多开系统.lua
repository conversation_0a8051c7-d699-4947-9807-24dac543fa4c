
local 多开系统类 = class()

function 多开系统类:初始化(id)
end

function 多开系统类:切换角色(id,内容)
    local 文本 = 内容.文本
    local 选择id = 内容.参数+0
    if 玩家数据[id] == nil then return end
    if 玩家数据[id].战斗 and 玩家数据[id].战斗~=0 then return end
    if 玩家数据[id].摊位数据 then
          常规提示(id,"#Y/摆摊状态下禁止此种行为")
          return
    end
    if 玩家数据[id].自动抓鬼 then
        常规提示(id,"#Y/自动抓鬼状态下禁止此种行为")
        return
    end
    if 玩家数据[id].自动遇怪 then
        常规提示(id,"#Y/自动遇怪状态下禁止此种行为")
        return
    end
    if 玩家数据[id].角色.数据.跑商 then
        发送数据(玩家数据[id].连接id,7,"#Y/跑商时不能使用此功能")
        return
    end
    if 玩家数据[id].角色.数据.跑镖 then
        发送数据(玩家数据[id].连接id,7,"#Y/押镖时不能使用此功能")
        return
    end
    local 当前地图 = 玩家数据[id].角色.数据.地图数据.编号
    if(当前地图>=1340 and 当前地图<=1342) or 当前地图==1332 or (当前地图>=1600 and 当前地图<=1620) or (当前地图>=6012 and 当前地图<=6019)
     or (当前地图>=6021 and 当前地图<=6039) or (当前地图>=7001 and 当前地图<=7004) or 当前地图 == 5001 or 当前地图 == 6001 or 当前地图 == 6002
     or 当前地图 == 6003 or 当前地图==6009  or 当前地图 >= 100000  then
       发送数据(玩家数据[id].连接id,7,"#Y/本地图无法使用该功能")
        return
    end
    if 当前地图==6004 and not 英雄大会:是否同组(id,选择id) then
        发送数据(玩家数据[id].连接id,7,"#Y/不是同组玩家无法操作")
        return
    end
    if 玩家数据[选择id] and 玩家数据[选择id].战斗 and 玩家数据[选择id].战斗~=0 then return end
    if 玩家数据[选择id] and 玩家数据[选择id].角色.数据.跑商 then
       发送数据(玩家数据[id].连接id,7,"#Y/对方跑商时不能使用此功能")
      return
    end
    if 玩家数据[选择id] and 玩家数据[选择id].角色.数据.跑镖  then
       发送数据(玩家数据[id].连接id,7,"#Y/对方押镖时不能使用此功能")
      return
    end
    if 玩家数据[选择id] and 玩家数据[选择id].摊位数据 then
        常规提示(id,"#Y/对方摆摊状态下禁止此种行为")
        return
    end
    if 玩家数据[选择id] and 玩家数据[选择id].角色.数据.离线摆摊 then
        发送数据(玩家数据[id].连接id,7,"#Y/对方摆摊状态下禁止此种行为")
        return
    end
    if 玩家数据[id].角色.数据.多角色操作 == nil then
        玩家数据[id].角色.数据.多角色操作 = false
    end
    玩家数据[id].角色经脉操作=nil
    玩家数据[id].升级角色=nil
    玩家数据[id].仓库操作=nil
   if 文本=="获取角色信息" then
      self:取角色选择信息(id,105)
   elseif 文本=="创建角色" then
      local 临时文件=table.loadstring(读入文件([[data/]]..玩家数据[id].账号..[[/信息.txt]]))
      if #临时文件>=5 then
         发送数据(玩家数据[id].连接id,7,"#Y/一个账号最多可以建立5个角色!")
         return
      end

    self:创建分角色(id,内容)
   elseif 文本=="切换角色" then
        if  (当前地图>=10000 and 当前地图<=10018) or 当前地图==6010   then
            常规提示(id,"#Y/本地图无法使用该功能")
            return
        end
        if 当前地图==6011 or 当前地图==6020 then
            if not 玩家数据[id].角色.数据.帮派数据 or not 玩家数据[id].角色.数据.帮派数据.编号 then
              常规提示(id,"#Y/你还没有帮派!")
              return
            end
            if not 玩家数据[选择id].角色.数据.帮派数据 or not 玩家数据[选择id].角色.数据.帮派数据.编号 then
              常规提示(id,"#Y/对方没有帮派!")
              return
            end
            local 帮派id = 玩家数据[id].角色.数据.帮派数据.编号
            if not 帮派数据[帮派id] then 常规提示(id,"#Y/未找到帮派!") return  end
            if 玩家数据[选择id].角色.数据.帮派数据.编号~=帮派id then
                常规提示(id,"#Y/你和对方不是同一帮派!")
                return
            end
        end


        if 玩家数据[选择id]==nil then
           常规提示(id,"#Y/请先参战再切换角色!")
           return
        end
        if not 玩家数据[id].角色.数据.多角色操作  then
            常规提示(id,"#Y/你没有多开无法切换角色!")
            return
        end
        if not 玩家数据[id].队长 then
          常规提示(id,"只有队长才可进行此操作")
          return
        end

        if 玩家数据[id].队伍~=玩家数据[选择id].队伍 then
          常规提示(id,"只有一个队伍才可进行此操作")
          return
        end


      if 玩家数据[选择id].子角色操作 ~= nil then
      local 序列 = 0
      local  团队成员 = {}

         if 玩家数据[id].队长 and 玩家数据[选择id]~=nil then
            local 队伍id = 玩家数据[id].队伍
               for n=1,#队伍数据[队伍id].成员数据 do
                  if 队伍数据[队伍id].成员数据[n] == 选择id then
                      序列 = 队伍数据[队伍id].成员数据[n]
                  else
                       团队成员[#团队成员+1]=队伍数据[队伍id].成员数据[n]
                  end
              end
              团队成员=删除重复(团队成员)
              if 序列== 0 then
                  常规提示(id,"角色未在队伍中无法切换")
                else
                    玩家数据[id].角色.数据.多角色操作 = false
                    队伍处理类:退出队伍(id,nil,true)
                    local 传入数据 ={连接id = 玩家数据[id].连接id,账号 = 玩家数据[id].账号,临时ip = 玩家数据[id].ip,玩家id=id}
                    self:断开游戏(id)
                    系统处理类:断开游戏(选择id)
                    self:角色更换处理(团队成员,选择id,传入数据)
              end
         end
       else
          常规提示(id,"该玩家不可以使用角色切换")
          return
       end

   elseif 文本=="角色参战" then
          if 当前地图>=10000 and 当前地图<=10018  then
              常规提示(id,"#Y/本地图无法使用该功能")
              return
          end
          if 玩家数据[选择id] then
              常规提示(id,"#Y玩家已经在线无法参战")
              return
          else
              local 角色数据 = 读入文件([[data/]]..玩家数据[id].账号..[[/]]..选择id..[[/角色.txt]])
              角色数据 = table.loadstring(角色数据)
              if 当前地图==6010 or 当前地图==6011 or 当前地图==6020 then
                  if not 玩家数据[id].角色.数据.帮派数据 or not 玩家数据[id].角色.数据.帮派数据.编号 then
                    常规提示(id,"#Y/你还没有帮派!")
                    return
                  end
                  if not 角色数据.帮派数据 or not 角色数据.帮派数据.编号 then
                    常规提示(id,"#Y/对方没有帮派!")
                    return
                  end
                  local 帮派id = 玩家数据[id].角色.数据.帮派数据.编号
                  if not 帮派数据[帮派id] then 常规提示(id,"#Y/未找到帮派!") return  end
                  if 角色数据.帮派数据.编号~=帮派id then
                      常规提示(id,"#Y/你和对方不是同一帮派!")
                      return
                  end
              end
              if 角色数据.跑商 then
                    常规提示(id,"#Y/对方正在跑商时不能使用此功能")
                    return
              elseif 角色数据.跑镖 then
                    常规提示(id,"#Y/对方身上有押镖任务无法使用此功能")
                    return
              end
              if not 玩家数据[id].队伍 or 玩家数据[id].队伍 == 0 then
                 local 建队信息 = {}
                 建队信息.ip = 内容.ip
                 建队信息.数字id = id
                 建队信息.id = id
                 队伍处理类:创建队伍(id,建队信息)
              end
              if 玩家数据[id].队长==false then
                常规提示(id,"只有队长才可进行此操作")
                return
              end
              self:角色进入游戏(id,选择id)
              self:角色加入队伍(id,选择id)
              玩家数据[id].角色.数据.多角色操作 = true
              队伍处理类:索取队伍信息(id,4004)
              发送数据(玩家数据[id].连接id,113)
              发送数据(玩家数据[id].连接id,115,{角色=选择id,玩家数据=玩家数据[选择id].角色:取总数据()})
              发送数据(玩家数据[id].连接id,116,{角色=选择id,召唤兽=玩家数据[选择id].召唤兽.数据})
          end
   elseif 文本=="角色退出" then
    if 玩家数据[id].角色.数据.多角色操作 == false then
       常规提示(id,"你没有多开无法退出角色")
        return
      end
      if 玩家数据[选择id]==nil or 选择id ==id then
         常规提示(id,"玩家不在线不用重复退出")
         return
      end
      if 玩家数据[选择id].子角色操作 ~= nil then
          发送数据(玩家数据[id].连接id,118,{角色=选择id})
          if 玩家数据[选择id].队伍~=0 then
             队伍处理类:退出队伍(选择id)
          else
             self:断开游戏(选择id)
          end

      else
          常规提示(id,"该玩家不可以使用角色退出")
          return
      end
  elseif 文本 == "角色操作" or 文本 == "角色加点" or 文本 == "角色升级" or 文本 == "升级师门" or 文本 == "升级辅助" or 文本 == "设置修炼"
         or 文本 == "打开八脉" or 文本 == "切换经脉" or 文本 == "经脉加点" or 文本 == "打开背包" or 文本 == "格子互换1" or 文本 == "索要行囊"
         or  文本 == "索要法宝" or 文本 == "修炼法宝" or 文本 == "整理背包" or 文本 == "卸下法宝" or 文本 == "替换法宝" or 文本 == "卸下装备"
         or 文本 == "佩戴装备" or 文本 == "卸下bb装备" or 文本 == "角色乘骑处理" or 文本 == "角色下骑处理" or 文本 == "卸下坐骑饰品"
         or 文本 == "清空背包" or 文本 == "佩戴bb装备" or 文本 == "穿戴坐骑饰品" or 文本 == "格子互换" or 文本 == "提取道具"
         or 文本 == "佩戴法宝" or 文本 == "打开仓库" or 文本 == "仓库道具" or 文本=="道具仓库" or 文本=="整理仓库" or 文本=="存入仓库" or 文本=="取出物品"
         or 文本=="获取宝宝仓库1" or 文本=="获取宝宝仓库" or 文本=="存入宝宝仓库"  or 文本=="取出宝宝仓库" or 文本=="回收系统" or 文本=="打开宠物"
         or 文本=="参战处理" or 文本=="改名处理" or 文本=="放生处理" or 文本=="加点处理" or 文本=="角色烹饪" or 文本=="角色炼药" or 文本=="提取银子"
    then


     if 玩家数据[id].角色.数据.多角色操作 == false then
        发送数据(玩家数据[id].连接id,7,"#Y/你没有多开无法操作!")
        return
      end
    if 玩家数据[选择id]==nil or 选择id ==id then
         发送数据(玩家数据[id].连接id,7,"#Y/角色不在线无法操作!")
         return
      end
      if 玩家数据[选择id].子角色操作 == nil and 玩家数据[选择id].子角色操作~=id then
        发送数据(玩家数据[id].连接id,7,"#Y/该玩家不可以使用该功能")
        return
      end

      --do
         if 文本 == "角色操作" then
           发送数据(玩家数据[id].连接id,6001,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
           -- 发送数据(玩家数据[id].连接id,6002,{角色=选择id,召唤兽=玩家数据[选择id].召唤兽.数据})

        elseif  文本 == "角色加点" then
            玩家数据[选择id].角色:添加属性点(内容.点数,选择id,id)
            发送数据(玩家数据[id].连接id,6001,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
        elseif 文本 == "角色升级" then
            玩家数据[id].升级角色 = 选择id
            玩家数据[选择id].角色:升级处理(玩家数据[id].连接id,nil,id)
            发送数据(玩家数据[id].连接id,6001,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
         elseif 文本 == "升级师门" then
          if 内容.编号~=nil and 内容.名称~=nil and 玩家数据[选择id].角色.数据.师门技能[内容.编号]~=nil and 玩家数据[选择id].角色.数据.师门技能[内容.编号].名称==内容.名称 then
             玩家数据[选择id].角色:学习门派技能(玩家数据[id].连接id,选择id,内容.编号,id)
             发送数据(玩家数据[id].连接id,6002,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
          end
          elseif 文本 == "升级辅助" then
                 if 内容.编号~=nil and 玩家数据[选择id].角色.数据.辅助技能[内容.编号]~=nil  then
                    玩家数据[选择id].角色:学习生活技能(玩家数据[id].连接id,选择id,内容.编号,id)
                    发送数据(玩家数据[id].连接id,6003,{角色=选择id,序列=内容.编号,等级=玩家数据[选择id].角色.数据.辅助技能[内容.编号].等级})
                    if 玩家数据[选择id].角色.数据.辅助技能[内容.编号].名称=="强身术" or 玩家数据[选择id].角色.数据.辅助技能[内容.编号].名称=="健身术" or
                       玩家数据[选择id].角色.数据.辅助技能[内容.编号].名称=="冥想" or 玩家数据[选择id].角色.数据.辅助技能[内容.编号].名称=="养生之道" or
                       玩家数据[选择id].角色.数据.辅助技能[内容.编号].名称=="强壮"  then
                         发送数据(玩家数据[id].连接id,6002,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
                    end

                 end
           elseif 文本 == "设置修炼" then
              if 内容.人物~=nil then
                  玩家数据[选择id].角色.数据.修炼.当前=内容.人物
              end
              if 内容.bb~=nil then
                玩家数据[选择id].角色.数据.bb修炼.当前=内容.bb
              end
              常规提示(id,"#Y/更换修炼类型成功！")
              发送数据(玩家数据[id].连接id,6004,{角色=选择id,人物=玩家数据[选择id].角色.数据.修炼,bb=玩家数据[选择id].角色.数据.bb修炼})
          elseif 文本 == "打开八脉" then
              发送数据(玩家数据[id].连接id,6005,{角色=选择id})
              玩家数据[id].角色经脉操作 = 选择id
          elseif 文本 == "切换经脉" then
             if 内容.经脉~=nil then
                玩家数据[选择id].经脉:切换奇经八脉处理(选择id,内容.经脉,id)
                发送数据(玩家数据[id].连接id,6002,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
                发送数据(玩家数据[id].连接id,6005,{角色=选择id})
             end
             玩家数据[id].角色经脉操作 = 选择id
          elseif 文本 == "经脉加点" then
              if 内容.序列~=nil then
                玩家数据[选择id].经脉:增加奇经八脉(内容,id)
                发送数据(玩家数据[id].连接id,6002,{角色=选择id,角色属性=玩家数据[选择id].角色:取总数据()})
                发送数据(玩家数据[id].连接id,6005,{角色=选择id})
             end
             玩家数据[id].角色经脉操作 = 选择id
           elseif 文本 == "打开背包" then
               发送数据(玩家数据[id].连接id,6007,{角色=选择id,道具=玩家数据[选择id].道具:索要道具2(选择id)})
               发送数据(玩家数据[id].连接id,6009,{角色=选择id,道具=玩家数据[选择id].道具:索要道具4(选择id,"行囊")})
               发送数据(玩家数据[id].连接id,6010,{角色=选择id,道具=玩家数据[选择id].道具:索要法宝(玩家数据[id].连接id,选择id,id)})
               发送数据(玩家数据[id].连接id,6008,{角色=选择id,召唤兽=玩家数据[选择id].召唤兽.数据})
               发送数据(玩家数据[id].连接id,6006,{角色=选择id})
            elseif 文本 == "格子互换1" then
              玩家数据[选择id].道具:道具格子互换1(玩家数据[id].连接id,选择id,内容,id)
             elseif 文本 == "索要行囊" then
              发送数据(玩家数据[id].连接id,6009,{角色=选择id,道具=玩家数据[选择id].道具:索要道具4(选择id,"行囊")})
            elseif 文本 == "索要法宝" then
              发送数据(玩家数据[id].连接id,6010,{角色=选择id,道具=玩家数据[选择id].道具:索要法宝(玩家数据[id].连接id,选择id,id)})
            elseif 文本 == "修炼法宝" then
               玩家数据[选择id].道具:修炼法宝(玩家数据[id].连接id,选择id,内容.序列,id)
            elseif 文本 == "整理背包" then
               玩家数据[选择id].道具:整理背包(选择id,内容.类型,id)
            elseif 文本 == "卸下法宝" then
             -- 玩家数据[选择id].道具:卸下法宝(玩家数据[id].连接id,选择id,内容.序列,id)
            elseif 文本 == "替换法宝" then
             -- 玩家数据[选择id].道具:替换法宝(玩家数据[id].连接id,选择id,内容.序列,内容.序列1,id)
            elseif 文本 == "佩戴法宝" then
             -- 玩家数据[选择id].道具:佩戴法宝(玩家数据[id].连接id,选择id,内容.类型,内容.编号+0,id)
            elseif 文本 == "卸下装备" then
              玩家数据[选择id].道具:卸下装备(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "佩戴装备" then
              玩家数据[选择id].道具:佩戴装备(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "卸下bb装备" then
              玩家数据[选择id].道具:卸下bb装备(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "角色乘骑处理" then
                 系统处理类:角色乘骑处理(内容,id,选择id)
            elseif 文本 == "角色下骑处理" then
                 系统处理类:角色下骑处理(内容,id,选择id)
            elseif 文本 == "卸下坐骑饰品" then
                  玩家数据[选择id].道具:卸下饰品(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "清空背包" then
                  玩家数据[选择id].道具:清空包裹(玩家数据[id].连接id,选择id,id)
            elseif 文本 == "穿戴坐骑饰品" then
                  玩家数据[选择id].道具:穿戴饰品(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "佩戴bb装备" then
                   玩家数据[选择id].道具:佩戴bb装备(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "格子互换" then
              玩家数据[选择id].道具:道具格子互换(玩家数据[id].连接id,选择id,内容,id)
            elseif 文本 == "提取银子" then
               if 玩家数据[选择id].角色.数据.银子<=0 then return end
               local 提取数量 = tonumber(玩家数据[选择id].角色.数据.银子)
               玩家数据[选择id].角色.数据.银子 = 0
               if isNaN(提取数量) then return end
               玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 提取数量
               常规提示(id,"提取玩家#R/"..玩家数据[选择id].角色.数据.名称.."#Y/银子#R/"..提取数量.."#Y/两")
               道具刷新(id)
               道具刷新(选择id,id)

            elseif 文本 == "提取道具" then
                if not 内容.类型 or not 内容.道具 or not tonumber(内容.道具) then return end
                if 内容.类型~="道具" and 内容.类型~="行囊" then return end
                local 编号 = tonumber(内容.道具)
                local 道具id = 玩家数据[选择id].角色.数据[内容.类型][编号]
                if 道具id==0 or 玩家数据[选择id].道具.数据[道具id] == nil then return end
                local 名称 = 玩家数据[选择id].道具.数据[道具id].名称
                local 道具格子=玩家数据[id].角色:取道具格子()
                if 道具格子==0 then
                    常规提示(id,"您的道具栏物品已经满啦")
                elseif 玩家数据[选择id].道具.数据[道具id].专用~=nil or 玩家数据[选择id].道具.数据[道具id].不可交易 or
                   玩家数据[选择id].道具.数据[道具id].名称=="帮派银票"  or 玩家数据[选择id].道具.数据[道具id].总类== "跑商商品"
                  then
                      常规提示(id,"该道具为不可交易无法提取")
                elseif 玩家数据[选择id].道具.数据[道具id].加锁~=nil then
                    常规提示(id,"该道具已加锁无法提取")
                else
                      local 道具编号=玩家数据[id].道具:取新编号()
                      玩家数据[id].道具.数据[道具编号]=table.loadstring(table.tostring(玩家数据[选择id].道具.数据[道具id]))--复制物品(对方id.物品[数据.道具])
                      玩家数据[id].道具.数据[道具编号].识别码 = 取唯一识别码(id)
                      if 玩家数据[选择id].道具.数据[道具id].数量 ~=nil then
                        玩家数据[id].道具.数据[道具编号].数量=math.floor(玩家数据[选择id].道具.数据[道具id].数量)
                      end
                      玩家数据[id].角色.数据.道具[道具格子]=道具编号
                      玩家数据[选择id].道具.数据[道具id] = nil
                      玩家数据[选择id].角色.数据[内容.类型][编号] = nil
                      常规提示(id,"道具提取成功")
                      道具刷新(id)
                      道具刷新(选择id,id)
                end
              elseif 文本 == "打开仓库" then
                     发送数据(玩家数据[id].连接id,6019,{角色=选择id,道具=玩家数据[选择id].道具:索要道具4(选择id,"道具")})
                     发送数据(玩家数据[id].连接id,6018,{角色=选择id,道具=玩家数据[选择id].道具仓库:索取仓库数据(1),仓库总数=#玩家数据[选择id].道具仓库.数据,召唤兽总数=#玩家数据[选择id].召唤兽仓库.数据})
                     玩家数据[id].仓库操作 = 选择id
              elseif 文本 == "仓库道具" then
                      发送数据(玩家数据[id].连接id,6019,{角色=选择id,道具=玩家数据[选择id].道具:索要道具4(选择id,内容.道具类型)})
                      发送数据(玩家数据[id].连接id,6020,{角色=选择id})--3525
                      玩家数据[id].仓库操作 = 选择id
              elseif 文本=="道具仓库" then
                      if 内容.序列>#玩家数据[选择id].道具仓库.数据 then
                        常规提示(id,"#Y/这已经是最后一页了")
                        return
                      elseif 内容.序列<1 then
                        return
                      end
                      发送数据(玩家数据[id].连接id,6021,{角色=选择id,道具=玩家数据[选择id].道具仓库:索取仓库数据(内容.序列),页数=内容.序列})--3524
                      玩家数据[id].仓库操作 = 选择id

              elseif 文本=="整理仓库" then
                     玩家数据[选择id].道具仓库.整理数据={}
                     if 内容.页数~=nil and 内容.页数>=1 and 内容.道具类型~=nil then
                        玩家数据[选择id].道具仓库:整理仓库(内容.页数,内容.道具类型,id)
                        道具刷新(选择id,id)
                     end
                     玩家数据[id].仓库操作 = 选择id
               elseif 文本=="存入仓库" then
                  玩家数据[选择id].道具仓库.存入数据={}
                  if 内容.页数~=nil and 内容.页数>=1 and 内容.类型~=nil and 内容.物品~=nil and
                     玩家数据[选择id].角色.数据[内容.类型][内容.物品]~=nil and
                     玩家数据[选择id].道具.数据[玩家数据[选择id].角色.数据[内容.类型][内容.物品]]~=nil then
                     玩家数据[选择id].道具仓库:存入仓库(内容.页数,内容.类型,内容.物品,id)
                  end
                  玩家数据[id].仓库操作 = 选择id
              elseif 文本=="取出物品" then
                  玩家数据[选择id].道具仓库.取出数据={}
                  if 内容.页数~=nil and 内容.页数>=1 and 内容.类型~=nil and 内容.物品~=nil and
                     玩家数据[选择id].道具仓库.数据[内容.页数]~=nil and 玩家数据[选择id].道具仓库.数据[内容.页数][内容.物品]~=nil then
                     玩家数据[选择id].道具仓库:取出仓库(内容.页数,内容.类型,内容.物品,id)
                  end
                  玩家数据[id].仓库操作 = 选择id
              elseif 文本=="获取宝宝仓库1" then--3526
                 发送数据(玩家数据[id].连接id,6023,{角色=选择id,召唤兽=玩家数据[选择id].召唤兽.数据,召唤兽仓库总数=#玩家数据[选择id].召唤兽仓库.数据,召唤兽仓库数据=玩家数据[选择id].召唤兽仓库:索取召唤兽仓库数据(选择id,1)})
                  玩家数据[id].仓库操作 = 选择id
              elseif 文本=="获取宝宝仓库" then
                     if 内容.序列 > #玩家数据[选择id].召唤兽仓库.数据 then
                      常规提示(id,"#Y/这已经是最后一页了")
                      return
                    elseif 内容.序列<1 then
                      return
                    end
                   发送数据(玩家数据[id].连接id,6021,{角色=选择id,召唤兽仓库数据=玩家数据[选择id].召唤兽仓库:索取召唤兽仓库数据(选择id,内容.序列),页数=内容.序列,宝宝列表=玩家数据[选择id].召唤兽.数据})--3524
                   玩家数据[id].仓库操作 = 选择id
              elseif 文本=="存入宝宝仓库" then
                    玩家数据[选择id].召唤兽仓库:存入召唤兽仓库(选择id,内容,id)
                    玩家数据[id].仓库操作 = 选择id
             elseif 文本=="取出宝宝仓库" then
                    玩家数据[选择id].召唤兽仓库:取出召唤兽仓库(选择id,内容,id)
                    玩家数据[id].仓库操作 = 选择id
             elseif 文本=="回收系统" then
                self:自动回收处理(内容,id,选择id)
             elseif 文本=="打开宠物" then
               发送数据(玩家数据[id].连接id,6008,{角色=选择id,召唤兽=玩家数据[选择id].召唤兽.数据})
             elseif 文本=="参战处理" then
                玩家数据[选择id].召唤兽:参战处理(玩家数据[id].连接id,5002,选择id,内容.序列,id)
             elseif 文本=="改名处理" then
                玩家数据[选择id].召唤兽:改名处理(玩家数据[id].连接id,5003,选择id,内容.序列,内容.名称,id)
             elseif 文本=="放生处理" then
                 玩家数据[选择id].召唤兽:放生处理(玩家数据[id].连接id,5005,选择id,内容,id)
             elseif 文本=="加点处理" then
                玩家数据[选择id].召唤兽:加点处理(玩家数据[id].连接id,5004,选择id,内容.点数,id)
             elseif 文本=="角色烹饪" then
                玩家数据[选择id].道具:烹饪处理(玩家数据[id].连接id,选择id,内容,id)
             elseif 文本=="角色炼药" then
                 玩家数据[选择id].道具:炼药处理(玩家数据[id].连接id,选择id,内容,id)
          end

   self:取角色选择信息(id,107)

   elseif 文本 == "一键退出" then
     if 玩家数据[id].角色.数据.多角色操作 == false then
        发送数据(玩家数据[id].连接id,7,"#Y/你没有多开无法一键退出!")
        return
      end
      local 临时文件=table.loadstring(读入文件([[data/]]..玩家数据[id].账号..[[/信息.txt]]))
      for n=1,#临时文件 do
         local 临时id = 临时文件[n] + 0
         if 临时id ~= id then
            if 玩家数据[临时id]~= nil then
               if 玩家数据[临时id].子角色操作~= nil then
                  发送数据(玩家数据[id].连接id,118,{角色=临时id})
                  if 玩家数据[临时id].队伍~=0 then
                      队伍处理类:退出队伍(临时id)
                  else
                      self:断开游戏(临时id)
                  end
              end
            end
         end
      end
      临时文件 = nil
      玩家数据[id].角色.数据.多角色操作 = false
      发送数据(玩家数据[id].连接id,7,"#Y/所有角色已退出")
      发送数据(玩家数据[id].连接id,114)


   end

end
function 多开系统类:自动回收处理(内容,id,选择id)
     local 序列 = 内容.序列
         if 自动回收[选择id] ==nil then
           自动回收[选择id] = {兽决=0,高级兽决=0,二级药=0,五宝=0,宝石=0,超级金柳露=0,环装=0,暗器=0,书铁=0,修练果=0,强化石=0,月华露=0,清灵净瓶=0,九转金丹=0,低级内丹=0,高级内丹=0}
        end

             if 序列 == "兽决" then
                  if 自动回收[选择id].兽决 == 1 then
                    自动回收[选择id].兽决 = 0
                  else
                    自动回收[选择id].兽决 = 1
                  end
               elseif 序列 == "高级兽决" then
                  if 自动回收[选择id].高级兽决 == 1 then
                    自动回收[选择id].高级兽决 = 0
                  else
                    自动回收[选择id].高级兽决 = 1
                  end
              elseif 序列 == "暗器" then
                if 自动回收[选择id].暗器 == 1 then
                    自动回收[选择id].暗器 = 0
                  else
                    自动回收[选择id].暗器 = 1
                  end
              elseif 序列 == "环装" then
                if 自动回收[选择id].环装 == 1 then
                    自动回收[选择id].环装 = 0
                  else
                    自动回收[选择id].环装 = 1
                  end
              elseif 序列 == "二级药" then
                if 自动回收[选择id].二级药 == 1 then
                    自动回收[选择id].二级药 = 0
                  else
                    自动回收[选择id].二级药 = 1
                  end
              elseif 序列 == "五宝" then
                if 自动回收[选择id].五宝 == 1 then
                    自动回收[选择id].五宝 = 0
                  else
                    自动回收[选择id].五宝 = 1
                  end
              elseif 序列 == "宝石" then
                if 自动回收[选择id].宝石 == 1 then
                    自动回收[选择id].宝石 = 0
                  else
                    自动回收[选择id].宝石 = 1
                  end
              elseif 序列 == "超级金柳露" then
                if 自动回收[选择id].超级金柳露 == 1 then
                    自动回收[选择id].超级金柳露 = 0
                  else
                    自动回收[选择id].超级金柳露 = 1
                  end
              elseif 序列 == "书铁" then
                if 自动回收[选择id].书铁 == 1 then
                    自动回收[选择id].书铁 = 0
                  else
                    自动回收[选择id].书铁 = 1
                  end

               elseif 序列 == "修练果" then
                if 自动回收[选择id].修练果 == 1 then
                    自动回收[选择id].修练果 = 0
                  else
                    自动回收[选择id].修练果 = 1
                  end
              elseif 序列 == "强化石" then
                if 自动回收[选择id].强化石 == 1 then
                    自动回收[选择id].强化石 = 0
                  else
                    自动回收[选择id].强化石 = 1
                  end
              elseif 序列 == "月华露" then
                if 自动回收[选择id].月华露 == 1 then
                    自动回收[选择id].月华露 = 0
                  else
                    自动回收[选择id].月华露 = 1
                  end
              elseif 序列 == "清灵净瓶" then
                if 自动回收[选择id].清灵净瓶 == 1 then
                    自动回收[选择id].清灵净瓶 = 0
                  else
                    自动回收[选择id].清灵净瓶 = 1
                  end
              elseif 序列 == "九转金丹" then
                if 自动回收[选择id].九转金丹 == 1 then
                    自动回收[选择id].九转金丹 = 0
                  else
                    自动回收[选择id].九转金丹 = 1
                  end
              elseif 序列 == "低级内丹" then
                if 自动回收[选择id].低级内丹 == 1 then
                    自动回收[选择id].低级内丹 = 0
                  else
                    自动回收[选择id].低级内丹 = 1
                  end

              elseif 序列 == "高级内丹" then
                if 自动回收[选择id].高级内丹 == 1 then
                    自动回收[选择id].高级内丹 = 0
                  else
                    自动回收[选择id].高级内丹 = 1
                  end

            end
             发送数据(玩家数据[id].连接id,6024,{角色=选择id,自动回收=自动回收[选择id],回收价格 =自定义回收价格})
        -- else
        --     常规提示(内容.数字id,"只有会员才可以使用该功能")
        --     return
        -- end

end


function 多开系统类:取角色选择信息(id,序号)--角色登陆读取数据
  local 临时文件=table.loadstring(读入文件([[data/]]..玩家数据[id].账号..[[/信息.txt]]))
  local 临时发送数据={}
  for n=1,#临时文件 do
      if 临时文件[n]~=id then
          local 读取文件=table.loadstring(读入文件([[data/]]..玩家数据[id].账号..[[/]]..临时文件[n]..[[/角色.txt]]))
          local 读取道具=table.loadstring(读入文件([[data/]]..玩家数据[id].账号..[[/]]..临时文件[n]..[[/道具.txt]]))
          local 取出装备 = {}
          local 取出锦衣 = {}
          if 读取文件.装备[3]~=nil then
              取出装备[3] = table.loadstring(table.tostring(读取道具[读取文件.装备[3]]))
          end
          if 读取文件.锦衣[1]~=nil then
            取出锦衣[1]=table.loadstring(table.tostring(读取道具[读取文件.锦衣[1]]))
          end
          if 读取文件.锦衣[2]~=nil then
            取出锦衣[2]=table.loadstring(table.tostring(读取道具[读取文件.锦衣[2]]))
          end
          if 读取文件.锦衣[3]~=nil then
            取出锦衣[3]=table.loadstring(table.tostring(读取道具[读取文件.锦衣[3]]))
          end
          if 读取文件.锦衣[4]~=nil then
            取出锦衣[4]=table.loadstring(table.tostring(读取道具[读取文件.锦衣[4]]))
          end
         临时发送数据[#临时发送数据+1]={名称=读取文件.名称,等级=读取文件.等级,模型=读取文件.模型,门派=读取文件.门派,id=读取文件.数字id,装备=取出装备,锦衣=取出锦衣,染色组=读取文件.染色组,染色方案=读取文件.染色方案,当前称谓=读取文件.当前称谓}
      end
  end
  发送数据(玩家数据[id].连接id,序号,临时发送数据)
  临时文件=nil
end


function 多开系统类:创建分角色(id,内容)
    if  内容.名称 == ""  or 内容.名称 == nil or string.find(内容.名称, "#") ~= nil or string.find(内容.名称,"/")~= nil
      or string.find(内容.名称, "@") ~= nil or string.find(内容.名称,"*")~= nil
      or string.find(内容.名称, " ") ~= nil or string.find(内容.名称,"~")~= nil
      or string.find(内容.名称, "GM") ~= nil or string.find(内容.名称,"gm")~= nil
      or string.find(内容.名称, "  ") ~= nil or string.find(内容.名称,"充值")~= nil
      or string.find(内容.名称, "游戏管理员") ~= nil or string.find(内容.名称,"·")~= nil
      or string.find(内容.名称,"小风")~= nil  or string.find(内容.名称,"群")~= nil
       or string.find(内容.名称,"裙")~= nil or string.find(内容.名称,"q")~= nil
       or string.find(内容.名称,"Q")~= nil or 判断特殊字符(内容.名称)
     then
      发送数据(id,7,"名称#Y"..内容.名称.."#/存在敏感词,请少侠换一个吧")
      return
    end

    if 敏感字判断(内容.名称,true) then
          发送数据(id,7,"名称#Y"..内容.名称.."#/存在敏感词,请少侠换一个吧")
        return
    end
    内容.名称=tostring(内容.名称)

     for n=1,#名称数据 do
      if 名称数据[n].名称==内容.名称 then
        发送数据(id,7,"#Y/这个名称已经被他人占用了，请重新再想个吧")
        return 0
      end
    end
      local 临时角色=角色处理类.创建()
      临时角色:创建角色(服务端参数.角色id,玩家数据[id].账号,内容.模型,内容.名称,内容.ip,0)
      写出文件([[tysj/名称数据.txt]],table.tostring(名称数据))
      临时角色=nil
      发送数据(玩家数据[id].连接id,112)
      发送数据(玩家数据[id].连接id,7,"#Y/角色创建成功")

end




function 多开系统类:角色加入队伍(id,对方id)
  local 角色xy={x=x,y=y}
  local 对方xy={x=0,y=0}
  对方xy.x,对方xy.y=玩家数据[对方id].角色.数据.地图数据.x,玩家数据[对方id].角色.数据.地图数据.y
  角色xy.x,角色xy.y=玩家数据[id].角色.数据.地图数据.x,玩家数据[id].角色.数据.地图数据.y
   local 队伍id= 玩家数据[id].队伍
  if 取两点距离(对方xy,角色xy)>=1000 then
    常规提示(id,"对方离你太远了~")
    return
  elseif not 队伍id or 队伍id ==0 then
      常规提示(id,"组队数据错误")
      return
  elseif not 队伍数据[队伍id] then
        常规提示(id,"组队数据错误")
        return
  elseif #队伍数据[队伍id].成员数据>=5 then
    常规提示(id,"队伍人数已满！")
    return
  elseif 玩家数据[对方id].队伍~=0 then
    常规提示(id,"对方已加入其他队伍")
    return
  end

  队伍数据[队伍id].成员数据[#队伍数据[队伍id].成员数据+1]=对方id
  玩家数据[对方id].队伍=队伍id
  玩家数据[对方id].队长=false
  if 玩家数据[id].角色.数据.飞行 and 队伍处理类:检查飞行(对方id) then
      玩家数据[对方id].角色.数据.飞行 =true
      发送数据(玩家数据[对方id].连接id,71)
      地图处理类:更新飞行(对方id,true)
  end
  广播队伍消息(队伍id,取名称(对方id).."加入了队伍")
  for n=1,#队伍数据[队伍id].成员数据 do
   队伍处理类:索取队伍信息(队伍数据[队伍id].成员数据[n],4004)
  end

end


function 多开系统类:角色进入游戏(id,数字id,是否切换)
  数字id=数字id+0
  local 临时角色连接id = 玩家数据[id].连接id+0
  local 临时角色账号 = 玩家数据[id].账号
  local 临时角色ip = 玩家数据[id].ip
  local 临时角色密码 = 玩家数据[id].密码
--    账号记录[临时角色账号..数字id]=数字id
    玩家数据[数字id]={}
    玩家数据[数字id].角色=角色处理类:创建()
    玩家数据[数字id].道具=道具处理类:创建()
    玩家数据[数字id].装备=装备处理类:创建()
    玩家数据[数字id].经脉=经脉处理类:创建()
    玩家数据[数字id].召唤兽=召唤兽处理类:创建()
--    玩家数据[数字id].每日活动=每日活动类:创建()
    玩家数据[数字id].召唤兽仓库 = 召唤兽仓库类:创建()
    玩家数据[数字id].道具仓库 = 道具仓库类:创建()
    玩家数据[数字id].加锁处理=物品加锁处理类:创建()
    玩家数据[数字id].神器=神器类:创建()            ------神器
    玩家数据[数字id].房屋=房屋处理类:创建()
    玩家数据[数字id].好友=好友处理类:创建()
    玩家数据[数字id].角色:加载数据(临时角色账号,数字id)
    玩家数据[数字id].道具:加载数据(临时角色账号,数字id)
    玩家数据[数字id].召唤兽:加载数据(临时角色账号,数字id)
    玩家数据[数字id].经脉:加载数据(数字id)
--    玩家数据[数字id].每日活动:加载数据(临时角色账号,数字id)
    玩家数据[数字id].召唤兽仓库:加载数据(临时角色账号,数字id)
    玩家数据[数字id].道具仓库:加载数据(临时角色账号,数字id)
    玩家数据[数字id].加锁处理:加载数据(临时角色账号,数字id)
    玩家数据[数字id].神器:加载数据(临时角色账号,数字id)
    玩家数据[数字id].房屋:加载数据(临时角色账号,数字id)
    玩家数据[数字id].好友:加载数据(临时角色账号,数字id)
    玩家数据[数字id].账号=临时角色账号
    玩家数据[数字id].当前频道=os.time()
    玩家数据[数字id].世界频道=os.time()
    玩家数据[数字id].传闻频道=os.time()
    玩家数据[数字id].遇怪时间=os.time()+取随机数(10,20)
    玩家数据[数字id].战斗=0
    玩家数据[数字id].队伍=0
    玩家数据[数字id].ip=临时角色ip
    玩家数据[数字id].连接id=nil
    玩家数据[数字id].商品列表=0
    玩家数据[数字id].移动数据={}
    玩家数据[数字id].最后事件=""
    玩家数据[数字id].道具操作={}
    玩家数据[数字id].自动遇怪 = nil
    --玩家数据[数字id].保存时间=os.time()+math.random(0,60)
    玩家数据[数字id].角色.数据.多角色操作 = false
    玩家数据[数字id].子角色操作 =id
    玩家数据[数字id].物品锁时间={时间=os.time(),开关=false}
    玩家数据[数字id].角色.数据.离线摆摊=nil
    玩家数据[数字id].角色.数据.地图数据.编号=玩家数据[id].角色.数据.地图数据.编号
    玩家数据[数字id].角色.数据.地图数据.x=玩家数据[id].角色.数据.地图数据.x+取随机数(10,50)
    玩家数据[数字id].角色.数据.地图数据.y=玩家数据[id].角色.数据.地图数据.y+取随机数(10,50)
    系统处理类:进入游戏数据表处理(数字id)
    系统处理类:进入游戏检测(数字id)
    地图处理类:加入玩家(数字id,玩家数据[数字id].角色.数据.地图数据.编号,玩家数据[数字id].角色.数据.地图数据.x,玩家数据[数字id].角色.数据.地图数据.y)
    if 共享货币[临时角色账号] then
        共享货币[临时角色账号]:加入玩家(数字id)
    end
    if 是否切换 == nil then
      刷新排行榜(数字id)
      系统处理类:进入事件(数字id,玩家数据[数字id].连接id)

    end

   -- collectgarbage("collect")
  end

function 多开系统类:角色更换处理(团队成员,选择id,数据)
   local 连接id = 数据.连接id
   local 账号 = 数据.账号
   local 临时ip = 数据.ip
   if 共享仓库[账号]==nil then
        共享仓库[账号]=共享仓库类:创建()
        共享仓库[账号]:加载数据(账号)
    end
    if 共享货币[账号]==nil then
        共享货币[账号]=共享货币类:创建()
        共享货币[账号]:加载数据(账号)
    end
--    账号记录[账号..选择id]=选择id
    玩家数据[选择id]={连接id=连接id}
    玩家数据[选择id].角色=角色处理类:创建(连接id)
    玩家数据[选择id].道具=道具处理类:创建(连接id)
    玩家数据[选择id].装备=装备处理类:创建()
    玩家数据[选择id].经脉=经脉处理类:创建()
    玩家数据[选择id].召唤兽=召唤兽处理类:创建()
--    玩家数据[选择id].每日活动=每日活动类:创建()
    玩家数据[选择id].召唤兽仓库 = 召唤兽仓库类:创建()
    玩家数据[选择id].道具仓库 = 道具仓库类:创建()
    玩家数据[选择id].加锁处理=物品加锁处理类:创建()
    玩家数据[选择id].神器=神器类:创建()            ------神器
    玩家数据[选择id].房屋=房屋处理类:创建()
    玩家数据[选择id].好友=好友处理类:创建()
    玩家数据[选择id].角色:加载数据(账号,选择id)
    玩家数据[选择id].道具:加载数据(账号,选择id)
    玩家数据[选择id].召唤兽:加载数据(账号,选择id)
    玩家数据[选择id].经脉:加载数据(选择id)
    --玩家数据[选择id].每日活动:加载数据(账号,选择id)
    玩家数据[选择id].召唤兽仓库:加载数据(账号,选择id)
    玩家数据[选择id].道具仓库:加载数据(账号,选择id)
    玩家数据[选择id].加锁处理:加载数据(账号,选择id)
    玩家数据[选择id].神器:加载数据(账号,选择id)
    玩家数据[选择id].房屋:加载数据(账号,选择id)
    玩家数据[选择id].好友:加载数据(账号,选择id)
    玩家数据[选择id].账号=账号
    玩家数据[选择id].当前频道=os.time()
    玩家数据[选择id].世界频道=os.time()
    玩家数据[选择id].传闻频道=os.time()
    玩家数据[选择id].遇怪时间=os.time()+取随机数(10,20)
    玩家数据[选择id].战斗=0
    玩家数据[选择id].队伍=0
    玩家数据[选择id].ip=临时ip
    玩家数据[选择id].连接id=连接id
    玩家数据[选择id].商品列表=0
    玩家数据[选择id].移动数据={}
    玩家数据[选择id].最后事件=""


    玩家数据[选择id].道具操作={}
    玩家数据[选择id].角色.数据.多角色操作 = true
    玩家数据[选择id].子角色操作 = nil
    玩家数据[选择id].物品锁时间={时间=os.time(),开关=false}
    玩家数据[选择id].自动遇怪 = nil
    共享仓库[账号]:加入玩家(选择id)
    共享货币[账号]:加入玩家(选择id)
    系统处理类:进入游戏数据表处理(选择id)
    系统处理类:进入游戏检测(选择id)
    发送数据(连接id,5,玩家数据[选择id].角色:取总数据())
    发送数据(连接id,16,玩家数据[选择id].召唤兽.数据)
    发送数据(连接id,1,{id=连接id,用户="正式用户"})
    发送数据(连接id,43,{时辰=时辰信息.当前})
    发送数据(连接id,42,玩家数据[选择id].角色.数据.快捷技能)
    发送数据(连接id,-2,选择id)
    发送数据(连接id,111)
    local 临时文件=table.loadstring(读入文件([[data/]]..账号..[[/信息.txt]]))
    local 选择角色id = 0
    for i=1,#临时文件 do
      if 选择id == 临时文件[i] then
         选择角色id = i
      end
    end
    发送数据(连接id,113,{选择角色=选择角色id})
    发送数据(连接id,86,{发送=战斗序号.收到,收到=战斗序号.发送})
    地图处理类:加入玩家(选择id,玩家数据[选择id].角色.数据.地图数据.编号,玩家数据[选择id].角色.数据.地图数据.x,玩家数据[选择id].角色.数据.地图数据.y)
    玩家数据[选择id].角色:刷新任务跟踪()
    local 建队信息 = {}
    建队信息.ip = 临时ip
    建队信息.数字id = 选择id
    建队信息.id = 选择id
    队伍处理类:创建队伍(选择id,建队信息)
    self:角色进入游戏(选择id,数据.玩家id,1)
    for n=1,#团队成员 do
        self:角色加入队伍(选择id,团队成员[n])
        if 玩家数据[团队成员[n]].子角色操作~=nil then
           玩家数据[团队成员[n]].子角色操作 = 选择id
           发送数据(玩家数据[选择id].连接id,115,{角色=团队成员[n],玩家数据=玩家数据[团队成员[n]].角色:取总数据()})
           发送数据(玩家数据[选择id].连接id,116,{角色=团队成员[n],召唤兽=玩家数据[团队成员[n]].召唤兽.数据})
        end
    end


--  玩家数据[选择id].角色.多角色操作 = true



end



function 多开系统类:断开游戏(数字id)
  if 玩家数据[数字id] == nil then
    return
  end
  if 玩家数据[数字id]~=nil  then
      if 玩家数据[数字id].战斗 and 玩家数据[数字id].战斗~=0 then
          if 战斗准备类.战斗盒子[玩家数据[数字id].战斗] then
             if not 玩家数据[数字id].观战 then
                战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置断线玩家(数字id)
                return
              else
                   战斗准备类.战斗盒子[玩家数据[数字id].战斗]:删除观战玩家(数字id)
              end
          end
      end
      if 玩家数据[数字id].交易信息~=nil then
          玩家数据[数字id].道具:取消交易(数字id)
      end

      if 玩家数据[数字id].队伍~=0 then
            if not 判断是否为空表(剑会天下.三人) then
                for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                    if 玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]]~=nil then
                        发送数据(玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]].连接id,128)
                        常规提示(队伍数据[玩家数据[数字id].队伍].成员数据[i],"#Y/因为队伍玩家离开取消了匹配！")
                    end
                end
                for n,v in pairs(剑会天下.三人) do
                    if v.id == 玩家数据[数字id].队伍 then
                        table.remove(剑会天下.三人,n)
                        break
                    end
                end
            end
            if not 判断是否为空表(剑会天下.五人) then
                for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                    if 玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]]~=nil then
                      发送数据(玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]].连接id,128)
                      常规提示(队伍数据[玩家数据[数字id].队伍].成员数据[i],"#Y/因为队伍玩家离开取消了匹配！")
                    end
                end
                for n,v in pairs(剑会天下.五人) do
                    if v.id == 玩家数据[数字id].队伍 then
                      table.remove(剑会天下.五人,n)
                      break
                    end
                end
            end
      else
          if not 判断是否为空表(剑会天下.单人) then
              for n,v in pairs(剑会天下.单人) do
                  if v.id == 数字id then
                    table.remove(剑会天下.单人,n)
                    break
                  end
              end
          end
      end
      if 玩家数据[数字id] ~= nil and  玩家数据[数字id].角色.数据.帮派数据 ~= nil and 玩家数据[数字id].角色.数据.帮派数据.编号~=nil and 玩家数据[数字id].角色.数据.帮派数据.编号>0 then
          local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
          帮派数据[帮派编号].成员数据[数字id].在线 = false
          帮派数据[帮派编号].成员数据[数字id].离线时间 = os.time()
      end
      玩家数据[数字id].角色.数据.离线时间=os.time()
      玩家数据[数字id].角色:存档()
      if 共享仓库[玩家数据[数字id].账号] then
         共享仓库[玩家数据[数字id].账号]:加入玩家(数字id,true)
      end
      if 共享货币[玩家数据[数字id].账号] then
         共享货币[玩家数据[数字id].账号]:加入玩家(数字id,true)
      end
      -- if 玩家数据[数字id].账号 == nil then
      --     玩家数据[数字id].账号 = 玩家数据[数字id].角色.数据.账号
      --     账号记录[玩家数据[数字id].账号..数字id]=nil
      -- else
      --   账号记录[玩家数据[数字id].账号..数字id]=nil --退出游戏
      -- end
      地图处理类:移除玩家(数字id)
      玩家数据[数字id]=nil
  end
end


return 多开系统类