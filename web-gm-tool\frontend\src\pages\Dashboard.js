import React, { useState, useEffect } from 'react';
import { Layout, Menu, Avatar, Dropdown, Badge, Typography, Space, Button } from 'antd';
import {
  UserOutlined,
  LogoutOutlined,
  DashboardOutlined,
  DollarOutlined,
  TeamOutlined,
  ToolOutlined,
  TrophyOutlined,
  SettingOutlined,
  ApiOutlined,
  WifiOutlined,
  DisconnectOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

import RechargePanel from '../components/RechargePanel';
import AccountPanel from '../components/AccountPanel';
import EquipmentPanel from '../components/EquipmentPanel';
import ActivityPanel from '../components/ActivityPanel';
import StatusPanel from '../components/StatusPanel';

import './Dashboard.css';

const { Header, Sider, Content } = Layout;
const { Title } = Typography;

const Dashboard = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedKey, setSelectedKey] = useState('status');
  const { userInfo, gameConnected, logout } = useAuth();

  const menuItems = [
    {
      key: 'status',
      icon: <DashboardOutlined />,
      label: '系统状态',
    },
    {
      key: 'recharge',
      icon: <DollarOutlined />,
      label: '充值操作',
    },
    {
      key: 'account',
      icon: <TeamOutlined />,
      label: '账号管理',
    },
    {
      key: 'equipment',
      icon: <ToolOutlined />,
      label: '装备管理',
    },
    {
      key: 'activity',
      icon: <TrophyOutlined />,
      label: '游戏活动',
    },
  ];

  const userMenuItems = [
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout
    }
  ];

  const renderContent = () => {
    switch (selectedKey) {
      case 'status':
        return <StatusPanel />;
      case 'recharge':
        return <RechargePanel />;
      case 'account':
        return <AccountPanel />;
      case 'equipment':
        return <EquipmentPanel />;
      case 'activity':
        return <ActivityPanel />;
      default:
        return <StatusPanel />;
    }
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        theme="dark"
        width={250}
      >
        <div className="dashboard-logo">
          <ApiOutlined style={{ fontSize: '24px', color: '#1890ff' }} />
          {!collapsed && (
            <span style={{ marginLeft: '12px', color: '#fff', fontSize: '16px', fontWeight: 'bold' }}>
              GM工具
            </span>
          )}
        </div>
        
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[selectedKey]}
          items={menuItems}
          onClick={({ key }) => setSelectedKey(key)}
        />
      </Sider>

      <Layout>
        <Header className="dashboard-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{ fontSize: '16px', width: 64, height: 64 }}
            />
            
            <div className="header-title">
              <Title level={4} style={{ margin: 0, color: '#fff' }}>
                {menuItems.find(item => item.key === selectedKey)?.label}
              </Title>
            </div>
          </div>

          <div className="header-right">
            <Space size="large">
              <Badge 
                status={gameConnected ? "success" : "error"} 
                text={
                  <span style={{ color: '#fff' }}>
                    {gameConnected ? "已连接" : "未连接"}
                  </span>
                }
              />
              
              <Dropdown
                menu={{ items: userMenuItems }}
                placement="bottomRight"
                arrow
              >
                <Space style={{ cursor: 'pointer', color: '#fff' }}>
                  <Avatar icon={<UserOutlined />} />
                  <span>{userInfo?.username}</span>
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>

        <Content className="dashboard-content">
          <div className="content-wrapper">
            {renderContent()}
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

// 导入图标
const MenuFoldOutlined = () => (
  <svg viewBox="64 64 896 896" focusable="false" data-icon="menu-fold" width="1em" height="1em" fill="currentColor" aria-hidden="true">
    <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM142.4 642.1L298.7 519a8.84 8.84 0 000-13.9L142.4 381.9c-5.8-4.6-14.4-.5-14.4 6.9v246.3c0 7.4 8.5 11.5 14.4 6.9z"></path>
  </svg>
);

const MenuUnfoldOutlined = () => (
  <svg viewBox="64 64 896 896" focusable="false" data-icon="menu-unfold" width="1em" height="1em" fill="currentColor" aria-hidden="true">
    <path d="M408 442h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8zm-8 204c0 4.4 3.6 8 8 8h480c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8H408c-4.4 0-8 3.6-8 8v56zm504-486H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zm0 632H120c-4.4 0-8 3.6-8 8v56c0 4.4 3.6 8 8 8h784c4.4 0 8-3.6 8-8v-56c0-4.4-3.6-8-8-8zM115.4 518.9L271.7 642c5.8 4.6 14.4.5 14.4-6.9V388.9c0-7.4-8.5-11.5-14.4-6.9L115.4 505.1a8.74 8.74 0 000 13.8z"></path>
  </svg>
);

export default Dashboard;
