2004-01-14  <PERSON>  <<EMAIL>>

	* configure.in: Add in AC_PREREQ(2.13)

2003-02-20  <PERSON>  <<EMAIL>>

	* configure.in: Propagate ORIGINAL_LD_FOR_MULTILIBS to
	config.status.
	* configure: Rebuilt.

2002-01-27  <PERSON>  <<EMAIL>>

	* configure.in (toolexecdir, toolexeclibdir): Set and AC_SUBST.
	Remove USE_LIBDIR conditional.
	* Makefile.am (toolexecdir, toolexeclibdir): Don't override.
	* Makefile.in, configure: Rebuilt.

Mon Aug  9 18:33:38 1999  <PERSON><PERSON>  <<EMAIL>>

	* include/Makefile.in: Rebuilt.
	* Makefile.in: Rebuilt
	* Makefile.am (toolexeclibdir): Add $(MULTISUBDIR) even for native
	builds.
	Use USE_LIBDIR.

	* configure: Rebuilt.
	* configure.in (USE_LIBDIR): Define for native builds.
	Use lowercase in configure --help explanations.

1999-08-08  <PERSON>  <<EMAIL>>

	* include/ffi.h.in (FFI_FN): Remove `...'.

1999-08-08  Anthony Green  <<EMAIL>>

	* Makefile.in: Rebuilt.
	* Makefile.am (AM_CFLAGS): Compile with -fexceptions.

	* src/x86/sysv.S: Add exception handling metadata.

