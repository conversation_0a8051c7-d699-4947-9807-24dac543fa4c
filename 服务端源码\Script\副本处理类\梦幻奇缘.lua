local 梦幻奇缘 = class()

function 梦幻奇缘:初始化()
    self.活动开关 = false
    self.仙境之门开启 = false
end

function 梦幻奇缘:开启活动()
    self:添加NPC()
    发送公告("#G(梦幻奇缘)#P仙境之门即将开启！请前往长安城寻找#R云游仙人#P领取任务，探寻失落的仙境奥秘！")
    广播消息({内容=format("#G(梦幻奇缘)#P仙境之门即将开启！请前往长安城寻找#R云游仙人#P领取任务，探寻失落的仙境奥秘！"),频道="hd"})
end

function 梦幻奇缘:关闭活动()
    发送公告("#R(梦幻奇缘)#W仙境之门已关闭，感谢各位仙友的探索！")
    广播消息({内容=format("#R(梦幻奇缘)#W仙境之门已关闭，感谢各位仙友的探索！"),频道="hd"})
end

function 梦幻奇缘:添加NPC()
    -- 云游仙人 (任务起始NPC)
    local 任务id = 取唯一识别码(400)
    任务数据[任务id] = {
        id = 任务id,
        销毁 = true,
        起始 = os.time(),
        结束 = 14400,
        名称 = "云游仙人",
        模型 = "太上老君",
        x = 230,
        y = 120,
        方向 = 0,
        地图编号 = 1501,
        地图名称 = "长安城",
        小地图名称颜色 = 6,
        类型 = 400
    }
    地图处理类:添加单位(任务id)

    -- 仙境使者 (任务引导NPC)
    任务id = 取唯一识别码(401)
    任务数据[任务id] = {
        id = 任务id,
        销毁 = true,
        起始 = os.time(),
        结束 = 14400,
        名称 = "仙境使者",
        模型 = "仙女",
        x = 85,
        y = 45,
        方向 = 0,
        地图编号 = 1146,
        地图名称 = "月宫",
        小地图名称颜色 = 6,
        类型 = 401
    }
    地图处理类:添加单位(任务id)

    -- 守护灵兽 (战斗NPC)
    任务id = 取唯一识别码(402)
    任务数据[任务id] = {
        id = 任务id,
        销毁 = true,
        起始 = os.time(),
        结束 = 14400,
        名称 = "守护灵兽",
        模型 = "超级神狗",
        x = 60,
        y = 70,
        方向 = 0,
        地图编号 = 1208,
        地图名称 = "蟠桃园",
        小地图名称颜色 = 6,
        类型 = 402
    }
    地图处理类:添加单位(任务id)

    -- 幻境之心 (最终BOSS)
    任务id = 取唯一识别码(403)
    任务数据[任务id] = {
        id = 任务id,
        销毁 = true,
        起始 = os.time(),
        结束 = 14400,
        名称 = "幻境之心",
        模型 = "蜃气妖",
        x = 45,
        y = 25,
        方向 = 0,
        地图编号 = 1226,
        地图名称 = "幻境核心",
        小地图名称颜色 = 6,
        类型 = 403
    }
    地图处理类:添加单位(任务id)
end

function 梦幻奇缘:设置副本任务(id)
    if 玩家数据[id].队伍 == 0 then
        添加最后对话(id,"此仙境探索需要三人以上组队方可进入。")
        return
    elseif 取队伍人数(id) < 3 then
        添加最后对话(id,"此仙境探索需要三人以上组队方可进入。")
        return
    elseif 活动次数查询(id,"梦幻奇缘") == false then
        添加最后对话(id,"队伍中有成员今日已探索过此仙境。")
        return
    elseif 取队伍任务(玩家数据[id].队伍, 410) then
        常规提示(id,"#Y/队伍中已有仙友领取过此任务")
        return
    end

    local 任务id = 取唯一识别码(410)
    任务数据[任务id] = {
        id = 任务id,
        起始 = os.time(),
        结束 = 14400,
        玩家id = id,
        进程 = 1,
        类型 = 410
    }
    玩家数据[id].角色:添加任务(任务id, 1)

    -- 发送任务开始提示
    添加最后对话(id, "云游仙人抚须微笑：仙友终于来了！老夫近日观测到一处失落仙境即将现世，其中蕴含上古仙灵之气...")
    添加最后对话(id, "云游仙人神色凝重：只是仙境中似有异常波动，恐有妖物作祟，还请仙友前往查探！")
end

function 梦幻奇缘:NPC对话内容(id, 名称, 地图, 任务id)
    local 对话数据 = {}

    if 任务数据[任务id] then
        local 进程 = 任务数据[任务id].进程

        -- 云游仙人 (起始NPC)
        if 地图 == 1501 and 名称 == "云游仙人" then
            if 进程 == 1 then
                对话数据.对话 = "仙友可愿助老夫探查这处失落的仙境？"
                对话数据.选项 = {"愿闻其详", "稍后再来"}
            elseif 进程 == 5 then
                对话数据.对话 = "仙友竟能净化幻境之心！看来这仙境认你为主了。"
                对话数据.选项 = {"此乃分内之事", "侥幸而已"}
            end

        -- 仙境使者 (引导NPC)
        elseif 地图 == 1146 and 名称 == "仙境使者" then
            if 进程 == 2 then
                对话数据.对话 = "仙长派来的帮手吗？请随我来，守护灵兽最近行为异常..."
                对话数据.选项 = {"请仙子带路", "稍作准备"}
            end

        -- 守护灵兽 (战斗NPC)
        elseif 地图 == 1208 and 名称 == "守护灵兽" then
            if 进程 == 3 then
                对话数据.对话 = "吼！何人胆敢擅闯仙境！"
                对话数据.选项 = {"我等受云游仙人所托", "看来只能一战了！"}
            end

        -- 幻境之心 (最终BOSS)
        elseif 地图 == 1226 and 名称 == "幻境之心" then
            if 进程 == 4 then
                对话数据.对话 = "千百年的孤寂...终于有人来了...留下来陪我吧！"
                对话数据.选项 = {"邪气侵体！需净化它！", "这力量太强，撤退！"}
            end
        end
    end

    return 对话数据
end

function 梦幻奇缘:NPC对话处理(id, 名称, 事件, 地图, 任务id)
    if not 任务数据[任务id] then return end

    local 进程 = 任务数据[任务id].进程

    -- 云游仙人 (起始NPC)
    if 地图 == 1501 and 名称 == "云游仙人" then
        if 事件 == "愿闻其详" and 进程 == 1 then
            self:更新副本任务(id, 任务id, 2)
            地图处理类:跳转地图(id, 1146, 80, 40)
            添加最后对话(id, "云游仙人挥袖施法：老夫这就送你们前往月宫仙境入口，寻找仙境使者会合！")
        end

    -- 仙境使者 (引导NPC)
    elseif 地图 == 1146 and 名称 == "仙境使者" then
        if 事件 == "请仙子带路" and 进程 == 2 then
            self:更新副本任务(id, 任务id, 3)
            地图处理类:跳转地图(id, 1208, 55, 65)
            添加最后对话(id, "仙境使者轻叹：守护灵兽本是仙境守护者，近日却被邪气侵染，请仙友小心...")
        end

    -- 守护灵兽 (战斗NPC)
    elseif 地图 == 1208 and 名称 == "守护灵兽" then
        if (事件 == "我等受云游仙人所托" or 事件 == "看来只能一战了！") and 进程 == 3 then
            战斗准备类:创建战斗(id, 200001, 任务id)
        end

    -- 幻境之心 (最终BOSS)
    elseif 地图 == 1226 and 名称 == "幻境之心" then
        if 事件 == "邪气侵体！需净化它！" and 进程 == 4 then
            战斗准备类:创建战斗(id, 200002, 任务id)
        end
    end
end

function 梦幻奇缘:任务说明(玩家id, 任务id)
    if not 任务数据[任务id] then return {"梦幻奇缘", "任务异常", "请联系管理员"} end

    local 名称 = "梦幻奇缘"
    local 说明 = ""
    local 备注 = "仙境探索"
    local 进程 = 任务数据[任务id].进程
    local 分钟 = 取分(任务数据[任务id].结束 - (os.time() - 任务数据[任务id].起始))

    if 进程 == 1 then
        说明 = "前往#G长安城(230,120)#W寻找#R云游仙人#W\n了解仙境异动详情，剩余时间#R"..分钟.."#W分钟"
    elseif 进程 == 2 then
        说明 = "在#G月宫(85,45)#W与#R仙境使者#W会合\n获取仙境情报，剩余时间#R"..分钟.."#W分钟"
    elseif 进程 == 3 then
        说明 = "调查#G蟠桃园(60,70)#W的#R守护灵兽异常#W\n剩余时间#R"..分钟.."#W分钟"
    elseif 进程 == 4 then
        说明 = "前往#G幻境核心(45,25)#W净化#R幻境之心#W\n剩余时间#R"..分钟.."#W分钟"
    elseif 进程 == 5 then
        说明 = "返回#G长安城(230,120)#W向#R云游仙人#W\n报告仙境情况，剩余时间#R"..分钟.."#W分钟"
    end

    return {名称, 说明, 备注}
end

function 梦幻奇缘:更新副本任务(id, 任务id, 进程, 时间)
    if 任务id and 任务数据[任务id] then
        if 进程 then
            任务数据[任务id].进程 = 进程
        end
        if 时间 then
            任务数据[任务id].结束 = 时间
        end
    end

    if 玩家数据[id].队伍 == 0 or not 玩家数据[id].队伍 then
        玩家数据[id].角色:刷新任务跟踪()
    else
        local 队伍id = 玩家数据[id].队伍
        for n = 1, #队伍数据[队伍id].成员数据 do
            玩家数据[队伍数据[队伍id].成员数据[n]].角色:刷新任务跟踪()
        end
    end
end

function 梦幻奇缘:战斗胜利(id组, 战斗类型, 任务id)
    if not 任务数据[任务id] then return end

    for i = 1, #id组 do
        local id = id组[i]

        if 战斗类型 == 200001 then  -- 守护灵兽战斗
            玩家数据[id].角色:添加经验(500000, "梦幻奇缘-守护灵兽")
            玩家数据[id].角色:添加储备(200000, "梦幻奇缘-守护灵兽")
            玩家数据[id].角色.数据.梦幻奇缘积分 = (玩家数据[id].角色.数据.梦幻奇缘积分 or 0) + 15
            常规提示(id, "#Y你获得了#R15#Y点仙境积分")

            -- 概率获得灵兽之魂
            if 取随机数(1, 100) <= 30 then
                玩家数据[id].道具:自定义给予道具(id, "灵兽之魂", 1)
                广播消息({内容=format("#S(梦幻奇缘)#R/%s#Y在净化守护灵兽时获得了#G灵兽之魂#Y！", 玩家数据[id].角色.数据.名称),频道="xt"})
            end

        elseif 战斗类型 == 200002 then  -- 幻境之心战斗
            玩家数据[id].角色:添加经验(1000000, "梦幻奇缘-幻境之心")
            玩家数据[id].角色:添加储备(500000, "梦幻奇缘-幻境之心")
            玩家数据[id].角色.数据.梦幻奇缘积分 = (玩家数据[id].角色.数据.梦幻奇缘积分 or 0) + 30
            常规提示(id, "#Y你获得了#R30#Y点仙境积分")

            -- 概率获得幻境结晶
            if 取随机数(1, 100) <= 15 then
                玩家数据[id].道具:自定义给予道具(id, "幻境结晶", 1)
                广播消息({内容=format("#S(梦幻奇缘)#R/%s#Y净化幻境之心时获得了至宝#G幻境结晶#Y！", 玩家数据[id].角色.数据.名称),频道="xt"})
            end

            -- 更新任务进程
            self:更新副本任务(id, 任务id, 5)
        end
    end

    -- 根据战斗类型更新任务进程
    if 战斗类型 == 200001 then
        self:更新副本任务(id组[1], 任务id, 4)
        地图处理类:跳转地图(id组[1], 1226, 40, 35)
        添加最后对话(id组[1], "仙境使者声音传来：灵兽体内邪气已除！幻境核心就在前方，千万小心...")
    end
end

function 梦幻奇缘:仙境怪物刷新()
    local 怪物列表 = {
        {名称 = "迷途小妖", 模型 = "狸", 战斗ID = 200003},
        {名称 = "幻境守卫", 模型 = "进阶锦毛貂精", 战斗ID = 200004},
        {名称 = "仙灵残影", 模型 = "画魂", 战斗ID = 200005}
    }

    local 刷新地图 = {1146, 1208, 1226}  -- 月宫、蟠桃园、幻境核心

    for _, 地图 in ipairs(刷新地图) do
        for i = 1, 取随机数(3, 5) do
            local xy = 地图处理类.地图坐标[地图]:取随机点()
            local 怪物 = 怪物列表[取随机数(1, #怪物列表)]
            local 任务id = 取唯一识别码(420)

            任务数据[任务id] = {
                id = 任务id,
                起始 = os.time(),
                结束 = 3600,  -- 1小时存在时间
                玩家id = 0,
                名称 = 怪物.名称,
                模型 = 怪物.模型,
                x = xy.x,
                y = xy.y,
                地图编号 = 地图,
                战斗显示 = true,
                地图名称 = 取地图名称(地图),
                销毁 = true,
                类型 = 420,
                战斗ID = 怪物.战斗ID
            }
            地图处理类:添加单位(任务id)
        end
    end

    广播消息({内容 = "#P(梦幻奇缘)#Y仙境中出现了各种#R迷途妖物#Y，请仙友们前往清除！", 频道 = "xt"})
end

function 梦幻奇缘:怪物对话内容(id, 类型, 标识)
    local 对话数据 = {}

    if 类型 == 420 then  -- 随机怪物
        对话数据.模型 = 任务数据[标识].模型
        对话数据.名称 = 任务数据[标识].名称

        if 任务数据[标识].战斗 then
            对话数据.对话 = "正在战斗中，请勿打扰。"
        else
            对话数据.对话 = "此处乃吾等领地，凡人速速退去！"
            对话数据.选项 = {"替天行道！", "误入此地，这就离开"}
        end
    else  -- 任务NPC
        if 任务数据[标识].战斗 then
            对话数据.对话 = "正在战斗中，请勿打扰。"
        else
            -- 根据NPC类型定制对话
            if 任务数据[标识].名称 == "守护灵兽" then
                对话数据.对话 = "吼！仙气...我要更多仙气！"
                对话数据.选项 = {"这就给你解脱", "暂避锋芒"}
            elseif 任务数据[标识].名称 == "幻境之心" then
                对话数据.对话 = "留下...永远留下...陪着我..."
                对话数据.选项 = {"邪魔退散！", "情况不妙，撤退"}
            end
        end
    end

    return 对话数据
end

function 梦幻奇缘:怪物对话处理(id, 名称, 事件, 类型, 标识)
    if 类型 == 420 then  -- 随机怪物
        if 事件 == "替天行道！" then
            战斗准备类:创建战斗(id, 任务数据[标识].战斗ID, 标识)
            任务数据[标识].战斗 = true
        end
    else  -- 任务NPC
        if 事件 == "这就给你解脱" and 名称 == "守护灵兽" then
            战斗准备类:创建战斗(id, 200001, 标识)
            任务数据[标识].战斗 = true
        elseif 事件 == "邪魔退散！" and 名称 == "幻境之心" then
            战斗准备类:创建战斗(id, 200002, 标识)
            任务数据[标识].战斗 = true
        end
    end
end

return 梦幻奇缘