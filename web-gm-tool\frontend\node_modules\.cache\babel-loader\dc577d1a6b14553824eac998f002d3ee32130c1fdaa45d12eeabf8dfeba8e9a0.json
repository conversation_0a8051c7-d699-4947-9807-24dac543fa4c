{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nexport function createContext(defaultValue) {\n  var Context = /*#__PURE__*/React.createContext(undefined);\n  var Provider = function Provider(_ref) {\n    var value = _ref.value,\n      children = _ref.children;\n    var valueRef = React.useRef(value);\n    valueRef.current = value;\n    var _React$useState = React.useState(function () {\n        return {\n          getValue: function getValue() {\n            return valueRef.current;\n          },\n          listeners: new Set()\n        };\n      }),\n      _React$useState2 = _slicedToArray(_React$useState, 1),\n      context = _React$useState2[0];\n    useLayoutEffect(function () {\n      unstable_batchedUpdates(function () {\n        context.listeners.forEach(function (listener) {\n          listener(value);\n        });\n      });\n    }, [value]);\n    return /*#__PURE__*/React.createElement(Context.Provider, {\n      value: context\n    }, children);\n  };\n  return {\n    Context: Context,\n    Provider: Provider,\n    defaultValue: defaultValue\n  };\n}\n\n/** e.g. useSelect(userContext) => user */\n\n/** e.g. useSelect(userContext, user => user.name) => user.name */\n\n/** e.g. useSelect(userContext, ['name', 'age']) => user { name, age } */\n\n/** e.g. useSelect(userContext, 'name') => user.name */\n\nexport function useContext(holder, selector) {\n  var eventSelector = useEvent(typeof selector === 'function' ? selector : function (ctx) {\n    if (selector === undefined) {\n      return ctx;\n    }\n    if (!Array.isArray(selector)) {\n      return ctx[selector];\n    }\n    var obj = {};\n    selector.forEach(function (key) {\n      obj[key] = ctx[key];\n    });\n    return obj;\n  });\n  var context = React.useContext(holder === null || holder === void 0 ? void 0 : holder.Context);\n  var _ref2 = context || {},\n    listeners = _ref2.listeners,\n    getValue = _ref2.getValue;\n  var valueRef = React.useRef();\n  valueRef.current = eventSelector(context ? getValue() : holder === null || holder === void 0 ? void 0 : holder.defaultValue);\n  var _React$useState3 = React.useState({}),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    forceUpdate = _React$useState4[1];\n  useLayoutEffect(function () {\n    if (!context) {\n      return;\n    }\n    function trigger(nextValue) {\n      var nextSelectorValue = eventSelector(nextValue);\n      if (!isEqual(valueRef.current, nextSelectorValue, true)) {\n        forceUpdate({});\n      }\n    }\n    listeners.add(trigger);\n    return function () {\n      listeners.delete(trigger);\n    };\n  }, [context]);\n  return valueRef.current;\n}", "map": {"version": 3, "names": ["_slicedToArray", "useEvent", "useLayoutEffect", "isEqual", "React", "unstable_batchedUpdates", "createContext", "defaultValue", "Context", "undefined", "Provider", "_ref", "value", "children", "valueRef", "useRef", "current", "_React$useState", "useState", "getValue", "listeners", "Set", "_React$useState2", "context", "for<PERSON>ach", "listener", "createElement", "useContext", "holder", "selector", "eventSelector", "ctx", "Array", "isArray", "obj", "key", "_ref2", "_React$useState3", "_React$useState4", "forceUpdate", "trigger", "nextValue", "nextSelectorValue", "add", "delete"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@rc-component/context/es/context.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport useEvent from \"rc-util/es/hooks/useEvent\";\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nimport isEqual from \"rc-util/es/isEqual\";\nimport * as React from 'react';\nimport { unstable_batchedUpdates } from 'react-dom';\nexport function createContext(defaultValue) {\n  var Context = /*#__PURE__*/React.createContext(undefined);\n  var Provider = function Provider(_ref) {\n    var value = _ref.value,\n      children = _ref.children;\n    var valueRef = React.useRef(value);\n    valueRef.current = value;\n    var _React$useState = React.useState(function () {\n        return {\n          getValue: function getValue() {\n            return valueRef.current;\n          },\n          listeners: new Set()\n        };\n      }),\n      _React$useState2 = _slicedToArray(_React$useState, 1),\n      context = _React$useState2[0];\n    useLayoutEffect(function () {\n      unstable_batchedUpdates(function () {\n        context.listeners.forEach(function (listener) {\n          listener(value);\n        });\n      });\n    }, [value]);\n    return /*#__PURE__*/React.createElement(Context.Provider, {\n      value: context\n    }, children);\n  };\n  return {\n    Context: Context,\n    Provider: Provider,\n    defaultValue: defaultValue\n  };\n}\n\n/** e.g. useSelect(userContext) => user */\n\n/** e.g. useSelect(userContext, user => user.name) => user.name */\n\n/** e.g. useSelect(userContext, ['name', 'age']) => user { name, age } */\n\n/** e.g. useSelect(userContext, 'name') => user.name */\n\nexport function useContext(holder, selector) {\n  var eventSelector = useEvent(typeof selector === 'function' ? selector : function (ctx) {\n    if (selector === undefined) {\n      return ctx;\n    }\n    if (!Array.isArray(selector)) {\n      return ctx[selector];\n    }\n    var obj = {};\n    selector.forEach(function (key) {\n      obj[key] = ctx[key];\n    });\n    return obj;\n  });\n  var context = React.useContext(holder === null || holder === void 0 ? void 0 : holder.Context);\n  var _ref2 = context || {},\n    listeners = _ref2.listeners,\n    getValue = _ref2.getValue;\n  var valueRef = React.useRef();\n  valueRef.current = eventSelector(context ? getValue() : holder === null || holder === void 0 ? void 0 : holder.defaultValue);\n  var _React$useState3 = React.useState({}),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    forceUpdate = _React$useState4[1];\n  useLayoutEffect(function () {\n    if (!context) {\n      return;\n    }\n    function trigger(nextValue) {\n      var nextSelectorValue = eventSelector(nextValue);\n      if (!isEqual(valueRef.current, nextSelectorValue, true)) {\n        forceUpdate({});\n      }\n    }\n    listeners.add(trigger);\n    return function () {\n      listeners.delete(trigger);\n    };\n  }, [context]);\n  return valueRef.current;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,OAAO,MAAM,oBAAoB;AACxC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,uBAAuB,QAAQ,WAAW;AACnD,OAAO,SAASC,aAAaA,CAACC,YAAY,EAAE;EAC1C,IAAIC,OAAO,GAAG,aAAaJ,KAAK,CAACE,aAAa,CAACG,SAAS,CAAC;EACzD,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;MACpBC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;IAC1B,IAAIC,QAAQ,GAAGV,KAAK,CAACW,MAAM,CAACH,KAAK,CAAC;IAClCE,QAAQ,CAACE,OAAO,GAAGJ,KAAK;IACxB,IAAIK,eAAe,GAAGb,KAAK,CAACc,QAAQ,CAAC,YAAY;QAC7C,OAAO;UACLC,QAAQ,EAAE,SAASA,QAAQA,CAAA,EAAG;YAC5B,OAAOL,QAAQ,CAACE,OAAO;UACzB,CAAC;UACDI,SAAS,EAAE,IAAIC,GAAG,CAAC;QACrB,CAAC;MACH,CAAC,CAAC;MACFC,gBAAgB,GAAGtB,cAAc,CAACiB,eAAe,EAAE,CAAC,CAAC;MACrDM,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAC/BpB,eAAe,CAAC,YAAY;MAC1BG,uBAAuB,CAAC,YAAY;QAClCkB,OAAO,CAACH,SAAS,CAACI,OAAO,CAAC,UAAUC,QAAQ,EAAE;UAC5CA,QAAQ,CAACb,KAAK,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;IACX,OAAO,aAAaR,KAAK,CAACsB,aAAa,CAAClB,OAAO,CAACE,QAAQ,EAAE;MACxDE,KAAK,EAAEW;IACT,CAAC,EAAEV,QAAQ,CAAC;EACd,CAAC;EACD,OAAO;IACLL,OAAO,EAAEA,OAAO;IAChBE,QAAQ,EAAEA,QAAQ;IAClBH,YAAY,EAAEA;EAChB,CAAC;AACH;;AAEA;;AAEA;;AAEA;;AAEA;;AAEA,OAAO,SAASoB,UAAUA,CAACC,MAAM,EAAEC,QAAQ,EAAE;EAC3C,IAAIC,aAAa,GAAG7B,QAAQ,CAAC,OAAO4B,QAAQ,KAAK,UAAU,GAAGA,QAAQ,GAAG,UAAUE,GAAG,EAAE;IACtF,IAAIF,QAAQ,KAAKpB,SAAS,EAAE;MAC1B,OAAOsB,GAAG;IACZ;IACA,IAAI,CAACC,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;MAC5B,OAAOE,GAAG,CAACF,QAAQ,CAAC;IACtB;IACA,IAAIK,GAAG,GAAG,CAAC,CAAC;IACZL,QAAQ,CAACL,OAAO,CAAC,UAAUW,GAAG,EAAE;MAC9BD,GAAG,CAACC,GAAG,CAAC,GAAGJ,GAAG,CAACI,GAAG,CAAC;IACrB,CAAC,CAAC;IACF,OAAOD,GAAG;EACZ,CAAC,CAAC;EACF,IAAIX,OAAO,GAAGnB,KAAK,CAACuB,UAAU,CAACC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACpB,OAAO,CAAC;EAC9F,IAAI4B,KAAK,GAAGb,OAAO,IAAI,CAAC,CAAC;IACvBH,SAAS,GAAGgB,KAAK,CAAChB,SAAS;IAC3BD,QAAQ,GAAGiB,KAAK,CAACjB,QAAQ;EAC3B,IAAIL,QAAQ,GAAGV,KAAK,CAACW,MAAM,CAAC,CAAC;EAC7BD,QAAQ,CAACE,OAAO,GAAGc,aAAa,CAACP,OAAO,GAAGJ,QAAQ,CAAC,CAAC,GAAGS,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACrB,YAAY,CAAC;EAC5H,IAAI8B,gBAAgB,GAAGjC,KAAK,CAACc,QAAQ,CAAC,CAAC,CAAC,CAAC;IACvCoB,gBAAgB,GAAGtC,cAAc,CAACqC,gBAAgB,EAAE,CAAC,CAAC;IACtDE,WAAW,GAAGD,gBAAgB,CAAC,CAAC,CAAC;EACnCpC,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACqB,OAAO,EAAE;MACZ;IACF;IACA,SAASiB,OAAOA,CAACC,SAAS,EAAE;MAC1B,IAAIC,iBAAiB,GAAGZ,aAAa,CAACW,SAAS,CAAC;MAChD,IAAI,CAACtC,OAAO,CAACW,QAAQ,CAACE,OAAO,EAAE0B,iBAAiB,EAAE,IAAI,CAAC,EAAE;QACvDH,WAAW,CAAC,CAAC,CAAC,CAAC;MACjB;IACF;IACAnB,SAAS,CAACuB,GAAG,CAACH,OAAO,CAAC;IACtB,OAAO,YAAY;MACjBpB,SAAS,CAACwB,MAAM,CAACJ,OAAO,CAAC;IAC3B,CAAC;EACH,CAAC,EAAE,CAACjB,OAAO,CAAC,CAAC;EACb,OAAOT,QAAQ,CAACE,OAAO;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}