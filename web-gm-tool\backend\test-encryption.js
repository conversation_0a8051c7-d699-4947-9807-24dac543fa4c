/**
 * 加密测试
 * 对比我们的加密结果和GM工具的结果
 */

const { jm, CHAR_KEY_MAP } = require('./src/protocol/encryption');

const testData = '112345*-*12345do local ret={["账号"]="888888",["密码"]="888888"} return ret end12345*-*12345';

console.log('=== 加密对比测试 ===');
console.log('原始数据:', testData);

// 我们的加密结果
const ourEncrypted = jm(testData);
console.log('\n我们的加密结果:');
console.log('长度:', ourEncrypted.length);
console.log('结果:', ourEncrypted);

// GM工具的加密结果（从日志复制）
const gmEncrypted = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';

console.log('\nGM工具的加密结果:');
console.log('长度:', gmEncrypted.length);
console.log('结果:', gmEncrypted);

console.log('\n对比结果:');
console.log('长度相同:', ourEncrypted.length === gmEncrypted.length);
console.log('内容相同:', ourEncrypted === gmEncrypted);

if (ourEncrypted !== gmEncrypted) {
    console.log('\n差异分析:');
    const ourParts = ourEncrypted.split(',');
    const gmParts = gmEncrypted.split(',');
    
    console.log('我们的部分数量:', ourParts.length);
    console.log('GM工具部分数量:', gmParts.length);
    
    const minLen = Math.min(ourParts.length, gmParts.length);
    for (let i = 0; i < minLen; i++) {
        if (ourParts[i] !== gmParts[i]) {
            console.log(`第${i}部分不同: 我们='${ourParts[i]}' GM='${gmParts[i]}'`);
            break;
        }
    }
    
    if (ourParts.length !== gmParts.length) {
        console.log(`部分数量不同: 我们=${ourParts.length} GM=${gmParts.length}`);
        console.log('我们的最后几部分:', ourParts.slice(-5));
        console.log('GM的最后几部分:', gmParts.slice(-5));
    }
}

// 检查字符映射表
console.log('\n字符映射表检查:');
console.log('映射表大小:', Object.keys(CHAR_KEY_MAP).length);
const base64Chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
const missingChars = [];
for (let char of base64Chars) {
    if (!CHAR_KEY_MAP[char]) {
        missingChars.push(char);
    }
}
console.log('缺失的字符:', missingChars);
