
function 战斗处理类:暗雷胜利奖励(id)
          local 下限,上限 = 取场景等级(self.战斗地图)
          local 奖励经验=math.floor(self.地图等级*5*(self.敌人数量))
          local 奖励参数=self:取野外等级差(self.地图等级,玩家数据[id].角色.数据.等级)
          玩家数据[id].角色:添加经验(math.floor(奖励经验*奖励参数*5),"野外")                    ------------野外怪物经验
          玩家数据[id].角色:添加法宝灵气(id,取随机数(1,3),下限,上限)
          if 玩家数据[id].角色.数据.参战信息~=nil then
             玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,奖励经验*5,id,"野外",self.地图等级)
          end
          local 地图名称=取地图名称(玩家数据[id].角色.数据.地图数据.编号)
          if 地图名称=="东海湾"  then
              if 成就数据[id].大海龟==nil then
                成就数据[id].大海龟=0
              end
              if 成就数据[id].大海龟<101 then
                 成就数据[id].大海龟=成就数据[id].大海龟+1
              end
              if 成就数据[id].大海龟 == 1 then
                  local 成就提示 = "我不是李永生"
                  local 成就提示1 = "完成1次了大海龟击杀"
                  发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
              elseif 成就数据[id].大海龟==100 then
                  local 成就提示 = "大海龟杀手-李永生"
                  local 成就提示1 = "完成100次了大海龟击杀"
                  成就数据[id].成就点 = 成就数据[id].成就点 + 1
                  玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                  常规提示(id,"#Y/恭喜你获得了1点成就")
                  发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                  玩家数据[id].角色:添加称谓("大海龟杀手")
              end
          elseif 地图名称=="江南野外"  then
                if 成就数据[id].野猪==nil then
                  成就数据[id].野猪=0
                end
                if 成就数据[id].野猪<101 then
                    成就数据[id].野猪=成就数据[id].野猪+1
                end
                if 成就数据[id].野猪 == 1 then
                    local 成就提示 = "宰的就是你"
                    local 成就提示1 = "完成1次了野猪击杀"
                    发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                elseif 成就数据[id].野猪==100 then
                      local 成就提示 = "荒漠屠夫"
                      local 成就提示1 = "完成100次了野猪击杀"
                      成就数据[id].成就点 = 成就数据[id].成就点 + 1
                      玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                      常规提示(id,"#Y/恭喜你获得了1点成就")
                      发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                 end
                 if 成就数据[id].野猪==101 then
                    玩家数据[id].角色:添加称谓("荒漠屠夫")
                    成就数据[id].野猪=成就数据[id].野猪+1
                 end
          end
            --心魔宝珠
          if 取随机数(1,100)<=10 then
            if 玩家数据[id].角色.数据.等级>=15 then
                 玩家数据[id].道具:给予道具(id,"心魔宝珠",1)
                 常规提示(id,"#Y你获得了心魔宝珠")
            end
          end
            -- 野外装备掉落
          if 取随机数(1,100)<=1 then
              野外掉落装备(id,self.地图等级)
              常规提示(id,"#Y你获得了一件#R装备\n目前挂野能获取到最高的装备只有40级")
          end
          if 玩家数据[id].角色.数据.等级>=下限 and 玩家数据[id].角色.数据.等级<=上限 and 取随机数(1,1000)<=20 then
              系统处理类:设置传说物品(id)
          end
          if 玩家数据[id].角色:取任务(6)~=0 then
              local 地图名称=取地图名称(玩家数据[id].角色.数据.地图数据.编号)
              local 任务id=玩家数据[id].角色:取任务(6)
              if 地图名称=="大雁塔一层" or 地图名称=="大雁塔二层" or 地图名称=="大雁塔三层"
                or 地图名称=="大雁塔四层" or 地图名称=="大雁塔五层" or 地图名称=="大雁塔六层"
                or 地图名称=="花果山" or 地图名称=="长寿郊外" or 地图名称=="大唐国境" or 地图名称=="大唐境外" then
                任务数据[任务id].数量=任务数据[任务id].数量-1
              end
              if 任务数据[任务id].数量<=0 then
                    任务数据[任务id]=nil
                    玩家数据[id].角色:取消任务(任务id)
                    local 经验=0
                    local 银子=0
                    local 储备=0
                    local 等级=玩家数据[id].角色.数据.等级
                    经验=等级*55
                    银子=等级*20

                    常规提示(id,"#Y/你获得了10个心魔宝珠")
                    玩家数据[id].道具:给予道具(id,"心魔宝珠",10)
                    玩家数据[id].角色:添加经验(经验,"建邺城赏金任务")
                    玩家数据[id].角色:添加银子(银子,"建邺城赏金任务",1)
                    玩家数据[id].角色:添加活跃积分(3,"建邺城赏金任务",1)
                    if 玩家数据[id].角色.数据.参战信息~=nil then
                        玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,math.floor(经验*0.35),id,"心魔宝珠",self.地图等级)
                    end
                    玩家数据[id].战斗对话={名称="皇宫护卫",模型="护卫",对话="你是否需要继续领取平定安邦任务？".."#"..取随机数(1,110),选项={"是的，我要继续平定安邦"}}
                    玩家数据[id].护卫对话=1
              end
              玩家数据[id].角色:刷新任务跟踪()
          end
                    -- 赵捕头的新手任务
          if 玩家数据[id].角色:取任务(66)~=0 then
              local 地图名称=取地图名称(玩家数据[id].角色.数据.地图数据.编号)
              local 任务id=玩家数据[id].角色:取任务(66)
              if 地图名称=="江南野外" or 地图名称=="东海湾" or 地图名称=="东海岩洞" or 地图名称=="东海海底" or 地图名称=="海底沉船" then
                任务数据[任务id].数量=任务数据[任务id].数量-1
              end
              if 任务数据[任务id].数量<=0 then
                  任务数据[任务id]=nil
                  玩家数据[id].角色:取消任务(任务id)
                  local 等级=玩家数据[id].角色.数据.等级
                  local 经验=0
                  local 银子=0
                  local 储备=0
                  经验=等级*55
                  银子=等级*20
                  玩家数据[id].角色:添加经验(经验,"建邺城新手任务")
                  玩家数据[id].角色:添加银子(银子,"建邺城新手任务",1)
                  玩家数据[id].角色:添加活跃积分(3,"建邺城新手任务",1)
                  if 玩家数据[id].角色.数据.参战信息~=nil then
                      玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,math.floor(经验*0.35),id,"心魔宝珠",self.地图等级)
                  end
                  玩家数据[id].战斗对话={名称="赵捕头",模型="男人_衙役",对话="你是否需要继续领取新手任务？".."#"..取随机数(1,110),选项={"是的，我要继续领取"}}
                  玩家数据[id].赵捕头对话=1
              end
                玩家数据[id].角色:刷新任务跟踪()
          end
end


local 完成降妖伏魔 =function(id,任务id)
          添加活动次数(id,"降妖伏魔")
          玩家数据[id].角色:添加活跃积分(1,"降妖伏魔",1)
          local 等级=玩家数据[id].角色.数据.等级
          local 经验=math.floor(等级*等级*20*任务数据[任务id].分类*0.3)
          local 银子=math.floor(等级*等级*20*任务数据[任务id].分类*0.3)
          玩家数据[id].角色:添加经验(经验,"降妖伏魔")
          玩家数据[id].角色:添加银子(银子,"降妖伏魔",1)
          if 玩家数据[id].角色.数据.参战信息~=nil then
             玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,math.floor(经验*0.35),id,"降妖")
          end
          玩家数据[id].角色:取消任务(任务id)
          if 降妖伏魔[id]==nil then
              降妖伏魔[id]=1
          end
          降妖伏魔[id]=降妖伏魔[id]+1
          if 取随机数(1,100)<=10 then
              local 名称="镇妖拘魂铃"
              玩家数据[id].道具:给予道具(id,名称,1)
              常规提示(id,"#Y/你运气爆棚,恭喜获得1个"..名称)
          end
          if 任务数据[任务id].分类==5 then
              降妖伏魔[id]=1
              local 名称="镇妖拘魂铃"
              玩家数据[id].道具:给予道具(id,名称,取随机数(5,10))
              常规提示(id,"#Y/你运气爆棚,恭喜获得5-10个"..名称)
          end
end


local 设置门派首席 =function(id)
          local 玩家门派 = 玩家数据[id].角色.数据.门派
          if 首席争霸[玩家门派].历史数据 then
              if 玩家数据[首席争霸[玩家门派].历史数据.数字id] then
                  玩家数据[首席争霸[玩家门派].历史数据.数字id].角色.数据.是否首席=nil
                  玩家数据[首席争霸[玩家门派].历史数据.数字id].角色:批量删除称谓("首席大弟子")
                  常规提示(首席争霸[玩家门派].历史数据.数字id,"#Y你的首席大弟子被夺走了!")
              else
                  local 角色数据 = 读入文件([[data/]]..首席争霸[玩家门派].历史数据.账号..[[/]]..首席争霸[玩家门派].历史数据.数字id..[[/角色.txt]])
                  角色数据 = table.loadstring(角色数据)
                  角色数据.是否首席=nil
                  写出文件([[data/]]..首席争霸[玩家门派].历史数据.账号..[[/]]..首席争霸[玩家门派].历史数据.数字id..[[/角色.txt]],table.tostring(角色数据))
              end
          end
          local 置空数据 = {"体质","魔力","力量","耐力","敏捷","锦衣","武器","染色组","副武器","BB攻击","BB防御","BB法术","BB抗法","攻击修炼",
                            "防御修炼","法术修炼","抗法修炼","宝宝数据","武器染色组","武器染色方案","经脉流派","奇经特效","奇经八脉","历史数据"}
          for i,v in ipairs(置空数据) do
                首席争霸[玩家门派][v]=nil
          end
          for i,v in ipairs(灵饰战斗属性) do
                首席争霸[玩家门派][v]=nil
          end
          local 初始数据 ={等级=150,气血=20000,魔法=20000,愤怒=150,命中=3000,伤害=4500,防御=1500,法伤=4300,法防=1300,速度=1000,躲避=100}
          for k,v in pairs(初始数据) do
                首席争霸[玩家门派][k]=v
          end
          首席争霸[玩家门派].名称 = 玩家数据[id].角色.数据.名称
          首席争霸[玩家门派].模型 = 玩家数据[id].角色.数据.模型
          首席争霸[玩家门派].气血 = 玩家数据[id].角色.数据.最大气血
          首席争霸[玩家门派].魔法 = 玩家数据[id].角色.数据.最大魔法
          首席争霸[玩家门派].锦衣 = 玩家数据[id].角色:取锦衣数据()
          首席争霸[玩家门派].奇经八脉 =DeepCopy(玩家数据[id].奇经八脉)
          首席争霸[玩家门派].奇经特效 =DeepCopy(玩家数据[id].奇经特效)
          首席争霸[玩家门派].历史数据 ={账号=玩家数据[id].账号,数字id=id}
          首席争霸[玩家门派].攻击修炼=玩家数据[id].角色.数据.修炼.攻击修炼[1]
          首席争霸[玩家门派].防御修炼=玩家数据[id].角色.数据.修炼.防御修炼[1]
          首席争霸[玩家门派].法术修炼=玩家数据[id].角色.数据.修炼.法术修炼[1]
          首席争霸[玩家门派].抗法修炼=玩家数据[id].角色.数据.修炼.抗法修炼[1]
          首席争霸[玩家门派].BB攻击=玩家数据[id].角色.数据.bb修炼.攻击控制力[1]
          首席争霸[玩家门派].BB防御=玩家数据[id].角色.数据.bb修炼.防御控制力[1]
          首席争霸[玩家门派].BB法术=玩家数据[id].角色.数据.bb修炼.法术控制力[1]
          首席争霸[玩家门派].BB抗法=玩家数据[id].角色.数据.bb修炼.抗法控制力[1]
          local 临时属性 = {"等级","命中","伤害","防御","法伤","法防","速度","躲避","体质","魔力","力量","耐力","敏捷","武器伤害","经脉流派"}
          for i,v in ipairs(临时属性) do
              if 玩家数据[id].角色.数据[v] then
                  首席争霸[玩家门派][v]=玩家数据[id].角色.数据[v]
              end
          end
          for i,v in ipairs(灵饰战斗属性) do
              if 玩家数据[id].角色.数据[v] then
                  首席争霸[玩家门派][v]=玩家数据[id].角色.数据[v]
              end
          end
          if 玩家数据[id].角色.数据.染色方案~=0 and 玩家数据[id].角色.数据.染色组 and 玩家数据[id].角色.数据.染色组~=0 and #玩家数据[id].角色.数据.染色组>0 then
              首席争霸[玩家门派].染色组=玩家数据[id].角色.数据.染色组
              首席争霸[玩家门派].染色方案=玩家数据[id].角色.数据.染色方案
          end
          if 玩家数据[id].角色.数据.装备[3] and 玩家数据[id].道具.数据[玩家数据[id].角色.数据.装备[3]] then
              首席争霸[玩家门派].武器=DeepCopy(玩家数据[id].道具.数据[玩家数据[id].角色.数据.装备[3]])
          end
          if 玩家门派=="九黎城" and  玩家数据[id].角色.数据.装备[4] and 玩家数据[id].道具.数据[玩家数据[id].角色.数据.装备[4]] and string.find(玩家数据[id].道具.数据[玩家数据[id].角色.数据.装备[4]].名称, "(坤)") then
               首席争霸[玩家门派].副武器=DeepCopy(玩家数据[id].道具.数据[玩家数据[id].角色.数据.装备[4]])
          end
          if 玩家数据[id].角色.数据.参战宝宝 and 玩家数据[id].角色.数据.参战宝宝.名称 then
              首席争霸[玩家门派].宝宝数据=DeepCopy(玩家数据[id].角色.数据.参战宝宝)
          end

          玩家数据[id].角色.数据.是否首席=true
          玩家数据[id].角色:添加称谓(玩家门派.."首席大弟子")
          常规提示(id,"恭喜你，获得了#R"..玩家门派.."首席大弟子#Y称谓！！！")
          任务处理类:加载首席单位()
end


local 抓鬼成绩记录 =function(id)
        玩家数据[id].角色.数据.捉鬼次数=玩家数据[id].角色.数据.捉鬼次数+1
        if 玩家数据[id].角色.数据.捉鬼次数>10 then
          玩家数据[id].角色.数据.捉鬼次数=1
        end
        if not 成就数据[id].抓鬼 then
            成就数据[id].抓鬼=0
        end
        if 成就数据[id].抓鬼<1001 then
            成就数据[id].抓鬼=成就数据[id].抓鬼+1
        end
        if 成就数据[id].抓鬼 == 1 then
            发送数据(玩家数据[id].连接id,149,{内容="钟馗小帮手",内容1="完成1次了抓鬼"})
        elseif 成就数据[id].抓鬼==1000 then
            成就数据[id].成就点 = 成就数据[id].成就点 + 1
            玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
            常规提示(id,"#Y/恭喜你获得了1点成就")
            发送数据(玩家数据[id].连接id,149,{内容="钟馗小帮手",内容1="完成1000次了抓鬼"})
            玩家数据[id].角色:添加称谓("僵尸道长")
        end
end
local 自动抓鬼设置 =function(id,自动)
        if not 活动次数查询(id,"抓鬼任务") then
            发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=自动})
            常规提示(id,"#Y/自动抓鬼已关闭需要请重新开启")
            玩家数据[id].自动抓鬼 = nil
        else
            玩家数据[id].自动抓鬼.进程 = 1
            玩家数据[id].自动抓鬼.开启 = true
            玩家数据[id].自动抓鬼.时间 =os.time()+2
        end
end


local 完成宝图任务=function(id)
        玩家数据[id].角色:添加储备(取随机数(1000,2000),"完成宝图任务",1)
        if 取随机数()<=50 then
            玩家数据[id].道具:给予道具(id,"藏宝图",0)
            常规提示(id,"#Y/你获得了一张藏宝图")
        end
end
local 完成封妖任务=function(id,等级)
        local 经验=qjy(等级)*10
        local 银子=qyz(等级)*5
        添加活动次数(id,"封妖战斗")
        玩家数据[id].角色:添加经验(经验,"封妖战斗",1)
        玩家数据[id].角色:添加银子(银子,"封妖战斗",1)
        玩家数据[id].角色:添加活跃积分(2,"封妖战斗",1)
        玩家数据[id].角色.数据.镇妖积分 = 玩家数据[id].角色.数据.镇妖积分 + 1
        常规提示(id,"#Y/你获得了#R/1#Y/点镇妖积分")
        if 玩家数据[id].角色.数据.参战信息~=nil then
           玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"封妖")
        end
        local 奖励参数=取随机数(1,100)
        if 奖励参数<=5 then
            玩家数据[id].道具:给予道具(id,"坐骑内丹",1)
            常规提示(id,"#Y/你获得了坐骑内丹")
        elseif 奖励参数<=20 then
                玩家数据[id].道具:给予道具(id,"超级金柳露",取随机数(1,5))
                常规提示(id,"#Y/你获得了超级金柳露")
        elseif 奖励参数<=30 then
                玩家数据[id].道具:给予道具(id,"清灵净瓶",1)
                常规提示(id,"#Y/你获得了清灵净瓶")
        elseif 奖励参数<=50 then
                玩家数据[id].道具:给予道具(id,"金柳露",取随机数(1,10))
                常规提示(id,"#Y/你获得了金柳露")
        elseif 奖励参数<=70 then
                local 名称=取强化石()
                玩家数据[id].道具:给予道具(id,名称,取随机数(1,5))
                常规提示(id,"#Y/你获得了"..名称)
        elseif 奖励参数<=90 then
                玩家数据[id].道具:给予道具(id,"海马",1)
        elseif 奖励参数<=100 then
                local 名称="金银锦盒"
                玩家数据[id].道具:给予道具(id,名称,1)
                常规提示(id,"#Y/你获得了"..名称)
        end
        if 取随机数(1,100)<=5 then
            local 名称="90-110级书铁"
            玩家数据[id].道具:给予书铁(id,{9,11})
            常规提示(id,"#Y/你获得了#R90-110级书铁")
            广播消息({内容=format("#S(封妖任务)#R/%s#Y完成封妖任务中表现优异，#W%s#Y因此获得了其赠送的#G90-110级书铁",玩家数据[id].角色.数据.名称,名称),频道="xt"})
        end
end
local 迷宫小怪奖励=function(id)
        if 取随机数()<=10 then
            玩家数据[id].道具:给予道具(id,"炼妖石",{105,105})
            常规提示(id,"#Y你获得了#R炼妖石")
        else
            玩家数据[id].角色:添加银子(500,"迷宫小怪",1)
            玩家数据[id].角色:添加经验(500,"迷宫小怪")
            常规提示(id,"#Y我有10%的几率掉落炼妖石,很可惜你没有获得\n象征性的给你点#Y经验和银子#吧!")
        end
end
local 设置门派闯关=function(id)
        local 任务id=玩家数据[id].角色:取任务(107)
        if not 任务id or 任务id==0 then return end
        local 对话门派=任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
        local 对话数据 ={名称=对话门派.."护法",模型=Q_闯关数据[对话门派].模型}
          table.remove(任务数据[任务id].闯关序列,任务数据[任务id].当前序列)
          if #任务数据[任务id].闯关序列==0 then
               对话数据.对话="恭喜你们完成了本轮门派闯关活动#1"
               取消队伍任务(任务id,107)
          else
               任务数据[任务id].当前序列=取随机数(1,#任务数据[任务id].闯关序列)
               local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
               对话数据.对话=format("你通过了本门派护法的考验，请继续前往下一关#Y/%s#W/接受考验。".."#"..取随机数(1,110),门派名称)
               刷新队伍任务跟踪(任务id)
          end
          玩家数据[id].战斗对话 = 对话数据
end

local 设置官职任务=function(id,任务id)
        任务数据[任务id].战斗次数=任务数据[任务id].战斗次数+1
        if 任务数据[任务id].战斗次数>=2 then
            任务处理类:完成官职任务(id,1)
        else
            local 地图范围={1001,1501,1092,1070,1040,1226,1208}
            local 地图=地图范围[取随机数(1,#地图范围)]
            local xy=地图处理类.地图坐标[地图]:取随机点()
            任务数据[任务id].地图编号=地图
            任务数据[任务id].地图名称=取地图名称(地图)
            任务数据[任务id].x=xy.x
            任务数据[任务id].y=xy.y
            玩家数据[id].战斗对话={名称="流氓",模型="赌徒",对话="打不过我还躲不过嘛，换个地方继续惹事#4"}
            地图处理类:添加单位(任务id)
            玩家数据[id].角色:刷新任务跟踪()
        end
end

local 完成皇宫飞贼=function(id,任务id)
        local 等级=玩家数据[id].角色.数据.等级
        local 经验=math.floor(qjy(等级)*任务数据[任务id].分类*3)
        添加活动次数(id,"皇宫飞贼")
        玩家数据[id].角色:添加活跃积分(1,"皇宫飞贼",1)
        玩家数据[id].角色:添加经验(经验,"皇宫飞贼")
        if 玩家数据[id].角色.数据.参战信息~=nil then
            玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,math.floor(经验*0.35),id,"皇宫")
        end
        玩家数据[id].角色:取消任务(任务id)
        if 皇宫飞贼[id]==nil then
            皇宫飞贼[id]=1
        end
        皇宫飞贼[id]=皇宫飞贼[id]+1
        if 取随机数()<=30 then
            local 名称="金银锦盒"
            玩家数据[id].道具:给予道具(id,名称,2)
            常规提示(id,"#Y/你获得了"..名称)
        end
        if 皇宫飞贼.贼王[id]==nil then
            皇宫飞贼.贼王[id]=0
        end
        皇宫飞贼.贼王[id]=皇宫飞贼.贼王[id]+1
end

local 镖王活动奖励=function(id,任务id,活动)  --"镖王任务" "镖王活动"
        local 难度=任务数据[任务id].难度
        if not 自定义数据 or not 自定义数据[活动]
          or not 自定义数据[活动][难度] then return end
        local 加成=1
        if 难度=="高级" then
            加成=1.25
        elseif 难度=="珍贵" then
                加成=1.5
        end
        玩家数据[id].角色:自定义银子添加(活动,加成)
        玩家数据[id].角色:添加活跃积分(5,活动,1)
        local 临时物品={}
         for i,v in ipairs(自定义数据[活动][难度]) do
           if 取随机数()<=v.概率 then
                table.insert(临时物品,v)
           end
       end
       临时物品=删除重复(临时物品)
        if 临时物品 then
            local 编号 = 取随机数(1,#临时物品)
            if 临时物品[编号] and 临时物品[编号].名称 and 临时物品[编号].数量 then
                玩家数据[id].道具:自定义给予道具(id,临时物品[编号].名称,临时物品[编号].数量,临时物品[编号].参数)
                广播消息({内容=format("#S(镖王大赛)#G/%s#Y/在押送#G%s镖银#Y/途中表现突出获得了郑镖头的赏识，因此获得了郑镖头额外奖励的#G/%s#Y/",玩家数据[id].角色.数据.名称,难度,临时物品[编号].名称),频道="xt"})
            end
        end
end


local 完成镖王活动=function(id组,任务id)
        for i,v in ipairs(id组) do
            镖王活动奖励(v,任务id,"镖王任务")
            if 任务数据[任务id].序列==6 then
                镖王活动奖励(v,任务id,"镖王活动")
                玩家数据[v].角色:取消任务(任务id)
            else
               常规提示(v,"#Y恭喜你们通过了本镖师考验，请立即前往下一个镖师处")
            end
        end
        if 任务数据[任务id].序列~=6 then
            任务数据[任务id].序列=任务数据[任务id].序列+1
            任务数据[任务id].地图=镖王数据[任务数据[任务id].序列].地图
            任务数据[任务id].y=镖王数据[任务数据[任务id].序列].y
            任务数据[任务id].x=镖王数据[任务数据[任务id].序列].x
            任务数据[任务id].地图名称=取地图名称(镖王数据[任务数据[任务id].序列].地图)
            table.remove(任务数据[任务id].完成,任务数据[任务id].战斗序列)
            刷新队伍任务跟踪(任务id)
        end
end


local 完成地煞星任务=function(任务id,id组)
        local 等级 = 任务数据[任务id].等级
        local 难度 = 任务数据[任务id].难度
        local 奖励系数 = string.format("%.2f", 等级/69)+(难度*0.1)
        for n=1,#id组 do
            local id=id组[n]
            玩家数据[id].角色:自定义银子添加("地煞任务",奖励系数)
            玩家数据[id].角色:添加活跃积分(3,"地煞星",1)
            添加活动次数(id,"地煞星")
            if 自定义数据.地煞任务 and 自定义数据.地煞任务[等级]  and 自定义数据.地煞任务[等级][难度] then
                  local 获得物品={}
                  for i=1,#自定义数据.地煞任务[等级][难度] do
                          if 取随机数()<=自定义数据.地煞任务[等级][难度][i].概率 then
                                 获得物品[#获得物品+1]=自定义数据.地煞任务[等级][难度][i]
                          end
                  end
                  获得物品=删除重复(获得物品)
                  if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                          玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         广播消息({内容=format("#S(地煞星)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                      end
                  end
            end
        end
end

local 完成生死劫挑战=function(任务id,id组)
        for n=1,#id组 do
            local id=id组[n]
            local 层数=生死劫数据[id组[n]]
            local 奖励=""
            if 玩家数据[id].角色.数据.生死劫 then
            else
              if 层数==1 then
                玩家数据[id].角色:添加称谓("生死劫·止戈")
              elseif 层数==2 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·清心")
              elseif 层数==3 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·雷霆")
              elseif 层数==4 then
               玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·惜花")
              elseif 层数==5 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·忘情")
              elseif 层数==6 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·卧龙")
              elseif 层数==7 then
               玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·天象")
              elseif 层数==8 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·轮回")
              elseif 层数==9 then
                玩家数据[id].角色:批量删除称谓("生死劫")
                玩家数据[id].角色:添加称谓("生死劫·娑罗")
              end
            end
            if 层数==1 then
               local 获得物品={}
               for i=1,#自定义数据.生死劫[1] do
                   if 取随机数()<=自定义数据.生死劫[1][i].概率 then
                      获得物品[#获得物品+1]=自定义数据.生死劫[1][i]
                   end
               end
               获得物品=删除重复(获得物品)
               if 获得物品~=nil then
                  local 取编号=取随机数(1,#获得物品)
                  if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                     玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                     奖励=获得物品[取编号].名称
                  end
               end
            elseif 层数==2 then
                local 获得物品={}
                 for i=1,#自定义数据.生死劫[2] do
                     if 取随机数()<=自定义数据.生死劫[2][i].概率 then
                        获得物品[#获得物品+1]=自定义数据.生死劫[2][i]
                     end
                 end
                 获得物品=删除重复(获得物品)
                 if 获得物品~=nil then
                    local 取编号=取随机数(1,#获得物品)
                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                       玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                       奖励=获得物品[取编号].名称
                    end
                 end
            elseif 层数==3 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[3] do
                       if 取随机数()<=自定义数据.生死劫[3][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[3][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==4 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[4] do
                       if 取随机数()<=自定义数据.生死劫[4][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[4][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==5 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[5] do
                       if 取随机数()<=自定义数据.生死劫[5][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[5][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==6 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[6] do
                       if 取随机数()<=自定义数据.生死劫[6][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[6][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==7 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[7] do
                       if 取随机数()<=自定义数据.生死劫[7][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[7][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==8 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[8] do
                       if 取随机数()<=自定义数据.生死劫[8][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[8][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
            elseif 层数==9 then
                   local 获得物品={}
                   for i=1,#自定义数据.生死劫[9] do
                       if 取随机数()<=自定义数据.生死劫[9][i].概率 then
                          获得物品[#获得物品+1]=自定义数据.生死劫[9][i]
                       end
                   end
                   获得物品=删除重复(获得物品)
                   if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                         玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                         奖励=获得物品[取编号].名称
                      end
                   end
              玩家数据[id].角色.数据.生死劫=true
            end
            广播消息({内容=format("#S(生死劫)#R/%s#Y成功完成了第#R%s#Y场生死劫挑战，丹青生对其欣赏有加，特赐#G/%s#Y以作奖励".."#57",玩家数据[id].角色.数据.名称,生死劫数据[id],奖励),频道="xt"})
            玩家数据[id].角色:添加活跃积分(5,"生死劫",1)
            生死劫数据[id]=生死劫数据[id]+1
            if 生死劫数据.次数[id]==nil then 生死劫数据.次数[id]=0 end
            生死劫数据.次数[id]=生死劫数据.次数[id]+1
            if 生死劫数据[id]==10 then 生死劫数据[id]=1 end
        end
end
local 完成天罡星任务=function(任务id,id组)
        local 等级 = 任务数据[任务id].等级
        local 难度 = 任务数据[任务id].难度
        local 奖励系数 = string.format("%.2f", 等级/69)+(难度*0.1)
        for n=1,#id组 do
            local id=id组[n]
            玩家数据[id].角色:自定义银子添加("天罡星任务",奖励系数)
            玩家数据[id].角色:添加活跃积分(3,"天罡星",1)
            添加活动次数(id,"天罡星")
            if 自定义数据.天罡星任务 and 自定义数据.天罡星任务[等级]  and 自定义数据.天罡星任务[等级][难度] then
                  local 获得物品={}
                  for i=1,#自定义数据.天罡星任务[等级][难度] do
                          if 取随机数()<=自定义数据.天罡星任务[等级][难度][i].概率 then
                                 获得物品[#获得物品+1]=自定义数据.天罡星任务[等级][难度][i]
                          end
                  end
                  获得物品=删除重复(获得物品)
                  if 获得物品~=nil then
                      local 取编号=取随机数(1,#获得物品)
                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                          玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                          广播消息({内容=format("#S(天罡星)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                      end
                  end
            end
        end
end
local 完成门派入侵=function(任务id,id组)
        for n=1,#id组 do
            local id=id组[n]
            玩家数据[id].角色:自定义银子添加("门派入侵",任务数据[任务id].难度)
            玩家数据[id].角色:添加活跃积分(5,"门派入侵",1)
            添加活动次数(id,"门派入侵")
            if 任务数据[任务id].难度 <= 2 then
                local 获得物品={}
                for i=1,#自定义数据.门派入侵[1] do
                  if 取随机数()<=自定义数据.门派入侵[1][i].概率 then
                     获得物品[#获得物品+1]=自定义数据.门派入侵[1][i]
                  end
                end
                获得物品=删除重复(获得物品)
                if 获得物品~=nil then
                    local 取编号=取随机数(1,#获得物品)
                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                       玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                       广播消息({内容=format("#S(门派入侵)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                    end
                end
            elseif 任务数据[任务id].难度 > 2 and 任务数据[任务id].难度 <= 5 then
                    local 获得物品={}
                      for i=1,#自定义数据.门派入侵[2] do
                        if 取随机数()<=自定义数据.门派入侵[2][i].概率 then
                           获得物品[#获得物品+1]=自定义数据.门派入侵[2][i]
                        end
                      end
                      获得物品=删除重复(获得物品)
                      if 获得物品~=nil then
                          local 取编号=取随机数(1,#获得物品)
                          if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                             玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                             广播消息({内容=format("#S(门派入侵)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                          end
                      end
            elseif 任务数据[任务id].难度 > 5 and 任务数据[任务id].难度 <= 7 then
                   local 获得物品={}
                    for i=1,#自定义数据.门派入侵[3] do
                      if 取随机数()<=自定义数据.门派入侵[3][i].概率 then
                         获得物品[#获得物品+1]=自定义数据.门派入侵[3][i]
                      end
                    end
                    获得物品=删除重复(获得物品)
                    if 获得物品~=nil then
                        local 取编号=取随机数(1,#获得物品)
                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                           玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                           广播消息({内容=format("#S(门派入侵)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                        end
                    end

            elseif 任务数据[任务id].难度 > 7 and 任务数据[任务id].难度 <= 9 then
                    local 获得物品={}
                      for i=1,#自定义数据.门派入侵[4] do
                        if 取随机数()<=自定义数据.门派入侵[4][i].概率 then
                           获得物品[#获得物品+1]=自定义数据.门派入侵[4][i]
                        end
                      end
                      获得物品=删除重复(获得物品)
                      if 获得物品~=nil then
                          local 取编号=取随机数(1,#获得物品)
                          if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                             玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                             广播消息({内容=format("#S(门派入侵)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                          end
                      end
            elseif 任务数据[任务id].难度 == 10 then
                   local 获得物品={}
                    for i=1,#自定义数据.门派入侵[5] do
                      if 取随机数()<=自定义数据.门派入侵[5][i].概率 then
                         获得物品[#获得物品+1]=自定义数据.门派入侵[5][i]
                      end
                    end
                    获得物品=删除重复(获得物品)
                    if 获得物品~=nil then
                        local 取编号=取随机数(1,#获得物品)
                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                           玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                           广播消息({内容=format("#S(门派入侵)#R/%s#Y经过一番激烈的战斗，最终战胜了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
                        end
                    end
            end
        end
end


local 完成天降晨星=function(任务id,id组)
        if not 任务数据[任务id] then return end
        local 进度 = 任务数据[任务id].进程
        if 任务数据[任务id] then
            for i=1,#id组 do
              local id = id组[i]
              玩家数据[id].角色:自定义银子添加("天降晨星",1)
              玩家数据[id].角色:添加活跃积分(1,"天降晨星",1)
              local 获得物品={}
                for i=1,#自定义数据.天降晨星 do
                  if 取随机数()<=自定义数据.天降晨星[i].概率 then
                     获得物品[#获得物品+1]=自定义数据.天降晨星[i]
                  end
                end
                获得物品=删除重复(获得物品)
                if 获得物品~=nil then
                    local 取编号=取随机数(1,#获得物品)
                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                        玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                        广播消息({内容=format("#S/(天降晨星)#R/%s#Y/最终赶走了#R/%s#Y/，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,Q_天降辰星[任务数据[任务id].进程].名称,获得物品[取编号].名称),频道="xt"})
                    end
                end
            end
        end
        if 任务数据[任务id].进程 >= 12 then
            for i,v in ipairs(id组) do
                玩家数据[v].角色:取消任务(任务id)
                玩家数据[v].角色:刷新任务跟踪(v)
                if 辰星数据[v] and 辰星数据.队伍[辰星数据[v].队长] then
                    辰星数据.队伍[辰星数据[v].队长].时间 = os.time() - 辰星数据.队伍[辰星数据[v].队长].开始
                end
            end
            任务数据[任务id] = nil
            local 队长id = id组[1]
            if 辰星数据[队长id] and 辰星数据.队伍[辰星数据[队长id].队长]  then
              local a,b = 取剩余分钟(辰星数据.队伍[辰星数据[队长id].队长].时间)
              广播消息({内容=string.format("#S/(游奕灵官)#R/%s#Y/的队伍完成了#R/十二元辰#Y/的的考验,共计用时#G/%s#Y/让我们祝贺他们！".."#"..取随机数(1,110),玩家数据[id组[1]].角色.数据.名称,(a.."分"..b.."秒")),频道="hd"})
              任务处理类:九耀星君(队长id)
              发送数据(玩家数据[队长id].连接id,1501,{名称="游奕灵官",模型="天兵",对话=format("恭喜你挑战成功，九耀星君听闻你的实力不错,特意下凡对你进行试炼，抓紧去寻找星君吧#Y(任务期间请勿更换队伍)。")})
            end
      else
           任务数据[任务id].进程 = 任务数据[任务id].进程 + 1
            for i=1,#id组 do
                玩家数据[id组[i]].角色:刷新任务跟踪()
            end
            发送数据(玩家数据[id组[1]].连接id,1501,{名称="游奕灵官",模型="天兵",对话=format("恭喜你挑战成功，请立即前往寻找#Y/%s#W/继续接受考验。",Q_天降辰星[任务数据[任务id].进程].名称)})
      end
end


local 完成福利宝箱=function(任务id,id组)
  if 任务数据[任务id]==nil then
    return
  end
  for n=1,#id组 do
    local id=id组[n]
      local 奖励参数=取随机数()
      if 奖励参数<=93 then
        local 名称="5W银子"
        玩家数据[id].角色:添加银子(50000,"福利宝箱",1)
        常规提示(id,"#Y/你获得了"..名称)
        --广播消息({内容=format("#S(福利宝箱)#R/%s#Y少侠开启福利宝箱时，里面居然藏着美味的海鲜，少侠口水直流，猛得咬下去，居然硌掉了大门牙，拿出来一看居然是#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
      elseif 奖励参数<=94 then
        local 名称="魔兽要诀"
        玩家数据[id].道具:给予道具(id,名称)
        常规提示(id,"#Y/你获得了"..名称)
        --广播消息({内容=format("#S(福利宝箱)#R/%s#Y少侠开启福利宝箱时，里面居然藏着美味的海鲜，少侠口水直流，猛得咬下去，居然硌掉了大门牙，拿出来一看居然是#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
      elseif 奖励参数<=95 then
        local 名称="5W银子"
        玩家数据[id].角色:添加银子(50000,"福利宝箱",1)
        常规提示(id,"#Y/你获得了"..名称)
        --广播消息({内容=format("#S(福利宝箱)#R/%s#Y少侠开启福利宝箱时，里面居然藏着美味的海鲜，少侠口水直流，猛得咬下去，居然硌掉了大门牙，拿出来一看居然是#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
      else
        local 名称="50W银子"
        玩家数据[id].角色:添加银子(500000,"福利宝箱",1)
        常规提示(id,"#Y/你获得了"..名称)
        --广播消息({内容=format("#S(福利宝箱)#R/%s#Y少侠开启福利宝箱时，里面居然藏着美味的海鲜，少侠口水直流，猛得咬下去，居然硌掉了大门牙，拿出来一看居然是#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
      end
    end
end

local 完成美食专家=function(任务id,id组)
        if 任务数据[任务id]==nil then
          return
        end
        for n=1,#id组 do
            local id=id组[n]
            玩家数据[id].角色:添加经验(100000,"美食专家",1)
            玩家数据[id].角色:添加银子(50000,"美食专家",1)
            玩家数据[id].角色:添加活跃积分(1,"美食专家",1)
            if 玩家数据[id].角色.数据.参战信息~=nil then
               玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,35000,id,"美食")
            end
            local 奖励参数=取随机数()
            if 奖励参数<=45 then
                  local 物品表={"烤肉","醉生梦死","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
                  local 名称=物品表[取随机数(1,#物品表)]
                  玩家数据[id].道具:给予道具(id,名称,1,100)
                  常规提示(id,"#Y/你获得了"..名称)
                  广播消息({内容=format("#S(美食专家)#R/%s#Y少侠打散了袭扰村民的食客，顺便扒拉食客的衣物拿去典当，居然换到了#G/%s#Y奖励。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
            elseif 奖励参数<=90 then
                    local 物品表={"金香玉","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","佛光舍利子","十香返生丸","五龙丹"}
                    local 名称=物品表[取随机数(1,#物品表)]
                    玩家数据[id].道具:给予道具(id,名称,1,150)
                    常规提示(id,"#Y/你获得了"..名称)
                    广播消息({内容=format("#S(美食专家)#R/%s#Y少侠打散了袭扰村民的食客，顺便扒拉食客的衣物拿去典当，居然换到了#G/%s#Y奖励。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
            end
        end
end

local 完成镇妖塔=function(任务id,id组)
          local 镇妖塔层数 = 镇妖塔数据[id组[1]].层数
          for n=1,#id组 do
              local id=id组[n]
              玩家数据[id].角色:自定义银子添加("镇妖塔",镇妖塔层数)
              玩家数据[id].角色:添加活跃积分(1,"勇闯镇妖塔",1)
              玩家数据[id].角色.数据.镇妖积分 = 玩家数据[id].角色.数据.镇妖积分 + 1
                常规提示(id,"#Y/你获得了#R/1#Y/点镇妖积分")


              local 获得物品={}
              for i=1,#自定义数据.镇妖塔[1] do
                  if 取随机数()<=自定义数据.镇妖塔[1][i].概率 then
                     获得物品[#获得物品+1]=自定义数据.镇妖塔[1][i]
                  end
              end
              获得物品=删除重复(获得物品)
              if 获得物品~=nil then
                 local 取编号=取随机数(1,#获得物品)
                 if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                    玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                    广播消息({内容=format("#S(勇闯镇妖塔)#R/%s#Y突破自我，勇闯镇妖塔，获得镇塔之神奖励的#G/%s#Y。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                  end
              end
              镇妖塔数据[id].层数=镇妖塔数据[id].层数+1
              if 镇妖塔数据[id].层数>=10 and 镇妖塔数据[id].层数/10==math.floor(镇妖塔数据[id].层数/10) then
                 local 获得物品={}
                  for i=1,#自定义数据.镇妖塔[2] do
                      if 取随机数()<=自定义数据.镇妖塔[2][i].概率 then
                         获得物品[#获得物品+1]=自定义数据.镇妖塔[2][i]
                      end
                  end
                  获得物品=删除重复(获得物品)
                  if 获得物品~=nil then
                     local 取编号=取随机数(1,#获得物品)
                     if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                        玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                       广播消息({内容=format("#S(勇闯镇妖塔)#G神勇无比，玩家#R"..玩家数据[id组[1]].角色.数据.名称.."#G带领队员完成了镇妖塔#Y "..镇妖塔数据[id].层数.." #G层的挑战，距离镇妖之路,距离又近了一步！获得了#S"..获得物品[取编号].名称),频道="xt"})
                      end
                  end
                 玩家数据[id].角色:批量删除称谓("镇妖塔")
                 玩家数据[id].角色:添加称谓("镇妖塔"..镇妖塔数据[id].层数.."层")
              end
          end
end


local 完成贼王的线索=function(任务id,id组)
        if 任务数据[任务id]==nil then
          return
        end
        for n=1,#id组 do
          local id=id组[n]
            local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
            玩家数据[id].角色:添加经验(50000,"贼王的线索",1)
            玩家数据[id].角色:添加银子(30000,"贼王的线索",1)
             if 玩家数据[id].角色.数据.参战信息~=nil then
               玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,7000,id,"贼王")
            end
            local 奖励参数=取随机数()
            if 奖励参数<=30 then
              local 名称=取宝石()
              玩家数据[id].道具:给予道具(id,名称,5)
              常规提示(id,"#Y/你获得了"..名称)
              广播消息({内容=format("#S(贼王的线索)#R/%s#Y少侠勇武过人，一举拿下臭名远播的贼王，获得#G/%s#Y奖励".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
            elseif 奖励参数<=33 then
              local 名称="元宵"
              玩家数据[id].道具:给予道具(id,名称,1)
              常规提示(id,"#Y/你获得了"..名称)
              广播消息({内容=format("#S(贼王的线索)#R/%s#Y少侠勇武过人，一举拿下臭名远播的贼王，获得#G/%s#Y奖励".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
            else
              local 名称="5W银子"
              玩家数据[id].角色:添加银子(50000,"贼王的线索",1)
              常规提示(id,"#Y/你获得了"..名称)
              广播消息({内容=format("#S(贼王的线索)#R/%s#Y少侠勇武过人，一举拿下臭名远播的贼王，获得#G/%s#Y奖励".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
            end
        end
end

local 完成貔貅的羁绊=function(任务id,id组)
          if 任务数据[任务id]==nil then
            return
          end
          for n=1,#id组 do
            local id=id组[n]
              玩家数据[id].角色:添加经验(30000,"貔貅的羁绊",1)
              玩家数据[id].角色:添加银子(20000,"貔貅的羁绊",1)
               if 玩家数据[id].角色.数据.参战信息~=nil then
                 玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,4500,id,"貔貅的羁绊")
              end
              local 奖励参数=取随机数()
              if 奖励参数 <= 92 then
                local 名称="元宵"
                玩家数据[id].道具:给予道具(id,名称,1)
                常规提示(id,"#Y/你获得了"..名称)
                广播消息({内容=format("#S(貔貅的羁绊)#R/%s#Y少侠与貔貅切磋武艺，刻苦努力，终于击败貔貅，获得村民奖励#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
              elseif 奖励参数 <= 93 then
                local 名称="高级藏宝图"
                玩家数据[id].道具:给予道具(id,名称,1)
                常规提示(id,"#Y/你获得了"..名称)
                广播消息({内容=format("#S(貔貅的羁绊)#R/%s#Y少侠与貔貅切磋武艺，刻苦努力，终于击败貔貅，获得村民奖励#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
              elseif 奖励参数 <= 98 then
                local 名称="藏宝图"
                玩家数据[id].道具:给予道具(id,名称,1)
                常规提示(id,"#Y/你获得了"..名称)
                广播消息({内容=format("#S(貔貅的羁绊)#R/%s#Y少侠与貔貅切磋武艺，刻苦努力，终于击败貔貅，获得村民奖励#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
              else
                local 名称="金柳露"
                玩家数据[id].道具:给予道具(id,名称,1)
                常规提示(id,"#Y/你获得了"..名称)
                广播消息({内容=format("#S(貔貅的羁绊)#R/%s#Y少侠与貔貅切磋武艺，刻苦努力，终于击败貔貅，获得村民奖励#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,名称),频道="xt"})
              end
          end

end

local 地宫排行刷新=function(id,层数)
      for i=1,15 do
          if not 地宫排行[i] or 层数 > 地宫排行[i].层数 then
              地宫排行[i]={层数=层数,名称=玩家数据[id].角色.数据.名称,等级=玩家数据[id].角色.数据.等级,门派=玩家数据[id].角色.数据.门派,id=id}
              break
          end
      end
end



local 完成雁塔地宫=function(任务id,id组)
          for i=1,#id组 do
              local id = id组[i]
              if 雁塔地宫[id] then
                  玩家数据[id].角色:自定义银子添加("雁塔地宫",雁塔地宫[id].层数)
                  雁塔地宫[id].层数 = 雁塔地宫[id].层数 + 1
                  地宫排行刷新(id,雁塔地宫[id].层数)
                  local 获得物品={}
                  for i=1,#自定义数据.雁塔地宫 do
                      if 取随机数()<=自定义数据.雁塔地宫[i].概率 then
                         获得物品[#获得物品+1]=自定义数据.雁塔地宫[i]
                      end
                  end
                  获得物品=删除重复(获得物品)
                  if 获得物品~=nil then
                        local 取编号=取随机数(1,#获得物品)
                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                            广播消息({内容=format("#S/(雁塔地宫)#R/%s#Y/带领队伍杀到了地宫第#G/%s#Y/层，获得天庭奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,雁塔地宫[id].层数,获得物品[取编号].名称),频道="xt"})
                         end
                  end
                  if math.floor(雁塔地宫[id].层数/10)==雁塔地宫[id].层数/10 then
                      玩家数据[id].道具:给予道具(id,"地宫礼包",1)
                      常规提示(id,"#Y/你获得了一个地宫礼包")
                  end
              end
          end
          广播消息({内容=format("#S/(雁塔地宫)#R/%s#Y/带领队伍杀到了地宫第#G/%s#Y/层".."#"..取随机数(1,110),玩家数据[id组[1]].角色.数据.名称,雁塔地宫[id组[1]].层数),频道="xt"})
end

local 完成封印蚩尤=function(任务id,id组)
          for i=1,#id组 do
            local id = id组[i]
              玩家数据[id].角色:自定义银子添加("蚩尤武魂",1)
              玩家数据[id].角色:添加活跃积分(1,"蚩尤武魂",1)

                local 获得物品={}
                for i=1,#自定义数据.蚩尤武魂 do
                  if 取随机数()<=自定义数据.蚩尤武魂[i].概率 then
                     获得物品[#获得物品+1]=自定义数据.蚩尤武魂[i]
                  end
                end
                获得物品=删除重复(获得物品)
                if 获得物品~=nil then
                    local 取编号=取随机数(1,#获得物品)
                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                        玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                        广播消息({内容=format("#S/(蚩尤武魂)#R/%s#Y/经过一番激烈的战斗，最终封印#R/魔神蚩尤#Y/成功，获得天庭奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                    end
                end
          end
          for i in pairs(战斗准备类.战斗盒子) do
              if 战斗准备类.战斗盒子[i].战斗类型==100223 then
                  战斗准备类.战斗盒子[i]:结束战斗处理(0,0,1)
              end
          end
          for i in pairs(战斗准备类.战斗盒子) do
                if 战斗准备类.战斗盒子[i].战斗类型==100224 then
                    战斗准备类.战斗盒子[i]:结束战斗处理(0,0,1)
                end
          end
          雁塔地宫 = {}
          地宫排行 = {}
          发送公告("#G/雁塔地宫数据已经被重置,各位玩家可以重新挑战了！！！")
          广播消息({内容="#S/(雁塔地宫)#Y/雁塔地宫数据已经被重置,各位玩家可以重新挑战了！！！",频道="xt"})
end


local 获得银子=function(id,银子,倍数,活跃,活动,任务)--id,银子,倍数,活跃,活动,任务
        if not 玩家数据[id] then return end
        if 银子 and 倍数 then
            玩家数据[id].角色:自定义银子添加(银子,倍数)
            if 活跃 then
                玩家数据[id].角色:添加活跃积分(活跃,银子,1)
            end

        end
        if 活动 then
            添加活动次数(id,活动)
        end
        if 任务 then
            玩家数据[id].角色:取消任务(任务)
        end
end

local 获得物品=function(id,物品,广播)--id,自定义数据[物品],广播
        if not 玩家数据[id] then return end
        if not 自定义数据 or not 自定义数据[物品] then
           __gge.print(true,12,"给予奖励未找到自定义数据:"..物品.."\n")
          return
        end
        local 临时物品 = {}
        for i,v in ipairs(自定义数据[物品]) do
            if 取随机数()<=v.概率 then
                table.insert(临时物品,v)
            end
        end
        临时物品=删除重复(临时物品)
        if 临时物品 then
            local 编号 = 取随机数(1,#临时物品)
            if 临时物品[编号] and 临时物品[编号].名称 and 临时物品[编号].数量 then
                 玩家数据[id].道具:自定义给予道具(id,临时物品[编号].名称,临时物品[编号].数量,临时物品[编号].参数)
                 if 广播 then
                     广播消息({内容="#S("..广播..")#R"..玩家数据[id].角色.数据.名称.."#Y完成了"..广播..",因此获得了#G"..临时物品[编号].名称.."#"..取随机数(1,110),频道="xt"})
                 end
            end
        end
end


local 完成抓鬼任务=function(id,任务id,进入玩家)
      local 倍数 = (1+玩家数据[id].角色.数据.捉鬼次数*0.2)
      获得银子(id,"抓鬼奖励",倍数,1,"抓鬼任务",任务id)--id,银子,倍数,活跃,活动,任务
      if 任务数据[任务id].变异奖励  then
          local 银子=取随机数(20000,40000)
          玩家数据[id].角色:添加银子(银子,"捉鬼触发变异",1)
          广播消息({内容=format("#S(捉鬼任务)#R/%s#Y在捉鬼任务中成功保护了#W%s#Y因此获得了其赠送的#G/%s#Y两银子".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"善良的"..任务数据[任务id].模型,银子),频道="xt"})
      end
      抓鬼成绩记录(id)
      if 玩家数据[id].角色.数据.捉鬼次数==10 then
          获得物品(id,"抓鬼奖励","捉鬼任务")--id,自定义数据[物品],广播
      end
      if 玩家数据[id].自动抓鬼 and type(玩家数据[id].自动抓鬼)=="table" and 玩家数据[id].自动抓鬼.事件 and 玩家数据[id].自动抓鬼.事件=="自动抓鬼" then
            自动抓鬼设置(id,"自动抓鬼")
      else
          if id==进入玩家 then
              玩家数据[id].战斗对话={名称="钟馗",模型="男人_钟馗",对话="少侠干得不错哟，如果你愿意继续协助我捉拿这些小鬼，我可以帮你直接传送回阴曹地府哟。",选项={"把我送回来","我不想协助你了"}}
              玩家数据[id].钟馗对话=1
          end
      end
end

local 完成鬼王任务=function(id,任务id,进入玩家)
      local 倍数 = (1+玩家数据[id].角色.数据.捉鬼次数*0.3)
      获得银子(id,"鬼王奖励",倍数,1,"抓鬼任务",任务id)--id,银子,倍数,活跃,活动,任务
      if 任务数据[任务id].变异奖励  then
          local 银子=取随机数(20000,40000)
          玩家数据[id].角色:添加银子(银子,"捉鬼触发变异",1)
          广播消息({内容=format("#S(鬼王任务)#R/%s#Y在捉鬼任务中成功保护了#W%s#Y因此获得了其赠送的#G/%s#Y两银子".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"善良的"..任务数据[任务id].模型,银子),频道="xt"})
      end
      抓鬼成绩记录(id)
      if 玩家数据[id].角色.数据.捉鬼次数==10 then
          获得物品(id,"鬼王奖励","鬼王任务")--id,自定义数据[物品],广播
      end
       if 玩家数据[id].自动抓鬼 and type(玩家数据[id].自动抓鬼)=="table" and 玩家数据[id].自动抓鬼.事件 and 玩家数据[id].自动抓鬼.事件=="自动鬼王" then
          自动抓鬼设置(id,"自动鬼王")
      else
          if id==进入玩家 then
              玩家数据[id].战斗对话={名称="黑无常",模型="黑无常",对话="少侠干得不错哟，如果你愿意继续协助我捉拿这些恶鬼，我可以帮你直接传送回阴曹地府哟。",选项={"请送我回来","我不想协助你了"}}
              玩家数据[id].黑无常对话=1
          end
      end
end





local 删除单位=function(任务id)
        if 任务id and 任务数据[任务id] then
            地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
        end
end
local 删除任务=function(任务id)
        if 任务id and 任务数据[任务id] then
            任务数据[任务id] = nil
        end
end


function 战斗处理类:战斗胜利处理(胜利id,失败id)
        local id组={}
        for k,v in pairs(self.参战玩家) do
            if v.队伍==胜利id and 玩家数据[v.id] then
                  id组[#id组+1]=v.id
                  玩家数据[v.id].道具:自动回收(v.id)
            end
        end
        if 胜利id~=0 then
              if not 玩家数据[self.进入玩家id] then print("进入战斗id玩家为空"..self.进入玩家id) return end
              local 执行删除=function(地图,任务)
                    if 地图 then
                        删除单位(self.任务id)
                    end
                    if 任务 then
                        删除任务(self.任务id)
                    end
              end
              self.玩家胜利=true
              self:剧情奖励处理(self.进入玩家id)--110002到110015 100255到100261
              self:副本奖励处理(id组)
              if self.战斗脚本 then
                  self.战斗脚本:战斗胜利(胜利id,失败id)
              elseif self.战斗类型==100001 or self.战斗类型==100007 then
                      if self.地图等级<=3 then self.地图等级=3 end
                      for i,v in ipairs(id组) do
                           self:暗雷胜利奖励(v)
                      end
                      if self.战斗类型==100007 then
                          执行删除(1,1)  --单位 任务
                      end
              elseif self.战斗类型==100002 and self.进入玩家id==任务数据[self.任务id].玩家id then --宝图强盗
                      完成宝图任务(self.进入玩家id)
                      玩家数据[self.进入玩家id].角色:取消任务(self.任务id)
                      执行删除(1,1) --单位 任务
              elseif self.战斗类型==100003 then --宝图强盗
                      玩家数据[self.进入玩家id].道具:完成宝图遇怪(self.进入玩家id)
              elseif self.战斗类型==100004 then
                      for i,v in ipairs(id组) do
                           完成封妖任务(v,任务数据[self.任务id].等级)
                      end
                      执行删除(1,1) --单位 任务
              elseif self.战斗类型==100006 then
                        游戏活动类:科举回答题目(玩家数据[self.进入玩家id].连接id,self.进入玩家id,答案,4)
              elseif self.战斗类型==100008 or self.战斗类型==100307  then
                      for i,v in ipairs(id组) do
                          if 取任务符合id(v,self.任务id) then
                              if self.战斗类型==100008 then
                                  完成抓鬼任务(v,self.任务id,self.进入玩家id)
                              else
                                  完成鬼王任务(v,self.任务id,self.进入玩家id)
                              end
                          end
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100009 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"星宿任务",1,1,"星宿")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"星宿任务","星宿")--id,自定义数据[物品],广播
                            if 取随机数()<=2 then
                                玩家数据[v].道具:给予道具(v,"三界悬赏令",1)
                                常规提示(v,"#Y你获得了三界悬赏令")
                            end
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100010  then
                      for i,v in ipairs(id组) do
                            获得银子(v,"妖魔鬼怪",1,1,"妖魔鬼怪")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"妖魔鬼怪","妖魔鬼怪")--id,自定义数据[物品],广播
                            玩家数据[v].角色.数据.妖魔积分 = 玩家数据[v].角色.数据.妖魔积分 + 1
                            常规提示(v,"#Y你获得了#R/1#Y/点妖魔积分")
                            if 取随机数()<=5 then
                                任务处理类:开启妖王1(v,玩家数据[v].角色.数据.地图数据.编号)
                            end
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100011 then
                      for i,v in ipairs(id组) do
                          if 玩家数据[v].角色:取任务(107)~=0 then
                              获得银子(v,"门派闯关",1,1,"门派闯关")--id,银子,倍数,活跃,活动,任务
                              获得物品(v,"门派闯关","门派闯关")--id,自定义数据[物品],广播
                          end
                      end
                      设置门派闯关(self.进入玩家id)
              elseif self.战斗类型==100012 then
                      任务数据[self.任务id].已战斗=true
              elseif self.战斗类型==100013 then
                      执行删除(1)--地图  任务
                      设置官职任务(self.进入玩家id,self.任务id)
              elseif self.战斗类型==100014 then
                      if 玩家数据[self.进入玩家id].角色:取道具格子()==0 then
                          常规提示(self.进入玩家id,"#Y/你的包裹已经满了，无法获得情报")
                      elseif 取随机数()<=50 then
                              玩家数据[self.进入玩家id].道具:给予道具(self.进入玩家id,"情报")
                              常规提示(self.进入玩家id,"#Y/你得到了#R/情报")
                              任务数据[self.任务id].情报=true
                              玩家数据[self.进入玩家id].角色:刷新任务跟踪()
                      end
              elseif self.战斗类型==100015 or self.战斗类型==100429 then
                      任务数据[self.任务id].巡逻=任务数据[self.任务id].巡逻+1
                      if 任务数据[self.任务id].巡逻==2 then
                          if self.战斗类型==100015 then
                              任务处理类:完成门派任务(self.进入玩家id,2)
                          else
                              任务处理类:完成文韵任务(self.进入玩家id,2)
                          end
                      else
                          常规提示(self.进入玩家id,"#Y/换个地方我继续捣乱，嘿嘿！")
                      end
              elseif self.战斗类型==100016 or self.战斗类型==100430 then
                      执行删除(1)--地图  任务
                      if self.战斗类型==100016 then
                          任务处理类:完成门派任务(self.进入玩家id,5)
                      else
                          任务处理类:完成文韵任务(self.进入玩家id,5)
                      end
              elseif self.战斗类型==100017 or self.战斗类型==100431 then
                      执行删除(1)--地图  任务
                      if self.同门死亡 then
                        玩家数据[self.进入玩家id].角色:取消任务(self.任务id)
                        任务数据[self.任务id]=nil
                        if self.战斗类型==100017 then
                              玩家数据[self.进入玩家id].角色.数据.师门次数=0
                              常规提示(self.进入玩家id,"#Y/你的师门援助任务失败了")
                          else
                                玩家数据[self.进入玩家id].角色.数据.文韵次数=0
                              常规提示(self.进入玩家id,"#Y/你的文韵墨香援助任务失败了")
                          end
                      else
                          if self.战斗类型==100017 then
                              任务处理类:完成门派任务(self.进入玩家id,6)
                          else
                              任务处理类:完成文韵任务(self.进入玩家id,6)
                          end
                      end
              elseif self.战斗类型==100018 or self.战斗类型==100432  then
                        执行删除(1)--地图  任务
                        if not 任务数据[self.任务id].乾坤袋 then
                            local xy=地图处理类.地图坐标[任务数据[self.任务id].地图编号]:取随机点()
                            任务数据[self.任务id].x,任务数据[self.任务id].y=xy.x,xy.y
                            玩家数据[self.进入玩家id].角色:刷新任务跟踪()
                            地图处理类:添加单位(self.任务id)
                        end
              elseif self.战斗类型==100019 then
                      for i,v in ipairs(id组) do
                          迷宫小怪奖励(v)
                      end
                      执行删除(1,1)
              elseif self.战斗类型==100020 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"封妖妖王",1,5,"妖王")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"封妖妖王","妖王")--id,自定义数据[物品],广播
                            玩家数据[v].角色.数据.妖魔积分 = 玩家数据[v].角色.数据.妖魔积分 + 1
                            常规提示(v,"#Y你获得了#R/1#Y/点妖魔积分")
                            玩家数据[v].角色.数据.镇妖积分 = 玩家数据[v].角色.数据.镇妖积分 + 1
                            常规提示(v,"#Y/你获得了#R/1#Y/点镇妖积分")
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100021 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"初出江湖",1,1,"初出江湖",self.任务id)--id,银子,倍数,活跃,活动,任务
                            if 玩家数据[v].角色.数据.江湖次数>=10 then
                                获得物品(v,"初出江湖","初出江湖")--id,自定义数据[物品],广播
                                玩家数据[v].角色.数据.江湖次数 = 0
                            end
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100022 then
                      for i,v in ipairs(id组) do
                            完成皇宫飞贼(v,self.任务id)
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100023 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"皇宫贼王",1,1,"皇宫飞贼贼王",self.任务id)--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"皇宫贼王","皇宫贼王")--id,自定义数据[物品],广播
                            皇宫飞贼.贼王[v]=nil
                      end
              elseif self.战斗类型==100024 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"世界BOSS",1,5,"世界BOSS")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"世界BOSS","世界BOSS")--id,自定义数据[物品],广播
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100025 then
                     完成镖王活动(id组,self.任务id)
              elseif self.战斗类型==100026 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"三界悬赏令",1,1,"三界悬赏令")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"三界悬赏令","三界悬赏令")--id,自定义数据[物品],广播
                      end
                      玩家数据[self.进入玩家id].角色:取消任务(self.任务id)
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100027 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"知了王",1,3,"知了王")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"知了王","知了王")--id,自定义数据[物品],广播
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100032 then--天庭叛逆
                      for i,v in ipairs(id组) do
                            获得银子(v,"天庭叛逆",1,3,"天庭叛逆")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"天庭叛逆","天庭叛逆")--id,自定义数据[物品],广播
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100033 then
                      for i,v in ipairs(id组) do
                            获得银子(v,"糖果派对",1,1,"糖果派对")--id,银子,倍数,活跃,活动,任务
                            获得物品(v,"糖果派对","糖果派对")--id,自定义数据[物品],广播
                      end
                      执行删除(1,1)--地图  任务
              elseif self.战斗类型==100034 then
                      任务数据[self.任务id].巡逻=任务数据[self.任务id].巡逻+1
                      if 任务数据[self.任务id].巡逻==2 then
                         任务处理类:完成青龙任务(self.任务id,self.进入玩家id,4)
                      else
                         常规提示(self.进入玩家id,"#Y/换个地方我继续捣乱，嘿嘿！")
                      end
              elseif self.战斗类型==100035 then
                    任务数据[self.任务id].巡逻=任务数据[self.任务id].巡逻+1
                    if 任务数据[self.任务id].巡逻==2 then
                       任务处理类:完成玄武任务(self.任务id,id组,4)
                    else
                       常规提示(self.进入玩家id,"#Y/换个地方我继续捣乱，嘿嘿！")
                    end
            elseif self.战斗类型==100037 then
                    完成地煞星任务(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100038 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"知了先锋",1,1,"知了先锋")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"知了先锋","知了先锋")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100039 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"创世佛屠",1,5,"创世佛屠")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"创世佛屠","创世佛屠")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100040 then
                    任务数据[self.任务id].分类=5
                    发送数据(玩家数据[胜利id].连接id,1501,{名称="大力神灵",模型="风伯",对话=format("跟你开了个小小的玩笑，千里眼刚已经找到相关的消息了，少侠可以去找他问问"),选项={"我这就去"}})
            elseif self.战斗类型==100041 then
                      任务数据[self.任务id].分类=8
                      发送数据(玩家数据[胜利id].连接id,1501,{名称="妖魔亲信",模型="蝴蝶仙子",对话=format("少侠饶命！少侠饶命！我这就送少侠去找我们老大"),选项={"速速送我过去","待我准备准备"}})
            elseif self.战斗类型==100042 then
                    任务数据[self.任务id].分类=9
                    发送数据(玩家数据[胜利id].连接id,1501,{名称="蜃妖元神",模型="炎魔神",对话=format("少侠饶命！少侠饶命！其实天马早就跑了，具体跑哪去了，我也不知道"),选项={"再敢出来吓唬人，看我不收了你"}})
            elseif self.战斗类型==100043 then
                    任务数据[self.任务id].分类=13
                    发送数据(玩家数据[胜利id].连接id,1501,{名称="周猎户",模型="男人_兰虎",对话=format("少侠饶命！少侠饶命！我这就把马儿还回去"),选项={"终于找到马儿，可以去找百兽王复命了"}})
            elseif self.战斗类型==100044 then
                    if 任务数据[self.任务id].分类==4 and self.任务id==玩家数据[胜利id].角色:取任务(308) then
                        任务处理类:完成法宝任务(self.任务id,胜利id)
                    end
            elseif self.战斗类型==100045 then
                    任务处理类:完成法宝内丹任务(self.任务id,胜利id)
            elseif self.战斗类型==100048 then
                    玩家数据[self.进入玩家id].角色:完成飞升()
            elseif self.战斗类型==100049 then
                    玩家数据[self.进入玩家id].角色:完成渡劫()
            elseif self.战斗类型==100050 then
                    玩家数据[self.进入玩家id].角色:完成入圣()
            elseif self.战斗类型==100051 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"天降灵猴",1,1,"天降灵猴")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"天降灵猴","天降灵猴")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100052 then
                    执行删除(1)--地图  任务
                    任务处理类:完成春节任务(self.进入玩家id)
            elseif self.战斗类型==100053 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"经验宝宝",1,1,"经验宝宝")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"经验宝宝","经验宝宝")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100054 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"星官",1,1,"星官")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"星官","星官")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100055 then
                    完成生死劫挑战(self.任务id,id组)
            elseif self.战斗类型==100056 then
                    完成天罡星任务(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100057 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"善恶如来",1,5,"善恶如来")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"善恶如来","善恶如来")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100058 then
                    完成门派入侵(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100059 then
                    完成天降晨星(self.任务id,id组)
            elseif self.战斗类型==100060 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"四墓灵鼠",1,1,"四墓灵鼠")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"四墓灵鼠","四墓灵鼠")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100061 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"九耀星君",1,1,"九耀星君")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"九耀星君","九耀星君")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100084 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"散财童子",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"散财童子","散财童子")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100085 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"倔强青铜",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"倔强青铜","倔强青铜")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100086 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"秩序白银",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"秩序白银","秩序白银")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100087 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"荣耀黄金",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"荣耀黄金","荣耀黄金")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100088 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"永恒钻石",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"永恒钻石","永恒钻石")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100089 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"至尊星耀",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"至尊星耀","至尊星耀")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100090 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"最强王者",1,1,"王者荣耀")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"最强王者","最强王者")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100097 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"哪吒之太乙",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"哪吒之太乙","魔童哪吒")--id,自定义数据[物品],广播
                    end
                    任务数据[self.任务id].进程=2
                    刷新队伍任务跟踪(self.任务id)
            elseif self.战斗类型==100098 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"哪吒之敖丙",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"哪吒之敖丙","魔童哪吒")--id,自定义数据[物品],广播
                    end
                    任务数据[self.任务id].进程=3
                    刷新队伍任务跟踪(self.任务id)
            elseif self.战斗类型==100099 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"哪吒之申公豹",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"哪吒之申公豹","魔童哪吒")--id,自定义数据[物品],广播
                    end
                    任务数据[self.任务id].进程=4
                    刷新队伍任务跟踪(self.任务id)
            elseif self.战斗类型==100100 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"哪吒之龙王",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"哪吒之龙王","魔童哪吒")--id,自定义数据[物品],广播
                    end
                    任务数据[self.任务id].进程=5
                    刷新队伍任务跟踪(self.任务id)
            elseif self.战斗类型==100101 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"哪吒之哪吒",1,1,nil,self.任务id)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"哪吒之哪吒","魔童哪吒")--id,自定义数据[物品],广播
                    end
                    删除任务(self.任务id)

            elseif self.战斗类型==100105 then
                  完成福利宝箱(self.任务id,id组)
                  执行删除(1,1)--地图  任务
            elseif self.战斗类型==100106 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"倾国倾城",1,1,"倾国倾城")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"倾国倾城","倾国倾城")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100107 then
                    完成美食专家(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100108 then
                    完成镇妖塔(self.任务id,id组)
            elseif self.战斗类型==100109 then
                    完成贼王的线索(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100110 then
                    完成貔貅的羁绊(self.任务id,id组)
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100120 then
                    local 主id = 任务数据[self.任务id].主任务
                    if 任务数据[主id] then
                        if 任务数据[主id].阶段<=3 then
                            任务数据[主id].成功操作 = 任务数据[主id].成功操作 + 1
                        elseif 任务数据[主id].阶段 == 4 then
                                任务数据[主id].次数 = 任务数据[主id].次数 + 1
                        end
                        任务数据[主id].刷出强盗 = nil
                    end
                    玩家数据[self.进入玩家id].角色:取消任务(self.任务id)
                    执行删除(1)--地图  任务
            elseif self.战斗类型==100122 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"捣乱年兽",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"捣乱年兽","捣乱年兽")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100123 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"年兽王",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"年兽王","年兽王")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==100124 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"邪恶年兽",1,5)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"邪恶年兽","邪恶年兽·魔")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型 >= 100136 and self.战斗类型 <= 100146 then----长安
                    嘉年华:战斗胜利(id组,self.战斗类型,self.任务id)
            elseif self.战斗类型==100158 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"会员福利",1,1)--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"会员福利","会员福利")--id,自定义数据[物品],广播
                    end
                    刷新队伍任务跟踪(self.任务id)
            elseif self.战斗类型==100159   then----彩虹
                    归墟活动:战斗胜利(self.任务id,id组)
            elseif self.战斗类型==100222 then
                    设置门派首席(self.进入玩家id)
            elseif self.战斗类型 == 100223 then
                    完成雁塔地宫(self.任务id,id组)
            elseif self.战斗类型 == 100224 then
                    完成封印蚩尤(self.任务id,id组)
            elseif self.战斗类型==100242 then
                    for i,v in ipairs(id组) do
                          完成降妖伏魔(v,self.任务id)
                    end
                    执行删除(1)--地图  任务
            elseif self.战斗类型==110001 or self.战斗类型==410005 then
                      游戏活动类:剑会天下结算处理(胜利id,失败id)
            elseif self.战斗类型==110016 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"影青龙",1,1,"圣兽残魂")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"影青龙","影青龙")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110017 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"影朱雀",1,1,"圣兽残魂")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"影朱雀","影朱雀")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110018 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"影白虎",1,1,"圣兽残魂")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"影白虎","影白虎")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110019 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"影玄武",1,1,"圣兽残魂")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"影玄武","影玄武")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110020 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"影麒麟",1,1,"圣兽残魂")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"影麒麟","影麒麟")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110021 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"新冠病毒",1,1,"新冠病毒")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"新冠病毒","新冠病毒")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110022 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"财神爷",1,1,"财神爷")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"财神爷","财神爷")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110023 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"门派师傅",1,1,"门派师傅")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"门派师傅","挑战门派师傅")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110024 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战六九",1,1,"挑战六九")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战六九","挑战69boss")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110025 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战八九",1,1,"挑战八九")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战八九","挑战89boss")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110026 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战幺零九",1,1,"挑战幺零九")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战幺零九","挑战109boss")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110027 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战幺二九",1,1,"挑战幺二九")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战幺二九","挑战129boss")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110028 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战幺五零",1,1,"挑战幺五零")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战幺五零","挑战150boss")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==110029 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"仙缘环任务",1,1,"仙缘活动")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"仙缘环任务","仙缘任务")--id,自定义数据[物品],广播
                          玩家数据[v].角色.数据.仙缘积分 = 玩家数据[v].角色.数据.仙缘积分 + 1
                          常规提示(v,"#Y你获得了#R1#Y点仙缘积分")
                    end

                    执行删除(1,1)--地图  任务
            elseif (self.战斗类型>=110030 and self.战斗类型<=110037)
                  or self.战斗类型==110039 or self.战斗类型==110040 then
                      local 后缀 = {"仙缘任务一","仙缘任务二","仙缘任务三","仙缘任务四",
                                    "仙缘任务五","仙缘任务六","仙缘任务七","仙缘任务八"}
                      local 积分 = self.战斗类型-110030+1
                      local 传入 = 后缀[积分]
                      if self.战斗类型==110039 then
                          传入 = "仙缘任务九"
                          积分 = 9
                      elseif self.战斗类型==110040 then
                          传入 = "仙缘任务十"
                          积分 = 10
                      end
                      获得银子(self.进入玩家id,传入,1,1,"仙缘活动",self.任务id)--id,银子,倍数,活跃,活动,任务
                      获得物品(self.进入玩家id,传入,"仙缘任务")--id,自定义数据[物品],广播
                      玩家数据[self.进入玩家id].角色.数据.仙缘积分 = 玩家数据[self.进入玩家id].角色.数据.仙缘积分 + 积分
                      常规提示(self.进入玩家id,"#Y你获得了#R"..积分.."#Y点仙缘积分")
                      执行删除(1,1)--地图  任务
            elseif self.战斗类型==110038 then
                    玩家数据[self.进入玩家id].角色.数据.跑镖=os.time()+取随机数(80,130)
            elseif self.战斗类型==110041 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"十二生肖",1,1,"十二生肖")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"十二生肖","十二生肖")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110042 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"桐人",1,1,"桐人")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"桐人","桐人")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110043 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"魔化桐人",1,1,"魔化桐人")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"魔化桐人","魔化桐人")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110044 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"混世魔王",1,1,"混世魔王")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"混世魔王","混世魔王")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110045 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"万象福",1,2,"万象福")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"万象福","万象福")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110046 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"新春快乐",1,2,"新春快乐")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"新春快乐","新春快乐")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型==110047 then--
                    for i,v in ipairs(id组) do
                          获得银子(v,"小小盲僧",1,2,"小小盲僧")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"小小盲僧","小小盲僧")--id,自定义数据[物品],广播
                    end
                    执行删除(1,1)--地图  任务
            elseif self.战斗类型>=130002 and self.战斗类型<=130029  then----彩虹
                  彩虹争霸:战斗胜利(self.任务id,id组,self.战斗类型)

            elseif self.战斗类型 >= 130030 and self.战斗类型 <= 130036  then
                    local 后缀 = {"轮回境一层","轮回境二层","轮回境三层","轮回境四层",
                                    "轮回境五层","轮回境六层","轮回境七层"}
                    local 传入 = 后缀[self.战斗类型-130030+1]
                    for i,v in ipairs(id组) do
                          获得银子(v,传入,1,1,"轮回境")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,传入,"轮回境")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型 == 130037 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"童子之力",1,30,"童子之力")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"童子之力","童子之力")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==130040 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"副本BOSS",1,30,"副本BOSS")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"副本BOSS","副本BOSS")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==130041 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"挑战GM",1,30,"挑战GM")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"挑战GM","挑战GM")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型==130042 then
                    for i,v in ipairs(id组) do
                          获得银子(v,"罗刹鬼将",1,30,"罗刹鬼将")--id,银子,倍数,活跃,活动,任务
                          获得物品(v,"罗刹鬼将","罗刹鬼将")--id,自定义数据[物品],广播
                    end
            elseif self.战斗类型>=130087 and self.战斗类型<=130096  then----长安
                    长安保卫战:战斗胜利(id组,self.战斗类型,self.任务id)
            elseif self.战斗类型==200005 then
        --           游戏活动类:首席争霸战斗处理(胜利id,失败id)
            elseif self.战斗类型==200006 then--胜利
                    帮战活动类:战斗胜利(胜利id,失败id)
            elseif self.战斗类型==200004 then---比武胜利
                    英雄大会:战斗胜利(胜利id,失败id)
            elseif self.战斗类型==200008 then
                    if 玩家数据[胜利id].队伍 == 0 or 玩家数据[胜利id].队长 then
                        玩家数据[胜利id].角色.数据.强P开关 = nil
                        发送数据(玩家数据[胜利id].连接id,94)
                        地图处理类:更改强PK(胜利id)
                        if 玩家数据[胜利id].角色.数据.PK开关 ~= nil then
                            发送数据(玩家数据[胜利id].连接id,93,{开关=true})
                            地图处理类:更改PK(胜利id,true)
                        end
                    end
        -----------------------------------------------------------------------------------------
            end
        end
end



function 战斗处理类:副本奖励处理(id组)
          local 执行删除=function(地图,任务)
                if 地图 then
                    删除单位(self.任务id)
                end
                if 任务 then
                    删除任务(self.任务id)
                end
          end
          if self.战斗类型==100028 then
                local 副本id=任务数据[self.任务id].副本id
                副本数据.乌鸡国.进行[副本id].木妖数量=副本数据.乌鸡国.进行[副本id].木妖数量+1
                if 副本数据.乌鸡国.进行[副本id].木妖数量>14 then
                    副本数据.乌鸡国.进行[副本id].进程=2
                    任务处理类:设置乌鸡国副本(副本id)
                end
                for i,v in ipairs(id组) do
                    玩家数据[v].角色:添加经验(50000,"乌鸡国木妖",1)
                    玩家数据[v].角色:添加银子(30000,"乌鸡国木妖",1)
                    常规提示(v,"#Y您的副本进度已经更新")
                    玩家数据[v].角色:刷新任务跟踪()
                end
                执行删除(1,1)--地图  任务
          elseif self.战斗类型==100029 then
                  添加最后对话(胜利id,format("哎哟别打了，我不敢搞事了！"))
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.乌鸡国.进行[副本id].序列[任务数据[self.任务id].序列]=true
                  local 通过=0
                  for n=1,3 do
                    if 副本数据.乌鸡国.进行[副本id].序列[n] then 通过=通过+1 end
                  end
                  if 通过>=3 then
                      副本数据.乌鸡国.进行[副本id].进程=4
                      任务处理类:设置乌鸡国副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                      玩家数据[v].角色:添加经验(50000,"乌鸡国",1)
                      玩家数据[v].角色:添加银子(30000,"乌鸡国",1)
                      玩家数据[v].角色:刷新任务跟踪()
                      if 通过>=3 then
                        常规提示(v,"#Y您的副本进度已经更新")
                      end
                  end
                  执行删除(1,1)--地图  任务
          elseif self.战斗类型==100030 then
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.乌鸡国.进行[副本id].鬼祟数量=副本数据.乌鸡国.进行[副本id].鬼祟数量+1
                  if 副本数据.乌鸡国.进行[副本id].鬼祟数量>4 then
                      副本数据.乌鸡国.进行[副本id].进程=5
                      任务处理类:设置乌鸡国副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                      玩家数据[v].角色:添加经验(50000,"乌鸡国小妖",1)
                      玩家数据[v].角色:添加银子(30000,"乌鸡国小妖",1)
                      常规提示(v,"#Y您的副本进度已经更新")
                      玩家数据[v].角色:刷新任务跟踪()
                  end
                  执行删除(1,1)--地图  任务
          elseif self.战斗类型==100031 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  for i,v in ipairs(id组) do
                      if 任务数据[self.任务id].真假 then
                          任务处理类:完成乌鸡国奖励(v)
                          玩家数据[v].战斗=0
                          玩家数据[v].角色:取消任务(玩家数据[v].角色:取任务(120))
                          副本数据.乌鸡国.进行[v]=nil
                          添加最后对话(v,format("恭喜你一次性就猜对真国王了!"))
                      else
                          添加最后对话(v,format("你没猜对,我是假国王,需要还战斗一次"))
                      end
                  end
                  if 任务数据[self.任务id].真假 then
                      地图处理类:跳转地图(id组[1],1001,288,85)
                  end
          elseif self.战斗类型==100066 then
                    local 副本id=任务数据[self.任务id].副本id
                    副本数据.车迟斗法.进行[副本id].车迟贡品=副本数据.车迟斗法.进行[副本id].车迟贡品+1
                    for i,v in ipairs(id组) do
                        玩家数据[v].角色:刷新任务跟踪()
                    end
                    执行删除(1,1)--地图  任务
                    添加最后对话(self.进入玩家id,format("少侠饶命！我再也不敢了"))
          elseif self.战斗类型==100067 then
                    添加最后对话(self.进入玩家id,format("哎哟别打了，我不敢搞事了！"))
                    local 副本id=任务数据[self.任务id].副本id
                    副本数据.车迟斗法.进行[副本id].序列[任务数据[self.任务id].序列]=true
                    local 通过=0
                    for n=1,3 do
                      if 副本数据.车迟斗法.进行[副本id].序列[n] then 通过=通过+1 end
                    end
                    if 通过>=3 then
                        副本数据.车迟斗法.进行[副本id].进程=5
                        任务处理类:设置车迟斗法副本(副本id)
                    end
                    for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(500000,"车迟斗法",1)
                        玩家数据[v].角色:添加银子(200000,"车迟斗法",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        if 通过>=3 then
                            常规提示(v,"#Y您的副本进度已经更新")
                        end
                    end
                    执行删除(1,1)--地图  任务
          elseif self.战斗类型==100068 then
                      添加最后对话(self.进入玩家id,format("幸亏少侠帮忙，不然我们就促成大错了！！！"))
                      local 副本id=任务数据[self.任务id].副本id
                      副本数据.车迟斗法.进行[副本id].序列[任务数据[self.任务id].序列]=true
                      local 通过=0
                      for n=1,3 do
                        if 副本数据.车迟斗法.进行[副本id].序列[n] then 通过=通过+1 end
                      end
                      if 通过>=3 then
                          副本数据.车迟斗法.进行[副本id].进程=8
                          任务处理类:设置车迟斗法副本(副本id)
                      end
                      for i,v in ipairs(id组) do
                          玩家数据[v].角色:添加经验(750000,"车迟斗法",1)
                          玩家数据[v].角色:添加银子(250000,"车迟斗法",1)
                          玩家数据[v].角色:刷新任务跟踪()
                          if 通过>=3 then
                            常规提示(v,"#Y您的副本进度已经更新")
                          end
                      end
                      执行删除(1,1)--地图  任务
          elseif self.战斗类型==100069 then
                      添加最后对话(self.进入玩家id,format("你输了，你先动的，先动的是王八！！！"))
                      local 副本id=任务数据[self.任务id].副本id
                      副本数据.车迟斗法.进行[副本id].序列[任务数据[self.任务id].序列]=true
                      local 通过=0
                      for n=1,3 do
                        if 副本数据.车迟斗法.进行[副本id].序列[n] then 通过=通过+1 end
                      end
                      if 通过>=3 then
                          副本数据.车迟斗法.进行[副本id].进程=8
                          任务处理类:设置车迟斗法副本(副本id)
                      end
                      for i,v in ipairs(id组) do
                          玩家数据[v].角色:添加经验(1000000,"车迟斗法",1)
                          玩家数据[v].角色:添加银子(500000,"车迟斗法",1)
                          玩家数据[v].角色:刷新任务跟踪()
                          if 通过>=3 then
                            常规提示(v,"#Y您的副本进度已经更新")
                          end
                      end
                      执行删除(1,1)--地图  任务
          elseif self.战斗类型==100070 then
                    执行删除(1)--地图  任务
                    local 副本id=任务数据[self.任务id].副本id
                    for i,v in ipairs(id组) do
                        任务处理类:完成车迟国奖励(v)
                        玩家数据[v].战斗=0
                        玩家数据[v].角色:取消任务(玩家数据[v].角色:取任务(130))
                        副本数据.车迟斗法.进行[v]=nil
                    end
                    地图处理类:跳转地图(id组[1],1070,122,144)
                    添加最后对话(self.进入玩家id,format("少侠饶命！我再也不敢了"))
          elseif self.战斗类型==100112 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(150)
                  local 副本id = 任务数据[任务id].副本id
                  任务数据[任务id].装潢=任务数据[任务id].装潢+2
                  玩家数据[self.进入玩家id].采摘木材=nil
                  副本数据.水陆大会.进行[副本id].装潢=副本数据.水陆大会.进行[副本id].装潢+2
                  常规提示(self.进入玩家id,"#Y完成了采摘木材，装潢任务进度+2")
                  if 副本数据.水陆大会.进行[副本id].装潢>=10 and 副本数据.水陆大会.进行[副本id].邀请>=10 then
                      for i,v in pairs(地图处理类.地图单位[6024]) do
                          if 地图处理类.地图单位[6024][i].名称 == "蟠桃树" and 任务数据[地图处理类.地图单位[6024][i].id].副本id == 副本id then
                              地图处理类:删除单位(6024,i)
                          end
                      end
                      副本数据.水陆大会.进行[副本id].进程=2
                      任务处理类:设置水陆大会副本(副本id)
                      发送数据(玩家数据[self.进入玩家id].连接id,1501,{名称="道场督僧",模型="男人_方丈",对话="感谢少侠为水陆大会建设做出的贡献，道场已经建设完毕"})
                  end
                  玩家数据[self.进入玩家id].角色:刷新任务跟踪()
          elseif self.战斗类型==100113 then
                  执行删除(1)--地图  任务
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(150)
                  local 副本id=任务数据[任务id].副本id
                  任务数据[任务id].装潢=任务数据[任务id].装潢+2
                  玩家数据[self.进入玩家id].驱逐泼猴=nil
                  副本数据.水陆大会.进行[副本id].装潢=副本数据.水陆大会.进行[副本id].装潢+2
                  玩家数据[self.进入玩家id].角色:取消任务(玩家数据[self.进入玩家id].角色:取任务(352))
                  常规提示(self.进入玩家id,"#Y完成了驱逐泼猴，装潢任务进度+2")
                  if 副本数据.水陆大会.进行[副本id].装潢>=10 and 副本数据.水陆大会.进行[副本id].邀请>=10 then
                      for i,v in pairs(地图处理类.地图单位[6024]) do
                          if 地图处理类.地图单位[6024][i].名称 == "蟠桃树" and 任务数据[地图处理类.地图单位[6024][i].id].副本id == 副本id then
                            地图处理类:删除单位(6024,i)
                          end
                      end
                      副本数据.水陆大会.进行[副本id].进程=2
                      任务处理类:设置水陆大会副本(副本id)
                      发送数据(玩家数据[self.进入玩家id].连接id,1501,{名称="道场督僧",模型="男人_方丈",对话="感谢少侠为水陆大会建设做出的贡献，道场已经建设完毕"})
                  end
                  玩家数据[self.进入玩家id].角色:刷新任务跟踪()
          elseif self.战斗类型==100116 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(150)
                  local 副本id=任务数据[任务id].副本id
                  副本数据.水陆大会.进行[副本id].击败翼虎=true
                  if 副本数据.水陆大会.进行[副本id].击败翼虎 and 副本数据.水陆大会.进行[副本id].击败蝰蛇 then
                    副本数据.水陆大会.进行[副本id].进程=6
                    任务处理类:设置水陆大会副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"水陆大会",1)
                        玩家数据[v].角色:添加银子(50000,"水陆大会",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100117 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(150)
                  local 副本id=任务数据[任务id].副本id
                  副本数据.水陆大会.进行[副本id].击败蝰蛇=true
                  if 副本数据.水陆大会.进行[副本id].击败翼虎 and 副本数据.水陆大会.进行[副本id].击败蝰蛇 then
                    副本数据.水陆大会.进行[副本id].进程=6
                    任务处理类:设置水陆大会副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"水陆大会",1)
                        玩家数据[v].角色:添加银子(50000,"水陆大会",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100118 then
                  执行删除(1)--地图  任务
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(150)
                  local 副本id=任务数据[任务id].副本id
                  if 任务数据[self.任务id].名称=="巡山小妖" then
                      副本数据.水陆大会.进行[副本id].击败小妖=副本数据.水陆大会.进行[副本id].击败小妖+1
                  elseif 任务数据[self.任务id].名称=="上古妖兽头领" then
                          副本数据.水陆大会.进行[副本id].击败头领=副本数据.水陆大会.进行[副本id].击败头领+1
                  elseif 任务数据[self.任务id].名称=="妖将军" then
                          副本数据.水陆大会.进行[副本id].击败将军=副本数据.水陆大会.进行[副本id].击败将军+1
                  else
                      if 任务数据[self.任务id].名称=="魑魅" then
                          副本数据.水陆大会.进行[副本id].击败魑魅=true
                      else
                          副本数据.水陆大会.进行[副本id].击败魍魉=true
                      end
                  end
                  if 副本数据.水陆大会.进行[副本id].击败魑魅 and 副本数据.水陆大会.进行[副本id].击败魍魉 then
                    副本数据.水陆大会.进行[副本id].进程=8
                    任务处理类:设置水陆大会副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                         玩家数据[v].角色:添加经验(400000,"水陆大会",1)
                         玩家数据[v].角色:添加银子(100000,"水陆大会",1)
                         玩家数据[v].角色:刷新任务跟踪()
                         常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100125 then
                  执行删除(1)--地图  任务
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(160)
                  local 副本id=任务数据[任务id].副本id
                  副本数据.通天河.进行[副本id].进程=4
                  任务处理类:设置通天河副本(副本id)
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"通天河",1)
                        玩家数据[v].角色:添加银子(50000,"通天河",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100126 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.通天河.进行[副本id].河妖=副本数据.通天河.进行[副本id].河妖+1
                  if 副本数据.通天河.进行[副本id].河妖>=5 then
                      玩家数据[self.进入玩家id].战斗=0
                      地图处理类:跳转地图(self.进入玩家id,6028,27,22)
                      副本数据.通天河.进行[副本id].进程=6
                      任务处理类:设置通天河副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"通天河",1)
                        玩家数据[v].角色:添加银子(50000,"通天河",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100127 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.通天河.进行[副本id].散财童子=true
                  if 副本数据.通天河.进行[副本id].散财童子 and 副本数据.通天河.进行[副本id].黑熊精 then
                    副本数据.通天河.进行[副本id].进程=7
                    任务处理类:设置通天河副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"通天河",1)
                        玩家数据[v].角色:添加银子(50000,"通天河",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100128 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.通天河.进行[副本id].黑熊精=true
                  if 副本数据.通天河.进行[副本id].散财童子 and 副本数据.通天河.进行[副本id].黑熊精 then
                    副本数据.通天河.进行[副本id].进程=7
                    任务处理类:设置通天河副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(100000,"通天河",1)
                        玩家数据[v].角色:添加银子(50000,"通天河",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100129 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  副本数据.通天河.进行[副本id].五色竹条=副本数据.通天河.进行[副本id].五色竹条+5
                  if 副本数据.通天河.进行[副本id].五色竹条>=50 then
                    玩家数据[self.进入玩家id].战斗=0
                    地图处理类:跳转地图(self.进入玩家id,6029,103,59)
                    副本数据.通天河.进行[副本id].进程=9
                    任务处理类:设置通天河副本(副本id)
                  end
                  for i,v in ipairs(id组) do
                         玩家数据[v].角色:添加经验(100000,"通天河",1)
                         玩家数据[v].角色:添加银子(50000,"通天河",1)
                         玩家数据[v].角色:刷新任务跟踪()
                         常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100134 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  玩家数据[self.进入玩家id].战斗=0
                  地图处理类:跳转地图(self.进入玩家id,6030,106,38)
                  副本数据.通天河.进行[副本id].进程=10
                  任务处理类:设置通天河副本(副本id)
                  for i,v in ipairs(id组) do
                        玩家数据[v].角色:添加经验(800000,"通天河",1)
                        玩家数据[v].角色:添加银子(200000,"通天河",1)
                        玩家数据[v].角色:刷新任务跟踪()
                        常规提示(v,"#Y您的副本进度已经更新")
                  end
          elseif self.战斗类型==100135 then
                  执行删除(1)--地图  任务
                  local 副本id=任务数据[self.任务id].副本id
                  for i,v in ipairs(id组) do
                      任务处理类:完成通天河奖励(v)
                      玩家数据[v].战斗=0
                      玩家数据[v].角色:取消任务(玩家数据[v].角色:取任务(160))
                      副本数据.通天河.进行[v]=nil
                  end
                  玩家数据[self.进入玩家id].战斗=0
                  副本数据.通天河.进行[副本id]=nil
                  地图处理类:跳转地图(self.进入玩家id,1070,52,74)
          elseif self.战斗类型==100147 then --大闹浇水

          elseif self.战斗类型==100148 then --大闹除虫
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  if 任务数据[任务id].三大力士.修桃力士 > 0 then
                      任务数据[任务id].三大力士.修桃力士 = 任务数据[任务id].三大力士.修桃力士 - 1
                      if 任务数据[任务id].完成三大力士 == nil then
                          任务数据[任务id].完成三大力士 = 0
                      end
                      任务数据[任务id].完成三大力士 = 任务数据[任务id].完成三大力士 + 1
                      if 任务数据[任务id].三大力士.修桃力士 <= 0 then
                          取消队伍任务(任务id,183)
                      else
                          玩家数据[self.进入玩家id].角色:取消任务(玩家数据[self.进入玩家id].角色:取任务(183))
                      end
                      if 任务数据[任务id].完成三大力士 >= 15 then
                          local 副本id = 任务数据[任务id].副本id
                          副本数据.大闹天宫.进行[副本id].进程 = 2
                          任务处理类:设置大闹天宫副本(副本id)
                      end
                      玩家数据[self.进入玩家id].角色:添加经验(100000,"大闹天宫除虫")
                      玩家数据[self.进入玩家id].角色:添加储备(500000,"大闹天宫除虫",1)
                      刷新队伍任务跟踪(任务id)
                  end
          elseif self.战斗类型==100149 then
                    local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                    local 副本id = 任务数据[任务id].副本id
                    任务处理类:完成大闹七仙女(self.任务id,id组)
                    副本数据.大闹天宫.进行[副本id].进程 = 4
                    任务处理类:设置大闹天宫副本(副本id)
                    刷新队伍任务跟踪(任务id)
                    for i,v in ipairs(id组) do
                        玩家数据[v].战斗=0
                        if 玩家数据[v].角色.数据.变身数据 == nil then
                          玩家数据[v].角色.数据.变身数据="菩提老祖"
                          玩家数据[v].角色.数据.变异=nil
                          玩家数据[v].角色:刷新信息()
                          发送数据(玩家数据[v].连接id,37,{变身数据=玩家数据[v].角色.数据.变身数据,变异=玩家数据[v].角色.数据.变异})
                          任务处理类:添加变身(v,9)
                          地图处理类:更改模型(v,{[1]=玩家数据[v].角色.数据.变身数据,[2]=玩家数据[v].角色.数据.变异},1)
                          常规提示(v,"#Y/此处甚好,且让我变化一番！")
                        end
                    end
                    地图处理类:跳转地图(self.进入玩家id,6032,103,59)
          elseif self.战斗类型>=100150 and self.战斗类型 <=100153 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  local 副本id = 任务数据[任务id].副本id
                  任务处理类:完成战诸神(self.任务id,id组)
                  if self.战斗类型 == 100150 then
                    任务数据[任务id].战诸神.造酒仙官 = true
                  elseif self.战斗类型 == 100151 then
                    任务数据[任务id].战诸神.运水道人 = true
                  elseif self.战斗类型 == 100152 then
                    任务数据[任务id].战诸神.烧火童子 = true
                  elseif self.战斗类型 == 100153 then
                    任务数据[任务id].战诸神.盘槽力士 = true
                  end
                  local 完成数据 = {"造酒仙官","运水道人","烧火童子","盘槽力士"}
                  local 是否完成 = true
                  for i=1,4 do
                    if not 任务数据[任务id].战诸神[完成数据[i]] then
                      是否完成 = false
                    end
                  end
                  if 是否完成 then
                    副本数据.大闹天宫.进行[副本id].进程 = 5
                    任务处理类:设置大闹天宫副本(副本id)
                    刷新队伍任务跟踪(任务id)
                    玩家数据[self.进入玩家id].战斗=0
                    地图处理类:跳转地图(self.进入玩家id,6033,103,59)
                  end
          elseif self.战斗类型==100154 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  if 任务id ~= 0 then
                      任务处理类:完成大闹天兵(self.任务id,id组)
                      任务数据[任务id].天兵天将.天兵=true
                      if 任务数据[任务id].天兵天将.天将 then
                          local 副本id = 任务数据[任务id].副本id
                          副本数据.大闹天宫.进行[副本id].进程 = 7
                          任务处理类:设置大闹天宫副本(副本id)
                          刷新队伍任务跟踪(任务id)
                      end
                  end
          elseif self.战斗类型==100155 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  if 任务id ~= 0 then
                      任务数据[任务id].天兵天将.天将=true
                      任务处理类:完成大闹天将(self.任务id,id组)
                      if 任务数据[任务id].天兵天将.天兵 then
                          local 副本id = 任务数据[任务id].副本id
                          副本数据.大闹天宫.进行[副本id].进程 = 7
                          任务处理类:设置大闹天宫副本(副本id)
                          刷新队伍任务跟踪(任务id)
                      end
                  end
          elseif self.战斗类型==100156 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  if 任务id ~= 0 then
                      任务处理类:完成大闹二郎神(self.任务id,id组)
                      local 副本id = 任务数据[任务id].副本id
                      玩家数据[self.进入玩家id].战斗=0
                      地图处理类:跳转地图(self.进入玩家id,6035,193,130)
                      副本数据.大闹天宫.进行[副本id].进程 = 8
                      任务处理类:设置大闹天宫副本(副本id)
                      刷新队伍任务跟踪(任务id)
                  end
          elseif self.战斗类型==100157 then
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(180)
                  if 任务id ~= 0 then
                    任务处理类:完成大闹雷神(self.任务id,id组)
                    for i,v in ipairs(id组) do
                        if 玩家数据[v].角色.数据.副本积分==nil then
                            玩家数据[v].角色.数据.副本积分=0
                        end
                        玩家数据[v].战斗=0
                        玩家数据[v].角色.数据.副本积分=玩家数据[v].角色.数据.副本积分+60
                        常规提示(v,"#Y恭喜你完成了大闹天宫副本获得60点副本积分")
                        玩家数据[v].角色:取消任务(任务id)
                        副本数据.大闹天宫.完成[v]=os.time()
                    end
                    local 副本id = 任务数据[任务id].副本id
                    地图处理类:跳转地图(self.进入玩家id,1070,53,196)
                    副本数据.大闹天宫.进行[副本id]=nil
                    任务数据[任务id]=nil
                  end
          elseif self.战斗类型==100214 then --大闹黑白无常
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].进程=3
                    任务处理类:设置齐天大圣副本(副本id)
                    刷新队伍任务跟踪(任务id)
                    任务处理类:完成齐天黑白无常(self.任务id,id组)
                  end
          elseif self.战斗类型==100215 then --大闹阎王
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].进程=5
                    任务处理类:设置齐天大圣副本(副本id)
                    刷新队伍任务跟踪(任务id)
                    任务处理类:完成齐天阎王(self.任务id,id组)
                  end
          elseif self.战斗类型==100216 then --大闹天王
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].进程=7
                    任务处理类:设置齐天大圣副本(副本id)
                    刷新队伍任务跟踪(任务id)
                    任务处理类:完成齐天天王(self.任务id,id组)
                  end
          elseif self.战斗类型==100217 then --大闹盗马贼
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].盗马贼=副本数据.齐天大圣.进行[副本id].盗马贼-1
                    if 副本数据.齐天大圣.进行[副本id].盗马贼 <= 0 then
                      副本数据.齐天大圣.进行[副本id].进程=9
                      任务处理类:设置齐天大圣副本(副本id)
                    end
                    任务处理类:完成齐天盗马贼(self.任务id,id组)
                    刷新队伍任务跟踪(任务id)
                  end
          elseif self.战斗类型==100218 then --大闹百万天兵
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵=true
                    任务处理类:完成齐天百万天兵(self.任务id,id组)
                  end
          elseif self.战斗类型==100219 then --大闹巨灵神
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                    local 副本id=任务数据[任务id].副本id
                    副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神=true
                    任务处理类:完成齐天巨灵神(self.任务id,id组)
                  end
          elseif self.战斗类型==100220 then  --大闹镇塔之神
                  local 任务id = 玩家数据[self.进入玩家id].角色:取任务(191)
                  if 任务id ~= 0 then
                      任务处理类:完成齐天镇塔之神(self.任务id,id组)
                      for i,v in ipairs(id组) do
                          if 玩家数据[v].角色.数据.副本积分==nil then
                              玩家数据[v].角色.数据.副本积分=0
                          end
                          玩家数据[v].角色.数据.副本积分=玩家数据[v].角色.数据.副本积分+20
                          常规提示(v,"#Y恭喜你完成了齐天大圣副本获得20点副本积分")
                          玩家数据[v].角色:取消任务(玩家数据[v].角色:取任务(191))
                          副本数据.齐天大圣.完成[v]=os.time()
                          玩家数据[v].角色:刷新任务跟踪()
                      end
                  end
                  local 副本id=任务数据[任务id].副本id
                  副本数据.齐天大圣.进行[副本id]=nil
                  玩家数据[self.进入玩家id].战斗=0
                  地图处理类:跳转地图(self.进入玩家id,1092,51,50,1)
                  任务数据[任务id]=nil
          end
end





function 战斗处理类:剧情奖励处理(id)
          if self.战斗类型==110002 then
          elseif self.战斗类型==110003 then
                -- if 玩家数据[id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[id].角色:取任务(999)].进程 == 7 then
                --     local 任务id=玩家数据[id].角色:取任务(999)
                --     玩家数据[id].角色:取消任务(任务id)
                --     任务处理类:设置商人的鬼魂(id)
                -- end
                -- if 成就数据[id].完成桃园==nil then
                --    成就数据[id].完成桃园=1
                -- local 成就提示 = "初入桃源村"
                -- local 成就提示1 = "完成桃源村系列任务"
                -- 发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                -- 成就数据[id].成就点 = 成就数据[id].成就点 + 1
                -- 玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                -- 常规提示(id,"#Y/恭喜你获得了1点成就")
               -- end
          elseif self.战斗类型==110004 then
                  if 玩家数据[id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[id].角色:取任务(998)].进程 == 11 then
                    xsjc1(id,12)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"商人的鬼魂",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(50000,"商人的鬼魂",1)
                  玩家数据[id].角色:添加银子(10000,"商人的鬼魂",1)
                  玩家数据[id].角色:添加储备(50000,"商人的鬼魂",1)
                  if 首杀记录.商人的鬼魂==0 then
                      首杀记录.商人的鬼魂=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"商人的鬼魂"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  if 成就数据[id].商人鬼魂==0 or 成就数据[id].商人鬼魂==nil then
                      成就数据[id].商人鬼魂=1
                      local 成就提示 = "商人的鬼魂"
                      local 成就提示1 = "完成建邺城系列任务"
                      发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                      成就数据[id].成就点 = 成就数据[id].成就点 + 1
                      玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                      常规提示(id,"#Y/恭喜你获得了1点成就")
                  end

          elseif self.战斗类型==110005 and not 玩家数据[id].角色.数据.妖风战斗 then
                  玩家数据[id].角色:添加剧情点1()
                  玩家数据[id].角色:添加活跃积分(10,"妖风战斗",1)
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  local 任务id=玩家数据[id].角色:取任务(898)
                  玩家数据[id].角色:取消任务(任务id)
                  玩家数据[id].角色:添加经验(200000,"妖风",1)
                  玩家数据[id].角色:添加储备(200000,"妖风",1)

                  if 首杀记录.妖风==0 then
                  首杀记录.妖风=玩家数据[id].角色.数据.名称
                  广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"妖风"),频道="xt"})
                  玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                  玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                  常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  玩家数据[id].角色.数据.妖风战斗=1
          elseif self.战斗类型==110006 then
                    if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 2 then
                      xsjc2(id,3)
                    end
                    玩家数据[id].角色:添加活跃积分(10,"白鹿精",1)
                    玩家数据[id].角色:添加剧情点1()
                    常规提示(id,"#Y/你获得了#G1点#剧情点数")
                    玩家数据[id].角色:添加经验(150000,"白鹿精",1)
                    玩家数据[id].角色:添加银子(50000,"白鹿精",1)
                    玩家数据[id].角色:添加储备(150000,"白鹿精",1)
                    if 首杀记录.白鹿精==0 then
                        首杀记录.白鹿精=玩家数据[id].角色.数据.名称
                        广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"白鹿精"),频道="xt"})
                        玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                        玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                        常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                    end
                    if 成就数据[id].白鹿精==0 or 成就数据[id].白鹿精==nil then
                        成就数据[id].白鹿精=1
                    end
          elseif self.战斗类型==110007 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 6 then
                      xsjc2(id,7)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"酒肉和尚假",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(100000,"酒肉和尚假",1)
                  玩家数据[id].角色:添加银子(20000,"酒肉和尚假",1)
                  玩家数据[id].角色:添加储备(100000,"酒肉和尚假",1)
          elseif self.战斗类型==110008 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 13 then
                      xsjc2(id,14)
                  end
                  玩家数据[id].角色:添加剧情点1()
                  玩家数据[id].角色:添加活跃积分(10,"执法天兵",1)
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  local 奖励数据=玩家数据[id].道具:给予书铁(id,{3,4})
                  常规提示(id,"#Y你得到了#R"..奖励数据[1])
                  玩家数据[id].角色:添加经验(100000,"执法天兵",1)
                  玩家数据[id].角色:添加银子(20000,"执法天兵",1)
                  玩家数据[id].角色:添加储备(100000,"执法天兵",1)
                  if 首杀记录.守门天兵==0 then
                      首杀记录.守门天兵=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"守门天兵"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  添加最后对话(id,"不好意思,我刚才不知道你是来打听#G白琉璃下落#的\n真是不打不相识,可能我们#R下手有点重#还望见谅\n你要找的#G白琉璃#是天宫的#Y琉璃盏#转世,只因卷帘大将失手摔碎#Y琉璃盏#才有的她\n但是我不知道为何她会盗取化生寺的#Y佛光舍利#\n但是我可以告诉你她的下落在哪,刚才我用#S千里眼#看了\n她就在#P大唐国境#附近,你去找他吧!")
          elseif self.战斗类型==110009 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 14 then
                      xsjc2(id,15)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"白琉璃",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  常规提示(id,"#Y/你获得了#佛光舍利子")
                  玩家数据[id].道具:给予道具(id,"佛光舍利子",1,nil,nil,"专用")
                  玩家数据[id].角色:添加经验(60000,"白琉璃",1)
                  玩家数据[id].角色:添加银子(20000,"白琉璃",1)
                  玩家数据[id].角色:添加储备(60000,"白琉璃",1)
                  添加最后对话(id,"你这个小东西还真有点意思,知道我的#Y弱点是暗器\n我也不瞒你说了,我的经历你大概应该知道了\n我偷佛光舍利的目的,其实就是想报答我的恩人#G卷帘大将#\n他为了让我们#S四姐妹#获得仙体,故意打碎了#Y琉璃盏\n从此我们获得了自由,但因此卷帘大将也被#R贬入凡间\n每天受#G七剑穿心#之苦,我不忍恩人受苦,采取偷取舍利\n希望能缓解他的痛苦,舍利我就交还与你了\n我现在变回#G琉璃碎片#,希望日后你有机会见到#S卷帘大将\n#P把我给他#14")
          elseif self.战斗类型==110010 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 17 then
                      xsjc2(id,18)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"酒肉和尚真",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(160000,"酒肉和尚真",1)
                  玩家数据[id].角色:添加银子(80000,"酒肉和尚真",1)
                  玩家数据[id].角色:添加储备(160000,"酒肉和尚真",1)
                  if 首杀记录.酒肉和尚==0 then
                      首杀记录.酒肉和尚=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"酒肉和尚"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  if 成就数据[id].酒肉和尚==0 or 成就数据[id].酒肉和尚==nil then
                      成就数据[id].酒肉和尚=1
                      local 成就提示 = "玄奘身世上篇"
                      local 成就提示1 = "完成酒肉和尚系列任务"
                      发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                      成就数据[id].成就点 = 成就数据[id].成就点 + 1
                      玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                      常规提示(id,"#Y/恭喜你获得了1点成就")
                  end
                  添加最后对话(id,"别打了,别打了,#Y我知错了#有个坏消息是:其实我也没有#G解药\n他这个毒需要用到仙家灵药#S九转回魂丹\n你可以去#P普陀山#问问,看谁有此药没#17")
          elseif self.战斗类型==110011 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 20 then
                      xsjc2(id,21)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"幽冥鬼",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(800000,"幽冥鬼",1)
                  玩家数据[id].角色:添加银子(180000,"幽冥鬼",1)
                  玩家数据[id].角色:添加储备(800000,"幽冥鬼",1)
                  if 首杀记录.幽冥鬼==0 then
                      首杀记录.幽冥鬼=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"幽冥鬼"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  添加最后对话(id,"其实我不是不想#Y轮回投胎#,只是我现在还放不下一人\n他叫#S文秀#,是我的妻子,我不知道她现在还过得这么样\n如果我能让我知道她还在世的话,我就放心了\n以前我们住在#G大唐国境#附近")
          elseif self.战斗类型==110012 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 32 then
                      xsjc2(id,33)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"蟹将军",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(800000,"蟹将军",1)
                  玩家数据[id].角色:添加银子(180000,"蟹将军",1)
                  玩家数据[id].角色:添加储备(800000,"蟹将军",1)
                  if 首杀记录.蟹将军==0 then
                      首杀记录.蟹将军=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"蟹将军"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  添加最后对话(id,"原来你是来归还#S定颜珠#的呀\n不好意思打错人了#17,我还以为你是#G偷盗的贼人#呢\n#P龟千岁#正等着你呢,快去吧~")
          elseif self.战斗类型==110013  then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 40 then
                      xsjc2(id,41)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"假刘洪",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(800000,"假刘洪",1)
                  玩家数据[id].角色:添加银子(180000,"假刘洪",1)
                  玩家数据[id].角色:添加储备(800000,"假刘洪",1)
                  玩家数据[id].战斗对话={名称="程咬金",模型="程咬金",对话="少侠你快去追击#Y真刘洪#吧,这里就交给我们善后了\n我估计他已经逃到#G大唐境外#去完了"}
          elseif self.战斗类型==110014 then
                  if 玩家数据[id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[id].角色:取任务(997)].进程 == 41 then
                      xsjc2(id,42)
                  end
                  玩家数据[id].角色:添加活跃积分(10,"真刘洪",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(800000,"真刘洪",1)
                  玩家数据[id].角色:添加银子(180000,"真刘洪",1)
                  玩家数据[id].角色:添加储备(800000,"真刘洪",1)
                  if 首杀记录.真刘洪==0 then
                      首杀记录.真刘洪=玩家数据[id].角色.数据.名称
                      广播消息({内容=format("#S(首杀公告)#Y恭喜%s#R成功首杀#G%s#Y大家为他欢呼吧!",玩家数据[id].角色.数据.名称,"真刘洪"),频道="xt"})
                      玩家数据[id].道具:给予道具(id,"高级魔兽要诀",1,nil,nil)
                      玩家数据[id].道具:给予道具(id,"神兜兜",1,nil,nil)
                      常规提示(id,"#Y/你额外获得首杀奖励:\n#G神兜兜和高级兽决#")
                  end
                  if 成就数据[id].真刘洪==0 or 成就数据[id].真刘洪==nil then
                      成就数据[id].真刘洪=1
                      local 成就提示 = "真刘洪"
                      local 成就提示1 = "完成玄奘身世系列任务"
                      发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                      成就数据[id].成就点 = 成就数据[id].成就点 + 1
                      玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                      常规提示(id,"#Y/恭喜你获得了1点成就")
                   end
          elseif self.战斗类型==110015 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 1 then
                      任务数据[玩家数据[id].角色:取任务(996)].进程=2
                  end
                  玩家数据[id].角色:添加活跃积分(10,"天兵飞剑",1)
                  玩家数据[id].角色:添加剧情点1()
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
                  玩家数据[id].角色:添加经验(800000,"天兵飞剑",1)
                  玩家数据[id].角色:添加银子(180000,"天兵飞剑",1)
                  玩家数据[id].角色:添加储备(800000,"天兵飞剑",1)
          elseif self.战斗类型==100255 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 2 then
                      任务数据[玩家数据[id].角色:取任务(996)].进程=3
                      玩家数据[id].角色:添加活跃积分(10,"卷帘大将1",1)
                      玩家数据[id].角色:添加剧情点1()
                      常规提示(id,"#Y/你获得了#G1点#剧情点数")
                      玩家数据[id].道具:给予道具(id,"九转金丹",10,100,nil,"专用")
                      常规提示(id,"#Y/你获得了#G10#个九转金丹")
                      玩家数据[id].角色:添加经验(800000,"卷帘大将1",1)
                      玩家数据[id].角色:添加银子(100000,"卷帘大将1",1)
                      玩家数据[id].角色:添加储备(500000,"卷帘大将1",1)
                  end
          elseif self.战斗类型==100257 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 9 then
                      任务数据[玩家数据[id].角色:取任务(996)].进程=10
                      玩家数据[id].角色:添加活跃积分(10,"路人甲",1)
                      玩家数据[id].角色:添加剧情点1()
                      常规提示(id,"#Y/你获得了#G1点#剧情点数")
                      玩家数据[id].角色:添加经验(800000,"路人甲",1)
                      玩家数据[id].角色:添加银子(100000,"路人甲",1)
                      玩家数据[id].角色:添加储备(500000,"路人甲",1)
                  end
          elseif self.战斗类型==100258 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 12 then
                      任务数据[玩家数据[id].角色:取任务(996)].进程=13
                      玩家数据[id].角色:添加活跃积分(10,"杨戬",1)
                      玩家数据[id].角色:添加剧情点1()
                      常规提示(id,"#Y/你获得了#G1点#剧情点数")
                      玩家数据[id].角色:添加经验(800000,"杨戬",1)
                      玩家数据[id].角色:添加银子(100000,"杨戬",1)
                      玩家数据[id].角色:添加储备(500000,"杨戬",1)
                  end
          elseif self.战斗类型==100259 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 13 then
                    任务数据[玩家数据[id].角色:取任务(996)].进程=14
                    玩家数据[id].角色:添加活跃积分(10,"龙孙",1)
                    玩家数据[id].角色:添加剧情点1()
                    常规提示(id,"#Y/你获得了#G1点#剧情点数")
                    local 名称=取宝石()
                    玩家数据[id].道具:给予道具(id,名称,取随机数(1,5))
                    常规提示(id,"#Y/你获得了宝石")
                    玩家数据[id].角色:添加经验(800000,"龙孙",1)
                    玩家数据[id].角色:添加银子(100000,"龙孙",1)
                    玩家数据[id].角色:添加储备(500000,"龙孙",1)
                  end
          elseif self.战斗类型==100260 then
                  if 玩家数据[id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[id].角色:取任务(996)].进程 == 15 then
                      local 任务id=玩家数据[id].角色:取任务(996)
                      玩家数据[id].角色:取消任务(任务id)
                      if 成就数据[id].大战心魔==0 or 成就数据[id].大战心魔==nil then
                         成就数据[id].大战心魔=1
                        local 成就提示 = "大战心魔"
                        local 成就提示1 = "完成大战心魔系列任务"
                        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                        成就数据[id].成就点 = 成就数据[id].成就点 + 1
                        玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                        常规提示(id,"#Y/恭喜你获得了1点成就")
                      end
                      玩家数据[id].角色:添加活跃积分(10,"卷帘大将3",1)
                      玩家数据[id].角色:添加剧情点1()
                      常规提示(id,"#Y/你获得了#G1点#剧情点数")
                      local 名称=取宝石()
                      玩家数据[id].道具:给予道具(id,名称,取随机数(1,5))
                      常规提示(id,"#Y/你获得了宝石")
                      玩家数据[id].角色:添加经验(800000,"卷帘大将3",1)
                      玩家数据[id].角色:添加银子(100000,"卷帘大将3",1)
                      玩家数据[id].角色:添加储备(500000,"卷帘大将3",1)
                  end
          elseif self.战斗类型==100261 then
                  if 成就数据[id].突破任务==0 or 成就数据[id].突破任务==nil then
                      成就数据[id].突破任务=1
                  end
                  添加最后对话(id,"恭喜你完成突破战斗,现在你可以升级到109了！")
                  玩家数据[id].角色:添加剧情点1()
                  玩家数据[id].角色:添加活跃积分(10,"突破109",1)
                  常规提示(id,"#Y/你获得了#G1点#剧情点数")
          elseif self.战斗类型==100226 then
             --  if 玩家数据[id].角色:取任务(401) ~= 0 then
             --    任务数据[玩家数据[id].角色:取任务(401)].进程=2
             --    if 支线奖励[id]==nil or 支线奖励[id].狸猫奖励==nil then
             --    支线奖励[id]={}
             --    支线奖励[id].狸猫奖励=true
             --    end
             --    常规提示(id,"#Y快去找郭大哥领取奖励吧")
             -- end
          elseif self.战斗类型==100227 then
                  if 玩家数据[id].角色:取任务(400) ~= 0 then
                      任务数据[玩家数据[id].角色:取任务(400)].进程=7
                      if 支线奖励[id]==nil or 支线奖励[id].虎子奖励==nil then
                      支线奖励[id]={}
                      支线奖励[id].虎子奖励=true
                      end
                      常规提示(id,"#Y快去找云游神医问个究竟!")
                 end
          end
end




function 战斗处理类:取野外等级差(地图等级,玩家等级)
  local 等级=math.abs(地图等级-玩家等级)
  if 等级<=5 then
    return 1
  elseif 等级<=10 then
    return 0.8
  elseif 等级<=20 then
    return 0.5
  else
    return 0.2
  end
end

