{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderAddFilledSvg from \"@ant-design/icons-svg/es/asn/FolderAddFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderAddFilled = function FolderAddFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderAddFilledSvg\n  }));\n};\n\n/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAyOTguNEg1MjFMNDAzLjcgMTg2LjJhOC4xNSA4LjE1IDAgMDAtNS41LTIuMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTYzMiA1NzdjMCAzLjgtMy40IDctNy41IDdINTQwdjg0LjljMCAzLjktMy4yIDcuMS03IDcuMWgtNDJjLTMuOCAwLTctMy4yLTctNy4xVjU4NGgtODQuNWMtNC4xIDAtNy41LTMuMi03LjUtN3YtNDJjMC0zLjggMy40LTcgNy41LTdINDg0di04NC45YzAtMy45IDMuMi03LjEgNy03LjFoNDJjMy44IDAgNyAzLjIgNyA3LjFWNTI4aDg0LjVjNC4xIDAgNy41IDMuMiA3LjUgN3Y0MnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderAddFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderAddFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FolderAddFilledSvg", "AntdIcon", "FolderAddFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/FolderAddFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderAddFilledSvg from \"@ant-design/icons-svg/es/asn/FolderAddFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderAddFilled = function FolderAddFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderAddFilledSvg\n  }));\n};\n\n/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAyOTguNEg1MjFMNDAzLjcgMTg2LjJhOC4xNSA4LjE1IDAgMDAtNS41LTIuMkgxNDRjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjU5MmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg3MzZjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzMwLjRjMC0xNy43LTE0LjMtMzItMzItMzJ6TTYzMiA1NzdjMCAzLjgtMy40IDctNy41IDdINTQwdjg0LjljMCAzLjktMy4yIDcuMS03IDcuMWgtNDJjLTMuOCAwLTctMy4yLTctNy4xVjU4NGgtODQuNWMtNC4xIDAtNy41LTMuMi03LjUtN3YtNDJjMC0zLjggMy40LTcgNy41LTdINDg0di04NC45YzAtMy45IDMuMi03LjEgNy03LjFoNDJjMy44IDAgNyAzLjIgNyA3LjFWNTI4aDg0LjVjNC4xIDAgNy41IDMuMiA3LjUgN3Y0MnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderAddFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderAddFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,eAAe,CAAC;AAC5D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,iBAAiB;AACzC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}