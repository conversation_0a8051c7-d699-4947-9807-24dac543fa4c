-- @作者: baidwwy
-- @邮箱:  <EMAIL>
-- @创建时间:   2021-05-08 13:00:06
-- @最后修改来自: baidwwy
-- @Last Modified time: 2024-08-08 20:18:44
function 取星位组合(sj,level,强制)
	if sj==nil or type(sj)~="table" then
		return nil
	end
	local 返回数据 = nil
	if not 强制 then
	    if sj[1] ==nil or sj[1].颜色==nil or sj[2] ==nil or sj[2].颜色==nil then
	      return nil
	    elseif sj[3] ==nil or sj[3].颜色==nil then
	      level = 1
	    elseif sj[4] ==nil or sj[4].颜色==nil then
	      level = 2
	    elseif  sj[5] ==nil or sj[5].颜色==nil then
	      level = 3
	    else
	      level = 4
	    end
 	end
	-- 部位限制 头=1 项链=2 武器=3 衣服=4 腰带=5 鞋子=6
	if level==1 then
		--一级符石组合
	    if sj[1].颜色 == "红色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="真元护体",部位=nil,门派=nil}
		elseif sj[1].颜色 == "红色" and sj[2].颜色 == "黄色" then
			返回数据={组合="风卷残云",部位=nil,门派=nil}
		elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "紫色" then
			返回数据={组合="万丈霞光",部位=nil,门派=nil}
		elseif sj[1].颜色 == "红色" and sj[2].颜色 == "白色" then
			返回数据={组合="无所畏惧",部位=nil,门派=nil}
		elseif sj[1].颜色 == "白色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="回眸一笑",部位=nil,门派=nil}
		elseif sj[1].颜色 == "红色" and sj[2].颜色 == "绿色" then
			返回数据={组合="柳暗花明",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "黄色" then
			返回数据={组合="雪照云光",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "紫色" then
			返回数据={组合="心灵手巧",部位=nil,门派=nil}
		elseif sj[1].颜色 == "紫色" and sj[2].颜色 == "绿色" then
			返回数据={组合="点石成金",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "紫色" then
			返回数据={组合="百步穿杨",部位=nil,门派=nil}
		elseif sj[1].颜色 == "白色" and sj[2].颜色 == "红色" then
			返回数据={组合="隔山打牛",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="心随我动",部位=nil,门派=nil}
		elseif sj[1].颜色 == "白色" and sj[2].颜色 == "黄色" then
			返回数据={组合="云随风舞",部位=nil,门派=nil}
		elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "红色" then
			返回数据={组合="无懈可击",部位=nil,门派=nil}
		elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "白色" then
			返回数据={组合="望穿秋水",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "黑色" then
			返回数据={组合="万里横行",部位=nil,门派=nil}
		elseif sj[1].颜色 == "红色" and sj[2].颜色 == "紫色" then
			返回数据={组合="日落西山",部位=nil,门派=nil}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "绿色" then
			返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
		elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "红色" then
			返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
		elseif sj[1].颜色 == "红色" and sj[2].颜色 == "红色" then
			返回数据={组合="天雷地火",部位=nil,门派="天宫"}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "黄色" then
			返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
		elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "黄色" then
			返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
		elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "黄色" then
			返回数据={组合="行云流水",部位=nil,门派="普陀山"}
		elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "红色" then
			返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
		-- elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "绿色" then
		-- 	返回数据={组合="势如破竹",部位=nil,门派="天机城"}
		elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
		elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "蓝色" then
			返回数据={组合="无心插柳",部位=nil,门派=nil}
		end
	elseif level==2 then
		--二级符石效果
		if  sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
			返回数据={组合="真元护体",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" then
			返回数据={组合="风卷残云",部位=nil,门派=nil}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
			返回数据={组合="降妖伏魔",部位=nil,门派=nil}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" then
			返回数据={组合="万丈霞光",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
			返回数据={组合="无所畏惧",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
			返回数据={组合="回眸一笑",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
			返回数据={组合="柳暗花明",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
			返回数据={组合="飞檐走壁",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
			返回数据={组合="雪照云光",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
			返回数据={组合="心灵手巧",部位=nil,门派=nil}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
			返回数据={组合="点石成金",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="百步穿杨",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
			返回数据={组合="隔山打牛",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
			返回数据={组合="心随我动",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
			返回数据={组合="云随风舞",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
			返回数据={组合="天降大任",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="高山流水",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
			返回数据={组合="百无禁忌",部位=6,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
			返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
			返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
			返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
			返回数据={组合="毒经符石",部位=1,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
			返回数据={组合="天罡气符石",部位=1,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" then
			返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
			返回数据={组合="周易学符石",部位=1,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="灵性符石",部位=1,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
			返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
			返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
			返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
		-- 	返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
			返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" then
		-- 	返回数据={组合="神工无形符石",部位=1,门派="天机城"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
			返回数据={组合="巫咒符石",部位=1,门派="神木林"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
			返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
			返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
			返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
			返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
			返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
			返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
			返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
			返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
			返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
			返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
			返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
			返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
		-- 	返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
			返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
		-- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
		-- 	返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
			返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
			返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="符之术符石",部位=4,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" then
			返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" then
			返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
			返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
			返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
			返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
			返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
			返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
		-- 	返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
			返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
		-- 	返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
			返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
			返回数据={组合="诵经符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
			返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
			返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" then
			返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
			返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
			返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
			返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
			返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
			返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
			返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" then
			返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
			返回数据={组合="混天术符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
			返回数据={组合="龙附符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
			返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
			返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
			返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
			返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
		-- 	返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
		-- 	返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
		-- 	返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
		-- 	返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" then
			返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" then
			返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
			返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" then
			返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
			返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
			返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
			返回数据={组合="清明自在符石",部位=2,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
			返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
			返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" then
			返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
			返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
			返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
			返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
			返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
		-- 	返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
			返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
		-- 	返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
			返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
			返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
			返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
			返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
			返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
			返回数据={组合="云霄步符石",部位=6,门派="天宫"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" then
			返回数据={组合="游龙术符石",部位=6,门派="	龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
			返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
			返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
			返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" then
			返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
			返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
			返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
		-- 	返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
			返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
		-- 	返回数据={组合="运思如电符石",部位=6,门派="天机城"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
			返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
			返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
			返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="天雷地火",部位=nil,门派="天宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" then
			返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黄色" then
			返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
			返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
			返回数据={组合="行云流水",部位=nil,门派="普陀山"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
			返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
			返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
		-- 	返回数据={组合="势如破竹",部位=nil,门派="天机城"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
			返回数据={组合="无心插柳",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
	      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
	      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
	      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
	    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" then
	      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
	    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
	      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
	      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
	    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
	      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}
	    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" then
	     返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
  	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
	     返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" then
	     返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
	     返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
	     返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
	     返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
        elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" then
	     返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}


		end
	elseif level==3 then
		--三级符石效果
		if  sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
			返回数据={组合="真元护体",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" then
			返回数据={组合="风卷残云",部位=nil,门派=nil}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
			返回数据={组合="降妖伏魔",部位=nil,门派=nil}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
			返回数据={组合="万丈霞光",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="无所畏惧",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="回眸一笑",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="柳暗花明",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="飞檐走壁",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="雪照云光",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
			返回数据={组合="心灵手巧",部位=nil,门派=nil}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
			返回数据={组合="点石成金",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
			返回数据={组合="百步穿杨",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="隔山打牛",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
			返回数据={组合="心随我动",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
			返回数据={组合="云随风舞",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
			返回数据={组合="天降大任",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
			返回数据={组合="高山流水",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
			返回数据={组合="百无禁忌",部位=6,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
			返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
			返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
			返回数据={组合="毒经符石",部位=1,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="天罡气符石",部位=1,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黄色" then
			返回数据={组合="周易学符石",部位=1,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
			返回数据={组合="灵性符石",部位=1,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
			返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
			返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
			返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
			返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
		-- 	返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
		-- 	返回数据={组合="神工无形符石",部位=1,门派="天机城"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="巫咒符石",部位=1,门派="神木林"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
			返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
			返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
			返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
			返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
			返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
			返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
		-- 	返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
			返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
		-- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
		-- 	返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
			返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" then
			返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
			返回数据={组合="符之术符石",部位=4,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
			返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
			返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
			返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
			返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
			返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
			返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
			返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
			返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
			返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
		-- 	返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
			返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
		-- 	返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
			返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
			返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黄色" then
			返回数据={组合="诵经符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
			返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
			返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色"  then
			返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
			返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
			返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
			返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
			返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色"  then
			返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
			返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
			返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
			返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
			返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" then
			返回数据={组合="混天术符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
			返回数据={组合="龙附符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
			返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
			返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
			返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
			返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
			返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
		-- 	返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
		-- 	返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
		-- 	返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
		-- 	返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色"  then
			返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
			返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
			返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" then
			返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
			返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="清明自在符石",部位=2,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
			返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
			返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
			返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
		-- 	返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
			返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
		-- 	返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
			返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
			返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
			返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="云霄步符石",部位=6,门派="天宫"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
			返回数据={组合="游龙术符石",部位=6,门派="	龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
			返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
			返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
			返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
			返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
		-- 	返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
			返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黄色" then
		-- 	返回数据={组合="运思如电符石",部位=6,门派="天机城"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" then
			返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
			返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
			返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
			返回数据={组合="天雷地火",部位=nil,门派="天宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
			返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
			返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
			返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
			返回数据={组合="行云流水",部位=nil,门派="普陀山"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
			返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" then
			返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
		-- 	返回数据={组合="势如破竹",部位=nil,门派="盘丝洞"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
			返回数据={组合="无心插柳",部位=nil,门派=nil}

		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
	      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
	      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
	      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
	    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
	      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
	    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
	      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" then
	      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
	    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
	      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}


	    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
	     返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
  	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
	     返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
	     返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
	     返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
	     返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
	     返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
        elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
	     返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}





		end
	elseif level==4 then
		--四级符石效果
		if sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="风卷残云",部位=nil,门派=nil}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
			返回数据={组合="万丈霞光",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
			返回数据={组合="无所畏惧",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
			返回数据={组合="回眸一笑",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
			返回数据={组合="柳暗花明",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
			返回数据={组合="飞檐走壁",部位=nil,门派=nil}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="点石成金",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="绿色" then
			返回数据={组合="百步穿杨",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="隔山打牛",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
			返回数据={组合="心随我动",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
			返回数据={组合="云随风舞",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
			返回数据={组合="暗渡陈仓",部位=nil,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
			返回数据={组合="化敌为友",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="白色" then
			返回数据={组合="天降大任",部位=nil,门派=nil}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
			返回数据={组合="高山流水",部位=nil,门派=nil}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
			返回数据={组合="百无禁忌",部位=6,门派=nil}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黑色" then
			返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
			返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
			返回数据={组合="毒经符石",部位=1,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
			返回数据={组合="天罡气符石",部位=1,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="绿色" then
			返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
			返回数据={组合="周易学符石",部位=1,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
			返回数据={组合="灵性符石",部位=1,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
			返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
			返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
			返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
			返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
		-- 	返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
			返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
		-- 	返回数据={组合="神工无形符石",部位=1,门派="天机城"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="巫咒符石",部位=1,门派="神木林"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
			返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黑色" then
			返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
			返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
			返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
			返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
			返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
			返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
		-- 	返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
			返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
		-- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
		-- 	返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
			返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
			返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
			返回数据={组合="符之术符石",部位=4,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
			返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="紫色" then
			返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
			返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="红色" then
			返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
			返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
			返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黄色" then
			返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
		-- 	返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
			返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
			返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
		-- 	返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
			返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
			返回数据={组合="诵经符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
			返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
			返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
			返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
			返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
			返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
			返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
			返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
			返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
			返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
			返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
			返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
			返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
			返回数据={组合="混天术符石",部位=3,门派="天宫"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
			返回数据={组合="龙附符石",部位=3,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
			返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
			返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="绿色" then
			返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
		elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
			返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
			返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
			返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="绿色" then
		-- 	返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
		-- 	返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
		-- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
		-- 	返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
		-- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="红色" then
		-- 	返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色"  then
			返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
			返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
			返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
			返回数据={组合="清明自在符石",部位=2,门派="天宫"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
			返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
			返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
			返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
			返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
		elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
			返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
		elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
			返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
		-- 	返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
			返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
		-- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="紫色" then
		-- 	返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
			返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}

		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
			返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="白色" then
			返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
			返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
			返回数据={组合="云霄步符石",部位=6,门派="天宫"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
			返回数据={组合="游龙术符石",部位=6,门派="	龙宫"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
		elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
			返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
			返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
		elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
			返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
		-- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
		-- 	返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="绿色" then
			返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
		-- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
		-- 	返回数据={组合="运思如电符石",部位=6,门派="天机城"}
		elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
			返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
	    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
	      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
	      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
	    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
	      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
	    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="红色" then
	      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
	    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
	      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="紫色" then
	      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
	    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
	      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}
        elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 == "白色" then
	     返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
  	    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 == "紫色" then
	     返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
		elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 == "黑色" then
	     返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
	    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 == "黑色" then
	     返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 == "白色" then
	     返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
        elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 == "蓝色" then
	     返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
        elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 == "绿色" then
	     返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}










		end
	end
	if 返回数据~=nil and 返回数据.组合~=nil then
		返回数据.等级=level
	elseif 返回数据==nil then
		if level~=1 then
	      return 取星位组合(sj,level-1,true)
	    end
	end
	return 返回数据
end

function 取开启星位消耗(lv)
	local 消耗 = {经验=0,金钱=0}
	if lv<=59 then
		消耗.经验 = 0
		消耗.金钱 = 0
	elseif lv<=60 then
		消耗.经验 = 9000000
		消耗.金钱 = 1850000
	elseif lv<=70 then
		消耗.经验 = 10000000
		消耗.金钱 = 2150000
	elseif lv<=80 then
		消耗.经验 = 12000000
		消耗.金钱 = 2400000
	elseif lv<=90 then
		消耗.经验 = 13000000
		消耗.金钱 = 2700000
	elseif lv<=100 then
		消耗.经验 = 15000000
		消耗.金钱 = 3000000
	elseif lv<=110 then
		消耗.经验 = 170000000
		消耗.金钱 = 34000000
	elseif lv<=120 then
		消耗.经验 = 18000000
		消耗.金钱 = 3800000
	elseif lv<=130 then
		消耗.经验 = 20000000
		消耗.金钱 = 4000000
	elseif lv<=140 then
		消耗.经验 = 21000000
		消耗.金钱 = 4300000
	elseif lv<=150 then
		消耗.经验 = 23000000
		消耗.金钱 = 4600000
	elseif lv<=160 then
		消耗.经验 = 25000000
		消耗.金钱 = 4900000
	end
	消耗.经验 = 消耗.经验 * 2
	消耗.金钱 = 消耗.金钱 * 4
	return 消耗
end

function 激活符石消耗(lv)
	local 消耗 = {经验=0,体力=0}
	消耗.经验 = (lv * 2 - 1) * 1000000
	消耗.体力 = (lv * 2 - 1) * 10
	return 消耗
end

function 取星位颜色(部位)
  if 部位==1 then
    return {[1]="黑",[2]="白"}
  elseif 部位==2 then
    return {[1]="红",[2]="蓝"}
  elseif 部位==3 then
    return {[1]="红",[2]="金"}
  elseif 部位==4 then
    return {[1]="白",[2]="黑"}
  elseif 部位==5 then
    return {[1]="蓝",[2]="绿"}
  elseif 部位==6 then
    return {[1]="蓝",[2]="红"}
  else
    return {[1]="无效道具",[2]="无效道具"}
  end
end

function 取星位属性(部位)
    local 属性表 = {}
    属性表.伤害 = {1.5,2,2.5,3,3.5,1.5,2,1.5,2,1.5,2,1.5,1.5,1.5}
    属性表.气血 = {5,10,15,20,25,5,10,5,10,5,10,5,5,5}
    属性表.速度 = {1.5,2,2.5,3,3.5,1.5,2,1.5,2,1.5,2,1.5,1.5,1.5}
    属性表.法术伤害 = {1.5,2,2.5,3,3.5,1.5,2,1.5,2,1.5,2,1.5,1.5,1.5}
    属性表.法术防御 = {1.5,2,2.5,3,3.5,1.5,2,1.5,2,1.5,2,1.5,1.5,1.5}
    属性表.防御 = {3,4,5,6,7,3,4,3,4,3,4,3,3,3}
    返回值={名称=nil,属性值=nil}
    local 属性范围
    if 部位==1 then
         属性范围 = {"气血","速度","法术伤害"}
    elseif 部位==2 then
        属性范围 = {"气血","法术伤害","法术防御"}
    elseif 部位==3 then
        属性范围 = {"伤害","速度","防御"}
    elseif 部位==4 then
        属性范围 = {"伤害","法术防御","防御"}
    elseif 部位==5 then
        属性范围 = {"伤害","气血","速度"}
    elseif 部位==6 then
        属性范围 = {"法术伤害","法术防御","防御"}
    else
        属性范围 = {"气血","速度","法术伤害"}
    end
    返回值.名称 = 属性范围[取随机数(1,#属性范围)]
    返回值.属性值 = 属性表[返回值.名称][取随机数(1,#属性表[返回值.名称])]
    -- print(#属性表[返回值.名称])
    return 返回值
end

function 取星位相互(部位)
	if 部位==1 then
		return "力量"
    elseif 部位==2 then
    	return "魔力"
    elseif 部位==3 then
    	return "体质"
    elseif 部位==4 then
    	return "耐力"
    elseif 部位==5 then
    	return "体质"
    elseif 部位==6 then
    	return "敏捷"
    else
    	return "力量"
    end
end

function 取孔数数量(等级)
	if 等级<=30 then
		return 1
	elseif 等级<=60 then
		return 2
	elseif 等级<=90 then
		return 3
	elseif 等级<=120 then
		return 4
	else
		return 5
	end
end

function 取星位套(ys,lv)
	local 返回组={名称="无",门派="无",等级=0,属性={}}
	if lv>=60 and lv<90 then
		返回组.等级=1
	elseif lv>=90 and lv<130 then
		返回组.等级=2
	elseif lv>=130 then
		返回组.等级=3
	else
	    返回组.等级=0
	end
	if ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="燃魂"
		返回组.门派="大唐官府"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="锐杀"
		返回组.门派="大唐官府"
		if 返回组.等级~=0 then
			local 临时属性表 = {10,20,30}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="灵压"
		返回组.门派="方寸山"
		if 返回组.等级~=0 then
			local 临时属性表 = {10,20,30}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="红" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="再塑"
		返回组.门派="方寸山"
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="花药"
		返回组.门派="女儿村"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="花冢"
		返回组.门派="女儿村"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,6,8}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="弘法"
		返回组.门派="化生寺"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,10,14}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="佛心"
		返回组.门派="化生寺"
		if 返回组.等级~=0 then
			local 临时属性表 = {5,8,11}
			返回组.属性={[1]={类型="体质",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="招云"
		返回组.门派="龙宫"
		if 返回组.等级~=0 then
			local 临时属性表 = {3,6,8}
			返回组.属性={[1]={类型="法术伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="腾蛟"
		返回组.门派="龙宫"
		if 返回组.等级~=0 then
			local 临时属性表 = {3,6,8}
			返回组.属性={[1]={类型="法术伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="天威"
		返回组.门派="天宫"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="凝力"
		返回组.门派="天宫"
		if 返回组.等级~=0 then
			local 临时属性表 = {2,4,6}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="法诀"
		返回组.门派="普陀山"
		if 返回组.等级~=0 then
			local 临时属性表 = {20,30,40}
			返回组.属性={[1]={类型="气血",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="莲佑"
		返回组.门派="普陀山"
		if 返回组.等级~=0 then
			local 临时属性表 = {20,30,40}
			返回组.属性={[1]={类型="气血",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="云步"
		返回组.门派="五庄观"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="清风"
		返回组.门派="五庄观"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="蕴气"
		返回组.门派="魔王寨"
		if 返回组.等级~=0 then
			local 临时属性表 = {3,6,8}
			返回组.属性={[1]={类型="法术伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="红" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="怒焰"
		返回组.门派="魔王寨"
		if 返回组.等级~=0 then
			local 临时属性表 = {3,6,8}
			返回组.属性={[1]={类型="法术伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="威压"
		返回组.门派="狮驼岭"
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="索命"
		返回组.门派="狮驼岭"
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="踏网"
		返回组.门派="盘丝洞"
		if 返回组.等级~=0 then
			local 临时属性表 = {8,10,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="艳妆"
		返回组.门派="盘丝洞"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="红" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="毒附"
		返回组.门派="阴曹地府"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,10,14}
			返回组.属性={[1]={类型="防御",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="魂附"
		返回组.门派="阴曹地府"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,9,12}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="秋风"
		返回组.门派="神木林"
		if 返回组.等级~=0 then
			local 临时属性表 = {12,24,32}
			返回组.属性={[1]={类型="法术伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="云击"
		返回组.门派="神木林"
		if 返回组.等级~=0 then
			local 临时属性表 = {2,4,9}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="山甲"
		返回组.门派="无底洞"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,10,14}
			返回组.属性={[1]={类型="防御",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="灵诀"
		返回组.门派="无底洞"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,8,12}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="怒袭"
		返回组.门派="凌波城"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,8,10}
			返回组.属性={[1]={类型="防御",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="狂扫"
		返回组.门派="凌波城"
		if 返回组.等级~=0 then
			local 临时属性表 = {6,8,10}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="红" then
		返回组.名称="全能"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {3,5,7}
			返回组.属性={
			[1]={类型="体质",数值=临时属性表[返回组.等级]},
			[2]={类型="力量",数值=临时属性表[返回组.等级]},
			[3]={类型="魔力",数值=临时属性表[返回组.等级]},
			[4]={类型="耐力",数值=临时属性表[返回组.等级]},
			[5]={类型="敏捷",数值=临时属性表[返回组.等级]},
						}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="药香"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {7,11,15}
			返回组.属性={[1]={类型="防御",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="黑" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="法门"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="红" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="屠兽"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="黑" and ys[6]=="红" and ys[2]=="红" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="逐兽"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="白" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="红" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="心印"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="速度",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="红" then
		返回组.名称="破杀"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="伤害",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="红" and ys[2]=="蓝" and ys[5]=="蓝" and ys[3]=="金" then
		返回组.名称="聚焦"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {4,7,10}
			返回组.属性={[1]={类型="灵力",数值=临时属性表[返回组.等级]}}
		end
	elseif ys[1]=="黑" and ys[4]=="白" and ys[6]=="蓝" and ys[2]=="蓝" and ys[5]=="绿" and ys[3]=="金" then
		返回组.名称="仙骨"
		返回组.门派="所有"
		if 返回组.等级~=0 then
			local 临时属性表 = {20,30,40}
			返回组.属性={[1]={类型="气血",数值=临时属性表[返回组.等级]}}
		end
	end
	return  返回组
end

