{"ast": null, "code": "var toString = require('./toString'),\n  upperFirst = require('./upperFirst');\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\nmodule.exports = capitalize;", "map": {"version": 3, "names": ["toString", "require", "upperFirst", "capitalize", "string", "toLowerCase", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/lodash/capitalize.js"], "sourcesContent": ["var toString = require('./toString'),\n    upperFirst = require('./upperFirst');\n\n/**\n * Converts the first character of `string` to upper case and the remaining\n * to lower case.\n *\n * @static\n * @memberOf _\n * @since 3.0.0\n * @category String\n * @param {string} [string=''] The string to capitalize.\n * @returns {string} Returns the capitalized string.\n * @example\n *\n * _.capitalize('FRED');\n * // => 'Fred'\n */\nfunction capitalize(string) {\n  return upperFirst(toString(string).toLowerCase());\n}\n\nmodule.exports = capitalize;\n"], "mappings": "AAAA,IAAIA,QAAQ,GAAGC,OAAO,CAAC,YAAY,CAAC;EAChCC,UAAU,GAAGD,OAAO,CAAC,cAAc,CAAC;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAOF,UAAU,CAACF,QAAQ,CAACI,MAAM,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AACnD;AAEAC,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}