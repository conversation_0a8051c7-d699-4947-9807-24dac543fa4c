{"ast": null, "code": "import * as React from 'react';\nimport { Item } from \"../Item\";\nexport default function useChildren(list, startIndex, endIndex, scrollWidth, offsetX, setNodeRef, renderFunc, _ref) {\n  var getKey = _ref.getKey;\n  return list.slice(startIndex, endIndex + 1).map(function (item, index) {\n    var eleIndex = startIndex + index;\n    var node = renderFunc(item, eleIndex, {\n      style: {\n        width: scrollWidth\n      },\n      offsetX: offsetX\n    });\n    var key = getKey(item);\n    return /*#__PURE__*/React.createElement(Item, {\n      key: key,\n      setRef: function setRef(ele) {\n        return setNodeRef(item, ele);\n      }\n    }, node);\n  });\n}", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "useChildren", "list", "startIndex", "endIndex", "scrollWidth", "offsetX", "setNodeRef", "renderFunc", "_ref", "<PERSON><PERSON><PERSON>", "slice", "map", "item", "index", "eleIndex", "node", "style", "width", "key", "createElement", "setRef", "ele"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-virtual-list/es/hooks/useChildren.js"], "sourcesContent": ["import * as React from 'react';\nimport { Item } from \"../Item\";\nexport default function useChildren(list, startIndex, endIndex, scrollWidth, offsetX, setNodeRef, renderFunc, _ref) {\n  var getKey = _ref.getKey;\n  return list.slice(startIndex, endIndex + 1).map(function (item, index) {\n    var eleIndex = startIndex + index;\n    var node = renderFunc(item, eleIndex, {\n      style: {\n        width: scrollWidth\n      },\n      offsetX: offsetX\n    });\n    var key = getKey(item);\n    return /*#__PURE__*/React.createElement(Item, {\n      key: key,\n      setRef: function setRef(ele) {\n        return setNodeRef(item, ele);\n      }\n    }, node);\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,SAAS;AAC9B,eAAe,SAASC,WAAWA,CAACC,IAAI,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,OAAO,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,EAAE;EAClH,IAAIC,MAAM,GAAGD,IAAI,CAACC,MAAM;EACxB,OAAOR,IAAI,CAACS,KAAK,CAACR,UAAU,EAAEC,QAAQ,GAAG,CAAC,CAAC,CAACQ,GAAG,CAAC,UAAUC,IAAI,EAAEC,KAAK,EAAE;IACrE,IAAIC,QAAQ,GAAGZ,UAAU,GAAGW,KAAK;IACjC,IAAIE,IAAI,GAAGR,UAAU,CAACK,IAAI,EAAEE,QAAQ,EAAE;MACpCE,KAAK,EAAE;QACLC,KAAK,EAAEb;MACT,CAAC;MACDC,OAAO,EAAEA;IACX,CAAC,CAAC;IACF,IAAIa,GAAG,GAAGT,MAAM,CAACG,IAAI,CAAC;IACtB,OAAO,aAAad,KAAK,CAACqB,aAAa,CAACpB,IAAI,EAAE;MAC5CmB,GAAG,EAAEA,GAAG;MACRE,MAAM,EAAE,SAASA,MAAMA,CAACC,GAAG,EAAE;QAC3B,OAAOf,UAAU,CAACM,IAAI,EAAES,GAAG,CAAC;MAC9B;IACF,CAAC,EAAEN,IAAI,CAAC;EACV,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}