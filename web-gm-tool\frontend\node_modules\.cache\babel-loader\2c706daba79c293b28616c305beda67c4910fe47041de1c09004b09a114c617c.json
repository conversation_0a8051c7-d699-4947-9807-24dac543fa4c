{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\n/**\n * Create Immutable pair for `makeImmutable` and `responseImmutable`.\n */\nexport default function createImmutable() {\n  var ImmutableContext = /*#__PURE__*/React.createContext(null);\n\n  /**\n   * Get render update mark by `makeImmutable` root.\n   * Do not deps on the return value as render times\n   * but only use for `useMemo` or `useCallback` deps.\n   */\n  function useImmutableMark() {\n    return React.useContext(ImmutableContext);\n  }\n\n  /**\n  * Wrapped Component will be marked as Immutable.\n  * When Component parent trigger render,\n  * it will notice children component (use with `responseImmutable`) node that parent has updated.\n  * @param Component Passed Component\n  * @param triggerRender Customize trigger `responseImmutable` children re-render logic. De<PERSON><PERSON> will always trigger re-render when this component re-render.\n  */\n  function makeImmutable(Component, shouldTriggerRender) {\n    var refAble = supportRef(Component);\n    var ImmutableComponent = function ImmutableComponent(props, ref) {\n      var refProps = refAble ? {\n        ref: ref\n      } : {};\n      var renderTimesRef = React.useRef(0);\n      var prevProps = React.useRef(props);\n\n      // If parent has the context, we do not wrap it\n      var mark = useImmutableMark();\n      if (mark !== null) {\n        return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n      }\n      if (\n      // Always trigger re-render if not provide `notTriggerRender`\n      !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n        renderTimesRef.current += 1;\n      }\n      prevProps.current = props;\n      return /*#__PURE__*/React.createElement(ImmutableContext.Provider, {\n        value: renderTimesRef.current\n      }, /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps)));\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n    }\n    return refAble ? /*#__PURE__*/React.forwardRef(ImmutableComponent) : ImmutableComponent;\n  }\n\n  /**\n   * Wrapped Component with `React.memo`.\n   * But will rerender when parent with `makeImmutable` rerender.\n   */\n  function responseImmutable(Component, propsAreEqual) {\n    var refAble = supportRef(Component);\n    var ImmutableComponent = function ImmutableComponent(props, ref) {\n      var refProps = refAble ? {\n        ref: ref\n      } : {};\n      useImmutableMark();\n      return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n    }\n    return refAble ? /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/React.memo(ImmutableComponent, propsAreEqual);\n  }\n  return {\n    makeImmutable: makeImmutable,\n    responseImmutable: responseImmutable,\n    useImmutableMark: useImmutableMark\n  };\n}", "map": {"version": 3, "names": ["_extends", "supportRef", "React", "createImmutable", "ImmutableContext", "createContext", "useImmutableMark", "useContext", "makeImmutable", "Component", "should<PERSON>rigger<PERSON>ender", "refAble", "ImmutableComponent", "props", "ref", "refProps", "renderTimesRef", "useRef", "prevProps", "mark", "createElement", "current", "Provider", "value", "process", "env", "NODE_ENV", "displayName", "concat", "name", "forwardRef", "responseImmutable", "propsAreEqual", "memo"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@rc-component/context/es/Immutable.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { supportRef } from \"rc-util/es/ref\";\nimport * as React from 'react';\n/**\n * Create Immutable pair for `makeImmutable` and `responseImmutable`.\n */\nexport default function createImmutable() {\n  var ImmutableContext = /*#__PURE__*/React.createContext(null);\n\n  /**\n   * Get render update mark by `makeImmutable` root.\n   * Do not deps on the return value as render times\n   * but only use for `useMemo` or `useCallback` deps.\n   */\n  function useImmutableMark() {\n    return React.useContext(ImmutableContext);\n  }\n\n  /**\n  * Wrapped Component will be marked as Immutable.\n  * When Component parent trigger render,\n  * it will notice children component (use with `responseImmutable`) node that parent has updated.\n  * @param Component Passed Component\n  * @param triggerRender Customize trigger `responseImmutable` children re-render logic. De<PERSON><PERSON> will always trigger re-render when this component re-render.\n  */\n  function makeImmutable(Component, shouldTriggerRender) {\n    var refAble = supportRef(Component);\n    var ImmutableComponent = function ImmutableComponent(props, ref) {\n      var refProps = refAble ? {\n        ref: ref\n      } : {};\n      var renderTimesRef = React.useRef(0);\n      var prevProps = React.useRef(props);\n\n      // If parent has the context, we do not wrap it\n      var mark = useImmutableMark();\n      if (mark !== null) {\n        return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n      }\n      if (\n      // Always trigger re-render if not provide `notTriggerRender`\n      !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {\n        renderTimesRef.current += 1;\n      }\n      prevProps.current = props;\n      return /*#__PURE__*/React.createElement(ImmutableContext.Provider, {\n        value: renderTimesRef.current\n      }, /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps)));\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      ImmutableComponent.displayName = \"ImmutableRoot(\".concat(Component.displayName || Component.name, \")\");\n    }\n    return refAble ? /*#__PURE__*/React.forwardRef(ImmutableComponent) : ImmutableComponent;\n  }\n\n  /**\n   * Wrapped Component with `React.memo`.\n   * But will rerender when parent with `makeImmutable` rerender.\n   */\n  function responseImmutable(Component, propsAreEqual) {\n    var refAble = supportRef(Component);\n    var ImmutableComponent = function ImmutableComponent(props, ref) {\n      var refProps = refAble ? {\n        ref: ref\n      } : {};\n      useImmutableMark();\n      return /*#__PURE__*/React.createElement(Component, _extends({}, props, refProps));\n    };\n    if (process.env.NODE_ENV !== 'production') {\n      ImmutableComponent.displayName = \"ImmutableResponse(\".concat(Component.displayName || Component.name, \")\");\n    }\n    return refAble ? /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(ImmutableComponent), propsAreEqual) : /*#__PURE__*/React.memo(ImmutableComponent, propsAreEqual);\n  }\n  return {\n    makeImmutable: makeImmutable,\n    responseImmutable: responseImmutable,\n    useImmutableMark: useImmutableMark\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,UAAU,QAAQ,gBAAgB;AAC3C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAAA,EAAG;EACxC,IAAIC,gBAAgB,GAAG,aAAaF,KAAK,CAACG,aAAa,CAAC,IAAI,CAAC;;EAE7D;AACF;AACA;AACA;AACA;EACE,SAASC,gBAAgBA,CAAA,EAAG;IAC1B,OAAOJ,KAAK,CAACK,UAAU,CAACH,gBAAgB,CAAC;EAC3C;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,SAASI,aAAaA,CAACC,SAAS,EAAEC,mBAAmB,EAAE;IACrD,IAAIC,OAAO,GAAGV,UAAU,CAACQ,SAAS,CAAC;IACnC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;MAC/D,IAAIC,QAAQ,GAAGJ,OAAO,GAAG;QACvBG,GAAG,EAAEA;MACP,CAAC,GAAG,CAAC,CAAC;MACN,IAAIE,cAAc,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC,CAAC;MACpC,IAAIC,SAAS,GAAGhB,KAAK,CAACe,MAAM,CAACJ,KAAK,CAAC;;MAEnC;MACA,IAAIM,IAAI,GAAGb,gBAAgB,CAAC,CAAC;MAC7B,IAAIa,IAAI,KAAK,IAAI,EAAE;QACjB,OAAO,aAAajB,KAAK,CAACkB,aAAa,CAACX,SAAS,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,EAAEE,QAAQ,CAAC,CAAC;MACnF;MACA;MACA;MACA,CAACL,mBAAmB,IAAIA,mBAAmB,CAACQ,SAAS,CAACG,OAAO,EAAER,KAAK,CAAC,EAAE;QACrEG,cAAc,CAACK,OAAO,IAAI,CAAC;MAC7B;MACAH,SAAS,CAACG,OAAO,GAAGR,KAAK;MACzB,OAAO,aAAaX,KAAK,CAACkB,aAAa,CAAChB,gBAAgB,CAACkB,QAAQ,EAAE;QACjEC,KAAK,EAAEP,cAAc,CAACK;MACxB,CAAC,EAAE,aAAanB,KAAK,CAACkB,aAAa,CAACX,SAAS,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,EAAEE,QAAQ,CAAC,CAAC,CAAC;IAChF,CAAC;IACD,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCd,kBAAkB,CAACe,WAAW,GAAG,gBAAgB,CAACC,MAAM,CAACnB,SAAS,CAACkB,WAAW,IAAIlB,SAAS,CAACoB,IAAI,EAAE,GAAG,CAAC;IACxG;IACA,OAAOlB,OAAO,GAAG,aAAaT,KAAK,CAAC4B,UAAU,CAAClB,kBAAkB,CAAC,GAAGA,kBAAkB;EACzF;;EAEA;AACF;AACA;AACA;EACE,SAASmB,iBAAiBA,CAACtB,SAAS,EAAEuB,aAAa,EAAE;IACnD,IAAIrB,OAAO,GAAGV,UAAU,CAACQ,SAAS,CAAC;IACnC,IAAIG,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;MAC/D,IAAIC,QAAQ,GAAGJ,OAAO,GAAG;QACvBG,GAAG,EAAEA;MACP,CAAC,GAAG,CAAC,CAAC;MACNR,gBAAgB,CAAC,CAAC;MAClB,OAAO,aAAaJ,KAAK,CAACkB,aAAa,CAACX,SAAS,EAAET,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,EAAEE,QAAQ,CAAC,CAAC;IACnF,CAAC;IACD,IAAIS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzCd,kBAAkB,CAACe,WAAW,GAAG,oBAAoB,CAACC,MAAM,CAACnB,SAAS,CAACkB,WAAW,IAAIlB,SAAS,CAACoB,IAAI,EAAE,GAAG,CAAC;IAC5G;IACA,OAAOlB,OAAO,GAAG,aAAaT,KAAK,CAAC+B,IAAI,CAAE,aAAa/B,KAAK,CAAC4B,UAAU,CAAClB,kBAAkB,CAAC,EAAEoB,aAAa,CAAC,GAAG,aAAa9B,KAAK,CAAC+B,IAAI,CAACrB,kBAAkB,EAAEoB,aAAa,CAAC;EAC1K;EACA,OAAO;IACLxB,aAAa,EAAEA,aAAa;IAC5BuB,iBAAiB,EAAEA,iBAAiB;IACpCzB,gBAAgB,EAAEA;EACpB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}