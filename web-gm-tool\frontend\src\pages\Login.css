.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.login-card {
  width: 100%;
  max-width: 400px;
  margin: 0 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  margin-bottom: 8px !important;
  color: #1890ff;
  font-weight: 600;
}

.login-title .anticon {
  margin-right: 8px;
}

.login-footer {
  text-align: center;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-input-affix-wrapper {
  border-radius: 8px;
}

.ant-btn-primary {
  border-radius: 8px;
  height: 48px;
  font-size: 16px;
  font-weight: 500;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.ant-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.ant-alert {
  border-radius: 8px;
  border: none;
  background: rgba(24, 144, 255, 0.05);
}

.ant-divider-horizontal.ant-divider-with-text-left::before {
  width: 20%;
}

.ant-divider-horizontal.ant-divider-with-text-left::after {
  width: 80%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-card {
    margin: 20px;
    max-width: none;
  }
  
  .login-container {
    padding: 20px;
  }
}

/* 加载动画 */
.ant-btn-loading-icon {
  margin-right: 8px;
}

/* 输入框聚焦效果 */
.ant-input-affix-wrapper:focus,
.ant-input-affix-wrapper-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 表单验证样式 */
.ant-form-item-has-error .ant-input-affix-wrapper {
  border-color: #ff4d4f;
}

.ant-form-item-has-error .ant-input-affix-wrapper:focus {
  box-shadow: 0 0 0 2px rgba(255, 77, 79, 0.2);
}
