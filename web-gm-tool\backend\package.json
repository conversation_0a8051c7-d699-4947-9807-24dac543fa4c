{"name": "web-gm-tool-backend", "version": "1.0.0", "description": "Web版GM工具后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.2", "msgpack-lite": "^0.1.26", "ws": "^8.13.0", "cors": "^2.8.5", "dotenv": "^16.3.1"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2"}, "keywords": ["gm-tool", "game-management", "websocket"], "author": "Web GM Tool Team", "license": "MIT"}