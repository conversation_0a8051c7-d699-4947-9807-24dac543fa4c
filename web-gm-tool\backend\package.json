{"name": "web-gm-tool-backend", "version": "1.0.0", "description": "Web版GM工具后端服务", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "ffi-napi": "^4.0.3", "msgpack-lite": "^0.1.26", "ref-napi": "^3.0.3", "socket.io": "^4.7.2", "ws": "^8.13.0"}, "devDependencies": {"jest": "^29.6.2", "nodemon": "^3.0.1"}, "keywords": ["gm-tool", "game-management", "websocket"], "author": "Web GM Tool Team", "license": "MIT"}