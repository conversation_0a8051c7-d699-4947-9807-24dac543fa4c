{"name": "web-gm-tool-frontend", "version": "1.0.0", "description": "Web版GM工具前端应用", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.4.3", "antd": "^5.0.0", "@ant-design/icons": "^4.8.0", "socket.io-client": "^4.7.2", "axios": "^1.1.3", "dayjs": "^1.11.6", "lodash": "^4.17.21", "classnames": "^2.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/lodash": "^4.14.188"}, "proxy": "http://localhost:3001"}