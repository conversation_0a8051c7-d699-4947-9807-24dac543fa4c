/**
 * Web版GM工具后端服务器
 * 主入口文件
 */

const express = require('express');
const http = require('http');
const cors = require('cors');
const path = require('path');
require('dotenv').config();

const SocketHandler = require('./src/websocket/socketHandler');
const { constants } = require('./src/protocol');

// 创建Express应用
const app = express();
const server = http.createServer(app);

// 配置中间件
app.use(cors({
    origin: process.env.CORS_ORIGIN || "http://localhost:3000",
    credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 静态文件服务（如果需要）
app.use('/static', express.static(path.join(__dirname, 'public')));

// 创建WebSocket处理器
const socketHandler = new SocketHandler(server, {
    corsOrigin: process.env.CORS_ORIGIN || "http://localhost:3000"
});

// API路由
app.get('/', (req, res) => {
    res.json({
        name: 'Web GM Tool Backend',
        version: '1.0.0',
        status: 'running',
        timestamp: new Date().toISOString()
    });
});

// 健康检查接口
app.get('/health', (req, res) => {
    const stats = socketHandler.getOnlineStats();
    res.json({
        status: 'healthy',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        onlineStats: stats,
        timestamp: new Date().toISOString()
    });
});

// 获取协议常量
app.get('/api/constants', (req, res) => {
    res.json({
        success: true,
        data: {
            protocolIds: constants.PROTOCOL_IDS,
            userLevels: constants.USER_LEVELS,
            equipmentTypes: constants.EQUIPMENT_TYPES,
            petTypes: constants.PET_TYPES,
            activityTypes: constants.ACTIVITY_TYPES,
            errorMessages: constants.ERROR_MESSAGES,
            successMessages: constants.SUCCESS_MESSAGES
        }
    });
});

// 获取在线统计
app.get('/api/stats', (req, res) => {
    const stats = socketHandler.getOnlineStats();
    res.json({
        success: true,
        data: stats
    });
});

// 协议测试接口
app.post('/api/test/protocol', (req, res) => {
    try {
        const { protocolHandler } = require('./src/protocol');
        const { action, data } = req.body;

        let result;
        switch (action) {
            case 'encrypt':
                result = protocolHandler.encrypt(data.text);
                break;
            case 'decrypt':
                result = protocolHandler.decrypt(data.text);
                break;
            case 'createLoginPacket':
                result = protocolHandler.createLoginPacket(data.username, data.password);
                break;
            case 'createRechargePacket':
                result = protocolHandler.createRechargePacket(data.type, data.account, data.amount);
                break;
            default:
                throw new Error('不支持的测试操作');
        }

        res.json({
            success: true,
            data: {
                action,
                result: Buffer.isBuffer(result) ? Array.from(result) : result
            }
        });
    } catch (error) {
        res.status(400).json({
            success: false,
            message: error.message
        });
    }
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('[服务器] 未处理的错误:', error);
    res.status(500).json({
        success: false,
        message: '服务器内部错误',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        message: '接口不存在'
    });
});

// 启动服务器
const PORT = process.env.PORT || 3001;
const HOST = process.env.HOST || '0.0.0.0';

server.listen(PORT, HOST, () => {
    console.log('=================================');
    console.log('🚀 Web GM Tool Backend Server');
    console.log('=================================');
    console.log(`📡 服务器地址: http://${HOST}:${PORT}`);
    console.log(`🌐 WebSocket地址: ws://${HOST}:${PORT}`);
    console.log(`🔧 环境: ${process.env.NODE_ENV || 'development'}`);
    console.log(`📊 健康检查: http://${HOST}:${PORT}/health`);
    console.log(`📋 API文档: http://${HOST}:${PORT}/api/constants`);
    console.log('=================================');
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('[服务器] 收到SIGTERM信号，开始优雅关闭...');
    server.close(() => {
        console.log('[服务器] 服务器已关闭');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('[服务器] 收到SIGINT信号，开始优雅关闭...');
    server.close(() => {
        console.log('[服务器] 服务器已关闭');
        process.exit(0);
    });
});

// 未捕获异常处理
process.on('uncaughtException', (error) => {
    console.error('[服务器] 未捕获的异常:', error);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('[服务器] 未处理的Promise拒绝:', reason);
    console.error('Promise:', promise);
});

module.exports = { app, server, socketHandler };
