--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-06-04 14:52:02
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解所以资源
--======================================================================--
function 取门派巡逻地图(m,地图)
 local cs
  if m == "方寸山" then
    cs = {1135,1137}
  elseif m == "女儿村" then
    cs = {1142,1143}
  elseif m == "神木林" then
    cs = {1138,1154}
  elseif m == "大唐官府" then
    cs = {1198,1054}
  elseif m == "化生寺" then
    cs = {1002,1528,1043}
  elseif m == "阴曹地府" then
    cs = {1122,1123,102}
  elseif m == "盘丝洞" then
    cs = {1513,1144}
  elseif m == "无底洞" then
    cs = {1139,1156}
  elseif m == "狮驼岭" then
    cs = {1131,1134}
  elseif m == "魔王寨" then
    cs = {1512,1145}
  elseif m == "普陀山" then
    cs = {1140,1141}
  elseif m == "天宫" then
    cs = {1111,1112}
  elseif m == "凌波城" then
    cs = {1150}
  elseif m == "五庄观" then
    cs = {1146,1147}
  elseif m == "龙宫" then
    cs = {1116,1117}
  elseif m == "九黎城" then
    cs = {2008}
  -- elseif m == "女魃墓" then
  --   cs = {1249,1252}
  -- elseif m == "天机城" then
  --   cs = {1250,1253}
  -- elseif m == "花果山" then
  --   cs = {1251}
  else
    return false
  end
	for n=1,#cs do
		if cs[n]==地图 then
			return true
		end
	end
  return false
end
function 取十二生肖任务(m)
  local cs
  if m == "子鼠" then
    cs = {1001,72,63}
  elseif m == "丑牛" then
    cs = {1001,37,37}
  elseif m == "寅虎" then
    cs = {1193,46,121}
  elseif m == "卯兔" then
    cs = {1193,131,82}
  elseif m == "辰龙" then
    cs = {1501,7,88}
  elseif m == "巳蛇" then
    cs = {1501,101,102}
  elseif m == "午马" then
    cs = {1506,174,31}
  elseif m == "未羊" then
    cs = {1514,61,125}
  elseif m == "申猴" then
    cs = {1514,109,77}
  elseif m == "酉鸡" then
    cs = {1173,76,29}
  elseif m == "戌狗" then
    cs = {1173,20,18}
  elseif m == "亥猪" then
    cs = {1173,175,122}
  end
  return cs
end

function 取文韵巡逻地图(m,地图)--------远方文韵墨香
 local cs
  if m == "方寸山" then
    cs = {1135,1137}
  elseif m == "女儿村" then
    cs = {1142,1143}
  elseif m == "神木林" then
    cs = {1138,1154}
  elseif m == "大唐官府" then
    cs = {1198,1054}
  elseif m == "化生寺" then
    cs = {1002,1528,1043}
  elseif m == "阴曹地府" then
    cs = {1122,1123,102}
  elseif m == "盘丝洞" then
    cs = {1513,1144}
  elseif m == "无底洞" then
    cs = {1139,1156}
  elseif m == "狮驼岭" then
    cs = {1131,1134}
  elseif m == "魔王寨" then
    cs = {1512,1145}
  elseif m == "普陀山" then
    cs = {1140,1141}
  elseif m == "天宫" then
    cs = {1111,1112}
  elseif m == "凌波城" then
    cs = {1150}
  elseif m == "五庄观" then
    cs = {1146,1147}
  elseif m == "龙宫" then
    cs = {1116,1117}
  elseif m == "女魃墓" then
    cs = {1249,1252}
  elseif m == "天机城" then
    cs = {1250,1253}
  elseif m == "花果山" then
    cs = {1251}
  else
    return false
  end
  for n=1,#cs do
    if cs[n]==地图 then
      return true
    end
  end
  return false
end
function 取门派地图(m)
	local cs
  if m == "方寸山" then
    cs = {1135,72,63}
  elseif m == "女儿村" then
    cs = {1142,37,37}
  elseif m == "神木林" then
    cs = {1138,46,121}
  elseif m == "大唐官府" then
    cs = {1198,131,82}
  elseif m == "化生寺" then
    cs = {1002,7,88}
  elseif m == "阴曹地府" then
    cs = {1122,101,102}
  elseif m == "盘丝洞" then
    cs = {1513,174,31}
  elseif m == "无底洞" then
    cs = {1139,61,125}
  elseif m == "狮驼岭" then
    cs = {1131,109,77}
  elseif m == "魔王寨" then
    cs = {1512,76,29}
  elseif m == "普陀山" then
    cs = {1140,20,18}
  elseif m == "天宫" then
    cs = {1111,175,122}
  elseif m == "凌波城" then
    cs = {1150,33,67}
  elseif m == "五庄观" then
    cs = {1146,26,55}
  elseif m == "龙宫" then
    cs = {1116,71,77}
  -- elseif m == "天机城" then
  --   cs = {1250,63,92}
  -- elseif m == "女魃墓" then
  --   cs = {1249,51,44}
  -- elseif m == "花果山" then
  --   cs = {1251,38,76}
  end
  return cs
end
function 取文韵地图(m)--------远方文韵墨香
  local cs
  if m == "方寸山" then
    cs = {1135,72,63}
  elseif m == "女儿村" then
    cs = {1142,37,37}
  elseif m == "神木林" then
    cs = {1138,46,121}
  elseif m == "大唐官府" then
    cs = {1198,131,82}
  elseif m == "化生寺" then
    cs = {1002,7,88}
  elseif m == "阴曹地府" then
    cs = {1122,101,102}
  elseif m == "盘丝洞" then
    cs = {1513,174,31}
  elseif m == "无底洞" then
    cs = {1139,61,125}
  elseif m == "狮驼岭" then
    cs = {1131,109,77}
  elseif m == "魔王寨" then
    cs = {1512,76,29}
  elseif m == "普陀山" then
    cs = {1140,20,18}
  elseif m == "天宫" then
    cs = {1111,175,122}
  elseif m == "凌波城" then
    cs = {1150,33,67}
  elseif m == "五庄观" then
    cs = {1146,26,55}
  elseif m == "龙宫" then
    cs = {1116,71,77}
  elseif m == "天机城" then
    cs = {1250,63,92}
  elseif m == "女魃墓" then
    cs = {1249,51,44}
  elseif m == "花果山" then
    cs = {1251,38,76}
  end
  return cs
end

function 判断文韵召唤兽选项(id,事件) ---------远方文韵墨香
  local 任务id=玩家数据[id].角色:取任务(112)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=3 then
    return
  end
  local 选项={}
  local 模型=任务数据[任务id].bb
  local 要求
  if 任务数据[任务id].要求=="武艺超群" then
    要求="力量"
  elseif 任务数据[任务id].要求=="法力高深" then
    要求="魔力"
  elseif 任务数据[任务id].要求=="身强体壮" then
    要求="体质"
  elseif 任务数据[任务id].要求=="刀枪不入" then
    要求="耐力"
  elseif 任务数据[任务id].要求=="身手敏捷" then
    要求="敏捷"
  end
  for n=1,#玩家数据[id].召唤兽.数据 do
    if 玩家数据[id].召唤兽.数据[n].模型==模型 then
      if 要求==nil then
        选项[#选项+1]={对话=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=n}
      else
        local 通过=2
        for i=1,#玩家数据[id].召唤兽.数据 do
          if i~=n then
            if 玩家数据[id].召唤兽.数据[n][要求]<玩家数据[id].召唤兽.数据[i][要求] then
              通过=通过-1
            end
          end
        end
        if 通过>=0 then
          选项[#选项+1]={对话=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=n}
        end
      end
    end
  end
  for n=1,#选项 do
    if 选项[n].对话==事件 then
      玩家数据[id].召唤兽:删除处理(id,选项[n].编号)
      任务处理类:完成文韵任务(id,3)
      return
    end
  end
  添加最后对话(id,"你所选的这只召唤兽并不是对方想要的")
end




function 取符合文韵召唤兽选项(id) ---------远方文韵墨香
  local 任务id=玩家数据[id].角色:取任务(112)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=3 then
    return
  end
  local 选项={}
  local 模型=任务数据[任务id].bb
  local 要求
  if 任务数据[任务id].要求=="武艺超群" then
    要求="力量"
  elseif 任务数据[任务id].要求=="法力高深" then
    要求="魔力"
  elseif 任务数据[任务id].要求=="身强体壮" then
    要求="体质"
  elseif 任务数据[任务id].要求=="刀枪不入" then
    要求="耐力"
  elseif 任务数据[任务id].要求=="身手敏捷" then
    要求="敏捷"
  end
  for n=1,#玩家数据[id].召唤兽.数据 do
        if 玩家数据[id].召唤兽.数据[n].模型==模型 and 玩家数据[id].召唤兽.数据[n].名称==模型 then
            if 要求==nil then
              选项[#选项+1]=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级)
            else
                local 通过=2
                for i=1,#玩家数据[id].召唤兽.数据 do

                    if i~=n then
                        if 玩家数据[id].召唤兽.数据[n][要求]<玩家数据[id].召唤兽.数据[i][要求] then
                         通过=通过-1
                      end
                  end
                end
                if 通过>=0 then
                   选项[#选项+1]=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级)
                end
          end
        end
  end
  return 选项
end


function 判断师门召唤兽选项(id,事件)
	local 任务id=玩家数据[id].角色:取任务(111)
	if 任务数据[任务id]==nil or 任务数据[任务id].分类~=3 then
		return
	end
	local 选项={}
	local 模型=任务数据[任务id].bb
	local 要求
	if 任务数据[任务id].要求=="武艺超群" then
		要求="力量"
	elseif 任务数据[任务id].要求=="法力高深" then
		要求="魔力"
	elseif 任务数据[任务id].要求=="身强体壮" then
		要求="体质"
	elseif 任务数据[任务id].要求=="刀枪不入" then
		要求="耐力"
	elseif 任务数据[任务id].要求=="身手敏捷" then
		要求="敏捷"
	end
	for n=1,#玩家数据[id].召唤兽.数据 do
		if 玩家数据[id].召唤兽.数据[n].模型==模型 then
			if 要求==nil then
				选项[#选项+1]={对话=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=n}
			else
				local 通过=2
				for i=1,#玩家数据[id].召唤兽.数据 do
					if i~=n then
						if 玩家数据[id].召唤兽.数据[n][要求]<玩家数据[id].召唤兽.数据[i][要求] then
							通过=通过-1
						end
					end
				end
				if 通过>=0 then
					选项[#选项+1]={对话=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=n}
				end
			end
		end
	end
	for n=1,#选项 do
		if 选项[n].对话==事件 then
			玩家数据[id].召唤兽:删除处理(id,选项[n].编号)
			任务处理类:完成门派任务(id,3)
			return
		end
	end
	添加最后对话(id,"你所选的这只召唤兽并不是对方想要的")
end

function 取符合师门召唤兽选项(id)
	local 任务id=玩家数据[id].角色:取任务(111)
	if 任务数据[任务id]==nil or 任务数据[任务id].分类~=3 then
		return
	end
	local 选项={}
	local 模型=任务数据[任务id].bb
	local 要求
	if 任务数据[任务id].要求=="武艺超群" then
		要求="力量"
	elseif 任务数据[任务id].要求=="法力高深" then
		要求="魔力"
	elseif 任务数据[任务id].要求=="身强体壮" then
		要求="体质"
	elseif 任务数据[任务id].要求=="刀枪不入" then
		要求="耐力"
	elseif 任务数据[任务id].要求=="身手敏捷" then
		要求="敏捷"
	end
	for n=1,#玩家数据[id].召唤兽.数据 do
        if 玩家数据[id].召唤兽.数据[n].模型==模型 and 玩家数据[id].召唤兽.数据[n].名称==模型 then
            if 要求==nil then
              选项[#选项+1]=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级)
            else
           	    local 通过=2
           	    for i=1,#玩家数据[id].召唤兽.数据 do

                    if i~=n then
                        if 玩家数据[id].召唤兽.数据[n][要求]<玩家数据[id].召唤兽.数据[i][要求] then
                         通过=通过-1
                     	end
                 	end
           	  	end
           	    if 通过>=0 then
                   选项[#选项+1]=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级)
           	   	end
         	end
       	end
 	end
	return 选项
end

Q_师门送信={
  {地图=1150,名称="二郎神"},
  {地图=1141,名称="观音姐姐"}
  ,{地图=1124,名称="地藏王"}
  ,{地图=1117,名称="东海龙王"}
  ,{地图=1001,名称="刘副将"}
  ,{地图=1001,名称="张老财"}
  ,{地图=1033,名称="小桃红"}
  ,{地图=1033,名称="陈妈妈"}
  ,{地图=1044,名称="魏征"}
  ,{地图=1044,名称="李世民"}
  ,{地图=1054,名称="程咬金"}
  ,{地图=1002,名称="慧海"}
  ,{地图=1043,名称="空度禅师"}
  ,{地图=1143,名称="孙婆婆"}
  ,{地图=1137,名称="菩提老祖"}
  ,{地图=1112,名称="李靖"}
  ,{地图=1145,名称="牛魔王"}
  ,{地图=1147,名称="镇元子"}
  ,{地图=1134,名称="大大王"}
  ,{地图=1154,名称="巫奎虎"}
  ,{地图=1156,名称="地涌夫人"}
  ,{地图=1144,名称="白晶晶"}
  ,{地图=2008,名称="刑天"}

}

Q_文韵送信={---------远方文韵墨香
  {地图=1150,名称="二郎神"}
  ,{地图=1141,名称="观音姐姐"}
  ,{地图=1124,名称="地藏王"}
  ,{地图=1117,名称="东海龙王"}
  ,{地图=1001,名称="刘副将"}
  ,{地图=1001,名称="张老财"}
  ,{地图=1033,名称="小桃红"}
  ,{地图=1033,名称="陈妈妈"}
  ,{地图=1044,名称="魏征"}
  ,{地图=1044,名称="李世民"}
  ,{地图=1054,名称="程咬金"}
  ,{地图=1002,名称="慧海"}
  ,{地图=1043,名称="空度禅师"}
  ,{地图=1143,名称="孙婆婆"}
  ,{地图=1137,名称="菩提老祖"}
  ,{地图=1112,名称="李靖"}
  ,{地图=1145,名称="牛魔王"}
  ,{地图=1147,名称="镇元子"}
  ,{地图=1134,名称="大大王"}
  ,{地图=1154,名称="巫奎虎"}
  ,{地图=1156,名称="地涌夫人"}
  ,{地图=1144,名称="白晶晶"}
}
Q_押镖数据={
  {地图=1150,名称="二郎神"},
  {地图=1141,名称="观音姐姐"}
  ,{地图=1124,名称="地藏王"}
  ,{地图=1117,名称="东海龙王"}
  ,{地图=1054,名称="程咬金"}
  ,{地图=1043,名称="空度禅师"}
  ,{地图=1143,名称="孙婆婆"}
  ,{地图=1137,名称="菩提老祖"}
  ,{地图=1112,名称="李靖"}
  ,{地图=1145,名称="牛魔王"}
  ,{地图=1147,名称="镇元子"}
  ,{地图=1134,名称="大大王"}
  ,{地图=1154,名称="巫奎虎"}
  ,{地图=1156,名称="地涌夫人"}
  ,{地图=1144,名称="白晶晶"}
}

Q_十二生肖怪物数据={
   子鼠={名称="子鼠",地图名称="长安城",模型="鼠先锋",x=54,y=135,方向=3,弟子={"鼠先锋"}}
  ,丑牛={名称="丑牛",地图名称="长安城",模型="踏云兽",x=505,y=212,方向=1,弟子={"踏云兽","踏云兽"}}
  ,寅虎={名称="寅虎",地图名称="江南野外",模型="噬天虎",x=17,y=99,方向=1,弟子={"噬天虎","噬天虎"}}
  ,卯兔={名称="卯兔",地图名称="江南野外",模型="兔子怪",x=137,y=21,方向=1,弟子={"兔子怪","兔子怪"}}
  ,辰龙={名称="辰龙",地图名称="建邺城",模型="蛟龙",x=32,y=9,方向=0,弟子={"蛟龙","蛟龙"}}
  ,巳蛇={名称="巳蛇",地图名称="建邺城",模型="千年蛇魅",x=263,y=110,方向=0,弟子={"千年蛇魅","千年蛇魅"}}
  ,午马={名称="午马",地图名称="东海湾",模型="马面",x=58,y=23,方向=0,弟子={"马面"}}
  ,未羊={名称="未羊",地图名称="花果山",模型="羊头怪",x=80,y=68,方向=0,弟子={"羊头怪","羊头怪"}}
  ,申猴={名称="申猴",地图名称="花果山",模型="长眉灵猴",x=126,y=12,方向=0,弟子={"长眉灵猴","长眉灵猴"}}
  ,酉鸡={名称="酉鸡",地图名称="大唐境外",模型="雷鸟人",x=66,y=26,方向=2,弟子={"雷鸟人","雷鸟人"}}
  ,戌狗={名称="戌狗",地图名称="大唐境外",模型="哮天犬",x=300,y=60,方向=0,弟子={"哮天犬"}}
  ,亥猪={名称="亥猪",地图名称="大唐境外",模型="野猪",x=576,y=89,方向=0,弟子={"野猪"}}
}

-- Q_门派法术1={
--   大唐官府={"横扫千军","后发制人"}
--   ,化生寺={"唧唧歪歪","推气过宫","雷击","奔雷咒"}
--   ,方寸山={"落雷符","五雷咒","催眠符","失心符","失魂符","定身符"}
--   ,女儿村={"雨落寒沙","似玉生香","莲步轻舞","如花解语"}
--   ,神木林={"落叶萧萧","荆棘舞","雾杀","星月之惠","蜜润"}
--   ,盘丝洞={"盘丝阵","天罗地网","含情脉脉","勾魂","摄魄"}
--   ,魔王寨={"飞砂走石","三昧真火","牛劲","魔王回首"}
--   ,狮驼岭={"狮搏","威慑","象形","鹰击","连环击"}
--   ,阴曹地府={"阎罗令","判官令","尸腐毒"}
--   ,无底洞={"夺魄令","煞气诀","夺命咒","明光宝烛","地涌金莲"}
--   ,天宫={"天雷斩","五雷轰顶","错乱","百万神兵"}
--   ,普陀山={"靛沧海","日光华","地裂火","苍茫树","紧箍咒","普渡众生"}
--   ,凌波城={"裂石","断岳势","浪涌","惊涛怒","天崩地裂","翻江搅海","碎星诀"}
--   ,五庄观={"飘渺式","日月乾坤","烟雨剑法","生命之泉"}
--   ,龙宫={"龙卷雨击","龙腾","龙吟"}
--   ,九黎城 = {"横扫千军","飘渺式","烟雨剑法","天雷斩"}
-- }

Q_门派法术={
  大唐官府={"横扫千军","后发制人","杀气诀","反间之计"}
  ,化生寺={"唧唧歪歪","推气过宫","舍身取义"}
  ,方寸山={"落雷符","五雷咒","离魂符","失魂符","定身符","催眠符"}
  ,女儿村={"一笑倾城","莲步轻舞","如花解语","似玉生香","满天花雨","雨落寒沙"}
  ,神木林={"雾杀","落叶萧萧","荆棘舞","尘土刃","冰川怒","血雨","蜜润"}
  ,龙宫={"龙吟","逆鳞","龙卷雨击","龙腾","二龙戏珠"}
  ,盘丝洞={"绝命毒牙","蛛丝缠绕","天罗地网","勾魂","摄魄","含情脉脉"}
  ,魔王寨={"飞砂走石","三昧真火","摇头摆尾","火甲术","牛劲","魔王回首","秘传飞砂走石"}
  ,狮驼岭={"威慑","象形","鹰击","狮搏","连环击"}
  ,阴曹地府={"阎罗令","判官令","寡欲令","尸腐毒"}
  ,无底洞={"夺命咒","追魂刺","地涌金莲","焕生咒"}
  ,天宫={"五雷轰顶","错乱","天雷斩","雷霆万钧"}
  ,普陀山={"剑意莲心","紧箍咒","日光华","靛沧海","巨岩破","苍茫树","地裂火","普渡众生","杨柳甘露"}
  ,凌波城={"裂石","断岳势","浪涌","惊涛怒","天崩地裂","翻江搅海","碎星诀"}
  ,五庄观={"烟雨剑法","飘渺式","炼气化神","生命之泉","日月乾坤"}
  ,九黎城 = {"横扫千军","飘渺式","烟雨剑法","天雷斩"}

}

Q_十二生肖法术={
  子鼠={"横扫千军","后发制人"}
  ,丑牛={"唧唧歪歪","推气过宫","雷击","奔雷咒"}
  ,寅虎={"落雷符","五雷咒","催眠符","失心符","失魂符","定身符"}
  ,卯兔={"雨落寒沙","似玉生香","莲步轻舞","如花解语"}
  ,辰龙={"落叶萧萧","荆棘舞","雾杀","星月之惠","蜜润"}
  ,巳蛇={"盘丝阵","天罗地网","含情脉脉","勾魂","摄魄"}
  ,午马={"飞砂走石","三昧真火","牛劲","魔王回首"}
  ,未羊={"狮搏","威慑","象形","鹰击","连环击"}
  ,申猴={"阎罗令","判官令","尸腐毒"}
  ,酉鸡={"夺魄令","煞气诀","夺命咒","明光宝烛","地涌金莲"}
  ,戌狗={"天雷斩","五雷轰顶","错乱","百万神兵"}
  ,亥猪={"靛沧海","日光华","地裂火","苍茫树","紧箍咒","普渡众生"}
}

Q_闯关数据={
  大唐官府={名称="剑侠客",模型="剑侠客",武器={名称="四法青云",等级=120},x=139,y=46,方向=1,弟子={"护卫"}}
  ,化生寺={名称="逍遥生",模型="逍遥生",武器={名称="秋水人家",等级=120},x=34,y=74,方向=1,弟子={"古代瑞兽","大力金刚"}}
  ,方寸山={名称="飞燕女",模型="飞燕女",武器={名称="九天金线",等级=120},x=46,y=125,方向=1,弟子={"僵尸","野鬼"}}
  ,女儿村={名称="英女侠",模型="英女侠",武器={名称="金龙双剪",等级=120},x=81,y=80,方向=1,弟子={"如意仙子","星灵仙子"}}
  ,神木林={名称="巫蛮儿",模型="巫蛮儿",武器={名称="紫金葫芦",等级=120},x=42,y=161,方向=0,弟子={"小龙女","白熊"}}
  ,龙宫={名称="龙太子",模型="龙太子",武器={名称="飞龙在天",等级=120},x=71,y=78,方向=1,弟子={"小龙女","龟丞相"}}
  ,盘丝洞={名称="狐美人",模型="狐美人",武器={名称="游龙惊鸿",等级=120},x=145,y=106,方向=0,弟子={"花妖","狐狸精"}}
  ,魔王寨={名称="巨魔王",模型="巨魔王",武器={名称="晓风残月",等级=120},x=32,y=43,方向=0,弟子={"牛妖"}}
  ,狮驼岭={名称="虎头怪",模型="虎头怪",武器={名称="元神禁锢",等级=120},x=90,y=5,方向=0,弟子={"蝴蝶仙子","雷鸟人"}}
  ,阴曹地府={名称="骨精灵",模型="骨精灵",武器={名称="九阴勾魂",等级=120},x=62,y=79,方向=0,弟子={"幽灵","吸血鬼"}}
  ,无底洞={名称="杀破狼",模型="杀破狼",武器={名称="冥火薄天",等级=120},x=58,y=86,方向=2,弟子={"老虎","黑熊"}}
  ,天宫={名称="舞天姬",模型="舞天姬",武器={名称="此最相思",等级=120},x=195,y=132,方向=0,弟子={"芙蓉仙子"}}
  ,普陀山={名称="玄彩娥",模型="玄彩娥",武器={名称="青藤玉树",等级=120},x=70,y=50,方向=0,弟子={"雾中仙"}}
  ,凌波城={名称="羽灵神",模型="羽灵神",武器={名称="庄周梦蝶",等级=120},x=44,y=79,方向=1,弟子={"凤凰","蛟龙"}}
  ,五庄观={名称="神天兵",模型="神天兵",武器={名称="九瓣莲花",等级=120},x=39,y=32,方向=0,弟子={"天将"}}

}

Q_门派编号={"大唐官府","化生寺","方寸山","女儿村","神木林","龙宫","五庄观","凌波城","普陀山","天宫","无底洞","阴曹地府","狮驼岭","魔王寨","盘丝洞"}
Q_十二生肖编号={"子鼠","丑牛","寅虎","卯兔","辰龙","巳蛇","午马","未羊","申猴","酉鸡","戌狗","亥猪"}

Q_青龙人物随机={
 [1]={地图=1501,名称="老孙头"}
,[2]={地图=1501,名称="牛大胆"}
,[3]={地图=1501,名称="管家"}
,[4]={地图=1501,名称="小花"}
,[5]={地图=1501,名称="马全有"}
,[6]={地图=1501,名称="王大嫂"}
,[7]={地图=1506,名称="楚恋依"}
,[8]={地图=1506,名称="海盗头子"}
,[9]={地图=1092,名称="金毛猿"}
,[10]={地图=1092,名称="蝴蝶妹妹"}
,[11]={地图=1142,名称="绿儿"}
,[12]={地图=1142,名称="翠儿"}
,[13]={地图=1142,名称="柳飞絮"}
,[14]={地图=1174,名称="龙女妹妹"}
,[15]={地图=1174,名称="雷鸟精"}
,[16]={地图=1174,名称="白熊怪"}
,[17]={地图=1174,名称="翻天怪"}
,[18]={地图=1070,名称="慧觉和尚"}
,[19]={地图=1070,名称="马婆婆"}
,[20]={地图=1070,名称="南极仙翁"}
,[21]={地图=1070,名称="许姑娘"}
,[22]={地图=1070,名称="凤凰姑娘"}
,[23]={地图=1070,名称="蝴蝶女"}
,[24]={地图=1070,名称="钟书生"}
,[25]={地图=1111,名称="守门天将"}
,[26]={地图=1111,名称="水兵统领"}
,[27]={地图=1111,名称="顺风耳"}
,[28]={地图=1111,名称="天牢守卫"}
,[29]={地图=1173,名称="山贼头子"}
,[30]={地图=1173,名称="牛将军"}
,[31]={地图=1173,名称="至尊宝"}
,[32]={地图=1173,名称="天兵飞剑"}
,[33]={地图=1173,名称="卷帘大将"}
,[34]={地图=1173,名称="姚太尉"}
,[35]={地图=1173,名称="云游僧"}
,[36]={地图=1173,名称="白鹿精"}
,[37]={地图=1173,名称="玉面狐狸"}
,[38]={地图=1173,名称="野猪王"}
,[39]={地图=1173,名称="天蓬元帅"}
,[40]={地图=1110,名称="山神"}
,[41]={地图=1110,名称="小芸芸"}
,[42]={地图=1110,名称="虾兵"}
,[43]={地图=1110,名称="吴文彩"}
,[44]={地图=1110,名称="吴老爹"}
,[45]={地图=1110,名称="者释和尚"}
,[46]={地图=1110,名称="业释和尚"}
,[47]={地图=1110,名称="海释和尚"}
,[48]={地图=1110,名称="小二"}
,[49]={地图=1110,名称="婆婆"}
,[50]={地图=1193,名称="江湖奸商"}
,[51]={地图=1193,名称="卵二姐"}
,[52]={地图=1193,名称="樵夫"}
}

Q_水陆大会邀请={
 [1]={名称="代笔师爷",x=483,y=125}
,[2]={名称="瓜农",x=478,y=119}
,[3]={名称="绣娘",x=459,y=162}
,[4]={名称="喜轿轿夫",x=483,y=147}
,[5]={名称="卖艺者",x=430,y=182}
,[6]={名称="乞丐乙",x=455,y=184}
,[7]={名称="阿咪",x=470,y=192}
,[8]={名称="马商",x=407,y=211}
,[9]={名称="小顽童",x=401,y=203}
,[10]={名称="卖花童",x=420,y=219}
,[11]={名称="丫鬟",x=424,y=222}
,[12]={名称="富家小姐",x=431,y=225}
,[13]={名称="卖鱼人",x=442,y=229}
,[14]={名称="面点师傅",x=443,y=223}
,[15]={名称="针线娘子",x=464,y=240}
,[16]={名称="樵夫",x=462,y=262}
,[17]={名称="大财主",x=427,y=228}
,[18]={名称="曾衙役",x=421,y=225}
,[19]={名称="布陈太医",x=370,y=243}
,[20]={名称="游方郎中",x=389,y=234}
}



Q_低级兽决表={
"高级反震","高级吸血","高级反击","高级连击","高级飞行","高级夜战","高级隐身","高级感知","高级再生","高级冥思","高级慧根","高级必杀","高级幸运","高级神迹","高级招架","高级永恒","高级敏捷","高级强力","高级防御","高级偷袭","高级毒","高级驱鬼","高级鬼魂术","高级魔之心","高级神佑复生","高级精神集中","高级否定信仰","奔雷咒","泰山压顶","水漫金山","地狱烈火","高级法术连击","高级法术暴击","高级法术波动","高级雷属性吸收","高级土属性吸收","高级火属性吸收","高级水属性吸收","反震","吸血","反击","连击","飞行","驱怪","隐身","感知","再生","冥思","慧根","必杀","幸运","神迹","招架","永恒","敏捷","强力","防御","偷袭","毒","驱鬼","鬼魂术","魔之心","神佑复生","精神集中","否定信仰","雷击","落岩","水攻","烈火","法术连击","法术暴击","法术波动","雷属性吸收","土属性吸收","火属性吸收","水属性吸收","迟钝"

}

Q_随机药品={
  "金香玉"
  ,"小还丹"
  ,"千年保心丹"
  ,"风水混元丹"
  ,"定神香"
  ,"蛇蝎美人"
  ,"九转回魂丹"
  ,"佛光舍利子"
  ,"十香返生丸"
  ,"金创药"
  ,"五龙丹"
  ,"孔雀红"
  ,"鹿茸"
  ,"仙狐涎"
  ,"地狱灵芝"
  ,"六道轮回"
  ,"凤凰尾"
  ,"火凤之睛"
  ,"龙之心屑"
  ,"紫石英"
  ,"白露为霜"
  ,"熊胆"
  ,"血色茶花"
  ,"丁香水"
  ,"麝香"
}

Q_随机三级药={
  "金香玉"
  ,"小还丹"
  ,"千年保心丹"
  ,"风水混元丹"
  ,"定神香"
  ,"蛇蝎美人"
  ,"九转回魂丹"
  ,"佛光舍利子"
  ,"十香返生丸"
  ,"金创药"
  ,"五龙丹"
}

Q_随机烹饪={
  "烤肉"
  ,"醉生梦死"
  ,"蛇胆酒"
  ,"百味酒"
  ,"梅花酒"
  ,"长寿面"
  ,"翡翠豆腐"
  ,"桂花丸"
  ,"珍露酒"
  ,"豆斋果"
  ,"臭豆腐"
  ,"佛跳墙"
  ,"烤鸭"
  ,"虎骨酒"
  ,"包子"
  ,"女儿红"
}

Q_随机材料={
  "天蚕丝"
  ,"玄龟板"
  ,"阴沉木"
  ,"麒麟血"
  ,"金凤羽"
  ,"补天石"
  ,"龙之筋"
}
