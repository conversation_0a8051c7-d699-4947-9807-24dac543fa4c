local 彩虹争霸 = class()

function 彩虹争霸:初始化()
	self.数据={}
	self.阵营分数={云影=500,虹光=500}
	self.云影={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
	self.虹光={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
	self.胜利方 = "云影阵营"
	self.红境数量 = 10
	self.黄境数量 = 20
	self.绿境数量 = 20
	-- if 调试模式 then
	-- 	self.红境数量 = 1
	-- 	self.黄境数量 = 1
	-- 	self.绿境数量 = 1
	-- end
	self.循环次数 = 2
	self.云影成员={}
	self.虹光成员={}
	self.云影玩家 = 0
	self.虹光玩家 = 0
	--
	self.活动开关 = false
	--self.活动时间= 自定义数据.活动任务时间.彩虹争霸
	--
	self:加载公共数据()
end

function 彩虹争霸:活动定时器()
	if self.活动开关 then
		if self.开始Time-os.time()>0 then --开活动的时间内
			if self.闯关开关 then --打怪时间
				if os.time()>= 600+self.刷怪Time then
					self:刷出场景怪()
					-- print("彩虹争霸刷出场景怪()")
					self.刷怪Time=os.time()
				end
				-- if self.泡泡Time and os.time() >= 60+self.泡泡Time then
				-- 	self:刷出泡泡()
				-- 	-- print("彩虹争霸刷出泡泡")
				-- 	self.泡泡Time=nil
				-- end
				if self.开始Time-os.time()<600 then --最后10分钟，关闭打怪 计算胜利
					self:计算胜利方()
					self.宝箱开关=true
					self.闯关开关=false --走下面
				end
			else
				if self.进场开关==false and self.进场Time-os.time()<0 then --开启进场
					self:开启彩虹进场()
					self.进场开关 = true
				elseif self.闯关开关==false and self.正式Time and self.正式Time-os.time()<0 then --正式开始 闯关开关为关闭状态
					self:正式开始比赛()
					self.正式Time=nil
					self.闯关开关 = true
				elseif self.宝箱开关 and self.开始Time-os.time()<300 then --最后5分钟 刷箱子
					if self.宝箱Time==nil then
						self.宝箱Time=os.time()
					end
					if os.time() >= 60+self.宝箱Time then --1分钟刷一次宝箱
						self:发放彩虹宝箱()
						-- print("彩虹争霸发放彩虹宝箱")
						self.宝箱Time=os.time()
					end
				end
			end
		else
			self:关闭活动()
		end
	else
		if 是否开启活动("彩虹争霸")  then
			self:开启报名()
		end
	end
end

function 彩虹争霸:开启报名()
	self.开始Time=os.time()+5100 --1小时40分钟，后面10分钟是刷箱子时间
	 self.进场Time=os.time()+600 --10后分钟
	 self.正式Time=os.time()+900 --15后分钟
	-- if 调试模式 then
	    -- self.进场Time=os.time()+10 --10后分钟
	 	--self.正式Time=os.time()+30 --15后分钟
	-- end
	self.活动开关=true
	self.进场开关 = false
	self.闯关开关 = false
	self.宝箱开关 = false


	self.数据={}
	self.阵营分数={云影=500,虹光=500}
	self.云影={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
	self.虹光={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
    self.红境数量=10
    self.黄境数量=20
    self.绿境数量=30
	self.云影成员 ={}
	self.虹光成员 ={}
	self.云影玩家 = 0
	self.虹光玩家 = 0
	发送公告("#G(彩虹争霸)#P已经开启，请提前20分钟到长安城的彩虹大使处报名参赛！")
	广播消息({内容=format("#G(彩虹争霸)#P已经开启，请提前20分钟到长安城的彩虹大使处报名参赛！"),频道="hd"})
end

function 彩虹争霸:开启彩虹进场()
	for k,v in pairs(self.数据) do
		if 玩家数据[k] and self.数据[k] and self.数据[k].阵营~="无" then
			发送数据(玩家数据[k].连接id,154,{云影=self.阵营分数.云影,虹光=self.阵营分数.虹光})
		end
	end
	发送公告("#G(彩虹争霸)#P5分钟后正式开始，请及时入场等待！")
	广播消息({内容=format("#G(彩虹争霸)#P5分钟后正式开始，请及时入场等待！"),频道="hd"})
end

function 彩虹争霸:正式开始比赛()
	self.刷怪Time=os.time()
	-- self.泡泡Time=os.time()
	self:刷出泡泡()
	self:刷出场景怪()
	广播消息({内容=format("#G(彩虹争霸)#W彩虹争霸正式开始！#81"),频道="hd"})
end

function 彩虹争霸:计算胜利方()
	if self.阵营分数.云影>=self.阵营分数.虹光 then
		for k,v in pairs(self.云影成员) do
			if self.数据[k]~=nil and self.数据[k].阵营 ~= nil and self.数据[k].阵营 == "云影" then
				self.数据[k].胜利=true
				if 玩家数据[k] then
					玩家数据[k].角色:添加称谓("彩虹霸主")
				end
			end
		end
		self.胜利方 = "云影阵营"
	else
		for k,v in pairs(self.虹光成员) do
			if self.数据[k]~=nil and self.数据[k].阵营 ~= nil and self.数据[k].阵营 == "虹光" then
				self.数据[k].胜利=true
				if 玩家数据[k] then
					玩家数据[k].角色:添加称谓("彩虹霸主")
				end
			end
		end
		self.胜利方 = "虹光阵营"
	end
	广播消息({内容=format("#G彩虹争霸活动结束，今日竞赛胜利方为#Y"..self.胜利方.."#G5分钟后将有彩虹宝箱投放于建邺城，请#Y"..self.胜利方.."#G做好准备！"),频道="hd"})
	发送公告("#G彩虹争霸活动结束，今日竞赛胜利方为#Y"..self.胜利方.."#G5分钟后将有彩虹宝箱投放于建邺城，请#Y"..self.胜利方.."#G做好准备！")
end

function 彩虹争霸:关闭活动()
	self.活动开关 = false
	self.进场开关 = false
	self.闯关开关 = false
	self.宝箱开关 = false
	--清空时间
	self.开始Time=nil
	self.进场Time=nil
	self.正式Time=nil
	self.泡泡Time=nil
	self.宝箱Time=nil
	self.刷怪Time=nil
	--
	-- self.数据={}
	-- self.阵营分数={云影=500,虹光=500}
	-- self.云影={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
	-- self.虹光={红境=0,橙境=0,黄境=0,绿境=0,蓝境=0,靛=0,紫=0}
	-- self.胜利方 = "云影阵营"
	-- self.红境数量 = 10
	-- self.黄境数量 = 20
	-- self.绿境数量 = 20
	-- self.云影成员={}
	-- self.虹光成员={}
	-- self.云影玩家 = 0
	-- self.虹光玩家 = 0

	for k,v in pairs(玩家数据) do
		if 玩家数据[k]~=nil  and 玩家数据[k].角色.数据~=nil then
			玩家数据[k].角色:取消任务(玩家数据[k].角色:取任务(6666))
			玩家数据[k].角色:删除称谓({"云影","虹光"})
		end
	end
	地图处理类:清除地图玩家(10000,1001,191,104)
	地图处理类:清除地图玩家(10001,1001,191,104)
	地图处理类:清除地图玩家(10002,1001,200,110)
	地图处理类:清除地图玩家(10003,1001,200,110)
	地图处理类:清除地图玩家(10004,1001,200,110)
	地图处理类:清除地图玩家(10005,1001,200,110)
	地图处理类:清除地图玩家(10006,1001,200,110)
	地图处理类:清除地图玩家(10007,1001,200,110)
	地图处理类:清除地图玩家(10008,1001,200,110)
	地图处理类:清除地图玩家(10009,1001,200,110)
	地图处理类:清除地图玩家(10012,1001,200,110)
	地图处理类:清除地图玩家(10013,1001,200,110)
	地图处理类:清除地图玩家(10014,1001,200,110)
	地图处理类:清除地图玩家(10015,1001,200,110)
	地图处理类:清除地图玩家(10016,1001,200,110)
	地图处理类:清除地图玩家(10017,1001,200,110)
	地图处理类:清除地图玩家(10018,1001,200,110)





	广播消息({内容=format("#G(彩虹争霸)#P活动已经结束！明天再接再厉！"),频道="hd"})
end

function 彩虹争霸:任务说明(玩家id,任务id)
	local 说明 = {}
	if not self.活动开关 then
		说明={"彩虹争霸帮助",format("活动已经结束。")}
		return 说明
	end
	if 任务数据[任务id].进程==1 then --准备
		说明={"彩虹争霸帮助",format("尽快抵达彩虹仙境，挑战七色泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==2 then --女儿  --女儿村
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(55，10)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==3 then --狮驼岭  --狮驼岭
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(109，75)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==4 then  --墨家村
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(40，10)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==5 then  --花果山
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(83，15)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==6 then -- 龙宫
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(187，21)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==7 then  --太岁府
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(9，15)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==8 then -- 月宫
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(73，15)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==9 then
		说明={"彩虹争霸帮助",format("本场景的#R传送大使#W在：(73，15)；快找他传送到下一场景吧。请尽快登顶到达彩虹仙境，挑战那里的七彩泡泡获得积分和奖励，活动结束时获得积分多的阵营获胜。")}
	elseif 任务数据[任务id].进程==10 then
		local sl = 0
		local sm = "无。"
		for k,v in pairs(任务数据[任务id].击杀) do
			if v~=false then
				sl=sl+1
				-- sm=k
			end
		end
		if sl~=0 then
			sm = ""
			for k,v in pairs(任务数据[任务id].击杀) do
				if sl==1 then
					if v~=false then
						sm=sm..k
						break
					end
				else
					if v~=false then
						sm=k.."、"..sm
					end
				end
			end
		end
		说明={"彩虹争霸帮助",format("#R彩虹仙子#W在:(14,22)。当前已经挑战过的泡泡颜色为：%s您的挑战级别为：%s级。",sm,任务数据[任务id].当前级别)}
	end
	return 说明
end

function 彩虹争霸:刷出场景怪()
	if self.活动开关 then
		self:刷出红镜怪物()
		self:刷出橙境怪物()
		self:刷出黄境怪物()
		self:刷出绿境怪物()
		self:刷出蓝境怪物()
		self:刷出靛境怪物()
		self:刷出紫境怪物()
	end
end

function 彩虹争霸:怪物的对话(id,序列,标识,地图)
	local 名称 = 任务数据[标识].名称
	local 对话数据={}
	if 名称=="彩虹宝箱" then
		self:捡宝箱(id,标识)
	else
		-- print(111)
		if self.数据[id]==nil or self.数据[id].阵营==nil or self.数据[id].阵营=="无" then
			常规提示(id,"至少要先领一个任务吧？")
			return
		elseif 玩家数据[id].队伍 == 0 then
			常规提示(id,"需要组队才能和我切磋哦")
			return
		end
		if self.活动开关==false then
			return
		end
		if 玩家数据[id].队长==false then
			常规提示(id,"这种重要的事情还是让队长来吧！")
			return
		end
		对话数据.模型=任务数据[标识].模型
		对话数据.名称=任务数据[标识].名称
		local 名称 = 任务数据[标识].名称
		local mc
		local dj
		if 任务数据[标识].名称=="红泡泡" then
		  mc="红"
		elseif 任务数据[标识].名称=="橙泡泡" then
		  mc="橙"
		elseif 任务数据[标识].名称=="黄泡泡" then
		  mc="黄"
		elseif 任务数据[标识].名称=="绿泡泡" then
		  mc="绿"
		elseif 任务数据[标识].名称=="蓝泡泡" then
		  mc="蓝"
		elseif 任务数据[标识].名称=="靛泡泡" then
		  mc="靛"
		elseif 任务数据[标识].名称=="紫泡泡" then
		  mc="紫"
		end
		local 任务id = 玩家数据[id].角色:取任务(6666)
		-- print(任务id)
		if 任务id==0 then return end
		for k,v in pairs(任务数据[任务id].击杀) do
			if k==mc and v==true then
				常规提示(id,"刚刚不是已经和我切磋过了吗？")
				return
			end
		end
		对话数据.对话="我是只可爱的#G"..mc.."#W泡泡，诸位的挑战级别是#G"..任务数据[任务id].当前级别.."#W级，你们确定要向我挑战嘛？"
		对话数据.选项={"我们已经磨刀霍霍","等我再准备下"}
	end
	return 对话数据
end

function 彩虹争霸:NPC对话内容(ID,编号,id)
	local wb = {}
	local xx = {}
	if ID == 10000 or ID == 10001  then
		if 编号 == 1 then
			wb[1] = "听说唐王准备了很多奖励品哦，大家要加油了"
			xx = {"请带我到活动场地","我还没准备好，要离开场地","点错了"}
			return {"男人_马副将","传送大使",wb[1],xx}
		end
	elseif ID == 10002 or ID == 10012  then  --女儿村
		if 编号 == 3 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 or 编号 == 2 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10003 or ID == 10013  then  --狮驼岭
		   if 编号 == 1 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 or 编号 == 2 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10004 or ID == 10014  then --墨家村
		if 编号 == 3 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 or 编号 == 2 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10005 or ID == 10015  then --花果山
		if 编号 == 3 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 or 编号 == 2 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10006 or ID == 10016  then --龙宫
		if 编号 == 3 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 or 编号 == 2 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10007 or ID == 10017  then --太岁府
		if 编号 == 2 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		elseif 编号 == 1 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		end
		return
	elseif ID == 10008 or ID == 10018  then --月宫
		if 编号 == 1 then
			wb[1] = "恭喜阁下在人海茫茫中找到了我，战胜我将去往新的境地。准备好接受我的考验了吗？"
			xx = {"我准备好了","再等等"}
			return {"男人_马副将","传送大使",wb[1],xx}
		end
		return
	elseif ID == 10009  then --彩虹仙境
		wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
		xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我要提升召唤兽忠诚","我只是看看"}
		return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
	end
	return
end

function 彩虹争霸:对话事件处理(id,名称,事件,地图ID)
	if 名称=="彩虹大使" then
		if 事件=="我想了解这个活动" then
			添加最后对话(id,"活动要求：需提前报名。否则，报名时间过了无法再参与活动。人物等级≥40级，报名、入场。分别为云影和虹光两个阵营闯关比拼，组队要求人数≥3人。具体活动时间，请看群通知。")
			return
		elseif 事件=="我来报名" then
			if 玩家数据[id].队伍~=0 and 玩家数据[id].队长==false then
				添加最后对话(id,"这种重要的事情还是让队长来吧！")
				return
			end
			if self.活动开关 then
				local 队伍id = 玩家数据[id].队伍
				if 队伍id ~= 0 then
					for n=1,#队伍数据[队伍id].成员数据 do
						local 成员id=队伍数据[队伍id].成员数据[n]
						if self.云影成员[成员id] or self.虹光成员[成员id] then
							添加最后对话(id,"队伍中：#R"..玩家数据[成员id].角色.数据.名称.."#W已经报过名了！")
							return
						end
					end
				else
					if self.云影成员[id] or self.虹光成员[id] then
						添加最后对话(id,"您已经报过名了！")
						return
					end
				end
				self:分配阵营(id)
				添加最后对话(id,"报名成功，请注意看进场时间！")
				常规提示(id,"报名成功，请注意看进场时间！")
			else
				添加最后对话(id,"当前不是活动时间")
			end
		elseif 事件=="我要参加活动，请送我入场" then
			if 玩家数据[id].队伍~=0 and 玩家数据[id].队长==false then
				添加最后对话(id,"这种重要的事情还是让队长来吧！")
				return
			elseif 玩家数据[id].角色.数据.飞行 then
				常规提示(id,"#Y/飞行状态下无法进入此地图！")
				return
			end
			if self.活动开关 and self.进场开关 then
				local 层数 = 1
				if self.数据[id] and self.数据[id].层数~=0 then
					层数=self.数据[id].层数
				end
				self:进入场地(id,层数) --进入后默认为1
			else
				添加最后对话(id,"活动还未开始，或还没到入场时间！")
			end
		elseif 事件=="我来取消任务" then
			玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(6666))
			常规提示(id,"取消成功")
		-- elseif 事件=="我来领取活动称谓" then
		end
	elseif 名称=="超级巫医" then
		local 商业对话栏 = require("Script/对话处理类/商业对话")()
		if 事件=="我的召唤兽受伤了，请帮我救治一下吧" then
			商业对话栏:治疗召唤兽气血(玩家数据[id].连接id,id)
			return true
		elseif 事件=="我的召唤兽忠诚度降低了，请帮我驯养一下吧" then
			商业对话栏:治疗召唤兽忠诚(玩家数据[id].连接id,id)
			return true
		elseif 事件=="我要同时补满召唤兽的气血、魔法和忠诚" then
			商业对话栏:治疗召唤兽全体(玩家数据[id].连接id,id)
			return true
		end
	elseif 事件=="我准备好了" then --各个场景的boss
		if 玩家数据[id].队伍==0 or 取队伍人数(id)<3 or 取队伍最低等级(玩家数据[id].队伍,40) then 添加最后对话(id,"彩虹争霸参与条件：≥40级，≥3人") return end --测试模式
		if 玩家数据[id].队长==false then
			添加最后对话(id,"这种重要的事情还是让队长来吧！")
			return
		end
		if self.活动开关 then
			if self.数据[id]==nil then
				添加最后对话(id,"首先得先报个名啊？")
				return
			end


			local ditu=玩家数据[id].角色.数据.地图数据.编号
			local 比较xy={x=qz(玩家数据[id].角色.数据.地图数据.x/20),y=qz(玩家数据[id].角色.数据.地图数据.y/20)}
			local 假人数据=地图处理类.NPC列表[ditu][玩家数据[id].最后对话.编号]
			if ditu~=地图ID or 取两点距离(比较xy,假人数据)>20 then
				 发送数据(id,7,"#Y/这个npc距离太远了")
				--print("彩虹争霸传送:脚本距离="..取两点距离(比较xy,假人数据))
				return
			end
			if 地图ID==10002 or 地图ID==10012 then --女儿村
				战斗准备类:创建战斗(id,130023,1)
			elseif 地图ID==10003 or 地图ID==10013 then --狮驼岭
				战斗准备类:创建战斗(id,130024,1)
			elseif 地图ID==10004 or 地图ID==10014 then --墨家村
				战斗准备类:创建战斗(id,130025,1)
			elseif 地图ID==10005 or 地图ID==10015 then --花果山
				战斗准备类:创建战斗(id,130026,1)
			elseif 地图ID==10006 or 地图ID==10016 then --龙宫
				战斗准备类:创建战斗(id,130027,1)
			elseif 地图ID==10007 or 地图ID==10017 then --太岁府
				战斗准备类:创建战斗(id,130028,1)
			elseif 地图ID==10008 or 地图ID==10018 then --月宫
				战斗准备类:创建战斗(id,130029,1)
			end
		end
	elseif 事件=="请带我到活动场地" then
		if 玩家数据[id].队伍==0 or 取队伍人数(id)<3 or 取队伍最低等级(玩家数据[id].队伍,40) then 添加最后对话(id,"彩虹争霸参与条件：≥40级，≥3人") return end --测试模式
		if 玩家数据[id].队长==false then
			添加最后对话(id,"这种重要的事情还是让队长来吧！")
			return
		end
		if self.活动开关 and self.闯关开关 then
			if self.数据[id]==nil then
				添加最后对话(id,"首先得先报个名啊？")
				return
			end
			if self.数据[id].层数==1 then --如果是1，那么就跳去女儿村
				self.数据[id].层数=2
			end
			self:跳转地图(id,self.数据[id].阵营,self.数据[id].层数)
			self:任务刷新(id)
		else
			添加最后对话(id,"游戏尚未开始，莫急。")
		end
	elseif 事件=="我还没准备好，要离开场地" then
		地图处理类:跳转地图(id,1208,127,37)
	elseif 事件=="我们已经磨刀霍霍" then
		if 玩家数据[id].队伍==0 or 取队伍人数(id)<3 or 取队伍最低等级(玩家数据[id].队伍,40) then 添加最后对话(id,"彩虹争霸参与条件：≥40级，≥3人") return end --测试模式
		if 玩家数据[id].队长==false then
			添加最后对话(id,"这种重要的事情还是让队长来吧！")
			return
		end
		if self.数据[id]==nil then
			添加最后对话(id,"首先得先报个名啊？")
			return
		end
		if self.活动开关 and 任务数据[玩家数据[id].地图单位.标识] then
			if 名称=="红泡泡" then
				战斗准备类:创建战斗(id,130016,玩家数据[id].地图单位.标识)
			elseif 名称=="橙泡泡" then
				战斗准备类:创建战斗(id,130017,玩家数据[id].地图单位.标识)
			elseif 名称=="黄泡泡" then
				战斗准备类:创建战斗(id,130018,玩家数据[id].地图单位.标识)
			elseif 名称=="绿泡泡" then
				战斗准备类:创建战斗(id,130019,玩家数据[id].地图单位.标识)
			elseif 名称=="蓝泡泡" then
				战斗准备类:创建战斗(id,130020,玩家数据[id].地图单位.标识)
			elseif 名称=="靛泡泡" then
				战斗准备类:创建战斗(id,130021,玩家数据[id].地图单位.标识)
			elseif 名称=="紫泡泡" then
				战斗准备类:创建战斗(id,130022,玩家数据[id].地图单位.标识)
			end
		else
			添加最后对话(id,"今日活动已经结束！")
		end
	end
end

function 彩虹争霸:取任务进程(地图)
	if 地图==10000 or 地图==10001 then
		return  1
	elseif 地图==10002 or 地图==10012 then
		return 2
	elseif 地图==10003 or 地图==10013 then
		return 3
	elseif 地图==10004 or 地图==10014 then
		return 4
	elseif 地图==10005 or 地图==10015 then
		return 5
	elseif 地图==10006 or 地图==10016 then
		return 6
	elseif 地图==10007 or 地图==10017 then
		return 7
	elseif 地图==10008 or 地图==10018 then
		return 9
	else
		return 10
	end
end

function 彩虹争霸:任务刷新(id)
	local 地图 = 玩家数据[id].角色.数据.地图数据.编号
	local 任务id= 玩家数据[id].角色:取任务(6666)
	if not 取队员任务一致(id,6666) then
		任务id=取唯一识别码(6666)
		任务数据[任务id]={
				id=任务id,
				起始=os.time(),
				玩家id=id,
				结束=3600,
				类型=6666,
				击杀={红=false,橙=false,黄=false,绿=false,蓝=false,靛=false,紫=false},
				当前级别 = 1
			}
		任务数据[任务id].进程 = self:取任务进程(地图)
        if 玩家数据[id].队伍 and 玩家数据[id].队伍~=0 then
        	if 玩家数据[id].队长 then
        		for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
					玩家数据[v].角色:取消任务(玩家数据[v].角色:取任务(6666))
				end
				玩家数据[id].角色:添加任务(任务id,1)
        	end
        else
             玩家数据[id].角色:添加任务(任务id)
        end
    else
    	任务数据[任务id].进程 = self:取任务进程(地图)
    	玩家数据[id].角色:刷新任务跟踪()
	end

end


function 彩虹争霸:分配阵营(id)
	local 队伍id = 玩家数据[id].队伍
	local zy = "虹光"
	if self.云影玩家 <= self.虹光玩家 then
		zy = "云影"
	end
	if 队伍id ~= 0 then
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			self.数据[成员id]={阵营=zy,层数=0}
			if zy == "云影" then
				self.云影成员[成员id]=1
				self.云影玩家 = self.云影玩家 + 1
			else
				self.虹光成员[成员id]=1
				self.虹光玩家 = self.虹光玩家 + 1
			end
		end
	else
		self.数据[id]={阵营=zy,层数=0}
		if zy == "云影" then
			self.云影成员[id]=1
			self.云影玩家 = self.云影玩家 + 1
		else
			self.虹光成员[id]=1
			self.虹光玩家 = self.虹光玩家 + 1
		end
	end
	return zy
end

function 彩虹争霸:跳转地图(id,阵营,类型)
	-- print("跳转地图========",id,阵营,类型)
	if 阵营=="云影" then
		if 类型 == 1 then
			地图处理类:跳转地图(id,10000,61,42)
		elseif 类型 == 2 then
			地图处理类:跳转地图(id,10002,103,117)
		elseif 类型 == 3 then
			地图处理类:跳转地图(id,10003,21,17)
		elseif 类型 == 4 then
			地图处理类:跳转地图(id,10004,46,112)
		elseif 类型 == 5 then
			地图处理类:跳转地图(id,10005,9,107)
		elseif 类型 == 6 then
			地图处理类:跳转地图(id,10006,20,103)
		elseif 类型 == 7 then
			地图处理类:跳转地图(id,10007,44,88)
		elseif 类型 == 8 then
			地图处理类:跳转地图(id,10008,116,84)
		elseif 类型 == 9 then
			地图处理类:跳转地图(id,10009,14,112)
		end
	else
		if 类型 == 1 then
			地图处理类:跳转地图(id,10001,61,42)
		elseif 类型 == 2 then
			地图处理类:跳转地图(id,10012,103,117)
		elseif 类型 == 3 then
			地图处理类:跳转地图(id,10013,21,17)
		elseif 类型 == 4 then
			地图处理类:跳转地图(id,10014,46,112)
		elseif 类型 == 5 then
			地图处理类:跳转地图(id,10015,9,107)
		elseif 类型 == 6 then
			地图处理类:跳转地图(id,10016,20,103)
		elseif 类型 == 7 then
			地图处理类:跳转地图(id,10017,44,88)
		elseif 类型 == 8 then
			地图处理类:跳转地图(id,10018,116,84)
		elseif 类型 == 9 then
			地图处理类:跳转地图(id,10009,14,112)
		end
	end
end

function 彩虹争霸:进入场地(id,类型)
	if not self.活动开关 then --测试模式
		常规提示(id,"当前不是活动时间无法入场")
		return
	elseif 取等级要求(id,40)==false then
		常规提示(id,"#Y/队伍中有成员等级不符合要求")
		return
	end

	local 队伍id = 玩家数据[id].队伍
	---入场检测 是否阵营一致
	if 队伍id ~= 0 then
		local 所属阵营1 = "无"
		local 可传送 = true
		if self.数据[id]~=nil and self.数据[id].阵营 ~= nil then
			所属阵营1 = self.数据[id].阵营
		end
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			local 所属阵营2 = "无"
			if self.数据[成员id]~=nil and self.数据[成员id].阵营 ~= nil then
				所属阵营2 = self.数据[成员id].阵营
			end
			if 所属阵营1~=所属阵营2 then
				可传送 = false
				break
			end
		end
		if 可传送 then --这里是大家都没报名的时候
			if 所属阵营1=="无" then
				所属阵营1 = self:分配阵营(id)
				常规提示(id,"自动报名成功！")
			end
			self:跳转地图(id,所属阵营1,类型)
			self:任务刷新(id)
			for n=1,#队伍数据[队伍id].成员数据 do
				local 成员id=队伍数据[队伍id].成员数据[n]
				if 类型>self.数据[成员id].层数 then
					self.数据[成员id].层数 = 类型
				end
				 玩家数据[成员id].角色:添加称谓(self.数据[成员id].阵营)
			end
		else
			常规提示(id,"队伍中有玩家所属阵营不一致。")
			return
		end
	else
		local 所属阵营1 = "无"
		if self.数据[id]~=nil and self.数据[id].阵营 ~= nil then
			所属阵营1 = self.数据[id].阵营
		end
		if 所属阵营1=="无" then --这里是没报名的时候也能进场
			所属阵营1 = self:分配阵营(id)
			常规提示(id,"自动报名成功！")
		end
		if 类型>self.数据[id].层数 then
			self.数据[id].层数 = 类型
		end
		self:跳转地图(id,所属阵营1,类型)
		self:任务刷新(id)
		玩家数据[id].角色:添加称谓(self.数据[id].阵营)
	end
end

function 彩虹争霸:刷出红镜怪物() --女
	local 地图范围={10002,10012}
	local 名称={"普通山猴","红境山猴"}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,15 do
				local mc=名称[取随机数(1,#名称)]
				local mx
				if mc=="普通山猴" then
					mx="巨力神猿"
				else
					mx="长眉灵猴"
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6667)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型=mx,
					x=self.女儿村坐标[n].x,
					y=self.女儿村坐标[n].y,
					事件="明雷",
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6667
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end


function 彩虹争霸:刷出橙境怪物() --狮驼岭
	local 地图范围={10003,10013}
	local 名称={"橙境护卫者","普通护卫者"}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,16 do
				local by=false
				local mc=名称[取随机数(1,#名称)]
				if mc=="橙境护卫者" then
					by=true
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6668)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型="鲛人",
					变异=by,
					x=self.狮驼岭坐标[n].x,
					y=self.狮驼岭坐标[n].y,
					事件="明雷",
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6668
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:刷出黄境怪物() --墨家村
	local 地图范围={10004,10014}
	local 名称={"黄境守卫者","普通守卫者"}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,15 do
				local mc=名称[取随机数(1,#名称)]
				local mx
				if mc=="黄境守卫者" then
					mx="巡游天神"
				else
					mx="古代瑞兽"
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6669)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型=mx,
					x=self.墨家村坐标[n].x,
					y=self.墨家村坐标[n].y,
					事件="明雷",
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6669
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end


function 彩虹争霸:刷出绿境怪物() --花果山
	local 地图范围={10005,10015}
	local 名称={"绿境战车","普通战车","绿境机器人","普通机器人"}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,12 do
				local mc=名称[取随机数(1,#名称)]
				local mx
				local by=false
				if mc=="绿境战车" then
					mx="连弩车"
					by=true
				elseif  mc=="普通战车" then
					mx="连弩车"
				elseif  mc=="绿境机器人" then
					mx="机关人"
					by=true
				else
					mx="机关鸟"
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6670)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型=mx,
					变异=by,
					x=self.花果山坐标[n].x,
					y=self.花果山坐标[n].y,
					事件="明雷",
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6670
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:刷出蓝境怪物() --龙宫
	local 地图范围={10006,10016}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,15 do
				local mc="普通小野猪"
				local by=false
				if 取随机数()<=25 then
					mc="蓝境小野猪"
					by=true
				end
				local 任务id=取唯一识别码(6671)
				随机序列=随机序列+1
				local xy=地图处理类.地图坐标[地图]:取随机点()
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型="野猪",
					x=xy.x,
					y=xy.y,
					事件="明雷",
					变异=by,
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6671
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:刷出靛境怪物() --太岁府
	local 地图范围={10007,10017}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,12 do
				local mc="普通守护伞"
				local mx="阴阳伞"
				local by=false
				if 取随机数()<=25 then
					mc="靛境守护伞"
					by=true
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6672)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型=mx,
					x=self.太岁府坐标[n].x,
					y=self.太岁府坐标[n].y,
					事件="明雷",
					变异=by,
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6672
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:刷出紫境怪物() --月宫
	local 地图范围={10008,10018}
	local 名称={"紫境守护兽","普通月宫兔"}
	for i=1,#地图范围 do
		local 地图=地图范围[i]
		for a=1,self.循环次数 do
			for n=1,13 do
				local mc=名称[取随机数(1,#名称)]
				local mx
				if mc=="紫境守护兽" then
					mx="炎魔神"
				else
					mx="兔子怪"
				end
				-- local xy=地图处理类.地图坐标[地图]:取随机点()
				local 任务id=取唯一识别码(6673)
				随机序列=随机序列+1
				任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=600,
					玩家id=0,
					名称=mc,
					模型=mx,
					变异=true,
					x=self.月宫坐标[n].x,
					y=self.月宫坐标[n].y,
					事件="明雷",
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6673
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:刷出泡泡()
	if self.活动开关 then
		local 地图=10009
		for n=1,3 do
			local 名称
			local 染色方案=nil
			local 染色组={}
			local ys
			for i=1,7 do
				if i==1 then
					染色方案=2078
					染色组={5,0,nil}
					名称="红泡泡"
					ys = "红"
				elseif i==2 then
					染色方案=92
					染色组={1,1,nil}
					名称="橙泡泡"
					ys = "橙"
				elseif i==3 then
					染色方案=nil
					染色组={}
					名称="黄泡泡"
					ys = "黄"
				elseif i==4 then
					染色方案=97
					染色组={1,0,nil}
					名称="绿泡泡"
					ys = "绿"
				elseif i==5 then
					染色方案=96
					染色组={1,0,nil}
					名称="蓝泡泡"
					ys = "蓝"
				elseif i==6 then
					染色方案=67
					染色组={1,0,nil}
					名称="靛泡泡"
					ys = "靛"
				elseif i==7 then
					染色方案=52
					染色组={1,0,nil}
					名称="紫泡泡"
					ys = "紫"
				end
				local 任务id=取唯一识别码(6674)
				随机序列=随机序列+1
				local xy=地图处理类.地图坐标[地图]:取随机点()
					任务数据[任务id]={
					id=任务id,
					起始=os.time(),
					结束=7200,
					玩家id=0,
					名称=名称,
					模型="泡泡",
					颜色= ys,
					染色组=染色组,
					染色方案=染色方案,
					行走开关=true,
					x=xy.x,
					y=xy.y,
					地图编号=地图,
					地图名称=取地图名称(地图),
					类型=6674
				}
				地图处理类:添加单位(任务id)
			end
		end
	end
end



function 彩虹争霸:战斗胜利(任务id,id组,战斗类型)
	-- print(战斗类型)
	if 战斗类型>=130023 and 战斗类型<=130029 then  --传送大使
		self.数据[id组[1]].层数=self.数据[id组[1]].层数+1
		for n=1,#id组 do
			local id=id组[n]
			玩家数据[id].战斗=0
			if self.数据[id组[n]] then
				self.数据[id组[n]].层数=self.数据[id组[1]].层数
			end
			  -- local 等级 = 玩家数据[id].角色.数据.等级
		   --    local 经验=等级*取随机数(800,900)+50000
		   --    local 银子=等级*250+17000
		   --    玩家数据[id].角色:添加经验(经验,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加储备(银子*2,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加银子(银子,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)

		   --    if 玩家数据[id].角色.数据.参战信息~=nil then
		   --       玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"福利")
		   --    end
         玩家数据[id].角色:自定义银子添加("彩虹传送使",1)
         玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)


		        local 获得物品={}
		        for i=1,#自定义数据.彩虹传送使 do
		          if 取随机数()<=自定义数据.彩虹传送使[i].概率 then
		             获得物品[#获得物品+1]=自定义数据.彩虹传送使[i]
		          end
		        end
		        获得物品=删除重复(获得物品)
		        if 获得物品~=nil then
		            local 取编号=取随机数(1,#获得物品)
		            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
		                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
		                广播消息({内容=format("#S/(彩虹争霸)#Y彩虹传送使对#G/%s#Y优异的表现表示赞赏，并送了他一个#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
		            end
		        end

			发送数据(玩家数据[id].连接id,1501,{名称="传送大使",模型="男人_马副将",对话=format("一关一关又一关，下一关一定更多惊喜等待各位！")})
		end
		self:跳转地图(id组[1],self.数据[id组[1]].阵营,self.数据[id组[1]].层数)
		self:任务刷新(id组[1])
	elseif 战斗类型>=130002 and 战斗类型<=130015 then  --各个场景怪
		if 任务数据[任务id]==nil then
			return
		end
		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
		任务数据[任务id]=nil
		for n=1,#id组 do
			local id=id组[n]
			-- 玩家数据[id].战斗=0
			if 战斗类型==130011 or  战斗类型==130013 then --蓝境小野猪
				玩家数据[id].战斗=0
			end

			if self.数据[id组[n]] then
				self.数据[id组[n]].层数=self.数据[id组[1]].层数
			end
			  -- local 等级 = 玩家数据[id].角色.数据.等级
		   --    local 经验=等级*取随机数(800,900)+30000
		   --    local 银子=等级*150+11000
		   --    玩家数据[id].角色:添加经验(经验,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加储备(银子*2,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加银子(银子,"彩虹争霸",1)
		   --    玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)

		   --    if 玩家数据[id].角色.数据.参战信息~=nil then
		   --       玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"福利")
		   --    end
         玩家数据[id].角色:自定义银子添加("彩虹小怪",1)
         玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)
		        local 获得物品={}
		        for i=1,#自定义数据.彩虹小怪 do
		          if 取随机数()<=自定义数据.彩虹小怪[i].概率 then
		             获得物品[#获得物品+1]=自定义数据.彩虹小怪[i]
		          end
		        end
		        获得物品=删除重复(获得物品)
		        if 获得物品~=nil then
		            local 取编号=取随机数(1,#获得物品)
		            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
		                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
		                广播消息({内容=format("#S/(彩虹争霸)#G/%s#Y舍己为人，战斗在最前线，为大家扫清通关的道路，奖励#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
		            end
		        end

		end
		if 战斗类型==130002 or 战斗类型==130003 then --女儿村
			self:红境机制(id组[1])
		elseif 战斗类型==130008 or 战斗类型==130009 then --花果山
			self:绿境机制(id组[1])
		elseif 战斗类型==130011 then --蓝境小野猪
			self:蓝境机制(id组[1])
		elseif 战斗类型==130013 then --靛境守护伞
			self:靛境机制(id组[1])
		end
	elseif 战斗类型>=130016 and 战斗类型<=130022 then  --顶层泡泡
		-- print(111111)
		-- print(任务id)
		if 任务数据[任务id]==nil then
			return
		end
		local 颜色=任务数据[任务id].颜色
		local dzrw=玩家数据[id组[1]].角色:取任务(6666)
		if 任务数据[dzrw] and 任务数据[dzrw].当前级别 then
			任务数据[dzrw].当前级别 = 任务数据[dzrw].当前级别 + 1
			任务数据[dzrw].击杀[颜色] = true
			if 任务数据[dzrw].当前级别>7 then
				任务数据[dzrw].击杀={红=false,橙=false,黄=false,绿=false,蓝=false,靛=false,紫=false}
				任务数据[dzrw].当前级别 = 1
			end
		end

		-- print("========")
		-- table.print(任务数据[dzrw])
		for n=1,#id组 do
			local id=id组[n]
			玩家数据[id].战斗=0
			-- self:杀泡泡任务刷新(id,颜色,任务数据[dzrw].当前级别)
			玩家数据[id].角色:刷新任务跟踪()
			if self.数据[id] then
				if self.数据[id].阵营=="云影" then
					self.阵营分数.云影=self.阵营分数.云影+1
					self.阵营分数.虹光=self.阵营分数.虹光-1
					if self.阵营分数.虹光<0 then
						self.阵营分数.虹光=0
					end
				else
					self.阵营分数.虹光=self.阵营分数.虹光+1
					self.阵营分数.云影=self.阵营分数.云影-1
					if self.阵营分数.云影<0 then
						self.阵营分数.云影=0
					end
				end
			end
			-- local 等级 = 玩家数据[id].角色.数据.等级
		 --      local 经验=等级*取随机数(800,900)+150000
		 --      local 银子=等级*700+42000
		 --      玩家数据[id].角色:添加经验(经验,"彩虹争霸",1)
		 --      玩家数据[id].角色:添加储备(银子*2,"彩虹争霸",1)
		 --      玩家数据[id].角色:添加银子(银子,"彩虹争霸",1)
		 --      玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)

		 --      if 玩家数据[id].角色.数据.参战信息~=nil then
		 --         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"福利")
		 --      end
         玩家数据[id].角色:自定义银子添加("彩虹泡泡",1)
         玩家数据[id].角色:添加活跃积分(1,"彩虹争霸",1)


		        local 获得物品={}
		        for i=1,#自定义数据.彩虹泡泡 do
		          if 取随机数()<=自定义数据.彩虹泡泡[i].概率 then
		             获得物品[#获得物品+1]=自定义数据.彩虹泡泡[i]
		          end
		        end
		        获得物品=删除重复(获得物品)
		        if 获得物品~=nil then
		            local 取编号=取随机数(1,#获得物品)
		            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
		                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
		                广播消息({内容=format("#S/(彩虹争霸)#G/%s#Y成功的战胜了顶层的可爱泡泡，获得一个#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
		            end
		        end


		end
	end
	for k,v in pairs(self.数据) do
		if 玩家数据[k] and 玩家数据[k].战斗==0 and self.数据[k] and self.数据[k].阵营~="无" and 玩家数据[k].角色.数据.地图数据.编号>=10000 and 玩家数据[k].角色.数据.地图数据.编号<=10018 then
			发送数据(玩家数据[k].连接id,154,{云影=self.阵营分数.云影,虹光=self.阵营分数.虹光})
		end
	end
end

function 彩虹争霸:杀泡泡任务刷新(id,颜色,级别)
	-- print(id,颜色,级别)
	local 任务id = 玩家数据[id].角色:取任务(6666)
	if 任务id==0 then return 0 end
	任务数据[任务id].当前级别=级别
	任务数据[任务id].击杀[颜色] = true
	if 任务数据[任务id].当前级别>7 then
		任务数据[任务id].击杀={红=false,橙=false,黄=false,绿=false,蓝=false,靛=false,紫=false}
		任务数据[任务id].当前级别 = 1
	end
	-- print(id,颜色)
	-- print("========")
	-- table.print(任务数据[任务id])
	玩家数据[id].角色:刷新任务跟踪()
end

function 彩虹争霸:红境机制(id)
	if self.数据[id]==nil or self.数据[id].阵营==nil or self.数据[id].阵营=="无" then
		return
	end
	local 地图范围={10002,10012}
	local 地图=地图范围[1]
	if self.数据[id].阵营=="云影" then
		地图=地图范围[2]
		self.云影.红境=self.云影.红境+1
		if self.云影.红境>=self.红境数量 then
			self.云影.红境 = 0
		else
			return
		end
	else
		self.虹光.红境=self.虹光.红境+1
		if self.虹光.红境>=self.红境数量 then
			self.虹光.红境 = 0
		else
			return
		end
	end
	for n=1,5 do
		local mc="普通山猴"
		local mx="巨力神猿"
		local xy=地图处理类.地图坐标[地图]:取随机点()
		local 任务id=取唯一识别码(6667)
		随机序列=随机序列+1
		任务数据[任务id]={
			id=任务id,
			起始=os.time(),
			结束=500,
			销毁=true,
			玩家id=0,
			名称=mc,
			模型=mx,
			x=xy.x,
			y=xy.y,
			事件="明雷",
			地图编号=地图,
			地图名称=取地图名称(地图),
			类型=6667
		}
		地图处理类:添加单位(任务id)
	end
end

function 彩虹争霸:绿境机制(id)
	if self.数据[id]==nil or self.数据[id].阵营==nil or self.数据[id].阵营=="无" then
		return
	end
	local 地图
	local mc = 玩家数据[id].角色.数据.名称
	if self.数据[id].阵营=="云影" then
		self.云影.绿境=self.云影.绿境+1
		if self.云影.绿境>=self.绿境数量 then
			self.云影.绿境 = 0
			for k,v in pairs(self.云影成员) do
				if 玩家数据[k]~=nil  and 玩家数据[k].角色.数据.地图数据.编号>=10000 and 玩家数据[k].角色.数据.地图数据.编号<=10018 then
					玩家数据[k].角色:添加经验(玩家数据[k].角色.数据.等级*200,"彩虹绿境机制")
				end
			end
			广播消息({内容=format("#G(彩虹争霸)#Y/玩家：#G"..mc.."#Y带领的队伍为“云影阵营”带来了绿境经验祝福",mc),频道="hd"})
		else
			return
		end
	else
		self.虹光.绿境=self.虹光.绿境+1
		if self.虹光.绿境>=self.绿境数量 then
			self.虹光.绿境 = 0
			for k,v in pairs(self.虹光成员) do
				if 玩家数据[k]~=nil and  玩家数据[k].角色.数据.地图数据.编号>=10000 and 玩家数据[k].角色.数据.地图数据.编号<=10018 then
					玩家数据[k].角色:添加经验(玩家数据[k].角色.数据.等级*200,"彩虹绿境机制")
				end
			end
			广播消息({内容=format("#G(彩虹争霸)#Y/玩家：#G"..mc.."#Y带领的队伍为“虹光阵营”带来了绿境经验祝福",mc),频道="hd"})
		else
			return
		end
	end
end

function 彩虹争霸:蓝境机制(id)
	if self.数据[id]==nil or self.数据[id].阵营==nil or self.数据[id].阵营=="无" then
		return
	end
	local 队伍id = 玩家数据[id].队伍
	self.数据[id].层数=self.数据[id].层数+1
	if 队伍id ~= 0 then
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			self.数据[成员id].层数=self.数据[id].层数
			常规提示(成员id,"#G小野猪把你传送到了下一层。")
		end
	end
	self:跳转地图(id,self.数据[id].阵营,self.数据[id].层数)
	self:任务刷新(id)
end

function 彩虹争霸:靛境机制(id)
	if self.数据[id]==nil or self.数据[id].阵营==nil or self.数据[id].阵营=="无" then
		return
	end
	local 队伍id = 玩家数据[id].队伍
	self.数据[id].层数=self.数据[id].层数-1
	if 队伍id ~= 0 then
		for n=1,#队伍数据[队伍id].成员数据 do
			local 成员id=队伍数据[队伍id].成员数据[n]
			self.数据[成员id].层数=self.数据[id].层数
			常规提示(成员id,"#R保护伞把你降到下一层。")
		end
	end
	self:跳转地图(id,self.数据[id].阵营,self.数据[id].层数)
	self:任务刷新(id)
end

function 彩虹争霸:发放彩虹宝箱()
  local 宝箱=取随机数(70,110)
  local 地图=1501
  for n=1,宝箱 do
	local 任务id=取唯一识别码(1159)
	随机序列=随机序列+1
	local xy=地图处理类.地图坐标[地图]:取随机点()
	任务数据[任务id]={
	  id=任务id,
	  起始=os.time(),
	  结束=55,
	  销毁=true,
	  玩家id=0,
	  名称="彩虹宝箱",
	  模型="宝箱",
	  x=xy.x,
	  y=xy.y,
	  删除=true,
	  地图编号=地图,
	  地图名称=取地图名称(地图),
	  类型=1159
	}
	地图处理类:添加单位(任务id)
  end


  广播消息({内容=format("#G彩虹宝箱已经刷新在建邺城啦，今日宝箱归属方为"..self.胜利方),频道="hd"})
  发送公告("#G彩虹宝箱已经刷新在建邺城啦，今日宝箱归属方为"..self.胜利方)
end

function 彩虹争霸:捡宝箱(id,任务id)
  if 任务数据[任务id]==nil then
	常规提示(id,"#Y这个宝箱已经被人抢走了！")
	return
  end
  if self.数据[id]~=nil and self.数据[id].阵营 ~= nil and self.数据[id].胜利 then
	地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
	任务数据[任务id]=nil
    local 获得物品={}
	for i=1,#自定义数据.彩虹宝箱 do
		if 取随机数()<=自定义数据.彩虹宝箱[i].概率 then
		    获得物品[#获得物品+1]=自定义数据.彩虹宝箱[i]
		end
	end
	获得物品=删除重复(获得物品)
	if 获得物品~=nil then
		local 取编号=取随机数(1,#获得物品)
		if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
		   玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
		   广播消息({内容=format("#S/(彩虹争霸)#G/%s#Y开启彩虹宝箱时，意外的得到了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
		end
	end

  else
	常规提示(id,"#Y这个宝箱属于"..self.胜利方.."再接再厉吧！")
  end
end

function 彩虹争霸:加载公共数据()
	-----加载地图部分
	---阵营地图  云影
	地图处理类:加载地图(10000,1197)
	地图处理类:加载地图(10001,1197)
	---------------------------------------------------
	地图处理类:加载地图(10002,1142)
	地图处理类:加载地图(10012,1142)
	--------
	地图处理类:加载地图(10003,1131)
	地图处理类:加载地图(10013,1131)
	--------
	地图处理类:加载地图(10004,1218)
	地图处理类:加载地图(10014,1218)
	--------
	地图处理类:加载地图(10005,1514)
	地图处理类:加载地图(10015,1514)
	--------
	地图处理类:加载地图(10006,1116)
	地图处理类:加载地图(10016,1116)
	--------
	地图处理类:加载地图(10007,1211)
	地图处理类:加载地图(10017,1211)
	--------
	地图处理类:加载地图(10008,1114)
	地图处理类:加载地图(10018,1114)
	--------
	地图处理类:加载地图(10009,1201)


	self.女儿村坐标={
	  [1]={["x"]=96,["y"]=111},
	  [2]={["x"]=94,["y"]=107},
	  [3]={["x"]=85,["y"]=103},
	  [4]={["x"]=78,["y"]=104},
	  [5]={["x"]=65,["y"]=93},
	  [6]={["x"]=46,["y"]=68},
	  [7]={["x"]=40,["y"]=61},
	  [8]={["x"]=29,["y"]=57},
	  [9]={["x"]=19,["y"]=42},
	  [10]={["x"]=33,["y"]=41},
	  [11]={["x"]=39,["y"]=31},
	  [12]={["x"]=47,["y"]=28},
	  [13]={["x"]=47,["y"]=22},
	  [14]={["x"]=24,["y"]=54},
	  [15]={["x"]=65,["y"]=88},
	}
	self.狮驼岭坐标={
	  [1]={["x"]=22,["y"]=22},
	  [2]={["x"]=26,["y"]=33},
	  [3]={["x"]=43,["y"]=23},
	  [4]={["x"]=57,["y"]=24},
	  [5]={["x"]=65,["y"]=14},
	  [6]={["x"]=79,["y"]=23},
	  [7]={["x"]=90,["y"]=30},
	  [8]={["x"]=110,["y"]=40},
	  [9]={["x"]=97,["y"]=56},
	  [10]={["x"]=88,["y"]=59},
	  [11]={["x"]=67,["y"]=61},
	  [12]={["x"]=60,["y"]=69},
	  [13]={["x"]=52,["y"]=79},
	  [14]={["x"]=49,["y"]=87},
	  [15]={["x"]=56,["y"]=91},
	  [16]={["x"]=87,["y"]=86},
	}
	self.墨家村坐标={
	  [1]={["x"]=36,["y"]=105},
	  [2]={["x"]=36,["y"]=97},
	  [3]={["x"]=36,["y"]=89},
	  [4]={["x"]=52,["y"]=84},
	  [5]={["x"]=63,["y"]=79},
	  [6]={["x"]=67,["y"]=71},
	  [7]={["x"]=59,["y"]=61},
	  [8]={["x"]=38,["y"]=60},
	  [9]={["x"]=27,["y"]=56},
	  [10]={["x"]=21,["y"]=52},
	  [11]={["x"]=19,["y"]=34},
	  [12]={["x"]=15,["y"]=25},
	  [13]={["x"]=67,["y"]=50},
	  [14]={["x"]=76,["y"]=35},
	  [15]={["x"]=55,["y"]=17},
	}
	self.花果山坐标={
	  [1]={["x"]=22,["y"]=101},
	  [2]={["x"]=22,["y"]=85},
	  [3]={["x"]=36,["y"]=84},
	  [4]={["x"]=54,["y"]=95},
	  [5]={["x"]=70,["y"]=95},
	  [6]={["x"]=92,["y"]=108},
	  [7]={["x"]=120,["y"]=106},
	  [8]={["x"]=133,["y"]=81},
	  [9]={["x"]=136,["y"]=67},
	  [10]={["x"]=108,["y"]=54},
	  [11]={["x"]=104,["y"]=21},
	  [12]={["x"]=94,["y"]=16},
	}
	self.太岁府坐标={
	  [1]={["x"]=35,["y"]=84},
	  [2]={["x"]=24,["y"]=83},
	  [3]={["x"]=21,["y"]=75},
	  [4]={["x"]=11,["y"]=67},
	  [5]={["x"]=22,["y"]=96},
	  [6]={["x"]=52,["y"]=66},
	  [7]={["x"]=43,["y"]=50},
	  [8]={["x"]=25,["y"]=53},
	  [9]={["x"]=13,["y"]=41},
	  [10]={["x"]=3,["y"]=34},
	  [11]={["x"]=2,["y"]=28},
	  [12]={["x"]=6,["y"]=19},
	}
	self.月宫坐标={
	  [1]={["x"]=102,["y"]=78},
	  [2]={["x"]=91,["y"]=78},
	  [3]={["x"]=83,["y"]=82},
	  [4]={["x"]=64,["y"]=85},
	  [5]={["x"]=43,["y"]=91},
	  [6]={["x"]=24,["y"]=85},
	  [7]={["x"]=14,["y"]=67},
	  [8]={["x"]=25,["y"]=57},
	  [9]={["x"]=33,["y"]=50},
	  [10]={["x"]=32,["y"]=30},
	  [11]={["x"]=44,["y"]=26},
	  [12]={["x"]=46,["y"]=23},
	  [13]={["x"]=71,["y"]=23},
	}
end

return 彩虹争霸