
function 战斗处理类:取基础封印计算(编号,目标,名称,等级,系数)
          --系数= {初始系数=1,叠加系数=0,初始伤害=0,防御系数=1,忽视防御=0, ---初始计算
          --       暴击系数=1,暴击增加=0,暴伤系数=1,暴伤增加=0,            ---暴击计算
          --       结果系数=1,结果伤害=0,特效={}}                         ---最终计算
------------------------------------------------------封印或抵抗封印命中等级计算
          if self.参战单位[编号].奇经八脉专神 then
              系数.初始伤害 = 系数.初始伤害 + 100
          end
          if self.参战单位[编号].奇经八脉.阴魅 then
             local 添加数额 = true
             for i=1,#self.参战单位 do
                 if self.参战单位[i].队伍~=  self.参战单位[编号].队伍 and self:取封印状态(i) then
                    添加数额 =false
                 end
             end
             if 添加数额 then
                系数.初始伤害 = 系数.初始伤害 + 玩家数据[self.参战单位[编号].玩家id].经脉:武器伤害处理(18)
             end
          end
          if self.参战单位[编号].奇经八脉.萦魄 and self:取增益数量(编号)>2 then
              系数.初始伤害 = 系数.初始伤害 + 玩家数据[self.参战单位[编号].玩家id].经脉:武器伤害处理(15)
          end
----------------------------------------------------削弱
          if self.参战单位[目标].神器技能 and self.参战单位[目标].神器技能.名称=="盏中晴雪" and self.参战单位[目标].速度>self.参战单位[编号].速度 then
              系数.初始伤害 = 系数.初始伤害 - self.参战单位[目标].神器技能.等级*(self.参战单位[目标].速度-self.参战单位[编号].速度)*0.5
          end
          if self.参战单位[目标].神器技能 and self.参战单位[目标].神器技能.名称=="金汤之固" and self.参战单位[目标].气血<=math.floor(self.参战单位[目标].最大气血*0.3)  then
              系数.初始伤害 = 系数.初始伤害 - self.参战单位[目标].神器技能.等级*240
          end
----------------------------------------------成功几率计算
-------------------------------------------------增强
          if self.参战单位[编号].自矜加成 then
              系数.结果系数 = 系数.结果系数 + 0.48
          end
          if self.参战单位[目标].奇经八脉燃魂 then
              系数.结果系数 = 系数.结果系数 + 0.2
          end

          if self.参战单位[编号].奇经八脉趁虚 then
              系数.结果系数 = 系数.结果系数 + 0.08
          end
          if self.参战单位[编号].奇经八脉.抑怒 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[目标].法术状态.画地为牢 then
              系数.结果系数 = 系数.结果系数 + 0.18
          end
          if self.参战单位[目标].奇经八脉.百爪狂杀 then ---结果系是
              系数.结果系数 = 系数.结果系数 + 0.2
          end
          if self.参战单位[编号].奇经八脉淬芒 and  self.参战单位[编号].奇经八脉淬芒.回合>0 then
              local 加成 =  self.参战单位[编号].奇经八脉淬芒.加成 * 8
              if 加成>16 then
                 加成 = 16
              end
              系数.结果系数 = 系数.结果系数+加成*0.01
          end
          if self.参战单位[编号].法术状态.四面埋伏 and  self.参战单位[编号].气血>= self.参战单位[编号].最大气血*0.4 then
              系数.结果系数 = 系数.结果系数 + 0.11
          end

          if self.参战单位[编号].法术状态.凝神术 then
              系数.结果伤害 = 系数.结果伤害 + 15
          end
          if 名称~="催眠符" and self.参战单位[编号].奇经八脉.制约 then
              系数.结果伤害 = 系数.结果伤害 + 5
           end
          if self.参战单位[编号].神话词条 and  self.参战单位[编号].神话词条.无相法门 then
              系数.结果伤害 = 系数.结果伤害 +   self.参战单位[编号].神话词条.无相法门*5
          end
          if self.参战单位[目标].奇经八脉.兽王 then
             for i=1,#self.参战单位 do
                 if self.参战单位[i]~=nil and self.参战单位[i].类型 ~= "角色" and self.参战单位[i].队伍 == self.参战单位[目标].队伍 then
                    系数.结果伤害 = 系数.结果伤害 + 2
                 end
             end
          end
------------------------------------------------------削弱
          if self.参战单位[目标].奇经八脉花护 then
              系数.结果系数 = 系数.结果系数 - 0.5
          end
          if self.参战单位[目标].奇经八脉空灵 then
              系数.结果系数 = 系数.结果系数 - 0.14
          end
          if self.参战单位[编号].奇经八脉.涂毒 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[编号].奇经特效.返璞 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[编号].奇经八脉.杏花 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[编号].奇经特效.奔雷  then
              系数.结果系数 = 系数.结果系数 - 0.15
          end
          if self.参战单位[编号].奇经特效.电芒 then
              系数.结果系数 = 系数.结果系数 - 0.15
          end
          if self.参战单位[目标].法术状态.清净 then
              系数.结果系数 = 系数.结果系数 - 0.3
          end
          if self.参战单位[编号].法术状态.相思寒针 then
              系数.结果系数 = 系数.结果系数 - 0.3
          end
          if self.参战单位[编号].法术状态.画地为牢 then
              系数.结果系数 = 系数.结果系数 - 0.24
          end
          if self.参战单位[目标].奇经八脉.风姿 and 取随机数()<=50 then
              系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[目标].奇经八脉.不羁 and self.参战单位[目标].法术状态.狂怒 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[目标].法术状态.宁心道符 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].法术状态.宁心道符.境界/100
          end
          if self.参战单位[目标].法术状态.护盾 and self.参战单位[目标].法术状态.护盾.明心 then
              系数.结果系数 = 系数.结果系数 - 0.05
          end
          if self.参战单位[编号].奇经八脉.暗伤 and  self.参战单位[编号].经脉流派=="花雨伊人" then
              系数.结果系数 = 系数.结果系数 - 0.1
          end

          if self.参战单位[目标].奇经八脉.三元 and self.参战单位[目标].人参娃娃~=nil and self.参战单位[目标].人参娃娃.回合>0 and self.参战单位[目标].人参娃娃.层数>=3 then
              系数.结果系数 = 系数.结果系数 - 0.15
          end

          if self.参战单位[目标].慧心 then
              系数.结果伤害 = 系数.结果伤害 - 6 * self.参战单位[目标].慧心
          end

          if self.参战单位[目标].符石技能.百无禁忌 then
              系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].符石技能.百无禁忌
          end
          if self.参战单位[目标].神话词条 and self.参战单位[目标].神话词条.飞天遁地 then
              系数.结果伤害 = 系数.结果伤害 -   self.参战单位[目标].神话词条.飞天遁地*5
          end
          if  self.参战单位[目标].类型=="角色" and self.参战单位[目标].奇经八脉.佛法 and self.参战单位[目标].法术状态.佛法无边 and self:取是否单独门派(目标) then
              系数.结果伤害 = 系数.结果伤害 - 10
          end



          --初始系数 ---玩家封印命中系数
          --初始伤害 ---增加玩家封印命中等级
          --忽视防御 ---减少目标抵抗封印等级
          --防御系数 ---目标抵抗封印系数
          --成功几率 = 30 + math.floor ((玩家命中等级 * 初始系数 + 初始伤害)/60 -(目标抗封等级 * 防御系数 - 忽视防御)/60)
          --结果系数 ---封印成功几率系数
          --结果伤害 ---封印成功几率
          ---成功几率 * 结果系数  +结果伤害
          local 封印数额 = 系数.初始伤害 + (self.参战单位[编号].封印命中等级 or 0) * 系数.初始系数
          local 抗封数额 = (self.参战单位[目标].抵抗封印等级 or 0) * 系数.防御系数 + 系数.忽视防御
          local 总体数额 = 0
          if 封印数额 > 0 then
              总体数额 = 总体数额 + math.floor(封印数额/60)
          end
          if 抗封数额 > 0 then
              总体数额 = 总体数额 - math.floor(抗封数额/60)
          end
          local 成功几率 = math.floor(30 + 总体数额 + (self.参战单位[编号].等级 - self.参战单位[目标].等级) * 0.1 + self.参战单位[编号].法术修炼*0.5 - self.参战单位[目标].抗法修炼*0.5)
          if 系数.结果系数<=0 then
             系数.结果系数 = 0.01
          end
          成功几率 = math.floor(成功几率*系数.结果系数 + 系数.结果伤害)
          if self.参战单位[编号].奇经八脉.必果 and  self.参战单位[编号].等级>=self.参战单位[目标].等级 and not  self.参战单位[编号].奇经八脉必果 then
              成功几率 = 70
              self.参战单位[编号].奇经八脉必果 = 1
          end
          return 成功几率
end


function 战斗处理类:封印技能计算(编号,名称,等级,额外命令,境界)
        local 前置 = {
            结尾 = 0,
            重复攻击 = false,
            目标数 = 1,
            目标 = 战斗技能[名称].目标(self, 编号, 额外命令 and 额外命令.目标 or self.参战单位[编号].指令.目标),
            流程 = #self.战斗流程
        }
        if 战斗技能[名称].人数 then
            前置.目标数 = 战斗技能[名称].人数(self, 编号,等级,境界)
        end
        前置.目标组 = self:取封印目标组(编号, 前置.目标, 前置.目标数, 名称)
        local 目标重置 = {}
        for k,v in pairs(前置.目标组) do
            if self.参战单位[v].法术状态.分身术 and not self.参战单位[v].法术状态.分身术.破解 and 取随机数()<=50 then
                self.参战单位[v].法术状态.分身术.破解=1
            else
                table.insert(目标重置,v)
            end
        end
        前置.目标组=目标重置
        前置.目标数=#目标重置
        if 前置.目标数==0 then return end
        if not 境界 and not 战斗技能[名称].消耗(self, 编号, 前置.目标数) then
            self:添加提示(self.参战单位[编号].玩家id, 编号, "#Y/未到达技能消耗要求,技能使用失败")
            return
        end

        if 战斗技能[名称].前置流程 then
              战斗技能[名称].前置流程(self,编号,等级,前置,"封印",境界)
        end
        if 前置.结束流程 then return end
        if 前置.名称 then
            名称 = 前置.名称
        end
        前置.流程 = #self.战斗流程 + 1
        self.执行等待 = self.执行等待 + 5
        self.战斗流程[前置.流程] = {
            流程 = 27,
            攻击方 = 编号,
            挨打方 = {}
        }
        if not 前置.技能提示 then
            self.战斗流程[前置.流程].提示 = {
                允许 = true,
                类型 = "封印",
                名称 = 名称
            }
            if 战斗技能[名称].特技 then
                self.战斗流程[前置.流程].提示.类型 = "特技"
            elseif 战斗技能[名称].法宝 then
                    self.战斗流程[前置.流程].提示.类型 = "法宝"
            elseif 战斗技能[名称].灵宝 then
                    self.战斗流程[前置.流程].提示.类型 = "灵宝"
            end
        end
        if 前置.全屏动画 then
            self.战斗流程[前置.流程].全屏动画=名称
        end
        if 前置.先手动画 then
            self.战斗流程[前置.流程].先手动画 = 名称
        end
        if 前置.取消状态 then
            self:解除状态组处理(编号,编号,前置.取消状态,名称,前置.流程)
        end
        if 前置.添加状态 then
            self:添加状态组处理(编号,编号,前置.添加状态,名称,等级,前置.流程)
        end

        if 前置.重复攻击 then
            local 临时目标=前置.目标组[1]
            前置.目标组={}
            for n=1,前置.目标数 do
              前置.目标组[n]=临时目标
            end
        end
        local function 执行封印(目标1)
            if not (目标1 and self:取目标状态(编号, 目标1, 1) and self:取行动状态(编号)) then
                return false
            end
            if self:取封印状态(目标1) then
                return false
            end
            if 境界 and self:取法宝封印状态(目标1) then
                return false
            end
            return true
        end
        local 战斗终止=false
        for n=1,前置.目标数 do
              if 战斗终止 or not 执行封印(前置.目标组[n]) then
                  终止战斗 = true
                  break
              end
              if 前置.重复攻击 and n ~= 1 then
                      前置.流程 = #self.战斗流程 + 1
                      self.执行等待 = self.执行等待 + 5
                      self.战斗流程[前置.流程] = {
                          流程 = 27,
                          攻击方 = 编号,
                          挨打方 = {},
                          提示 = {允许 = false}
                      }
                      if 前置.全屏动画 then
                          self.战斗流程[前置.流程].全屏动画=名称
                      end
                      if 前置.先手动画 then
                          self.战斗流程[前置.流程].先手动画 = 名称
                      end
                      self:封印循环处理(编号,前置.目标组[n],n,名称,前置.目标数,等级,前置.流程,1,境界)
              else
                  self:封印循环处理(编号,前置.目标组[n],n,名称,前置.目标数,等级,前置.流程,n,境界)
              end
        end
        if self.参战单位[编号].气血>0 and 前置.结尾 and 前置.结尾>0 then
            self.战斗流程[前置.流程].减少气血=前置.结尾
            self.战斗流程[前置.流程].死亡 = self:减少气血(编号,前置.结尾,编号,名称)
        end
        local 返回 = {}
        if 战斗技能[名称].结束流程 then
              战斗技能[名称].结束流程(self,编号,self.伤害输出,等级,前置,返回,"封印")
        end
        if 返回.取消状态 then
            self:解除状态组处理(编号,编号,返回.取消状态,名称,前置.流程)
        end
        if 返回.添加状态 then
            self:添加状态组处理(编号,编号,返回.添加状态,名称,等级,前置.流程)
        end

end





function 战斗处理类:封印循环处理(编号,目标,次数,名称,总数,等级,流程,挨打,境界)
          self.战斗流程[流程].挨打方[挨打]={特效={},挨打方=目标}
          self.战斗流程[流程].挨打方[挨打].特效[1] = 战斗技能[名称].特效 and 战斗技能[名称].特效("封印") or 名称
          local 基础 = DeepCopy(self.计算属性)
          local 数据 = {次数=次数,总数=总数,流程=流程,挨打=挨打}
          if 战斗技能[名称].基础计算 then
              战斗技能[名称].基础计算(self,编号,目标,等级,数据,基础,"封印",境界)
          end
          基础.特效 = {}
          local 计算 = self:取基础封印计算(编号,目标,名称,等级,基础)
          if 基础.特效 then
              if type(基础.特效) =="table" then
                    for k,v in ipairs(基础.特效) do
                      table.insert(self.战斗流程[流程].挨打方[挨打].特效,v)
                    end
              end
          end
          if self.参战单位[目标].奇经八脉.忍辱 then
              self:增加愤怒(目标,15)
          end
          if self.参战单位[目标].奇经八脉.轻霜 and 取随机数()<=30 and not  self.参战单位[编号].法术状态.暗器毒 then
              local 加成 =  玩家数据[self.参战单位[目标].玩家id].经脉:取师门技能等级("毒经") * 3
              self:添加状态("暗器毒",编号,目标,加成)
              self.参战单位[编号].法术状态.暗器毒.回合 = 5
              self:处理流程状态(self.战斗流程[流程],"暗器毒",编号)
          end
          if self.参战单位[编号].奇经八脉.迷梦 and not self.参战单位[目标].法术状态.瘴气 and 玩家数据[self.参战单位[编号].玩家id].经脉:取师门技能等级("迷情大法")>=120 and 取随机数()<=60 then
              self:添加状态("瘴气",目标,编号,等级)
              self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"瘴气",目标)
          end
          if self.参战单位[目标].奇经八脉.迷梦 and not self.参战单位[编号].法术状态.瘴气 and 玩家数据[self.参战单位[目标].玩家id].经脉:取师门技能等级("迷情大法")>=120 and 取随机数()<=60 then
              self:添加状态("瘴气",编号,目标,等级)
               self:处理流程状态(self.战斗流程[流程],"瘴气",编号)
          end




          if (self:取是否封印(编号,目标,名称,等级) and 取随机数(1,100)<= 计算 and not 基础.不可封印) or 基础.直接封印 then
              self:添加状态(名称,目标,编号,等级,境界)
              self:处理流程状态(self.战斗流程[流程].挨打方[挨打],名称,目标)
              local 返回 = {}
              if 战斗技能[名称].封印成功 then
                  战斗技能[名称].封印成功(self,编号,目标,等级,数据,返回)
              end
              if 返回.取消状态 then
                   self:解除状态组处理(编号,目标,返回.取消状态,名称,流程,挨打)
              end
              if 返回.添加状态 then
                    self:添加状态组处理(编号,目标,返回.添加状态,名称,等级,流程,挨打)
              end

              if self.参战单位[编号].奇经八脉.毒雾 and 取随机数()<=50 then
                  self:添加状态("暗器毒",目标,编号,self.参战单位[编号].等级*2)
                  self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"暗器毒",目标)
              end
              if 名称~="一笑倾城" and 名称~="飞花摘叶" and self.参战单位[编号].自矜加成 then
                  self.参战单位[编号].自矜加成 = nil
              end
              if 基础.增加魔法 then
                  self:增加魔法(目标,math.floor(基础.增加魔法))
              end
              if 基础.减少气血 then
                  self.战斗流程[流程].挨打方[挨打].伤害 = 基础.减少气血
                  self.战斗流程[流程].挨打方[挨打].类型 = 1
                  self.战斗流程[流程].挨打方[挨打].死亡 = self:减少气血(目标,基础.减少气血,编号,名称)
              end
          elseif not 基础.不可封印 then
                local 返回 = {}
                if 战斗技能[名称].封印失败 then
                    战斗技能[名称].封印失败(self,编号,目标,等级,数据,返回)
                end
                if 返回.取消状态 then
                     self:解除状态组处理(编号,目标,返回.取消状态,名称,流程,挨打)
                end
                if 返回.添加状态 then
                      self:添加状态组处理(编号,目标,返回.添加状态,名称,等级,流程,挨打)
                end

                if 名称~="一笑倾城" and 名称~="飞花摘叶" and self.参战单位[编号].奇经特效.自矜 then
                    if 取随机数()<= 40 or self.参战单位[编号].奇经八脉.独尊 then
                        self.参战单位[编号].自矜加成 = 1
                    end
                end
          end
          if 战斗技能[名称].循环结束 then
              战斗技能[名称].循环结束(self,编号,目标,self.伤害输出,等级,数据,"封印")
          end
          if 数据.取消状态 then
               self:解除状态组处理(编号,目标,数据.取消状态,名称,流程,挨打)
          end
          if 数据.添加状态 then
                self:添加状态组处理(编号,目标,数据.添加状态,名称,等级,流程,挨打)
          end

end






function 战斗处理类:取是否封印(编号,目标,名称,等级)
          if self.参战单位[目标].不可封印 then
              return false
          elseif self.参战单位[目标].精神 then
                  return false
          elseif self.参战单位[目标].信仰 then
                  return false
          elseif self.参战单位[目标].神迹==3 then
                  return false
          elseif self.参战单位[目标].法术状态.乾坤妙法 then
                  return false
          elseif 名称~="日月乾坤" and self.参战单位[目标].神迹==2 then
                  return false
          elseif self.参战单位[目标].鬼魂 and not self.参战单位[编号].奇经八脉.鬼念 then
                  return false
          end
          return true
end



