{
	"auto_complete":
	{
		"selected_items":
		[
			[
				"pr",
				"print"
			],
			[
				"prin",
				"print(2	(修复神器.lua)"
			],
			[
				"无",
				"无称谓"
			],
			[
				"table",
				"table.print	(gge)"
			],
			[
				"for",
				"forp	for k,v in pairs()"
			],
			[
				"ma",
				"math.random	([m [, n]])"
			],
			[
				"删除",
				"删除同类物品"
			],
			[
				"to",
				"tonumber	(e [, base])"
			],
			[
				"t",
				"type	(v)"
			],
			[
				"判断",
				"判断是否为空表	(共用.lua)"
			],
			[
				"tab",
				"table.remove	(table [, pos])"
			],
			[
				"r",
				"remove	(奇经八脉.lua)"
			],
			[
				"math",
				"math.floor	(x)"
			],
			[
				"tu",
				"tonumber	(e [, base])"
			],
			[
				"get",
				"getn"
			],
			[
				"fo",
				"forp	for k,v in pairs()"
			],
			[
				"c",
				"ceil	(设置.lua)"
			],
			[
				"st",
				"string.len	(s)"
			],
			[
				"e",
				"else	else end"
			],
			[
				"self",
				"self:打开"
			],
			[
				"fl",
				"floor"
			],
			[
				"lo",
				"local	local x = 1"
			],
			[
				"ba",
				"break	(帮派查看类.lua)"
			],
			[
				"多角色",
				"多角色操作数据"
			],
			[
				"s",
				"string"
			],
			[
				"els",
				"else	else end"
			],
			[
				"无介绍",
				"无介绍报错管理员	(技能库.lua)"
			],
			[
				"物品",
				"物品信息	(道具行囊.lua)"
			],
			[
				"刷新",
				"刷新道具行囊"
			],
			[
				"os",
				"os.time	([table])"
			],
			[
				"取",
				"取随机数"
			],
			[
				"p",
				"print"
			],
			[
				"else",
				"else	else end"
			]
		]
	},
	"buffers":
	[
		{
			"file": "main.lua",
			"settings":
			{
				"buffer_size": 51323,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/ggeserver.lua",
			"settings":
			{
				"buffer_size": 8315,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/服务网络/Socket.lua",
			"settings":
			{
				"buffer_size": 4830,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/服务网络/TcpServer.lua",
			"settings":
			{
				"buffer_size": 3104,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/服务网络/PackServer.lua",
			"settings":
			{
				"buffer_size": 3930,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/服务网络/MessagePack.lua",
			"settings":
			{
				"buffer_size": 29539,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/系统处理类/管理工具类.lua",
			"settings":
			{
				"buffer_size": 73051,
				"line_ending": "Windows"
			}
		},
		{
			"contents": "Searching 114 files for \"断开连接\"\n\nD:\\meng\\服务端源码\\Script\\服务网络\\MessagePack.lua:\n  840      if cursor.i <= cursor.j then\n  841          --error \"extra bytes\"\n  842:         -- __S服务:断开连接(id)\n  843:         print(\"断开连接\")\n  844      else\n  845          return data\n\nD:\\meng\\服务端源码\\Script\\服务网络\\Socket.lua:\n   22      self._hp:SendPackets(dwConnID,pBuffers,iCount)\n   23  end\n   24: -- 连接 ID-- 是否强制断开连接\n   25: function socket:断开连接(dwConnID,bForce)\n   26      return self._hp:Disconnect(dwConnID,(bForce==nil or bForce) and 1 or 0) ==1\n   27  end\n   28: -- 时长（毫秒）-- 是否强制断开连接\n   29  function socket:断开超时连接(dwPeriod,bForce)\n   30      return self._hp:DisconnectLongConnections(dwPeriod,(bForce==nil or bForce) and 1 or 0) ==1\n   31  end\n   32: -- 时长（毫秒）-- 是否强制断开连接\n   33  function socket:断开静默连接(dwPeriod,bForce)\n   34      return self._hp:DisconnectSilenceConnections(dwPeriod,(bForce==nil or bForce) and 1 or 0) ==1\n\nD:\\meng\\服务端源码\\Script\\系统处理类\\共用.lua:\n  173        发送数据(玩家数据[id].连接id,998,\"您的账号已经被封禁\")\n  174        广播消息(9, \"#xt/#g/ \" .. 玩家数据[id].角色.数据.名称 .. \"使用非法软件,已经封号,封禁原因为:\"..内容)\n  175:       -- __S服务:断开连接(玩家数据[id].连接id)\n  176  	  --玩家数据[id].连接id=id\n  177  	  系统处理类:断开游戏(id)\n\nD:\\meng\\服务端源码\\Script\\系统处理类\\管理工具类.lua:\n   34  if 数据[1]==nil or  tonumber(数据[1])==nil or tonumber(数据[1])<=0 then\n   35     self:发送数据(id,999,\"滚你麻痹\")\n   36:    __S服务:断开连接(id)\n   37     __C客户信息[id]=nil\n   38       return\n   ..\n   44        if 数据[3]==nil or 数据[3]==\"\" or  f函数.读配置(程序目录..[[data\\]]..数据[3]..[[\\账号信息.txt]],\"账号配置\",\"管理\")~=\"16888\" then\n   45              self:发送数据(id,999,\"你的账号尚未开通此工具的使用权限\")\n   46:             __S服务:断开连接(id)\n   47              __C客户信息[id]=nil\n   48              return\n   ..\n   50        -- if 服务端参数.授权账号 and 服务端参数.授权账号~=\"\" and 服务端参数.授权账号~=self.数据[3] then\n   51        --       self:发送数据(self.连接id,999,\"你的账号尚未开通此工具的使用权限\")\n   52:       --       __S服务:断开连接(self.连接id)\n   53        --       __C客户信息[self.连接id]=nil\n   54        --       return\n   ..\n   64      if f函数.文件是否存在([[data/]]..账号)==false then\n   65          self:发送数据(id,999,\"这样的账号并不存在\")\n   66:       __S服务:断开连接(id)\n   67         __C客户信息[id]=nil\n   68        return\n   69      elseif 密码~=f函数.读配置(程序目录..[[data\\]]..账号..[[\\账号信息.txt]],\"账号配置\",\"密码\") then\n   70            self:发送数据(id,999,\"密码错误\")\n   71:           __S服务:断开连接(id)\n   72            __C客户信息[id]=nil\n   73            return\n   74      elseif f函数.读配置(程序目录..[[data\\]]..账号..[[\\账号信息.txt]],\"账号配置\",\"管理\")~=\"16888\" then\n   75            self:发送数据(id,999,\"你的账号尚未开通此工具的使用权限\")\n   76:           __S服务:断开连接(id)\n   77            __C客户信息[id]=nil\n   78            return\n   79      -- elseif 服务端参数.授权账号 and 服务端参数.授权账号~=\"\" and 服务端参数.授权账号~=账号 then\n   80      --       self:发送数据(self.连接id,999,\"你的账号尚未开通此工具的使用权限\")\n   81:     --       __S服务:断开连接(self.连接id)\n   82      --       __C客户信息[self.连接id]=nil\n   83      --       return\n\nD:\\meng\\服务端源码\\Script\\系统处理类\\网络处理类.lua:\n   44  \n   45    if 内容==nil or 内容==\"\" then\n   46:      --self:断开连接(id,\"通讯密码错误\")\n   47      return\n   48    end\n   ..\n   54    self.数据=分割文本(内容,fgf)\n   55    if self.数据==\"\" or self.数据==nil then\n   56:     self:断开连接(id,\"通讯密码错误\")\n   57      return\n   58    end\n\nD:\\meng\\服务端源码\\Script\\角色处理类\\召唤兽处理类.lua:\n 2493           --  __S服务:输出(\"玩家\"..id..\" 非法修改数据警告!属性修改\")\n 2494           -- 封禁账号(id,\"CE修改\")\n 2495:          --  __S服务:断开连接(玩家数据[id].连接id)\n 2496           -- __S服务:连接退出(玩家数据[内容.数字id].连接id)\n 2497          return 0\n\nD:\\meng\\服务端源码\\Script\\角色处理类\\角色处理类.lua:\n  781    常规提示(id,\"#Y/恭喜你，密码修改成功，请下线重新登录\")\n  782        发送数据(玩家数据[id].连接id,998,\"修改密码成功，请重新登陆！\")\n  783:       --__S服务:断开连接(玩家数据[id].连接id)\n  784        玩家数据[id].连接id=id\n  785        系统处理类:断开游戏(id)\n  ...\n 1166      -- 写配置(\"./ip封禁.ini\",\"ip\",玩家数据[self.数据.数字id].ip..\" 非法修改数据警告!修改人物属性,玩家ID:\"..self.数据.数字id,1)\n 1167      -- 发送数据(玩家数据[self.数据.数字id].连接id,998,\"请注意你的角色异常！已经对你进行封IP\")\n 1168:     -- __S服务:断开连接(玩家数据[self.数据.数字id].连接id)\n 1169      return 0\n 1170    end\n ....\n 2589  \n 2590  --        发送数据(玩家数据[id].连接id,998,\"你已经与这个世界隔离了,88了您\")\n 2591: --        --__S服务:断开连接(玩家数据[id].连接id)\n 2592  -- 		玩家数据[id].连接id=id\n 2593  --              系统处理类:断开游戏(id)\n\nD:\\meng\\服务端源码\\Script\\角色处理类\\道具处理类.lua:\n 4906          写配置(\"./ip封禁.ini\",\"ip\",玩家数据[id].ip..\" 疑似重复刷跑商任务,玩家ID:\"..id,1)\n 4907          发送数据(玩家数据[id].连接id,998,\"请注意你的角色异常！已经对你进行封IP\")\n 4908:         --__S服务:断开连接(玩家数据[id].连接id)\n 4909          return\n 4910        elseif os.time() - 玩家数据[id].角色.数据.跑商时间 <= 30 then\n\nD:\\meng\\服务端源码\\Script\\ggeserver.lua:\n  134  					self._client[dwConnID].error = true\n  135  \n  136: 				   	self:断开连接(dwConnID)\n  137  				    break\n  138  				end\n  ...\n  218  	return self._hp:GetKeepAliveInterval()\n  219  end\n  220: function ggesvr:断开连接(dwConnID,bForce)\n  221: 	if self.CB_断开连接 then\n  222: 	    __gge.safecall(self.CB_断开连接,self,dwConnID)\n  223  	end\n  224  	return self._hp:Disconnect(dwConnID,(bForce==nil or bForce) and 1 or 0) ==1\n\n25 matches across 9 files\n",
			"settings":
			{
				"buffer_size": 4816,
				"line_ending": "Windows",
				"name": "Find Results",
				"scratch": true
			}
		},
		{
			"file": "Script/系统处理类/系统处理类.lua",
			"settings":
			{
				"buffer_size": 149368,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/常用变量.lua",
			"settings":
			{
				"buffer_size": 17588,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Script/系统处理类/网络处理类.lua",
			"settings":
			{
				"buffer_size": 10260,
				"line_ending": "Windows"
			}
		}
	],
	"build_system": "Packages/Lua/ggeserver.sublime-build",
	"build_system_choices":
	[
		[
			[
				[
					"Packages/Lua/ggeserver.sublime-build",
					""
				],
				[
					"Packages/Lua/ggeserver.sublime-build",
					"Run"
				],
				[
					"Packages/Lua/ggeserver.sublime-build",
					"RunInCommand"
				],
				[
					"Packages/Lua/ggeserver.sublime-build",
					"SetGGE"
				],
				[
					"Packages/Lua/ggeserver.sublime-build",
					"Stop"
				],
				[
					"Packages/Lua/ggeserver.sublime-build",
					"AboutGGE"
				]
			],
			[
				"Packages/Lua/ggeserver.sublime-build",
				""
			]
		]
	],
	"build_varint": "",
	"command_palette":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"console":
	{
		"height": 0.0,
		"history":
		[
		]
	},
	"distraction_free":
	{
		"menu_visible": true,
		"show_minimap": false,
		"show_open_files": false,
		"show_tabs": false,
		"side_bar_visible": false,
		"status_bar_visible": false
	},
	"expanded_folders":
	[
		"/C/Users/<USER>/Desktop/mh/服务端源码",
		"/C/Users/<USER>/Desktop/mh/服务端源码/Script",
		"/C/Users/<USER>/Desktop/mh/服务端源码/Script/服务网络",
		"/C/Users/<USER>/Desktop/mh/服务端源码/Script/系统处理类"
	],
	"file_history":
	[
		"/C/Users/<USER>/Desktop/mh/服务端源码/Script/系统处理类/GM管理服务器.lua",
		"/D/meng/服务端源码/Script/对话处理类/活动内容.lua",
		"/D/meng/服务端源码/Script/系统处理类/共用.lua",
		"/D/meng/服务端源码/Script/系统处理类/管理工具类.lua",
		"/D/meng/服务端源码/Script/对话处理类/对话处理.lua",
		"/D/meng/服务端源码/Script/对话处理类/对话内容.lua",
		"/D/meng/服务端源码/Script/战斗处理类/战斗计算/结束流程.lua",
		"/D/meng/服务端源码/Script/战斗处理类/战斗准备类.lua",
		"/D/meng/服务端源码/Script/对话处理类/对话预处理.lua",
		"/D/meng/服务端源码/Script/角色处理类/角色处理类.lua",
		"/D/meng/服务端源码/Script/常用变量.lua",
		"/D/meng/服务端源码/Script/系统处理类/聊天处理类.lua",
		"/D/meng/服务端源码/配置文件.ini",
		"/D/防江南老版/新江南/客户端111/script/数据中心/物品库.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗技能栏.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗类.lua",
		"/D/防江南老版/新江南/客户端111/Main.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/主控.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/主显.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/人物.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/开启前界面.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/自己_专用.LUA",
		"/D/防江南老版/新江南/客户端111/script/初系统/创建.lua",
		"/D/防江南老版/新江南/客户端111/script/系统类/丰富文本.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/变量1.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/玩家.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/地图类.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/技能库.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/染色.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/好友列表.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/分区.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/登陆.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/聊天框外部.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/标题.lua",
		"/D/防江南老版/新江南/客户端111/gge引擎.lua",
		"/D/防江南老版/新江南/客户端111/script/显示类/提示类.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗动画类.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/战斗模型库.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗单位类.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/染色.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/武器染色.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/召唤兽属性栏.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/召唤兽染色.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/召唤兽饰品染色.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/坐骑染色.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/动画类.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/SP.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/动画类 - 副本.lua",
		"/D/防江南老版/新江南/客户端111/script/网络/数据交换.lua",
		"/D/防江南老版/新江南/客户端111/script/新界面/仙缘商店.lua",
		"/D/防江南老版/新江南/客户端111/script/神器类/神器查看.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色仓库类.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色召唤兽属性栏.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色召唤兽资质栏.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色回收系统.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色奇经八脉.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色状态栏.lua",
		"/D/防江南老版/新江南/客户端111/script/多角色操作/多角色道具行囊.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/SP - 副本.lua",
		"/D/防江南老版/新江南/客户端111/script/txt2wpal.lua",
		"/D/防江南老版/新江南/客户端111/script/神器类/合成灵犀玉.lua",
		"/D/防江南老版/新江南/客户端111/script1111111/数据中心/物品库.lua",
		"/D/防江南老版/新江南/客户端111/script1111111/数据中心/庭院特效.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/庭院特效.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/商店.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/底图框.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/多开系统.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/底图框.lua",
		"/D/防江南老版/新江南/客户端111/script/系统类/人物框.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/队伍栏.lua",
		"/D/防江南老版/新江南/客户端111/script/新界面/共享仓库.lua",
		"/D/JNHT/lua/授权列表.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/会员福利.lua",
		"/D/防江南老版/新江南/客户端111/script/系统类/快捷技能栏.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/变量2.lua",
		"/D/防江南老版/新江南/客户端111/script/藏宝阁/藏宝阁上架货币.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/传送点坐标.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/事件.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/人物状态栏.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/人物称谓栏.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/仓库类.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/任务追踪栏.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/传送点.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/制作仙露.lua",
		"/D/防江南老版/新江南/服务端源码/Script/副本处理类/梦幻奇缘.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/-",
		"/D/防江南老版/新江南/服务端源码/Script/副本处理类/梦幻奇缘",
		"/D/防江南老版/新江南/服务端源码/Script/副本处理类/副本.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/音效库.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/普通模型库.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/头像库.lua",
		"/D/防江南老版/新江南/客户端111/script/积分商店/仙玉商城类.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/梦幻指引.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/加载类.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/装备开运.lua",
		"/D/防江南老版/新江南/客户端111/script/资源类/路径类.lua",
		"/D/防江南老版/新江南/客户端111/script/全局/假人.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/设置.lua",
		"/D/防江南老版/新江南/客户端111/script/场景类/第二场景.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$7/战斗单位类.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/剧情动画.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/好友查找类.lua",
		"/D/防江南老版/新江南/客户端111/script/系统类/队伍_格子.lua",
		"/D/防江南老版/新江南/客户端111/script/新界面/符石数据.lua",
		"/D/防江南老版/新江南/客户端111/script/新界面/战斗排行框.lua",
		"/D/防江南老版/新江南/客户端111/script/神器类/神器更换五行.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/小地图.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/多角色自动栏.lua",
		"/D/防江南老版/新江南/客户端111/script/功能界面/世界地图分类小地图.lua",
		"/D/防江南老版/新江南/客户端111/script/数据中心/场景.lua",
		"/D/防江南老版/新江南/客户端111/script/系统类/Greedy.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/多角色技能栏.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗位置.lua",
		"/D/防江南老版/新江南/客户端111/script/神器类/修复神器.lua",
		"/D/防江南老版/新江南/客户端111/script/新界面/CDK充值.lua",
		"/D/防江南老版/新江南/客户端111/script/战斗类/战斗命令类.lua",
		"/D/防江南老版/新江南/客户端111/script/初系统/对话栏.lua",
		"/D/防江南老版/新江南/客户端111/script/属性控制/队伍.lua",
		"/C/Users/<USER>/Documents/Tencent Files/1486276011/FileRecv/Main(1).lua",
		"/D/防江南老版/新江南/服务端源码/ggedebug.lua",
		"/D/防江南老版/江南修改新/客户端111/Main.lua",
		"/D/防江南老版/江南修改新/客户端111/script/数据中心/技能库.lua",
		"/D/防江南老版/江南修改新/客户端111/script/全局/主控.lua",
		"/D/防江南老版/江南修改新/客户端111/script/数据中心/头像库.lua",
		"/D/防江南老版/江南修改新/客户端111/script/初系统/注册.lua",
		"/D/防江南老版/江南修改新/客户端111/script/数据中心/普通模型库.lua",
		"/D/防江南老版/江南修改新/客户端111/script/全局/人物.lua",
		"/D/防江南老版/江南修改新/客户端111/script/全局/假人.lua"
	],
	"find":
	{
		"height": 30.0
	},
	"find_in_files":
	{
		"height": 129.0,
		"where_history":
		[
			"D:\\meng\\服务端源码\\Script",
			"D:\\meng\\服务端源码\\main.lua",
			"D:\\meng\\服务端源码\\Script",
			"D:\\meng\\服务端源码\\Script\\对话处理类",
			"D:\\meng\\服务端源码\\Script",
			"D:\\meng\\服务端源码\\Script\\系统处理类",
			"D:\\meng\\服务端源码\\Script",
			"D:\\meng\\服务端源码\\main.lua",
			"D:\\meng\\服务端源码\\Script",
			"D:\\防江南老版\\新江南\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\战斗处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\对话处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\对话处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\战斗处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\对话处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\服务端源码",
			"E:\\江南互通全套_20240614_024632\\服务端源码\\Script",
			"E:\\江南互通全套_20240614_024632\\客户端111",
			"E:\\江南互通全套_20240614_024632\\客户端111\\script",
			"E:\\江南互通全套_20240614_024632\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111\\script",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111\\script",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111\\script",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111\\script",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"D:\\防江南老版\\江南修改新\\客户端111\\script",
			"D:\\防江南老版\\江南修改新\\客户端111",
			"E:\\防江南修改4.3\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\系统处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\战斗处理类\\战斗处理类.lua",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"D:\\防江南老版\\江南修改新\\服务端源码\\Script\\战斗处理类",
			"D:\\防江南老版\\江南修改新\\服务端源码",
			"E:\\防江南修改\\服务端源码",
			"E:\\防江南修改\\服务端源码\\Script",
			"E:\\防江南修改\\服务端源码"
		]
	},
	"find_state":
	{
		"case_sensitive": false,
		"find_history":
		[
			"断开连接",
			"异常进入",
			"期望:",
			"localmp",
			"localmp.",
			"置工作线程数量",
			"工作线程数量",
			"取工作线程数量",
			"GetWorkerThreadCount",
			"取工作线程数量",
			"工作线程数量",
			"数据到达",
			"print",
			"账号验证",
			"数据处理",
			"管理工具类:数据处理",
			"json",
			"table.tostring",
			"网络处理类:jm",
			"网关认证",
			"数据解密处理",
			"fgf",
			"12345",
			"账号验证",
			"100046",
			"连续战斗",
			"渡劫",
			"飞升",
			"含冤小白龙",
			"进程 == 1 ",
			"进程 == 1",
			"6888",
			"8080",
			"os.exit",
			"xsjc11",
			"剧情",
			"主线",
			"剧情",
			"取任务",
			" function 取任务",
			"取任务",
			"996",
			"商人鬼魂",
			"挑战最少",
			"必须5人",
			"你的账号尚未",
			"管理权限",
			"@",
			"假人",
			"管理工具",
			"管理工具进入",
			"授权编号",
			"8080",
			"版本号",
			"指梦西游",
			"风靡江南",
			"当年情",
			"无与伦比",
			"怀旧西游",
			"扶摇万里",
			"兰亭序",
			"花样年华",
			"问界",
			"追梦西游",
			"测试专用",
			"三年二班",
			"测试专用",
			"was",
			"延迟",
			"gif",
			"png",
			"_ds",
			"wdf配置",
			"图像类",
			"gge图像类",
			"gge引擎",
			"开启前界面",
			"登录",
			"开启前",
			"底图资源",
			"底图",
			"self.底图 ",
			"小摊位",
			"self.影子",
			"self.任务图标",
			"self.画线",
			"登陆资源",
			"加载类",
			"登陆资源",
			"引擎.场景",
			"gge图像类",
			"wdf",
			"wdf配置",
			"金钟",
			"太级",
			"罗汉",
			"染色方案",
			"置全屏",
			"wdf配置",
			"logo",
			"wdf配置",
			"渲染开始",
			"图像:",
			"取图像",
			"置透明色",
			"渲染开始",
			"无框",
			"置无框",
			"垂直同步",
			"无框",
			"w无框",
			"全局游戏标题",
			"置",
			"全局游戏标题",
			"无框模式",
			"引擎",
			"引擎(全局游戏标题",
			"_base",
			"引擎(全局游戏标题",
			"引擎",
			"资源",
			"wdf配置",
			"无框模式",
			"引擎类",
			"开启前界面",
			"青花瓷",
			"江南风云",
			"青花瓷"
		],
		"highlight": true,
		"in_selection": false,
		"preserve_case": false,
		"regex": false,
		"replace_history":
		[
			"zts",
			"30002",
			"家具图标.wdf",
			""
		],
		"reverse": false,
		"show_context": true,
		"use_buffer2": true,
		"whole_word": false,
		"wrap": true
	},
	"groups":
	[
		{
			"selected": 0,
			"sheets":
			[
				{
					"buffer": 0,
					"file": "main.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 51323,
						"regions":
						{
						},
						"selection":
						[
							[
								17655,
								17655
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								9,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 580,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 346.0,
						"translation.y": 12617.0,
						"zoom_level": 1.0
					},
					"stack_index": 0,
					"type": "text"
				},
				{
					"buffer": 1,
					"file": "Script/ggeserver.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 8315,
						"regions":
						{
						},
						"selection":
						[
							[
								1914,
								1914
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								47,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 66,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"translate_tabs_to_spaces": false
						},
						"translation.x": 0.0,
						"translation.y": 1116.0,
						"zoom_level": 1.0
					},
					"stack_index": 7,
					"type": "text"
				},
				{
					"buffer": 2,
					"file": "Script/服务网络/Socket.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 4830,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								4830
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 2530.0,
						"zoom_level": 1.0
					},
					"stack_index": 5,
					"type": "text"
				},
				{
					"buffer": 3,
					"file": "Script/服务网络/TcpServer.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 3104,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								3104
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 4,
					"type": "text"
				},
				{
					"buffer": 4,
					"file": "Script/服务网络/PackServer.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 3930,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								0
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 45,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 3,
					"type": "text"
				},
				{
					"buffer": 5,
					"file": "Script/服务网络/MessagePack.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 29539,
						"regions":
						{
						},
						"selection":
						[
							[
								29107,
								29107
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 881,
							"origin_encoding": "ASCII",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 18155.0,
						"zoom_level": 1.0
					},
					"stack_index": 6,
					"type": "text"
				},
				{
					"buffer": 6,
					"file": "Script/系统处理类/管理工具类.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 73051,
						"regions":
						{
						},
						"selection":
						[
							[
								2592,
								2592
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 103,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 1660.0,
						"zoom_level": 1.0
					},
					"stack_index": 2,
					"type": "text"
				},
				{
					"buffer": 7,
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 4816,
						"regions":
						{
							"match":
							{
								"flags": 112,
								"regions":
								[
									[
										176,
										180
									],
									[
										207,
										211
									],
									[
										387,
										391
									],
									[
										415,
										419
									],
									[
										558,
										562
									],
									[
										746,
										750
									],
									[
										1092,
										1096
									],
									[
										1329,
										1333
									],
									[
										1587,
										1591
									],
									[
										1838,
										1842
									],
									[
										2050,
										2054
									],
									[
										2257,
										2261
									],
									[
										2488,
										2492
									],
									[
										2719,
										2723
									],
									[
										2906,
										2910
									],
									[
										3052,
										3056
									],
									[
										3259,
										3263
									],
									[
										3508,
										3512
									],
									[
										3783,
										3787
									],
									[
										3945,
										3949
									],
									[
										4231,
										4235
									],
									[
										4449,
										4453
									],
									[
										4587,
										4591
									],
									[
										4628,
										4632
									],
									[
										4673,
										4677
									]
								],
								"scope": ""
							}
						},
						"selection":
						[
							[
								106,
								106
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content"
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"detect_indentation": false,
							"function_name_status_row": 3,
							"line_numbers": false,
							"output_tag": 1,
							"result_base_dir": "",
							"result_file_regex": "^([^ 	].*):$",
							"result_line_regex": "^ +([0-9]+):",
							"scroll_past_end": true,
							"syntax": "Packages/Default/Find Results.hidden-tmLanguage"
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 1,
					"type": "text"
				},
				{
					"buffer": 8,
					"file": "Script/系统处理类/系统处理类.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 149368,
						"regions":
						{
						},
						"selection":
						[
							[
								140169,
								140173
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 3945,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 82630.0,
						"zoom_level": 1.0
					},
					"stack_index": 9,
					"type": "text"
				},
				{
					"buffer": 9,
					"file": "Script/常用变量.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 17588,
						"regions":
						{
						},
						"selection":
						[
							[
								175,
								175
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 14,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 10,
					"type": "text"
				},
				{
					"buffer": 10,
					"file": "Script/系统处理类/网络处理类.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 10260,
						"regions":
						{
						},
						"selection":
						[
							[
								5303,
								5303
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								18,
								21,
								32,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 194,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 3339.0,
						"zoom_level": 1.0
					},
					"stack_index": 8,
					"type": "text"
				}
			]
		}
	],
	"incremental_find":
	{
		"height": 30.0
	},
	"input":
	{
		"height": 34.0
	},
	"layout":
	{
		"cells":
		[
			[
				0,
				0,
				1,
				1
			]
		],
		"cols":
		[
			0.0,
			1.0
		],
		"rows":
		[
			0.0,
			1.0
		]
	},
	"menu_visible": true,
	"output.exec":
	{
		"height": 190.0
	},
	"output.find_results":
	{
		"height": 0.0
	},
	"pinned_build_system": "Packages/Lua/ggeserver.sublime-build",
	"project": "游戏模板.sublime-project",
	"replace":
	{
		"height": 56.0
	},
	"save_all_on_build": true,
	"select_file":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"select_project":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"select_symbol":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"selected_group": 0,
	"settings":
	{
	},
	"show_minimap": true,
	"show_open_files": false,
	"show_tabs": true,
	"side_bar_visible": true,
	"side_bar_width": 252.0,
	"status_bar_visible": true,
	"template_settings":
	{
	}
}
