function 取商城宝宝(bb)
local bbs = {}
if   bb == "超级泡泡(法术型)"  or  bb == "超级灵鹿(法术型)"  or  bb == "超级腾蛇(法术型)"  or  bb == "超级白泽(法术型)"
      or bb == "超级金猴(法术型)"  or  bb == "超级玉兔(法术型)"    or  bb == "超级大熊猫(法术型)"
      or bb == "超级人参娃娃(法术型)"
    then --一代神兽
		    bbs[1] = 0
			bbs[2] = 1600 --攻资
			bbs[3] = 1600 --防资
			bbs[4] = 4000 --体资
			bbs[5] = 2500 --法资
			bbs[6] = 1550 --速资
			bbs[7] = 1500 --躲闪
			bbs[8] = 1.3
			bbs[9] = {"奔雷咒","高级法术连击","高级魔之心","高级法术波动","高级驱鬼","高级永恒"}
		    bbs[10] = {100,20,20,20,20,20}
		    bbs[11] = {"须弥真言"}

     elseif bb == "超级孔雀(法术型)"    or   bb == "超级青鸾(法术型)"      or   bb == "恶魔泡泡(法术型)"     or  bb == "超级灵狐(法术型)"
         or bb == "超级赤焰兽(法术型)"    or  bb == "超级筋斗云(法术型)"   or   bb == "超级土地公公(法术型)"  or  bb == "超级六耳猕猴(法术型)"
     then--二代神兽
			bbs[1] = 0
			bbs[2] = 1800
			bbs[3] = 1800
			bbs[4] = 4500
			bbs[5] = 3000
			bbs[6] = 1650
			bbs[7] = 1600
			bbs[8] = 1.5
			bbs[9] = {"奔雷咒","高级法术连击","高级魔之心","高级法术波动","高级驱鬼","高级永恒","高级感知"}
		    bbs[10] = {100,20,20,20,20,20}
		    bbs[11] = {"须弥真言","净台妙谛"}

     elseif bb == "超级神猪(法术型)"  or  bb == "超级神狗(法术型)"  or bb == "超级神狗(法术型)"  or bb == "超级神鸡(法术型)"
         or bb == "超级神猴(法术型)"  or  bb == "超级神羊(法术型)"  or bb == "超级神马(法术型)"  or bb == "超级神蛇(法术型)"
         or bb == "超级神龙(法术型)"  or  bb == "超级神兔(法术型)"  or bb == "超级神虎(法术型)"  or bb == "超级神牛(法术型)"
         or bb == "超级神鼠(法术型)"  or  bb == "超级海豚(法术型)"  or bb == "超级大鹏(法术型)"  or bb == "超级大象(法术型)"
         or bb == "超级麒麟(法术型)"  or  bb == "超级翼龙(法术型)"
     then --三代神兽
			bbs[1] = 0
			bbs[2] = 2050
			bbs[3] = 2050
			bbs[4] = 5125
			bbs[5] = 3625
			bbs[6] = 1750
			bbs[7] = 1700
			bbs[8] = 1.75
			bbs[9] = {"奔雷咒","高级法术连击","高级魔之心","高级法术波动","高级驱鬼","高级永恒","高级感知","高级神佑复生","高级精神集中"}
		    bbs[10] = {100,20,20,20,20,20}
		    bbs[11] = {"须弥真言","净台妙谛","灵能激发"}


      elseif bb == "独角兽(法术型)"   or  bb == "月影(法术型)" or  bb == "飞天(法术型)"  or  bb == "雪仙(法术型)"   or  bb == "花仙(法术型)"
          or bb == "小精灵(法术型)" or  bb == "裁决者(法术型)"  or  bb == "葬送者(法术型)"  or  bb == "画江湖(法术型)"
          or bb == "超级尾巴(法术型)" or  bb == "超级阿狸(法术型)"   or  bb == "超级贪狼(法术型)"  or  bb == "超级武罗(法术型)"
          or bb == "超级小白龙(法术型)" or  bb == "超级猪小戒(法术型)"  or   bb == "超级小猴子(法术型)" or bb == "超级小萝莉(法术型)"
          or bb == "超级帝释天(法术型)"
      then --四代神兽
			bbs[1] = 0
			bbs[2] = 2150
			bbs[3] = 2150
			bbs[4] = 5500
			bbs[5] = 4000
			bbs[6] = 1800
			bbs[7] = 1750
			bbs[8] = 1.85
			bbs[9] = {"奔雷咒","高级法术连击","高级魔之心","高级法术波动","高级驱鬼","高级永恒","高级感知","高级再生","高级冥思","高级幸运","高级神佑复生","高级精神集中"}
		    bbs[10] = {100,20,20,20,20,20}
		    bbs[11] = {"须弥真言","净台妙谛","灵能激发","浮云神马"}

    elseif bb == "超级泡泡(物理型)"  or  bb == "超级灵鹿(物理型)"  or  bb == "超级腾蛇(物理型)"  or  bb == "超级白泽(物理型)"
        or bb == "超级金猴(物理型)"  or  bb == "超级玉兔(物理型)"  or  bb == "超级天马(物理型)"  or  bb == "超级大熊猫(物理型)"
        or bb == "超级人参娃娃(物理型)"
    then --一代神兽
        bbs[1] = 0
		bbs[2] = 1600 --攻资
		bbs[3] = 1600 --防资
		bbs[4] = 4000 --体资
		bbs[5] = 2500 --法资
		bbs[6] = 1550 --速资
		bbs[7] = 1500 --躲闪
		bbs[8] = 1.3
		bbs[9] = {"高级连击","高级必杀","高级吸血","高级强力","高级驱鬼","高级偷袭"}
        bbs[10] = {100,20,20,20,20,20}
        bbs[11] = {"嗜血追击"}


     elseif bb == "超级孔雀(物理型)"      or  bb == "超级仙鹿(物理型)"     or   bb == "超级青鸾(物理型)"     or   bb =="恶魔泡泡(物理型)"   or  bb == "超级灵狐(物理型)"
         or bb == "超级赤焰兽(物理型)"    or  bb == "超级筋斗云(物理型)"   or   bb == "超级土地公公(物理型)"  or  bb == "超级六耳猕猴(物理型)"
     then--二代神兽
		bbs[1] = 0
		bbs[2] = 1800
		bbs[3] = 1800
		bbs[4] = 4500
		bbs[5] = 3000
		bbs[6] = 1650
		bbs[7] = 1600
		bbs[8] = 1.5
        bbs[9] = {"高级连击","高级必杀","高级吸血","高级强力","高级驱鬼","高级偷袭","高级招架"}
        bbs[10] = {100,20,20,20,20,20}
        bbs[11] = {"嗜血追击","理直气壮"}

     elseif bb == "超级神猪(物理型)"  or  bb == "超级神狗(物理型)"  or bb == "超级神狗(物理型)"  or bb == "超级神鸡(物理型)"
         or bb == "超级神猴(物理型)"  or  bb == "超级神羊(物理型)"  or bb == "超级神马(物理型)"  or bb == "超级神蛇(物理型)"
         or bb == "超级神龙(物理型)"  or  bb == "超级神兔(物理型)"  or bb == "超级神虎(物理型)"  or bb == "超级神牛(物理型)"
         or bb == "超级神鼠(物理型)"  or  bb == "超级海豚(物理型)"  or bb == "超级大鹏(物理型)"  or bb == "超级大象(物理型)"
         or bb == "超级麒麟(物理型)"  or  bb == "超级翼龙(物理型)"
     then --三代神兽
		bbs[1] = 0
		bbs[2] = 2050
		bbs[3] = 2050
		bbs[4] = 5125
		bbs[5] = 3625
		bbs[6] = 1750
		bbs[7] = 1700
		bbs[8] = 1.75
        bbs[9] = {"高级连击","高级必杀","高级吸血","高级强力","高级驱鬼","高级偷袭","高级招架","高级永恒","高级感知"}
        bbs[10] = {100,20,20,20,20,20}
        bbs[11] = {"嗜血追击","理直气壮","大快朵颐"}

      elseif bb == "独角兽(物理型)"   or  bb == "月影(物理型)"  or  bb == "飞天(物理型)"  or  bb == "雪仙(物理型)"   or  bb == "花仙(物理型)"
          or bb == "小精灵(物理型)" or  bb == "裁决者(物理型)"  or  bb == "葬送者(物理型)"  or  bb == "画江湖(物理型)"
          or bb == "超级尾巴(物理型)" or  bb == "超级阿狸(物理型)"   or  bb == "超级贪狼(物理型)"  or  bb == "超级武罗(物理型)"
          or bb == "超级小白龙(物理型)" or  bb == "超级猪小戒(物理型)"  or   bb == "超级小猴子(物理型)" or bb == "超级小萝莉(物理型)"
          or bb == "超级帝释天(物理型)"
      then --四代神兽
		bbs[1] = 0
		bbs[2] = 2150
		bbs[3] = 2150
		bbs[4] = 5500
		bbs[5] = 4000
		bbs[6] = 1800
		bbs[7] = 1750
		bbs[8] = 1.85
        bbs[9] = {"高级连击","高级必杀","高级吸血","高级强力","高级驱鬼","高级偷袭","高级招架","高级永恒","高级感知","高级神迹","高级幸运","高级神佑复生"}
        bbs[10] = {100,20,20,20,20,20}
        bbs[11] = {"嗜血追击","理直气壮","大快朵颐","浮云神马"}

   end

   return bbs
end




function 加载首充数据()
    自定义数据.首充数据={}
    自定义数据.首充数据.消费说明=f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","消费说明")
    自定义数据.首充数据.经验=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","经验"))
    自定义数据.首充数据.储备=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","储备"))
    自定义数据.首充数据.银子=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","银子"))
    自定义数据.首充数据.仙玉=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","仙玉"))
    自定义数据.首充数据.点卡=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","点卡"))
    自定义数据.首充数据.召唤兽=f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","召唤兽")
    自定义数据.首充数据.类型=f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","类型")
    if 自定义数据.首充数据.类型==nil or 自定义数据.首充数据.类型=="" then
       自定义数据.首充数据.类型 ="宝宝"
    end
    自定义数据.首充数据.物品数量=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","物品数量"))
    自定义数据.首充数据.物品={}
    for k=1,自定义数据.首充数据.物品数量 do
        自定义数据.首充数据.物品[k]={}
        自定义数据.首充数据.物品[k].名称 = f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","物品"..k)
        自定义数据.首充数据.物品[k].数量=tonumber(f函数.读配置(程序目录..[[每日活动\首充配置.txt]],"首充配置","数量"..k))
        if not 自定义数据.首充数据.物品[k].名称 or 自定义数据.首充数据.物品[k].名称=="" or 自定义数据.首充数据.物品[k].名称=="空" then
            __gge.print(true,12,"首充数据:物品缺少"..k.."项目\n")
        end
        if not 自定义数据.首充数据.物品[k].数量 then
            __gge.print(true,12,"首充数据:物品缺少"..k.."数量\n")
        end
    end
    if not 自定义数据.首充数据 then
          __gge.print(true,12,"首充数据配置错误,文件地址:每日活动\n")
    end

end

function 加载累充数据()
    自定义数据.累充数据={}
    local 取出数据 = 取文件的所有名 (程序目录..[[\每日活动\累计充值\]])
    for n=1,#取出数据 do
        自定义数据.累充数据[n]={}
        自定义数据.累充数据[n].文本 = "充值"..n
        自定义数据.累充数据[n].编号 = n
        自定义数据.累充数据[n].需求充值 = tonumber(f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","需求充值"))
        自定义数据.累充数据[n].货币数量 = tonumber(f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","货币数量"))
        自定义数据.累充数据[n].货币类型 = f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","货币类型")
        自定义数据.累充数据[n].召唤兽 = f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","召唤兽")
        自定义数据.累充数据[n].召唤兽类型 = f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","类型")
        if 自定义数据.累充数据[n].召唤兽类型==nil or 自定义数据.累充数据[n].召唤兽类型=="" then
           自定义数据.累充数据[n].召唤兽类型 ="宝宝"
        end
        自定义数据.累充数据[n].物品数量 = tonumber(f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","物品数量"))
        自定义数据.累充数据[n].物品 ={}
        for k=1,自定义数据.累充数据[n].物品数量 do
            自定义数据.累充数据[n].物品[k]={}
            自定义数据.累充数据[n].物品[k].名称 = f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","物品"..k)
            自定义数据.累充数据[n].物品[k].数量=tonumber(f函数.读配置(程序目录..[[每日活动\累计充值\充值]]..n..".txt","累充配置","数量"..k))
            if not 自定义数据.累充数据[n].物品[k].名称 or 自定义数据.累充数据[n].物品[k].名称=="" or 自定义数据.累充数据[n].物品[k].名称=="空" then
                __gge.print(true,12,"累充数据"..n..":物品缺少"..k.."项目\n")
            end
            if not 自定义数据.累充数据[n].物品[k].数量 then
                __gge.print(true,12,"累充数据"..n..":物品缺少"..k.."数量\n")
            end
        end
        if not 自定义数据.累充数据[n] then
              __gge.print(true,12,"累充数据["..n.."]配置错误,文件地址:每日活动\n")
        end
    end
    if not 自定义数据.累充数据 then
          __gge.print(true,12,"累充数据配置错误,文件地址:每日活动\n")
    end


end


function 加载签到物品数据()
  自定义数据.签到物品={[7]={},[14]={},[21]={},[28]={}}
  自定义数据.签到物品[7].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","经验"))
  自定义数据.签到物品[7].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","储备"))
  自定义数据.签到物品[7].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","银子"))
  自定义数据.签到物品[7].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","仙玉"))
  自定义数据.签到物品[7].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","点卡"))
  自定义数据.签到物品[7].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","物品数量"))
  自定义数据.签到物品[7].物品 ={}
  for i=1,自定义数据.签到物品[7].物品数量 do
      自定义数据.签到物品[7].物品[i]={}
      自定义数据.签到物品[7].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","物品"..i)
      自定义数据.签到物品[7].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"七天配置","数量"..i))
      if not 自定义数据.签到物品[7].物品[i].名称 or 自定义数据.签到物品[7].物品[i].名称=="" or 自定义数据.签到物品[7].物品[i].名称=="空" then
          __gge.print(true,12,"签到物品7:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.签到物品[7].物品[i].数量 then
          __gge.print(true,12,"签到物品7:物品缺少"..i.."数量\n")
      end
  end
  if not 自定义数据.签到物品[7] then
      __gge.print(true,12,"签到物品[7]配置错误,文件地址:活动攻略\n")
  end
  自定义数据.签到物品[14].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","经验"))
  自定义数据.签到物品[14].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","储备"))
  自定义数据.签到物品[14].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","银子"))
  自定义数据.签到物品[14].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","仙玉"))
  自定义数据.签到物品[14].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","点卡"))
  自定义数据.签到物品[14].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","物品数量"))
  自定义数据.签到物品[14].物品 ={}
  for i=1,自定义数据.签到物品[14].物品数量 do
      自定义数据.签到物品[14].物品[i]={}
      自定义数据.签到物品[14].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","物品"..i)
      自定义数据.签到物品[14].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"十四天配置","数量"..i))
      if not 自定义数据.签到物品[14].物品[i].名称 or 自定义数据.签到物品[14].物品[i].名称=="" or 自定义数据.签到物品[14].物品[i].名称=="空" then
          __gge.print(true,12,"签到物品14:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.签到物品[14].物品[i].数量 then
          __gge.print(true,12,"签到物品14:物品缺少"..i.."数量\n")
      end
  end
  if not 自定义数据.签到物品[14] then
      __gge.print(true,12,"签到物品[14]配置错误,文件地址:活动攻略\n")
  end
  自定义数据.签到物品[21].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","经验"))
  自定义数据.签到物品[21].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","储备"))
  自定义数据.签到物品[21].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","银子"))
  自定义数据.签到物品[21].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","仙玉"))
  自定义数据.签到物品[21].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","点卡"))
  自定义数据.签到物品[21].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","物品数量"))
  自定义数据.签到物品[21].物品 ={}
  for i=1,自定义数据.签到物品[21].物品数量 do
      自定义数据.签到物品[21].物品[i]={}
      自定义数据.签到物品[21].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","物品"..i)
      自定义数据.签到物品[21].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十一天配置","数量"..i))
      if not 自定义数据.签到物品[21].物品[i].名称 or 自定义数据.签到物品[21].物品[i].名称=="" or 自定义数据.签到物品[21].物品[i].名称=="空" then
          __gge.print(true,12,"签到物品21:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.签到物品[21].物品[i].数量 then
          __gge.print(true,12,"签到物品21:物品缺少"..i.."数量\n")
      end
  end
  if not 自定义数据.签到物品[21] then
      __gge.print(true,12,"签到物品[21]配置错误,文件地址:活动攻略\n")
  end
  自定义数据.签到物品[28].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","经验"))
  自定义数据.签到物品[28].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","储备"))
  自定义数据.签到物品[28].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","银子"))
  自定义数据.签到物品[28].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","仙玉"))
  自定义数据.签到物品[28].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","点卡"))
  自定义数据.签到物品[28].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","物品数量"))
  自定义数据.签到物品[28].物品 ={}
  for i=1,自定义数据.签到物品[28].物品数量 do
      自定义数据.签到物品[28].物品[i]={}
      自定义数据.签到物品[28].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","物品"..i)
      自定义数据.签到物品[28].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\签到配置.txt]],"二十八天配置","数量"..i))
      if not 自定义数据.签到物品[28].物品[i].名称 or 自定义数据.签到物品[28].物品[i].名称=="" or 自定义数据.签到物品[28].物品[i].名称=="空" then
          __gge.print(true,12,"签到物品28:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.签到物品[28].物品[i].数量 then
          __gge.print(true,12,"签到物品28:物品缺少"..i.."数量\n")
      end
  end
  if not 自定义数据.签到物品[28] then
       __gge.print(true,12,"签到物品[28]配置错误,文件地址:活动攻略\n")
  end
end

function 加载活跃物品数据()
  自定义数据.活跃度物品={[100]={},[200]={},[300]={},[400]={},[500]={},[600]={}}
  自定义数据.活跃度物品[100].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","经验"))
  自定义数据.活跃度物品[100].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","储备"))
  自定义数据.活跃度物品[100].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","银子"))
  自定义数据.活跃度物品[100].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","仙玉"))
  自定义数据.活跃度物品[100].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","点卡"))
  自定义数据.活跃度物品[100].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","物品数量"))
  自定义数据.活跃度物品[100].物品 ={}
  for i=1,自定义数据.活跃度物品[100].物品数量 do
      自定义数据.活跃度物品[100].物品[i]={}
      自定义数据.活跃度物品[100].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","物品"..i)
      自定义数据.活跃度物品[100].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"一百配置","数量"..i))
      if not 自定义数据.活跃度物品[100].物品[i].名称 or 自定义数据.活跃度物品[100].物品[i].名称=="" or 自定义数据.活跃度物品[100].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度100:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[100].物品[i].数量 then
          __gge.print(true,12,"活跃度100:物品缺少"..i.."数量\n")
      end
  end
  自定义数据.活跃度物品[200].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","经验"))
  自定义数据.活跃度物品[200].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","储备"))
  自定义数据.活跃度物品[200].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","银子"))
  自定义数据.活跃度物品[200].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","仙玉"))
  自定义数据.活跃度物品[200].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","点卡"))
  自定义数据.活跃度物品[200].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","物品数量"))
  自定义数据.活跃度物品[200].物品 ={}
  for i=1,自定义数据.活跃度物品[200].物品数量 do
      自定义数据.活跃度物品[200].物品[i]={}
      自定义数据.活跃度物品[200].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","物品"..i)
      自定义数据.活跃度物品[200].物品[i].数量 =  tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"二百配置","数量"..i))
      if not 自定义数据.活跃度物品[200].物品[i].名称 or 自定义数据.活跃度物品[200].物品[i].名称=="" or 自定义数据.活跃度物品[200].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度200:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[200].物品[i].数量 then
          __gge.print(true,12,"活跃度200:物品缺少"..i.."数量\n")
      end
  end

  自定义数据.活跃度物品[300].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","经验"))
  自定义数据.活跃度物品[300].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","储备"))
  自定义数据.活跃度物品[300].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","银子"))
  自定义数据.活跃度物品[300].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","仙玉"))
  自定义数据.活跃度物品[300].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","点卡"))
  自定义数据.活跃度物品[300].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","物品数量"))
  自定义数据.活跃度物品[300].物品 ={}
  for i=1,自定义数据.活跃度物品[300].物品数量 do
      自定义数据.活跃度物品[300].物品[i]={}
      自定义数据.活跃度物品[300].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","物品"..i)
      自定义数据.活跃度物品[300].物品[i].数量 =  tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"三百配置","数量"..i))
      if not 自定义数据.活跃度物品[300].物品[i].名称 or 自定义数据.活跃度物品[300].物品[i].名称=="" or 自定义数据.活跃度物品[300].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度300:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[300].物品[i].数量 then
          __gge.print(true,12,"活跃度300:物品缺少"..i.."数量\n")
      end
  end

  自定义数据.活跃度物品[400].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","经验"))
  自定义数据.活跃度物品[400].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","储备"))
  自定义数据.活跃度物品[400].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","银子"))
  自定义数据.活跃度物品[400].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","仙玉"))
  自定义数据.活跃度物品[400].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","点卡"))
  自定义数据.活跃度物品[400].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","物品数量"))
  自定义数据.活跃度物品[400].物品 ={}
  for i=1,自定义数据.活跃度物品[400].物品数量 do
      自定义数据.活跃度物品[400].物品[i]={}
      自定义数据.活跃度物品[400].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","物品"..i)
      自定义数据.活跃度物品[400].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"四百配置","数量"..i))
      if not 自定义数据.活跃度物品[400].物品[i].名称 or 自定义数据.活跃度物品[400].物品[i].名称=="" or 自定义数据.活跃度物品[400].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度400:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[400].物品[i].数量 then
          __gge.print(true,12,"活跃度400:物品缺少"..i.."数量\n")
      end
  end
  自定义数据.活跃度物品[500].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","经验"))
  自定义数据.活跃度物品[500].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","储备"))
  自定义数据.活跃度物品[500].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","银子"))
  自定义数据.活跃度物品[500].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","仙玉"))
  自定义数据.活跃度物品[500].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","点卡"))
  自定义数据.活跃度物品[500].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","物品数量"))
  自定义数据.活跃度物品[500].物品 ={}
  for i=1,自定义数据.活跃度物品[500].物品数量 do
      自定义数据.活跃度物品[500].物品[i]={}
      自定义数据.活跃度物品[500].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","物品"..i)
      自定义数据.活跃度物品[500].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"五百配置","数量"..i))
      if not 自定义数据.活跃度物品[500].物品[i].名称 or 自定义数据.活跃度物品[500].物品[i].名称=="" or 自定义数据.活跃度物品[500].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度500:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[500].物品[i].数量 then
          __gge.print(true,12,"活跃度500:物品缺少"..i.."数量\n")
      end
  end

  自定义数据.活跃度物品[600].经验 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","经验"))
  自定义数据.活跃度物品[600].储备 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","储备"))
  自定义数据.活跃度物品[600].银子 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","银子"))
  自定义数据.活跃度物品[600].仙玉 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","仙玉"))
  自定义数据.活跃度物品[600].点卡 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","点卡"))
  自定义数据.活跃度物品[600].物品数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","物品数量"))
  自定义数据.活跃度物品[600].物品 ={}
  for i=1,自定义数据.活跃度物品[600].物品数量 do
      自定义数据.活跃度物品[600].物品[i]={}
      自定义数据.活跃度物品[600].物品[i].名称 = f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","物品"..i)
      自定义数据.活跃度物品[600].物品[i].数量 = tonumber(f函数.读配置(程序目录..[[活动攻略\活跃度配置.txt]],"六百配置","数量"..i))
      if not 自定义数据.活跃度物品[600].物品[i].名称 or 自定义数据.活跃度物品[600].物品[i].名称=="" or 自定义数据.活跃度物品[600].物品[i].名称=="空" then
          __gge.print(true,12,"活跃度600:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.活跃度物品[600].物品[i].数量 then
          __gge.print(true,12,"活跃度600:物品缺少"..i.."数量\n")
      end
  end
  for i=1,6 do
      if not 自定义数据.活跃度物品[i*100] then
          __gge.print(true,12,"活跃度"..(i*100).."配置错误,文件地址:活动攻略\n")
      end
  end
end

function 更新神兽配置()
  local 代码函数=loadstring(读入文件([[神兽配置.txt]]))
  if 代码函数 then
      代码函数()
  else
      __gge.print(true,12,"未找到神兽配置文本,文件地址:跟目录\n")
  end
  if not 取商城宝宝 or type(取商城宝宝)~="function" then
      __gge.print(true,12,"神兽配置脚本错误,文件地址:跟目录\n")
  end
end

function 更新回收价格配置()
  local 代码函数=loadstring(读入文件([[活动攻略\回收配置.txt]]))
  if 代码函数 then
    代码函数()
  else
      __gge.print(true,12,"未找到回收配置文本,文件地址:活动攻略\n")
  end
  if not 自定义回收价格 or type(自定义回收价格)~="table" then
    __gge.print(true,12,"自定义回收价格脚本错误,文件地址:活动攻略\n")
  else
      for k,v in pairs(自定义回收价格) do
          if type(v)~="number" then
               __gge.print(true,12,"自定义回收价格:"..k.." 项目值格式错误")
          end
      end
  end


end
function 加载回收配置文档()
          if not 自定义数据.一键回收配置 then
                 __gge.print(true,12,"未找到一键回收配置,已初始化一键回收配置,文件地址:商店价格\n")
                自定义数据.一键回收配置={飞行符 = {货币="银子",数量=1}}
          end
          local 排序列表 = {}
          local 长度 = 0
          for k,v in pairs(自定义数据.一键回收配置) do
                if 长度<string.len(k) then
                    长度 = string.len(k)
                    if string.find(k, "级装备")~= nil  or string.find(k, "级灵饰") ~= nil or string.find(k, "级宠物装备")~= nil  then
                        local 取出=分割文本(k,"级")
                        if 取出[1] and tonumber(取出[1])>=100 then
                          长度=长度+1
                        end
                    end
                end
                table.insert(排序列表,{名称=k,序号=string.len(k),价格=v.数量,货币=v.货币})
          end
          table.sort(排序列表,function(a,b) return a.序号<b.序号 end )
          自定义数据.回收配置文档="#H"
          for i, v in ipairs(排序列表) do
              if v.名称 then
                  local 名称 = v.名称.." "
                  for n=string.len(v.名称),长度+1 do
                      名称=名称.." "
                  end

                  if v.名称=="珍珠" or v.名称=="战魂" or v.名称=="钨金" or  v.名称=="灵犀玉" or v.名称=="强化符" or v.名称=="设计图" or v.名称=="天眼珠"
                      or v.名称=="钟灵石" or v.名称=="炼妖石" or v.名称=="附魔宝珠" or v.名称=="精魄灵石"  or v.名称=="元灵晶石" or v.名称=="灵宝图鉴"
                      or v.名称=="神兵图鉴" or v.名称=="灵饰图鉴" or v.名称=="百炼精铁" or   v.名称=="怪物卡片" or v.名称=="灵饰指南书"  or v.名称=="制造指南书"
                      or v.名称=="超级附魔宝珠" or  v.名称=="上古锻造图策" or v.名称 == "红玛瑙" or v.名称 == "太阳石" or v.名称 == "舍利子" or v.名称 == "黑宝石"
                      or v.名称 == "月亮石" or v.名称 == "星辉石" or v.名称 == "光芒石" then
                          自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*等级".."#L"..v.货币.."\n#H"
                    elseif v.名称=="烤肉" or v.名称=="烤鸭" or v.名称=="小还丹" or v.名称=="金香玉" or v.名称=="定神香" or v.名称=="五龙丹" or v.名称=="珍露酒"
                            or v.名称=="虎骨酒" or v.名称=="豆斋果" or v.名称=="佛跳墙" or v.名称=="桂花丸" or v.名称=="臭豆腐" or v.名称=="长寿面" or v.名称=="梅花酒"
                            or v.名称=="百味酒" or v.名称=="蛇胆酒"  or v.名称=="月华露" or v.名称=="醉生梦死" or v.名称=="翡翠豆腐" or v.名称=="蛇蝎美人"
                            or v.名称=="九转金丹" or v.名称=="风水混元丹" or v.名称=="十香返生丸" or v.名称=="千年保心丹" or v.名称=="九转回魂丹" or v.名称=="佛光舍利子" then
                              自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*品质".."#L"..v.货币.."\n#H"
                    elseif string.find(v.名称, "级装备") ~= nil or string.find(v.名称, "级灵饰") ~= nil or string.find(v.名称, "级宠物装备") ~= nil then
                             自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*等级".."#L"..v.货币.."\n#H"
                    elseif string.find(v.名称, "符石") ~= nil and  v.名称~="符石卷轴" then
                              自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*等级".."#L"..v.货币.."\n#H"
                    elseif v.名称=="宝石" then
                            自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*等级".."#L"..v.货币.."\n#H"
                    elseif v.名称=="烹饪" or v.名称=="三级药" then
                            自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*品质".."#L"..v.货币.."\n#H"
                    elseif v.名称=="归墟之证" then
                            自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*星级".."#L"..v.货币.."\n#H"
                    else
                          自定义数据.回收配置文档=自定义数据.回收配置文档..名称.."每个#R"..v.价格.."*数量".."#L"..v.货币.."\n#H"
                    end
              end
          end

end



function 加载假人配置()
      假人数据 = {}
      local 代码函数=loadstring(读入文件([[每日活动\假人配置.txt]]))
      if 代码函数 then
        代码函数()
      else
        __gge.print(true,12,"未找到假人配置,文件地址:每日活动\n")
      end
      if not 假人数据 or type(假人数据)~="table" then
          __gge.print(true,12,"假人配置错误,文件地址:每日活动")
      elseif 假人数据.地图人数 and type(假人数据.地图人数)~="table" then
            __gge.print(true,12,"假人配置地图人数错误,文件地址:每日活动")
      elseif 假人数据.地图名称 and type(假人数据.地图名称)~="table" then
            __gge.print(true,12,"假人配置地图名称错误,文件地址:每日活动")
      elseif 假人数据.地图称谓 and type(假人数据.地图称谓)~="table" then
            __gge.print(true,12,"假人配置地图称谓错误,文件地址:每日活动")
      elseif 假人数据.坐骑祥瑞 and type(假人数据.坐骑祥瑞)~="table" then
            __gge.print(true,12,"假人配置坐骑祥瑞错误,文件地址:每日活动")
      elseif 假人数据.地图锦衣 and type(假人数据.地图锦衣)~="table" then
            __gge.print(true,12,"假人配置地图锦衣错误,文件地址:每日活动")
      elseif 假人数据.地图光环 and type(假人数据.地图光环)~="table" then
            __gge.print(true,12,"假人配置地图光环错误,文件地址:每日活动")
      elseif 假人数据.地图发言 and type(假人数据.地图发言)~="table" then
            __gge.print(true,12,"假人配置地图发言错误,文件地址:每日活动")
      else
          for k,v in pairs(假人数据) do
              if type(v)~="table" then
                      __gge.print(true,12,"假人配置["..k.."],类型错误\n")
              else
                  for n,z in pairs(v) do
                      if k =="地图人数" then
                          if type(n) ~= "number" then
                              __gge.print(true,12,"假人配置["..k.."],["..n.."]类型错误\n")
                          elseif type(z) ~= "table" then
                                  __gge.print(true,12,"假人配置["..k.."],["..n.."]值类型错误\n")
                                  for j,i in pairs(z) do
                                        if type(i) ~= "number" then
                                             __gge.print(true,12,"假人配置["..k.."],["..n.."],["..j.."]值类型错误\n")
                                        end
                                  end
                          end
                      else
                          if type(z) ~= "string" then
                                __gge.print(true,12,"假人配置["..k.."],["..n.."]值类型错误\n")
                          end
                      end
                  end
              end
          end
      end
end




function 加载摆摊假人()
      摆摊假人 = {}
      local 代码函数=loadstring(读入文件([[每日活动\摆摊假人.txt]]))
      if 代码函数 then
        代码函数()
      else
        __gge.print(true,12,"未找到摆摊假人,文件地址:每日活动\n")
      end
      if not 摆摊假人 or type(摆摊假人)~="table" then
          __gge.print(true,12,"假人配置错误,文件地址:每日活动")
      else
          for k,v in pairs(摆摊假人) do
              if type(v)~="table" then
                      __gge.print(true,12,"假人配置["..k.."],类型错误\n")
              else
                  if not v.地图 then
                      __gge.print(true,12,"假人配置["..k.."],未找到地图配置\n")
                  end
                  if not v.x or not v.y or type(v.x)~="number" or type(v.y)~="number"  then
                      __gge.print(true,12,"假人配置["..k.."],未找到坐标配置或坐标配置类型错误\n")
                  end
                  if not v.摊位名称 or type(v.摊位名称)~="string"  then
                      __gge.print(true,12,"假人配置["..k.."],未找到摊位名称配置或摊位名称配置类型错误\n")
                  end


              end
          end
      end
end





function 加载新人礼包配置()
      local 代码函数=loadstring(读入文件([[每日活动\新人礼包.txt]]))
      if 代码函数 then
        代码函数()
      else
        __gge.print(true,12,"未找到新人礼包文本,文件地址:每日活动\n")
      end
      if not 自定义数据.新人礼包 or type(自定义数据.新人礼包)~="table" then
          __gge.print(true,12,"新人礼包配置错误,文件地址:每日活动")
      else
          for k,v in pairs(自定义数据.新人礼包) do
              if v.经验 and type(v.经验)~="number" then
                    __gge.print(true,12,"新人礼包["..k.."],经验类型错误\n")
              elseif v.储备 and type(v.储备)~="number" then
                      __gge.print(true,12,"新人礼包["..k.."],储备类型错误\n")
              elseif v.银子 and type(v.银子)~="number" then
                      __gge.print(true,12,"新人礼包["..k.."],银子类型错误\n")
              elseif v.仙玉 and type(v.仙玉)~="number" then
                      __gge.print(true,12,"新人礼包["..k.."],仙玉类型错误\n")
              elseif v.点卡 and type(v.点卡)~="number" then
                      __gge.print(true,12,"新人礼包["..k.."],点卡类型错误\n")
              elseif v.召唤兽 and type(v.召唤兽)~="string" then
                      __gge.print(true,12,"新人礼包["..k.."],召唤兽类型错误\n")
              elseif v.召唤兽 and (not v.类型 or type(v.类型)~="string") then
                      __gge.print(true,12,"新人礼包["..k.."],类型-类型错误\n")
              elseif  v.物品 and type(v.物品)~="table" then
                       __gge.print(true,12,"新人礼包["..k.."],物品类型错误\n")
              else
                  if v.物品 then
                    for i,n in pairs(v.物品) do
                        if not n.名称 or type(n.名称)~="string" then
                              __gge.print(true,12,"新人礼包["..k.."],物品["..i"],名称类型错误\n")
                        elseif not n.数量 or type(n.数量)~="number" then
                                __gge.print(true,12,"新人礼包["..k.."],物品["..i"],数量类型错误\n")
                        end
                    end
                  end
              end
          end
      end
end
function 加载商店价格数据()
      local 文件名 = 取文件的所有名 (程序目录..[[\商店价格\]])
      for n=1,#文件名 do
          local 代码函数=loadstring(读入文件([[商店价格\]]..文件名[n]..".txt"))
          if 代码函数 then
              代码函数()
          else
              __gge.print(true,12,"未找到"..文件名[n].."文本,文件地址:商店价格\n")
          end
          if 文件名[n]=="胚子配置" then
                if not 自定义数据.低级胚子礼包  or type(自定义数据.低级胚子礼包)~="table" then
                       __gge.print(true,12,"低级胚子礼包配置错误,文件地址:商店价格\n")
                else
                    for k,v in pairs(自定义数据.低级胚子礼包) do
                        if type(v)~="table" then
                             __gge.print(true,12,"低级胚子礼包["..k.."],值类型错误\n")
                        end
                    end
                end
                if not 自定义数据.中级胚子礼包 or type(自定义数据.中级胚子礼包)~="table" then
                       __gge.print(true,12,"中级胚子礼包配置错误,文件地址:商店价格\n")
                else
                    for k,v in pairs(自定义数据.中级胚子礼包) do
                        if type(v)~="table" then
                             __gge.print(true,12,"中级胚子礼包["..k.."],值类型错误\n")
                        end
                    end
                end
                if not 自定义数据.高级胚子礼包 or type(自定义数据.高级胚子礼包)~="table" then
                       __gge.print(true,12,"高级胚子礼包配置错误,文件地址:商店价格\n")
                else
                    for k,v in pairs(自定义数据.高级胚子礼包) do
                        if type(v)~="table" then
                             __gge.print(true,12,"高级胚子礼包["..k.."],值类型错误\n")
                        end
                    end
                end
          elseif 文件名[n]=="一键回收配置" then
                  if not 自定义数据.一键附魔配置 or type(自定义数据.一键附魔配置)~="table"  then
                       __gge.print(true,12,"一键附魔配置错误,文件地址:商店价格\n")
                  else
                      for k,v in pairs(自定义数据.一键附魔配置) do
                          if not 自定义数据.一键附魔配置.货币类型 or type(自定义数据.一键附魔配置.货币类型)~="string"  then
                               __gge.print(true,12,"一键附魔配置[货币类型],值类型错误\n")
                          end
                          if not 自定义数据.一键附魔配置.数量 or type(自定义数据.一键附魔配置.数量)~="number"  then
                              __gge.print(true,12,"一键附魔配置[数量],值类型错误\n")
                          end
                          if not 自定义数据.一键附魔配置.时间 or type(自定义数据.一键附魔配置.时间)~="number"  then
                             __gge.print(true,12,"一键附魔配置[时间],值类型错误\n")
                          end
                      end
                  end
                  if not 自定义数据.一键回收配置 or type(自定义数据.一键回收配置)~="table"  then
                       __gge.print(true,12,"一键回收配置错误,文件地址:商店价格\n")
                  else
                      for k,v in pairs(自定义数据.一键回收配置) do
                            if type(v)~="table" then
                               __gge.print(true,12,"一键回收配置["..k.."],值类型错误\n")
                            else
                                if not v.货币 or type(v.货币)~="string"  then
                                   __gge.print(true,12,"一键回收配置["..k.."][货币],值类型错误\n")
                                end
                                if not v.数量 or type(v.数量)~="number"  then
                                   __gge.print(true,12,"一键回收配置["..k.."][数量],值类型错误\n")
                                end
                            end
                      end
                  end
          elseif 文件名[n]=="仙灵店铺" then
                local 临时循环 = {"一","二","三","四","五"}
                for i,z in ipairs(临时循环) do
                     if not 自定义数据[z.."级仙灵店铺"] then
                         __gge.print(true,12,z.."级仙灵店铺错误,文件地址:商店价格\n")
                      else
                          for k,v in pairs(自定义数据[z.."级仙灵店铺"]) do
                              if not v.名称 or type(v.名称)~="string" then
                                  __gge.print(true,12,z.."级仙灵店铺["..k.."],名称类型错误\n")
                              end
                              if not v.价格 or type(v.价格)~="number" then
                                  __gge.print(true,12,z.."级仙灵店铺["..k.."],价格类型错误\n")
                              end
                          end
                      end

                end
          elseif not 自定义数据[文件名[n]] or type(自定义数据[文件名[n]])~="table" then
                    __gge.print(true,12,文件名[n].."脚本错误,文件地址:商店价格\n")
          else
              if 文件名[n]=="抽奖配置" or 文件名[n]=="盲盒配置"  or 文件名[n]=="嘉年华配置"  or string.find(文件名[n],"礼包") then
                  for k,v in pairs(自定义数据[文件名[n]]) do
                      if not v.名称 or type(v.名称)~="string" then
                          __gge.print(true,12,文件名[n].."["..k.."],名称类型错误\n")
                      end
                      if not v.数量 or type(v.数量)~="table" then
                          __gge.print(true,12,文件名[n].."["..k.."],数量类型错误\n")
                      end
                      if not v.概率 or type(v.概率)~="number" then
                          __gge.print(true,12,文件名[n].."["..k.."],概率类型错误\n")
                      end
                  end
              elseif 文件名[n]=="活动次数" or  文件名[n]=="背包整理配置" or 文件名[n]=="炼丹炉"  then
                      for k,v in pairs(自定义数据[文件名[n]]) do
                          if type(v)~="number" then
                              __gge.print(true,12,文件名[n].."["..k.."],值类型错误\n")
                          end
                      end
              elseif 文件名[n]=="更换模型配置" then
                    if not 自定义数据.更换模型配置.货币类型 or type(自定义数据.更换模型配置.货币类型)~="string" then
                         __gge.print(true,12,文件名[n].."[货币类型],值类型错误\n")
                    end
                    if not 自定义数据.更换模型配置.数量  or  type(自定义数据.更换模型配置.数量)~="number" then
                        __gge.print(true,12,文件名[n].."[数量],值类型错误\n")
                    end
              elseif 文件名[n]=="兽决兑换配置" then
                    if not 自定义数据.兽决兑换配置.特殊 or type(自定义数据.兽决兑换配置.特殊)~="number" then
                         __gge.print(true,12,文件名[n].."[特殊],值类型错误\n")
                    end
                    if not 自定义数据.兽决兑换配置.超级  or  type(自定义数据.兽决兑换配置.超级)~="number" then
                        __gge.print(true,12,文件名[n].."[超级],值类型错误\n")
                    end
              else
                  for k,v in pairs(自定义数据[文件名[n]]) do
                          if not v.名称 or type(v.名称)~="string"  then
                              __gge.print(true,12,文件名[n].."["..k.."],名称值类型错误\n")
                          end
                          if not v.价格 or type(v.价格)~="number"  then
                              __gge.print(true,12,文件名[n].."["..k.."],价格值类型错误\n")
                          end
                  end
              end
          end
      end
end










function 加载自定义爆率数据()
    local 文件名 = 取文件的所有名 (程序目录..[[\物品爆率\]])
    for n=1,#文件名 do
        local 代码函数=loadstring(读入文件([[物品爆率\]]..文件名[n]..".txt"))
        if 代码函数 then
            代码函数()
        else
             __gge.print(true,12,"未找到"..文件名[n].."文本,文件地址:物品爆率\n")
        end
        if 文件名[n]=="归墟活动" then
            local 临时循环 = {"抓鬼任务","鬼王任务","皇宫飞贼","雁塔地宫"}
            for i,v in ipairs(临时循环) do
                if not 自定义数据[文件名[n]][v] or type(自定义数据[文件名[n]][v])~="table" then
                     __gge.print(true,12,文件名[n].."["..v.."],值类型错误,文件地址:物品爆率\n")
                else
                    for k,z in pairs(自定义数据[文件名[n]][v]) do
                        if not z.名称 or type(z.名称)~="string" then
                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],名称值类型错误\n")
                        end
                        if not z.数量 or type(z.数量)~="table" then
                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],数量值类型错误\n")
                        end
                        if not z.概率 or type(z.概率)~="number" then
                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],概率值类型错误\n")
                        end
                    end
                end
            end
        elseif 文件名[n]=="镖王活动" or 文件名[n]=="镖王任务" then
              local 临时循环 = {"普通","高级","珍贵"}
              for i,v in ipairs(临时循环) do
                  if not 自定义数据[文件名[n]][v] or type(自定义数据[文件名[n]][v])~="table" then
                       __gge.print(true,12,文件名[n].."["..v.."],值类型错误,文件地址:物品爆率\n")
                  else
                      for k,z in pairs(自定义数据[文件名[n]][v]) do
                          if not z.名称 or type(z.名称)~="string" then
                              __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],名称值类型错误\n")
                          end
                          if not z.数量 or type(z.数量)~="table" then
                              __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],数量值类型错误\n")
                          end
                          if not z.概率 or type(z.概率)~="number" then
                              __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],概率值类型错误\n")
                          end
                      end
                  end
              end
        elseif 文件名[n]=="世界挑战" then
                if not 自定义数据[文件名[n]].最终一击 or type(自定义数据[文件名[n]].最终一击)~="table" then
                    __gge.print(true,12,文件名[n].."[最终一击],值类型错误,文件地址:物品爆率\n")
                else
                    for k,z in pairs(自定义数据[文件名[n]].最终一击) do
                        if not z.名称 or type(z.名称)~="string" then
                            __gge.print(true,12,文件名[n].."[最终一击]".."["..k.."],名称值类型错误\n")
                        end
                        if not z.数量 or type(z.数量)~="table" then
                            __gge.print(true,12,文件名[n].."[最终一击]".."["..k.."],数量值类型错误\n")
                        end
                        if not z.概率 or type(z.概率)~="number" then
                            __gge.print(true,12,文件名[n].."[最终一击]".."["..k.."],概率值类型错误\n")
                        end
                    end
                end
                if not 自定义数据[文件名[n]].第一名 or type(自定义数据[文件名[n]].第一名)~="table" then
                    __gge.print(true,12,文件名[n].."[第一名],值类型错误,文件地址:物品爆率\n")
                else
                    for k,z in pairs(自定义数据[文件名[n]].第一名) do
                        if not z.名称 or type(z.名称)~="string" then
                            __gge.print(true,12,文件名[n].."[第一名]".."["..k.."],名称值类型错误\n")
                        end
                        if not z.数量 or type(z.数量)~="table" then
                            __gge.print(true,12,文件名[n].."[第一名]".."["..k.."],数量值类型错误\n")
                        end
                        if not z.概率 or type(z.概率)~="number" then
                            __gge.print(true,12,文件名[n].."[第一名]".."["..k.."],概率值类型错误\n")
                        end
                    end
                end
                if not 自定义数据[文件名[n]].第二名 or type(自定义数据[文件名[n]].第二名)~="table" then
                    __gge.print(true,12,文件名[n].."[第二名],值类型错误,文件地址:物品爆率\n")
                else
                    for k,z in pairs(自定义数据[文件名[n]].第二名) do
                        if not z.名称 or type(z.名称)~="string" then
                            __gge.print(true,12,文件名[n].."[第二名]".."["..k.."],名称值类型错误\n")
                        end
                        if not z.数量 or type(z.数量)~="table" then
                            __gge.print(true,12,文件名[n].."[第二名]".."["..k.."],数量值类型错误\n")
                        end
                        if not z.概率 or type(z.概率)~="number" then
                            __gge.print(true,12,文件名[n].."[第二名]".."["..k.."],概率值类型错误\n")
                        end
                    end
                end

                if not 自定义数据[文件名[n]].第三名 or type(自定义数据[文件名[n]].第三名)~="table" then
                    __gge.print(true,12,文件名[n].."[第三名],值类型错误,文件地址:物品爆率\n")
                else
                    for k,z in pairs(自定义数据[文件名[n]].第三名) do
                        if not z.名称 or type(z.名称)~="string" then
                            __gge.print(true,12,文件名[n].."[第三名]".."["..k.."],名称值类型错误\n")
                        end
                        if not z.数量 or type(z.数量)~="table" then
                            __gge.print(true,12,文件名[n].."[第三名]".."["..k.."],数量值类型错误\n")
                        end
                        if not z.概率 or type(z.概率)~="number" then
                            __gge.print(true,12,文件名[n].."[第三名]".."["..k.."],概率值类型错误\n")
                        end
                    end
                end
        elseif 文件名[n]=="门派入侵" or 文件名[n]=="生死劫"  or 文件名[n]=="镇妖塔"  or 文件名[n]=="嘉年华副本" then
                for k,v in pairs(自定义数据[文件名[n]]) do
                    if type(v)~="table" then
                           __gge.print(true,12,文件名[n].."["..k.."],值类型错误,文件地址:物品爆率\n")
                    else
                        for i,z in pairs(v) do
                            if type(z)~="table" then
                                __gge.print(true,12,文件名[n].."["..k.."]".."["..i.."],值类型错误\n")
                            else
                                if not z.名称 or type(z.名称)~="string" then
                                    __gge.print(true,12,文件名[n].."["..k.."]".."["..i.."],名称值类型错误\n")
                                end
                                if not z.数量 or type(z.数量)~="table" then
                                    __gge.print(true,12,文件名[n].."["..k.."]".."["..i.."],数量值类型错误\n")
                                end
                                if not z.概率 or type(z.概率)~="number" then
                                    __gge.print(true,12,文件名[n].."["..k.."]".."["..i.."],概率值类型错误\n")
                                end
                            end
                        end
                    end
                end
        elseif 文件名[n]=="地煞任务" or 文件名[n]=="天罡星任务" then
                local 任务等级={69,89,109,129,149,169,189}
                for i,v in ipairs(任务等级) do
                    if not 自定义数据[文件名[n]][v] or type(自定义数据[文件名[n]][v])~="table" then
                        __gge.print(true,12,文件名[n].."["..v.."],值类型错误,文件地址:物品爆率\n")
                    else
                        for k=1,10 do
                            if not 自定义数据[文件名[n]][v][k] or type(自定义数据[文件名[n]][v][k])~="table" then
                                __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."],值类型错误\n")
                            else
                                for j,z in pairs(自定义数据[文件名[n]][v][k]) do
                                    if type(z)~="table" then
                                        __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."]".."["..j.."],值类型错误\n")
                                    else
                                        if not z.名称 or type(z.名称)~="string" then
                                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."]".."["..j.."],名称值类型错误\n")
                                        end
                                        if not z.数量 or type(z.数量)~="table" then
                                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."]".."["..j.."],数量值类型错误\n")
                                        end
                                        if not z.概率 or type(z.概率)~="number" then
                                            __gge.print(true,12,文件名[n].."["..v.."]".."["..k.."]".."["..j.."],概率值类型错误\n")
                                        end
                                    end
                                end
                            end
                        end
                    end
                end
        elseif not 自定义数据[文件名[n]] or type(自定义数据[文件名[n]])~="table" then
                __gge.print(true,12,文件名[n].."脚本错误,文件地址:物品爆率\n")
        else
            if 文件名[n]=="帮战宝箱" then
                for k,z in pairs(自定义数据[文件名[n]]) do
                    if not z.名称 or type(z.名称)~="string" then
                        __gge.print(true,12,文件名[n].."["..k.."],名称值类型错误\n")
                    end
                    if not z.数量 or type(z.数量)~="table" then
                        __gge.print(true,12,文件名[n].."["..k.."],数量值类型错误\n")
                    end
                    if not z.概率 or type(z.概率)~="number" then
                        __gge.print(true,12,文件名[n].."["..k.."],概率值类型错误\n")
                    end
                end
                if not 自定义数据.帮战宝箱刷出数量 or type(自定义数据.帮战宝箱刷出数量)~="table" then
                       __gge.print(true,12,文件名[n].." 宝箱刷出数量,脚本错误,文件地址:物品爆率\n")
                else
                    if not 自定义数据.帮战宝箱刷出数量[1] or type(自定义数据.帮战宝箱刷出数量[1])~="number" then
                         __gge.print(true,12,文件名[n].." 宝箱刷出数量[1],值类型错误\n")
                    end
                    if not 自定义数据.帮战宝箱刷出数量[2] or type(自定义数据.帮战宝箱刷出数量[2])~="number" then
                         __gge.print(true,12,文件名[n].." 宝箱刷出数量[2],值类型错误\n")
                    end
                end
                if not 自定义数据.帮战宝箱拾取数量 or type(自定义数据.帮战宝箱拾取数量)~="number" then
                     __gge.print(true,12,文件名[n].." 宝箱拾取数量,脚本错误,文件地址:物品爆率\n")
                end
            elseif 文件名[n]=="辰星排行奖励" then
                    for i=1,15 do
                         if not 自定义数据[文件名[n]][i] or type(自定义数据[文件名[n]][i])~="table" then
                               __gge.print(true,12,文件名[n].."["..i.."],值类型错误,文件地址:物品爆率\n")
                         else
                              for k,z in pairs(自定义数据[文件名[n]][i]) do
                                  if not z.名称 or type(z.名称)~="string" then
                                      __gge.print(true,12,文件名[n].."["..k.."],名称值类型错误\n")
                                  end
                                  if not z.数量 or type(z.数量)~="table" then
                                      __gge.print(true,12,文件名[n].."["..k.."],数量值类型错误\n")
                                  end
                                  if not z.概率 or type(z.概率)~="number" then
                                      __gge.print(true,12,文件名[n].."["..k.."],概率值类型错误\n")
                                  end
                              end
                         end
                    end
            else
                  for k,z in pairs(自定义数据[文件名[n]]) do
                      if not z.名称 or type(z.名称)~="string" then
                          __gge.print(true,12,文件名[n].."["..k.."],名称值类型错误\n")
                      end
                      if not z.数量 or type(z.数量)~="table" then
                          __gge.print(true,12,文件名[n].."["..k.."],数量值类型错误\n")
                      end
                      if not z.概率 or type(z.概率)~="number" then
                          __gge.print(true,12,文件名[n].."["..k.."],概率值类型错误\n")
                      end
                  end
            end
        end
    end




end







function 加载月卡数据()
    自定义数据.月卡数据={}
    自定义数据.月卡数据.标题 = "查看会员"
    自定义数据.月卡数据.消费说明 = f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","消费说明")
    自定义数据.月卡数据.月卡价格 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","月卡价格"))
    自定义数据.月卡数据.月卡货币 = f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","月卡货币")
    自定义数据.月卡数据.经验 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","经验"))
    自定义数据.月卡数据.储备 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","储备"))
    自定义数据.月卡数据.银子 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","银子"))
    自定义数据.月卡数据.仙玉 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","仙玉"))
    自定义数据.月卡数据.点卡 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","点卡"))
    自定义数据.月卡数据.抓鬼 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","抓鬼"))
    自定义数据.月卡数据.物品数量 = tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","物品数量"))
    自定义数据.月卡数据.物品 ={}
    自定义数据.月卡数据.显示物品 = ""
    if  自定义数据.月卡数据.物品数量>12 then
       自定义数据.月卡数据.物品数量 = 12
    end
    if 自定义数据.月卡数据.经验>0 then
      自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "经验:"..自定义数据.月卡数据.经验.."、"
    end
    if 自定义数据.月卡数据.储备>0 then
      自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "储备:"..自定义数据.月卡数据.储备.."、"
    end
    if 自定义数据.月卡数据.银子>0 then
      自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "银子:"..自定义数据.月卡数据.银子.."、"
    end
    if 自定义数据.月卡数据.仙玉>0 then
      自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "仙玉:"..自定义数据.月卡数据.仙玉.."、"
    end
    if 自定义数据.月卡数据.点卡>0 then
       自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "点卡:"..自定义数据.月卡数据.点卡.."、"
    end
    if 自定义数据.月卡数据.抓鬼>0 then
       自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 .. "自动抓鬼:"..自定义数据.月卡数据.抓鬼.."、"
    end


    for i=1,自定义数据.月卡数据.物品数量 do
      自定义数据.月卡数据.物品[i]={}
      自定义数据.月卡数据.物品[i].名称 = f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","物品"..i)
      自定义数据.月卡数据.物品[i].数量 =  tonumber(f函数.读配置(程序目录..[[每日活动\月卡配置.txt]],"月卡配置","数量"..i))
      if string.len(自定义数据.月卡数据.显示物品)<=110 then
          自定义数据.月卡数据.显示物品 = 自定义数据.月卡数据.显示物品 ..自定义数据.月卡数据.物品[i].名称.. "、"
      end
      if not 自定义数据.月卡数据.物品[i].名称 or 自定义数据.月卡数据.物品[i].名称=="" or 自定义数据.月卡数据.物品[i].名称=="空" then
          __gge.print(true,12,"月卡数据:物品缺少"..i.."项目\n")
      end
      if not 自定义数据.月卡数据.物品[i].数量 then
          __gge.print(true,12,"月卡数据:物品缺少"..i.."数量\n")
      end
    end
    if not 自定义数据.月卡数据 then
        __gge.print(true,12,"月卡数据配置错误,文件地址:每日活动\n")
    end
end

function 加载路费数据()
    自定义数据.路费数据={}
    自定义数据.路费数据.经验 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","经验"))
    自定义数据.路费数据.储备 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","储备"))
    自定义数据.路费数据.银子 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","银子"))
    自定义数据.路费数据.仙玉 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","仙玉"))
    自定义数据.路费数据.点卡 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","点卡"))
    自定义数据.路费数据.抓鬼 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","抓鬼"))
    自定义数据.路费数据.物品数量 = tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","物品数量"))
    自定义数据.路费数据.物品 ={}
    for i=1,自定义数据.路费数据.物品数量 do
        自定义数据.路费数据.物品[i]={}
        自定义数据.路费数据.物品[i].名称 = f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","物品"..i)
        自定义数据.路费数据.物品[i].数量 =  tonumber(f函数.读配置(程序目录..[[每日活动\路费配置.txt]],"路费配置","数量"..i))
        if not 自定义数据.路费数据.物品[i].名称 or 自定义数据.路费数据.物品[i].名称=="" or 自定义数据.路费数据.物品[i].名称=="空" then
            __gge.print(true,12,"路费数据:物品缺少"..i.."项目\n")
        end
        if not 自定义数据.路费数据.物品[i].数量 then
            __gge.print(true,12,"路费数据:物品缺少"..i.."数量\n")
        end
    end
    if not 自定义数据.路费数据 then
        __gge.print(true,12,"路费数据配置错误,文件地址:每日活动\n")
    end


end

function 加载自定义活动刷新时间()
        自定义数据.活动刷新时间 ={}
        local 代码函数=loadstring(读入文件([[活动刷新时间\活动刷新时间.txt]]))
        if 代码函数 then
             代码函数()
        else
            __gge.print(true,12,"活动刷新时间配置错误,文件地址:活动刷新时间\n")
        end
        if not 自定义数据.活动刷新时间 or type(自定义数据.活动刷新时间)~="table" then
             __gge.print(true,12,"活动刷新时间配置错误,文件地址:活动刷新时间\n")
        else
            for k,v in pairs(自定义数据.活动刷新时间) do
                if not v.名称 or type(v.名称)~="string" then
                     __gge.print(true,12,"活动刷新时间["..k.."],名称值类型错误\n")
                end
                if not v.时间 or type(v.时间)~="number" then
                     __gge.print(true,12,"活动刷新时间["..k.."],时间值类型错误\n")
                end
                if not v.分钟 or type(v.分钟)~="number" or v.分钟==0 then
                     __gge.print(true,12,"活动刷新时间["..k.."],分钟值类型错误\n")
                end
            end
        end



        自定义数据.活动任务时间={}
        local 代码函数1=loadstring(读入文件([[活动刷新时间\任务活动时间.txt]]))
        if 代码函数1 then
             代码函数1()
              if 彩虹争霸 and 自定义数据.活动任务时间.彩虹争霸 then
                 彩虹争霸.活动时间= 自定义数据.活动任务时间.彩虹争霸
              end
              if 长安保卫战 and 自定义数据.活动任务时间.长安保卫战 then
                 长安保卫战.活动时间= 自定义数据.活动任务时间.长安保卫战
              end
        else
           __gge.print(true,12,"活动任务时间配置错误,文件地址:活动刷新时间\n")
        end
        if not 自定义数据.活动任务时间 or type(自定义数据.活动任务时间)~="table" then
             __gge.print(true,12,"活动任务时间配置错误,文件地址:活动刷新时间\n")
        else
            for k,v in pairs(自定义数据.活动任务时间) do
                if not v.时间 or type(v.时间)~="number" then
                     __gge.print(true,12,"活动任务时间["..k.."],时间值类型错误\n")
                end
                if not v.分钟 or type(v.分钟)~="number" or v.分钟==0 then
                     __gge.print(true,12,"活动任务时间["..k.."],分钟值类型错误\n")
                end
                if not v.日期 or (type(v.日期)~="string" and  type(v.日期)~="table" and type(v.日期)~="number") then
                     __gge.print(true,12,"活动任务时间["..k.."],日期值类型错误\n")
                end
            end
        end
end

function 加载敏感字判断()
  local 代码函数=loadstring(读入文件([[每日活动\判断敏感字.txt]]))
  if 代码函数 then
      代码函数()
  else
     __gge.print(true,12,"判断敏感字配置错误,文件地址:每日活动\n")
  end
  if not 自定义数据.判断敏感字 or type(自定义数据.判断敏感字)~="table" then
             __gge.print(true,12,"判断敏感字配置错误,文件地址:每日活动\n")
  else
        for k,v in pairs(自定义数据.判断敏感字) do
              if type(v)~="table" then
                      __gge.print(true,12,"判断敏感字["..k.."],值类型错误\n")
              elseif not v[1] or type(v[1])~="string" then
                      __gge.print(true,12,"判断敏感字["..k.."],[1]值类型错误\n")
              elseif not v[2] or type(v[2])~="string" then
                      __gge.print(true,12,"判断敏感字["..k.."],[2]值类型错误\n")
              end
        end
  end
end

function 敏感字判断(r, t)
      if t then
          for k,v in pairs(自定义数据.判断敏感字) do
                  if string.find(r,v[1]) then
                        return true
                  end
          end
      else
          for k,v in pairs(自定义数据.判断敏感字) do
                  if string.find(r,v[1]) then
                        return string.gsub(r,v[1],v[2])
                  end
          end
          return r
      end
end




function 刷新活动自定义数据()
        自定义数据.自定义公告内容 = 读入文件([[活动攻略\标题配置.txt]])
        刷新新手使者奖励()
        加载自定义活动刷新时间()
        加载月卡数据()
        加载首充数据()
        加载累充数据()
        加载活跃物品数据()
        加载签到物品数据()
        更新回收价格配置()
        更新神兽配置()
        加载路费数据()

end


function 刷新自定义数据()
    刷新活动自定义数据()
    加载自定义爆率数据()
    加载新人礼包配置()
    加载商店价格数据()
    战斗准备类:加载战斗数据()
    商店处理类:更新商店价格数据()
    加载回收配置文档()
    加载敏感字判断()
end






function 删除所有玩家数据()
  local 玩家账号=取文件夹的所有名 (程序目录..[[\data]])
      for n=1,#玩家账号 do
          local 玩家id = 取文件夹的所有名(程序目录..[[\data\]]..玩家账号[n]..[[\]])
           for i=1,#玩家id do
              写出文件([[data/]]..玩家账号[n]..[[/]]..玩家id[i]..[[/角色.txt]],"")
              写出文件([[data/]]..玩家账号[n]..[[/]]..玩家id[i]..[[/道具.txt]],"")
              写出文件([[data/]]..玩家账号[n]..[[/]]..玩家id[i]..[[/召唤兽.txt]],"")
              写出文件([[data/]]..玩家账号[n]..[[/]]..玩家id[i]..[[/召唤兽仓库.txt]],"")
              写出文件([[data/]]..玩家账号[n]..[[/]]..玩家id[i]..[[/道具仓库.txt]],"")
              os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\]]..玩家id[i]..[[\角色.txt]])
              os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\]]..玩家id[i]..[[\道具.txt]])
              os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\]]..玩家id[i]..[[\召唤兽.txt]])
              os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\]]..玩家id[i]..[[\召唤兽仓库.txt]])
              os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\]]..玩家id[i]..[[\道具仓库.txt]])
           end

           写出文件([[data/]]..玩家账号[n]..[[/信息.txt]],"")
           写出文件([[data/]]..玩家账号[n]..[[/账号信息.txt]],"")

           os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\信息.txt]])
           os.remove(程序目录..[[\data\]]..玩家账号[n]..[[\账号信息.txt]])
      end
 写出文件([[配置文件.ini]],"")
 写出文件([[仙玉商城.txt]],"")
 写出文件([[仙玉商城购买处理.txt]],"")
 os.remove(程序目录..[[\配置文件.ini]])
 os.remove(程序目录..[[\仙玉商城.txt]])
 os.remove(程序目录..[[\仙玉商城购买处理.txt]])
end