-- @作者: baidwwy
-- @邮箱:  <EMAIL>
-- @创建时间:   2017-03-20 12:38:43
-- @最后修改来自: baidwwy
-- @Last Modified time: 2017-03-24 09:42:31
local __hpnew 	 = require("luahp.server")
local tcpserver  = require("hp/TcpServer")
local packserver = class(tcpserver)
packserver._mode = 'pack'
packserver._new  = __hpnew
function packserver:初始化()
	self._hp = self._new(__gge.cs,__gge.state)
	self._hp:Create_TcpPackServer(self)
end

function packserver:OnReceive(dwConnID,pData)--数据到达
	print("[gge引擎 PackServer] ========== OnReceive被调用 ==========")
	print("[gge引擎 PackServer] 连接ID:", dwConnID)
	print("[gge引擎 PackServer] 数据包地址:", pData)
	print("[gge引擎 PackServer] 数据类型:", type(pData))
	
	if pData then
		local dataLen = #pData
		print("[gge引擎 PackServer] 数据长度:", dataLen)
		
		if dataLen > 0 then
			-- 打印数据的前64字节（十六进制）
			local hexStr = ""
			local maxBytes = math.min(64, dataLen)
			for i = 1, maxBytes do
				hexStr = hexStr .. string.format("%02X ", string.byte(pData, i))
				if i % 16 == 0 then hexStr = hexStr .. "\n" end
			end
			print("[gge引擎 PackServer] 接收到的数据(前"..maxBytes.."字节):")
			print(hexStr)
			
			-- 检查数据格式
			if dataLen >= 2 then
				local firstByte = string.byte(pData, 1)
				local secondByte = string.byte(pData, 2)
				print("[gge引擎 PackServer] 数据开头字节:", string.format("0x%02X 0x%02X", firstByte, secondByte))
				
				if firstByte == 0x91 then
					print("[gge引擎 PackServer] ✓ 检测到MessagePack数组标识(0x91)")
				elseif firstByte == 0x2E and secondByte == 0x03 then
					print("[gge引擎 PackServer] ✓ 检测到PackServer包头标识(814)")
				else
					print("[gge引擎 PackServer] ⚠ 未知数据格式")
				end
			end
		else
			print("[gge引擎 PackServer] ⚠ 数据长度为0")
		end
	else
		print("[gge引擎 PackServer] ✗ 收到空数据包")
	end
	
	if self.数据到达 then
		print("[gge引擎 PackServer] 调用数据到达回调函数")
	    __gge.safecall(self.数据到达,self,dwConnID,pData)
	else
		print("[gge引擎 PackServer] 警告: 数据到达回调函数未设置")
	end
	print("[gge引擎 PackServer] ========== OnReceive处理完成 ==========")
	return 0
end
function packserver:发送(dwConnID,文本)
	self._hp:Send(dwConnID,ffi.getptr(文本),#文本,0)
end
--/* 设置数据包最大长度（有效数据包最大长度不能超过 4194303/0x3FFFFF 字节，默认：262144/0x40000） */
function packserver:置数据最大长度(dwMaxPackSize)
	self._hp:SetMaxPackSize(dwMaxPackSize)
end
--/* 设置包头标识（有效包头标识取值范围 0 ~ 1023/0x3FF，当包头标识为 0 时不校验包头，默认：0） */
function packserver:置包头标识(usPackHeaderFlag)
	self._hp:SetPackHeaderFlag(usPackHeaderFlag)
end
--/* 获取数据包最大长度 */
function packserver:取数据包最大长度()
	return self._hp:GetMaxPackSize()
end
--/* 获取包头标识 */
function packserver:取包头标识()
	return self._hp:GetPackHeaderFlag()
end
return packserver