-- @Author: baidwwy
-- @Date:   2023-12-06 02:25:03
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-06-06 20:09:50
战斗处理类 = class()

----:解除状态结果(单位,名称,编号,技能名称)      解除状态组
--:取恢复气血                                   local 基础 = DeepCopy(self.计算属性) 基础.初始伤害 = 1 local 结果=self:取基础治疗计算(编号,目标,"不灭",单位.等级,基础)
--奇经八脉套装触发几率                          套装触发几率
--奇经八脉取装备追加法术                        装备追加法术
--奇经八脉经脉异常解除                          解除所有经脉异常
--取封印状态技能                                self.技能数据.封印
--取异常状态法术                                self.技能数据.异常
--取封印状态1                                   取友封印数量
--取法宝异常法术                                self.技能数据.法宝异常
--取对方可偷取增益技能1                         重复函数已移除  随机对方增益状态
--奇经八脉增益技能驱散                          移除相应内容写道技能数据的 解除状态 函数中
--取对方可偷取增益技能                          随机对方增益状态
--取玩家经脉增益                                随机取消经脉增益
--取玩家增益技能数量                            取增益数量
--取玩家异常状态数量                            取异常数量
--:取多个友方目标                            --取友方目标组
function 战斗处理类:初始化()
    self.计算属性= {初始系数=1,叠加系数=0,初始伤害=0,防御系数=1,忽视防御=0, ---初始计算
                    暴击系数=1,暴击增加=0,暴伤系数=0,暴伤增加=0,            ---暴击计算
                    结果系数=1,结果伤害=0}                                  ---最终计算

    self.五维属性 = {"体质","魔力","力量","耐力","敏捷"}
    self.修炼类型 = { "攻击修炼","法术修炼","防御修炼","抗法修炼","猎术修炼"}
    self.加载需求 = {"变异","饰品","法宝","武器","装备","锦衣","变身","副武器","染色组","染色方案","捉鬼变异","物伤减少",
                    "法伤减少","躲避减少","不可封印","同门单位","武器染色组","饰品染色组","武器染色方案",
                    "饰品染色方案"}

    self.基础属性 = {"气血","魔法","命中","伤害","法伤","防御","法防","速度","最大气血","最大魔法"}

    self.基础加载 = {名称 = "未知",模型="泡泡",等级=175,门派 = "无门派",武器伤害 = 500,追加概率 = 0,经脉流派 = "无",附加阵法="普通"}
    self.判断空表 = {"技能","奇经八脉","奇经特效","追加法术","附加状态","内丹数据","特技技能","符石技能","神话词条"}
    self.重置空表 = {"已加技能","法术状态","首次触发","法宝佩戴","主动技能"}
    self.重置加载 = {必杀=1,法暴=0,法连=0,驱怪=0,连击=0,溅射=0,战意=0,狮魂=0,风灵=0,符咒=0,剑意=0,溅射人数=0,超级战意=0,
                   高山流水=0,天降大任=0,飞檐走壁=0,慈悲效果=0,毫毛次数=0,法术吸血=0,攻击五行="",防御五行="",怒击效果=false,
                   灵元 = {数值=1,回合=3},灵药={红=0,蓝=0,黄=0},人参娃娃 ={层数=0,回合=0},五行珠={金=0,木=0,水=0,火=0,土=0},
                   雷法={雷法崩裂=0,雷法震煞=0,雷法坤伏=0,雷法翻天=0,雷法倒海=0}}

    self.灵饰属性={"气血回复效果","抗法术暴击等级","格挡值","法术防御","抗物理暴击等级","封印命中等级","抵抗封印等级","固定伤害","法术伤害","法术暴击等级","物理暴击等级","狂暴等级","穿刺等级","法术伤害结果","治疗能力"}
    self.状态属性 ={类型=1,回合=0,躲闪=0,必杀=0,连击=0,法暴=0,法连=0,躲避减少=0}
    for i, v in ipairs(self.五维属性) do
        self.状态属性[v]=0
    end
    for i, v in ipairs(self.基础属性) do
        self.状态属性[v]=0
    end
    for i, v in ipairs(self.灵饰属性) do
        self.状态属性[v]=0
    end
    self.技能类型 = {普攻=1,物伤=1,法伤=1,固伤=1,复活=1,恢复=1,增益=1,减益=1,封印=1,群体物伤=1}
    self.冷却数据 ={"佛眷加成","奇经八脉傲视","奇经八脉潜心","奇经八脉攻伐","奇经八脉摧心","奇经八脉余悸","奇经八脉专神",
                     "救死扶伤","奇经八脉慈心","奇经八脉强袭","奇经八脉清吟","奇经八脉龙啸","奇经八脉蚀天","奇经八脉燃魂",
                     "奇经八脉烈焰","奇经八脉空灵","奇经八脉偷龙转凤","奇经八脉莲音","奇经八脉蔓延","奇经八脉嗜血",
                     "奇经八脉轰鸣","奇经八脉趁虚","奇经八脉存雄","奇经八脉噬魂","奇经八脉恶焰","奇经八脉入魂","超级感知",
                     "超级冥思","已招架","怒击触发","嗜血触发","超级弱点土","超级弱点雷","超级弱点火","超级弱点水","强力眩晕","超级抵抗",
                     "超级驱怪","灵宝惊兽云尺","智能特技"}

   self.技能数据={封印={},增益={},减益={},异常={},法宝异常={},经脉增益={},经脉异常={},主动法宝={}}
   self:初始化技能数据()
end

function 战斗处理类:初始化技能数据()
          for k,v in pairs(战斗技能) do
                if v.类型=="封印" then
                      table.insert(self.技能数据.封印, k)
                elseif v.类型=="增益" then
                        table.insert(self.技能数据.增益, k)
                elseif v.类型=="敌方增益" or v.类型=="减益" then
                        table.insert(self.技能数据.减益, k)
                end
          end
          self.技能数据.异常 = {"象形","摧心","瘴气","黄泉之息",
          "轰鸣","破甲术","碎甲术","停陷术","凝滞术","河东狮吼","锢魂术",
          "放下屠刀","腾雷","冰川怒","日月乾坤","毒","暗器毒","尸腐毒 ","雾杀",
          "威慑","夺魄令","煞气诀","锋芒毕露","诱袭","反间之计","一笑倾城",
          "百万神兵","错乱","镇妖","催眠符","失心符","落魄符","失忆符","追魂符",
          "离魂符","失魂符","定身符","莲步轻舞","如花解语","似玉生香","含情脉脉","魔音摄魂","天罗地网",
          "催化","雷浪穿云","画地为牢","顺势而为","落花成泥"
          }
          self.技能数据.法宝异常 ={"无字经","无尘扇","摄魂","无魂傀儡","断线木偶","鬼泣","惊魂铃","发瘟匣","鬼泣"}

          local 临时封印={"楚楚可怜","象形","天罗地网","冰川怒"}
          for i,v in ipairs(临时封印) do
                table.insert(self.技能数据.封印,v)
          end


          local 临时增益={"龙魂","龙骇龙腾","龙骇龙卷","御风","狂怒","智眼","怒眼","天眼","噬毒","清净"}
          for i,v in ipairs(临时增益) do
                table.insert(self.技能数据.增益,v)
          end


          self.技能数据.经脉增益 ={"奇经八脉潜心","奇经八脉专神","奇经八脉花护","奇经八脉淬芒","奇经八脉空灵 ","佛眷加成",
                    "聚气加成","奇经八脉跃动","奇经八脉蔓延","奇经八脉傲视","奇经八脉强袭","奇经八脉天照",
                    "奇经八脉破浪","奇经八脉蚀天","奇经八脉嗜血","奇经八脉莲音","奇经八脉趁虚","奇经八脉存雄","奇经八脉噬魂",
                    "奇经八脉恶焰","奇经八脉入魂"}
          self.技能数据.经脉异常 = {"奇经八脉摧心","奇经八脉勇武加成","奇经八脉销武","奇经八脉仁心","奇经八脉龙啸","奇经八脉清吟",
                    "奇经八脉情劫","奇经八脉慧眼","奇经八脉轰鸣","奇经八脉陷阱削弱","奇经八脉踏魄","奇经八脉致命","映日妙舞减伤"}

          self.技能数据.主动法宝 ={"干将莫邪","苍白纸人","五彩娃娃","混元伞","乾坤玄火塔","聚妖铃","万鬼幡",
                      "鬼泣","摄魂","断线木偶","无魂傀儡","缚妖索","捆仙绳","无字经","发瘟匣","七杀","无尘扇","惊魂铃","清心咒"}

end

require("Script/战斗处理类/战斗计算/进入战斗")
require("Script/战斗处理类/战斗计算/加载数据")
require("Script/战斗处理类/战斗计算/客户处理")
require("Script/战斗处理类/战斗计算/命令流程")
require("Script/战斗处理类/战斗计算/技能使用")
require("Script/战斗处理类/战斗计算/道具使用")
require("Script/战斗处理类/战斗计算/战斗流程/物理流程")
require("Script/战斗处理类/战斗计算/战斗流程/物理计算")
require("Script/战斗处理类/战斗计算/战斗流程/普攻流程")
require("Script/战斗处理类/战斗计算/战斗流程/法伤流程")
require("Script/战斗处理类/战斗计算/战斗流程/法伤计算")
require("Script/战斗处理类/战斗计算/战斗流程/固伤流程")
require("Script/战斗处理类/战斗计算/战斗流程/封印流程")
require("Script/战斗处理类/战斗计算/战斗流程/治疗流程")
require("Script/战斗处理类/战斗计算/战斗流程/状态流程")
require("Script/战斗处理类/战斗计算/战斗流程/总伤计算")
require("Script/战斗处理类/战斗计算/胜利计算")
require("Script/战斗处理类/战斗计算/结束流程")


--return 战斗处理类