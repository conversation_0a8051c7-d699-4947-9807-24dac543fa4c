--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-01-08 21:31:03
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解所以资源
--======================================================================--
function 取敌人信息(dr)
	local drs = {}
	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回####################  175级
	if dr == 29998 then
		drs = {"持国巡守","持国巡守",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,20281}
	elseif dr == 29999 then
		drs = {"持国巡守头领","持国巡守",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,20281}
	elseif dr == 30000 then
		drs = {"持国巡守宝宝","持国巡守",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,0}
	elseif dr == 30001 then
		drs = {"持国巡守宝宝","持国巡守",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,0}

	elseif dr == 30002 then
		drs = {"毗舍童子","毗舍童子",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,20281}
	elseif dr == 30003 then
		drs = {"毗舍童子头领","毗舍童子",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,20281}
	elseif dr == 30004 then
		drs = {"毗舍童子宝宝","毗舍童子",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,0}
	elseif dr == 30005 then
		drs = {"毗舍童子宝宝","毗舍童子",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,0}

	elseif dr == 130002 then
		drs = {"灵灯侍者","灵灯侍者",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,20281}
	elseif dr == 130003 then
		drs = {"灵灯侍者头领","灵灯侍者",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,20281}
	elseif dr == 130004 then
		drs = {"灵灯侍者宝宝","灵灯侍者",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,0}
	elseif dr == 130005 then
		drs = {"灵灯侍者宝宝","灵灯侍者",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"合纵","高级敏捷","高级法术抵抗","高级连击","精神集中"},nil,true,0}

	elseif dr == 130006 then
		drs = {"般若天女","般若天女",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,20281}
	elseif dr == 130007 then
		drs = {"般若天女头领","般若天女",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,20281}
	elseif dr == 130008 then
		drs = {"般若天女宝宝","般若天女",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,0}
	elseif dr == 130009 then
		drs = {"般若天女宝宝","般若天女",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"须弥真言","再生","奔雷咒","神佑复生"},nil,true,0}

	elseif dr == 30006 then
		drs = {"真陀护法","真陀护法",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 30007 then
		drs = {"真陀护法头领","真陀护法",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 30008 then
		drs = {"真陀护法宝宝","真陀护法",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 30009 then
		drs = {"真陀护法宝宝","真陀护法",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}

	elseif dr == 40009 then
		drs = {"增长巡守","增长巡守",175,2,1420,1600,4500,2500,1610,1500,0,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 40010 then
		drs = {"增长巡守头领","增长巡守",175,2.2,1420,1600,4500,2500,1610,1500,1,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 40011 then
		drs = {"增长巡守宝宝","增长巡守",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}
	elseif dr == 40012 then
		drs = {"增长巡守宝宝","增长巡守",1,2,1420,1600,4500,2500,1610,1500,2,nil,nil,{"高级精神集中","必杀","偷袭","吸血","驱鬼","连击"},nil,true,0}


	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回####################  165级
	elseif dr == -9999 then
		drs = {"金身罗汉","金身罗汉",165,2,1380,1500,4400,2400,1560,1440,0,nil,nil,{"永恒","高级反震","神佑复生","盾气","高级敏捷"},nil,true,15281}
	elseif dr == -9998 then
		drs = {"金身罗汉头领","金身罗汉",165,2.2,1380,1500,4400,2400,1560,1440,1,nil,nil,{"永恒","高级反震","神佑复生","盾气","高级敏捷"},nil,true,15581}
	elseif dr == -9997 then
		drs = {"金身罗汉宝宝","金身罗汉",0,1,0,0,0,0,0,0,2,nil,nil,{"永恒","高级反震","神佑复生","盾气","高级敏捷"},nil,true,0}
	elseif dr == -9996 then
		drs = {"变异金身罗汉","金身罗汉",0,1,0,0,0,0,0,0,2,2000,{2,0},{"永恒","高级反震","神佑复生","盾气","高级敏捷"},nil,true,0}

	elseif dr == -9995 then
		drs = {"曼珠沙华","曼珠沙华",165,2,1440,1440,3800,2640,1500,1440,0,nil,nil,{"奔雷咒","魔之心","反震","法术连击","高级再生"},nil,nil,15281}
	elseif dr == -9994 then
		drs = {"曼珠沙华头领","曼珠沙华",165,2.2,1440,1440,3800,2640,1500,1440,1,nil,nil,{"奔雷咒","魔之心","反震","法术连击","高级再生"},nil,nil,15581}
	elseif dr == -9993 then
		drs = {"曼珠沙华宝宝","曼珠沙华",0,1,0,0,0,0,0,0,2,nil,nil,{"奔雷咒","魔之心","反震","法术连击","高级再生"},nil,nil,0}
	elseif dr == -9992 then
		drs = {"变异曼珠沙华","曼珠沙华",0,1,0,0,0,0,0,0,3,2070,{1,0},{"奔雷咒","魔之心","反震","法术连击","高级再生"},nil,nil,0}

	elseif dr == -9991 then
		drs = {"修罗傀儡妖","修罗傀儡妖",165,2,1536,1380,3800,2400,1500,1440,0,nil,nil,{"合纵","感知","高级幸运","高级连击"},nil,true,15281}
	elseif dr == -9990 then
		drs = {"修罗傀儡妖头领","修罗傀儡妖",165,2.2,1536,1380,3800,2400,1500,1440,1,nil,nil,{"合纵","感知","高级幸运","高级连击"},nil,true,15581}
	elseif dr == -9989 then
		drs = {"修罗傀儡妖宝宝","修罗傀儡妖",0,1,1536,1380,3800,2400,1500,1440,2,nil,nil,{"合纵","感知","高级幸运","高级连击"},nil,true,0}
	elseif dr == -9988 then
		drs = {"变异修罗傀儡妖","修罗傀儡妖",0,1,1536,1380,3800,2400,1500,1440,3,2078,{5,0},{"合纵","感知","高级幸运","高级连击"},nil,true,0}
	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回####################  155级
	elseif dr == -9987 then
		drs = {"蜃气妖","蜃气妖",155,2,1464,1440,5040,3240,1320,1440,0,nil,nil,{"高级法术连击","雷击","法术暴击","上古灵符"},nil,true,14281}
	elseif dr == -9986 then
		drs = {"蜃气妖头领","蜃气妖",155,2.2,1464,1440,5040,3240,1320,1440,1,nil,nil,{"高级法术连击","雷击","法术暴击","上古灵符"},nil,true,14581}
	elseif dr == -9985 then
		drs = {"蜃气妖宝宝","蜃气妖",0,1,0,0,0,0,0,0,2,nil,nil,{"高级法术连击","雷击","法术暴击","上古灵符"},nil,true,0}
	elseif dr == -9984 then
		drs = {"变异蜃气妖","蜃气妖",0,1,0,0,0,0,0,0,0,2079,{5,0},{"高级法术连击","雷击","法术暴击","上古灵符"},nil,true,0}

	elseif dr == -9983 then
		drs = {"蔓藤妖花","蔓藤妖花",155,2,1464,1440,4560,3120,1200,1440,0,nil,nil,{"灵能激发","落岩","高级招架","高级法术连击"},nil,nil,14281}
	elseif dr == -9982 then
		drs = {"蔓藤妖花头领","蔓藤妖花",155,2.2,1464,1440,4560,3120,1200,1440,1,nil,nil,{"灵能激发","落岩","高级招架","高级法术连击"},nil,nil,14581}
	elseif dr == -9981 then
		drs = {"蔓藤妖花宝宝","蔓藤妖花",0,1,0,0,0,0,0,0,2,nil,nil,{"灵能激发","落岩","高级招架","高级法术连击"},nil,nil,0}
	elseif dr == -9980 then
		drs = {"变异蔓藤妖花","蔓藤妖花",0,1,0,0,0,0,0,0,3,2042,{1,0},{"灵能激发","落岩","高级招架","高级法术连击"},nil,nil,0}

	elseif dr == -9979 then
		drs = {"修罗傀儡鬼","修罗傀儡鬼",155,2,1524,1380,5040,2400,1440,1440,0,nil,nil,{"高级驱鬼","反震","高级必杀","强力","嗜血追击"},nil,true,14281}
	elseif dr == -9978 then
		drs = {"修罗傀儡鬼头领","修罗傀儡鬼",155,2.2,1524,1380,5040,2400,1440,1440,1,nil,nil,{"高级驱鬼","反震","高级必杀","强力","嗜血追击"},nil,true,14581}
	elseif dr == -9977 then
		drs = {"修罗傀儡鬼宝宝","修罗傀儡鬼",0,1,0,0,0,0,0,0,2,nil,nil,{"高级驱鬼","反震","高级必杀","强力","嗜血追击"},nil,true,0}
	elseif dr == -9976 then
		drs = {"变异修罗傀儡鬼","修罗傀儡鬼",0,1,0,0,0,0,0,0,3,2065,{1,0},{"高级驱鬼","反震","高级必杀","强力","嗜血追击"},nil,true,0}

	elseif dr == -9975 then
		drs = {"巨力神猿","巨力神猿",155,2,1500,1440,4560,2640,1500,1560,0,nil,nil,{"敏捷","高级盾气","高级连击","强力","高级驱鬼"},nil,nil,14281}
	elseif dr == -9974 then
		drs = {"巨力神猿头领","巨力神猿",155,2.2,1500,1440,4560,2640,1500,1560,1,nil,nil,{"敏捷","高级盾气","高级连击","强力","高级驱鬼"},nil,nil,14581}
	elseif dr == -9973 then
		drs = {"巨力神猿宝宝","巨力神猿",0,1,0,0,0,0,0,0,2,nil,nil,{"敏捷","高级盾气","高级连击","强力","高级驱鬼"},nil,nil,0}
	elseif dr == -9972 then
		drs = {"变异巨力神猿","巨力神猿",0,1,0,0,0,0,0,0,3,2079,{0,1},{"敏捷","高级盾气","高级连击","强力","高级驱鬼"},nil,nil,0}

	elseif dr == -9971 then
		drs = {"长眉灵猴","长眉灵猴",155,2,1500,1440,4560,2640,1500,1560,0,nil,nil,{"奔雷咒","高级法术暴击","冥思","高级再生"},nil,nil,14281}
	elseif dr == -9970 then
		drs = {"长眉灵猴头领","长眉灵猴",155,2.2,1500,1440,4560,2640,1500,1560,1,nil,nil,{"奔雷咒","高级法术暴击","冥思","高级再生"},nil,nil,14581}
	elseif dr == -9969 then
		drs = {"长眉灵猴宝宝","长眉灵猴",0,1,0,0,0,0,0,0,2,nil,nil,{"奔雷咒","高级法术暴击","冥思","高级再生"},nil,nil,0}
	elseif dr == -9968 then
		drs = {"变异长眉灵猴","长眉灵猴",0,1,0,0,0,0,0,0,3,2051,{1,0},{"奔雷咒","高级法术暴击","冥思","高级再生"},nil,nil,0}

	elseif dr == -9967 then
		drs = {"混沌兽","混沌兽",155,2,1440,1476,5400,3240,1320,1560,0,nil,nil,{"再生","高级慧根","高级永恒","高级魔之心","奔雷咒"},nil,nil,14281}
	elseif dr == -9966 then
		drs = {"混沌兽头领","混沌兽",155,2.2,1440,1476,5400,3240,1320,1560,1,nil,nil,{"再生","高级慧根","高级永恒","高级魔之心","奔雷咒"},nil,nil,15281}
	elseif dr == -9965 then
		drs = {"混沌兽宝宝","混沌兽",0,1,0,0,0,0,0,0,2,nil,nil,{"再生","高级慧根","高级永恒","高级魔之心","奔雷咒"},nil,nil,0}
	elseif dr == -9964 then
		drs = {"变异混沌兽","混沌兽",0,1,0,0,0,0,0,0,3,2078,{6,0},{"再生","高级慧根","高级永恒","高级魔之心","奔雷咒"},nil,nil,0}

	elseif dr == -9963 then
		drs = {"狂豹","狂豹人形",155,2,1536,1440,4800,2280,1440,1560,0,nil,nil,{"驱鬼","高级飞行","高级强力","偷袭","吸血"},nil,nil,14281}
	elseif dr == -9962 then
		drs = {"狂豹头领","狂豹人形",155,2.2,1536,1440,4800,2280,1440,1560,1,nil,nil,{"驱鬼","高级飞行","高级强力","偷袭","吸血"},nil,nil,15281}
	elseif dr == -9961 then
		drs = {"狂豹宝宝","狂豹人形",0,1,0,0,0,0,0,0,2,nil,nil,{"驱鬼","高级飞行","高级强力","偷袭","吸血"},nil,nil,0}
	elseif dr == -9960 then
		drs = {"变异狂豹","狂豹人形",0,1,0,0,0,0,0,0,3,20230,{3,0},{"驱鬼","高级飞行","高级强力","偷袭","吸血"},nil,nil,0}

	elseif dr == -9959 then
		drs = {"猫灵","猫灵人形",155,2,1524,1464,4560,2640,1500,1680,0,nil,nil,{"必杀","高级幸运","高级偷袭","反击","敏捷"},nil,nil,14281}
	elseif dr == -9958 then
		drs = {"猫灵头领","猫灵人形",155,2.2,1524,1464,4560,2640,1500,1680,1,nil,nil,{"必杀","高级幸运","高级偷袭","反击","敏捷"},nil,nil,15281}
	elseif dr == -9957 then
		drs = {"猫灵宝宝","猫灵人形",0,1,0,0,0,0,0,0,2,nil,nil,{"必杀","高级幸运","高级偷袭","反击","敏捷"},nil,nil,0}
	elseif dr == -9956 then
		drs = {"变异猫灵","猫灵人形",0,1,0,0,0,0,0,0,3,2042,{1,0},{"必杀","高级幸运","高级偷袭","反击","敏捷"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 145级
	elseif dr == -9955 then
		drs = {"巴蛇","巴蛇",145,2.1,1524,1440,4800,1560,1560,1560,0,nil,nil,{"敏捷","嗜血追击","感知","毒","再生"},nil,nil,13581}
	elseif dr == -9954 then
		drs = {"巴蛇头领","巴蛇",145,2.3,1524,1440,4800,1560,1560,1560,1,nil,nil,{"敏捷","嗜血追击","感知","毒","再生"},nil,nil,13881}
	elseif dr == -9953 then
		drs = {"巴蛇宝宝","巴蛇",0,1,0,0,0,0,0,0,2,nil,nil,{"敏捷","嗜血追击","感知","毒","再生"},nil,nil,0}
	elseif dr == -9952 then
		drs = {"变异巴蛇","巴蛇",0,1,0,0,0,0,0,0,3,2070,{1,0},{"敏捷","嗜血追击","感知","毒","再生"},nil,nil,0}

	elseif dr == -9951 then
		drs = {"连弩车","连弩车",145,2.1,1500,1560,5400,2400,1200,1320,0,nil,nil,{"高级强力","防御","连击","迟钝"},nil,nil,13581}
	elseif dr == -9950 then
		drs = {"连弩车头领","连弩车",145,2.3,1500,1560,5400,2400,1200,1320,1,nil,nil,{"高级强力","防御","连击","迟钝"},nil,nil,13881}
	elseif dr == -9949 then
		drs = {"连弩车宝宝","连弩车",0,1,0,0,0,0,0,0,2,nil,nil,{"高级强力","防御","连击","迟钝"},nil,nil,0}
	elseif dr == -9948 then
		drs = {"变异连弩车","连弩车",0,1,0,0,0,0,0,0,3,2070,{1,0},{"高级强力","防御","连击","迟钝"},nil,nil,0}

	elseif dr == -9947 then
		drs = {"机关鸟","机关鸟",145,2.1,1500,1344,4800,2520,1560,1560,0,nil,nil,{"高级偷袭","驱鬼","高级再生","神迹","飞行"},nil,nil,13581}
	elseif dr == -9946 then
		drs = {"机关鸟头领","机关鸟",145,2.3,1500,1344,4800,2520,1560,1560,1,nil,nil,{"高级偷袭","驱鬼","高级再生","神迹","飞行"},nil,nil,13881}
	elseif dr == -9945 then
		drs = {"机关鸟宝宝","机关鸟",0,1,0,0,0,0,0,0,2,nil,nil,{"高级偷袭","驱鬼","高级再生","神迹","飞行"},nil,nil,0}
	elseif dr == -9944 then
		drs = {"变异机关鸟","机关鸟",0,1,0,0,0,0,0,0,3,2070,{1,0},{"高级偷袭","驱鬼","高级再生","神迹","飞行"},nil,nil,0}

	elseif dr == -9943 then
		drs = {"机关兽","机关兽",145,2.1,1440,1500,5280,2880,1440,1440,0,nil,nil,{"魔之心","高级法术连击","土属性吸收","烈火"},nil,nil,13581}
	elseif dr == -9942 then
		drs = {"机关兽头领","机关兽",145,2.3,1440,1500,5280,2880,1440,1440,1,nil,nil,{"魔之心","高级法术连击","土属性吸收","烈火"},nil,nil,13881}
	elseif dr == -9941 then
		drs = {"机关兽宝宝","机关兽",0,1,0,0,0,0,0,0,2,nil,nil,{"魔之心","高级法术连击","土属性吸收","烈火"},nil,nil,0}
	elseif dr == -9940 then
		drs = {"变异机关兽","机关兽",0,1,0,0,0,0,0,0,3,2070,{1,0},{"魔之心","高级法术连击","土属性吸收","烈火"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 135级
	elseif dr == -9939 then
		drs = {"机关人","机关人",135,2.1,1500,1500,5400,1680,1320,1440,0,nil,nil,{"壁垒击破","弱点火","必杀","高级招架"},nil,nil,13081}
	elseif dr == -9938 then
		drs = {"机关人头领","机关人",135,2.3,1500,1500,5400,1680,1320,1440,1,nil,nil,{"壁垒击破","弱点火","必杀","高级招架"},nil,nil,13381}
	elseif dr == -9937 then
		drs = {"机关人宝宝","机关人",0,1,0,0,0,0,0,0,2,nil,nil,{"壁垒击破","弱点火","必杀","高级招架"},nil,nil,0}
	elseif dr == -9936 then
		drs = {"变异机关人","机关人",0,1,0,0,0,0,0,0,3,2070,{1,0},{"壁垒击破","弱点火","必杀","高级招架"},nil,nil,0}

	elseif dr == -9935 then
		drs = {"龙龟","龙龟",135,2.1,1500,1500,5400,1680,1320,1440,0,nil,nil,{"水属性吸收","反震","高级防御","法术防御","水攻"},nil,true,13081}
	elseif dr == -9934 then
		drs = {"龙龟头领","龙龟",135,2.3,1500,1500,5400,1680,1320,1440,1,nil,nil,{"水属性吸收","反震","高级防御","法术防御","水攻"},nil,true,13381}
	elseif dr == -9933 then
		drs = {"龙龟宝宝","龙龟",0,1,0,0,0,0,0,0,2,nil,nil,{"水属性吸收","反震","高级防御","法术防御","水攻"},nil,true,0}
	elseif dr == -9932 then
		drs = {"变异龙龟","龙龟",0,1,0,0,0,0,0,0,3,112,{0,1},{"水属性吸收","反震","高级防御","法术防御","水攻"},nil,true,0}

	elseif dr == -9931 then
		drs = {"红萼仙子","红萼仙子",135,2.1,1500,1500,5400,3000,960,1800,0,nil,nil,{"上古灵符","高级飞行","高级冥思","奔雷咒"},nil,true,13081}
	elseif dr == -9930 then
		drs = {"红萼仙子头领","红萼仙子",135,2.3,1500,1500,5400,3000,960,1800,1,nil,nil,{"上古灵符","高级飞行","高级冥思","奔雷咒"},nil,true,13381}
	elseif dr == -9929 then
		drs = {"红萼仙子宝宝","红萼仙子",0,1,0,0,0,0,0,0,2,nil,nil,{"上古灵符","高级飞行","高级冥思","奔雷咒"},nil,true,0}
	elseif dr == -9928 then
		drs = {"变异红萼仙子","红萼仙子",0,1,0,0,0,0,0,0,3,111,{0,1},{"上古灵符","高级飞行","高级冥思","奔雷咒"},nil,true,0}

	elseif dr == -9927 then
		drs = {"踏云兽","踏云兽",135,2.1,1524,1440,5400,1880,1440,1440,0,nil,nil,{"高级招架","高级必杀","高级强力","弱点土"},nil,nil,13081}
	elseif dr == -9926 then
		drs = {"踏云兽头领","踏云兽",135,2.3,1524,1440,5400,1880,1440,1440,1,nil,nil,{"高级招架","高级必杀","高级强力","弱点土"},nil,nil,13381}
	elseif dr == -9925 then
		drs = {"踏云兽宝宝","踏云兽",0,1,0,0,0,0,0,0,2,nil,nil,{"高级招架","高级必杀","高级强力","弱点土"},nil,nil,0}
	elseif dr == -9924 then
		drs = {"变异踏云兽","踏云兽",0,1,0,0,0,0,0,0,3,110,{0,1},{"高级招架","高级必杀","高级强力","弱点土"},nil,nil,0}

	elseif dr == -9923 then
		drs = {"猫灵","猫灵兽形",135,2.15,1464,1464,4080,2400,1560,1680,0,nil,nil,{"必杀","敏捷","高级偷袭","弱点水"},nil,nil,13081}
	elseif dr == -9922 then
		drs = {"猫灵头领","猫灵兽形",135,2.35,1464,1464,4080,2400,1560,1680,1,nil,nil,{"必杀","敏捷","高级偷袭","弱点水"},nil,nil,13381}
	elseif dr == -9921 then
		drs = {"猫灵宝宝","猫灵兽形",0,1,0,0,0,0,0,0,2,nil,nil,{"必杀","敏捷","高级偷袭","弱点水"},nil,nil,0}
	elseif dr == -9920 then
		drs = {"变异猫灵","猫灵兽形",0,1,0,0,0,0,0,0,3,2057,{0,1},{"必杀","敏捷","高级偷袭","弱点水"},nil,nil,0}

	elseif dr == -9919 then
		drs = {"狂豹","狂豹兽形",135,2.15,1500,1440,4560,2160,1320,1560,0,nil,nil,{"高级强力","驱鬼","高级飞行","偷袭"},nil,nil,13081}
	elseif dr == -9918 then
		drs = {"狂豹头领","狂豹兽形",135,2.35,1500,1440,4560,2160,1320,1560,1,nil,nil,{"高级强力","驱鬼","高级飞行","偷袭"},nil,nil,13381}
	elseif dr == -9917 then
		drs = {"狂豹宝宝","狂豹兽形",0,1,0,0,0,0,0,0,2,nil,nil,{"高级强力","驱鬼","高级飞行","偷袭"},nil,nil,0}
	elseif dr == -9916 then
		drs = {"变异狂豹","狂豹兽形",0,1,0,0,0,0,0,0,3,2065,{1,0},{"高级强力","驱鬼","高级飞行","偷袭"},nil,nil,0}

	elseif dr == -9915 then
		drs = {"蝎子精","蝎子精",135,2.15,1464,1464,6240,2880,1320,1920,0,nil,nil,{"高级反震","招架","高级再生","毒"},nil,nil,13081}
	elseif dr == -9914 then
		drs = {"蝎子精头领","蝎子精",135,2.35,1464,1464,6240,2880,1320,1920,1,nil,nil,{"高级反震","招架","高级再生","毒"},nil,nil,13381}
	elseif dr == -9913 then
		drs = {"蝎子精宝宝","蝎子精",0,1,0,0,0,0,0,0,2,nil,nil,{"高级反震","招架","高级再生","毒"},nil,nil,0}
	elseif dr == -9912 then
		drs = {"变异蝎子精","蝎子精",0,1,0,0,0,0,0,0,3,2071,{1,0},{"高级反震","招架","高级再生","毒"},nil,nil,0}

	elseif dr == -9911 then
		drs = {"葫芦宝贝","葫芦宝贝",135,2.15,1400,1500,4800,2760,1320,1800,0,nil,nil,{"高级冥思","上古灵符","反震","魔之心"},nil,nil,13081}
	elseif dr == -9910 then
		drs = {"葫芦宝贝头领","葫芦宝贝",135,2.35,1400,1500,4800,2760,1320,1800,1,nil,nil,{"高级冥思","上古灵符","反震","魔之心"},nil,nil,13381}
	elseif dr == -9909 then
		drs = {"葫芦宝贝宝宝","葫芦宝贝",0,1,0,0,0,0,0,0,2,nil,nil,{"高级冥思","上古灵符","反震","魔之心"},nil,nil,0}
	elseif dr == -9908 then
		drs = {"变异葫芦宝贝","葫芦宝贝",0,1,0,0,0,0,0,0,3,2071,{1,0},{"高级冥思","上古灵符","反震","魔之心"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 125级
	elseif dr == -9907 then
		drs = {"琴仙","琴仙",125,2.15,1555,1555,5184,3499,1490,1555,0,nil,nil,{"泰山压顶","魔之心","神佑复生","再生","敏捷"},nil,nil,12381}
	elseif dr == -9906 then
		drs = {"琴仙头领","琴仙",125,2.35,1555,1555,5184,3499,1490,1555,1,nil,nil,{"泰山压顶","魔之心","神佑复生","再生","敏捷"},nil,nil,12781}
	elseif dr == -9905 then
		drs = {"琴仙宝宝","琴仙",0,1,0,0,0,0,0,0,2,nil,nil,{"泰山压顶","魔之心","神佑复生","再生","敏捷"},nil,nil,0}
	elseif dr == -9904 then
		drs = {"变异琴仙","琴仙",0,1,0,0,0,0,0,0,3,2071,{1,0},{"泰山压顶","魔之心","神佑复生","再生","敏捷"},nil,nil,0}

	elseif dr == -9903 then
		drs = {"噬天虎","噬天虎",125,2.15,1500,1440,4800,2400,1500,1560,0,nil,nil,{"高级连击","驱鬼","幸运","高级强力"},nil,true,12381}
	elseif dr == -9902 then
		drs = {"噬天虎头领","噬天虎",125,2.35,1500,1440,4800,2400,1500,1560,1,nil,nil,{"高级连击","驱鬼","幸运","高级强力"},nil,true,12781}
	elseif dr == -9901 then
		drs = {"噬天虎宝宝","噬天虎",0,1,0,0,0,0,0,0,2,nil,nil,{"高级连击","驱鬼","幸运","高级强力"},nil,true,0}
	elseif dr == -9900 then
		drs = {"变异噬天虎","噬天虎",0,1,0,0,0,0,0,0,3,106,{1,0},{"高级连击","驱鬼","幸运","高级强力"},nil,true,0}

	elseif dr == -9899 then
		drs = {"炎魔神","炎魔神",125,2.15,1500,1440,4800,3000,1440,1320,0,nil,nil,{"高级必杀","高级火属性吸收","烈火","地狱烈火"},nil,nil,12381}
	elseif dr == -9898 then
		drs = {"炎魔神头领","炎魔神",125,2.35,1500,1440,4800,2400,1500,1560,1,nil,nil,{"高级必杀","高级火属性吸收","烈火","地狱烈火"},nil,nil,12781}
	elseif dr == -9897 then
		drs = {"炎魔神宝宝","炎魔神",0,1,0,0,0,0,0,0,2,nil,nil,{"高级必杀","高级火属性吸收","烈火","地狱烈火"},nil,nil,0}
	elseif dr == -9896 then
		drs = {"变异炎魔神","炎魔神",0,1,0,0,0,0,0,0,3,105,{1,0},{"高级必杀","高级火属性吸收","烈火","地狱烈火"},nil,nil,0}

	elseif dr == -9895 then
		drs = {"夜罗刹","夜罗刹",125,2.15,1500,1440,5760,2880,1440,1440,0,nil,nil,{"必杀","高级敏捷","高级魔之心","夜舞倾城"},nil,true,12381}
	elseif dr == -9894 then
		drs = {"夜罗刹头领","夜罗刹",125,2.35,1500,1440,5760,2880,1440,1440,1,nil,nil,{"必杀","高级敏捷","高级魔之心","夜舞倾城"},nil,true,12781}
	elseif dr == -9893 then
		drs = {"夜罗刹宝宝","夜罗刹",0,1,0,0,0,0,0,0,2,nil,nil,{"必杀","高级敏捷","高级魔之心","夜舞倾城"},nil,true,0}
	elseif dr == -9892 then
		drs = {"变异夜罗刹","夜罗刹",0,1,0,0,0,0,0,0,3,104,{1,0},{"必杀","高级敏捷","高级魔之心","夜舞倾城"},nil,true,0}

	elseif dr == -9891 then
		drs = {"金铙僧","金铙僧",125,2.15,1620,1555,5443,2462,1555,1684,0,nil,nil,{"再生","必杀","招架","偷袭","高级防御"},nil,nil,12381}
	elseif dr == -9890 then
		drs = {"金铙僧头领","金铙僧",125,2.35,1620,1555,5443,2462,1555,1684,1,nil,nil,{"再生","必杀","招架","偷袭","高级防御"},nil,nil,12781}
	elseif dr == -9889 then
		drs = {"金铙僧宝宝","金铙僧",0,1,0,0,0,0,0,0,2,nil,nil,{"再生","必杀","招架","偷袭","高级防御"},nil,nil,0}
	elseif dr == -9888 then
		drs = {"变异金铙僧","金铙僧",0,1,0,0,0,0,0,0,3,2069,{0,1},{"再生","必杀","招架","偷袭","高级防御"},nil,nil,0}

	elseif dr == -9887 then
		drs = {"灵鹤","灵鹤",125,2.15,1440,1440,4560,2760,1560,1440,0,nil,nil,{"高级永恒","高级驱鬼","高级再生","高级慧根","飞行"},nil,true,12381}
	elseif dr == -9886 then
		drs = {"灵鹤头领","灵鹤",125,2.35,1440,1440,4560,2760,1560,1440,1,nil,nil,{"高级永恒","高级驱鬼","高级再生","高级慧根","飞行"},nil,true,12781}
	elseif dr == -9885 then
		drs = {"灵鹤宝宝","灵鹤",0,1,0,0,0,0,0,0,2,nil,nil,{"高级永恒","高级驱鬼","高级再生","高级慧根","飞行"},nil,true,0}
	elseif dr == -9884 then
		drs = {"变异灵鹤","灵鹤",0,1,0,0,0,0,0,0,3,103,{1,0},{"高级永恒","高级驱鬼","高级再生","高级慧根","飞行"},nil,true,0}

	elseif dr == -9883 then
		drs = {"雾中仙","雾中仙",125,2.15,1440,1500,5400,3000,1320,1800,0,nil,nil,{"高级神佑复生","高级感知","法术连击","敏捷"},nil,true,12381}
	elseif dr == -9882 then
		drs = {"雾中仙头领","雾中仙",125,2.35,1440,1500,5400,3000,1320,1800,1,nil,nil,{"高级神佑复生","高级感知","法术连击","敏捷"},nil,true,12781}
	elseif dr == -9881 then
		drs = {"雾中仙宝宝","雾中仙",0,1,0,0,0,0,0,0,2,nil,nil,{"高级神佑复生","高级感知","法术连击","敏捷"},nil,true,0}
	elseif dr == -9880 then
		drs = {"变异雾中仙","雾中仙",0,1,0,0,0,0,0,0,3,102,{0,1},{"高级神佑复生","高级感知","法术连击","敏捷"},nil,true,0}

	elseif dr == -9879 then
		drs = {"大力金刚","大力金刚",125,2.15,1548,1344,6000,2640,1200,1320,0,nil,nil,{"高级强力","泰山压顶","力劈华山","高级永恒"},nil,true,12381}
	elseif dr == -9878 then
		drs = {"大力金刚头领","大力金刚",125,2.35,1548,1344,6000,2640,1200,1320,1,nil,nil,{"高级强力","泰山压顶","力劈华山","高级永恒"},nil,true,12781}
	elseif dr == -9877 then
		drs = {"大力金刚宝宝","大力金刚",0,1,0,0,0,0,0,0,2,nil,nil,{"高级强力","泰山压顶","力劈华山","高级永恒"},nil,true,0}
	elseif dr == -9876 then
		drs = {"变异大力金刚","大力金刚",0,1,0,0,0,0,0,0,3,101,{1,0},{"高级强力","泰山压顶","力劈华山","高级永恒"},nil,true,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 105级
	elseif dr == -8000 then
		drs = {"鬼将","鬼将",105,2.5,1524,1380,5040,1440,1320,1320,0,nil,nil,{"惊心一剑","高级必杀","冥思","鬼魂术"},nil,true,12381}
	elseif dr == -7999 then
		drs = {"鬼将头领","鬼将",105,2.6,1524,1380,5040,1440,1320,1320,1,nil,nil,{"惊心一剑","高级必杀","冥思","鬼魂术"},nil,true,12781}
	elseif dr == -7998 then
		drs = {"鬼将宝宝","鬼将",0,1,0,0,0,0,0,0,2,nil,nil,{"惊心一剑","高级必杀","冥思","鬼魂术"},nil,true,0}
	elseif dr == -7997 then
		drs = {"变异鬼将","鬼将",0,1,0,0,0,0,0,0,3,95,{1,0},{"惊心一剑","高级必杀","冥思","鬼魂术"},nil,true,0}

	elseif dr == -7986 then
		drs = {"画魂","画魂",105,2.5,1380,1440,4320,2880,1320,1440,0,nil,nil,{"高级鬼魂术","地狱烈火","幸运","高级魔之心"},nil,true,12381}
	elseif dr == -7985 then
		drs = {"画魂头领","画魂",105,2.6,1380,1440,4320,2880,1320,1440,1,nil,nil,{"高级鬼魂术","地狱烈火","幸运","高级魔之心"},nil,true,12781}
	elseif dr == -7984 then
		drs = {"画魂宝宝","画魂",0,1,0,0,0,0,0,0,2,nil,nil,{"高级鬼魂术","地狱烈火","幸运","高级魔之心"},nil,true,0}
	elseif dr == -7983 then
		drs = {"变异画魂","画魂",0,1,0,0,0,0,0,0,3,20113,{4,0},{"高级鬼魂术","地狱烈火","幸运","高级魔之心"},nil,true,0}

	elseif dr == -7982 then
		drs = {"幽萤娃娃","幽萤娃娃",105,2.5,1440,1440,4200,2640,1536,1560,0,nil,nil,{"高级鬼魂术","高级防御","敏捷","高级法术抵抗"},nil,true,12381}
	elseif dr == -7981 then
		drs = {"幽萤娃娃头领","幽萤娃娃",105,2.6,1440,1440,4200,2640,1536,1560,1,nil,nil,{"高级鬼魂术","高级防御","敏捷","高级法术抵抗"},nil,true,12781}
	elseif dr == -7980 then
		drs = {"幽萤娃娃宝宝","幽萤娃娃",0,1,0,0,0,0,0,0,2,nil,nil,{"高级鬼魂术","高级防御","敏捷","高级法术抵抗"},nil,true,0}
	elseif dr == -7979 then
		drs = {"变异幽萤娃娃","幽萤娃娃",0,1,0,0,0,0,0,0,3,20113,{4,0},{"高级鬼魂术","高级防御","敏捷","高级法术抵抗"},nil,true,0}

	elseif dr == -7978 then
		drs = {"净瓶女娲","净瓶女娲",105,2.5,1464,1440,4800,2820,1560,1560,0,nil,nil,{"上古灵符","奔雷咒","高级慧根","感知"},nil,true,12381}
	elseif dr == -7977 then
		drs = {"净瓶女娲头领","净瓶女娲",105,2.6,1464,1440,4800,2820,1560,1560,1,nil,nil,{"上古灵符","奔雷咒","高级慧根","感知"},nil,true,12781}
	elseif dr == -7976 then
		drs = {"净瓶女娲宝宝","净瓶女娲",0,1,0,0,0,0,0,0,2,nil,nil,{"上古灵符","奔雷咒","高级慧根","感知"},nil,true,0}
	elseif dr == -7975 then
		drs = {"变异净瓶女娲","净瓶女娲",0,1,0,0,0,0,0,0,3,100,{1,0},{"上古灵符","奔雷咒","高级慧根","感知"},nil,true,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 95级
	elseif dr == -7974 then
		drs = {"吸血鬼","吸血鬼",95,2.5,1440,1320,3600,2400,1320,1800,0,nil,nil,{"偷袭","吸血","鬼魂术","驱鬼","弱点水"},nil,true,11381}
	elseif dr == -7973 then
		drs = {"吸血鬼头领","吸血鬼",95,2.6,1440,1320,3600,2400,1320,1800,1,nil,nil,{"偷袭","吸血","鬼魂术","驱鬼","弱点水"},nil,true,11781}
	elseif dr == -7972 then
		drs = {"吸血鬼宝宝","吸血鬼",0,1,0,0,0,0,0,0,2,nil,nil,{"偷袭","吸血","鬼魂术","驱鬼","弱点水"},nil,true,0}
	elseif dr == -7971 then
		drs = {"变异吸血鬼","吸血鬼",0,1,0,0,0,0,0,0,3,96,{1,0},{"偷袭","吸血","鬼魂术","驱鬼","弱点水"},nil,true,0}

	elseif dr == -7970 then
		drs = {"幽灵","幽灵",95,2.5,1476,1440,4200,2640,1320,1680,0,nil,nil,{"高级反击","高级飞行","死亡召唤","鬼魂术"},nil,true,11381}
	elseif dr == -7969 then
		drs = {"幽灵头领","幽灵",95,2.6,1476,1440,4200,2640,1320,1680,1,nil,nil,{"高级反击","高级飞行","死亡召唤","鬼魂术"},nil,true,11781}
	elseif dr == -7968 then
		drs = {"幽灵宝宝","幽灵",0,1,0,0,0,0,0,0,2,nil,nil,{"高级反击","高级飞行","死亡召唤","鬼魂术"},nil,true,0}
	elseif dr == -7967 then
		drs = {"变异幽灵","幽灵",0,1,0,0,0,0,0,0,3,97,{1,0},{"高级反击","高级飞行","死亡召唤","鬼魂术"},nil,true,0}

	elseif dr == -7966 then
		drs = {"律法女娲","律法女娲",95,2.5,1440,1560,4440,2400,1440,1680,0,nil,nil,{"善恶有报","敏捷","再生","高级反击"},nil,true,11381}
	elseif dr == -7965 then
		drs = {"律法女娲头领","律法女娲",95,2.6,1440,1560,4440,2400,1440,1680,1,nil,nil,{"善恶有报","敏捷","再生","高级反击"},nil,true,11781}
	elseif dr == -7964 then
		drs = {"律法女娲宝宝","律法女娲",0,1,0,0,0,0,0,0,2,nil,nil,{"善恶有报","敏捷","再生","高级反击"},nil,true,0}
	elseif dr == -7963 then
		drs = {"变异律法女娲","律法女娲",0,1,0,0,0,0,0,0,3,99,{1,0},{"善恶有报","敏捷","再生","高级反击"},nil,true,0}

	elseif dr == -7962 then
		drs = {"灵符女娲","灵符女娲",95,2.5,1440,1560,4440,2400,1440,1680,0,nil,nil,{"上古灵符","高级冥思","地狱烈火","落岩"},nil,true,11381}
	elseif dr == -7961 then
		drs = {"灵符女娲头领","灵符女娲",95,2.6,1440,1560,4440,2400,1440,1680,1,nil,nil,{"上古灵符","高级冥思","地狱烈火","落岩"},nil,true,11781}
	elseif dr == -7960 then
		drs = {"灵符女娲宝宝","灵符女娲",0,1,0,0,0,0,0,0,2,nil,nil,{"上古灵符","高级冥思","地狱烈火","落岩"},nil,true,0}
	elseif dr == -7959 then
		drs = {"变异灵符女娲","灵符女娲",0,1,0,0,0,0,0,0,3,98,{1,0},{"上古灵符","高级冥思","地狱烈火","落岩"},nil,true,0}

	elseif dr == -7958 then
		drs = {"阴阳伞","阴阳伞",95,2.5,1440,1440,4800,3000,1500,1440,0,nil,nil,{"上古灵符","驱鬼","剑荡四方","高级飞行"},nil,nil,11381}
	elseif dr == -7957 then
		drs = {"阴阳伞头领","阴阳伞",95,2.6,1440,1440,4800,3000,1500,1440,1,nil,nil,{"上古灵符","驱鬼","剑荡四方","高级飞行"},nil,nil,11781}
	elseif dr == -7956 then
		drs = {"阴阳伞宝宝","阴阳伞",0,1,0,0,0,0,0,0,2,nil,nil,{"上古灵符","驱鬼","剑荡四方","高级飞行"},nil,nil,0}
	elseif dr == -7955 then
		drs = {"变异阴阳伞","阴阳伞",0,1,0,0,0,0,0,0,3,2070,{1,0},{"上古灵符","驱鬼","剑荡四方","高级飞行"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 85级
	elseif dr == -7000 then
		drs = {"泪妖","泪妖",85,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"冥思","高级魔之心","法术暴击","弱点土","水攻"},nil,nil,10381}
	elseif dr == -6999 then
		drs = {"泪妖头领","泪妖",85,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"冥思","高级魔之心","法术暴击","弱点土","水攻"},nil,nil,10781}
	elseif dr == -6998 then
		drs = {"泪妖宝宝","泪妖",0,1,0,0,0,0,0,0,2,nil,nil,{"冥思","高级魔之心","法术暴击","弱点土","水攻"},nil,nil,0}
	elseif dr == -6997 then
		drs = {"变异泪妖","泪妖",0,1,0,0,0,0,0,0,3,20113,{6},{"冥思","高级魔之心","法术暴击","弱点土","水攻"},nil,nil,0}

	elseif dr == -6996 then
		drs = {"镜妖","镜妖",85,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"反震","高级驱鬼","鬼魂术","雷击"},nil,nil,10381}
	elseif dr == -6995 then
		drs = {"镜妖头领","镜妖",85,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"反震","高级驱鬼","鬼魂术","雷击"},nil,nil,10781}
	elseif dr == -6994 then
		drs = {"镜妖宝宝","镜妖",0,1,0,0,0,0,0,0,2,nil,nil,{"反震","高级驱鬼","鬼魂术","雷击"},nil,nil,0}
	elseif dr == -6993 then
		drs = {"变异镜妖","镜妖",0,1,0,0,0,0,0,0,3,2062,{1,0},{"反震","高级驱鬼","鬼魂术","雷击"},nil,nil,0}

	elseif dr == -6992 then
		drs = {"鼠先锋","鼠先锋",85,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"驱鬼","冥思","泰山压顶","敏捷"},nil,nil,10381}
	elseif dr == -6991 then
		drs = {"鼠先锋头领","鼠先锋",85,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"驱鬼","冥思","泰山压顶","敏捷"},nil,nil,10781}
	elseif dr == -6990 then
		drs = {"鼠先锋宝宝","鼠先锋",0,1,0,0,0,0,0,0,2,nil,nil,{"驱鬼","冥思","泰山压顶","敏捷"},nil,nil,0}
	elseif dr == -6989 then
		drs = {"变异鼠先锋","鼠先锋",0,1,0,0,0,0,0,0,3,109,{1,0},{"驱鬼","冥思","泰山压顶","敏捷"},nil,nil,0}

	elseif dr == -6988 then
		drs = {"百足将军","百足将军",85,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"毒","落岩","高级驱鬼","弱点水"},nil,nil,10381}
	elseif dr == -6987 then
		drs = {"百足将军头领","百足将军",85,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"毒","落岩","高级驱鬼","弱点水"},nil,nil,10781}
	elseif dr == -6986 then
		drs = {"百足将军宝宝","百足将军",0,1,0,0,0,0,0,0,2,nil,nil,{"毒","落岩","高级驱鬼","弱点水"},nil,nil,0}
	elseif dr == -6985 then
		drs = {"变异百足将军","百足将军",0,1,0,0,0,0,0,0,3,108,{1,1},{"毒","落岩","高级驱鬼","弱点水"},nil,nil,0}

	elseif dr == -6984 then
		drs = {"野猪精","野猪精",85,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"反击","感知","弱点水","高级强力"},nil,nil,10381}
	elseif dr == -6983 then
		drs = {"野猪精头领","野猪精",85,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"反击","感知","弱点水","高级强力"},nil,nil,10781}
	elseif dr == -6982 then
		drs = {"野猪精宝宝","野猪精",0,1,0,0,0,0,0,0,2,nil,nil,{"反击","感知","弱点水","高级强力"},nil,nil,0}
	elseif dr == -6981 then
		drs = {"变异野猪精","野猪精",0,1,0,0,0,0,0,0,3,107,{1,1},{"反击","感知","弱点水","高级强力"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 75级
	elseif dr == -3004 then
		drs = {"犀牛将军","犀牛将军人形",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,85650}
	elseif dr == -3003 then
		drs = {"犀牛将军头领","犀牛将军人形",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,90120}
	elseif dr == -3002 then
		drs = {"犀牛将军宝宝","犀牛将军人形",0,1,0,0,0,0,0,0,2,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,0}
	elseif dr == -3001 then
		drs = {"变异犀牛将军","犀牛将军人形",0,1,0,0,0,0,0,0,3,20104,{0,5},{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,0}

	elseif dr == -3000 then
		drs = {"犀牛将军","犀牛将军兽形",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,85650}
	elseif dr == -2999 then
		drs = {"犀牛将军头领","犀牛将军兽形",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,90120}
	elseif dr == -2998 then
		drs = {"犀牛将军宝宝","犀牛将军兽形",0,1,0,0,0,0,0,0,2,nil,nil,{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,0}
	elseif dr == -2997 then
		drs = {"变异犀牛将军","犀牛将军兽形",0,1,0,0,0,0,0,0,3,20104,{0,5},{"法术暴击","土属性吸收","法术波动","落岩"},nil,nil,0}

	elseif dr == -2996 then
		drs = {"锦毛貂精","锦毛貂精",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"冥思","泰山压顶","法术连击","敏捷"},nil,nil,85650}
	elseif dr == -2995 then
		drs = {"锦毛貂精头领","锦毛貂精",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"冥思","泰山压顶","法术连击","敏捷"},nil,nil,90120}
	elseif dr == -2994 then
		drs = {"锦毛貂精宝宝","锦毛貂精",0,1,0,0,0,0,0,0,2,nil,nil, {"冥思","泰山压顶","法术连击","敏捷"},nil,nil,0}
	elseif dr == -2993 then
		drs = {"变异锦毛貂精","锦毛貂精",0,1,0,0,0,0,0,0,3,20103,{0,3}, {"冥思","泰山压顶","法术连击","敏捷"},nil,nil,0}

	elseif dr == -2992 then
		drs = {"千年蛇魅","千年蛇魅",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"敏捷","毒","偷袭","高级吸血"},nil,nil,85650}
	elseif dr == -2991 then
		drs = {"千年蛇魅头领","千年蛇魅",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"敏捷","毒","偷袭","高级吸血"},nil,nil,90120}
	elseif dr == -2990 then
		drs = {"千年蛇魅宝宝","千年蛇魅",0,1,0,0,0,0,0,0,2,nil,nil,{"敏捷","毒","偷袭","高级吸血"},nil,nil,0}
	elseif dr == -2989 then
		drs = {"变异千年蛇魅","千年蛇魅",0,1,0,0,0,0,0,0,3,20104,{5,0},{"敏捷","毒","偷袭","高级吸血"},nil,nil,0}

	elseif dr == -2975 then
		drs = {"如意仙子","如意仙子",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"奔雷咒","地狱烈火","泰山压顶","烈火","雷击","弱点水"},nil,nil,85650}
	elseif dr == -2974 then
		drs = {"如意仙子头领","如意仙子",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"奔雷咒","地狱烈火","泰山压顶","烈火","雷击","弱点水"},nil,nil,90120}
	elseif dr == -2973 then
		drs = {"如意仙子宝宝","如意仙子",0,1,0,0,0,0,0,0,2,nil,nil,{"奔雷咒","地狱烈火","泰山压顶","烈火","雷击","弱点水"},nil,nil,0}
	elseif dr == -2972 then
		drs = {"变异如意仙子","如意仙子",0,1,0,0,0,0,0,0,3,59,{1,1},{"奔雷咒","地狱烈火","泰山压顶","烈火","雷击","弱点水"},nil,nil,0}

	elseif dr == -2971 then
		drs = {"芙蓉仙子","芙蓉仙子",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"高级再生","高级飞行","高级幸运"},nil,nil,85650}
	elseif dr == -2970 then
		drs = {"芙蓉仙子头领","芙蓉仙子",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"高级再生","高级飞行","高级幸运"},nil,nil,90120}
	elseif dr == -2969 then
		drs = {"芙蓉仙子宝宝","芙蓉仙子",0,1,0,0,0,0,0,0,2,nil,nil,{"高级再生","高级飞行","高级幸运"},nil,nil,0}
	elseif dr == -2968 then
		drs = {"变异芙蓉仙子","芙蓉仙子",0,1,0,0,0,0,0,0,3,55,{1,1},{"高级再生","高级飞行","高级幸运"},nil,nil,0}

	elseif dr == -2967 then
		drs = {"巡游天神","巡游天神",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"泰山压顶","地狱烈火","高级招架","高级必杀"},nil,nil,85650}
	elseif dr == -2966 then
		drs = {"巡游天神头领","巡游天神",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"泰山压顶","地狱烈火","高级招架","高级必杀"},nil,nil,90120}
	elseif dr == -2965 then
		drs = {"巡游天神宝宝","巡游天神",0,1,0,0,0,0,0,0,2,nil,nil,{"泰山压顶","地狱烈火","高级招架","高级必杀"},nil,nil,0}
	elseif dr == -2964 then
		drs = {"变异巡游天神","巡游天神",0,1,0,0,0,0,0,0,3,92,{1,1},{"泰山压顶","地狱烈火","高级招架","高级必杀"},nil,nil,0}

	elseif dr == -2963 then
		drs = {"星灵仙子","星灵仙子",75,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"雷击","奔雷咒","水漫金山","高级慧根"},nil,nil,85650}
	elseif dr == -2962 then
		drs = {"星灵仙子头领","星灵仙子",75,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"雷击","奔雷咒","水漫金山","高级慧根"},nil,nil,90120}
	elseif dr == -2961 then
		drs = {"星灵仙子宝宝","星灵仙子",0,1,0,0,0,0,0,0,2,nil,nil,{"雷击","奔雷咒","水漫金山","高级慧根"},nil,nil,0}
	elseif dr == -2960 then
		drs = {"变异星灵仙子","星灵仙子",0,1,0,0,0,0,0,0,3,88,{1,1},{"雷击","奔雷咒","水漫金山","高级慧根"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 65级
	elseif dr == -2988 then
		drs = {"蚌精","蚌精",65,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"水攻","慧根","高级水属性吸收","神迹","冥思"},nil,nil,85650}
	elseif dr == -2987 then
		drs = {"蚌精头领","蚌精",65,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"水攻","慧根","高级水属性吸收","神迹","冥思"},nil,nil,90120}
	elseif dr == -2986 then
		drs = {"蚌精宝宝","蚌精",0,1,0,0,0,0,0,0,2,nil,nil,{"水攻","慧根","高级水属性吸收","神迹","冥思"},nil,nil,0}
	elseif dr == -2985 then
		drs = {"变异蚌精","蚌精",0,1,0,0,0,0,0,0,3,20306,{1},{"水攻","慧根","高级水属性吸收","神迹","冥思"},nil,nil,0}

	elseif dr == -2984 then
		drs = {"碧水夜叉","碧水夜叉",65,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"高级反震","奔雷咒","强力","壁垒击破"},nil,nil,85650}
	elseif dr == -2983 then
		drs = {"碧水夜叉头领","碧水夜叉",65,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"高级反震","奔雷咒","强力","壁垒击破"},nil,nil,90120}
	elseif dr == -2982 then
		drs = {"碧水夜叉宝宝","碧水夜叉",0,1,0,0,0,0,0,0,2,nil,nil,{"高级反震","奔雷咒","强力","壁垒击破"},nil,nil,0}
	elseif dr == -2981 then
		drs = {"变异碧水夜叉","碧水夜叉",0,1,0,0,0,0,0,0,3,2059,{1},{"高级反震","奔雷咒","强力","壁垒击破"},nil,nil,0}

	elseif dr == -2980 then
		drs = {"鲛人","鲛人",65,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"连击","高级水属性吸收","移花接木","敏捷"},nil,nil,85650}
	elseif dr == -2979 then
		drs = {"鲛人头领","鲛人",65,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"连击","高级水属性吸收","移花接木","敏捷"},nil,nil,90120}
	elseif dr == -2978 then
		drs = {"鲛人宝宝","鲛人",0,1,0,0,0,0,0,0,2,nil,nil,{"连击","高级水属性吸收","移花接木","敏捷"},nil,nil,0}
	elseif dr == -2977 then
		drs = {"变异鲛人","鲛人",0,1,0,0,0,0,0,0,3,2057,{1,1},{"连击","高级水属性吸收","移花接木","敏捷"},nil,nil,0}

	elseif dr == -2959 then
		drs = {"凤凰","凤凰",65,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"地狱烈火","高级神佑复生","高级火属性吸收","飞行"},nil,true,85650}
	elseif dr == -2958 then
		drs = {"凤凰头领","凤凰",65,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"地狱烈火","高级神佑复生","高级火属性吸收","飞行"},nil,true,90120}
	elseif dr == -2957 then
		drs = {"凤凰宝宝","凤凰",0,1,0,0,0,0,0,0,2,nil,nil,{"地狱烈火","高级神佑复生","高级火属性吸收","飞行"},nil,true,0}
	elseif dr == -2956 then
		drs = {"变异凤凰","凤凰",0,1,0,0,0,0,0,0,3,91,{1,1},{"地狱烈火","高级神佑复生","高级火属性吸收","飞行"},nil,true,0}

	elseif dr == -2955 then
		drs = {"蛟龙","蛟龙",65,2.6,1300,1300,5000,2800,1300,1500,0,nil,nil,{"水漫金山","感知","高级永恒","高级水属性吸收"},nil,nil,85650}
	elseif dr == -2954 then
		drs = {"蛟龙头领","蛟龙",65,2.7,1300,1300,5000,2800,1300,1500,1,nil,nil,{"水漫金山","感知","高级永恒","高级水属性吸收"},nil,nil,90120}
	elseif dr == -2953 then
		drs = {"蛟龙宝宝","蛟龙",0,1,0,0,0,0,0,0,2,nil,nil,{"水漫金山","感知","高级永恒","高级水属性吸收"},nil,nil,0}
	elseif dr == -2952 then
		drs = {"变异蛟龙","蛟龙",0,1,0,0,0,0,0,0,3,90,{1,1},{"水漫金山","感知","高级永恒","高级水属性吸收"},nil,nil,0}

	elseif dr == -1500 then
		drs = {"雨师","雨师",65,2.6,1350,1550,5000,3400,1600,1800,0,nil,nil,{"水攻","烈火","高级雷属性吸收","高级水属性吸收","高级土属性吸收","高级火属性吸收"},nil,nil,85650}
	elseif dr == -1499 then
		drs = {"雨师头领","雨师",65,2.7,1350,1550,5000,3400,1600,1800,1,nil,nil,{"水攻","烈火","高级雷属性吸收","高级水属性吸收","高级土属性吸收","高级火属性吸收"},nil,nil,90120}
	elseif dr == -1498 then
		drs = {"雨师宝宝","雨师",0,1,0,0,0,0,0,0,2,nil,nil,{"水攻","烈火","高级雷属性吸收","高级水属性吸收","高级土属性吸收","高级火属性吸收"},nil,nil,0}
	elseif dr == -1497 then
		drs = {"变异雨师","雨师",0,1,0,0,0,0,0,0,3,114,{1,0},{"水攻","烈火","高级雷属性吸收","高级水属性吸收","高级土属性吸收","高级火属性吸收"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 55级
	elseif dr == -1496 then
		drs = {"天将","天将",55,2.6,1350,1550,5000,3400,1600,1800,0,nil,nil,{"高级强力","驱鬼","连击","幸运"},nil,nil,75650}
	elseif dr == -1495 then
		drs = {"天将头领","天将",55,2.7,1350,1550,5000,3400,1600,1800,1,nil,nil,{"高级强力","驱鬼","连击","幸运"},nil,nil,80120}
	elseif dr == -1494 then
		drs = {"天将宝宝","天将",0,1,0,0,0,0,0,0,2,nil,nil,{"高级强力","驱鬼","连击","幸运"},nil,nil,0}
	elseif dr == -1493 then
		drs = {"变异天将","天将",0,1,0,0,0,0,0,0,3,71,{1,0},{"高级强力","驱鬼","连击","幸运"},nil,nil,0}

	elseif dr == -1492 then
		drs = {"天兵","天兵",55,2.6,1350,1550,5000,3400,1600,1800,0,nil,nil,{"高级防御","高级感知","必杀","高级驱鬼"},nil,true,75650}
	elseif dr == -1491 then
		drs = {"天兵头领","天兵",55,2.7,1350,1550,5000,3400,1600,1800,1,nil,nil,{"高级防御","高级感知","必杀","高级驱鬼"},nil,true,80120}
	elseif dr == -1490 then
		drs = {"天兵宝宝","天兵",0,1,0,0,0,0,0,0,2,nil,nil,{"高级防御","高级感知","必杀","高级驱鬼"},nil,true,0}
	elseif dr == -1489 then
		drs = {"变异天兵","天兵",0,1,0,0,0,0,0,0,3,82,{1,0},{"高级防御","高级感知","必杀","高级驱鬼"},nil,true,0}

	elseif dr == -1488 then
		drs = {"地狱战神","地狱战神",55,2.6,1350,1550,5000,3400,1600,1800,0,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,75650}
	elseif dr == -1487 then
		drs = {"地狱战神头领","地狱战神",55,2.7,1350,1550,5000,3400,1600,1800,1,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,80120}
	elseif dr == -1486 then
		drs = {"地狱战神宝宝","地狱战神",0,1,0,0,0,0,0,0,2,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,0}
	elseif dr == -1485 then
		drs = {"变异地狱战神","地狱战神",0,1,0,0,0,0,0,0,3,94,{1,1},{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,0}

	elseif dr == -1484 then
		drs = {"风伯","风伯",55,2.6,1350,1550,5000,3400,1600,1800,0,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,75650}
	elseif dr == -1483 then
		drs = {"风伯头领","风伯",55,2.7,1350,1550,5000,3400,1600,1800,1,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,80120}
	elseif dr == -1482 then
		drs = {"风伯宝宝","风伯",0,1,0,0,0,0,0,0,2,nil,nil,{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,0}
	elseif dr == -1481 then
		drs = {"变异风伯","风伯",0,1,0,0,0,0,0,0,3,89,{1,0},{"泰山压顶","高级连击","高级魔之心","高级反震"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 45级
	elseif dr == -700 then
		drs = {"雷鸟人","雷鸟人",45,2.6,1350,1550,4800,3100,1500,1500,0,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,true,48520}
	elseif dr == -699 then
		drs = {"雷鸟人头领","雷鸟人",45,2.7,1350,1550,4800,3100,1500,1500,1,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,true,55650}
	elseif dr == -698 then
		drs = {"雷鸟人宝宝","雷鸟人",0,1,0,0,0,0,0,0,2,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,true,0}
	elseif dr == -697 then
		drs = {"变异雷鸟人","雷鸟人",0,1,0,0,0,0,0,0,3,62,{1,0},{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,true,0}

	elseif dr == -696 then
		drs = {"蝴蝶仙子","蝴蝶仙子",45,2.6,1350,1550,4800,3100,1500,1500,0,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,nil,48520}
	elseif dr == -695 then
		drs = {"蝴蝶仙子头领","蝴蝶仙子",45,2.7,1350,1550,4800,3100,1500,1500,1,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,nil,55650}
	elseif dr == -694 then
		drs = {"蝴蝶仙子宝宝","蝴蝶仙子",0,1,0,0,0,0,0,0,2,nil,nil,{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,nil,0}
	elseif dr == -693 then
		drs = {"变异蝴蝶仙子","蝴蝶仙子",0,1,0,0,0,0,0,0,3,57,{1,0},{"高级雷属性吸收","奔雷咒","飞行","弱点土","雷击"},nil,nil,0}

	elseif dr == -692 then
		drs = {"古代瑞兽","古代瑞兽",45,2.6,1350,1550,4800,3100,1500,1500,0,nil,nil,{"高级神迹","高级反震","泰山压顶","高级驱鬼"},nil,nil,48520}
	elseif dr == -691 then
		drs = {"古代瑞兽头领","古代瑞兽",45,2.7,1350,1550,4800,3100,1500,1500,1,nil,nil,{"高级神迹","高级反震","泰山压顶","高级驱鬼"},nil,nil,55650}
	elseif dr == -690 then
		drs = {"古代瑞兽宝宝","古代瑞兽",0,1,0,0,0,0,0,0,2,nil,nil,{"高级神迹","高级反震","泰山压顶","高级驱鬼"},nil,nil,0}
	elseif dr == -689 then
		drs = {"变异古代瑞兽","古代瑞兽",0,1,0,0,0,0,0,0,3,73,{1,1},{"高级神迹","高级反震","泰山压顶","高级驱鬼"},nil,nil,0}

	elseif dr == -688 then
		drs = {"白熊","白熊",45,2.6,1350,1550,4800,3100,1500,1500,0,nil,nil,{"迟钝","强力","防御","高级反击","高级必杀"},nil,nil,48520}
	elseif dr == -687 then
		drs = {"白熊头领","白熊",45,2.7,1350,1550,4800,3100,1500,1500,1,nil,nil,{"迟钝","强力","防御","高级反击","高级必杀"},nil,nil,55650}
	elseif dr == -686 then
		drs = {"白熊宝宝","白熊",0,1,0,0,0,0,0,0,2,nil,nil,{"迟钝","强力","防御","高级反击","高级必杀"},nil,nil,0}
	elseif dr == -685 then
		drs = {"变异白熊","白熊",0,1,0,0,0,0,0,0,3,72,{1,1},{"迟钝","强力","防御","高级反击","高级必杀"},nil,nil,0}

	elseif dr == -684 then
		drs = {"黑山老妖","黑山老妖",45,2.6,1350,1550,4800,3100,1500,1500,0,nil,nil,{"高级偷袭","高级吸血","高级精神集中"},nil,nil,48520}
	elseif dr == -683 then
		drs = {"黑山老妖头领","黑山老妖",45,2.7,1350,1550,4800,3100,1500,1500,1,nil,nil,{"高级偷袭","高级吸血","高级精神集中"},nil,nil,55650}
	elseif dr == -682 then
		drs = {"黑山老妖宝宝","黑山老妖",0,1,0,0,0,0,0,0,2,nil,nil,{"高级偷袭","高级吸血","高级精神集中"},nil,nil,0}
	elseif dr == -681 then
		drs = {"变异黑山老妖","黑山老妖",0,1,0,0,0,0,0,0,3,93,{1,1},{"高级偷袭","高级吸血","高级精神集中"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 35级
	elseif dr == -600 then
		drs = {"龟丞相","龟丞相",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"冥思","驱鬼","防御","水漫金山","水属性吸收","水攻"},nil,nil,22520}
	elseif dr == -599 then
		drs = {"龟丞相头领","龟丞相",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"冥思","驱鬼","防御","水漫金山","水属性吸收","水攻"},nil,nil,23050}
	elseif dr == -598 then
		drs = {"龟丞相宝宝","龟丞相",0,1,0,0,0,0,0,0,2,nil,nil,{"冥思","驱鬼","防御","水漫金山","水属性吸收","水攻"},nil,nil,0}
	elseif dr == -597 then
		drs = {"变异龟丞相","龟丞相",0,1,0,0,0,0,0,0,3,85,{1,0},{"冥思","驱鬼","防御","水漫金山","水属性吸收","水攻"},nil,nil,0}

	elseif dr == -596 then
		drs = {"兔子怪","兔子怪",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"高级感知","高级冥思","高级驱鬼","高级幸运","高级永恒","高级敏捷","弱点土"},nil,nil,22520}
	elseif dr == -595 then
		drs = {"兔子怪头领","兔子怪",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"高级感知","高级冥思","高级驱鬼","高级幸运","高级永恒","高级敏捷","弱点土"},nil,nil,23050}
	elseif dr == -594 then
		drs = {"兔子怪宝宝","兔子怪",0,1,0,0,0,0,0,0,2,nil,nil,{"高级感知","高级冥思","高级驱鬼","高级幸运","高级永恒","高级敏捷","弱点土"},nil,nil,0}
	elseif dr == -593 then
		drs = {"变异兔子怪","兔子怪",0,1,0,0,0,0,0,0,3,51,{1,0},{"高级感知","高级冥思","高级驱鬼","高级幸运","高级永恒","高级敏捷","弱点土"},nil,nil,0}

	elseif dr == -592 then
		drs = {"蜘蛛精","蜘蛛精",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"吸血","高级感知","高级毒","弱点土"},nil,nil,22520}
	elseif dr == -591 then
		drs = {"蜘蛛精头领","蜘蛛精",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"吸血","高级感知","高级毒","弱点土"},nil,nil,23050}
	elseif dr == -590 then
		drs = {"蜘蛛精宝宝","蜘蛛精",0,1,0,0,0,0,0,0,2,nil,nil,{"吸血","高级感知","高级毒","弱点土"},nil,nil,0}
	elseif dr == -589 then
		drs = {"变异蜘蛛精","蜘蛛精",0,1,0,0,0,0,0,0,3,80,{1,0},{"吸血","高级感知","高级毒","弱点土"},nil,nil,0}

	elseif dr == -588 then
		drs = {"黑熊精","黑熊精",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"反震","必杀","幸运","高级强力","弱点雷"},nil,nil,22520}
	elseif dr == -587 then
		drs = {"黑熊精头领","黑熊精",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"反震","必杀","幸运","高级强力","弱点雷"},nil,nil,23050}
	elseif dr == -586 then
		drs = {"黑熊精宝宝","黑熊精",0,1,0,0,0,0,0,0,2,nil,nil,{"反震","必杀","幸运","高级强力","弱点雷"},nil,nil,0}
	elseif dr == -585 then
		drs = {"变异黑熊精","黑熊精",0,1,0,0,0,0,0,0,3,53,{1,0},{"反震","必杀","幸运","高级强力","弱点雷"},nil,nil,0}

	elseif dr == -584 then
		drs = {"僵尸","僵尸",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"土属性吸收","弱点雷","防御","鬼魂术","驱鬼"},nil,nil,22520}
	elseif dr == -583 then
		drs = {"僵尸头领","僵尸",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"土属性吸收","弱点雷","防御","鬼魂术","驱鬼"},nil,nil,23050}
	elseif dr == -582 then
		drs = {"僵尸宝宝","僵尸",0,1,0,0,0,0,0,0,2,nil,nil,{"土属性吸收","弱点雷","防御","鬼魂术","驱鬼"},nil,nil,0}
	elseif dr == -581 then
		drs = {"变异僵尸","僵尸",0,1,0,0,0,0,0,0,3,79,{1,0},{"土属性吸收","弱点雷","防御","鬼魂术","驱鬼"},nil,nil,0}

	elseif dr == -580 then
		drs = {"牛头","牛头",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"驱鬼","高级必杀","招架","高级鬼魂术","驱鬼"},nil,nil,22520}
	elseif dr == -579 then
		drs = {"牛头头领","牛头",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"驱鬼","高级必杀","招架","高级鬼魂术","驱鬼"},nil,nil,23050}
	elseif dr == -578 then
		drs = {"牛头宝宝","牛头",0,1,0,0,0,0,0,0,2,nil,nil,{"驱鬼","高级必杀","招架","高级鬼魂术","驱鬼"},nil,nil,0}
	elseif dr == -577 then
		drs = {"变异牛头","牛头",0,1,0,0,0,0,0,0,3,77,{1,0},{"驱鬼","高级必杀","招架","高级鬼魂术","驱鬼"},nil,nil,0}

	elseif dr == -576 then
		drs = {"马面","马面",35,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"驱鬼","高级必杀","强力","高级鬼魂术","驱鬼"},nil,nil,22520}
	elseif dr == -575 then
		drs = {"马面头领","马面",35,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"驱鬼","高级必杀","强力","高级鬼魂术","驱鬼"},nil,nil,23050}
	elseif dr == -574 then
		drs = {"马面宝宝","马面",0,1,0,0,0,0,0,0,2,nil,nil,{"驱鬼","高级必杀","强力","高级鬼魂术","驱鬼"},nil,nil,0}
	elseif dr == -573 then
		drs = {"变异马面","马面",0,1,0,0,0,0,0,0,3,78,{1,0},{"驱鬼","高级必杀","强力","高级鬼魂术","驱鬼"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回#################### 25级
	elseif dr == -572 then
		drs = {"牛妖","牛妖",25,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"高级反击","高级慧根","高级防御","烈火"},nil,nil,22520}
	elseif dr == -571 then
		drs = {"牛妖头领","牛妖",25,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"高级反击","高级慧根","高级防御","烈火"},nil,nil,23050}
	elseif dr == -570 then
		drs = {"牛妖宝宝","牛妖",0,1,0,0,0,0,0,0,2,nil,nil,{"高级反击","高级慧根","高级防御","烈火"},nil,nil,0}
	elseif dr == -569 then
		drs = {"变异牛妖","牛妖",0,1,0,0,0,0,0,0,3,87,{1,0},{"高级反击","高级慧根","高级防御","烈火"},nil,nil,0}

	elseif dr == -568 then
		drs = {"小龙女","小龙女",25,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"神佑复生","高级驱鬼","慧根","水攻","高级水属性吸收"},nil,nil,22520}
	elseif dr == -567 then
		drs = {"小龙女头领","小龙女",25,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"神佑复生","高级驱鬼","慧根","水攻","高级水属性吸收"},nil,nil,23050}
	elseif dr == -566 then
		drs = {"小龙女宝宝","小龙女",0,1,0,0,0,0,0,0,2,nil,nil,{"神佑复生","高级驱鬼","慧根","水攻","高级水属性吸收"},nil,nil,0}
	elseif dr == -565 then
		drs = {"变异小龙女","小龙女",0,1,0,0,0,0,0,0,3,63,{1,0},{"神佑复生","高级驱鬼","慧根","水攻","高级水属性吸收"},nil,nil,0}

	elseif dr == -564 then
		drs = {"野鬼","野鬼",25,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"落岩","弱点雷","鬼魂术","驱鬼"},nil,nil,22520}
	elseif dr == -563 then
		drs = {"野鬼头领","野鬼",25,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"落岩","弱点雷","鬼魂术","驱鬼"},nil,nil,23050}
	elseif dr == -562 then
		drs = {"野鬼宝宝","野鬼",0,1,0,0,0,0,0,0,2,nil,nil,{"落岩","弱点雷","鬼魂术","驱鬼"},nil,nil,0}
	elseif dr == -561 then
		drs = {"变异野鬼","野鬼",0,1,0,0,0,0,0,0,3,80,{1,0},{"落岩","弱点雷","鬼魂术","驱鬼"},nil,nil,0}

	elseif dr == -560 then
		drs = {"狼","狼",25,2.6,1300,1500,5000,2300,1200,1200,0,nil,nil,{"高级连击","驱鬼","驱鬼","连击","偷袭"},nil,nil,22520}
	elseif dr == -559 then
		drs = {"狼头领","狼",25,2.7,1350,1550,5000,2300,1200,1200,1,nil,nil,{"高级连击","驱鬼","驱鬼","连击","偷袭"},nil,nil,23050}
	elseif dr == -558 then
		drs = {"狼宝宝","狼",0,1,0,0,0,0,0,0,2,nil,nil,{"高级连击","驱鬼","驱鬼","连击","偷袭"},nil,nil,0}
	elseif dr == -557 then
		drs = {"变异狼","狼",0,1,0,0,0,0,0,0,3,68,{1,0},{"高级连击","驱鬼","驱鬼","连击","偷袭"},nil,nil,0}

	elseif dr == -556 then
		drs = {"虾兵","虾兵",25,2,1300,1500,5000,2120,1000,1000,0,nil,nil,{"高级反击","高级必杀","驱鬼","水属性吸收"},nil,nil,12520}
	elseif dr == -555 then
		drs = {"虾兵头领","虾兵",25,2.2,1300,1500,5000,2120,1000,1000,1,nil,nil,{"高级反击","高级必杀","驱鬼","水属性吸收"},nil,nil,12550}
	elseif dr == -554 then
		drs = {"虾兵宝宝","虾兵",0,1,0,0,0,0,0,0,2,nil,nil,{"高级反击","高级必杀","驱鬼","水属性吸收"},nil,nil,0}
	elseif dr == -553 then
		drs = {"变异虾兵","虾兵",0,1,0,0,0,0,0,0,3,83,{1,0},{"高级反击","高级必杀","驱鬼","水属性吸收"},nil,nil,0}

	elseif dr == -552 then
		drs = {"蟹将","蟹将",25,2,1300,1500,5000,2120,1000,1000,0,nil,nil,{"高级连击","精神集中","招架","水属性吸收"},nil,nil,12520}
	elseif dr == -551 then
		drs = {"蟹将头领","蟹将",25,2.2,1300,1500,5000,2120,1000,1000,1,nil,nil,{"高级连击","精神集中","招架","水属性吸收"},nil,nil,12550}
	elseif dr == -550 then
		drs = {"蟹将宝宝","蟹将",0,1,0,0,0,0,0,0,2,nil,nil,{"高级连击","精神集中","招架","水属性吸收"},nil,nil,0}
	elseif dr == -549 then
		drs = {"变异蟹将","蟹将",0,1,0,0,0,0,0,0,3,84,{1,0},{"高级连击","精神集中","招架","水属性吸收"},nil,nil,0}

	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回####################  15级
	elseif dr == -548 then
		drs = {"骷髅怪","骷髅怪",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"土属性吸收","弱点雷","鬼魂术","驱鬼"},nil,nil,3205}
	elseif dr == -547 then
		drs = {"骷髅怪头领","骷髅怪",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"土属性吸收","弱点雷","鬼魂术","驱鬼"},nil,nil,3500}
	elseif dr == -546 then
		drs = {"骷髅怪宝宝","骷髅怪",0,1,0,0,0,0,0,0,2,nil,nil,{"土属性吸收","弱点雷","鬼魂术","驱鬼"},nil,nil,0}
	elseif dr == -545 then
		drs = {"变异骷髅怪","骷髅怪",0,1,0,0,0,0,0,0,3,60,{1,0},{"土属性吸收","弱点雷","鬼魂术","驱鬼"},nil,nil,0}

	elseif dr == -544 then
		drs = {"羊头怪","羊头怪",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"连击","必杀","幸运","永恒"},nil,nil,3205}
	elseif dr == -543 then
		drs = {"羊头怪头领","羊头怪",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"连击","必杀","幸运","永恒"},nil,nil,3500}
	elseif dr == -542 then
		drs = {"羊头怪宝宝","羊头怪",0,1,0,0,0,0,0,0,2,nil,nil,{"连击","必杀","幸运","永恒"},nil,nil,0}
	elseif dr == -541 then
		drs = {"变异羊头怪","羊头怪",0,1,0,0,0,0,0,0,3,54,{1,0},{"连击","必杀","幸运","永恒"},nil,nil,0}

	elseif dr == -540 then
		drs = {"蛤蟆精","蛤蟆精",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"毒","必杀"},nil,nil,3205}
	elseif dr == -539 then
		drs = {"蛤蟆精头领","蛤蟆精",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"毒","必杀"},nil,nil,3500}
	elseif dr == -538 then
		drs = {"蛤蟆精宝宝","蛤蟆精",0,1,0,0,0,0,0,0,2,nil,nil,{"毒","必杀"},nil,nil,0}
	elseif dr == -537 then
		drs = {"变异蛤蟆精","蛤蟆精",0,1,0,0,0,0,0,0,3,61,{1,0},{"毒","必杀"},nil,nil,0}

	elseif dr == -536 then
		drs = {"狐狸精","狐狸精",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"驱鬼","高级感知","慧根","高级慧根","弱点雷"},nil,nil,3205}
	elseif dr == -535 then
		drs = {"狐狸精头领","狐狸精",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"驱鬼","高级感知","慧根","高级慧根","弱点雷"},nil,nil,3500}
	elseif dr == -534 then
		drs = {"狐狸精宝宝","狐狸精",0,1,0,0,0,0,0,0,2,nil,nil,{"驱鬼","高级感知","慧根","高级慧根","弱点雷"},nil,nil,0}
	elseif dr == -533 then
		drs = {"变异狐狸精","狐狸精",0,1,0,0,0,0,0,0,3,81,{1,0},{"驱鬼","高级感知","慧根","高级慧根","弱点雷"},nil,nil,0}

	elseif dr == -532 then
		drs = {"老虎","老虎",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"连击","驱鬼","幸运","强力"},nil,nil,3205}
	elseif dr == -531 then
		drs = {"老虎头领","老虎",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"连击","驱鬼","幸运","强力"},nil,nil,3500}
	elseif dr == -530 then
		drs = {"老虎宝宝","老虎",0,1,0,0,0,0,0,0,2,nil,nil,{"连击","驱鬼","幸运","强力"},nil,nil,0}
	elseif dr == -529 then
		drs = {"变异老虎","老虎",0,1,0,0,0,0,0,0,3,64,{1,0},{"连击","驱鬼","幸运","强力"},nil,nil,0}

	elseif dr == -528 then
		drs = {"黑熊","黑熊",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"反击","必杀","强力","防御","迟钝"},nil,nil,3205}
	elseif dr == -527 then
		drs = {"黑熊头领","黑熊",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"反击","必杀","强力","防御","迟钝"},nil,nil,3500}
	elseif dr == -526 then
		drs = {"黑熊宝宝","黑熊",0,1,0,0,0,0,0,0,2,nil,nil,{"反击","必杀","强力","防御","迟钝"},nil,nil,0}
	elseif dr == -525 then
		drs = {"变异黑熊","黑熊",0,1,0,0,0,0,0,0,3,65,{1,0},{"反击","必杀","强力","防御","迟钝"},nil,nil,0}

	elseif dr == -524 then
		drs = {"花妖","花妖",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"感知","慧根","落岩","防御","水属性吸收"},nil,nil,3205}
	elseif dr == -523 then
		drs = {"花妖头领","花妖",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"感知","慧根","落岩","防御","水属性吸收"},nil,nil,3500}
	elseif dr == -522 then
		drs = {"花妖宝宝","花妖",0,1,0,0,0,0,0,0,2,nil,nil,{"感知","慧根","落岩","防御","水属性吸收"},nil,nil,0}
	elseif dr == -521 then
		drs = {"变异花妖","花妖",0,1,0,0,0,0,0,0,3,58,{1,0},{"感知","慧根","落岩","防御","水属性吸收"},nil,nil,0}

	elseif dr == -520 then
		drs = {"赌徒","赌徒",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"反击","偷袭"},nil,nil,3205}
	elseif dr == -519 then
		drs = {"赌徒头领","赌徒",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"反击","偷袭"},nil,nil,3500}
	elseif dr == -518 then
		drs = {"赌徒宝宝","赌徒",0,1,0,0,0,0,0,0,2,nil,nil,{"反击","偷袭"},nil,nil,0}
	elseif dr == -517 then
		drs = {"变异赌徒","赌徒",0,1,0,0,0,0,0,0,3,76,{1,0},{"反击","偷袭"},nil,nil,0}

	elseif dr == -516 then
		drs = {"强盗","强盗",15,2,1300,1500,4000,1800,1000,1000,0,nil,nil,{"连击","烈火","强力","否定信仰"},nil,nil,3205}
	elseif dr == -515 then
		drs = {"强盗头领","强盗",15,2.2,1300,1500,4000,1800,1000,1000,1,nil,nil,{"连击","烈火","强力","否定信仰"},nil,nil,3500}
	elseif dr == -514 then
		drs = {"强盗宝宝","强盗",0,1,0,0,0,0,0,0,2,nil,nil,{"连击","烈火","强力","否定信仰"},nil,nil,0}
	elseif dr == -513 then
		drs = {"变异强盗","强盗",0,1,0,0,0,0,0,0,3,74,{1,0},{"连击","烈火","强力","否定信仰"},nil,nil,0}
	--▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回▆▇██■▓回回▆▇██■▓回▆▇██■▓回▆▇██■▓回####################  5级
	elseif dr == -512 then
		drs = {"海毛虫","海毛虫",5,2,1200,1200,3500,1400,900,900,0,nil,nil,{"毒","高级反震","必杀","驱鬼","弱点火"},nil,nil,3205}
	elseif dr == -511 then
		drs = {"海毛虫头领","海毛虫",6,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"毒","高级反震","必杀","驱鬼","弱点火"},nil,nil,3500}
	elseif dr == -510 then
		drs = {"海毛虫宝宝","海毛虫",0,1,0,0,0,0,0,0,2,nil,nil,{"毒","高级反震","必杀","驱鬼","弱点火"},nil,nil,0}
	elseif dr == -509 then
		drs = {"变异海毛虫","海毛虫",0,1,0,0,0,0,0,0,3,67,{1,0},{"毒","高级反震","必杀","驱鬼","弱点火"},nil,nil,0}

	elseif dr == -508 then
		drs = {"大蝙蝠","大蝙蝠",5,2,1200,1200,3500,1400,900,900,0,nil,nil,{"吸血","高级感知","高级驱鬼","飞行","高级驱鬼","弱点水"},nil,nil,3205}
	elseif dr == -507 then
		drs = {"大蝙蝠头领","大蝙蝠",5,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"吸血","高级感知","高级驱鬼","飞行","高级驱鬼","弱点水"},nil,nil,3500}
	elseif dr == -506 then
		drs = {"大蝙蝠宝宝","大蝙蝠",0,1,0,0,0,0,0,0,2,nil,nil,{"吸血","高级感知","高级驱鬼","飞行","高级驱鬼","弱点水"},nil,nil,0}
	elseif dr == -505 then
		drs = {"变异大蝙蝠","大蝙蝠",0,1,0,0,0,0,0,0,3,66,{1,0},{"吸血","高级感知","高级驱鬼","飞行","高级驱鬼","弱点水"},nil,nil,0}

	elseif dr == -504 then
		drs = {"山贼","山贼",5,2,1200,1200,3500,1400,900,900,0,nil,nil,{"招架","偷袭","强力","高级否定信仰"},nil,nil,3205}
	elseif dr == -503 then
		drs = {"山贼头领","山贼",6,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"招架","偷袭","强力","高级否定信仰"},nil,nil,3500}
	elseif dr == -502 then
		drs = {"山贼宝宝","山贼",0,1,0,0,0,0,0,0,2,nil,nil,{"招架","偷袭","强力","高级否定信仰"},nil,nil,0}
	elseif dr == -501 then
		drs = {"变异山贼","山贼",0,1,0,0,0,0,0,0,3,75,{1,0},{"招架","偷袭","强力","高级否定信仰"},nil,nil,0}

	elseif dr == -500 then
		drs = {"野猪","野猪",6,2,1200,1200,3500,1400,900,900,0,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,3205}
	elseif dr == -499 then
		drs = {"野猪头领","野猪",7,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,3500}
	elseif dr == -498 then
		drs = {"野猪宝宝","野猪",0,1,0,0,0,0,0,0,2,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,0}
	elseif dr == -497 then
		drs = {"变异野猪","野猪",0,1,0,0,0,0,0,0,3,52,{1,0},{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,0}

	elseif dr == -496 then
		drs = {"狸","浣熊",6,2,1200,1200,3500,1400,900,900,0,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,3205}
	elseif dr == -495 then
		drs = {"狸头领","浣熊",7,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,3500}
	elseif dr == -494 then
		drs = {"狸宝宝","浣熊",0,1,0,0,0,0,0,0,2,nil,nil,{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,0}
	elseif dr == -493 then
		drs = {"变异狸","浣熊",0,1,0,0,0,0,0,0,3,2079,{5},{"感知","高级感知","高级幸运","强力","弱点土","弱点火"},nil,nil,0}

	elseif dr == -492 then
		drs = {"巨蛙","巨蛙",5,2,1200,1200,3500,1400,900,900,0,nil,nil,{"慧根","幸运","水攻","弱点火"},nil,nil,3205}
	elseif dr == -491 then
		drs = {"巨蛙头领","巨蛙",6,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"慧根","幸运","水攻","弱点火"},nil,nil,3500}
	elseif dr == -490 then
		drs = {"巨蛙宝宝","巨蛙",0,1,0,0,0,0,0,0,2,nil,nil,{"慧根","幸运","水攻","弱点火"},nil,nil,0}
	elseif dr == -489 then
		drs = {"变异巨蛙","巨蛙",0,1,0,0,0,0,0,0,3,70,{1,0},{"慧根","幸运","水攻","弱点火"},nil,nil,0}

	elseif dr == -488 then
		drs = {"大海龟","大海龟",0,2,1200,1200,3500,1400,900,900,0,nil,nil,{"反震","慧根","幸运","水属性吸收","防御"},nil,nil,3205}
	elseif dr == -487 then
		drs = {"大海龟头领","大海龟",1,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"反震","慧根","幸运","水属性吸收","防御"},nil,nil,3500}
	elseif dr == -486 then
		drs = {"大海龟宝宝","大海龟",0,1,0,0,0,0,0,0,2,nil,nil,{"反震","慧根","幸运","水属性吸收","防御"},nil,nil,0}
	elseif dr == -485 then
		drs = {"变异大海龟","大海龟",0,1,0,0,0,0,0,0,3,69,{1,0},{"反震","慧根","幸运","水属性吸收","防御"},nil,nil,0}

	elseif dr == -484 then
		drs = {"护卫","护卫",5,2,1200,1200,3500,1400,900,900,0,nil,nil,{"反击","必杀","强力"},nil,nil,3205}
	elseif dr == -483 then
		drs = {"护卫头领","护卫",6,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"反击","必杀","强力"},nil,nil,3500}
	elseif dr == -482 then
		drs = {"护卫宝宝","护卫",0,1,0,0,0,0,0,0,2,nil,nil,{"反击","必杀","强力"},nil,nil,0}
	elseif dr == -481 then
		drs = {"变异护卫","护卫",0,1,0,0,0,0,0,0,3,2051,{1,0},{"反击","必杀","强力"},nil,nil,0}

	elseif dr == -480 then
		drs = {"树怪","树怪",6,2,1200,1200,3500,1400,900,900,0,nil,nil,{"反击","感知","驱鬼","再生","烈火","弱点火","迟钝"},nil,nil,3205}
	elseif dr == -479 then
		drs = {"树怪头领","树怪",7,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"反击","感知","驱鬼","再生","烈火","弱点火","迟钝"},nil,nil,3500}
	elseif dr == -478 then
		drs = {"树怪宝宝","树怪",0,1,0,0,0,0,0,0,2,nil,nil,{"反击","感知","驱鬼","再生","烈火","弱点火","迟钝"},nil,nil,0}
	elseif dr == -477 then
		drs = {"变异树怪","树怪",0,1,0,0,0,0,0,0,3,56,{1,0},{"反击","感知","驱鬼","再生","烈火","弱点火","迟钝"},nil,nil,0}

	elseif dr == -476 then
		drs = {"章鱼","章鱼",2,2,1200,1200,3500,1400,900,900,0,nil,nil,{"连击","毒","吸血","水属性吸收","弱点火"},nil,nil,3205}
	elseif dr == -475 then
		drs = {"章鱼头领","章鱼",3,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"连击","毒","吸血","水属性吸收","弱点火"},nil,nil,3500}
	elseif dr == -474 then
		drs = {"章鱼宝宝","章鱼",0,1,0,0,0,0,0,0,2,nil,nil,{"连击","毒","吸血","水属性吸收","弱点火"},nil,nil,0}
	elseif dr == -473 then
		drs = {"变异章鱼","章鱼",0,1,0,0,0,0,0,0,3,119,{2},{"连击","毒","吸血","水属性吸收","弱点火"},nil,nil,0}

	elseif dr == -472 then
		drs = {"海星","海星",0,2,1200,1200,3500,1400,900,900,0,nil,nil,{"水属性吸收","弱点火","慧根","高级反震","水攻"},nil,nil,3205}
	elseif dr == -471 then
		drs = {"海星头领","海星",1,2.2,1200,1200,3500,1400,900,900,1,nil,nil,{"水属性吸收","弱点火","慧根","高级反震","水攻"},nil,nil,3500}
	elseif dr == -470 then
		drs = {"海星宝宝","海星",0,1,0,0,0,0,0,0,2,nil,nil,{"水属性吸收","弱点火","慧根","高级反震","水攻"},nil,nil,0}
	elseif dr == -469 then
		drs = {"变异海星","海星",0,1,0,0,0,0,0,0,3,119,{0},{"水属性吸收","弱点火","慧根","高级反震","水攻"},nil,nil,0}

	elseif dr == 9999 then
		drs = {"泡泡宝宝","泡泡",0,1,0,0,0,0,0,0,2,nil,nil,{"高级防御","高级幸运","连击","精神集中","再生"},nil,nil,0}
	elseif dr == 9998 then
		drs = {"变异泡泡","泡泡",0,1,0,0,0,0,0,0,3,119,{0,0},{"高级防御","高级幸运","连击","精神集中","再生"},nil,nil,0}

	--####################################################### 级
	elseif dr == -99999 then
		drs = {"鬼将","鬼将",1,1,1,0,4800,0,0,0,3,95,{1,0},{},true,nil,-1,{1,1,1,1,1,1}}
	elseif dr == -999999 then
		drs = {"鬼将","鬼将",1,1,1,0,4800,0,0,0,3,95,{1,0},{},true,nil,-1,{1,1,1,1,1,1}}
  --   elseif dr == -99999118 then
		-- drs = {"蚩尤","蚩尤",15,1.2,1300,1500,6000,1800,1000,1000,1,nil,nil,{"土属性吸收","弱点雷","鬼魂术","驱鬼"},nil,nil,3600}
	elseif dr == -9999988 then--长安布公子
		local 属性表 = {}
		属性表[1] =5000-- 体力/气血
		属性表[2] =800--防御
		属性表[3] =2300-- 攻击
		属性表[4] = 170--等级
		属性表[5] =700-- 速度
		属性表[6] =1500-- 法力/灵力
		drs = {"布公子","逍遥生",175,4,1700,1700,5560,2640,1700,1700,-1,1,{6,1,6},{"横扫千军","高级反震","高级防御","高级神迹","弱点击破","破血狂攻"},nil,"星瀚",14281,属性表,2,"大唐官府",185}

--======================================================================
	end
	--怪物排序是 普通 头领 宝宝 变异宝宝    3 2 1 0
	-- 关于武器and饰品的解析；如果值为布尔型则表示开启饰品，如果值为字符串则开启武器，空值跳过
	-- 关于种类的详细，-1为不可捉对象，0为普通野怪，1为头领(最终等级会比普通野怪高出1级)，2为宝宝，3为变异
	-- 1为名称，2为模型，3为等级，4为成长，5为攻击资质，6为防御资质，7为体力资质，8为法力资质，9为速度资质，10为躲闪资质，11为种类，12为染色方案(可空),13为染色ID(可空),14为技能组，15为是否开启必带技能，16为武器逻辑,17为经验,18为默认分配潜能，19为开启智能(不开启也就是进行傻逼般的行为),20为门派，21为技能等级(留空默认为人物等级)

	return drs
end