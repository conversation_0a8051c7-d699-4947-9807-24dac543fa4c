--[[
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2025-07-16 20:23:10
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2025-07-16 23:41:12
FilePath: \mh\服务端源码\Script\服务网络\PackServer.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]
local ffi = require("ffi")
local __hpserver = require("__ggehpserver__")
local PackServer = class(require"Script/服务网络/TcpServer")
PackServer._mode = 'pack'
PackServer._new  = require("luahp.server")
function PackServer:初始化(t,网向)
    self._hp = self._new(__gge.cs,__gge.state)
    self._hp:Create_TcpPackServer(self)
    self._title         = t or ''
    self._网向          = 网向 or ''
    -- __gge.print(false,15,"-------------------------------------------------------------------------\n")
    -- __gge.print(true,7,string.format("创建%s-->", self._title))
    -- __gge.print(false,10,"【成功】！\n")
    -- __gge.print(false,15,"-------------------------------------------------------------------------\n")
    local Flag = 0
    for i,v in ipairs{string.byte("GGELUA_FLAG", 1, 11)} do
        Flag = Flag+v
    end
    self._hp:SetPackHeaderFlag(Flag)
end

function PackServer:启动(ip,port)
    __gge.print(false,10,"-------------------------------------------------------------------------\n")
    __gge.print(true,10,string.format('启动[%s]服务端："%s:%s"', self._title,ip,port))
    if self._hp:Start(ip,port) == 0 then
        __gge.print(false,12," 【失败】！\n")
        return false
    end
    return true
end

function PackServer:OnPrepareListen(soListen)--启动成功
   __gge.print(false,10," 【成功】！"..self._网向.."\n")
   __gge.print(false,10,"-------------------------------------------------------------------------\n")
    __gge.print(false,7,"工作线程数量:")
    __gge.print(false,11,self:取工作线程数量())
    __gge.print(false,7,"|并发请求数量:")
    __gge.print(false,11,self:取预投递数量())
    __gge.print(false,7,"|缓冲区大小:")
    __gge.print(false,11,self:取缓冲区大小().."\n")
    -- __gge.print(false,7,"|等候队列大小:")
    -- __gge.print(false,11,self:取等候队列大小().."\n")
     __gge.print(false,10,"-------------------------------------------------------------------------\n")
    if self.启动成功 then
        return __gge.safecall(self.启动成功,self)
    end
    return 0
end

function PackServer:置标题(str)
    ffi.C.SetConsoleTitleA(str)
    return self
end

function PackServer:OnReceive(dwConnID,pData)--数据到达
    print("=== PackServer底层数据接收 ===")
    print("服务器标题:", self._title)
    print("连接ID:", dwConnID)
    print("数据类型:", type(pData))
    print("数据长度:", pData and #pData or "nil")
    if pData and #pData > 0 then
        print("数据前50字符:", string.sub(pData, 1, 50))
    end
    print("=== 调用上层数据到达函数 ===")
    if self.数据到达 then
        __gge.safecall(self.数据到达,self,dwConnID,pData)
    else
        print("警告：没有数据到达处理函数")
    end
    print("=== PackServer数据处理完成 ===")
    return 0
end
function PackServer:发送(dwConnID,Data)
    assert(#Data<4194303, '数据过长！')
    return self._hp:SendPack(dwConnID,Data)==1
end
--/* 设置数据包最大长度（有效数据包最大长度不能超过 4194303/0x3FFFFF 字节，默认：262144/0x40000） */
function PackServer:置数据最大长度(dwMaxPackSize)
    self._hp:SetMaxPackSize(dwMaxPackSize)
end
--/* 设置包头标识（有效包头标识取值范围 0 ~ 1023/0x3FF，当包头标识为 0 时不校验包头，默认：0） */
function PackServer:置包头标识(usPackHeaderFlag)
    assert(usPackHeaderFlag<1023, message)
    self._hp:SetPackHeaderFlag(usPackHeaderFlag)
end
--/* 获取数据包最大长度 */
function PackServer:取数据包最大长度()
    return self._hp:GetMaxPackSize()
end
--/* 获取包头标识 */
function PackServer:取包头标识()
    return self._hp:GetPackHeaderFlag()
end

function PackServer:输出(str)
    __gge.print(true,7,str.."\n")
end

return PackServer