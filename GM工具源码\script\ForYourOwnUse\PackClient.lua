-- @Author: baidwwy
-- @Date:   2017-08-22 19:07:39
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2022-10-01 21:43:30

local PackClient = class(require"script/ForYourOwnUse/TcpClient")
PackClient._mode = 'pack'
PackClient._new  = require("luahp.client")
function PackClient:初始化()
    self._hp = self._new(__gge.cs,__gge.state)
    self._hp:Create_TcpPackClient(self)

    -- 计算包头标识
    local Flag = 0
    for i,v in ipairs{string.byte("GGELUA_FLAG", 1, 11)} do
        Flag = Flag+v
    end

    self._hp:SetPackHeaderFlag(Flag)
    print('[PackClient] 初始化完成 - 包头标识:', Flag, '最大包大小:', self._hp:GetMaxPackSize())
end

function PackClient:OnReceive(pData)--数据到达
    if self.数据到达 then
        __gge.safecall(self.数据到达,self,pData)
    end
    return 0
end
function PackClient:发送(Data)
    assert(#Data<4194303, '数据过长！')

    -- 关键数据包信息
    local firstByte = #Data > 0 and string.byte(Data, 1) or 0
    local hexStr = ""
    for i = 1, math.min(#Data, 16) do
        hexStr = hexStr .. string.format("%02X ", string.byte(Data, i))
    end

    print('[PackClient] 发送数据包 - 长度:', #Data, '首字节: 0x' .. string.format('%02X', firstByte), '前16字节:', hexStr)

    -- HP-Socket底层发送
    self._hp:SendPack(Data)
end
--/* 设置数据包最大长度（有效数据包最大长度不能超过 4194303/0x3FFFFF 字节，默认：262144/0x40000） */
function PackClient:置数据最大长度(dwMaxPackSize)
    self._hp:SetMaxPackSize(dwMaxPackSize)
end
--/* 设置包头标识（有效包头标识取值范围 0 ~ 1023/0x3FF，当包头标识为 0 时不校验包头，默认：0） */
function PackClient:置包头标识(usPackHeaderFlag)
    assert(usPackHeaderFlag<1023, message)
    self._hp:SetPackHeaderFlag(usPackHeaderFlag)
end
--/* 获取数据包最大长度 */
function PackClient:取数据包最大长度()
    return self._hp:GetMaxPackSize()
end
--/* 获取包头标识 */
function PackClient:取包头标识()
    return self._hp:GetPackHeaderFlag()
end


return PackClient