-- @Author: baidwwy
-- @Date:   2017-08-22 19:07:39
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2022-10-01 21:43:30

local PackClient = class(require"script/ForYourOwnUse/TcpClient")
PackClient._mode = 'pack'
PackClient._new  = require("luahp.client")
function PackClient:初始化()
    print('[GM工具] PackClient:初始化 - 开始初始化PackClient')
    
    self._hp = self._new(__gge.cs,__gge.state)
    print('[GM工具] PackClient:初始化 - HP-Socket对象创建完成')
    
    self._hp:Create_TcpPackClient(self)
    print('[GM工具] PackClient:初始化 - TcpPackClient创建完成')
    
    -- 计算包头标识
    local Flag = 0
    local flagStr = "GGELUA_FLAG"
    print('[GM工具] PackClient:初始化 - 计算包头标识，字符串:', flagStr)
    
    for i,v in ipairs{string.byte(flagStr, 1, 11)} do
        print('[GM工具] PackClient:初始化 - 字符', i, ':', string.char(v), '值:', v)
        Flag = Flag+v
    end
    
    print('[GM工具] PackClient:初始化 - 计算得到包头标识:', Flag)
    self._hp:SetPackHeaderFlag(Flag)
    
    -- 显示其他配置信息
    local maxPackSize = self._hp:GetMaxPackSize()
    print('[GM工具] PackClient:初始化 - 最大数据包大小:', maxPackSize)
    
    local currentFlag = self._hp:GetPackHeaderFlag()
    print('[GM工具] PackClient:初始化 - 当前包头标识:', currentFlag)
    
    print('[GM工具] PackClient:初始化 - PackClient初始化完成')
end

function PackClient:OnReceive(pData)--数据到达
    if self.数据到达 then
        __gge.safecall(self.数据到达,self,pData)
    end
    return 0
end
function PackClient:发送(Data)
    assert(#Data<4194303, '数据过长！')
    
    -- 添加详细的发送调试日志
    print('[GM工具] PackClient:发送 - 数据长度:', #Data)
    print('[GM工具] PackClient:发送 - 数据类型:', type(Data))
    print('[GM工具] PackClient:发送 - 前32字节内容:', string.sub(Data, 1, 32))
    
    -- 显示数据的十六进制表示（前64字节）
    local hexStr = ""
    for i = 1, math.min(#Data, 64) do
        hexStr = hexStr .. string.format("%02X ", string.byte(Data, i))
    end
    print('[GM工具] PackClient:发送 - 前64字节Hex:', hexStr)
    
    -- 显示包头标识
    local flag = self._hp:GetPackHeaderFlag()
    print('[GM工具] PackClient:发送 - 当前包头标识:', flag)
    
    -- 检查是否为MessagePack数据
    if #Data > 0 then
        local firstByte = string.byte(Data, 1)
        print('[GM工具] PackClient:发送 - 首字节:', firstByte, '(0x' .. string.format('%02X', firstByte) .. ')')
        if firstByte == 0x91 then
            print('[GM工具] PackClient:发送 - ✓ 检测到MessagePack数组标识(0x91)')
        end
    end
    
    print('[GM工具] PackClient:发送 - 调用HP-Socket SendPack函数')
    self._hp:SendPack(Data)
    print('[GM工具] PackClient:发送 - SendPack调用完成')
end
--/* 设置数据包最大长度（有效数据包最大长度不能超过 4194303/0x3FFFFF 字节，默认：262144/0x40000） */
function PackClient:置数据最大长度(dwMaxPackSize)
    self._hp:SetMaxPackSize(dwMaxPackSize)
end
--/* 设置包头标识（有效包头标识取值范围 0 ~ 1023/0x3FF，当包头标识为 0 时不校验包头，默认：0） */
function PackClient:置包头标识(usPackHeaderFlag)
    assert(usPackHeaderFlag<1023, message)
    self._hp:SetPackHeaderFlag(usPackHeaderFlag)
end
--/* 获取数据包最大长度 */
function PackClient:取数据包最大长度()
    return self._hp:GetMaxPackSize()
end
--/* 获取包头标识 */
function PackClient:取包头标识()
    return self._hp:GetPackHeaderFlag()
end


return PackClient