/**
 * 协议层集成测试
 * 测试完整的协议处理流程
 */

const { protocolHandler, constants } = require('./index');

console.log('=== GM工具协议层集成测试 ===\n');

// 测试1: 登录数据包创建
console.log('1. 测试登录数据包创建:');
try {
    const loginPacket = protocolHandler.createLoginPacket('testuser', 'testpass');
    console.log('登录数据包长度:', loginPacket.length);
    console.log('登录数据包创建成功 ✓');
} catch (error) {
    console.error('登录数据包创建失败 ✗:', error.message);
}
console.log();

// 测试2: 充值数据包创建
console.log('2. 测试充值数据包创建:');
try {
    const rechargePacket = protocolHandler.createRechargePacket(
        constants.PROTOCOL_IDS.RECHARGE_JADE, 
        'testuser', 
        1000
    );
    console.log('充值数据包长度:', rechargePacket.length);
    console.log('充值数据包创建成功 ✓');
} catch (error) {
    console.error('充值数据包创建失败 ✗:', error.message);
}
console.log();

// 测试3: 账号管理数据包创建
console.log('3. 测试账号管理数据包创建:');
try {
    const banPacket = protocolHandler.createAccountManagePacket(
        constants.PROTOCOL_IDS.BAN_ACCOUNT,
        'baduser',
        { 原因: '违规操作', 时长: 24 }
    );
    console.log('封禁数据包长度:', banPacket.length);
    console.log('账号管理数据包创建成功 ✓');
} catch (error) {
    console.error('账号管理数据包创建失败 ✗:', error.message);
}
console.log();

// 测试4: 装备发送数据包创建
console.log('4. 测试装备发送数据包创建:');
try {
    const equipment = {
        名称: '神器长剑',
        等级: 150,
        属性: {
            攻击力: 1000,
            命中: 500,
            暴击: 300
        }
    };
    const equipPacket = protocolHandler.createEquipmentPacket('testuser', equipment);
    console.log('装备数据包长度:', equipPacket.length);
    console.log('装备发送数据包创建成功 ✓');
} catch (error) {
    console.error('装备发送数据包创建失败 ✗:', error.message);
}
console.log();

// 测试5: 游戏活动数据包创建
console.log('5. 测试游戏活动数据包创建:');
try {
    const activityPacket = protocolHandler.createActivityPacket(
        constants.PROTOCOL_IDS.TOMB_SPIRIT,
        { 地图: '大雁塔', 层数: 7 }
    );
    console.log('活动数据包长度:', activityPacket.length);
    console.log('游戏活动数据包创建成功 ✓');
} catch (error) {
    console.error('游戏活动数据包创建失败 ✗:', error.message);
}
console.log();

// 测试6: 数据包完整性验证
console.log('6. 测试数据包完整性验证:');
try {
    const testPacket = protocolHandler.createLoginPacket('test', 'test');
    
    // 测试完整数据包
    const isComplete = protocolHandler.isPacketComplete(testPacket);
    console.log('完整数据包验证:', isComplete ? '✓' : '✗');
    
    // 测试不完整数据包
    const incompletePacket = testPacket.slice(0, 5);
    const isIncomplete = protocolHandler.isPacketComplete(incompletePacket);
    console.log('不完整数据包验证:', !isIncomplete ? '✓' : '✗');
    
    // 测试所需长度计算
    const requiredLength = protocolHandler.getRequiredPacketLength(testPacket);
    console.log('所需长度计算:', requiredLength === testPacket.length ? '✓' : '✗');
    
} catch (error) {
    console.error('数据包完整性验证失败 ✗:', error.message);
}
console.log();

// 测试7: 协议常量访问
console.log('7. 测试协议常量访问:');
try {
    const loginId = protocolHandler.getProtocolId('LOGIN');
    console.log('登录协议ID:', loginId);
    
    const errorMsg = protocolHandler.getErrorMessage('INVALID_ACCOUNT');
    console.log('错误消息:', errorMsg);
    
    const successMsg = protocolHandler.getSuccessMessage('LOGIN_SUCCESS');
    console.log('成功消息:', successMsg);
    
    console.log('协议常量访问成功 ✓');
} catch (error) {
    console.error('协议常量访问失败 ✗:', error.message);
}
console.log();

// 测试8: 加密解密功能
console.log('8. 测试加密解密功能:');
try {
    const testData = '这是一个测试数据包含中文和English123!@#';
    const encrypted = protocolHandler.encrypt(testData);
    const decrypted = protocolHandler.decrypt(encrypted);
    
    console.log('原始数据:', testData);
    console.log('加密后长度:', encrypted.length);
    console.log('解密后数据:', decrypted);
    console.log('加密解密正确:', testData === decrypted ? '✓' : '✗');
} catch (error) {
    console.error('加密解密测试失败 ✗:', error.message);
}
console.log();

// 测试9: 数据格式化功能
console.log('9. 测试数据格式化功能:');
try {
    const complexData = {
        用户名: 'testuser',
        等级: 150,
        金币: 999999,
        装备: '神器套装',
        宠物: ['神兽', '变异宠物']
    };
    
    const formatted = protocolHandler.formatData(complexData);
    console.log('复杂数据对象:', complexData);
    console.log('格式化结果:', formatted);
    console.log('数据格式化成功 ✓');
} catch (error) {
    console.error('数据格式化失败 ✗:', error.message);
}
console.log();

// 测试10: 性能测试
console.log('10. 性能测试:');
try {
    const startTime = Date.now();
    const iterations = 1000;
    
    for (let i = 0; i < iterations; i++) {
        const packet = protocolHandler.createLoginPacket(`user${i}`, `pass${i}`);
        protocolHandler.isPacketComplete(packet);
    }
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    const packetsPerSecond = Math.round((iterations / duration) * 1000);
    
    console.log(`创建${iterations}个数据包耗时: ${duration}ms`);
    console.log(`处理速度: ${packetsPerSecond} 包/秒`);
    console.log('性能测试完成 ✓');
} catch (error) {
    console.error('性能测试失败 ✗:', error.message);
}

console.log('\n=== 协议层集成测试完成 ===');
console.log('协议层实现完成，所有核心功能正常工作！');

module.exports = {
    runIntegrationTests: () => {
        console.log('运行协议层集成测试...');
        // 这里可以添加更多自动化测试
    }
};
