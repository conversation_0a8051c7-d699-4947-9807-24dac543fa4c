--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-01-18 17:38:42
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解所以资源
--======================================================================--
function 判断修炼召唤兽选项(id,事件)
	local 任务id=玩家数据[id].角色:取任务(13)
	if 任务数据[任务id]==nil or 任务数据[任务id].分类~=15 then
		return
	end
	local 选项={}
	local 模型=任务数据[任务id].bb
	for n=1,#玩家数据[id].召唤兽.数据 do
		if 玩家数据[id].召唤兽.数据[n].模型==模型 and 玩家数据[id].召唤兽.数据[n].种类=="变异" then
			选项[#选项+1]={对话=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=n}
		end
	end
	for n=1,#选项 do
		if 选项[n].对话==事件 then
			玩家数据[id].召唤兽:删除处理(id,选项[n].编号)
			任务处理类:完成宠修任务(id,任务id)
			return
		end
	end
	添加最后对话(id,"你所选的这只召唤兽并不是对方想要的")
end

function 取符合修炼召唤兽选项(id)
	local 任务id=玩家数据[id].角色:取任务(13)
	if 任务数据[任务id]==nil or 任务数据[任务id].分类~=15 then
		return
	end
	local 选项={}
	local 模型=任务数据[任务id].bb
	for n=1,#玩家数据[id].召唤兽.数据 do
       if 玩家数据[id].召唤兽.数据[n].模型==模型 and 玩家数据[id].召唤兽.数据[n].种类=="变异" then
            选项[#选项+1]=format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级)
       	end
 	end
	return 选项
end





-- function 计算修炼等级经验(等级,上限)
-- 	if 等级==0 then return 110 end
-- 	local 临时经验=110
-- 	for n=1,上限+1 do
-- 		临时经验=临时经验+20+n*20
-- 		if n==等级 then return  math.floor(临时经验) end
-- 	end

-- end



修炼经验={[0]=110}
for i=1,35 do
	修炼经验[i]=修炼经验[i-1]+20+i*20
end


法宝经验={
	{469350,712000,2028000,4469000,8500000,14424000,22652000,33568000,47556000},
	{210268,472144,1082236,2237152,4133500,6967888,10936924,16237216,23065372,31618000,40000000,54683104},
	{199452,385616,790204,1544928,2781500,4631632,7227036,10699424,15180508,20802000,27695612,35993056,45826044,57326288,70625500},
	{1834389,2935112,4348503,6220896,8698625,11928024,16055427,21227168,27589581,35289000,44471759,55284192,67872633,82383416,98962875,117757344,138913157,162576648},
}

技能消耗={}
技能消耗.经验={
	[1]=16,
	[2]=32,
	[3]=52,
	[4]=75,
	[5]=103,
	[6]=136,
	[7]=179,
	[8]=231,
	[9]=295,
	[10]=372,
	[11]=466,
	[12]=578,
	[13]=711,
	[14]=867,
	[15]=1049,
	[16]=1280,
	[17]=1503,
	[18]=1780,
	[19]=2096,
	[20]=2452,
	[21]=2854,
	[22]=3304,
	[23]=3807,
	[24]=4364,
	[25]=4983,
	[26]=5664,
	[27]=6415,
	[28]=7238,
	[29]=8138,
	[30]=9120,
	[31]=10188,
	[32]=11347,
	[33]=12602,
	[34]=13959,
	[35]=15423,
	[36]=16998,
	[37]=18692,
	[38]=20508,
	[39]=22452,
	[40]=24532,
	[41]=26753,
	[42]=29121,
	[43]=31642,
	[44]=34323,
	[45]=37169,
	[46]=40186,
	[47]=43388,
	[48]=46773,
	[49]=50352,
	[50]=54132,
	[51]=58120,
	[52]=62324,
	[53]=66750,
	[54]=71407,
	[55]=76303,
	[56]=81444,
	[57]=86840,
	[58]=92500,
	[59]=104640,
	[60]=111136,
	[61]=117931,
	[62]=125031,
	[63]=132444,
	[64]=140183,
	[65]=148253,
	[66]=156666,
	[67]=156666,
	[68]=165430,
	[69]=174556,
	[70]=184052,
	[71]=193930,
	[72]=204198,
	[73]=214868,
	[74]=225948,
	[75]=237449,
	[76]=249383,
	[77]=261760,
	[78]=274589,
	[79]=287884,
	[80]=301652,
	[81]=315908,
	[82]=330662,
	[83]=345924,
	[84]=361708,
	[85]=378023,
	[86]=394882,
	[87]=412297,
	[88]=430280,
	[89]=448844,
	[90]=468000,
	[91]=487760,
	[92]=508137,
	[93]=529145,
	[94]=550796,
	[95]=573103,
	[96]=596078,
	[97]=619735,
	[98]=644088,
	[99]=669149,
	[100]=721452,
	[101]=748722,
	[102]=776755,
	[103]=805566,
	[104]=835169,
	[105]=865579,
	[106]=896809,
	[107]=928876,
	[108]=961792,
	[109]=995572,
	[110]=1030234,
	[111]=1065190,
	[112]=1102256,
	[113]=1139649,
	[114]=1177983,
	[115]=1217273,
	[116]=1256104,
	[117]=1298787,
	[118]=1341043,
	[119]=1384320,
	[120]=1428632,
	[121]=1473999,
	[122]=1520435,
	[123]=1567957,
	[124]=1616583,
	[125]=1666328,
	[126]=1717211,
	[127]=1769248,
	[128]=1822456,
	[129]=1876852,
	[130]=1932456,
	[131]=1989284,
	[132]=2047353,
	[133]=2106682,
	[134]=2167289,
	[135]=2229192,
	[136]=2292410,
	[137]=2356960,
	[138]=2422861,
	[139]=2490132,
	[140]=2558792,
	[141]=2628860,
	[142]=2700356,
	[143]=2773296,
	[144]=2847703,
	[145]=2923593,
	[146]=3000989,
	[147]=3079908,
	[148]=3160372,
	[149]=3242400,
	[150]=6652022,
	[151]=6822452,
	[152]=6996132,
	[153]=7173104,
	[154]=7353406,
	[155]=11305620,
	[156]=15305620,
	[157]=22305620,
	[158]=27305620,
	[159]=37305620,
	[160]=45305620,
	[161]=54305620,
	[162]=57305620,
	[163]=60305620,
	[164]=65305620,
	[165]=70305620,
	[166]=84305620,
}

for n=167,250 do
  技能消耗.经验[n]=math.floor(技能消耗.经验[n-1]*1.2)
end

技能消耗.金钱={
	[1]=6,
	[2]=12,
	[3]=19,
	[4]=28,
	[5]=38,
	[6]=51,
	[7]=67,
	[8]=86,
	[9]=110,
	[10]=139,
	[11]=174,
	[12]=216,
	[13]=266,
	[14]=325,
	[15]=393,
	[16]=472,
	[17]=563,
	[18]=667,
	[19]=786,
	[20]=919,
	[21]=1070,
	[22]=1238,
	[23]=1426,
	[24]=1636,
	[25]=1868,
	[26]=2124,
	[27]=2404,
	[28]=2714,
	[29]=3050,
	[30]=3420,
	[31]=3820,
	[32]=4255,
	[33]=4725,
	[34]=5234,
	[35]=5783,
	[36]=6374,
	[37]=7009,
	[38]=7680,
	[39]=8419,
	[40]=9199,
	[41]=10032,
	[42]=10920,
	[43]=11865,
	[44]=12871,
	[45]=13938,
	[46]=15070,
	[47]=16270,
	[48]=17540,
	[49]=18882,
	[50]=20299,
	[51]=21795,
	[52]=23371,
	[53]=25031,
	[54]=26777,
	[55]=28613,
	[56]=30541,
	[57]=32565,
	[58]=34687,
	[59]=36911,
	[60]=39240,
	[61]=41676,
	[62]=44224,
	[63]=46886,
	[64]=49666,
	[65]=52568,
	[66]=55595,
	[67]=58749,
	[68]=62036,
	[69]=65458,
	[70]=69019,
	[71]=72723,
	[72]=76574,
	[73]=80575,
	[74]=84730,
	[75]=89043,
	[76]=93516,
	[77]=98160,
	[78]=102971,
	[79]=107956,
	[80]=113119,
	[81]=118465,
	[82]=123998,
	[83]=129721,
	[84]=135640,
	[85]=141758,
	[86]=148080,
	[87]=154611,
	[88]=161355,
	[89]=168316,
	[90]=175500,
	[91]=182910,
	[92]=190551,
	[93]=198429,
	[94]=206548,
	[95]=214913,
	[96]=223529,
	[97]=232400,
	[98]=241533,
	[99]=250931,
	[100]=260599,
	[101]=270544,
	[102]=280770,
	[103]=291283,
	[104]=302087,
	[105]=313188,
	[106]=324592,
	[107]=336303,
	[108]=348328,
	[109]=360672,
	[110]=373339,
	[111]=386337,
	[112]=399671 ,
	[113]=413346,
	[114]=427368,
	[115]=441743,
	[116]=456477,
	[117]=471576,
	[118]=487045,
	[119]=502891,
	[120]=519120,
	[121]=535737,
	[122]=552749,
	[123]=570163,
	[124]=587984,
	[125]=606218,
	[126]=624873,
	[127]=643954,
	[128]=663468 ,
	[129]=683421,
	[130]=703819,
	[131]=724671,
	[132]=745981,
	[133]=767757,
	[134]=790005,
	[135]=812733,
	[136]=835947 ,
	[137]=859653,
	[138]=883860,
	[139]=908573 ,
	[140]=933799 ,
	[141]=959547 ,
	[142]=985822,
	[143]=1012633,
	[144]=1039986,
	[145]=1067888 ,
	[146]=1096347,
	[147]=1125371,
	[148]=1154965,
	[149]=1185139,
	[150]=1215900,
	[151]=2494508,
	[152]=2558419,
	[153]=2623549,
	[154]=2689914,
	[155]=2757527,
	[156]=4239607,
	[157]=6239607,
	[158]=8239607,
	[159]=10239607,
	[160]=15239607,
	[161]=18239607,
	[162]=20239607,
	[163]=25239607,
	[164]=30239607,
	[165]=36239607,
	[166]=40239607,
}
for n=167,250 do
  技能消耗.金钱[n]=math.floor(技能消耗.金钱[n-1]*1.2)
end


升级消耗={}
升级消耗.角色={
	[1]=40,
	[2]=110,
	[3]=237,
	[4]=450,
	[5]=779,
	[6]=1252,
	[7]=1898,
	[8]=2745,
	[9]=3822,
	[10]=5159,
	[11]=6784,
	[12]=8726,
	[13]=11013,
	[14]=13674,
	[15]=16739,
	[16]=20236,
	[17]=24194,
	[18]=28641,
	[19]=33606,
	[20]=39119,
	[21]=45208,
	[22]=51902,
	[23]=55229,
	[24]=67218,
	[25]=75899,
	[26]=85300,
	[27]=95450,
	[28]=106377,
	[29]=118110,
	[30]=130679,
	[31]=144112,
	[32]=158438,
	[33]=173685,
	[34]=189882,
	[35]=207059,
	[36]=225244,
	[37]=244466,
	[38]=264753,
	[39]=286134,
	[40]=308639,
	[41]=332296,
	[42]=357134,
	[43]=383181,
	[44]=410466,
	[45]=439019,
	[46]=468868,
	[47]=500042,
	[48]=532569,
	[49]=566478,
	[50]=601799,
	[51]=638560,
	[52]=676790,
	[53]=716517,
	[54]=757770,
	[55]=800579,
	[56]=844972,
	[57]=890978,
	[58]=938625,
	[59]=987942,
	[60]=1038959,
	[61]=1091704,
	[62]=1146206,
	[63]=1202493,
	[64]=1260594,
	[65]=1320539,
	[66]=1382356,
	[67]=1446074,
	[68]=1511721,
	[69]=1579326,
	[70]=1648919,
	[71]=1720528,
	[72]=1794182,
	[73]=1869909,
	[74]=1947738,
	[75]=2027699,
	[76]=2109820,
	[77]=2194130,
	[78]=2280657,
	[79]=2369431,
	[80]=2460479,
	[81]=2553832,
	[82]=2649518,
	[83]=2747565,
	[84]=2848003,
	[85]=2950859,
	[86]=3056164,
	[87]=3163946,
	[88]=3274233,
	[89]=3387055,
	[90]=3502439,
	[91]=3620416,
	[92]=3741014,
	[93]=3864261,
	[94]=3990187,
	[95]=4118819,
	[96]=4250188,
	[97]=4384322,
	[98]=4521249,
	[99]=4660999,
	[100]=4803599,
	[101]=4998571,
	[102]=5199419,
	[103]=5406260,
	[104]=5619213,
	[105]=5838397,
	[106]=6063933,
	[107]=6295941,
	[108]=6534544,
	[109]=6779867,
	[110]=7032035,
	[111]=7291172,
	[112]=7557407,
	[113]=7830869,
	[114]=8111686,
	[115]=8399990,
	[116]=8695912,
	[117]=8999586,
	[118]=9311145,
	[119]=9630726,
	[120]=9958463,
	[121]=10294496,
	[122]=10638964,
	[123]=10992005,
	[124]=11353761,
	[125]=11724374,
	[126]=12103988,
	[127]=12492748,
	[128]=12890799,
	[129]=13298287,
	[130]=13715362,
	[131]=14142172,
	[132]=14578867,
	[133]=15025600,
	[134]=15482522,
	[135]=15949788,
	[136]=16427552,
	[137]=16915970,
	[138]=17415202,
	[139]=17925402,
	[140]=18446732,
	[141]=18979354,
	[142]=19523428,
	[143]=20079116,
	[144]=20646584,
	[145]=212259980,
	[146]=436350440,
	[147]=448426480,
	[148]=460751480,
	[149]=473328860,
	[150]=486162000,
	[151]=748881480,
	[152]=768914010,
	[153]=789345801,
	[154]=810182190,
	[155]=831428350,
	[156]=853089690,
	[157]=879774210,
	[158]=897679440,
	[159]=920618700,
	[160]=1461487640,
	[161]=1500947800,
	[162]=1541473400,
	[163]=1583093180,
	[164]=1625836690,
	[165]=1669734280,
	[166]=1714817110,
	[167]=1761117170,
	[168]=1808667340,
	[169]=1857801350,
	[170]=2406029040,
	[171]=5336793620,
	[172]=8194071000,
	[173]=11181699470,
	[174]=14303066640,
	[175]=17561612250,
	[176]=20960828530,
}
for n=177,250 do
  	升级消耗.角色[n]=math.floor(升级消耗.角色[n-1]*1.3)
end

升级消耗.宠物={
   	[1]=50,
   	[2]=200,
   	[3]=450,
   	[4]=800,
   	[5]=1250,
   	[6]=1800,
   	[7]=2450,
   	[8]=3250,
   	[9]=4050,
   	[10]=5000,
   	[11]=6050,
   	[12]=7200,
   	[13]=8450,
   	[14]=9800,
   	[15]=11250,
   	[16]=12800,
   	[17]=14450,
   	[18]=16200,
   	[19]=18050,
   	[20]=20000,
   	[21]=22050,
   	[22]=24200,
   	[23]=26450,
   	[24]=28800,
   	[25]=31250,
   	[26]=33800,
   	[27]=36450,
   	[28]=39200,
   	[29]=42050,
   	[30]=45000,
   	[31]=48050,
   	[32]=51200,
   	[33]=54450,
   	[34]=57800,
   	[35]=61250,
   	[36]=64800,
   	[37]=68450,
   	[38]=72200,
   	[39]=76050,
   	[40]=80000,
   	[41]=84050,
   	[42]=88200,
   	[43]=92450,
   	[44]=96800,
   	[45]=101250,
   	[46]=105800,
   	[47]=110450,
   	[48]=115200,
   	[49]=120050,
   	[50]=125000,
   	[51]=130050,
   	[52]=135200,
   	[53]=140450,
   	[54]=145800,
   	[55]=151250,
   	[56]=156800,
   	[57]=162450,
   	[58]=168200,
   	[59]=174050,
   	[60]=180000,
   	[61]=186050,
   	[62]=192200,
   	[63]=198450,
   	[64]=204800,
   	[65]=211250,
   	[66]=217800,
   	[67]=224450,
   	[68]=231200,
   	[69]=238050,
   	[70]=245000,
   	[71]=252050,
   	[72]=259200,
   	[73]=266450,
   	[74]=273800,
   	[75]=281250,
   	[76]=288800,
   	[77]=296450,
   	[78]=304200,
   	[79]=312050,
   	[80]=320000,
   	[81]=328050,
   	[82]=336200,
   	[83]=344450,
   	[84]=352800,
   	[85]=361250,
   	[86]=369800,
   	[87]=378450,
   	[88]=387200,
   	[89]=396050,
   	[90]=405000,
   	[91]=414050,
   	[92]=423200,
   	[93]=432450,
   	[94]=441800,
   	[95]=451250,
   	[96]=460800,
   	[97]=470450,
   	[98]=480200,
   	[99]=490050,
   	[100]=500000,
   	[101]=510050,
   	[102]=520200,
   	[103]=530450,
   	[104]=540800,
   	[105]=551250,
   	[106]=561800,
   	[107]=572450,
   	[108]=583200,
   	[109]=594050,
   	[110]=605000,
   	[111]=616050,
   	[112]=627200,
   	[113]=638450,
   	[114]=649800,
   	[115]=661250,
   	[116]=672800,
   	[117]=684450,
   	[118]=696200,
   	[119]=708050,
   	[120]=720000,
   	[121]=732050,
   	[122]=744200,
   	[123]=756450,
   	[124]=768800,
   	[125]=781250,
   	[126]=793800,
   	[127]=806450,
   	[128]=819200,
   	[129]=832050,
   	[130]=845000,
   	[131]=858050,
   	[132]=871200,
   	[133]=884450,
   	[134]=897800,
   	[135]=911250,
   	[136]=924800,
   	[137]=938450,
   	[138]=952200,
   	[139]=966050,
   	[140]=980000,
   	[141]=994050,
   	[142]=1008200,
   	[143]=1022450,
   	[144]=1036800,
   	[145]=1051250,
   	[146]=1065800,
   	[147]=1080450,
   	[148]=1095200,
   	[149]=1110050,
   	[150]=1125000,
   	[151]=1140050,
   	[152]=1155200,
   	[153]=1170450,
   	[154]=1185800,
   	[155]=1201250,
   	[156]=1216800,
   	[157]=1232450,
   	[158]=1248200,
   	[159]=1264050,
   	[160]=1280000,
   	[161]=1300000,
   	[162]=1340000,
   	[163]=1380000,
   	[164]=1420000,
   	[165]=1460000,
   	[166]=1500000,
   	[167]=1540000,
   	[168]=1580000,
   	[169]=1700000,
   	[170]=1780000,
   	[171]=1820000,
   	[172]=1940000,
   	[173]=2400000,
   	[174]=2880000,
   	[175]=3220000,
   	[176]=4020000,
   	[177]=4220000,
   	[178]=4420000,
   	[179]=4620000,
   	[180]=5220000,
   	[181]=5820000,
   	[182]=6220000,
   	[183]=7020000,
   	[184]=8020000,
   	[185]=9020000,
}
for n=186,250 do
  	升级消耗.宠物[n]=math.floor(升级消耗.宠物[n-1]*1.2)
end

function 角色取技能消耗(目标技能等级,类型)--1师门  2生活
  local cc = 0
  local vv = 0
  local 等级 = 目标技能等级
  if 等级 > 0 and 等级 <= 2 then
    cc = 8*(目标技能等级+1)*目标技能等级
  elseif 等级 > 2 and 等级 <= 5 then
    cc = 8*(目标技能等级+1)*目标技能等级+2^目标技能等级
  elseif 等级 > 5 and 等级 <= 11 then
    cc = 目标技能等级*目标技能等级*(目标技能等级+5)
  elseif 等级 > 11 and 等级 <= 18 then
    cc = 目标技能等级*(目标技能等级+1)*10*(1.625+(目标技能等级-12)*0.2)
  elseif 等级 > 18 and 等级 <= 25 then
    cc = 目标技能等级*(目标技能等级+10)*10*(2.37+(目标技能等级-19)*0.25)
  elseif 等级 > 25 and 等级 <= 31 then
    cc = 目标技能等级*(目标技能等级-10)*(94.3+(目标技能等级-26)*5.5)
  elseif 等级 > 25 and 等级 <= 31 then
    cc = 目标技能等级*(目标技能等级-10)*(94.3+(目标技能等级-26)*5.5)
  elseif 等级 > 31 and 等级 <= 40 then
    cc = 目标技能等级*(目标技能等级-10)*(130.22+(目标技能等级-32)*8)
  elseif 等级 > 40 and 等级 <= 50 then
    cc = 目标技能等级*(目标技能等级-10)*(207.4+(目标技能等级-41)*11)
  elseif 等级 > 50 and 等级 <= 60 then
    cc = 目标技能等级*(目标技能等级-10)*(329.41+(目标技能等级-51)*16)
  elseif 等级 > 60 and 等级 <= 70 then
    cc = 目标技能等级*(目标技能等级-10)*(495.2+(目标技能等级-61)*21)
  elseif 等级 > 70 and 等级 <= 80 then
    cc = 目标技能等级*(目标技能等级-10)*(710.93+(目标技能等级-71)*26)
  elseif 等级 > 80 and 等级 <= 90 then
    cc = 目标技能等级*(目标技能等级-10)*(982.9+(目标技能等级-81)*33)
  elseif 等级 > 90 and 等级 <= 100 then
    cc = 目标技能等级*(目标技能等级-10)*(1317.47+(目标技能等级-91)*39)
  elseif 等级 > 100 and 等级 <= 110 then
    cc = 目标技能等级*(目标技能等级-10)*(1720.997+(目标技能等级-101)*47)
  elseif 等级 > 110 and 等级 <= 120 then
    cc = 目标技能等级*(目标技能等级-10)*(2199.87+(目标技能等级-111)*55)
  elseif 等级 > 120 and 等级 <= 130 then
    cc = 目标技能等级*(目标技能等级-10)*(2760.33+(目标技能等级-121)*64)
  elseif 等级 > 130 and 等级 <= 140 then
    cc = 目标技能等级*(目标技能等级-10)*(3409.09+(目标技能等级-131)*73)
  elseif 等级 > 140 and 等级 <= 150 then
    cc = 目标技能等级*(目标技能等级-10)*(4152.37+(目标技能等级-141)*83)
  elseif 等级 > 150 and 等级 <= 155 then
    cc = 目标技能等级*(目标技能等级-10)*(5152.78+(目标技能等级-151)*247)
  elseif 等级 > 155 and 等级 <= 160 then
    cc = 目标技能等级*(目标技能等级-10)*(6557.72+(目标技能等级-156)*417)
  elseif 等级 > 160 and 等级 <= 170 then
    cc = 目标技能等级*(目标技能等级-10)*(8739.79+(目标技能等级-161)*503)
  elseif 等级 > 170 and 等级 <= 174 then
    cc = 目标技能等级*(目标技能等级-10)*(13980.32+(目标技能等级-171)*707)
  elseif 等级 > 174 and 等级 <= 179 then
    cc = 目标技能等级*(目标技能等级-10)*(16804.73+(目标技能等级-175)*918)
  elseif 等级 > 179 then
    local 临时等级 = 目标技能等级 -1
    cc=临时等级*(临时等级-10)*(16804.73+(临时等级-175)*918)*1.2
  end
  if 等级 > 0 and 等级 <= 3 then
    vv = 3*(目标技能等级+1)*目标技能等级
  elseif 等级 > 3 and 等级 <= 9 then
    vv = (目标技能等级+1)*目标技能等级*(3.25+(目标技能等级-4)*0.25)
  elseif 等级 > 9 and 等级 <= 16 then
    vv = (目标技能等级-5)*目标技能等级*(11.12+(目标技能等级-10)*0.4)
  elseif 等级 > 16 and 等级 <= 20 then
    vv = (目标技能等级-10)*目标技能等级*(24.91+(目标技能等级-17)*0.55)
  elseif 等级 > 20 and 等级 <= 30 then
    vv = (目标技能等级-10)*目标技能等级*(27.73+(目标技能等级-21)*1.7)
  elseif 等级 > 30 and 等级 <= 40 then
    vv = (目标技能等级-10)*目标技能等级*(46.24+(目标技能等级-31)*3)
  elseif 等级 > 40 and 等级 <= 50 then
    vv = (目标技能等级-10)*目标技能等级*(77.75+(目标技能等级-41)*4.4)
  elseif 等级 > 50 and 等级 <= 60 then
    vv = (目标技能等级-10)*目标技能等级*(123.51+(目标技能等级-51)*6)
  elseif 等级 > 60 and 等级 <= 70 then
    vv = (目标技能等级-10)*目标技能等级*(185.69+(目标技能等级-61)*7.8)
  elseif 等级 > 70 and 等级 <= 80 then
    vv = (目标技能等级-10)*目标技能等级*(266.59+(目标技能等级-71)*10)
  elseif 等级 > 80 and 等级 <= 90 then
    vv = (目标技能等级-10)*目标技能等级*(368.58+(目标技能等级-81)*12.3)
  elseif 等级 > 90 and 等级 <= 100 then
    vv = (目标技能等级-10)*目标技能等级*(494.04+(目标技能等级-91)*14.8)
  elseif 等级 > 100 and 等级 <= 110 then
    vv = (目标技能等级-10)*目标技能等级*(645.36+(目标技能等级-101)*17.6)
  elseif 等级 > 110 and 等级 <= 120 then
    vv = (目标技能等级-10)*目标技能等级*(824.94+(目标技能等级-111)*20.65)
  elseif 等级 > 120 and 等级 <= 130 then
    vv = (目标技能等级-10)*目标技能等级*(1035.17+(目标技能等级-121)*24)
  elseif 等级 > 130 and 等级 <= 140 then
    vv = (目标技能等级-10)*目标技能等级*(1278.45+(目标技能等级-131)*27.5)
  elseif 等级 > 140 and 等级 <= 150 then
    vv = (目标技能等级-10)*目标技能等级*(1557.17+(目标技能等级-141)*31.3)
  elseif 等级 > 150 and 等级 <= 155 then
    vv = (目标技能等级-10)*目标技能等级*(1932.32+(目标技能等级-151)*92.6)
  elseif 等级 > 155 and 等级 <= 160 then
    vv = (目标技能等级-10)*目标技能等级*(2459.17+(目标技能等级-156)*156.3)
  elseif 等级 > 160 and 等级 <= 170 then
    vv = (目标技能等级-10)*目标技能等级*(3231.07+(目标技能等级-161)*144)
  elseif 等级 > 170 and 等级 <= 175 then
    vv = (目标技能等级-10)*目标技能等级*(4731.98+(目标技能等级-171)*205.3)
  elseif 等级 > 175 and 等级 <= 180 then
    vv = (目标技能等级-10)*目标技能等级*(5825.51+(目标技能等级-176)*283)
  elseif 等级 > 180 then
    local 临时等级 = 目标技能等级 -1
    vv = (临时等级-10)*临时等级*(5825.51+(临时等级-176)*283)*1.2
  end
  if 类型==1 then
    return {经验=math.floor(cc),金钱=math.floor(vv)}
  else
    return {经验=math.floor(cc*5),金钱=math.floor(vv*5)}--{经验=floor(技能消耗.经验[等级]*5),金钱=floor(技能消耗.金钱[等级]*3)}
  end
end
