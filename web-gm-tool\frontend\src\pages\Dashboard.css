.dashboard-logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px;
  border-radius: 8px;
  transition: all 0.3s;
}

.dashboard-logo:hover {
  background: rgba(255, 255, 255, 0.15);
}

.dashboard-header {
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  margin-left: 16px;
}

.header-right {
  padding-right: 24px;
}

.dashboard-content {
  margin: 0;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  padding: 24px;
  height: 100%;
}

/* 侧边栏菜单样式 */
.ant-layout-sider-dark .ant-menu-dark {
  background: transparent;
}

.ant-layout-sider-dark .ant-menu-dark .ant-menu-item {
  margin: 4px 12px;
  border-radius: 8px;
  transition: all 0.3s;
}

.ant-layout-sider-dark .ant-menu-dark .ant-menu-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.ant-layout-sider-dark .ant-menu-dark .ant-menu-item-selected {
  background: #1890ff !important;
  color: #fff;
}

.ant-layout-sider-dark .ant-menu-dark .ant-menu-item-selected::after {
  display: none;
}

/* 头部按钮样式 */
.dashboard-header .ant-btn {
  color: #fff;
  border: none;
  background: transparent;
}

.dashboard-header .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
}

/* 用户下拉菜单 */
.ant-dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 状态徽章 */
.ant-badge-status-text {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 0 16px;
  }
  
  .header-title {
    display: none;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .ant-layout-sider {
    position: fixed !important;
    height: 100vh;
    z-index: 999;
  }
  
  .ant-layout-sider-collapsed {
    transform: translateX(-100%);
  }
}

/* 滚动条样式 */
.ant-layout-sider::-webkit-scrollbar {
  width: 6px;
}

.ant-layout-sider::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.ant-layout-sider::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.ant-layout-sider::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 动画效果 */
.ant-layout-sider {
  transition: all 0.2s;
}

.dashboard-content {
  transition: all 0.2s;
}

/* 内容区域样式 */
.content-wrapper > * {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
