{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"./useEvent\";\n/**\n * Same as React.useState but will always get latest state.\n * This is useful when React merge multiple state updates into one.\n * e.g. onTransitionEnd trigger multiple event at once will be merged state update in React.\n */\nexport default function useSyncState(defaultValue) {\n  var _React$useReducer = React.useReducer(function (x) {\n      return x + 1;\n    }, 0),\n    _React$useReducer2 = _slicedToArray(_React$useReducer, 2),\n    forceUpdate = _React$useReducer2[1];\n  var currentValueRef = React.useRef(defaultValue);\n  var getValue = useEvent(function () {\n    return currentValueRef.current;\n  });\n  var setValue = useEvent(function (updater) {\n    currentValueRef.current = typeof updater === 'function' ? updater(currentValueRef.current) : updater;\n    forceUpdate();\n  });\n  return [getValue, setValue];\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useEvent", "useSyncState", "defaultValue", "_React$useReducer", "useReducer", "x", "_React$useReducer2", "forceUpdate", "currentValueRef", "useRef", "getValue", "current", "setValue", "updater"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-util/es/hooks/useSyncState.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\nimport useEvent from \"./useEvent\";\n/**\n * Same as React.useState but will always get latest state.\n * This is useful when React merge multiple state updates into one.\n * e.g. onTransitionEnd trigger multiple event at once will be merged state update in React.\n */\nexport default function useSyncState(defaultValue) {\n  var _React$useReducer = React.useReducer(function (x) {\n      return x + 1;\n    }, 0),\n    _React$useReducer2 = _slicedToArray(_React$useReducer, 2),\n    forceUpdate = _React$useReducer2[1];\n  var currentValueRef = React.useRef(defaultValue);\n  var getValue = useEvent(function () {\n    return currentValueRef.current;\n  });\n  var setValue = useEvent(function (updater) {\n    currentValueRef.current = typeof updater === 'function' ? updater(currentValueRef.current) : updater;\n    forceUpdate();\n  });\n  return [getValue, setValue];\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,QAAQ,MAAM,YAAY;AACjC;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,YAAY,EAAE;EACjD,IAAIC,iBAAiB,GAAGJ,KAAK,CAACK,UAAU,CAAC,UAAUC,CAAC,EAAE;MAClD,OAAOA,CAAC,GAAG,CAAC;IACd,CAAC,EAAE,CAAC,CAAC;IACLC,kBAAkB,GAAGR,cAAc,CAACK,iBAAiB,EAAE,CAAC,CAAC;IACzDI,WAAW,GAAGD,kBAAkB,CAAC,CAAC,CAAC;EACrC,IAAIE,eAAe,GAAGT,KAAK,CAACU,MAAM,CAACP,YAAY,CAAC;EAChD,IAAIQ,QAAQ,GAAGV,QAAQ,CAAC,YAAY;IAClC,OAAOQ,eAAe,CAACG,OAAO;EAChC,CAAC,CAAC;EACF,IAAIC,QAAQ,GAAGZ,QAAQ,CAAC,UAAUa,OAAO,EAAE;IACzCL,eAAe,CAACG,OAAO,GAAG,OAAOE,OAAO,KAAK,UAAU,GAAGA,OAAO,CAACL,eAAe,CAACG,OAAO,CAAC,GAAGE,OAAO;IACpGN,WAAW,CAAC,CAAC;EACf,CAAC,CAAC;EACF,OAAO,CAACG,QAAQ,EAAEE,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}