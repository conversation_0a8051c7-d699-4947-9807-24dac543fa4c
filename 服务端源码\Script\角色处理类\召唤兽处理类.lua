
local 召唤兽处理类 = class()
local 五行_ = {"金","木","水","火","土"}

require("Script/数据中心/梦战召唤兽模型")
function 召唤兽处理类:初始化(id)
 self.生成数据 = {}
 self.资质范围={"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}
 self.属性范围={"体质","魔力","力量","耐力","敏捷"}
end

function 召唤兽处理类:数据处理(连接id,序号,id,内容)
  if 玩家数据[id].摊位数据~=nil and 序号~=5001 then
    常规提示(id,"#Y/摆摊状态下禁止此种行为")
    return
  end
  if 序号==5001 then
    发送数据(连接id,17,self.数据)
  elseif 序号==5002 then
    self:参战处理(连接id,序号,id,内容.序列)
  elseif 序号==5003 then
    self:改名处理(连接id,序号,id,内容.序列,内容.名称)
  elseif 序号==5004 then
    self:加点处理(连接id,序号,id,内容)
  elseif 序号==5005 then
    self:放生处理(连接id,序号,id,内容)
  elseif 序号==5006 then
    发送数据(连接id,22,玩家数据[id].角色.数据.宠物)
  elseif 序号==5007 then
    发送数据(连接id,16,self.数据)
    发送数据(连接id,23,玩家数据[id].道具:索要道具2(id))
  elseif 序号==5008 then
    self:洗练处理(连接id,序号,id,内容)
  elseif 序号==5009 then
    self:合宠处理(连接id,序号,id,内容)
  elseif 序号==5010 then
    self:炼化处理(连接id,序号,id,内容)
  elseif 序号==5011 then
    self:召唤兽染色(连接id,序号,id,内容)
  elseif 序号==5011.1 then
    self:召唤兽还原染色(连接id,序号,id,内容)
  elseif 序号==5012 then
    self:召唤兽饰品染色(连接id,序号,id,内容)
  elseif 序号==5015.1 then
    发送数据(连接id,16,self.数据)
    发送数据(连接id,151,玩家数据[id].道具:索要道具2(id))
  elseif 序号==5015 then
    发送数据(连接id,3526,{召唤兽=self.数据,召唤兽仓库总数=#玩家数据[id].召唤兽仓库.数据,召唤兽仓库数据=玩家数据[id].召唤兽仓库:索取召唤兽仓库数据(id,1)})
  elseif 序号==5016 then
    发送数据(连接id,16,self.数据)
    发送数据(连接id,152,玩家数据[id].道具:索要道具2(id))
  elseif 序号==5017 then
    发送数据(连接id,16,self.数据)
    发送数据(连接id,153,玩家数据[id].道具:索要道具2(id))
  elseif 序号==5020 then
    发送数据(连接id,119,self.数据)
  elseif 序号==5021 then
    self:召唤兽赐福(连接id,序号,id,内容)






  end
end


function 召唤兽处理类:召唤兽赐福(连接id,序号,id,内容)
   local 请求类型=内容.类型
   if 请求类型==nil then return end
   local 编号=0
   if 内容.序列~=nil then
      编号=self:取编号(内容.序列)
   end
   if 编号~=0 and 编号~=nil and self.数据[编号]==nil then return end



   if 请求类型=="打开" then
      发送数据(连接id,55,{召唤兽=self.数据,道具=玩家数据[id].道具:索要道具2(id)})
   elseif 请求类型=="保存技能" then
       if 编号==nil or 编号==0 then
         常规提示(id,"#Y请先选中宝宝后在操作")
         return
      end
      if  self.数据[编号].临时赐福~=nil and  self.数据[编号].临时赐福[4]~=nil then
          self.数据[编号].超级赐福=DeepCopy(self.数据[编号].临时赐福)
          发送数据(连接id,55,{召唤兽=self.数据,道具=玩家数据[id].道具:索要道具2(id)})
      else
          常规提示(id,"#Y数据错误,请重新操作")
          return
      end
   elseif 请求类型=="锁定技能" then
        if 编号==nil or 编号==0 then
           常规提示(id,"#Y请先选中宝宝后在操作")
           return
        elseif 内容.编号==nil or tonumber(内容.编号)==nil or tonumber(内容.编号)==0 then
          常规提示(id,"#Y数据错误,请重新操作")
           return
        end
        if self.数据[编号].赐福锁定==nil then
            self.数据[编号].赐福锁定={}
        end
        local 锁定数量=0
        for i=1,4 do
            if self.数据[编号].赐福锁定[i] then
                锁定数量=锁定数量+1
            end
        end
        if 锁定数量>=2 then
           常规提示(id,"#Y最多可用锁定2个技能")
           return
        end
        self.数据[编号].赐福锁定[tonumber(内容.编号)]=true
        发送数据(连接id,55,{召唤兽=self.数据,道具=玩家数据[id].道具:索要道具2(id)})
    elseif 请求类型=="解锁技能" then
        if 编号==nil or 编号==0 then
           常规提示(id,"#Y请先选中宝宝后在操作")
           return
        elseif 内容.编号==nil or tonumber(内容.编号)==nil or tonumber(内容.编号)==0 then
          常规提示(id,"#Y数据错误,请重新操作")
           return
        end
        if self.数据[编号].赐福锁定==nil then
            self.数据[编号].赐福锁定={}
        end
        self.数据[编号].赐福锁定[tonumber(内容.编号)]=nil
        发送数据(连接id,55,{召唤兽=self.数据,道具=玩家数据[id].道具:索要道具2(id)})

    elseif 请求类型=="随机赐福" then
            if 编号~=nil and  编号~=0 then
                 if self.数据[编号].赐福锁定==nil then
                    self.数据[编号].赐福锁定={}
                 end
                 local 数量=1
                 local 锁定数量=0
                 for i=1,4 do
                      if self.数据[编号].赐福锁定[i] then
                          锁定数量=锁定数量+1
                      end
                 end
                if 锁定数量==1 then
                    数量=5
                elseif 锁定数量>=2 then
                    数量=25
                else
                    数量=1
                end
                if 玩家数据[id].道具:消耗背包道具(id,"仙露丸子",数量) then
                    local 高级技能={高级毒=true,高级夜战=true,高级反震=true,高级吸血=true,高级连击=true,高级飞行=true,高级隐身=true,
                         高级感知=true,高级再生=true,高级冥思=true,高级驱鬼=true,高级慧根=true,高级必杀=true,高级幸运=true,
                          高级神迹=true,高级招架=true,高级永恒=true,高级敏捷=true,高级偷袭=true,高级强力=true,高级防御=true,
                         高级盾气=true,高级合纵=true,高级魔之心=true,奔雷咒=true,泰山压顶=true,水漫金山=true,高级驱怪=true,
                         地狱烈火=true,高级进击必杀=true,高级进击法暴=true,高级法术连击=true,高级法术暴击=true,
                         高级法术波动=true,壁垒击破=true,高级法术抵抗=true,高级精神集中=true,高级否定信仰=true,
                         高级雷属性吸收=true,高级土属性吸收=true,高级水属性吸收=true,高级火属性吸收=true}

                         if self.数据[编号].临时赐福~=nil then
                              for i=1,4 do
                                   if self.数据[编号].临时赐福[i]~=nil and 高级技能[self.数据[编号].临时赐福[i]] then
                                      高级技能[self.数据[编号].临时赐福[i]]=nil
                                   end
                              end
                         else
                              self.数据[编号].临时赐福={}
                         end
                         local 临时技能 = {}
                         for k,v in pairs(高级技能) do
                             临时技能[#临时技能+1]=k
                         end
                         临时技能=删除重复(临时技能)
                         local 临时获得={}
                         for i=1,4 do
                             if self.数据[编号].赐福锁定==nil  or (self.数据[编号].赐福锁定~=nil and self.数据[编号].赐福锁定[i]==nil) or self.数据[编号].临时赐福[i]==nil then
                                  local 随机技能=取随机数(1,#临时技能)
                                  self.数据[编号].临时赐福[i]=临时技能[随机技能]
                                  table.remove(临时技能,随机技能)
                             end
                             临时获得[i]=self.数据[编号].临时赐福[i]
                         end
                         if 取随机数()<=8 then
                              玩家数据[id].道具:给予道具(id,"仙露小丸子",1)
                             常规提示(id,"#Y恭喜你获得1个#R仙露小丸子")
                         elseif 取随机数()<=2  then
                              local 获得名称=临时获得[取随机数(1,4)]
                              local 超级名称=获得名称
                              if 获得名称=="奔雷咒"or 获得名称=="泰山压顶"or 获得名称=="水漫金山"or 获得名称=="壁垒击破"or 获得名称=="地狱烈火" then
                                  超级名称="超级"..获得名称
                              else
                                  if string.find(获得名称, "高级")~= nil then
                                    local 临时名称=分割文本(获得名称, "高级")
                                     超级名称="超级"..临时名称[2]
                                end
                              end
                              玩家数据[id].道具:给予道具(id,"超级魔兽要诀",nil,超级名称)
                              发送数据(连接id,161,{技能=超级名称})
                         end
                        发送数据(连接id,58,{技能=self.数据[编号].临时赐福,道具=玩家数据[id].道具:索要道具2(id)})
                end
          end



   end






end
function 召唤兽处理类:加载数据(账号,数字id)
  self.数字id=数字id
  self.数据=table.loadstring(读入文件([[data/]]..账号..[[/]]..数字id..[[/召唤兽.txt]]))
  for i=1,#self.数据 do
    if self.数据[i].统御属性 == nil then
       self.数据[i].统御属性={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
    end
    if self.数据[i].加点记录 == nil then
        self.数据[i].加点记录={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
    end
    if self.数据[i].初始属性 == nil then
       self.数据[i].初始属性 = self:重置初始属性(self.数据[i].种类)
    end
    local  是否超过 = false
    if self.数据[i].灵性 ==nil then self.数据[i].灵性 = 0 end
    for n=1,5 do
        local 总属性 = self.数据[i].初始属性[self.属性范围[n]]+self.数据[i].等级*5+self.数据[i].灵性*2+self.数据[i].等级
        if  self.数据[i][self.属性范围[n]]>总属性 then
          是否超过 = true
        end
    end
    if 是否超过 or self.数据[i].等级>185 then
        if self.数据[i].等级>185 then
            self.数据[i].等级=185
        end
        for n=1,5 do
            self.数据[i].加点记录[self.属性范围[n]] = 0
        end
        self.数据[i].潜力 = self.数据[i].等级*5+self.数据[i].灵性*2
    end
    if 玩家数据[数字id].角色.数据.参战宝宝.认证码  ~= nil and self.数据[i].认证码==玩家数据[数字id].角色.数据.参战宝宝.认证码 then
      if 玩家数据[数字id].角色.数据.参战宝宝.等级-10 > 玩家数据[数字id].角色.数据.等级+0 then
        玩家数据[数字id].角色.数据.参战宝宝={}
        self.数据[i].参战信息= nil
        玩家数据[数字id].角色.数据.参战信息=nil
      else
        玩家数据[数字id].角色.数据.参战宝宝={}
        玩家数据[数字id].角色.数据.参战宝宝=self.数据[i]
        玩家数据[数字id].角色.数据.参战信息=1
        self.数据[i].参战信息=1
      end
    end
    self:刷新信息(i)
  end
end


function 召唤兽处理类:是否携带上限()
  if #self.数据>=玩家数据[self.数字id].角色.数据.携带宠物 then
     return true
  end
return false
end


function 召唤兽处理类:重置初始属性(类型)
   if 类型 == "野怪"  then
    return {
          体质=取随机数(5,15),
          魔力=取随机数(5,15),
          力量=取随机数(5,15),
          耐力=取随机数(5,15),
          敏捷=取随机数(5,15)
            }
  elseif 类型 == "宝宝" then
    return{
          体质=10,
          魔力=10,
          力量=10,
          耐力=10,
          敏捷=10
            }
  elseif 类型 == "变异" or 类型 == "孩子" then
    return{
          体质=15,
          魔力=15,
          力量=15,
          耐力=15,
          敏捷=15
            }
  elseif 类型 == "神兽" then
       return{
          体质=20,
          魔力=20,
          力量=20,
          耐力=20,
          敏捷=20
            }
  end
end



 function 召唤兽处理类:添加召唤兽(模型,名称,类型,物法,等级,技能组,专用,不可交易)
      local 编号 = #self.数据+1
      self.生成数据 = {}
      if 物法 == nil  then
        if 类型~="神兽" then
          self.数据[编号]=self:置新对象(模型,名称,类型)
        else
          self.数据[编号]=self:置神兽对象(模型,名称)
        end
      else
        self.数据[编号]=self:置神兽对象(模型,名称,物法)
      end

      if 不可交易~= nil then
          self.数据[编号].不可交易=不可交易
      else
          self.数据[编号].不可交易=false
      end

      if 专用~= nil then
         self.数据[编号].专用=专用
      end

      if 等级~=nil and 等级~=0 then
        self.数据[编号].等级 = 等级
        for n=1,5 do
            self.数据[编号].加点记录[self.属性范围[n]]=0
        end
        self.数据[编号].潜力=self.数据[编号].等级*5+self.数据[编号].灵性*2
      end

      if 技能组~=nil then
        self.数据[编号].技能 =DeepCopy(技能组)
        self.数据[编号].技能 =删除重复(self.数据[编号].技能)
      end

      self:刷新信息(编号,"1")
      发送数据(玩家数据[self.数字id].连接id,17.1,self.数据)
end




function 召唤兽处理类:置新对象(模型,名称,类型)
  self.生成数据 = {}
  if 类型 == "神兽"  then
   return self:置神兽对象(模型,名称)
  end
  local n = 取宝宝(模型)
  if n[1] == nil  then
    self.生成数据.模型 = 模型
    self.生成数据.等级 =  0
    self.生成数据.种类 = 类型
    self.生成数据.名称 = 名称 or 模型
    self.生成数据.攻击资质 = 900
    self.生成数据.防御资质 = 900
    self.生成数据.体力资质 = 1500
    self.生成数据.法力资质 = 1000
    self.生成数据.速度资质 = 900
    self.生成数据.躲闪资质 = 900
    self.生成数据.技能 =  {}
    self.生成数据.成长 = 0.9
    self.生成数据.参战等级 =  0
    self.生成数据.五行 = 五行_[取随机数(1,5)]
    self.生成数据.内丹 ={内丹上限=math.floor(self.生成数据.参战等级 / 35)+1,可用内丹=math.floor(self.生成数据.参战等级 / 35)+1}
    self.生成数据.内丹数据={}
    self.生成数据.天生技能 ={}
    self.生成数据.忠诚 = 100
    self.生成数据.体质 = 0
    self.生成数据.魔力 = 0
    self.生成数据.力量 = 0
    self.生成数据.耐力 = 0
    self.生成数据.敏捷 = 0
    self.生成数据.最大气血 = 0
    self.生成数据.最大魔法 = 0
    self.生成数据.伤害 = 0
    self.生成数据.防御 = 0
    self.生成数据.灵力 = 0
    self.生成数据.速度 = 0
    self.生成数据.气血 = 0
    self.生成数据.魔法 = 0
    self.生成数据.法伤 = 0
    self.生成数据.法防 = 0
    self.生成数据.潜力 = 0
    self.生成数据.灵性 = 0
    self.生成数据.进阶 = false
    self.生成数据.仙露上限 = 7
    self.生成数据.特性 = "无"
    self.生成数据.特性几率 = 0
    self.生成数据.初始属性={
          体质=10,
          魔力=10,
          力量=10,
          耐力=10,
          敏捷=10
            }
    self.生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
    self.生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
    self.生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
    self.生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
    self.生成数据.饰品 = nil
    self.生成数据.装备 = {}
    self.生成数据.当前经验=0
    self.生成数据.最大经验=50
    self.生成数据.认证码=取唯一识别码(self.数字id)
    return self.生成数据
  end
  self.生成数据.等级 =  0
  self.生成数据.模型 = 模型
  self.生成数据.种类 = 类型 or "野怪"
  self.生成数据.寿命=5000
  self.生成数据.天生技能 ={}
  self.生成数据.体质 = 0
  self.生成数据.魔力 = 0
  self.生成数据.力量 = 0
  self.生成数据.耐力 = 0
  self.生成数据.敏捷 = 0
  self.生成数据.最大气血 = 0
  self.生成数据.最大魔法 = 0
  self.生成数据.伤害 = 0
  self.生成数据.防御 = 0
  self.生成数据.灵力 = 0
  self.生成数据.速度 = 0
  self.生成数据.气血 = 0
  self.生成数据.魔法 = 0
  self.生成数据.法伤 = 0
  self.生成数据.法防 = 0
  self.生成数据.潜力 = 0
  self.生成数据.忠诚 = 100
  local 波动上限 = 1
  local 能力 = 0
  if self.生成数据.种类 == "野怪"  then
     能力 = 0.725
     波动上限 = 0.95
    self.生成数据.忠诚 = 80
    self.生成数据.初始属性={
          体质=取随机数(5,15),
          魔力=取随机数(5,15),
          力量=取随机数(5,15),
          耐力=取随机数(5,15),
          敏捷=取随机数(5,15)
            }
  elseif self.生成数据.种类 == "宝宝" then
    能力 = 0.925
    波动上限 = 1.1
    self.生成数据.初始属性={
          体质=10,
          魔力=10,
          力量=10,
          耐力=10,
          敏捷=10
            }
  elseif self.生成数据.种类 == "变异" or self.生成数据.种类 == "孩子" then
    能力 = 1.05
    波动上限 = 1.2
    self.生成数据.初始属性={
          体质=15,
          魔力=15,
          力量=15,
          耐力=15,
          敏捷=15
            }
  end
  self.生成数据.名称 = 名称 or 模型
  self.生成数据.参战等级 = n[1]
  if self.生成数据.名称 == "超级大熊猫" then
    self.生成数据.攻击资质= n[2]
    self.生成数据.防御资质= n[3]
    self.生成数据.体力资质= n[4]
    self.生成数据.法力资质= n[5]
    self.生成数据.速度资质= n[6]
    self.生成数据.躲闪资质= n[7]
  else
    self.生成数据.攻击资质= math.ceil(n[2]*取随机小数(能力,波动上限))
    self.生成数据.防御资质= math.ceil(n[3]*取随机小数(能力,波动上限))
    self.生成数据.体力资质= math.ceil(n[4]*取随机小数(能力,波动上限))
    self.生成数据.法力资质= math.ceil(n[5]*取随机小数(能力,波动上限))
    self.生成数据.速度资质= math.ceil(n[6]*取随机小数(能力,波动上限))
    self.生成数据.躲闪资质= math.ceil(n[7]*取随机小数(能力,波动上限))
  end
  local jn = n[9]
  local jn0 = {}
  local cz1 = 取随机数(1,100)
  if cz1 < 30 then
    self.生成数据.成长 = n[8][1]
  elseif cz1 > 30  and cz1 < 60 then
    self.生成数据.成长 = n[8][2]
  elseif cz1 > 60  and cz1 < 80 then
    self.生成数据.成长 = n[8][3]
  elseif cz1 > 80  and cz1 < 95 then
    self.生成数据.成长 = n[8][4]
  elseif cz1 > 95  and cz1 < 100 then
    self.生成数据.成长 = n[8][5]
  end
  if self.生成数据.成长 == 0 or self.生成数据.成长 == nil  then
    self.生成数据.成长 = n[8][1]
  end

  if  self.生成数据.名称 == "超级大熊猫" then
    self.生成数据.技能=DeepCopy(n[9])
  else
    for q=1,#jn do
      table.insert(jn0, jn[取随机数(1,#jn)])
    end
    self.生成数据.技能={}
    jn0 = 删除重复(jn0)
    for j=1,#jn0 do
      self.生成数据.技能[j] = jn0[j]
    end
  end
  self.生成数据.五行 = 五行_[取随机数(1,5)]
  if n.染色方案 ~= nil then
    self.生成数据.染色方案 = n.染色方案
    self.生成数据.染色组 = {1,0}
  end
  if 类型=="变异" then
    self.生成数据.变异=true
    if 染色信息[模型]~=nil then
      self.生成数据.染色方案 = 染色信息[模型].id
      self.生成数据.染色组 = {染色信息[模型].方案[1],染色信息[模型].方案[2]}
    end
  end
  self.生成数据.灵性 = 0
  self.生成数据.进阶 = false
  self.生成数据.仙露上限 = 7
  self.生成数据.特性 = "无"
  self.生成数据.特性几率 = 0
  self.生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
  self.生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  self.生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  self.生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
  self.生成数据.饰品 = nil
  self.生成数据.装备 = {}
  self.生成数据.当前经验=0
  self.生成数据.最大经验=50
  self.生成数据.内丹数据 = {}
  self.生成数据.内丹 ={内丹上限=math.floor(self.生成数据.参战等级 / 35)+1,可用内丹=math.floor(self.生成数据.参战等级 / 35)+1}
  self.生成数据.认证码=取唯一识别码(self.数字id)
return  self.生成数据

end



function 召唤兽处理类:置神兽对象(模型,名称,物法)
 self.生成数据 = {}
  local 宝宝名称=模型
  if 物法 ~= nil then
         宝宝名称 = 模型..物法
     else
      if 取随机数()<=50 then
         宝宝名称 = 模型.."(法术型)"
        else
         宝宝名称 = 模型.."(物理型)"
        end
     end
  local n = 取商城宝宝(宝宝名称)
  self.生成数据.模型 = 模型
  self.生成数据.等级 =  0
  self.生成数据.参战等级 = n[1]
  self.生成数据.种类 = "神兽"
  self.生成数据.名称 = 名称 or 模型
  self.生成数据.忠诚 = 100
  self.生成数据.体质 = 0
  self.生成数据.魔力 = 0
  self.生成数据.力量 = 0
  self.生成数据.耐力 = 0
  self.生成数据.敏捷 = 0
  self.生成数据.最大气血 = 0
  self.生成数据.最大魔法 = 0
  self.生成数据.伤害 = 0
  self.生成数据.防御 = 0
  self.生成数据.灵力 = 0
  self.生成数据.速度 = 0
  self.生成数据.气血 = 0
  self.生成数据.魔法 = 0
  self.生成数据.法伤 = 0
  self.生成数据.法防 = 0
  self.生成数据.寿命=10000
  self.生成数据.攻击资质= n[2]
  self.生成数据.防御资质= n[3]
  self.生成数据.体力资质= n[4]
  self.生成数据.法力资质= n[5]
  self.生成数据.速度资质= n[6]
  self.生成数据.躲闪资质= n[7]
  self.生成数据.技能=DeepCopy(n[9])
  self.生成数据.天生技能 = DeepCopy(n[11])
  self.生成数据.成长 = n[8]
  self.生成数据.潜力 = 0
  self.生成数据.五行 = 五行_[取随机数(1,5)]
  if self.生成数据.天生技能 ~=nil and self.生成数据.天生技能[1]~=nil  then
   for i = 1, #self.生成数据.天生技能 do
    self.生成数据.技能[#self.生成数据.技能+1] = self.生成数据.天生技能[i]
   end
  end
  self.生成数据.灵性 = 0
  self.生成数据.进阶 = false
  self.生成数据.仙露上限 = 7
  self.生成数据.特性 = "无"
  self.生成数据.特性几率 = 0
  self.生成数据.初始属性={
        体质=20,
        魔力=20,
        力量=20,
        耐力=20,
        敏捷=20
      }
  self.生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
  self.生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  self.生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  self.生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
  self.生成数据.饰品 = nil
  self.生成数据.装备 = {}
  self.生成数据.当前经验=0
  self.生成数据.最大经验=50
  self.生成数据.内丹数据 = {}
  self.生成数据.内丹 ={内丹上限=6,可用内丹=6}
  self.生成数据.认证码=取唯一识别码(self.数字id)

 return self.生成数据
end




-- function 召唤兽处理类:图鉴属性()
--   local 编号
--   if 玩家数据[self.数字id].角色.图鉴刷新时间 == nil then
--     玩家数据[self.数字id].角色.图鉴刷新时间 = 0
--   end
--   if os.time()-玩家数据[self.数字id].角色.图鉴刷新时间<10 then
--     常规提示(self.数字id,"#Y每次刷新时间间隔10秒")
--     return
--   end
--   if 玩家数据[self.数字id].召唤兽.数据[1]~=nil then
--     for h,v in pairs(玩家数据[self.数字id].召唤兽.数据) do
--       编号=h
--       if self.数据[编号].宠物图鉴属性==nil then
--           self.数据[编号].宠物图鉴属性={攻击资质=0,防御资质=0,体力资质=0,法力资质=0,速度资质=0,躲闪资质=0,成长=0,体质=0,魔力=0,力量=0,耐力=0,敏捷=0,最大气血1=0,最大气血2=0,速度1=0,最大魔法=0,伤害=0,防御=0,速度2=0,灵力=0,速度3=0}
--       end
--       local 发送数据 = {攻击资质=0,防御资质=0,体力资质=0,法力资质=0,速度资质=0,躲闪资质=0,成长=0,体质=0,魔力=0,力量=0,耐力=0,敏捷=0,最大气血1=0,最大气血2=0,速度1=0,最大魔法=0,伤害=0,防御=0,速度2=0,灵力=0,速度3=0}
--       local 地图名称 = {[1]={[3]="东海湾",[1]="体质",[2]=5}
--                       ,[2]={[3]="江南野外",[1]="最大气血1",[2]=40}
--                       ,[3]={[3]="大雁塔",[1]="攻击资质",[2]=10}
--                       ,[4]={[3]="大唐国境",[1]="魔力",[2]=5}
--                       ,[5]={[3]="大唐境外",[1]="力量",[2]=5}
--                       ,[6]={[3]="魔王寨",[1]="最大气血2",[2]=40}
--                       ,[7]={[3]="普陀山",[1]="速度1",[2]=5}
--                       ,[8]={[3]="盘丝岭",[1]="最大魔法",[2]=40}
--                       ,[9]={[3]="狮驼岭",[1]="伤害",[2]=5}
--                       ,[10]={[3]="西牛贺州",[1]="防御",[2]=5}
--                       ,[11]={[3]="花果山",[1]="速度2",[2]=5}
--                       ,[12]={[3]="海底迷宫",[1]="耐力",[2]=5}
--                       ,[13]={[3]="地狱迷宫",[1]="防御资质",[2]=10}
--                       ,[14]={[3]="北俱芦洲",[1]="敏捷",[2]=5}
--                       ,[15]={[3]="龙窟",[1]="体力资质",[2]=10}
--                       ,[16]={[3]="凤巢",[1]="法力资质",[2]=10}
--                       ,[17]={[3]="无名鬼蜮",[1]="速度3",[2]=5}
--                       ,[18]={[3]="小西天",[1]="成长",[2]=0.05}
--                       ,[19]={[3]="女娲神迹",[1]="灵力",[2]=5}
--                       ,[20]={[3]="小雷音寺",[1]="速度资质",[2]=10}
--                       ,[21]={[3]="蓬莱仙岛",[1]="攻击资质",[2]=5}
--                       ,[22]={[3]="月宫",[1]="攻击资质",[2]=5}
--                       ,[23]={[3]="蟠桃园",[1]="攻击资质",[2]=5}
--                       ,[24]={[3]="墨家禁地",[1]="攻击资质",[2]=5}
--                       ,[25]={[3]="解阳山",[1]="攻击资质",[2]=5}
--                       ,[26]={[3]="子母河底",[1]="攻击资质",[2]=5}
--                       ,[27]={[3]="麒麟山",[1]="法力资质",[2]=5}
--                       ,[28]={[3]="碗子山",[1]="法力资质",[2]=5}
--                       ,[29]={[3]="波月洞",[1]="法力资质",[2]=5}
--                       ,[30]={[3]="柳林坡",[1]="法力资质",[2]=5}
--                       ,[31]={[3]="比丘国",[1]="法力资质",[2]=5}
--                       ,[32]={[3]="须弥东界",[1]="法力资质",[2]=5}}



--       if 图鉴系统[self.数字id]~=nil then
--         for n=1,32 do
--           if 图鉴系统[self.数字id][地图名称[n][3]].激活==101  then
--             发送数据[地图名称[n][1]] = 发送数据[地图名称[n][1]]+ 地图名称[n][2]*1
--           elseif 图鉴系统[self.数字id][地图名称[n][3]].激活==102 then
--             发送数据[地图名称[n][1]] = 发送数据[地图名称[n][1]]+ 地图名称[n][2]*2
--           elseif 图鉴系统[self.数字id][地图名称[n][3]].激活==103 then
--             发送数据[地图名称[n][1]] = 发送数据[地图名称[n][1]]+ 地图名称[n][2]*3
--           end
--         end
--       end



--       local 全激活 = 0
--       local 数据 = 发送数据
--       for i,v in pairs(图鉴系统[self.数字id]) do
--         if 图鉴系统[self.数字id][i].激活==103 then
--           全激活 = 全激活+1
--         end
--       end
--       if 全激活==32 then
--           for t,v in pairs(发送数据) do
--             数据[t]=发送数据[t]+ 发送数据[t]/3
--           end
--         end
--       玩家数据[self.数字id].角色.图鉴刷新时间 =os.time()
--       self:刷新信息(编号,"66",数据)
--     end
--   end
-- end






function 召唤兽处理类:刷新信息(编号,是否,图鉴)
 if 编号==nil or self.数据[编号]==nil then return end
  -- if self.数据[编号].宠物图鉴属性==nil then
  --   self.数据[编号].宠物图鉴属性={攻击资质=0,防御资质=0,体力资质=0,法力资质=0,速度资质=0,躲闪资质=0,成长=0,体质=0,魔力=0,力量=0,耐力=0,敏捷=0,最大气血1=0,最大气血2=0,速度1=0,最大魔法=0,伤害=0,防御=0,速度2=0,灵力=0,速度3=0}
  -- end

  -- if 是否=="66" then
  --     self.数据[编号].宠物图鉴属性= 图鉴
  -- end
  self.数据[编号].饰品体资=0
  self.数据[编号].饰品攻资=0
  self.数据[编号].饰品法资=0
  self.数据[编号].饰品防资=0
  self.数据[编号].饰品速资=0
  self.数据[编号].饰品躲资=0
  if self.数据[编号].饰品~= nil then
     self.数据[编号].饰品体资=math.floor(self.数据[编号].体力资质*0.05)
     self.数据[编号].饰品攻资=math.floor(self.数据[编号].攻击资质*0.1)
     self.数据[编号].饰品法资=math.floor(self.数据[编号].法力资质*0.1)
     self.数据[编号].饰品防资=math.floor(self.数据[编号].防御资质*0.1)
     self.数据[编号].饰品速资=math.floor(self.数据[编号].速度资质*0.1)
     self.数据[编号].饰品躲资=math.floor(self.数据[编号].躲闪资质*0.1)
  end


 -- local 总体力资质 = self.数据[编号].体力资质 + self.数据[编号].饰品体资 + self.数据[编号].宠物图鉴属性.体力资质
 -- local 总攻击资质 = self.数据[编号].攻击资质 + self.数据[编号].饰品攻资 + self.数据[编号].宠物图鉴属性.攻击资质
 -- local 总法力资质 = self.数据[编号].法力资质 + self.数据[编号].饰品法资 + self.数据[编号].宠物图鉴属性.法力资质
 -- local 总防御资质 = self.数据[编号].防御资质 + self.数据[编号].饰品防资 + self.数据[编号].宠物图鉴属性.防御资质
 -- local 总速度资质 = self.数据[编号].速度资质 + self.数据[编号].饰品速资 + self.数据[编号].宠物图鉴属性.速度资质
 -- local 总躲闪资质 = self.数据[编号].躲闪资质 + self.数据[编号].饰品躲资 + self.数据[编号].宠物图鉴属性.躲闪资质

 local 总体力资质 = self.数据[编号].体力资质 + self.数据[编号].饰品体资
 local 总攻击资质 = self.数据[编号].攻击资质 + self.数据[编号].饰品攻资
 local 总法力资质 = self.数据[编号].法力资质 + self.数据[编号].饰品法资
 local 总防御资质 = self.数据[编号].防御资质 + self.数据[编号].饰品防资
 local 总速度资质 = self.数据[编号].速度资质 + self.数据[编号].饰品速资
 local 总躲闪资质 = self.数据[编号].躲闪资质 + self.数据[编号].饰品躲资
 for n=1,5 do
     self.数据[编号][self.属性范围[n]]=self.数据[编号].初始属性[self.属性范围[n]]+self.数据[编号].加点记录[self.属性范围[n]]+self.数据[编号].等级
 end
-- local 总体质 = self.数据[编号].体质+self.数据[编号].进阶属性.体质+self.数据[编号].统御属性.体质+ self.数据[编号].宠物图鉴属性.体质
-- local 总魔力 = self.数据[编号].魔力+self.数据[编号].进阶属性.魔力+self.数据[编号].统御属性.魔力+ self.数据[编号].宠物图鉴属性.魔力
-- local 总力量 = self.数据[编号].力量+self.数据[编号].进阶属性.力量+self.数据[编号].统御属性.力量+ self.数据[编号].宠物图鉴属性.力量
-- local 总耐力 = self.数据[编号].耐力+self.数据[编号].进阶属性.耐力+self.数据[编号].统御属性.耐力+ self.数据[编号].宠物图鉴属性.耐力
-- local 总敏捷 = self.数据[编号].敏捷+self.数据[编号].进阶属性.敏捷+self.数据[编号].统御属性.敏捷+ self.数据[编号].宠物图鉴属性.敏捷
-- local 总成长 = self.数据[编号].成长 + self.数据[编号].宠物图鉴属性.成长

local 总体质 = self.数据[编号].体质+self.数据[编号].进阶属性.体质+self.数据[编号].统御属性.体质
local 总魔力 = self.数据[编号].魔力+self.数据[编号].进阶属性.魔力+self.数据[编号].统御属性.魔力
local 总力量 = self.数据[编号].力量+self.数据[编号].进阶属性.力量+self.数据[编号].统御属性.力量
local 总耐力 = self.数据[编号].耐力+self.数据[编号].进阶属性.耐力+self.数据[编号].统御属性.耐力
local 总敏捷 = self.数据[编号].敏捷+self.数据[编号].进阶属性.敏捷+self.数据[编号].统御属性.敏捷
local 总成长 = self.数据[编号].成长

if 服务端参数.宠物仿官 then
    self.数据[编号].最大气血 =  math.ceil(self.数据[编号].等级 * 总体力资质 /1000 + 总体质 * 总成长 * 6)+ self.数据[编号].装备属性.气血
    self.数据[编号].最大魔法 = math.ceil(self.数据[编号].等级 * 总法力资质 /500 + 总魔力 * 总成长 * 3)+ self.数据[编号].装备属性.魔法
    self.数据[编号].伤害 =math.ceil(self.数据[编号].等级 * 总攻击资质 * (13.8+10*总成长)/7500 + 总力量 * 总成长+ self.数据[编号].装备属性.命中/3) + self.数据[编号].装备属性.伤害
    self.数据[编号].防御 =math.ceil(self.数据[编号].等级 * 总防御资质 *(4.8+9.25*总成长)/7500 + 总耐力 * 总成长 * 1.33) + self.数据[编号].装备属性.防御
    self.数据[编号].灵力 = math.ceil(self.数据[编号].等级 * 总法力资质 /3400  +总魔力 * 总成长 * 0.7 + 总力量*0.4+ 总体质 *0.3+总耐力*0.2) + self.数据[编号].装备属性.灵力
    self.数据[编号].速度 = math.ceil(总敏捷 * 总速度资质/1000) + self.数据[编号].装备属性.速度
    self.数据[编号].躲闪 = math.ceil(self.数据[编号].等级 * 总躲闪资质 * 0.000845 + 总敏捷 * 总成长 * 1.3)
else
    -- self.数据[编号].最大气血 = math.ceil(self.数据[编号].等级 * 总体力资质 * 0.004 + 总体质 * 总成长 * 7.5)+ self.数据[编号].装备属性.气血
    -- self.数据[编号].最大魔法 = math.ceil(self.数据[编号].等级 * 总法力资质 * 0.003 + 总魔力 * 总成长 * 6)+ self.数据[编号].装备属性.魔法
    -- self.数据[编号].伤害 = math.ceil(self.数据[编号].等级 * 总攻击资质 * 0.0035 + 总力量 * 总成长 * 2.4  + self.数据[编号].装备属性.命中/4+ 总体质 *0.3) + self.数据[编号].装备属性.伤害
    -- self.数据[编号].防御 = math.ceil(self.数据[编号].等级 * 总防御资质 * 0.003 + 总耐力 * 总成长 * 1.6) + self.数据[编号].装备属性.防御
    -- self.数据[编号].速度 = math.ceil(self.数据[编号].等级 * 总速度资质 * 0.0025 + 总敏捷 * 总成长 * 1.3) + self.数据[编号].装备属性.速度
    -- self.数据[编号].灵力 = math.ceil(self.数据[编号].等级 * 总法力资质 * 0.0015 + 总魔力 * 总成长 * 1.8 + 总力量*0.3+ 总体质 *0.2+总耐力*0.1) + self.数据[编号].装备属性.灵力
    -- self.数据[编号].躲闪 = math.ceil(self.数据[编号].等级 * 总躲闪资质 * 0.001 + 总敏捷 * 总成长 * 1.3)

    self.数据[编号].最大气血 = math.ceil(self.数据[编号].等级 * 总体力资质 * 0.003 + 总体质 * 总成长 * 8)+ self.数据[编号].装备属性.气血
    self.数据[编号].最大魔法 = math.ceil(self.数据[编号].等级 * 总法力资质 * 0.002 + 总魔力 * 总成长 * 6)+ self.数据[编号].装备属性.魔法
    self.数据[编号].伤害 = math.ceil(self.数据[编号].等级 * 总攻击资质 * 0.003 + 总力量 * 总成长 * 2.4  + self.数据[编号].装备属性.命中/4+ 总体质 *0.3) + self.数据[编号].装备属性.伤害
    self.数据[编号].防御 = math.ceil(self.数据[编号].等级 * 总防御资质 * 0.0025 + 总耐力 * 总成长 * 1.6 ) + self.数据[编号].装备属性.防御
    self.数据[编号].速度 = math.ceil(self.数据[编号].等级 * 总速度资质 * 0.0025 + 总敏捷 * 总成长 * 1.3) + self.数据[编号].装备属性.速度
    self.数据[编号].灵力 = math.ceil(self.数据[编号].等级 * 总法力资质 * 0.0009 + 总魔力 * 总成长 * 1.3 + 总力量*0.3+ 总体质 *0.2+总耐力*0.1) + self.数据[编号].装备属性.灵力
    self.数据[编号].躲闪 = math.ceil(self.数据[编号].等级 * 总躲闪资质 * 0.001 + 总敏捷 * 总成长 * 1.3)



end





  self.数据[编号].最大气血 = self.数据[编号].最大气血 + 玩家数据[self.数字id].角色:取强化技能等级("宠物气血")*45
  self.数据[编号].伤害 = self.数据[编号].伤害 + 玩家数据[self.数字id].角色:取强化技能等级("宠物伤害")*8
  self.数据[编号].防御 = self.数据[编号].防御 + 玩家数据[self.数字id].角色:取强化技能等级("宠物防御")*6
  self.数据[编号].灵力 = self.数据[编号].灵力 + 玩家数据[self.数字id].角色:取强化技能等级("宠物灵力")*4
  self.数据[编号].速度 = self.数据[编号].速度 + 玩家数据[self.数字id].角色:取强化技能等级("宠物速度")*4


  if  self.数据[编号].等级 <= 185 then
      self.数据[编号].最大经验 = 升级消耗.宠物[self.数据[编号].等级+1]
  end
  if self.数据[编号].内丹数据 ~= nil and self.数据[编号].内丹数据[1] ~= nil then
  for i=1,#self.数据[编号].内丹数据 do
    if self.数据[编号].内丹数据[i].技能=="迅敏" then
        self.数据[编号].伤害=self.数据[编号].伤害+qz((self.数据[编号].等级*0.08)*self.数据[编号].内丹数据[i].等级)
        self.数据[编号].速度=self.数据[编号].速度+qz((self.数据[编号].等级*0.05)*self.数据[编号].内丹数据[i].等级)
      end
      if self.数据[编号].内丹数据[i].技能=="静岳" then
        self.数据[编号].灵力=self.数据[编号].灵力+qz((self.数据[编号].等级*0.04)*self.数据[编号].内丹数据[i].等级)
        self.数据[编号].最大气血=self.数据[编号].最大气血+qz((self.数据[编号].等级*0.4)*self.数据[编号].内丹数据[i].等级)
      end
      if self.数据[编号].内丹数据[i].技能=="矫健" then
        self.数据[编号].最大气血=self.数据[编号].最大气血+qz((self.数据[编号].等级*0.5)*self.数据[编号].内丹数据[i].等级)
        self.数据[编号].速度=self.数据[编号].速度+qz((self.数据[编号].等级*0.05)*self.数据[编号].内丹数据[i].等级)
      end


      if self.数据[编号].内丹数据[i].技能=="灵光" then
        self.数据[编号].灵力=self.数据[编号].灵力+qz((self.数据[编号].魔力*0.02)*self.数据[编号].内丹数据[i].等级)
      end

      if self.数据[编号].内丹数据[i].技能=="阴阳护" then
        self.数据[编号].最大魔法=self.数据[编号].最大魔法+qz((self.数据[编号].等级*0.5)*self.数据[编号].内丹数据[i].等级)
      end


      if self.数据[编号].内丹数据[i].技能=="凛冽气" then
        self.数据[编号].速度=self.数据[编号].速度+qz((self.数据[编号].等级*0.08)*self.数据[编号].内丹数据[i].等级)
      end


      if self.数据[编号].内丹数据[i].技能=="玄武躯" then
        self.数据[编号].最大气血=self.数据[编号].最大气血+qz((self.数据[编号].等级*2)*self.数据[编号].内丹数据[i].等级)
      end
      if self.数据[编号].内丹数据[i].技能=="龙胄铠" then
        self.数据[编号].防御=self.数据[编号].防御+qz((self.数据[编号].等级*0.5)*self.数据[编号].内丹数据[i].等级)
      end
    end
  end
  self.数据[编号].法伤 = self.数据[编号].灵力
  self.数据[编号].法防 = self.数据[编号].灵力*0.7

  if self:取指定技能(编号,"溜之大吉") then
     self.数据[编号].速度=math.floor(self.数据[编号].速度+self.数据[编号].速度*0.2)
  end
  if self:取指定技能(编号,"赴汤蹈火") then
      self.数据[编号].法防 =  self.数据[编号].法防 +162
      self.数据[编号].最大气血 =  self.数据[编号].最大气血 + 540
  end
  if self:取指定技能(编号,"开门见山") then
      self.数据[编号].伤害 =  self.数据[编号].伤害 +100
      self.数据[编号].最大气血 =  self.数据[编号].最大气血 + 380
  end
  if self:取指定技能(编号,"净台妙谛") then
      self.数据[编号].最大气血 =  math.floor(self.数据[编号].最大气血 +总体质*self.数据[编号].成长*2)
  end
  if self:取指定技能(编号,"张弛有道") then
      self.数据[编号].法伤 =  math.floor(self.数据[编号].法伤 +总魔力*0.5)
  end

  if self:取指定技能(编号,"须弥真言") then
      self.数据[编号].法伤 =  math.floor(self.数据[编号].法伤 +总魔力*0.4)
  end
  if self:取指定技能(编号,"灵能激发") then
      self.数据[编号].法伤 =  math.floor(self.数据[编号].法伤*1.1)
  end
  if self:取指定技能(编号,"灵山禅语") then
      self.数据[编号].法防 =  math.floor(self.数据[编号].法防 +总魔力*(self.数据[编号].成长-0.3))
  end
  if self:取指定技能(编号,"义薄云天") then
      self.数据[编号].法伤 =  math.floor(self.数据[编号].法伤+self.数据[编号].等级*0.55)
  end








  if self.数据[编号].气血==nil then self.数据[编号].气血=1 end
  if self.数据[编号].魔法==nil then self.数据[编号].魔法=1 end
  if 是否 == "1" then
    self.数据[编号].气血 = self.数据[编号].最大气血
    self.数据[编号].魔法 = self.数据[编号].最大魔法
  end
  if self.数据[编号].气血 > self.数据[编号].最大气血 then
    self.数据[编号].气血 = self.数据[编号].最大气血
  end
  if self.数据[编号].魔法 > self.数据[编号].最大魔法 then
    self.数据[编号].魔法 = self.数据[编号].最大魔法
  end




end

function 召唤兽处理类:穿戴装备(装备,格子,编号)
  if 装备.气血 ~= nil then
    self.数据[编号].装备属性.气血 = self.数据[编号].装备属性.气血 + (装备.气血 or 0)
  end
  if 装备.魔法 ~= nil then
    self.数据[编号].装备属性.魔法 = self.数据[编号].装备属性.魔法 + (装备.魔法 or 0)
  end
  if 装备.命中 ~= nil then
    self.数据[编号].装备属性.命中 = self.数据[编号].装备属性.命中 + (装备.命中 or 0)
  end
  if 装备.伤害 ~= nil then
    self.数据[编号].装备属性.伤害 = self.数据[编号].装备属性.伤害 + (装备.伤害 or 0)
  end
  if 装备.防御 ~= nil then
    self.数据[编号].装备属性.防御 = self.数据[编号].装备属性.防御 + (装备.防御 or 0)
  end
  if 装备.速度 ~= nil then
    self.数据[编号].装备属性.速度 = self.数据[编号].装备属性.速度 + (装备.速度 or 0)
  end
  if 装备.躲避 ~= nil then
    self.数据[编号].装备属性.躲避 = self.数据[编号].装备属性.躲避 + (装备.躲避 or 0)
  end
  if 装备.灵力 ~= nil then
    self.数据[编号].装备属性.灵力 = self.数据[编号].装备属性.灵力 + (装备.灵力 or 0)
  end
  if 装备.体质 ~= nil then
    self.数据[编号].装备属性.体质 = self.数据[编号].装备属性.体质 + (装备.体质 or 0)
    self.数据[编号].装备属性.气血 = self.数据[编号].装备属性.气血 + (装备.体质 or 0)*5
  end
  if 装备.魔力 ~= nil then
    self.数据[编号].装备属性.魔力 = self.数据[编号].装备属性.魔力 + (装备.魔力 or 0)
    self.数据[编号].装备属性.魔法 = self.数据[编号].装备属性.魔法 + (装备.魔力 or 0)*5
    self.数据[编号].装备属性.灵力 = self.数据[编号].装备属性.灵力 + math.floor(((装备.魔力 or 0)*1.5))
  end
  if 装备.力量 ~= nil then
    self.数据[编号].装备属性.力量 = self.数据[编号].装备属性.力量 + (装备.力量 or 0)
    self.数据[编号].装备属性.伤害 = self.数据[编号].装备属性.伤害 + math.floor(((装备.力量 or 0)*3.5))
  end
  if 装备.耐力 ~= nil then
    self.数据[编号].装备属性.耐力 = self.数据[编号].装备属性.耐力 + (装备.耐力 or 0)
    self.数据[编号].装备属性.防御 = self.数据[编号].装备属性.防御 + math.floor(((装备.耐力 or 0)*2.3))
  end
  if 装备.敏捷 ~= nil then
    self.数据[编号].装备属性.敏捷 = self.数据[编号].装备属性.敏捷 + (装备.敏捷 or 0)
    self.数据[编号].装备属性.速度 = self.数据[编号].装备属性.速度 + math.floor(((装备.敏捷 or 0)*2.3))
  end
  self.数据[编号].装备[格子] = 装备
  if 装备.套装效果 ~= nil then
    local sl = {}
    local ab = true
    self.数据[编号].套装 = self.数据[编号].套装 or {}
    for i=1,#self.数据[编号].套装 do
      if self.数据[编号].套装[i][1] == 装备.套装效果[1] and self.数据[编号].套装[i][2] == 装备.套装效果[2] then
        local abc = false
        local abd = true
        for s=1,#self.数据[编号].套装[i][4] do
          if self.数据[编号].套装[i][4][s] == 格子 then
              abd = false
              break
          end
        end
        if abd then
          table.insert(self.数据[编号].套装[i][4],格子)
          abc = true
        end
        if abc then
          self.数据[编号].套装[i][3] = (self.数据[编号].套装[i][3] or 0) + 1
        end
        ab = false
        break
      end
    end
    if ab then
      table.insert(self.数据[编号].套装,{装备.套装效果[1],装备.套装效果[2],1,{格子}})
    end
  end
  self:刷新信息(编号)
end

function 召唤兽处理类:卸下装备(装备,格子,编号)
  if 装备.气血 ~= nil then
    self.数据[编号].装备属性.气血 = self.数据[编号].装备属性.气血 - (装备.气血 or 0)
  end
  if 装备.魔法 ~= nil then
    self.数据[编号].装备属性.魔法 = self.数据[编号].装备属性.魔法 - (装备.魔法 or 0)
  end
  if 装备.命中 ~= nil then
    self.数据[编号].装备属性.命中 = self.数据[编号].装备属性.命中 - (装备.命中 or 0)
  end
  if 装备.伤害 ~= nil then
    self.数据[编号].装备属性.伤害 = self.数据[编号].装备属性.伤害 - (装备.伤害 or 0)
  end
  if 装备.防御 ~= nil then
    self.数据[编号].装备属性.防御 = self.数据[编号].装备属性.防御 - (装备.防御 or 0)
  end
  if 装备.速度 ~= nil then
    self.数据[编号].装备属性.速度 = self.数据[编号].装备属性.速度 - (装备.速度 or 0)
  end
  if 装备.躲避 ~= nil then
    self.数据[编号].装备属性.躲避 = self.数据[编号].装备属性.躲避 - (装备.躲避 or 0)
  end
  if 装备.灵力 ~= nil then
    self.数据[编号].装备属性.灵力 = self.数据[编号].装备属性.灵力 - (装备.灵力 or 0)
  end
  if 装备.体质 ~= nil then
    self.数据[编号].装备属性.体质 = self.数据[编号].装备属性.体质 - (装备.体质 or 0)
    self.数据[编号].装备属性.气血 = self.数据[编号].装备属性.气血 - (装备.体质 or 0)*5
  end
  if 装备.魔力 ~= nil then
    self.数据[编号].装备属性.魔力 = self.数据[编号].装备属性.魔力 - (装备.魔力 or 0)
    self.数据[编号].装备属性.魔法 = self.数据[编号].装备属性.魔法 - (装备.魔力 or 0)*5
    self.数据[编号].装备属性.灵力 = self.数据[编号].装备属性.灵力 - math.floor(((装备.魔力 or 0)*1.5))
  end
  if 装备.力量 ~= nil then
    self.数据[编号].装备属性.力量 = self.数据[编号].装备属性.力量 - (装备.力量 or 0)
    self.数据[编号].装备属性.伤害 = self.数据[编号].装备属性.伤害 - math.floor(((装备.力量 or 0)*3.5))
  end
  if 装备.耐力 ~= nil then
    self.数据[编号].装备属性.耐力 = self.数据[编号].装备属性.耐力 - (装备.耐力 or 0)
    self.数据[编号].装备属性.防御 = self.数据[编号].装备属性.防御 - math.floor(((装备.耐力 or 0)*2.3))
  end
  if 装备.敏捷 ~= nil then
    self.数据[编号].装备属性.敏捷 = self.数据[编号].装备属性.敏捷 - (装备.敏捷 or 0)
    self.数据[编号].装备属性.速度 = self.数据[编号].装备属性.速度 - math.floor(((装备.敏捷 or 0)*2.3))
  end
  self:刷新信息(编号)
end





function 召唤兽处理类:飞升降级(id)
  for n=1,#self.数据 do
    if self.数据[n].等级>=130 and self.数据[n].种类~="野怪" then
      self.数据[n].等级 = self.数据[n].等级 - 15
      for i=1,5 do
         self.数据[n].加点记录[self.属性范围[i]]=0
      end
      self.数据[n].潜力=self.数据[n].等级*5+self.数据[n].灵性*2
      常规提示(id,"#Y/您的召唤兽#G/"..self.数据[n].名称.."#Y等级已由原来的#R/"..(self.数据[n].等级+15).."#Y/级下降至#R/"..self.数据[n].等级.."#Y/级")
    end
  end
end









function 召唤兽处理类:取指定技能(编号,名称)
  for n=1,#self.数据[编号].技能 do
    if self.数据[编号].技能[n]==名称 then
      return true
    end
  end
  return false
end


function 召唤兽处理类:取存档数据(编号)
  if 编号 ~= nil then
    return self.数据[编号]
  end
  return self.数据
end

function 召唤兽处理类:获取指定数据(编号)
        local 返回数据 = {}
        for k,v in pairs(self.数据[编号]) do
            返回数据[k]=v
        end
        return 返回数据
end

function 召唤兽处理类:放生处理(连接id,序号,id,点数,多角色)
  local 临时编号=self:取编号(点数.序列)
  if 临时编号==0 or self.数据[临时编号]==nil  then
    常规提示(id,"你没有这只召唤兽",多角色)
    return
 elseif  self.数据[临时编号].加锁 then
  常规提示(id,"加锁召唤兽无法放生",多角色)
    return
  elseif self:是否有装备(临时编号) then
    常规提示(id,"请先卸下召唤兽所穿戴的装备",多角色)
    return
  elseif  self.数据[临时编号].统御 ~= nil then
    常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作",多角色)
    return
  else--先判断是否有bb装备
    --       self.数据[临时编号]=nil
    table.remove(self.数据,临时编号) --先抹去参战信息
    if 点数.序列==玩家数据[id].角色.数据.参战宝宝.认证码 then
      玩家数据[id].角色.数据.参战宝宝={}
      if 多角色~=nil then
          发送数据(玩家数据[多角色].连接id,6008,{角色=id,召唤兽=玩家数据[id].召唤兽.数据})
          发送数据(玩家数据[多角色].连接id,6025,{角色=id,召唤兽=玩家数据[id].角色.数据.参战宝宝})
          发送数据(玩家数据[多角色].连接id,6026,{角色=id})
      else
          发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
      end
    end
    常规提示(id,"你的这只召唤兽从你的眼前消失了~~",多角色)
    if 多角色~=nil then
          发送数据(玩家数据[多角色].连接id,6028,{角色=id})
      else
          发送数据(连接id,21,临时编号)
      end
  end
end

function 召唤兽处理类:删除处理(id,临时编号)
  --local 临时编号=self:取编号(点数.序列)
  if 临时编号==0 or self.数据[临时编号]==nil then
    常规提示(id,"你没有这只召唤兽")
    return
  elseif self:是否有装备(临时编号) then
    常规提示(id,"请先卸下召唤兽所穿戴的装备")
    return
  elseif  self.数据[临时编号].统御 ~= nil then
    常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
    return
  else--先判断是否有bb装备
    --       self.数据[临时编号]=nil
    if self.数据[临时编号].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
      玩家数据[id].角色.数据.参战宝宝={}
      发送数据(玩家数据[id].连接id,18,玩家数据[id].角色.数据.参战宝宝)
    end
    table.remove(self.数据,临时编号)--先抹去参战信息
    发送数据(玩家数据[id].连接id,45,临时编号)
  end
end

function 召唤兽处理类:是否有装备(编号)
  if self.数据[编号]==nil then
    return true
  end
  for n=1,3 do
    if  self.数据[编号].装备[n]~=nil then
      return true
    end
  end
  return false
end

function 召唤兽处理类:是否有统御()
  for i=1,#self.数据 do
    if self.数据[i].统御 ~= nil then
      return true
    end
  end
  return false
end


function 召唤兽处理类:耐久处理(id,认证码)
  local 编号 = self:取编号(认证码)
  if self:是否有装备(编号) then
    for n=1,3 do
      if self.数据[编号].装备[n]~=nil and self.数据[编号].装备[n].耐久~=nil then
          self.数据[编号].装备[n].耐久 = self.数据[编号].装备[n].耐久 - 0.0125
          if self.数据[编号].装备[n].耐久 <= 0 then
              self.数据[编号].装备[n].耐久 = 0
              发送数据(玩家数据[id].连接id,7,"#y/你的#r/"..self.数据[编号].装备[n].名称.."#y/因耐久度过低已无法使用")
          end
      end
    end
  end
end

function 召唤兽处理类:取气血差()
  local 数值=0
  for n=1,#self.数据 do
    数值=数值+(self.数据[n].最大气血-self.数据[n].气血)
  end
  return 数值
end

function 召唤兽处理类:取魔法差()
  local 数值=0
  for n=1,#self.数据 do
    数值=数值+(self.数据[n].最大魔法-self.数据[n].魔法)
  end
  return 数值
end

function 召唤兽处理类:取忠诚差()
  local 数值=0
  for n=1,#self.数据 do
    数值=数值+(100-self.数据[n].忠诚)
  end
  return 数值
end



function 召唤兽处理类:合宠处理(连接id,序号,id,内容)
  local 认证码1=内容.序列
  local 认证码2=内容.序列1
  local bb1 = nil
  local bb2 = nil
  for k,v in pairs(self.数据) do
    if v.认证码 == 认证码1 then
      bb1 = k
    end
    if v.认证码 == 认证码2 then
      bb2 = k
    end
  end
  if self.数据[bb1]==nil or self.数据[bb2]==nil then
    return
  end

  if self.数据[bb1].等级<30 or self.数据[bb2].等级<30 then
    常规提示(id,"要炼妖的召唤兽等级未达到30级!")
    return
  end
 if self.数据[bb1].种类=="神兽" or self.数据[bb2].种类=="神兽" then
    常规提示(id,"神兽不可进行此操作")
    return
  end
  -- if self.数据[bb1].种类=="变异" or self.数据[bb2].种类=="变异" then
  --   常规提示(id,"此种召唤兽不可进行此操作")
  --   return
  -- end

  if self.数据[bb1].种类=="孩子" or self.数据[bb2].种类=="孩子" then
   常规提示(id,"孩子不能进行炼化")
    return
  end


 if self:是否有装备(bb1) or self:是否有装备(bb2) then
    常规提示(id,"请先卸下召唤兽所穿戴的装备")
    return
  end
 if self.数据[bb1].参战信息~=nil  or self.数据[bb2].参战信息~=nil  then
    常规提示(id,"召唤兽处于参战状态，请解除参战后再进行此操作")
      return
  end
  if  self.数据[bb1].统御 ~= nil or self.数据[bb2].统御 ~= nil  then
    常规提示(id,"召唤兽处于统御状态,请解除统御后再进行此操作")
    return
  end
  if  self.数据[bb1].进阶 or  self.数据[bb2].进阶 then
    常规提示(id,"进阶的召唤兽无法进行此操作")
    return
  end
  --  if self.数据[bb1].法术认证~=nil or self.数据[bb2].法术认证~=nil then
  --   常规提示(id,"法术认证的宝宝不能合")
  --   return
  -- end


  local 随机类型=""
    随机类型=self.数据[bb1].模型
    local 随机数值=取随机数()
    local 特殊合宠=false
    if 随机数值<=5 then
      特殊合宠=true
      随机类型="泡泡"
    elseif 随机数值<=10 then
      特殊合宠=true
      随机类型="大海龟"
    elseif 随机数值<=30 then
      随机类型=self.数据[bb2].模型
    else
      随机类型=self.数据[bb1].模型
    end
    if 特殊合宠 then
     self.数据[bb1]=self:置新对象(随机类型,随机类型,"宝宝")
     常规提示(id,"恭喜你合出了一只#R/"..随机类型)
     table.remove(self.数据,bb2)
     self:刷新信息(bb1,"1")
    else
      self.数据[bb1].模型=随机类型
      for n=1,#self.资质范围 do
        local 本次资质 = self.数据[bb1][self.资质范围[n]]
        if self.数据[bb1][self.资质范围[n]]>self.数据[bb2][self.资质范围[n]] then
            本次资质 =math.floor(取随机数(self.数据[bb2][self.资质范围[n]]*0.95,self.数据[bb1][self.资质范围[n]]*1.05))
        else
            本次资质 =math.floor(取随机数(self.数据[bb1][self.资质范围[n]]*0.95,self.数据[bb2][self.资质范围[n]]*1.05))
        end
         self.数据[bb1][self.资质范围[n]] = 本次资质
        if self.资质范围[n] == "攻击资质" and self.数据[bb1][self.资质范围[n]]>1650 then
          self.数据[bb1][self.资质范围[n]] = 1650
        elseif  self.资质范围[n] == "防御资质" and self.数据[bb1][self.资质范围[n]]>1650 then
          self.数据[bb1][self.资质范围[n]] = 1650
        elseif  self.资质范围[n] == "体力资质" and self.数据[bb1][self.资质范围[n]]>5500 then
          self.数据[bb1][self.资质范围[n]] = 5500
        elseif  self.资质范围[n] == "法力资质" and self.数据[bb1][self.资质范围[n]]>3000 then
          self.数据[bb1][self.资质范围[n]] = 3000
        elseif  self.资质范围[n] == "速度资质" and self.数据[bb1][self.资质范围[n]]>1550 then
          self.数据[bb1][self.资质范围[n]] = 1550
        elseif  self.资质范围[n] == "躲闪资质" and self.数据[bb1][self.资质范围[n]]>1500 then
          self.数据[bb1][self.资质范围[n]] = 1500
        end
      end
      --计算成长

      local 成长数 = self.数据[bb1].成长
      local 成长加成1 =self.数据[bb1].成长*1000
      local 成长加成2 =self.数据[bb2].成长*1000
      if 成长加成1>成长加成2 then
          成长数=math.floor(取随机数(成长加成2*0.96,成长加成1*1.02))
      else
          成长数=math.floor(取随机数(成长加成1*0.96,成长加成2*1.02))
      end
      成长数 = 成长数/1000
      self.数据[bb1].成长 = 保留小数位数(成长数,3)
      if self.数据[bb1].成长>1.3 then
        self.数据[bb1].成长=1.3
      end
     --计算技能
     if self.数据[bb1].法术认证~=nil  then
        self.数据[bb1].法术认证 = nil
     end
     if self.数据[bb2].法术认证~=nil  then
        self.数据[bb2].法术认证 = nil
     end
     local XX = 取宝宝(self.数据[bb1].模型)
     local 天生自带1 = XX[9]
     local YY = 取宝宝(self.数据[bb2].模型)
     local 天生自带2 = YY[9]
     local 技能数量1 = #self.数据[bb1].技能
     local 技能数量2 = #self.数据[bb2].技能
    local 最小技能 = 0
     local 剩余技能 = 0
     if 技能数量1>技能数量2 then
         最小技能 = 技能数量1
         剩余技能=技能数量2
      else
         最小技能 = 技能数量2
         剩余技能=技能数量1
      end
      --local 成品技能数 = 最小技能
      最小技能 = math.floor(最小技能*0.75)--3/2 =1
      local 成品技能数 = 最小技能+剩余技能
      local 技能增长 = 取随机数()
      if 技能增长<=5 then
        成品技能数= math.floor(技能数量1+ 技能数量2+#天生自带1+#天生自带2)
      elseif 技能增长<=8 and 技能增长>5 then
        成品技能数= math.floor(技能数量1/2+ 技能数量2 + #天生自带1/2+#天生自带2)
      elseif 技能增长<=15 and 技能增长>8 then
        成品技能数=math.floor(技能数量1/2+ 技能数量2/2 + #天生自带1/2+#天生自带2/2)
      elseif 技能增长<=30 and 技能增长>15  then
        成品技能数=math.floor(最小技能 + 剩余技能 + #天生自带1/2+#天生自带2/2)
      elseif 技能增长<=45 and 技能增长>30  then
        成品技能数=math.floor(最小技能 + 剩余技能 + #天生自带1/3+#天生自带2/3)
      end
       local 技能表={}
       if 天生自带1~=nil then
         for n=1,#天生自带1 do
           技能表[#技能表+1]=天生自带1[n]
         end
       end
        if 天生自带2~=nil then
         for n=1,#天生自带2 do
           技能表[#技能表+1]=天生自带2[n]
         end
       end
       for n=1,#self.数据[bb2].技能 do
         技能表[#技能表+1]=self.数据[bb2].技能[n]
       end
      for n=1,#self.数据[bb1].技能 do
        技能表[#技能表+1]=self.数据[bb1].技能[n]
      end
      技能表=删除重复(技能表)
      for n=1,#技能表 do
        技能表[n]={名称=技能表[n],排列=取随机数(1,10000)}
      end
      if 成品技能数> #技能表 then
         成品技能数 = #技能表
      end
      table.sort(技能表,function(a,b) return a.排列>b.排列 end )
      local 技能总数 = 取随机数(最小技能,成品技能数)

      self.数据[bb1].技能={}
      if 技能总数>=20 then
         技能总数=20
      end
      if 技能表~={}  then
          for n=1,技能总数 do
            local 成功几率=100-#self.数据[bb1].技能*4+最小技能
            if 取随机数()<=成功几率 then
              self.数据[bb1].技能[#self.数据[bb1].技能+1]=技能表[n].名称
             end
          end
      end
      --计算等级
      local 等级总数=math.floor(((self.数据[bb1].等级+self.数据[bb2].等级)/2*0.5))
      if 等级总数<0 then
         等级总数=0
      end
      self.数据[bb1].等级=等级总数
      self.数据[bb1].当前经验=0
      self.数据[bb1].最大经验=升级消耗.宠物[self.数据[bb1].等级+1]
      --计算属性点
      local 生成类型=""
      local 随机点数=""
      local 种类="野怪"


      if self.数据[bb1].种类=="野怪" and self.数据[bb2].种类=="宝宝" then
        if 取随机数() <=20 then
          种类="宝宝"
        end
      elseif self.数据[bb1].种类=="宝宝" and self.数据[bb2].种类=="野怪" then
        if 取随机数() <=20 then
          种类="宝宝"
        end
      elseif self.数据[bb1].种类=="宝宝" and self.数据[bb2].种类=="宝宝" then
          种类="宝宝"
      elseif self.数据[bb1].种类=="变异" and self.数据[bb2].种类=="变异" then
          种类="变异"
      elseif self.数据[bb1].种类=="变异" and self.数据[bb2].种类=="宝宝" then
          if 取随机数() <=20 then
              种类="变异"
          else
              种类="宝宝"
          end
      elseif self.数据[bb1].种类=="宝宝" and self.数据[bb2].种类=="变异" then
            if 取随机数() <=20 then
                种类="变异"
            else
                种类="宝宝"
            end

      end
    self.数据[bb1].种类=种类
    self.数据[bb1].等级=0
    for n=1,5 do
        self.数据[bb1].加点记录[self.属性范围[n]]=0
    end
    self.数据[bb1].潜力=self.数据[bb1].等级*5+self.数据[bb1].灵性*2
    self.数据[bb1].初始属性 = self:重置初始属性(self.数据[bb1].种类)
    self.数据[bb1].饰品 = nil
    self.数据[bb1].寿命= 取随机数(4000,7000)
    self.数据[bb1].自动指令=nil
    self.数据[bb1].元宵 = {}
    self.数据[bb1].名称=self.数据[bb1].模型
    local mx参战等级 =取宝宝(self.数据[bb1].模型)
    self.数据[bb1].参战等级 = mx参战等级[1]
    self.数据[bb1].内丹数据 = {}
    self.数据[bb1].内丹 ={内丹上限=math.floor(mx参战等级[1] /35)+1,可用内丹=math.floor(mx参战等级[1]/35)+1}
    local 成品数量 = #self.数据[bb1].技能
    if 成品数量>=10 then
        local 成就提示  = "合宠之路"
        if 成品数量>=20 then
            成就提示  = "合宠神人"
        elseif 成品数量>=18 then
                成就提示  = "合宠达人"
        elseif 成品数量>=16 then
                成就提示  = "合宠专家"
        elseif 成品数量>=14 then
                成就提示  = "合宠高手"
        elseif 成品数量>=12 then
                成就提示  = "合宠小能手"
        end

        local 成就提示1 = "恭喜玩家合出"..成品数量.."技能的召唤兽"
        if not self.数据[bb1].合宠技能 or self.数据[bb1].合宠技能<成品数量 then
            self.数据[bb1].合宠技能=成品数量
            玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
            成就提示1 = "恭喜玩家该召唤兽第一次合出"..成品数量.."技能"
            常规提示(id,"#Y/恭喜你获得了1成就积分")
        end
        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
    end
    table.remove(self.数据,bb2)
    self:刷新信息(bb1,"1")
    常规提示(id,"恭喜你合出了一只#R/"..随机类型)
  end
  发送数据(连接id,16,self.数据)
  发送数据(连接id,26)
end



function 召唤兽处理类:取野外等级差(地图等级,玩家等级)
  local 等级=math.abs(地图等级-玩家等级)
  if 等级<=5 then
    return 1
  elseif 等级<=10 then
    return 0.8
  elseif 等级<=20 then
    return 0.5
  else
    return 0.2
  end
end

function 召唤兽处理类:获得经验(认证码,经验,id,类型,地图等级)
  local 编号=self:取编号(认证码)
  if 编号==0 or self.数据[编号]==nil then return  end
  if self.数据[编号].等级>=玩家数据[id].角色.数据.等级+5 then
    --发送数据(玩家数据[id].连接id,38,{内容="你的召唤兽当前等级已经超过人物等级+5，目前已经无法再获得更多的经验了。"})
    return
  end
  local 临时经验=经验*1
  if 类型=="野外" then
    local 临时参数=self:取野外等级差(self.数据[编号].等级,地图等级)
    临时经验=临时经验*临时参数
  end
  local 倍率=服务端参数.经验获得率
  if 类型=="野外" or 类型=="捉鬼" or 类型=="官职" or 类型=="封妖战斗" or 类型=="种族" or 类型=="门派闯关" or 类型=="初出江湖" or 类型=="悬赏任务" then
    if 玩家数据[id].角色:取任务(2)~=0 then
      倍率=倍率+1
    end
    if 玩家数据[id].角色:取任务(3)~=0 then
      倍率=倍率+1
    end
  end
  临时经验=math.floor(临时经验*倍率)
  self:添加经验(玩家数据[id].连接id,id,编号,临时经验)
end

function 召唤兽处理类:升级(编号,id)
    if self.数据[编号].当前经验>=self.数据[编号].最大经验 then
        self.数据[编号].等级 = self.数据[编号].等级 + 1
        self.数据[编号].体质 = self.数据[编号].体质 + 1
        self.数据[编号].魔力 = self.数据[编号].魔力 + 1
        self.数据[编号].力量 = self.数据[编号].力量 + 1
        self.数据[编号].耐力 = self.数据[编号].耐力 + 1
        self.数据[编号].敏捷 = self.数据[编号].敏捷 + 1
        self.数据[编号].潜力 = self.数据[编号].潜力 + 5
        self.数据[编号].当前经验 = self.数据[编号].当前经验 - self.数据[编号].最大经验
        self.数据[编号].最大经验 = 升级消耗.宠物[self.数据[编号].等级+1]
    end
end



function 召唤兽处理类:添加经验(连接id,id,编号,数额,月华)
  if self.数据[编号].等级 >=185 then --
    return
  end
  local  限制等级 = 玩家数据[id].角色.数据.等级+5
  if 月华 then
      限制等级 = 玩家数据[id].角色.数据.等级+10
  end

  if self.数据[编号].等级>=限制等级 then
    --发送数据(玩家数据[id].连接id,38,{内容="你的召唤兽当前等级已经超过人物等级+5，目前已经无法再获得更多的经验了"})
    return
  end
  if self.数据[编号].当前经验==nil or self.数据[编号].最大经验 == nil then
    print("角色:"..玩家数据[id].角色.数据.名称.."召唤兽处理类:添加经验()接口处的数据异常")
    return
  end
  local 实际数额=数额
  self.数据[编号].当前经验=self.数据[编号].当前经验+实际数额
  发送数据(连接id,27,{文本="#W/你的"..self.数据[编号].名称.."#W/获得了"..实际数额.."点经验",频道="xt"})
  if self.数据[编号].当前经验>=self.数据[编号].最大经验 and self.数据[编号].等级<限制等级  then
       for i=self.数据[编号].等级,限制等级 do
           if self.数据[编号].当前经验>=self.数据[编号].最大经验 and self.数据[编号].等级<限制等级 then
              self:升级(编号,id)
              发送数据(连接id,27,{文本="#W/你的#R/"..self.数据[编号].名称.."#W/等级提升到了#R/"..self.数据[编号].等级.."#W/级",频道="xt"})
           else
              break
           end
       end
  end
  self:刷新信息(编号,"1")
  发送数据(连接id,20,self:取存档数据(编号))
  if self.数据[编号].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
    玩家数据[id].角色.数据.参战宝宝={}
    玩家数据[id].角色.数据.参战宝宝=DeepCopy(self:取存档数据(编号))
    玩家数据[id].角色.数据.参战信息=1
    self.数据[编号].参战信息=1
    发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
  end
end

function 召唤兽处理类:加寿命处理(编号,数额,中毒,连接id,id)
  self.数据[编号].寿命=self.数据[编号].寿命+数额
  常规提示(id,"该召唤兽寿命增加了#R/"..数额.."点")
  if 中毒~=nil and 中毒~=0 and 中毒>=取随机数() then
    local 减少类型=""
    local 减少数量=0
    local 随机参数=取随机数(1,6)
    if 随机参数==1 then
      减少类型="攻击资质"
      减少数量=取随机数(1,5)
    elseif 随机参数==2 then
      减少类型="防御资质"
      减少数量=取随机数(1,5)
    elseif 随机参数==3 then
      减少类型="体力资质"
      减少数量=取随机数(5,20)
    elseif 随机参数==4 then
      减少类型="法力资质"
      减少数量=取随机数(3,10)
    elseif 随机参数==5 then
      减少类型="躲闪资质"
      减少数量=取随机数(1,5)
    elseif 随机参数==6 then
      减少类型="速度资质"
      减少数量=取随机数(1,5)
    end
    self.数据[编号][减少类型]=self.数据[编号][减少类型]-减少数量
    常规提示(id,"#W/你的召唤兽出现了中毒现象从而导致#G/"..减少类型.."#W/减少了#R/"..减少数量.."#W/点")
  end
  发送数据(连接id,20,self:取存档数据(编号))
end

function 召唤兽处理类:加血处理(编号,数额,连接id,id)
  self.数据[编号].气血=self.数据[编号].气血+数额
  if self.数据[编号].气血>self.数据[编号].最大气血 then
    self.数据[编号].气血=self.数据[编号].最大气血
  end
  发送数据(连接id,20,self:取存档数据(编号))
  if self.数据[编号].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
    玩家数据[id].角色.数据.参战宝宝={}
    玩家数据[id].角色.数据.参战宝宝=DeepCopy(self:取存档数据(编号))
    玩家数据[id].角色.数据.参战信息=1
    self.数据[编号].参战信息=1
    发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
  end
end

function 召唤兽处理类:加蓝处理(编号,数额,连接id,id)
  self.数据[编号].魔法=self.数据[编号].魔法+数额
  if self.数据[编号].魔法>self.数据[编号].最大魔法 then
    self.数据[编号].魔法=self.数据[编号].最大魔法
  end
  发送数据(连接id,20,self:取存档数据(编号))
  if self.数据[编号].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
    玩家数据[id].角色.数据.参战宝宝={}
    玩家数据[id].角色.数据.参战宝宝=DeepCopy(self:取存档数据(编号))
    玩家数据[id].角色.数据.参战信息=1
    self.数据[编号].参战信息=1
    发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
  end
end

function 召唤兽处理类:召唤兽染色(连接id,序号,id,内容)
  local 编号=self:取编号(内容.序列)
 if 编号==0 or self.数据[编号]==nil then return  end
  if 玩家数据[id].道具:消耗背包道具(id,"彩果",20) then
          if self.数据[编号].染色方案==nil then
              self.数据[编号].染色方案=0
          end
          if self.数据[编号].染色组==nil then
              self.数据[编号].染色组={}
          end
          if not 内容.序列1 and not 内容.序列2 and 内容.序列3  then
             self.数据[编号].染色方案 =nil
             self.数据[编号].染色组=nil
          else
             self.数据[编号].染色方案 = 内容.序列1
             self.数据[编号].染色组={}
             self.数据[编号].染色组[1] = 内容.序列2
             self.数据[编号].染色组[2] = 内容.序列3
             self.数据[编号].染色组[3] = 内容.序列4
          end

          常规提示(id,"#Y恭喜你，召唤兽染色成功！换个颜色换个心情")
          发送数据(连接id,3699)
          发送数据(连接id,16,self.数据)
          发送数据(连接id,20,self:取存档数据(编号))
    else
        常规提示(id,"你身上彩果不足20个")
    end
end


function 召唤兽处理类:召唤兽还原染色(连接id,序号,id,内容)
  local 编号=self:取编号(内容.序列)
  if 编号==0 or self.数据[编号]==nil then return  end
  if 玩家数据[id].道具:消耗背包道具(id,"彩果",20) then
      self.数据[编号].染色方案 =nil
      self.数据[编号].染色组=nil
      常规提示(id,"#Y恭喜你，召唤兽还原")
      发送数据(连接id,3699)
      发送数据(连接id,16,self.数据)
      发送数据(连接id,20,self:取存档数据(编号))
  else
      常规提示(id,"你身上彩果不足20个")
      return
  end
end

function 召唤兽处理类:召唤兽饰品染色(连接id,序号,id,内容)
    local 编号=self:取编号(内容.序列)
    if 编号==0 or self.数据[编号]==nil then return  end
    if 玩家数据[id].道具:消耗背包道具(id,"彩果",20) then
        if self.数据[编号].饰品染色方案==nil then
            self.数据[编号].饰品染色方案=0
        end
        if self.数据[编号].饰品染色组==nil then
            self.数据[编号].饰品染色组={}
        end
        self.数据[编号].饰品染色方案 = 内容.序列1
        self.数据[编号].饰品染色组={}
        self.数据[编号].饰品染色组[1] = 内容.序列2
        self.数据[编号].饰品染色组[2] = 内容.序列3
        self.数据[编号].饰品染色组[3] = 内容.序列4
        常规提示(id,"#Y恭喜你，召唤兽饰品染色成功！换个颜色换个心情")
        发送数据(连接id,16,self.数据)
        发送数据(连接id,3699)
        发送数据(连接id,20,self:取存档数据(编号))
    else
        常规提示(id,"你身上彩果不足20个")
        return
    end

end

function 召唤兽处理类:炼化处理(连接id,序号,id,内容)
  local 物品=内容.序列
  local 认证码=内容.序列1
  local bb = nil--你这里改了？这里bb=ni，下面肯定异常。
  for k,v in pairs(self.数据) do
   if v~=nil and v.认证码 == 认证码 then
     bb = k
     break
   end
 end
  if bb == nil then
    常规提示(id,"召唤兽数据异常，请重新打开界面进行操作。")
    return
  end
  local 物品名称=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].名称

  if 物品名称==nil or self.数据[bb]==nil  then
    return
  elseif self.数据[bb].参战信息~=nil then
    常规提示(id,"请先取消召唤兽的参战状态")
    return
  elseif self.数据[bb].种类=="孩子" then
    常规提示(id,"孩子不能进行炼化")
    return
  elseif  self.数据[bb].统御 ~= nil then
      常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
      return
  elseif 物品名称=="炼妖石" then
    local 临时等级=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].级别限制
    local 临时灵气=math.floor(self.数据[bb].等级/10)
    local 成功几率=25+math.floor(self.数据[bb].等级/10)
    if self.数据[bb].种类=="宝宝" then
      成功几率=成功几率+70
    end
    if 成功几率>=取随机数() then
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].名称="天眼珠"
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].灵气=临时灵气+取随机数(20,80)
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].分类=11
      if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].灵气>100 then
        玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].灵气=100
      end
      常规提示(id,"炼化成功！")
    else
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
      玩家数据[id].角色.数据.道具[物品]=nil
      常规提示(id,"很遗憾，本次炼化失败了！！！")
    end
    table.remove(self.数据,bb)
    发送数据(连接id,16,self.数据)
    发送数据(连接id,24,玩家数据[id].道具:索要道具2(id))
    发送数据(连接id,3699)
    发送数据(连接id,26)
    return

  elseif 物品名称=="圣兽丹" then
    local 成功几率=1
    if self.数据[bb].种类=="变异" then
      成功几率=成功几率+4
    end
  if 成功几率>=取随机数(1,100) then
         玩家数据[id].道具:给予道具(id,"普通宝宝饰品丹")
         常规提示(id,"炼化成功")
    else
      常规提示(id,"很遗憾，本次炼化失败了！！！")
    end
    -- if 成功几率>=取随机数(1,100) then
    --      玩家数据[id].道具:给予道具(id,"宠物饰品通用丹")
    --      常规提示(id,"炼化成功")
    -- else
    --   常规提示(id,"很遗憾，本次炼化失败了！！！")
    -- end
    玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 = 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 -1
    if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 <= 0 then
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
      玩家数据[id].角色.数据.道具[物品]=nil
    end
    table.remove(self.数据,bb)
    发送数据(连接id,16,self.数据)
    发送数据(连接id,24,玩家数据[id].道具:索要道具2(id))
    发送数据(连接id,3699)
    发送数据(连接id,26)

  elseif 物品名称=="吸附石" then
    if 取随机数(1,100)<=5 then
        local 吸附技能=self.数据[bb].技能[取随机数(1,#self.数据[bb].技能)]
		if 吸附技能=="须弥真言" then
			常规提示(id,"很遗憾，本次炼化失败了！！！")
		else
			玩家数据[id].道具:给予道具(id,"点化石",1,吸附技能)
			常规提示(id,"恭喜你获得了一个#R"..吸附技能.."#Y点化石")
		end
    else
      常规提示(id,"很遗憾，本次炼化失败了！！！")
    end
    玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 = 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 -1
    if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 <= 0 then
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
      玩家数据[id].角色.数据.道具[物品]=nil
    end
    table.remove(self.数据,bb)
    发送数据(连接id,16,self.数据)
    发送数据(连接id,24,玩家数据[id].道具:索要道具2(id))
    发送数据(连接id,3699)
    发送数据(连接id,26)
  else--检查银子和体力
    常规提示(id,"这是什么东西")
  end
end

function 召唤兽处理类:进阶造型处理(id)
  if 玩家数据[id].角色.数据.参战信息==nil then
    常规提示(id,"请将要改变造型的召唤兽参战")
    return
  end
  local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
  if 编号==0 or self.数据[编号]==nil then
      常规提示(id,"请先将要更改造型的召唤兽设置为参战！")
      return
  elseif 玩家数据[id].召唤兽.数据[编号].种类=="神兽"then
      常规提示(id,"神兽已经是进阶造型了")
      return
  elseif 玩家数据[id].召唤兽.数据[编号].灵性 < 50 then
      常规提示(id,"该召唤兽没有达到更改造型的要求,灵性不能小于50")
      return
  elseif 玩家数据[id].召唤兽.数据[编号].参战等级 <= 35 and 玩家数据[id].召唤兽.数据[编号].种类~="神兽" and 玩家数据[id].召唤兽.数据[编号].种类~="孩子" then
      常规提示(id,"参战等级小于35的召唤兽没有进阶造型")
      return
  elseif 玩家数据[id].召唤兽.数据[编号].进阶 then
      常规提示(id,"该召唤兽已经改变造型了，你是来寻我开心的吗？")
      return
  end
  玩家数据[id].召唤兽.数据[编号].模型 = "进阶"..玩家数据[id].召唤兽.数据[编号].模型
  玩家数据[id].召唤兽.数据[编号].名称 = 玩家数据[id].召唤兽.数据[编号].模型
  玩家数据[id].召唤兽.数据[编号].进阶 = true
  常规提示(id,"恭喜你，该召唤兽更改为新的造型了")
end

function 召唤兽处理类:染色还原(id)
  if 玩家数据[id].角色.数据.参战信息==nil then
    常规提示(id,"请将要法术认证的召唤兽参战")
    return
  end
  local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
  if 编号==0 or self.数据[编号]==nil then
    常规提示(id,"请先将要更改造型的召唤兽设置为参战！")
      return
  end
  玩家数据[id].召唤兽.数据[编号].饰品染色组={}
  玩家数据[id].召唤兽.数据[编号].染色组={}
  玩家数据[id].召唤兽.数据[编号].饰品染色方案=nil
  玩家数据[id].召唤兽.数据[编号].染色方案=nil
  常规提示(id,"恭喜你，该召唤兽染色状态还原了")
end




function 召唤兽处理类:洗练处理(连接id,序号,id,内容)
          local 物品=内容.序列
          local 认证码=内容.序列1
          local 选择内丹=内容.序列2
          local bb=self:取编号(认证码)
          if bb == 0 or self.数据[bb]==nil then
            常规提示(id,"召唤兽数据异常，请重新打开界面进行操作。")
            return
          end
          if 玩家数据[id].角色.数据.道具[物品]==nil then return end
          if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]==nil then return end
          local 物品名称=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].名称
          local 携带技能 = 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能
          local 格子编号=0
          if 物品名称==nil or self.数据[bb]==nil  then
            return
          elseif 物品名称=="金柳露" or 物品名称=="超级金柳露" then
                  if 物品名称=="金柳露"  then
                      常规提示(id,"金柳露不用作用于这种召唤兽身上")
                      return
                  elseif self.数据[bb].种类=="神兽" then
                        常规提示(id,"金柳露不用作用于神兽身上")
                        return
                  elseif self:是否有装备(bb) then
                          常规提示(id,"请先卸下召唤兽所穿戴的装备")
                          return
                  elseif self.数据[bb].进阶 then
                          常规提示(id,"金柳露不用作用于进阶召唤兽身上")
                          return
                  elseif self.数据[bb].统御 ~= nil then
                          常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
                          return
                  elseif 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 == nil then
                          常规提示(id,"这种金柳露已经作废无法使用,请重新购买")
                          return
                  end
                  local 数量 = 1
                  if self.数据[bb].种类=="宝宝" then
                      数量 = 2
                  elseif self.数据[bb].种类=="变异" then
                          数量 = 5
                  elseif self.数据[bb].种类=="孩子" then
                          数量 = 10
                  end

                  if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 < 数量 then
                      常规提示(id,"你的道具数量不足，洗练此宝宝需要#R/"..数量.."#Y/个道具")
                      return
                  end
                  if self.数据[bb].种类=="野怪" or self.数据[bb].种类=="宝宝"  then
                      self.数据[bb]=self:置新对象(self.数据[bb].模型,self.数据[bb].名称,"宝宝")
                  elseif self.数据[bb].种类=="变异"  then
                          self.数据[bb]=self:置新对象(self.数据[bb].模型,self.数据[bb].名称,"变异")
                  elseif self.数据[bb].种类=="孩子"  then
                          self.数据[bb]=self:置新对象(self.数据[bb].模型,self.数据[bb].名称,"孩子")
                  end
                  self.数据[bb].等级 = 0
                  for n=1,5 do
                      self.数据[bb].加点记录[self.属性范围[n]]=0
                  end
                  self.数据[bb].潜力=self.数据[bb].等级*5+self.数据[bb].灵性*2
                  self.数据[bb].当前经验=0
                  玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 = 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 -数量
                  if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].数量 <= 0 then
                      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
                      玩家数据[id].角色.数据.道具[物品]=nil
                  end
                  self:刷新信息(bb)
                  发送数据(连接id,16,self.数据)
                  发送数据(连接id,24,玩家数据[id].道具:索要道具2(id))
                  发送数据(连接id,25)
                  道具刷新(id)
                  常规提示(id,"使用"..物品名称.."成功")
          elseif 物品名称=="魔兽要诀" or 物品名称=="高级魔兽要诀" or 物品名称=="特殊魔兽要诀" then
                  local 分割高级={}
                  if (物品名称=="高级魔兽要诀" or 物品名称=="特殊魔兽要诀") and string.find(玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能,"高级")~=nil then
                      分割高级=分割文本(玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能,"高级")
                  end
                  for n=1,#self.数据[bb].技能 do
                      if self.数据[bb].技能[n]==玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能 then
                          常规提示(id,"#Y/你的召唤兽已经拥有此技能了哟！")
                          return
                      elseif string.find(self.数据[bb].技能[n],"超级")~=nil then
                            local 分割超级 = 分割文本(self.数据[bb].技能[n],"超级")
                            if (分割超级[2]=="奔雷咒" or 分割超级[2]=="泰山压顶"  or 分割超级[2]=="水漫金山" or 分割超级[2]=="地狱烈火" or 分割超级[2]=="壁垒击破") and 分割超级[2]==玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能 then
                                   常规提示(id,"#Y/你的召唤兽已经拥有此技能的超级技能了哟！")
                                  return
                            elseif 分割高级[2] and 分割超级[2]==分割高级[2] then
                                  常规提示(id,"#Y/你的召唤兽已经拥有此技能的超级技能了哟！")
                                  return
                            end
                      end
                  end
                  if self.数据[bb].种类=="神兽" then
                    for n=1,#self.数据[bb].技能 do
                       for i=1,#self.数据[bb].天生技能 do
                        if self.数据[bb].技能[n] == self.数据[bb].天生技能[i] then
                          table.remove(self.数据[bb].技能,n)
                        end
                      end
                    end
                  end
                 格子编号=取随机数(1,#self.数据[bb].技能)
                  if self.数据[bb].法术认证~=nil and self.数据[bb].技能[格子编号]==self.数据[bb].法术认证[1] then
                    while (self.数据[bb].技能[格子编号]==self.数据[bb].法术认证[1]) do
                         格子编号=取随机数(1,#self.数据[bb].技能)
                    end
                  end
                  local 顶掉技能 = self.数据[bb].技能[格子编号]
                  local 增加格子 = false
                  local 格子几率 = 0
                  if self.数据[bb].种类=="神兽" and self.数据[bb].天生技能~=nil then
                      local 技能数量 = #self.数据[bb].技能 + #self.数据[bb].天生技能
                      if  技能数量<=8  then
                        格子几率 = 120
                      elseif 技能数量>8 and  技能数量<=12 then
                        格子几率 = 40
                      elseif  技能数量>12 and 技能数量<=16 then
                         格子几率 = 25
                      -- elseif 技能数量>16 and 技能数量<=20 then
                      --   格子几率 = 10
                      -- elseif 技能数量>20 and 技能数量<24 then
                      --   格子几率 = 5
                      else
                        格子几率 = 0
                      end
                      if 玩家数据[id].角色.数据.顶书概率~=nil and 技能数量<24 then
                        格子几率 = 格子几率 + 玩家数据[id].角色.数据.顶书概率 *10
                        玩家数据[id].角色.数据.顶书概率 = 0
                      end
                  else
                        local 技能数量 = #self.数据[bb].技能
                        if  技能数量<=8 then
                            格子几率 = 20
                        elseif 技能数量>8 and 技能数量<12  then
                            格子几率 = 10
                        else
                          格子几率 = 0
                        end
                        if 玩家数据[id].角色.数据.顶书概率~=nil and 技能数量<24 then
                            格子几率 = 格子几率 + 玩家数据[id].角色.数据.顶书概率 *10
                            玩家数据[id].角色.数据.顶书概率 = 0
                        end
                  end
                  if 取随机数(1,1000)<=格子几率  then
                    增加格子 = true
                  end
                  if 增加格子 then
                      格子编号=#self.数据[bb].技能+1
                  end
                  self.数据[bb].技能[格子编号]=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能
                  self.数据[bb].技能=删除重复(self.数据[bb].技能)
                  if self.数据[bb].种类=="神兽" and self.数据[bb].天生技能~=nil then
                      for i=1,#self.数据[bb].天生技能 do
                         self.数据[bb].技能[#self.数据[bb].技能+1] = self.数据[bb].天生技能[i]
                      end
                  end
                  self.数据[bb].自动指令=nil
                  if 增加格子 then
                      常规提示(id,"你的这只召唤兽学会了新技能#R/"..玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能)
                  else
                      常规提示(id,"你的这只召唤兽的#R/"..顶掉技能.."#Y/被替换为:#R/"..玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能)
                  end
                  玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
                  玩家数据[id].角色.数据.道具[物品]=nil
                  self:刷新信息(bb)
                  发送数据(连接id,16,self.数据)
                  发送数据(连接id,95)
                  发送数据(连接id,96,玩家数据[id].道具:索要道具2(id))
                  道具刷新(id)
          elseif 物品名称=="超级魔兽要诀" then
                  for n=1,#self.数据[bb].技能 do
                    if self.数据[bb].技能[n]==玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能 then
                      常规提示(id,"#Y/你的召唤兽已经拥有此技能了哟！")
                      return
                    end
                  end
                  local 宝宝技能 = DeepCopy(self.数据[bb].技能)
                  if self.数据[bb].种类=="神兽" then
                        for n=1,#宝宝技能 do
                            for i=1,#self.数据[bb].天生技能 do
                                if 宝宝技能[n] == self.数据[bb].天生技能[i] then
                                    table.remove(宝宝技能,n)
                                end
                            end
                        end
                  end
                  local 格子编号,升级技能=取召唤兽技能编号(宝宝技能,玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能)
                  if 格子编号==0 then
                        常规提示(id,"#Y/你的召唤兽没有"..升级技能.."无法使用超级兽决,天生技能无法进化")
                        return
                  end
                  if self.数据[bb].法术认证~=nil and 宝宝技能[格子编号]==self.数据[bb].法术认证[1] then
                      常规提示(id,"#Y/清取消该法术认证在来进化")
                      return
                  end
                  local 技能数量 = #self.数据[bb].技能
                  if 技能数量<=7 then
                      常规提示(id,"#Y/技能数量不足8个无法进化超级技能")
                      return
                  end
                  local 超级数量 = 0
                  for i=1,#self.数据[bb].技能 do
                    if self.数据[bb].技能[i]~="超级三昧真火" and  string.find(self.数据[bb].技能[i], "超级")~=nil then
                        超级数量=超级数量+1
                    end
                  end
                  if 超级数量>=4 then
                    常规提示(id,"#Y/召唤兽最多可以进化4个超级技能")
                    return
                  end
                  if 技能数量>7 and 技能数量<=11 and 超级数量>=1 then
                      常规提示(id,"#Y/该召唤兽最多可以进化一个技能")
                      return
                  elseif 技能数量>11 and 技能数量<=15 and 超级数量>=2 then
                      常规提示(id,"#Y/该召唤兽最多可以进化二个技能")
                      return
                  elseif 技能数量>15 and 技能数量<=20 and 超级数量>=3 then
                      常规提示(id,"#Y/该召唤兽最多可以进化三个技能")
                      return
                  end
                  宝宝技能[格子编号]=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能
                  if self.数据[bb].种类=="神兽" and self.数据[bb].天生技能~=nil then
                      for i=1,#self.数据[bb].天生技能 do
                         宝宝技能[#宝宝技能+1] = self.数据[bb].天生技能[i]
                      end
                  end
                  self.数据[bb].技能=DeepCopy(宝宝技能)
                  self.数据[bb].自动指令=nil
                  常规提示(id,"你的这只召唤兽的#R/"..升级技能.."#Y/进化为#R/"..玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]].附带技能)
                  玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[物品]]=nil
                  玩家数据[id].角色.数据.道具[物品]=nil
                  self:刷新信息(bb)
                  发送数据(连接id,16,self.数据)
                  发送数据(连接id,95)
                  发送数据(连接id,96,玩家数据[id].道具:索要道具2(id))
                  道具刷新(id)
          elseif 物品名称 == "召唤兽内丹" or 物品名称 == "高级召唤兽内丹" then
                  self.数据[bb].自动指令 = nil
                  if #self.数据[bb].内丹数据 >= 1 then
                      local 查找 = 0
                      for k,v in pairs(self.数据[bb].内丹数据) do
                          if v.技能 == 携带技能 then
                              查找 = k
                              break
                          end
                      end
                      if 查找~=0 then
                        if self.数据[bb].内丹数据[查找].等级>=5 then
                            常规提示(id,"该内丹已学满")
                            return
                        else
                            self.数据[bb].内丹数据[查找].等级 = self.数据[bb].内丹数据[查找].等级 + 1
                            玩家数据[id].角色.数据.道具[物品]=nil
                            常规提示(id,"恭喜你的"..self.数据[bb].名称.."#R/"..self.数据[bb].内丹数据[查找].技能.."#Y/升到第#R/"..self.数据[bb].内丹数据[查找].等级.."#Y/层")
                        end
                      else
                         if #self.数据[bb].内丹数据< self.数据[bb].内丹.内丹上限 and self.数据[bb].内丹.可用内丹>0  then
                              local 编号 =0
                              for i=1,self.数据[bb].内丹.内丹上限 do
                                  if not self.数据[bb].内丹数据[i] then
                                    编号 = i
                                    break
                                  end
                              end
                              if 编号>0 and 编号<=self.数据[bb].内丹.内丹上限 then
                                  self.数据[bb].内丹数据[编号] = {}
                                  self.数据[bb].内丹数据[编号].技能 = 携带技能
                                  self.数据[bb].内丹数据[编号].等级 = 1
                                  self.数据[bb].内丹.可用内丹 = self.数据[bb].内丹.可用内丹 - 1
                                  玩家数据[id].角色.数据.道具[物品]=nil
                                  常规提示(id,"恭喜你的"..self.数据[bb].名称.."学会了#R/"..携带技能)
                              end
                         elseif 选择内丹 and 选择内丹~=0  and 选择内丹<=self.数据[bb].内丹.内丹上限 and self.数据[bb].内丹数据[选择内丹] then
                                if self.数据[bb].内丹数据[选择内丹].技能 ~= 携带技能 then
                                    常规提示(id,"你的召唤兽"..self.数据[bb].名称.."遗忘了#R/"..self.数据[bb].内丹数据[选择内丹].技能)
                                    self.数据[bb].内丹数据[选择内丹] = {}
                                    self.数据[bb].内丹数据[选择内丹].技能 = 携带技能
                                    self.数据[bb].内丹数据[选择内丹].等级 = 1
                                    玩家数据[id].角色.数据.道具[物品]=nil
                                    常规提示(id,"恭喜你的"..self.数据[bb].名称.."学会了#R/"..携带技能)
                                else
                                    常规提示(id,"你已学会该技能,无需替换")
                                    return
                                end
                         else
                              常规提示(id,"请选择需要覆盖的内丹")
                              return
                         end
                      end
                      self:刷新信息(bb)
                      发送数据(连接id,16,self.数据)
                      发送数据(连接id,95)
                      发送数据(连接id,96,玩家数据[id].道具:索要道具2(id))
                      发送数据(连接id,3699)
                  else
                      self.数据[bb].内丹数据[1] = {}
                      self.数据[bb].内丹数据[1].技能 = 携带技能
                      self.数据[bb].内丹数据[1].等级 = 1
                      self.数据[bb].内丹.可用内丹 = self.数据[bb].内丹.可用内丹 - 1
                      玩家数据[id].角色.数据.道具[物品]=nil
                      常规提示(id,"恭喜你的"..self.数据[bb].名称.."学会了#R/"..携带技能)
                      self:刷新信息(bb)
                      发送数据(连接id,16,self.数据)
                      发送数据(连接id,95)
                      发送数据(连接id,96,玩家数据[id].道具:索要道具2(id))
                      发送数据(连接id,3699)
                  end
          end
end

function 召唤兽处理类:法术认证(连接id,id)

   local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
    if 编号 == 0 or self.数据[编号]==nil  then
         常规提示(id,"请将要法术认证的召唤兽参战")
         return
    elseif 取银子(id) < 1000000 then
         常规提示(id,"你的钱不够100万无法取消！")
         return
     elseif self:是否有装备(编号) then
          常规提示(id,"请先卸下召唤兽所穿戴的装备")
          return
     elseif  self.数据[编号].统御 ~= nil then
          常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
          return
      elseif self.数据[编号].种类=="神兽" then
        常规提示(id,"神兽无法认证法术")
        return
     end
  local 认证法术={}
  local 临时选项={}
    if #self.数据[编号].技能~=nil then
      for i=1,#self.数据[编号].技能 do
        if self.数据[编号].技能[i]=="雷击" or self.数据[编号].技能[i]=="水攻" or self.数据[编号].技能[i]=="烈火" or self.数据[编号].技能[i]=="落岩" or self.数据[编号].技能[i]=="奔雷咒" or self.数据[编号].技能[i]=="水漫金山" or self.数据[编号].技能[i]=="地狱烈火" or self.数据[编号].技能[i]=="泰山压顶" or self.数据[编号].技能[i]=="上古灵符" then
          认证法术[#认证法术+1]=self.数据[编号].技能[i]
        end
      end
    end

  if #认证法术>=1 then
    for i=1,#认证法术 do
      临时选项[i]=认证法术[i]
      临时选项[#认证法术+1]="不认证了"
      发送数据(连接id,1501,{名称="老马猴",模型="马猴",对话=format("少侠要对该召唤兽的哪个技能进行认证呢？"),选项=临时选项})
    end
    玩家数据[id].宝宝认证技能 = self.数据[编号].认证码
  else
    添加最后对话(id,"该召唤兽没有可认证的法术哦")
    玩家数据[id].宝宝认证技能 =nil
  end
end

function 召唤兽处理类:法术认证处理(连接id,id,事件)
  if 玩家数据[id].宝宝认证技能==nil then return end
  local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
  if 编号 == 0 or self.数据[编号]==nil  then
       常规提示(id,"请将要法术认证的召唤兽参战")
       return
  elseif self.数据[编号].认证码 ~= 玩家数据[id].宝宝认证技能 then
       常规提示(id,"你要认证的好像不是这个宝宝")
       return
  elseif 取银子(id) < 1000000 then
       常规提示(id,"你的钱不够100万无法取消！")
       return
   elseif self:是否有装备(编号) then
        常规提示(id,"请先卸下召唤兽所穿戴的装备")
        return
   elseif  self.数据[编号].统御 ~= nil then
        常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
        return
    elseif self.数据[编号].种类=="神兽" then
        常规提示(id,"神兽无法认证法术")
        return
  end
    if #self.数据[编号].技能~=nil then
      local  找到技能 = 0
      for i=1,#self.数据[编号].技能 do
        if self.数据[编号].技能[i]==事件 then
           找到技能 = i
        end
      end
      if 找到技能~= 0 then
          玩家数据[id].角色:扣除银子(1000000,"法术认证",1)
          table.remove(self.数据[编号].技能, 找到技能)
          self.数据[编号].法术认证={}
          self.数据[编号].法术认证[1]=事件
          添加最后对话(id,"少侠的召唤兽法术认证成功")
      end
      self:刷新信息(编号)
    end

end

function 召唤兽处理类:取消法术认证(连接id,id)

  local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
    if 编号 == 0 or self.数据[编号]==nil  then
       常规提示(id,"请将要取消法术认证的召唤兽参战")
       return
    elseif 取银子(id) < 1000000 then
       常规提示(id,"你的钱不够100万无法取消！")
       return
   elseif self:是否有装备(编号) then
        常规提示(id,"请先卸下召唤兽所穿戴的装备")
        return
   elseif  self.数据[编号].统御 ~= nil then
        常规提示(id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
        return
   elseif  self.数据[编号].法术认证 == nil then
        常规提示(id,"该召唤兽没有法术认证")
        return
    end

    玩家数据[id].角色:扣除银子(1000000,"法术认证",1)
    local 技能重复=0
    if #self.数据[编号].技能~=nil then
       for i=1,#self.数据[编号].技能 do
          if self.数据[编号].技能[i] == self.数据[编号].法术认证[1] then
             技能重复 = i
          end
       end
    end
     if 技能重复==0 then
        self.数据[编号].技能[#self.数据[编号].技能+1] = self.数据[编号].法术认证[1]
     end
     self.数据[编号].法术认证=nil
     添加最后对话(id,"少侠的召唤兽法术认证取消成功")
     self:刷新信息(编号)


end

function 召唤兽处理类:洗点处理(id)

  local 编号=self:取编号(玩家数据[id].角色.数据.参战宝宝.认证码)
  if 编号 == 0 or self.数据[编号]==nil  then
       常规提示(id,"请先将要洗点的召唤兽设置成出战状态")
       return
    end
    if self:是否有装备(编号) then
        常规提示(id,"有装备的召唤兽无法洗点")
        return false
      end

    if 玩家数据[id].角色.数据.银子<3000000 then
      常规提示(id,"你的银子不足300W")
      return
    end

    if self.数据[编号].统御~=nil then
        常规提示(id,"#Y/统御的召唤兽无法洗点。")
      return
    end

  玩家数据[id].角色:扣除银子(3000000,"花费300万银子重置召唤兽属性点",1)
  if self.数据[编号].初始属性==nil then
     self.数据[编号].初始属性 = self:重置初始属性(self.数据[编号].种类)
  end
  for n=1,5 do
      self.数据[编号].加点记录[self.属性范围[n]]=0
  end
  self.数据[编号].潜力=self.数据[编号].等级*5+self.数据[编号].灵性*2
  -- if self.数据[编号].宠物图鉴系统==nil then
  --    self.数据[编号].宠物图鉴系统={东海湾=100,江南野外=100,大雁塔=100,大唐国境=100,大唐境外=100,魔王寨=100,普陀山=100,盘丝岭=100,狮驼岭=100,西牛贺州=100,花果山=100,海底迷宫=100,地狱迷宫=100,北俱芦洲=100,龙窟=100,凤巢=100,无名鬼蜮=100,小西天=100,女娲神迹=100,小雷音寺=100
  --     ,蓬莱仙岛=100,月宫=100,蟠桃园=100,墨家禁地=100,解阳山=100,子母河底=100,麒麟山=100,碗子山=100,波月洞=100,柳林坡=100,比丘国=100,须弥东界=100}
  --    self.数据[编号].宠物图鉴属性={攻击资质=0,防御资质=0,体力资质=0,法力资质=0,速度资质=0,躲闪资质=0,成长=0,体质=0,魔力=0,力量=0,耐力=0,敏捷=0,最大气血1=0,最大气血2=0,速度1=0,最大魔法=0,伤害=0,防御=0,速度2=0,灵力=0,速度3=0}
  -- end
  常规提示(id,"#Y洗点成功")
  self:刷新信息(编号,"1")
end


function 召唤兽处理类:降级处理(id,认证码)
  local 编号 = self:取编号(认证码)
  if not 编号 or 编号 == 0 or not self.数据[编号] then
      常规提示(id, "未找到你的召唤兽")
      return
  elseif self:是否有装备(编号) then
          常规提示(id, "有装备的召唤兽无法降级")
          return
  elseif self.数据[编号].统御 then
          常规提示(id, "#Y/统御的召唤兽无法降级。")
          return
  elseif self.数据[编号].等级 <= 10 then
          常规提示(id, "#Y该召唤兽等级不够无法降级")
          return
  elseif 玩家数据[id].角色.数据.银子 < 500000 then
          常规提示(id, "你的银子不足50万")
          return
  end
  玩家数据[id].角色:扣除银子(500000, "降级消耗", 1)
  self.数据[编号].等级 = math.max(self.数据[编号].等级 - 10, 0)
  self.数据[编号].潜力 = self.数据[编号].等级 * 5 + (self.数据[编号].灵性 or 0) * 2
  for n=1,5 do
      self.数据[编号].加点记录[self.属性范围[n]] = 0
  end
  self:刷新信息(编号, "1")
  常规提示(id, "#Y召唤兽成功降级10级")
end
function 召唤兽处理类:加点处理(连接id,序号,id,点数,多角色)
  local 临时编号=self:取编号(点数.序列)

  if 临时编号==0 or self.数据[临时编号]==nil then return  end
  local 监控开关 = false
  local 属性总和=0
  for n=1,5 do
   if 点数[self.属性范围[n]]<0 or 点数[self.属性范围[n]]>self.数据[临时编号].潜力 then
      监控开关 = true
    end
     属性总和=属性总和+点数[self.属性范围[n]]
  end
     if 监控开关 then
         -- 发送数据(玩家数据[id].连接id,998,"请注意你的角色异常！已经对你进行封号")
         --  __S服务:输出("玩家"..id.." 非法修改数据警告!属性修改")
         -- 封禁账号(id,"CE修改")
         --  __S服务:断开连接(玩家数据[id].连接id)
         -- __S服务:连接退出(玩家数据[内容.数字id].连接id)
        return 0
      end


    if 属性总和==0 then
      常规提示(id,"您到底是要添加哪种属性点呢？",多角色)
      return 0
    elseif self.数据[临时编号].潜力<属性总和 then
      常规提示(id,"你没有那么多可分配的属性点！",多角色)
      return 0
    else
        for n=1,5 do
          self.数据[临时编号].加点记录[self.属性范围[n]]=self.数据[临时编号].加点记录[self.属性范围[n]]+点数[self.属性范围[n]]
        end
        self.数据[临时编号].潜力=self.数据[临时编号].潜力-属性总和
        self:刷新信息(临时编号,"1")
        if 多角色~=nil then
           发送数据(玩家数据[多角色].连接id,6008,{角色=id,召唤兽=玩家数据[id].召唤兽.数据})
           发送数据(玩家数据[多角色].连接id,6026,{角色=id})
        else
            发送数据(连接id,20,self:取存档数据(临时编号))
        end
        if self.数据[临时编号].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
           玩家数据[id].角色.数据.参战宝宝={}
           玩家数据[id].角色.数据.参战宝宝=DeepCopy(self:取存档数据(临时编号))
           玩家数据[id].角色.数据.参战信息=1
           self.数据[临时编号].参战信息=1
           if 多角色~=nil then
              发送数据(玩家数据[多角色].连接id,6008,{角色=id,召唤兽=玩家数据[id].召唤兽.数据})
              发送数据(玩家数据[多角色].连接id,6025,{角色=id,召唤兽=玩家数据[id].角色.数据.参战宝宝})
              发送数据(玩家数据[多角色].连接id,6026,{角色=id})
          else
              发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
          end

        end
    end


end

function 召唤兽处理类:改名处理(连接id,序号,id,序列,名称,多角色)
  local 临时编号=self:取编号(序列)
  if 临时编号==0 or self.数据[临时编号]==nil then
    常规提示(id,"你没有这只召唤兽",多角色)
    return
  elseif 名称=="" or 名称==nil then
    常规提示(id,"名称不能为空",多角色)
    return
  elseif 敏感字判断(名称,true) then
        常规提示(id,"#Y名字不能含有敏感字符")
        return
  elseif #名称>12 then
    常规提示(id,"名称太长了，换个试试！",多角色)
    return
   elseif 判断特殊字符(名称) then
     常规提示(id,"名称不能有特殊字符！",多角色)
    return
  else
    self.数据[临时编号].名称=名称
    常规提示(id,"召唤兽名称修改成功！",多角色)
    if 多角色~=nil then
       发送数据(玩家数据[多角色].连接id,6027,{角色=id,序列=临时编号,名称=名称})
    else
      发送数据(连接id,19,{序列=临时编号,名称=名称})
    end
  end
end



function 召唤兽处理类:参战处理(连接id,序号,id,序列,多角色)
  local 临时编号=self:取编号(序列)
  if 临时编号==0 or self.数据[临时编号]==nil then
    常规提示(id,"你没有这只召唤兽",多角色)
    return
  elseif 玩家数据[id].角色.数据.等级+10 < self.数据[临时编号].等级 then
    常规提示(id,"你目前的等级小于该召唤兽10级以上,不允许参战",多角色)
    return
  -- elseif 玩家数据[id].角色.数据.等级<self.数据[临时编号].参战等级 then
  --    常规提示(id,"以你目前的能力还不足以驾驭此类型召唤兽")
  --    return
  elseif self.数据[临时编号].寿命 <= 50 then
    常规提示(id,"该召唤兽的寿命低于50无法参战",多角色)
    return
  else
    if 玩家数据[id].角色.数据.参战宝宝.认证码==self.数据[临时编号].认证码 then
      玩家数据[id].角色.数据.参战宝宝={}
      self.数据[临时编号].参战信息=nil
      玩家数据[id].角色.数据.参战信息=nil
    else
      for n=1,#self.数据 do
        if self.数据[n].认证码==玩家数据[id].角色.数据.参战宝宝.认证码 then
          self.数据[n].参战信息=nil
        end
      end
      玩家数据[id].角色.数据.参战宝宝={}
      玩家数据[id].角色.数据.参战宝宝=self.数据[临时编号]
      玩家数据[id].角色.数据.参战信息=1
      self.数据[临时编号].参战信息=1
    end
    if 多角色~=nil then
        发送数据(玩家数据[多角色].连接id,6008,{角色=id,召唤兽=玩家数据[id].召唤兽.数据})
        发送数据(玩家数据[多角色].连接id,6025,{角色=id,召唤兽=玩家数据[id].角色.数据.参战宝宝})
        发送数据(玩家数据[多角色].连接id,6026,{角色=id})
    else
        发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
    end

  end
end
function 召唤兽处理类:死亡处理(认证码)
  local 编号=self:取编号(认证码)
   if 编号==0 or self.数据[编号]==nil then return end
  if self.数据[编号].种类 ~= "神兽" then
    self.数据[编号].寿命 = self.数据[编号].寿命 - 25
    if self.数据[编号].寿命<=0 then
        self.数据[编号].寿命 = 0
    end
  end
  if self.数据[编号].寿命 <= 50 then
    -- table.remove(self.数据,编号)
    self.数据[编号].参战信息=nil
    常规提示(玩家数据[self.数字id].连接id,"该召唤兽寿命过低，无法参加战斗")
  end
end

function 召唤兽处理类:刷新信息1(认证码,参数)
  local 编号=self:取编号(认证码)
  if 编号==0 or self.数据[编号]==nil then return end
  self:刷新信息(编号,参数)
  if self.数据[编号].参战信息~=nil then
    玩家数据[self.数字id].角色.数据.参战宝宝=self.数据[编号]
    发送数据(玩家数据[self.数字id].连接id,18,玩家数据[self.数字id].角色.数据.参战宝宝)
  end
end

function 召唤兽处理类:取编号(认证码)   ---
  if self.数据==nil then return 0 end
  for n=1,#self.数据 do
    if self.数据[n] ~= nil and self.数据[n].认证码==认证码 then
      return n
    end
  end
  return 0
end







return 召唤兽处理类