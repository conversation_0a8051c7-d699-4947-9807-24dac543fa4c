{"ast": null, "code": "/**\n * Provide a default hook since not everyone needs to config this.\n */\nvar useDefaultCSP = function useDefaultCSP() {\n  return {};\n};\nexport default useDefaultCSP;", "map": {"version": 3, "names": ["useDefaultCSP"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@ant-design/cssinjs-utils/es/hooks/useCSP.js"], "sourcesContent": ["/**\n * Provide a default hook since not everyone needs to config this.\n */\nvar useDefaultCSP = function useDefaultCSP() {\n  return {};\n};\nexport default useDefaultCSP;"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EAC3C,OAAO,CAAC,CAAC;AACX,CAAC;AACD,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}