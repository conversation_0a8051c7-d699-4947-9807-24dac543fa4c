
local 装备处理类 = class()

local 附加范围={"力量","敏捷","体质","耐力","魔力"}
local 附加上限={
  [60]={27,22}
  ,[80]={30,25}
  ,[90]={33,28}
  ,[100]={36,31}
  ,[110]={39,34}
  ,[120]={42,37}
  ,[130]={45,40}
  ,[140]={48,43}
  ,[150]={51,46}
  ,[160]={54,49}
}


function 装备处理类:初始化()
  self.打造物品=绑定等级物品()
end

function 装备处理类:数据处理(连接id,序号,id,内容)
  if 序号==4501 then
    if 内容.类型== nil  or 内容.分类 == nil  then return  end
    if 内容.序列 ==nil or 内容.序列=="" then return end
    if 玩家数据[id].摊位数据~=nil then 常规提示(id,"#Y/摆摊状态下禁止此种行为") return end
    if 玩家数据[id].交易信息~=nil or 交易数据[id]~=nil then 常规提示(id,"#Y/交易中无法使用改功能") return end
    if 内容.分类 == "强化人物装备" and 玩家数据[id].角色:取任务(5)~=0 then 常规提示(id,"#Y/你已经有一个打造任务在进行了")  return  end
    if 内容.分类 == "强化灵饰淬灵" and 玩家数据[id].角色:取任务(5)~=0 then 常规提示(id,"#Y/你已经有一个打造任务在进行了")  return  end
    if 内容.类型 == "打造"  then
      if 内容.序列1 ==nil or 内容.序列1=="" then 常规提示(id,"#Y请放入正确的材料") return end
       if 内容.分类 == "普通人物装备" or 内容.分类 == "强化人物装备" then
          self:打造人物装备处理(连接id,序号,id,内容)
        elseif 内容.分类 == "灵饰淬灵" or 内容.分类 == "强化灵饰淬灵" then
             self:打造灵饰处理(连接id,序号,id,内容)
        elseif 内容.分类 == "召唤兽装备" then
            self:打造召唤兽装备处理(连接id,序号,id,内容)
        end
    elseif 内容.类型 == "镶嵌"  then
          if 内容.序列1 ==nil or 内容.序列1=="" then 常规提示(id,"#Y请放入正确的材料") return end
           if 内容.分类 == "宝石" then
               self:镶嵌宝石(连接id,序号,id,内容)
            elseif 内容.分类 == "星辉石" then
                self:镶嵌星辉石(连接id,序号,id,内容)
            elseif 内容.分类 == "钟灵石" then
                self:镶嵌钟灵石(连接id,序号,id,内容)
            elseif 内容.分类 == "珍珠" then
                self:镶嵌珍珠(连接id,序号,id,内容)
            elseif 内容.分类 == "点化石" then
                self:镶嵌点化石(连接id,序号,id,内容)
            elseif 内容.分类 == "精魄灵石" then
                 self:精魄灵石(连接id,序号,id,内容)
            elseif 内容.分类 == "宝石转移" then
                 self:转移宝石(连接id,序号,id,内容)
            elseif 内容.分类 == "星辉石转移" then
                 self:转移星辉石(连接id,序号,id,内容)
            end
    elseif 内容.类型 == "合成"  then
            if 内容.序列1 ==nil or 内容.序列1=="" then 常规提示(id,"#Y请放入正确的材料") return end
            if 内容.分类 == "宝石" then
               self:合成宝石(连接id,序号,id,内容)
            elseif 内容.分类 == "星辉石" then
                   if 内容.序列2 ==nil or 内容.序列2=="" then 常规提示(id,"#Y请放入正确的材料") return end
                   self:合成星辉石(连接id,序号,id,内容)
            elseif 内容.分类 == "钟灵石" then
                   self:合成钟灵石(连接id,序号,id,内容)
            elseif 内容.分类 == "变身卡" then
                   self:合成怪物卡片(连接id,序号,id,内容)
            elseif 内容.分类 == "百炼精铁" then
                   self:合成百炼精铁(连接id,序号,id,内容)
            elseif 内容.分类 == "暗器" then
                   self:合成暗器(连接id,序号,id,内容)
            elseif 内容.分类 == "精魄灵石" then
                  self:合成精魄灵石(连接id,序号,id,内容)


            end
    elseif 内容.类型 == "修理"  then
           self:修理装备(连接id,序号,id,内容)
    elseif 内容.类型 == "熔炼"  then
          if 内容.分类 == "熔炼装备" then
              if 内容.序列1 ==nil or 内容.序列1=="" then 常规提示(id,"#Y请放入正确的材料") return end
              self:熔炼装备(连接id,序号,id,内容)
          elseif 内容.分类 == "还原装备" then
              self:还原装备(连接id,序号,id,内容)
          end
    elseif 内容.类型 == "分解"  then
           if 内容.序列1 ==nil or 内容.序列1=="" then 常规提示(id,"#Y请放入正确的材料") return end
           self:分解装备(连接id,序号,id,内容)
    end

  elseif 序号==4502 then --装备开运
    self:装备开运(连接id,序号,id,内容)
   elseif 序号==4502.1 then --装备开运
    self:装备开启星位(连接id,序号,id,内容)


  elseif 序号==4510 then
    if 内容.序列2 then
      local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
      if  not 装备id  then return end
      if  玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then return end
      local 鉴定等级 =0
      if 内容.序列1 then
          if 内容.序列1==0 then return end
         local 鉴定id =玩家数据[id].角色.数据.道具[内容.序列1]
        if not 鉴定id or 鉴定id ==0 or not 玩家数据[id].道具.数据[鉴定id] or 玩家数据[id].道具.数据[鉴定id]==0 then return end
          鉴定等级=玩家数据[id].道具.数据[鉴定id].子类
          玩家数据[id].道具.数据[鉴定id] = nil
      elseif 内容.数据 and type(内容.数据)=="table" then
            鉴定等级 = 内容.数据.等级
      else
          return
      end
      if 玩家数据[id].道具.数据[装备id].级别限制>鉴定等级 then
         常规提示(id,"#Y你无法鉴定这个道具")
         return
      end
      玩家数据[id].道具.数据[装备id].鉴定 = true
      玩家数据[id].道具.数据[装备id].耐久 =  取随机数(500,1000)
      道具刷新(id)
      常规提示(id,"#Y/恭喜你，装备鉴定成功!")
      if 玩家数据[id].道具.数据[装备id].级别限制>=120 then
          if 玩家数据[id].道具.数据[装备id].特效~=nil and 玩家数据[id].道具.数据[装备id].特效=="无级别限制" then
              local 组合语句=玩家数据[id].道具.数据[装备id].级别限制.."级无级别"
              local 成就提示  = "牛了,无级别!"
              local 成就提示1 = "恭喜鉴定出"..组合语句
              发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
              发送公告("恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/鉴定出"..组合语句.."装备,大家都来膜拜他吧！！！")
          end
          if 玩家数据[id].道具.数据[装备id].赐福~=nil then
              local 成就提示  = "仙人赐福"
              local 成就提示1 = "恭喜玩家仙人赐福,某种属性加强了"
              发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
              发送公告("恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/鉴定装备时,运气爆棚吸引仙人注意降下赐福！！！")
              发送传音("[系统]","#Y/恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/鉴定装备时,运气爆棚吸引仙人注意降下赐福！！！",取随机数(1,4))
          end
      end

    end

  elseif 序号==4511 then
    --self:添加符石(id,内容.道具id,内容.装备id)
  elseif 序号==4512 then
    self:装备附魔(连接id,序号,id,内容)
  elseif 序号==4513 then
    self:技能附魔(连接id,序号,id,内容)
  elseif 序号==4514 then
    --self:装备附魔宝珠(连接id,序号,id,内容)
  elseif 序号==4515 then
    self:装备碎星锤(连接id,序号,id,内容)
  elseif 序号==4516 then
    self:装备灵箓(连接id,序号,id,内容)
  elseif 序号==4517 then
    --幻化处理类:数据处理(连接id,序号,id,内容)
   elseif 序号==4518 then
      if 内容.序列1 and 内容.序列2 then
        local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
        local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
        if not 鉴定id or not 装备id  then return end
        if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
        if 玩家数据[id].道具.数据[装备id].灵饰 or 玩家数据[id].道具.数据[装备id].分类>6 or 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
        if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="仙灵丹" then  常规提示(id,"#Y/数据错误") return  end
           玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
           if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
              玩家数据[id].道具.数据[鉴定id]=nil
           end
          local 成功几率=100
          local 是否赐福 = false
          if 玩家数据[id].道具.数据[装备id].赐福==nil then
             成功几率 = 5
             是否赐福 = true
          end
           if 取随机数()<=成功几率 then
              玩家数据[id].道具.数据[装备id].赐福={总类="",类型="",数值=0}
              local 随机总类={"基础","战斗"}
              玩家数据[id].道具.数据[装备id].赐福.总类=随机总类[取随机数(1,2)]
              if 玩家数据[id].道具.数据[装备id].赐福.总类=="基础" then
                 local 随机类型 ={"气血","魔法","命中","伤害","防御","速度","躲避","灵力","体质","魔力","力量","耐力","敏捷",
                       "气血回复效果","抗法术暴击等级","格挡值","法术防御","抗物理暴击等级","封印命中等级","穿刺等级",
                        "抵抗封印等级","固定伤害","法术伤害","法术暴击等级","物理暴击等级","狂暴等级","法术伤害结果",
                        "治疗能力"}
                 玩家数据[id].道具.数据[装备id].赐福.类型= 随机类型[取随机数(1,#随机类型)]
                 if 玩家数据[id].道具.数据[装备id].赐福.类型=="体质" or 玩家数据[id].道具.数据[装备id].赐福.类型=="魔力" or 玩家数据[id].道具.数据[装备id].赐福.类型=="力量" or 玩家数据[id].道具.数据[装备id].赐福.类型=="耐力" or 玩家数据[id].道具.数据[装备id].赐福.类型=="敏捷" then
                      玩家数据[id].道具.数据[装备id].赐福.数值=取随机数(5,20)
                  elseif 玩家数据[id].道具.数据[装备id].赐福.类型=="气血" or 玩家数据[id].道具.数据[装备id].赐福.类型=="魔法" then
                      玩家数据[id].道具.数据[装备id].赐福.数值=取随机数(50,200)
                  elseif 玩家数据[id].道具.数据[装备id].赐福.类型=="命中" or 玩家数据[id].道具.数据[装备id].赐福.类型=="伤害" or 玩家数据[id].道具.数据[装备id].赐福.类型=="防御" or 玩家数据[id].道具.数据[装备id].赐福.类型=="速度" or 玩家数据[id].道具.数据[装备id].赐福.类型=="躲避" or 玩家数据[id].道具.数据[装备id].赐福.类型=="灵力" then
                      玩家数据[id].道具.数据[装备id].赐福.数值=取随机数(20,50)
                  else
                      玩家数据[id].道具.数据[装备id].赐福.数值=取随机数(10,25)
                  end
              else
                 local 随机类型 ={"伤害结果","法伤结果","物伤结果","固伤结果","治疗结果","伤害减免","物伤减免","法伤减免","固伤减免","技能连击"}
                 玩家数据[id].道具.数据[装备id].赐福.类型= 随机类型[取随机数(1,#随机类型)]
                 玩家数据[id].道具.数据[装备id].赐福.数值=取随机数(1,3)
              end

              常规提示(id,"#Y/恭喜你，装备重置成功!")
              if 是否赐福 then
                 local 成就提示  = "仙人赐福"
                 local 成就提示1 = "恭喜玩家仙人赐福,某种属性加强了"
                 发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                 发送公告("恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/鉴定装备时,运气爆棚吸引仙人注意降下赐福！！！")
                 发送传音("[系统]","#Y/恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/鉴定装备时,运气爆棚吸引仙人注意降下赐福！！！",取随机数(1,4))
                 玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                 常规提示(id,"#Y/恭喜你获得了1成就积分")
              end
           else
              常规提示(id,"#Y/很遗憾，装备添加赐福失败!")
          end
          道具刷新(id)
         end
   elseif 序号==4519 then
          if 内容.序列1 and 内容.序列2 then
              local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
              local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
              if not 鉴定id or not 装备id  then return end
              if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
              if 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="特效点化石" then  常规提示(id,"#Y/数据错误") return  end
              if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
              if 玩家数据[id].道具.数据[装备id].分类>6  or 玩家数据[id].道具.数据[装备id].灵饰 then 常规提示(id,"#Y/只有装备可以点化") return  end
              玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
                 玩家数据[id].道具.数据[鉴定id]=nil
              end
              local 成功几率=100
              if 玩家数据[id].道具.数据[装备id].特效==nil  then
                 成功几率 = 5
              end
               if 取随机数()<=成功几率 then
                   local 通用特效 = {"神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生","易修理","超级简易"}
                    if 玩家数据[id].道具.数据[装备id].分类 == 5 then
                       table.insert(通用特效,"愤怒")
                       table.insert(通用特效,"暴怒")
                    end
                    玩家数据[id].道具.数据[装备id].特效=通用特效[取随机数(1,#通用特效)]
                    常规提示(id,"#Y/恭喜你，装备添加"..玩家数据[id].道具.数据[装备id].特效.."成功!")
                 else
                     常规提示(id,"#Y/很遗憾,本次添加特效失败了!")
                 end
                 道具刷新(id)
            end
      elseif 序号==4520 then

           if 内容.序列1 and 内容.序列2 then
              local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
              local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
              if not 鉴定id or not 装备id  then return end
              if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
              if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
              if 玩家数据[id].道具.数据[装备id].灵饰 or 玩家数据[id].道具.数据[装备id].分类>6  or 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="特技点化石" then  常规提示(id,"#Y/数据错误") return  end
                玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
                if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
                   玩家数据[id].道具.数据[鉴定id]=nil
                end
                local 成功几率=100
                if 玩家数据[id].道具.数据[装备id].特技==nil then
                   成功几率 = 5
                end
                 if 取随机数()<=成功几率 then
                   local 通用特技 = {"修罗咒","天衣无缝","气疗术","心疗术","命疗术","凝气诀","凝神诀","气归术","命归术","四海升平","回魂咒",
                        "起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼","碎甲术","破甲术",
                        "破血狂攻","罗汉金钟","慈航普渡","太极护法","光辉之甲","圣灵之甲","野兽之力","魔兽之印","流云诀","啸风诀",
                        "笑里藏刀","绝幻魔音","凝滞术","停陷术","破碎无双","琴音三叠","菩提心佑","先发制人","身似菩提"}
                         玩家数据[id].道具.数据[装备id].特技=通用特技[取随机数(1,#通用特技)]
                         常规提示(id,"#Y/恭喜你，装备添加"..玩家数据[id].道具.数据[装备id].特技.."成功!")
                 else
                     常规提示(id,"#Y/很遗憾,本次添加特技失败了!")
                 end
                 道具刷新(id)
            end
       elseif 序号==4521 then
          if 内容.序列1 and 内容.序列2 then
              local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
              local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
              if not 鉴定id or not 装备id  then return end
              if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
              if 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="无级别点化石" then  常规提示(id,"#Y/数据错误") return  end
              if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
              if 玩家数据[id].道具.数据[装备id].分类>6  or 玩家数据[id].道具.数据[装备id].灵饰 then 常规提示(id,"#Y/只有装备可以点化") return  end
              玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
                 玩家数据[id].道具.数据[鉴定id]=nil
              end
              local 成功几率=100
              local 是否赐福 = false
              if 玩家数据[id].道具.数据[装备id].第二特效==nil then
                 成功几率 = 5
                 是否赐福 = true
              end

               if 取随机数()<=成功几率 then
                    玩家数据[id].道具.数据[装备id].第二特效="无级别限制"
                    常规提示(id,"#Y/恭喜你，装备添加"..玩家数据[id].道具.数据[装备id].第二特效.."成功!")
                     if 是否赐福 and 玩家数据[id].道具.数据[装备id].级别限制>=120 and 玩家数据[id].道具.数据[装备id].第二特效=="无级别限制" then
                        local 组合语句=玩家数据[id].道具.数据[装备id].级别限制.."级无级别"
                        local 成就提示  = "牛了,无级别!"
                        local 成就提示1 = "恭喜鉴定出"..组合语句
                        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                        发送公告("恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/一次就附加出"..组合语句.."装备,大家都来膜拜他吧！！！")
                        玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                        常规提示(id,"#Y/恭喜你获得了1成就积分")
                     end
                 else
                     常规提示(id,"#Y/很遗憾,本次添加特效失败了!")
                 end
                 道具刷新(id)
            end
        elseif 序号==4522 then
          if 内容.序列1 and 内容.序列2 then
              local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
              local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
              if not 鉴定id or not 装备id  then return end
              if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
              if 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="灵饰点化石" then  常规提示(id,"#Y/数据错误") return  end
              if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
              if not 玩家数据[id].道具.数据[装备id].灵饰 then 常规提示(id,"#Y/只有灵饰可以点化") return  end
              玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
              if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
                 玩家数据[id].道具.数据[鉴定id]=nil
              end
              local 成功几率=100
              local 是否赐福 = false
              if 玩家数据[id].道具.数据[装备id].特效==nil then
                 成功几率 = 5
                 是否赐福 = true
              end

               if 取随机数()<=成功几率 then
                   local 通用特效 = {"无级别限制","简易","超级简易"}
                    玩家数据[id].道具.数据[装备id].特效=通用特效[取随机数(1,#通用特效)]
                    常规提示(id,"#Y/恭喜你，装备添加"..玩家数据[id].道具.数据[装备id].特效.."成功!")
                     if 是否赐福 and 玩家数据[id].道具.数据[装备id].级别限制>=120 and 玩家数据[id].道具.数据[装备id].特效=="无级别限制" then
                        local 组合语句=玩家数据[id].道具.数据[装备id].级别限制.."级无级别"
                        local 成就提示  = "牛了,无级别!"
                        local 成就提示1 = "恭喜鉴定出"..组合语句
                        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                        发送公告("恭喜玩家#G/"..玩家数据[id].角色.数据.名称.."#Y/一次就附加出"..组合语句.."装备,大家都来膜拜他吧！！！")
                        玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                        常规提示(id,"#Y/恭喜你获得了1成就积分")
                     end
                 else
                     常规提示(id,"#Y/很遗憾,本次添加特效失败了!")
                 end
                 道具刷新(id)
            end
        elseif 序号==4523 then
              if 内容.序列1 and 内容.序列2 then
                  local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
                  local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
                  if not 鉴定id or not 装备id  then return end
                  if 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
                  if 玩家数据[id].道具.数据[鉴定id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
                  if 玩家数据[id].道具.数据[鉴定id].数量<=0 or 玩家数据[id].道具.数据[鉴定id].名称~="灵饰洗炼石" then  常规提示(id,"#Y/数据错误") return  end
                  if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
                  if not 玩家数据[id].道具.数据[装备id].灵饰 then 常规提示(id,"#Y/只有灵饰可以洗练") return  end
                  玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
                  if 玩家数据[id].道具.数据[鉴定id].数量<=0 then
                     玩家数据[id].道具.数据[鉴定id]=nil
                  end
                  local 临时类型 = 玩家数据[id].道具.数据[装备id].部位类型
                  local 数量上限 = #玩家数据[id].道具.数据[装备id].幻化属性.附加
                  local 级别限制 = 玩家数据[id].道具.数据[装备id].级别限制
                  local 洗练属性=""
                  local 幻化等级=0
                  if 玩家数据[id].道具.数据[装备id].幻化等级~=nil and tonumber(玩家数据[id].道具.数据[装备id].幻化等级)~=nil and tonumber(玩家数据[id].道具.数据[装备id].幻化等级)~=0 then
                      幻化等级=玩家数据[id].道具.数据[装备id].幻化等级
                  end
                  for n=1,数量上限 do
                        local 副属性=灵饰属性[临时类型].副属性[取随机数(1,#灵饰属性[临时类型].副属性)]
                        local 下限 = 灵饰属性.基础[副属性][级别限制].a+级别限制/20
                        local 上限 = 灵饰属性.基础[副属性][级别限制].b+级别限制/10
                        local 副数值=math.floor(取随机数(下限,上限))
                        玩家数据[id].道具.数据[装备id].幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
                        --玩家数据[id].道具.数据[装备id].幻化属性.附加[n].强化=math.floor(灵饰强化[副属性]*幻化等级)
                        玩家数据[id].道具.数据[装备id].幻化属性.附加[n].强化=取灵饰强化(副属性,级别限制,幻化等级)
                        洗练属性=洗练属性..副属性..","
                  end



                  常规提示(id,"#Y/灵饰属性已变更为:"..洗练属性)
                  道具刷新(id)
              end

        elseif 序号==4524 then
            if 内容.序列1 and 内容.序列2 then
                local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
                local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
                if not 鉴定id or not 装备id  then return end

                self:装备境界处理(id,装备id,鉴定id,内容.序列1)
            end






  end
end










function 装备处理类:装备境界处理(id,装备id,道具id,道具格子)
      if 玩家数据[id].道具.数据[道具id]==nil or 玩家数据[id].道具.数据[装备id]==nil then 常规提示(id,"#Y/数据错误") return end
      if 玩家数据[id].道具.数据[道具id].数量==nil then  常规提示(id,"#Y/数据错误") return  end
      if 玩家数据[id].道具.数据[道具id].数量<=0 then  常规提示(id,"#Y/数据错误") return  end
      if not 玩家数据[id].道具.数据[装备id].鉴定  then 常规提示(id,"#Y/未鉴定的装备无法使用") return  end
      if 玩家数据[id].道具.数据[装备id].分类>6  then 常规提示(id,"#Y/只有装备才可以使用该道具") return  end
      if not 玩家数据[id].道具.数据[装备id].装备境界 then
          玩家数据[id].道具.数据[装备id].装备境界={品质="普通",升级值=0,洗练值=0,神话值=0,词条={},词条共鸣=false}
      end
      local 使用成功=false
      local 操作类型 = 玩家数据[id].道具.数据[道具id].名称
      local 装备分类 = 玩家数据[id].道具.数据[装备id].分类
      local 装备境界 = 玩家数据[id].道具.数据[装备id].装备境界.品质
      local 装备词条 = DeepCopy(玩家数据[id].道具.数据[装备id].装备境界.词条)
      if 操作类型 == "鸿蒙灵宝" then
          if 装备境界=="传说" or 装备境界=="神话" then
              玩家数据[id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[id].道具.数据[装备id].装备境界.洗练值 + 3
              常规提示(id,"#Y/装备增加了#R3#Y点仙宝值")
          else
                local 成功几率 = 2
                if 装备境界=="普通" then
                    成功几率 =6
                elseif 装备境界=="优秀" then
                   成功几率 =3
                end
                成功几率 = 成功几率 + math.floor(玩家数据[id].道具.数据[装备id].装备境界.升级值/100)
                if 成功几率>=取随机数() then
                      if 装备境界=="普通" then
                          玩家数据[id].道具.数据[装备id].装备境界.品质="优秀"
                          常规提示(id,"#Y/你的装备境界提升,当前装备境界#G优秀")

                      elseif 装备境界=="优秀" then
                          玩家数据[id].道具.数据[装备id].装备境界.品质="稀有"
                          常规提示(id,"#Y/你的装备境界提升,当前装备境界#L稀有")
                      elseif 装备境界=="稀有" then
                          玩家数据[id].道具.数据[装备id].装备境界.品质="传说"
                          常规提示(id,"#Y/你的装备境界提升,当前装备境界#X传说")
                      end
                      玩家数据[id].道具.数据[装备id].装备境界.升级值 = 0
                      玩家数据[id].道具.数据[装备id].装备境界.洗练值 = 0
                      玩家数据[id].道具.数据[装备id].装备境界.神话值 = 0
                else
                     if 装备境界=="普通" then
                          玩家数据[id].道具.数据[装备id].装备境界.升级值 = 玩家数据[id].道具.数据[装备id].装备境界.升级值 + 4
                          常规提示(id,"#Y/装备境界提升失败,增加#R4#Y点境界值")
                      elseif 装备境界=="优秀" then
                              玩家数据[id].道具.数据[装备id].装备境界.升级值 = 玩家数据[id].道具.数据[装备id].装备境界.升级值 + 2
                              常规提示(id,"#Y/装备境界提升失败,增加#R2#Y点境界值")
                      elseif 装备境界=="稀有" then
                              玩家数据[id].道具.数据[装备id].装备境界.升级值 = 玩家数据[id].道具.数据[装备id].装备境界.升级值 + 1
                              常规提示(id,"#Y/装备境界提升失败,增加#R1#Y点境界值")
                      end
                end
          end
          使用成功=true
    elseif 操作类型 == "鸿蒙仙宝" then
              if  装备境界=="普通" then
                  常规提示(id,"#Y/你的装备境界太低了,无法使用该道具")
                  return
              end
              local 临时列表 = {}
              for k,v in pairs(境界属性) do
                  if v.分类 and v.分类[装备分类] then
                      table.insert(临时列表, k)
                  end
              end
              local 临时词条= 临时列表[取随机数(1,#临时列表)]
              local 临时数额=取随机数(境界属性[临时词条][装备境界][1],境界属性[临时词条][装备境界][2])
              local 添加 = 0
              if 装备境界=="优秀" then
                  if not 装备词条[1] then
                    添加 = 1
                  end
              elseif 装备境界=="稀有" then
                    if not 装备词条[1] then
                      添加 = 1
                    elseif 装备词条[1] and not 装备词条[2] then
                          添加 = 2
                    end
              elseif 装备境界=="传说" or 装备境界=="神话" then
                    if not 装备词条[1] then
                        添加 = 1
                    elseif 装备词条[1] and not 装备词条[2] then
                          添加 = 2
                    elseif 装备词条[1] and 装备词条[2] and not 装备词条[3]  then
                          添加 = 3
                    end
              end
              local 洗练经验 = math.floor(玩家数据[id].道具.数据[装备id].装备境界.洗练值/100)
              if 添加~=0 then
                    local 添加几率 = 0
                    if 添加==1 then
                        添加几率 = 6+洗练经验
                    elseif 添加==2 then
                          添加几率 = 3+洗练经验
                    elseif 添加==3 then
                          添加几率 = 2+洗练经验
                    end
                    if 添加几率>=取随机数() then
                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=0
                          if 添加==1 then
                               玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                               常规提示(id,"#Y/装备添加词条成功,本次添加词条为:"..临时词条..",仙宝值清空")
                          elseif 添加==2  then
                                  if 临时词条 == 装备词条[1].类型 then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=装备词条[1].类型,数额=math.floor(装备词条[1].数额/2)}
                                      常规提示(id,"#Y/恭喜你添加出双同词条,本次添加词条为:"..装备词条[1].类型..",仙宝值清空")
                                  else
                                       玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条,数额=math.floor(临时数额/2)}
                                       常规提示(id,"#Y/装备添加词条成功,本次添加词条为:"..临时词条..",仙宝值清空")
                                  end
                          else
                              if 临时词条== 装备词条[1].类型 and 临时词条~= 装备词条[2].类型 then
                                  玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=装备词条[1].类型,数额=math.floor(装备词条[1].数额/2)}
                                  常规提示(id,"#Y/恭喜你添加出双同词条,本次添加词条为:"..装备词条[1].类型..",仙宝值清空")
                              elseif 临时词条~= 装备词条[1].类型 and 临时词条== 装备词条[2].类型  then
                                       玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=装备词条[2].类型,数额=装备词条[2].数额}
                                      常规提示(id,"#Y/恭喜你添加出双同词条,本次添加词条为:"..装备词条[2].类型..",仙宝值清空")
                              elseif 临时词条== 装备词条[1].类型 and 临时词条== 装备词条[2].类型  then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=装备词条[1].类型,数额=math.floor(装备词条[1].数额/2)}
                                      常规提示(id,"#Y/恭喜你添加出三同词条,本次添加词条为:"..装备词条[1].类型..",仙宝值清空")
                              else
                                   玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条,数额=math.floor(临时数额/2)}
                                   常规提示(id,"#Y/装备添加词条成功,本次添加词条为:"..临时词条..",仙宝值清空")
                              end
                          end
                    else
                        local 添加数量=0
                        if 装备词条[1] then
                            添加数量=添加数量+1
                        end
                        if 装备词条[2] then
                            添加数量=添加数量+1
                        end
                        if 装备词条[3] then
                            添加数量=添加数量+1
                        end
                        if 添加数量==0 then
                            玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                            常规提示(id,"#Y/装备添加词条失败,增加#R4#Y点仙宝值")
                        elseif 添加数量==1 then
                              玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                               常规提示(id,"#Y/装备添加词条失败,增加#R2#Y点仙宝值")
                        else
                              玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+1
                              常规提示(id,"#Y/装备添加词条失败,增加#R1#Y点仙宝值")
                        end
                    end
              else
                    玩家数据[id].道具.数据[装备id].装备境界.词条共鸣=false
                    if 装备境界=="优秀" then
                          玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                          常规提示(id,"#Y/洗练装备词条成功,本次洗练词条为:"..临时词条)
                    elseif 装备境界=="稀有" then
                            for k,v in pairs(临时列表) do
                                if v==临时词条 then
                                    table.remove(临时列表,k)
                                end
                            end
                            local 临时词条1= 临时列表[取随机数(1,#临时列表)]
                            local 临时数额1=取随机数(境界属性[临时词条1][装备境界][1],境界属性[临时词条1][装备境界][2])
                            if 装备词条[1] and 装备词条[2] then
                                if 洗练经验>=取随机数() then
                                    玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=装备词条[1].类型,数额=math.floor(装备词条[1].数额/2)}
                                    玩家数据[id].道具.数据[装备id].装备境界.洗练值= 0
                                    常规提示(id,"#Y/恭喜你洗练出同类词条,本次洗练词条为:"..装备词条[1].类型..",仙宝值清空")
                                else
                                    玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                    玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                    玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                                    常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..",增加#R2#Y点仙宝值")
                                end
                            else
                                  local 添加数量=0
                                  if 装备词条[1] then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                  end
                                  if 装备词条[2] then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                  end

                                  if 装备词条[1] and not 装备词条[2] then
                                        玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                                        常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..",增加#R4#Y点仙宝值")
                                  elseif not 装备词条[1] and  装备词条[2] then
                                        玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                                        常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条1..",增加#R4#Y点仙宝值")
                                  end
                            end
                    elseif 装备境界=="传说" or 装备境界=="神话" then
                            for k,v in pairs(临时列表) do
                                if v==临时词条 then
                                    table.remove(临时列表,k)
                                end
                            end
                            local 临时词条1= 临时列表[取随机数(1,#临时列表)]
                            local 临时数额1=取随机数(境界属性[临时词条1][装备境界][1],境界属性[临时词条1][装备境界][2])
                            for k,v in pairs(临时列表) do
                                if v==临时词条1 then
                                    table.remove(临时列表,k)
                                end
                            end
                            local 临时词条2= 临时列表[取随机数(1,#临时列表)]
                            local 临时数额2=取随机数(境界属性[临时词条2][装备境界][1],境界属性[临时词条2][装备境界][2])
                            if 装备词条[1] and 装备词条[2] and 装备词条[3] then---都有
                               if 装备词条[1].类型==装备词条[2].类型 and 装备词条[1].类型==装备词条[3].类型  then---都相同
                                  if 玩家数据[id].道具.数据[装备id].装备境界.洗练值>=900 then
                                      local 对话内容 = "请选择你要更换的词条类型,更换后该类型达到最大值"
                                      local 对话模型=玩家数据[id].角色.数据.模型
                                      local 对话名称=玩家数据[id].角色.数据.名称
                                      local 对话选项 = {}
                                      for k,v in pairs(境界属性) do
                                          if v.分类 and v.分类[装备分类] then
                                              table.insert(对话选项, k)
                                          end
                                      end
                                      发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
                                      玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="鸿蒙仙宝"}
                                      return
                                  else
                                     玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                     玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条,数额=math.floor(临时数额/2)}
                                     玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条,数额=math.floor(临时数额/2)}
                                     常规提示(id,"#Y/恭喜你装备词条洗练成功,本次洗练词条为:"..临时词条)
                                  end
                               else---有不同
                                    local 查找不同 = 0
                                    if 装备词条[1].类型==装备词条[2].类型 and 装备词条[1].类型~=装备词条[3].类型 then --12 同
                                        查找不同=3
                                    elseif 装备词条[1].类型~=装备词条[2].类型 and 装备词条[1].类型==装备词条[3].类型 then --13同
                                            查找不同=2
                                    elseif 装备词条[1].类型~=装备词条[2].类型 and 装备词条[2].类型==装备词条[3].类型 then --23同
                                            查找不同=1
                                    end
                                    if 查找不同==0 then---3不同
                                        if 洗练经验>=取随机数() then--洗出2同
                                              local 词条几率=取随机数(1,3)
                                              if 词条几率==1 then
                                                 玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                 玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                 玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                              elseif 词条几率==2 then
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条,数额=math.floor(临时数额/2)}

                                              elseif 词条几率==2 then
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条,数额=math.floor(临时数额/2)}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                              end
                                              玩家数据[id].道具.数据[装备id].装备境界.洗练值= 0
                                              常规提示(id,"#Y/恭喜你洗练双同词条,本次洗练词条为:"..临时词条..","..临时词条1..",仙宝值清空")
                                        else
                                              玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                              玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                              玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条2,数额=math.floor(临时数额2/2)}
                                              玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                                              常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..","..临时词条2..",增加#R2#Y点仙宝值")
                                        end
                                    else---2不同
                                          if 洗练经验>=取随机数() then---洗出三同
                                                玩家数据[id].道具.数据[装备id].装备境界.洗练值= 0
                                                玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条,数额=math.floor(临时数额/2)}
                                                玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条,数额=math.floor(临时数额/2)}
                                                常规提示(id,"#Y/恭喜你洗练三同词条,本次洗练词条为:"..临时词条..",仙宝值清空")
                                          else
                                                玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+1
                                                if 查找不同==1 then
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                    玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                    常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..",增加#R1#Y点仙宝值")
                                                elseif 查找不同==2 then
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条,数额=math.floor(临时数额/2)}
                                                      常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..",增加#R1#Y点仙宝值")
                                                elseif 查找不同==3 then
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条,数额=math.floor(临时数额/2)}
                                                      玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                                      常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..",增加#R1#Y点仙宝值")
                                                end
                                          end
                                    end
                               end
                            else---缺少词条
                                  if 装备词条[1] then
                                    玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=临时词条,数额=临时数额}
                                  end
                                  if 装备词条[2] then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[2]={类型=临时词条1,数额=math.floor(临时数额1/2)}
                                  end
                                  if 装备词条[3] then
                                      玩家数据[id].道具.数据[装备id].装备境界.词条[3]={类型=临时词条2,数额=math.floor(临时数额2/2)}
                                  end
                                  if 装备词条[1] and not 装备词条[2] and not 装备词条[3] then---1有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..",增加#R4#Y点仙宝值")
                                  elseif not 装备词条[1] and  装备词条[2] and not 装备词条[3]then---2有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条1..",增加#R4#Y点仙宝值")
                                  elseif not 装备词条[1] and not 装备词条[2] and 装备词条[3]then---3有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+4
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条2..",增加#R4#Y点仙宝值")
                                  elseif 装备词条[1] and  装备词条[2] and not 装备词条[3] then---1 2 有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条1..",增加#R2#Y点仙宝值")
                                  elseif not 装备词条[1] and  装备词条[2] and  装备词条[3] then--2 3有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条1..","..临时词条2..",增加#R2#Y点仙宝值")
                                  elseif 装备词条[1] and  not 装备词条[2] and  装备词条[3] then--1 3有
                                          玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值+2
                                          常规提示(id,"#Y/装备词条洗练成功,本次洗练词条为:"..临时词条..","..临时词条2..",增加#R2#Y点仙宝值")
                                  end
                            end

                            local 临时境界 = 玩家数据[id].道具.数据[装备id].装备境界
                            if 临时境界.词条 and 临时境界.词条[1] and 临时境界.词条[2] and 临时境界.词条[3]
                              and 临时境界.词条[1].类型==临时境界.词条[2].类型 and 临时境界.词条[1].类型==临时境界.词条[3].类型 then
                                玩家数据[id].道具.数据[装备id].装备境界.词条[2].数额 =math.floor(临时境界.词条[1].数额/2)
                                玩家数据[id].道具.数据[装备id].装备境界.词条[3].数额 =math.floor(临时境界.词条[1].数额/2)
                                玩家数据[id].道具.数据[装备id].装备境界.词条共鸣=境界属性[临时境界.词条[1].类型].共鸣
                            end
                    else
                        常规提示(id,"#Y/该装备数据异常,请联系管理员")
                        return
                    end
              end
              使用成功=true
      elseif 操作类型 == "太初灵石" then
            if 装备境界~="传说" and  装备境界~="神话" then
                常规提示(id,"#Y/只有传说装备或神话装备才可以使用该道具")
                return
            end
            if 装备境界=="神话" then
                  玩家数据[id].道具.数据[装备id].装备境界.神话值 = 玩家数据[id].道具.数据[装备id].装备境界.神话值 + 3
                  常规提示(id,"#Y/该神话装备增加了#R3#Y点神话值")
            else
                if 1 + math.floor(玩家数据[id].道具.数据[装备id].装备境界.神话值/80)>=取随机数() then
                     玩家数据[id].道具.数据[装备id].装备境界.品质="神话"
                     玩家数据[id].道具.数据[装备id].装备境界.神话值 = 0
                     常规提示(id,"#Y/装备境界提升成功,神话值清空")
                else
                    玩家数据[id].道具.数据[装备id].装备境界.神话值 = 玩家数据[id].道具.数据[装备id].装备境界.神话值 + 1
                    常规提示(id,"#Y/装备境界提升失败,增加#R1#Y点神话值")
                end
            end

            使用成功=true

      elseif 操作类型 == "太初仙石" then
            if 装备境界~="神话" then
                常规提示(id,"#Y/只有神话装备才可以使用该道具")
                return
            end
            local 神话词条 = 玩家数据[id].道具.数据[装备id].装备境界.神话词条
            local 临时列表 = {}
            for k,v in pairs(境界属性) do
                  if v.分类 and v.分类[装备分类] then
                      table.insert(临时列表, k)
                  end
            end
            for k,v in pairs(神话属性) do
                  if v[装备分类] then
                      table.insert(临时列表, k)
                  end
            end
            local 临时词条 = 临时列表[取随机数(1,#临时列表)]
            if not 神话词条 or 神话词条=="" then
                if 1+math.floor(玩家数据[id].道具.数据[装备id].装备境界.神话值/50)>=取随机数() then
                      玩家数据[id].道具.数据[装备id].装备境界.神话词条=临时词条
                      玩家数据[id].道具.数据[装备id].装备境界.神话值= 0
                      常规提示(id,"#Y/恭喜你神话词条添加成功,本次洗练词条为:"..临时词条..",神话值清空")
                else
                    玩家数据[id].道具.数据[装备id].装备境界.神话值=玩家数据[id].道具.数据[装备id].装备境界.神话值+1
                    常规提示(id,"#Y/神话词条添加失败,增加#R1#Y点神话值")
                end

            else
                if 玩家数据[id].道具.数据[装备id].装备境界.神话值>=600 then
                      local 对话内容 = "请选择你要添加的神话词条"
                      local 对话模型=玩家数据[id].角色.数据.模型
                      local 对话名称=玩家数据[id].角色.数据.名称
                      local 对话选项=DeepCopy(临时列表)
                      发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
                      玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="太初仙石"}
                     return
                else
                    玩家数据[id].道具.数据[装备id].装备境界.神话词条=临时词条
                    常规提示(id,"#Y/恭喜你神话词条洗练成功,本次洗练词条为:"..临时词条)
                end
            end
            使用成功=true


      elseif 操作类型 == "鸿蒙神宝" then
             if  装备境界=="普通" then
                  常规提示(id,"#Y/你的装备境界太低了,无法使用该道具")
                  return
              end
              if 装备词条 then
                local 对话成功 = false
                local  对话内容 = "请选中提取的词条,提取后词条会消失"
                local 对话选项={}
                if 装备词条[1] then
                    对话成功 = true
                    table.insert(对话选项,"提取第一词条:"..装备词条[1].类型)
                end
                if 装备词条[2] then
                    对话成功 = true
                    table.insert(对话选项,"提取第二词条:"..装备词条[2].类型)

                end
                if 装备词条[3] then
                    对话成功 = true
                    table.insert(对话选项,"提取第三词条:"..装备词条[3].类型)
                end
                if 对话成功 then
                    local 对话模型=玩家数据[id].角色.数据.模型
                    local 对话名称=玩家数据[id].角色.数据.名称
                    发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
                    玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="鸿蒙神宝"}
                else
                  常规提示(id,"#Y/你的装备没有词条")
                end

              else
                  常规提示(id,"#Y/你的装备没有词条")
              end
              return
      elseif 操作类型 == "太初神石" then
              if 装备境界~="神话" then
                  常规提示(id,"#Y/只有神话装备才可以使用该道具")
                  return
              end
              local  神话词条 = 玩家数据[id].道具.数据[装备id].装备境界.神话词条
              if not 神话词条 or 神话词条=="" then
                  常规提示(id,"#Y/该装备还没有神话词条")
                  return
              end
              local 对话内容 = "你确定吸附该装备的神话词条么?"
              local 对话模型=玩家数据[id].角色.数据.模型
              local 对话名称=玩家数据[id].角色.数据.名称
              local 对话选项={"确定吸附神话词条"}
              发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="太初神石"}
              return
      elseif 操作类型 == "鸿蒙原石" then
              local 附带词条 = 玩家数据[id].道具.数据[道具id].附带词条
              local 词条数额 = 玩家数据[id].道具.数据[道具id].数额
              if not 附带词条 or not 词条数额 then 常规提示(id,"#Y/数据错误") return end
              if 装备境界=="普通" then 常规提示(id,"#Y/你的装备境界太低了,无法使用该道具") return  end
              if not 境界属性[附带词条] then 常规提示(id,"#Y/数据错误") return end
              if not 境界属性[附带词条].分类[装备分类] then 常规提示(id,"#Y/该装备无法使用") return end
              local 是否对话 =false
              local 对话内容 = "请选更换词条还是附魔词条"
              local 对话模型=玩家数据[id].角色.数据.模型
              local 对话名称=玩家数据[id].角色.数据.名称
              local 对话选项={}
              local 添加 = 0
              if 装备境界=="优秀" then
                  if not 装备词条[1] then
                    添加 = 1
                  end
              elseif 装备境界=="稀有" then
                    if not 装备词条[1] then
                      添加 = 1
                    elseif 装备词条[1] and not 装备词条[2] then
                          添加 = 2
                    end
              elseif 装备境界=="传说" or 装备境界=="神话" then
                    if not 装备词条[1] then
                        添加 = 1
                    elseif 装备词条[1] and not 装备词条[2] then
                          添加 = 2
                    elseif 装备词条[1] and 装备词条[2] and not 装备词条[3]  then
                          添加 = 3
                    end
              end
              if 添加~=0 then
                  玩家数据[id].道具.数据[装备id].装备境界.词条共鸣 = false
                  if 添加==1 then
                        if not 装备词条[2] and not 装备词条[3] then
                            if 玩家数据[id].道具.数据[装备id].装备境界.洗练值<200 then 常规提示(id,"#Y/添加词条需要200仙宝值") return end
                            玩家数据[id].道具.数据[装备id].装备境界.洗练值=玩家数据[id].道具.数据[装备id].装备境界.洗练值-200
                            玩家数据[id].道具.数据[装备id].装备境界.词条[1]={类型=附带词条,数额=词条数额}
                            常规提示(id,"#Y/你对装备第一个词条添加了"..附带词条)
                            使用成功=true
                        else
                            对话选项={"添加第一词条"}
                            是否对话=true
                        end
                  elseif 添加==2 then
                          对话选项={"添加第二词条"}
                          是否对话=true
                  elseif 添加==3 then
                          对话选项={"添加第三词条"}
                          是否对话=true
                  end
              elseif 装备词条[1] or 装备词条[2] or 装备词条[3] then
                      是否对话=true
              end

              if 是否对话 then
                  if 装备词条[1] then
                        table.insert(对话选项,"更换第一词条:"..装备词条[1].类型)
                  end
                  if 装备词条[2] then
                      table.insert(对话选项,"更换第二词条:"..装备词条[2].类型)
                  end
                  if 装备词条[3] then
                      table.insert(对话选项,"更换第三词条:"..装备词条[3].类型)
                  end
                  发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
                  玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="鸿蒙原石"}
                  return
              end
      elseif 操作类型 == "太初原石" then
              local 附带词条 = 玩家数据[id].道具.数据[道具id].附带词条
              if not 附带词条 then 常规提示(id,"#Y/数据错误") return end
              if 装备境界~="神话" then  常规提示(id,"#Y/只有神话装备才可以使用该道具") return end
              if not 境界属性[附带词条] and not 神话属性[附带词条] then 常规提示(id,"#Y/数据错误") return end
              if 境界属性[附带词条] and not  境界属性[附带词条].分类[装备分类] then 常规提示(id,"#Y/该装备无法使用") return end
              if 神话属性[附带词条] and not 神话属性[附带词条][装备分类] then 常规提示(id,"#Y/该装备无法使用") return end
              local 对话内容 = "你确定要这样操作神话词条"
              local 对话模型=玩家数据[id].角色.数据.模型
              local 对话名称=玩家数据[id].角色.数据.名称
              local 对话选项={"确定更换神话词条"}
              local  神话词条 = 玩家数据[id].道具.数据[装备id].装备境界.神话词条
              if not 神话词条 or 神话词条=="" then
                    对话选项={"确定添加神话词条"}
              else
                  对话选项={"确定更换神话词条"..神话词条}
              end
              发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              玩家数据[id].提取词条={装备id=装备id,道具id=道具id,道具格子=道具格子,名称="太初原石"}
              return
      end

      if 使用成功 then
         玩家数据[id].道具.数据[道具id].数量 = 玩家数据[id].道具.数据[道具id].数量 - 1
         if 玩家数据[id].道具.数据[道具id].数量<=0 then
              玩家数据[id].道具.数据[道具id]=nil
         end
         道具刷新(id)
      end

end








function 装备处理类:打造任务处理(连接id,序号,id,内容)
  local 格子1=玩家数据[id].角色.数据.道具[内容.格子[1]]
  if 玩家数据[id].道具.数据[格子1]==nil  then
    道具刷新(id)
    return
  end
  local 物品名称 =  玩家数据[id].道具.数据[格子1].名称
  local 任务id = 玩家数据[id].角色:取任务(5)
  if 物品名称 == nil then return end
  if 任务数据[任务id] == nil then 玩家数据[id].角色:刷新任务跟踪() return end
  if 物品名称~= 任务数据[任务id].石头 then
    常规提示(id,"#Y/我需要#R/"..任务数据[任务id].石头..",#Y/并不需要#R/"..物品名称)
    道具刷新(id)
     return
  end
   if 玩家数据[id].道具.数据[格子1].数量 == nil then return end
  if 玩家数据[id].道具.数据[格子1].数量 < 任务数据[任务id].数量 then
     常规提示(id,"#Y/我需要#R/"..任务数据[任务id].数量.."#Y/个强化石,你的强化石数量不够")
     道具刷新(id)
     return
  end

   玩家数据[id].道具.数据[格子1].数量=玩家数据[id].道具.数据[格子1].数量 - 任务数据[任务id].数量
   if 玩家数据[id].道具.数据[格子1].数量<= 0 then
      玩家数据[id].道具.数据[格子1] = nil
      玩家数据[id].角色.数据.道具[内容.格子[1]]=nil
   end
  local 临时等级 = 任务数据[任务id].级别
  if 任务数据[任务id].打造类型 == "装备" then
    local 特殊几率 = 5
    local 熟练度 = 玩家数据[id].角色.数据.打造熟练度.打造技巧
      if 任务数据[任务id].序列<= 18 then
        熟练度 = 玩家数据[id].角色.数据.打造熟练度.打造技巧
      elseif 任务数据[任务id].序列== 21 or 任务数据[任务id].序列 == 19 then
        熟练度 = 玩家数据[id].角色.数据.打造熟练度.裁缝技巧
      elseif 任务数据[任务id].序列 == 20 or 任务数据[任务id].序列 == 22 or 任务数据[任务id].序列 == 23 then
        熟练度 = 玩家数据[id].角色.数据.打造熟练度.炼金术
      end

      if 任务数据[任务id].购买~=nil then
         熟练度 = 任务数据[任务id].购买.熟练度
         玩家数据[id].摊位购买打造=nil
      end

      if 熟练度>=5000 and 熟练度<10000 then
        特殊几率 = 8
      elseif 熟练度>=10000 and 熟练度<25000  then
        特殊几率 = 11
      elseif 熟练度>=25000 and 熟练度<50000  then
        特殊几率 = 14
      elseif 熟练度>=50000 and 熟练度<80000  then
        特殊几率 = 17
      elseif 熟练度>=80000 then
        特殊几率 = 20
      end

    self:添加强化打造装备(id,任务id,特殊几率,熟练度)
    local 添加熟练度 =  math.floor(取随机数(临时等级*0.05,临时等级*0.3))

    if 任务数据[任务id].购买~=nil then
        if 玩家数据[任务数据[任务id].购买.对方id] ~= nil then
          if 任务数据[任务id].序列<= 18 then
            玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.打造技巧 = 玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.打造技巧 + 添加熟练度
            常规提示(任务数据[任务id].购买.对方id,"你的#R/打造技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          elseif 任务数据[任务id].序列== 21 or 任务数据[任务id].序列 == 19 then
            玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.裁缝技巧 = 玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.裁缝技巧 + 添加熟练度
            常规提示(任务数据[任务id].购买.对方id,"你的#R/裁缝技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          elseif 任务数据[任务id].序列 == 20 or 任务数据[任务id].序列 == 22 or 任务数据[任务id].序列 == 23 then
            玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.炼金术 = 玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.炼金术 + 添加熟练度
            常规提示(任务数据[任务id].购买.对方id,"你的#R/炼金术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          end
        end
      else
        if 任务数据[任务id].序列<= 18 then
          玩家数据[id].角色.数据.打造熟练度.打造技巧 = 玩家数据[id].角色.数据.打造熟练度.打造技巧 + 添加熟练度
          常规提示(id,"你的#R/打造技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
        elseif 任务数据[任务id].序列== 21 or 任务数据[任务id].序列 == 19 then
          玩家数据[id].角色.数据.打造熟练度.裁缝技巧 = 玩家数据[id].角色.数据.打造熟练度.裁缝技巧 + 添加熟练度
          常规提示(id,"你的#R/裁缝技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
        elseif 任务数据[任务id].序列 == 20 or 任务数据[任务id].序列 == 22 or 任务数据[任务id].序列 == 23 then
          玩家数据[id].角色.数据.打造熟练度.炼金术 = 玩家数据[id].角色.数据.打造熟练度.炼金术 + 添加熟练度
          常规提示(id,"你的#R/炼金术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
        end
      end


   elseif 任务数据[任务id].打造类型 == "灵饰" then
      local 熟练度 = 玩家数据[id].角色.数据.打造熟练度.淬灵之术
      if 任务数据[任务id].购买~=nil then
         熟练度 = 任务数据[任务id].购买.熟练度
         玩家数据[id].摊位购买打造=nil
      end
      self:添加强化打造灵饰(id,任务id,熟练度)
      local 添加熟练度 = math.floor(取随机数(临时等级*0.05,临时等级*0.3))
      if 任务数据[任务id].购买~=nil then
        if 玩家数据[任务数据[任务id].购买.对方id] ~= nil then
          玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.淬灵之术 = 玩家数据[任务数据[任务id].购买.对方id].角色.数据.打造熟练度.淬灵之术 + 添加熟练度
          常规提示(任务数据[任务id].购买.对方id,"你的#R/淬灵之术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
        end

      else
        玩家数据[id].角色.数据.打造熟练度.淬灵之术 = 玩家数据[id].角色.数据.打造熟练度.淬灵之术 + 添加熟练度
        常规提示(id,"你的#R/淬灵之术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
      end
    end
   玩家数据[id].角色:取消任务(任务id)
   任务数据[任务id]=nil
   道具刷新(id)

end



function 装备处理类:装备开启星位(连接id,序号,id,内容)
  -- table.print(内容)
  if not 内容.序列 then return end
  local 装备编号 = 玩家数据[id].角色.数据.道具[内容.序列]
  if not 装备编号 then return end
  if not 玩家数据[id].道具.数据[装备编号] or 玩家数据[id].道具.数据[装备编号]==0 then
      常规提示(id,"少侠，只有人物装备才可以开启星位。")
      return
  elseif 玩家数据[id].道具.数据[装备编号].总类~=2 or 玩家数据[id].道具.数据[装备编号].灵饰 or 玩家数据[id].道具.数据[装备编号].分类>6 then
      常规提示(id,"少侠，只有人物装备才可以开启星位。")
      return
  elseif 玩家数据[id].道具.数据[装备编号].级别限制==nil and 玩家数据[id].道具.数据[装备编号].级别限制<60 then
    常规提示(id,"少侠，需要装备等级达到60级才可以开启星位。")
    return
  elseif 玩家数据[id].道具.数据[装备编号].开运孔数==nil or 玩家数据[id].道具.数据[装备编号].开运孔数.上限<玩家数据[id].道具.数据[装备编号].开运孔数.当前 then
    常规提示(id,"少侠，你的装备孔数没满，不能开启星位。")
    return
  elseif 玩家数据[id].道具.数据[装备编号].星位组~=nil then
    常规提示(id,"少侠，你这件装备已经开启过星位了别闹了（&……*&")
    return
  end
  local 临时消耗 = 取开启星位消耗(玩家数据[id].道具.数据[装备编号].级别限制)
  if 玩家数据[id].角色.数据.银子<临时消耗.金钱 then
    常规提示(id,format("开启星位需要消耗#Z/%s#Y/两银子，你似乎手头有点紧哟？",临时消耗.金钱))
    return
  elseif 玩家数据[id].角色.数据.当前经验<临时消耗.经验 then
    常规提示(id,format("开启星位需要消耗#Z/%s#Y/点经验，你似乎没有那么多的经验？",临时消耗.经验))
    return
  end
  玩家数据[id].角色:扣除银子(临时消耗.金钱,"开启星位",1)
  玩家数据[id].角色:扣除经验(临时消耗.经验,0,1)
  玩家数据[id].道具.数据[装备编号].星位组 = true
  常规提示(id,"#Z/恭喜你开启星位成功！")
  道具刷新(id)
end

function 装备处理类:装备灵箓(连接id,序号,id,内容)
    self.临时id1 = 玩家数据[id].角色.数据.道具[内容.序列2]
    self.临时id2 = 玩家数据[id].角色.数据.道具[内容.序列1]
    if 玩家数据[id].道具.数据[self.临时id1]==nil or 玩家数据[id].道具.数据[self.临时id1]==0 then
      常规提示(id,"你似乎并没有这样的道具")
      return 0
    elseif 玩家数据[id].道具.数据[self.临时id2]==nil then
      常规提示(id,"你似乎并没有这样的道具")
      return 0
    end
    if 玩家数据[id].道具.数据[self.临时id1].分类<10 or 玩家数据[id].道具.数据[self.临时id1].分类>13 then
      常规提示(id,"灵箓只能用于灵饰")
      return 0
    elseif 玩家数据[id].道具.数据[self.临时id2].名称 ~="灵箓" then
      常规提示(id,"请不要移动灵箓的位置")
      return 0
    elseif 玩家数据[id].道具.数据[self.临时id1].灵饰 == nil then
      常规提示(id,"只有灵饰才可以使用此物品")
      return 0
    elseif 玩家数据[id].道具.数据[self.临时id1].附加特性 == nil or 玩家数据[id].道具.数据[self.临时id1].附加特性.幻化等级 == nil then
      常规提示(id,"该装备尚未附加特性")
      return 0
    end
    玩家数据[id].道具.数据[self.临时id1].附加特性 = nil
    玩家数据[id].角色.数据.道具[内容.序列1] = nil
    玩家数据[id].道具.数据[self.临时id2] = nil
    道具刷新(id)
    常规提示(id,"灵箓使用成功")
end


-- function 装备处理类:装备碎星锤(连接id,序号,id,内容)
--     self.临时id1 = 玩家数据[id].角色.数据.道具[内容.序列2]
--     self.临时id2 = 玩家数据[id].角色.数据.道具[内容.序列1]
--     if 玩家数据[id].道具.数据[self.临时id1]==nil or 玩家数据[id].道具.数据[self.临时id1]==0 then
--       常规提示(id,"你似乎并没有这样的道具")
--       return 0
--     elseif 玩家数据[id].道具.数据[self.临时id2]==nil then
--       常规提示(id,"你似乎并没有这样的道具")
--       return 0
--     end
--     if 玩家数据[id].道具.数据[self.临时id1].分类>=7 then
--       常规提示(id,"碎星锤只能用于装备")
--       return 0
--     elseif 玩家数据[id].道具.数据[self.临时id2].名称 ~="碎星锤" and 玩家数据[id].道具.数据[self.临时id2].名称 ~= "超级碎星锤" then
--       常规提示(id,"请不要移动碎星锤的位置")
--       return 0
--     elseif 玩家数据[id].道具.数据[self.临时id1].镶嵌类型 == nil or 玩家数据[id].道具.数据[self.临时id1].锻炼等级 == nil or 玩家数据[id].道具.数据[self.临时id1].锻炼等级 <= 0 then
--       常规提示(id,"该装备尚未镶嵌任何宝石")
--       return 0
--     end





--     local 最高宝石 = {}
--     if 玩家数据[id].道具.数据[self.临时id2].名称 == "超级碎星锤" then
--             local 镶嵌类型 = {[1]={类型=玩家数据[id].道具.数据[self.临时id1].镶嵌类型[1],数量=1}}
--             for i=2,玩家数据[id].道具.数据[self.临时id1].锻炼等级 do
--                 if 玩家数据[id].道具.数据[self.临时id1].镶嵌类型[i] == 镶嵌类型[1].类型 then
--                       镶嵌类型[1].数量 = 镶嵌类型[1].数量 +1
--                 else
--                     if 镶嵌类型[2] ~= nil then
--                           镶嵌类型[2].数量 = 镶嵌类型[2].数量 +1
--                     else
--                           镶嵌类型[2] = {类型=玩家数据[id].道具.数据[self.临时id1].镶嵌类型[i],数量=1}
--                     end
--                 end
--             end
--             最高宝石.名称 = 玩家数据[id].道具.数据[self.临时id1].镶嵌类型[#玩家数据[id].道具.数据[self.临时id1].镶嵌类型]
--             最高宝石.等级 = 玩家数据[id].道具.数据[self.临时id1].锻炼等级
--             for i=1,#镶嵌类型 do
--                 if 镶嵌类型[i].类型 == "太阳石" then
--                       玩家数据[id].道具.数据[self.临时id1].伤害 = math.floor(玩家数据[id].道具.数据[self.临时id1].伤害-镶嵌类型[i].数量*8)
--                 elseif 镶嵌类型[i].类型 == "红玛瑙" then
--                       玩家数据[id].道具.数据[self.临时id1].命中 = math.floor(玩家数据[id].道具.数据[self.临时id1].命中-镶嵌类型[i].数量*25)
--                 elseif 镶嵌类型[i].类型 == "月亮石" then
--                       玩家数据[id].道具.数据[self.临时id1].防御 = math.floor(玩家数据[id].道具.数据[self.临时id1].防御-镶嵌类型[i].数量*12)
--                 elseif 镶嵌类型[i].类型 == "黑宝石" then
--                       玩家数据[id].道具.数据[self.临时id1].速度 = math.floor(玩家数据[id].道具.数据[self.临时id1].速度-镶嵌类型[i].数量*8)
--                 elseif 镶嵌类型[i].类型 == "舍利子" then
--                       玩家数据[id].道具.数据[self.临时id1].灵力 = math.floor(玩家数据[id].道具.数据[self.临时id1].灵力-镶嵌类型[i].数量*6)
--                 elseif 镶嵌类型[i].类型 == "光芒石" then
--                       玩家数据[id].道具.数据[self.临时id1].气血 = math.floor(玩家数据[id].道具.数据[self.临时id1].气血-镶嵌类型[i].数量*40)
--                 elseif 镶嵌类型[i].类型 == "神秘石" then
--                       玩家数据[id].道具.数据[self.临时id1].躲避 = math.floor(玩家数据[id].道具.数据[self.临时id1].躲避-镶嵌类型[i].数量*20)
--                 end
--             end
--             玩家数据[id].道具.数据[self.临时id1].锻炼等级 = nil
--             玩家数据[id].道具.数据[self.临时id1].镶嵌宝石 = nil
--             玩家数据[id].道具.数据[self.临时id1].镶嵌类型 = nil
--     elseif 玩家数据[id].道具.数据[self.临时id2].名称 == "碎星锤" then
--               local 镶嵌类型 = 玩家数据[id].道具.数据[self.临时id1].镶嵌类型[#玩家数据[id].道具.数据[self.临时id1].镶嵌类型]
--               if 镶嵌类型 == "太阳石" then
--                 玩家数据[id].道具.数据[self.临时id1].伤害 = math.floor(玩家数据[id].道具.数据[self.临时id1].伤害-8)
--               elseif 镶嵌类型 == "红玛瑙" then
--                 玩家数据[id].道具.数据[self.临时id1].命中 = math.floor(玩家数据[id].道具.数据[self.临时id1].命中-25)
--               elseif 镶嵌类型 == "月亮石" then
--                 玩家数据[id].道具.数据[self.临时id1].防御 = math.floor(玩家数据[id].道具.数据[self.临时id1].防御-12)
--               elseif 镶嵌类型 == "黑宝石" then
--                 玩家数据[id].道具.数据[self.临时id1].速度 = math.floor(玩家数据[id].道具.数据[self.临时id1].速度-8)
--               elseif 镶嵌类型 == "舍利子" then
--                 玩家数据[id].道具.数据[self.临时id1].灵力 = math.floor(玩家数据[id].道具.数据[self.临时id1].灵力-6)
--               elseif 镶嵌类型 == "光芒石" then
--                 玩家数据[id].道具.数据[self.临时id1].气血 = math.floor(玩家数据[id].道具.数据[self.临时id1].气血-40)
--               elseif 镶嵌类型 == "神秘石" then
--                 玩家数据[id].道具.数据[self.临时id1].躲避 = math.floor(玩家数据[id].道具.数据[self.临时id1].躲避-20)
--               end
--               玩家数据[id].道具.数据[self.临时id1].锻炼等级 = 玩家数据[id].道具.数据[self.临时id1].锻炼等级 -1
--               if 玩家数据[id].道具.数据[self.临时id1].锻炼等级 <= 0 then
--                     玩家数据[id].道具.数据[self.临时id1].锻炼等级 = nil
--                     玩家数据[id].道具.数据[self.临时id1].镶嵌宝石 = nil
--                     玩家数据[id].道具.数据[self.临时id1].镶嵌类型 = nil
--               else
--                     玩家数据[id].道具.数据[self.临时id1].镶嵌类型[#玩家数据[id].道具.数据[self.临时id1].镶嵌类型] = nil
--                     if #玩家数据[id].道具.数据[self.临时id1].镶嵌宝石 >= 2 then
--                         local 找到 = false
--                         for n=1,#玩家数据[id].道具.数据[self.临时id1].镶嵌类型 do
--                             if 玩家数据[id].道具.数据[self.临时id1].镶嵌宝石[1] == 玩家数据[id].道具.数据[self.临时id1].镶嵌类型[n] then
--                               找到 = true
--                               break
--                             end
--                         end
--                         if 找到 == false then
--                             table.remove(玩家数据[id].道具.数据[self.临时id1].镶嵌宝石,1)
--                         else
--                             找到 = false
--                             for n=1,#玩家数据[id].道具.数据[self.临时id1].镶嵌类型 do
--                                 if 玩家数据[id].道具.数据[self.临时id1].镶嵌宝石[2] == 玩家数据[id].道具.数据[self.临时id1].镶嵌类型[n] then
--                                   找到 = true
--                                   break
--                                 end
--                             end
--                             if 找到 == false then
--                               table.remove(玩家数据[id].道具.数据[self.临时id1].镶嵌宝石,2)
--                             end
--                         end
--                     end
--               end
--     end

--      if 玩家数据[id].道具.数据[self.临时id2].数量~=nil then
--       玩家数据[id].道具.数据[self.临时id2].数量 = 玩家数据[id].道具.数据[self.临时id2].数量 - 1
--       if 玩家数据[id].道具.数据[self.临时id2].数量 <=0 then
--          玩家数据[id].角色.数据.道具[内容.序列1] = nil
--          玩家数据[id].道具.数据[self.临时id2] = nil
--       end
--     else
--         玩家数据[id].角色.数据.道具[内容.序列1] = nil
--         玩家数据[id].道具.数据[self.临时id2] = nil
--     end
--     if 最高宝石.等级 ~= nil and 最高宝石.名称 ~= nil then
--       玩家数据[id].道具:给予道具(id,最高宝石.名称,最高宝石.等级)
--     end
--     道具刷新(id)
--     常规提示(id,"碎星锤使用成功")
-- end



function 装备处理类:装备拆除宝石(装备id,鉴定id,id)
        if 玩家数据[id].道具.数据[装备id].镶嵌类型 == nil or 玩家数据[id].道具.数据[装备id].锻炼等级 == nil or 玩家数据[id].道具.数据[装备id].锻炼等级 <= 0 then
            常规提示(id,"该装备尚未镶嵌任何宝石")
            return 0
        end
        if 玩家数据[id].道具.数据[鉴定id].名称 == "超级碎星锤" then
                local 镶嵌类型 = {[1]={类型=玩家数据[id].道具.数据[装备id].镶嵌类型[1],数量=1}}
                for i=2,玩家数据[id].道具.数据[装备id].锻炼等级 do
                    if 玩家数据[id].道具.数据[装备id].镶嵌类型[i] == 镶嵌类型[1].类型 then
                          镶嵌类型[1].数量 = 镶嵌类型[1].数量 +1
                    else
                        if 镶嵌类型[2] ~= nil then
                              镶嵌类型[2].数量 = 镶嵌类型[2].数量 +1
                        else
                              镶嵌类型[2] = {类型=玩家数据[id].道具.数据[装备id].镶嵌类型[i],数量=1}
                        end
                    end
                end
                local 宝石名称 = 玩家数据[id].道具.数据[装备id].镶嵌类型[#玩家数据[id].道具.数据[装备id].镶嵌类型]
                local 宝石等级 =  玩家数据[id].道具.数据[装备id].锻炼等级
                for i=1,#镶嵌类型 do
                    if 镶嵌类型[i].类型 == "太阳石" then
                          玩家数据[id].道具.数据[装备id].伤害 = math.floor(玩家数据[id].道具.数据[装备id].伤害-镶嵌类型[i].数量*8)
                    elseif 镶嵌类型[i].类型 == "红玛瑙" then
                          玩家数据[id].道具.数据[装备id].命中 = math.floor(玩家数据[id].道具.数据[装备id].命中-镶嵌类型[i].数量*25)
                    elseif 镶嵌类型[i].类型 == "月亮石" then
                          玩家数据[id].道具.数据[装备id].防御 = math.floor(玩家数据[id].道具.数据[装备id].防御-镶嵌类型[i].数量*12)
                    elseif 镶嵌类型[i].类型 == "黑宝石" then
                          玩家数据[id].道具.数据[装备id].速度 = math.floor(玩家数据[id].道具.数据[装备id].速度-镶嵌类型[i].数量*8)
                    elseif 镶嵌类型[i].类型 == "舍利子" then
                          玩家数据[id].道具.数据[装备id].灵力 = math.floor(玩家数据[id].道具.数据[装备id].灵力-镶嵌类型[i].数量*6)
                    elseif 镶嵌类型[i].类型 == "光芒石" then
                          玩家数据[id].道具.数据[装备id].气血 = math.floor(玩家数据[id].道具.数据[装备id].气血-镶嵌类型[i].数量*40)
                    elseif 镶嵌类型[i].类型 == "神秘石" then
                          玩家数据[id].道具.数据[装备id].躲避 = math.floor(玩家数据[id].道具.数据[装备id].躲避-镶嵌类型[i].数量*20)
                    end
                end
                玩家数据[id].道具.数据[装备id].锻炼等级 = nil
                玩家数据[id].道具.数据[装备id].镶嵌宝石 = nil
                玩家数据[id].道具.数据[装备id].镶嵌类型 = nil
                玩家数据[id].道具:给予道具(id,宝石名称,宝石等级)
        elseif 玩家数据[id].道具.数据[鉴定id].名称 == "碎星锤" then
                  local 镶嵌类型 = 玩家数据[id].道具.数据[装备id].镶嵌类型[#玩家数据[id].道具.数据[装备id].镶嵌类型]
                  if 镶嵌类型 == "太阳石" then
                    玩家数据[id].道具.数据[装备id].伤害 = math.floor(玩家数据[id].道具.数据[装备id].伤害-8)
                  elseif 镶嵌类型 == "红玛瑙" then
                    玩家数据[id].道具.数据[装备id].命中 = math.floor(玩家数据[id].道具.数据[装备id].命中-25)
                  elseif 镶嵌类型 == "月亮石" then
                    玩家数据[id].道具.数据[装备id].防御 = math.floor(玩家数据[id].道具.数据[装备id].防御-12)
                  elseif 镶嵌类型 == "黑宝石" then
                    玩家数据[id].道具.数据[装备id].速度 = math.floor(玩家数据[id].道具.数据[装备id].速度-8)
                  elseif 镶嵌类型 == "舍利子" then
                    玩家数据[id].道具.数据[装备id].灵力 = math.floor(玩家数据[id].道具.数据[装备id].灵力-6)
                  elseif 镶嵌类型 == "光芒石" then
                    玩家数据[id].道具.数据[装备id].气血 = math.floor(玩家数据[id].道具.数据[装备id].气血-40)
                  elseif 镶嵌类型 == "神秘石" then
                    玩家数据[id].道具.数据[装备id].躲避 = math.floor(玩家数据[id].道具.数据[装备id].躲避-20)
                  end
                  玩家数据[id].道具.数据[装备id].锻炼等级 = 玩家数据[id].道具.数据[装备id].锻炼等级 -1
                  if 玩家数据[id].道具.数据[装备id].锻炼等级 <= 0 then
                        玩家数据[id].道具.数据[装备id].锻炼等级 = nil
                        玩家数据[id].道具.数据[装备id].镶嵌宝石 = nil
                        玩家数据[id].道具.数据[装备id].镶嵌类型 = nil
                  else
                        玩家数据[id].道具.数据[装备id].镶嵌类型[#玩家数据[id].道具.数据[装备id].镶嵌类型] = nil
                        if #玩家数据[id].道具.数据[装备id].镶嵌宝石 >= 2 then
                            local 找到 = false
                            for n=1,#玩家数据[id].道具.数据[装备id].镶嵌类型 do
                                if 玩家数据[id].道具.数据[装备id].镶嵌宝石[1] == 玩家数据[id].道具.数据[装备id].镶嵌类型[n] then
                                  找到 = true
                                  break
                                end
                            end
                            if 找到 == false then
                                table.remove(玩家数据[id].道具.数据[装备id].镶嵌宝石,1)
                            else
                                找到 = false
                                for n=1,#玩家数据[id].道具.数据[装备id].镶嵌类型 do
                                    if 玩家数据[id].道具.数据[装备id].镶嵌宝石[2] == 玩家数据[id].道具.数据[装备id].镶嵌类型[n] then
                                      找到 = true
                                      break
                                    end
                                end
                                if 找到 == false then
                                  table.remove(玩家数据[id].道具.数据[装备id].镶嵌宝石,2)
                                end
                            end
                        end
                  end
        end

       if 玩家数据[id].道具.数据[鉴定id].数量~=nil then
          玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
          if 玩家数据[id].道具.数据[鉴定id].数量 <=0 then
             玩家数据[id].道具.数据[鉴定id] = nil
          end
      else
          玩家数据[id].道具.数据[鉴定id] = nil
      end

      道具刷新(id)
      常规提示(id,"碎星锤使用成功")
end

function 装备处理类:召唤兽拆除宝石(装备id,鉴定id,id)
         if 玩家数据[id].道具.数据[装备id].镶嵌宝石 == nil or 玩家数据[id].道具.数据[装备id].镶嵌等级 == nil or 玩家数据[id].道具.数据[装备id].镶嵌等级 <= 0 then
              常规提示(id,"该装备尚未镶嵌任何宝石")
              return 0
          end
          local 宝石类型 = 玩家数据[id].道具.数据[装备id].镶嵌宝石
          local 宝石等级 = 玩家数据[id].道具.数据[装备id].镶嵌等级
          if 玩家数据[id].道具.数据[鉴定id].名称 == "超级碎星锤" then
                if 宝石类型== "伤害" then
                    玩家数据[id].道具.数据[装备id].伤害= math.floor(玩家数据[id].道具.数据[装备id].伤害-宝石等级*10)
                    if 玩家数据[id].道具.数据[装备id].伤害<=0 then
                        玩家数据[id].道具.数据[装备id].伤害=nil
                    end
                elseif 宝石类型== "灵力" then
                      玩家数据[id].道具.数据[装备id].灵力= math.floor(玩家数据[id].道具.数据[装备id].灵力-宝石等级*4)
                      if 玩家数据[id].道具.数据[装备id].灵力<=0 then
                          玩家数据[id].道具.数据[装备id].灵力=nil
                      end

                elseif 宝石类型== "速度" then
                        玩家数据[id].道具.数据[装备id].速度= math.floor(玩家数据[id].道具.数据[装备id].速度-宝石等级*6)
                        if 玩家数据[id].道具.数据[装备id].速度<=0 then
                            玩家数据[id].道具.数据[装备id].速度=nil
                        end

                elseif 宝石类型== "气血" then
                        玩家数据[id].道具.数据[装备id].气血= math.floor(玩家数据[id].道具.数据[装备id].气血-宝石等级*30)
                        if 玩家数据[id].道具.数据[装备id].气血<=0 then
                            玩家数据[id].道具.数据[装备id].气血=nil
                        end

                elseif 宝石类型== "防御" then
                        玩家数据[id].道具.数据[装备id].防御= math.floor(玩家数据[id].道具.数据[装备id].防御-宝石等级*8)
                        if 玩家数据[id].道具.数据[装备id].防御<=0 then
                            玩家数据[id].道具.数据[装备id].防御=nil
                        end
                end
                玩家数据[id].道具.数据[装备id].镶嵌宝石=nil
                玩家数据[id].道具.数据[装备id].镶嵌等级=nil
                玩家数据[id].道具:给予道具(id,"精魄灵石",宝石等级,宝石类型)
          elseif 玩家数据[id].道具.数据[鉴定id].名称 == "碎星锤" then
                  if 宝石类型== "伤害" then
                      玩家数据[id].道具.数据[装备id].伤害= math.floor(玩家数据[id].道具.数据[装备id].伤害-10)
                      if 玩家数据[id].道具.数据[装备id].伤害<=0 then
                          玩家数据[id].道具.数据[装备id].伤害=nil
                      end
                elseif 宝石类型== "灵力" then
                      玩家数据[id].道具.数据[装备id].灵力= math.floor(玩家数据[id].道具.数据[装备id].灵力-4)
                      if 玩家数据[id].道具.数据[装备id].灵力<=0 then
                          玩家数据[id].道具.数据[装备id].灵力=nil
                      end
                elseif 宝石类型== "速度" then
                        玩家数据[id].道具.数据[装备id].速度= math.floor(玩家数据[id].道具.数据[装备id].速度-6)
                        if 玩家数据[id].道具.数据[装备id].速度<=0 then
                            玩家数据[id].道具.数据[装备id].速度=nil
                        end

                elseif 宝石类型== "气血" then
                        玩家数据[id].道具.数据[装备id].气血= math.floor(玩家数据[id].道具.数据[装备id].气血-30)
                        if 玩家数据[id].道具.数据[装备id].气血<=0 then
                            玩家数据[id].道具.数据[装备id].气血=nil
                        end
                elseif 宝石类型== "防御" then
                        玩家数据[id].道具.数据[装备id].防御= math.floor(玩家数据[id].道具.数据[装备id].防御-8)
                        if 玩家数据[id].道具.数据[装备id].防御<=0 then
                            玩家数据[id].道具.数据[装备id].防御=nil
                        end
                end
                玩家数据[id].道具.数据[装备id].镶嵌等级=玩家数据[id].道具.数据[装备id].镶嵌等级-1
                if 玩家数据[id].道具.数据[装备id].镶嵌等级<=0 then
                    玩家数据[id].道具.数据[装备id].镶嵌宝石=nil
                    玩家数据[id].道具.数据[装备id].镶嵌等级=nil
                end
          end
          if 玩家数据[id].道具.数据[鉴定id].数量~=nil then
                玩家数据[id].道具.数据[鉴定id].数量 = 玩家数据[id].道具.数据[鉴定id].数量 - 1
                if 玩家数据[id].道具.数据[鉴定id].数量 <=0 then
                   玩家数据[id].道具.数据[鉴定id] = nil
                end
          else
                玩家数据[id].道具.数据[鉴定id] = nil
          end
          道具刷新(id)
          常规提示(id,"碎星锤使用成功")
end

function 装备处理类:装备碎星锤(连接id,序号,id,内容)
      if not 内容.序列1 or not 内容.序列2 then return end
      local 鉴定id = 玩家数据[id].角色.数据.道具[内容.序列1]
      local 装备id  = 玩家数据[id].角色.数据.道具[内容.序列2]
      if not 鉴定id or not 装备id  then return end
      if 玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then
          常规提示(id,"你似乎并没有这样的道具")
          return 0
      elseif 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[鉴定id]==0 then
          常规提示(id,"你似乎并没有这样的道具")
          return 0
      elseif 玩家数据[id].道具.数据[装备id].总类~=2 then
           常规提示(id,"你似乎并没有这样的道具")
            return 0
      elseif 玩家数据[id].道具.数据[鉴定id].名称 ~="碎星锤" and 玩家数据[id].道具.数据[鉴定id].名称 ~= "超级碎星锤" then
            常规提示(id,"请不要移动碎星锤的位置")
            return 0
      end
      if 玩家数据[id].道具.数据[装备id].分类<=6 then
            self:装备拆除宝石(装备id,鉴定id,id)
      elseif 玩家数据[id].道具.数据[装备id].分类 > 6 and 玩家数据[id].道具.数据[装备id].分类 <= 9 then
            self:召唤兽拆除宝石(装备id,鉴定id,id)
      else
          常规提示(id,"这个道具无法使用碎星锤")
      end
end





-- function 装备处理类:装备附魔宝珠(连接id,序号,id,内容)

--     if not 内容.序列1 or not 内容.序列2 then return  end
--     local 鉴定id =玩家数据[id].角色.数据.道具[内容.序列1]
--     local 装备id = 玩家数据[id].角色.数据.道具[内容.序列2]
--     if not 鉴定id or not 装备id then return end
--     if 玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then
--       常规提示(id,"你似乎并没有这样的道具")
--       return 0
--     elseif 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[鉴定id]==0  then
--       常规提示(id,"你似乎并没有这样的道具")
--       return 0
--     elseif 玩家数据[id].道具.数据[鉴定id].名称 ~= "附魔宝珠" and 玩家数据[id].道具.数据[鉴定id].名称 ~= "超级附魔宝珠" then
--       常规提示(id,"请不要移动附魔宝珠的位置！")
--       return 0
--     elseif 玩家数据[id].道具.数据[装备id].级别限制 > 玩家数据[id].道具.数据[鉴定id].级别限制 then
--       常规提示(id,"你的附魔宝珠等级太低！")
--       return 0
--     elseif not 玩家数据[id].道具.数据[装备id].鉴定 then
--         常规提示(id,"#Y未鉴定装备无法附魔")
--         return 0
--     end
--     if 玩家数据[id].道具.数据[装备id].分类>=7 then
--       常规提示(id,"该物品无法附魔")
--       return 0
--     elseif 玩家数据[id].道具.数据[装备id].分类==3 then
--       常规提示(id,"武器无法附魔")
--       return 0
--     end
--     local 套装类型={"附加状态","追加法术"}
--     套装类型=套装类型[取随机数(1,#套装类型)]
--     local 套装效果={
--       附加状态={"金刚护法","金刚护体","生命之泉","炼气化神","普渡众生","定心术","碎星诀","变身","凝神术","逆鳞","不动如山","法术防御","明光宝烛","天神护体","移魂化骨","蜜润","杀气诀","韦陀护法","一苇渡江","天神护法","乘风破浪","魔息术","盘丝阵","炎护"},
--       追加法术={"横扫千军","善恶有报","惊心一剑","壁垒击破","满天花雨","浪涌","唧唧歪歪",
--       "五雷咒","龙卷雨击","剑荡四方","裂石","烟雨剑法","天雷斩","力劈华山","夜舞倾城",
--       "上古灵符","叱咤风云","天降灵葫","月光","八凶法阵","死亡召唤","天罗地网","夺命咒",
--       "落叶萧萧","落雷符","雨落寒沙",
--       "苍茫树","飞砂走石","阎罗令","水攻","烈火","落岩","雷击",
--       "泰山压顶","水漫金山","地狱烈火","奔雷咒"}
--     }
--     玩家数据[id].道具.数据[装备id].套装效果={套装类型,套装效果[套装类型][取随机数(1,#套装效果[套装类型])]}
--     玩家数据[id].角色.数据.道具[内容.序列1] = nil
--     玩家数据[id].道具.数据[鉴定id] = nil
--     道具刷新(id)
--     常规提示(id,"附魔宝珠使用成功")
-- end


function 装备处理类:技能附魔(连接id,序号,id,内容)
    if not 内容.序列2 or not 内容.数据 then return end
    local 装备id = 玩家数据[id].角色.数据.道具[内容.序列2]
    if not 装备id then return end
    if 玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then
      常规提示(id,"你似乎并没有这样的道具")
      return 0
    elseif not 玩家数据[id].道具.数据[装备id].鉴定 then
        常规提示(id,"#Y未鉴定装备无法附魔")
        return 0
    end
    if 玩家数据[id].道具.数据[装备id].临时附魔 == nil then
      玩家数据[id].道具.数据[装备id].临时附魔 = {体质={数值=0,时间=0},伤害={数值=0,时间=0},防御={数值=0,时间=0},气血={数值=0,时间=0},魔法={数值=0,时间=0},命中={数值=0,时间=0},法术防御={数值=0,时间=0},速度={数值=0,时间=0},愤怒={数值=0,时间=0},魔力={数值=0,时间=0},法术伤害={数值=0,时间=0},耐力={数值=0,时间=0}}
    end
    self.到期时间 = os.time() + 86400
    if 玩家数据[id].角色.数据.活力 >= 内容.数据.等级 then
      if 内容.数据.名称 == "嗜血" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.1,内容.数据.等级*0.2))
        玩家数据[id].道具.数据[装备id].临时附魔.体质.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.体质.时间 = 时间转换2(self.到期时间)
        玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.时间 = 0
      elseif 内容.数据.名称 == "莲华妙法" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.15,内容.数据.等级*0.3))
        玩家数据[id].道具.数据[装备id].临时附魔.体质.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.体质.时间 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "轻如鸿毛" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级,内容.数据.等级*2))
        玩家数据[id].道具.数据[装备id].临时附魔.魔法.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.魔法.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "神木呓语" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.15,内容.数据.等级*0.3))
        玩家数据[id].道具.数据[装备id].临时附魔.魔法.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.魔法.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "拈花妙指" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.15,内容.数据.等级*0.3))
        玩家数据[id].道具.数据[装备id].临时附魔.速度.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.速度.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "盘丝舞" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.5,内容.数据.等级))
        玩家数据[id].道具.数据[装备id].临时附魔.防御.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.防御.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "一气化三清" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.1,内容.数据.等级*0.2))
        玩家数据[id].道具.数据[装备id].临时附魔.魔力.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.魔力.时间 = 时间转换2(self.到期时间)
        玩家数据[id].道具.数据[装备id].临时附魔.法术防御.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.法术防御.时间 = 0
      elseif 内容.数据.名称 == "浩然正气" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.15,内容.数据.等级*0.3))
        玩家数据[id].道具.数据[装备id].临时附魔.法术防御.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.法术防御.时间 = 时间转换2(self.到期时间)
        玩家数据[id].道具.数据[装备id].临时附魔.魔力.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.魔力.时间 = 0
      elseif 内容.数据.名称 == "龙附" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.5,内容.数据.等级))
        玩家数据[id].道具.数据[装备id].临时附魔.伤害.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.伤害.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "穿云破空" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.5,内容.数据.等级))
        玩家数据[id].道具.数据[装备id].临时附魔.伤害.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.伤害.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "神兵护法" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.5,内容.数据.等级))
        玩家数据[id].道具.数据[装备id].临时附魔.命中.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.命中.时间 = 时间转换2(self.到期时间)
        玩家数据[id].道具.数据[装备id].临时附魔.耐力.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.耐力.时间 = 0
      elseif 内容.数据.名称 == "魔王护持" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级,内容.数据.等级*2))
        玩家数据[id].道具.数据[装备id].临时附魔.气血.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.气血.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "元阳护体" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级,内容.数据.等级*2))
        玩家数据[id].道具.数据[装备id].临时附魔.气血.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.气血.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "神力无穷" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.16,内容.数据.等级*0.32))
        玩家数据[id].道具.数据[装备id].临时附魔.愤怒.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.愤怒.时间 = 时间转换2(self.到期时间)
      elseif 内容.数据.名称 == "尸气漫天" then
        self.临时数值 = math.floor(取随机数(内容.数据.等级*0.1,内容.数据.等级*0.2))
        玩家数据[id].道具.数据[装备id].临时附魔.耐力.数值 = self.临时数值
        玩家数据[id].道具.数据[装备id].临时附魔.耐力.时间 = 时间转换2(self.到期时间)
        玩家数据[id].道具.数据[装备id].临时附魔.命中.数值 = 0
        玩家数据[id].道具.数据[装备id].临时附魔.命中.时间 = 0
      end
      玩家数据[id].角色.数据.活力 = 玩家数据[id].角色.数据.活力 - 内容.数据.等级
      道具刷新(id)
      常规提示(id,"添加临时附魔成功")
    else
      常规提示(id,"活力不足,无法附魔")
    end
end

function 装备处理类:装备附魔(连接id,序号,id,内容)
  if not 内容.序列1 or not 内容.序列2 then return end
  local 鉴定id =玩家数据[id].角色.数据.道具[内容.序列1]
  local 装备id =玩家数据[id].角色.数据.道具[内容.序列2]
  if not 鉴定id or not 装备id then return end
  if 玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then
    常规提示(id,"你似乎并没有这样的道具")
    return 0
  elseif 玩家数据[id].道具.数据[鉴定id]==nil or 玩家数据[id].道具.数据[鉴定id]==0 then
    常规提示(id,"你似乎并没有这样的道具")
    return 0
  elseif not 玩家数据[id].道具.数据[装备id].鉴定 then
        常规提示(id,"#Y未鉴定装备无法附魔")
        return 0
  end
  if 玩家数据[id].道具.数据[装备id].临时附魔 == nil then
      玩家数据[id].道具.数据[装备id].临时附魔 = {体质={数值=0,时间=0},伤害={数值=0,时间=0},防御={数值=0,时间=0},气血={数值=0,时间=0},魔法={数值=0,时间=0},命中={数值=0,时间=0},法术防御={数值=0,时间=0},速度={数值=0,时间=0},愤怒={数值=0,时间=0},魔力={数值=0,时间=0},法术伤害={数值=0,时间=0},耐力={数值=0,时间=0}}
  end
  self.到期时间 = os.time() + 86400
  if 玩家数据[id].道具.数据[鉴定id].类型 == "嗜血" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.1,玩家数据[id].道具.数据[鉴定id].等级*0.2))
      玩家数据[id].道具.数据[装备id].临时附魔.体质.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.体质.时间 = 时间转换2(self.到期时间)
      玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.时间 = 0
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "莲华妙法" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.15,玩家数据[id].道具.数据[鉴定id].等级*0.3))
      玩家数据[id].道具.数据[装备id].临时附魔.体质.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.体质.时间 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.法术伤害.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "轻如鸿毛" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级,玩家数据[id].道具.数据[鉴定id].等级*2))
      玩家数据[id].道具.数据[装备id].临时附魔.魔法.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.魔法.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "神木呓语" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.15,玩家数据[id].道具.数据[鉴定id].等级*0.3))
      玩家数据[id].道具.数据[装备id].临时附魔.魔法.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.魔法.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "拈花妙指" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.15,玩家数据[id].道具.数据[鉴定id].等级*0.3))
      玩家数据[id].道具.数据[装备id].临时附魔.速度.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.速度.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "盘丝舞" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.5,玩家数据[id].道具.数据[鉴定id].等级))
      玩家数据[id].道具.数据[装备id].临时附魔.防御.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.防御.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "一气化三清" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.1,玩家数据[id].道具.数据[鉴定id].等级*0.2))
      玩家数据[id].道具.数据[装备id].临时附魔.魔力.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.魔力.时间 = 时间转换2(self.到期时间)
      玩家数据[id].道具.数据[装备id].临时附魔.法术防御.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.法术防御.时间 = 0
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "浩然正气" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.15,玩家数据[id].道具.数据[鉴定id].等级*0.3))
      玩家数据[id].道具.数据[装备id].临时附魔.法术防御.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.法术防御.时间 = 时间转换2(self.到期时间)
      玩家数据[id].道具.数据[装备id].临时附魔.魔力.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.魔力.时间 = 0
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "龙附" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.5,玩家数据[id].道具.数据[鉴定id].等级))
      玩家数据[id].道具.数据[装备id].临时附魔.伤害.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.伤害.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "穿云破空" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.5,玩家数据[id].道具.数据[鉴定id].等级))
      玩家数据[id].道具.数据[装备id].临时附魔.伤害.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.伤害.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "神兵护法" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.5,玩家数据[id].道具.数据[鉴定id].等级))
      玩家数据[id].道具.数据[装备id].临时附魔.命中.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.命中.时间 = 时间转换2(self.到期时间)
      玩家数据[id].道具.数据[装备id].临时附魔.耐力.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.耐力.时间 = 0
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "魔王护持" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级,玩家数据[id].道具.数据[鉴定id].等级*2))
      玩家数据[id].道具.数据[装备id].临时附魔.气血.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.气血.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "元阳护体" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级,玩家数据[id].道具.数据[鉴定id].等级*2))
      玩家数据[id].道具.数据[装备id].临时附魔.气血.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.气血.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "神力无穷" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.16,玩家数据[id].道具.数据[鉴定id].等级*0.32))
      玩家数据[id].道具.数据[装备id].临时附魔.愤怒.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.愤怒.时间 = 时间转换2(self.到期时间)
  elseif 玩家数据[id].道具.数据[鉴定id].类型 == "尸气漫天" then
      self.临时数值 = math.floor(取随机数(玩家数据[id].道具.数据[鉴定id].等级*0.1,玩家数据[id].道具.数据[鉴定id].等级*0.2))
      玩家数据[id].道具.数据[装备id].临时附魔.耐力.数值 = self.临时数值
      玩家数据[id].道具.数据[装备id].临时附魔.耐力.时间 = 时间转换2(self.到期时间)
      玩家数据[id].道具.数据[装备id].临时附魔.命中.数值 = 0
      玩家数据[id].道具.数据[装备id].临时附魔.命中.时间 = 0
  end
  玩家数据[id].道具.数据[鉴定id] = nil
  道具刷新(id)
  常规提示(id,"添加临时附魔成功")
end





function 装备处理类:一键附魔(id)
    if not 自定义数据.一键附魔配置.时间 then
        自定义数据.一键附魔配置.时间 = 1
    end
    local 时间计算 = 自定义数据.一键附魔配置.时间*86400
    local 到期时间 = 时间转换2(os.time() + 时间计算)
    for k,v in pairs(玩家数据[id].角色.数据.装备) do
        if 玩家数据[id].道具.数据[v] then
            玩家数据[id].道具.数据[v].临时附魔 = {体质={数值=0,时间=0},伤害={数值=0,时间=0},防御={数值=0,时间=0},气血={数值=0,时间=0},魔法={数值=0,时间=0},命中={数值=0,时间=0},法术防御={数值=0,时间=0},速度={数值=0,时间=0},愤怒={数值=0,时间=0},魔力={数值=0,时间=0},法术伤害={数值=0,时间=0},耐力={数值=0,时间=0}}
            local 分类 = 玩家数据[id].道具.数据[v].分类
            local 分类类型={
                  [1]={命中=185,耐力=37},--1 --0.2
                  [2]={体质=37,法术伤害=56},--0.2 --0.3
                  [3]={伤害=185,防御=185,气血=370,魔法=370},
                  [4]={魔力=37,法术防御=56},
                  [5]={伤害=185,愤怒=60},
                  [6]={速度=56,魔法=370},
              }
              for z,n in pairs(分类类型[分类]) do
                  玩家数据[id].道具.数据[v].临时附魔[z]={数值=n,时间=到期时间}
              end
        end
    end
   -- 道具刷新(id)
    发送数据(玩家数据[id].连接id,3503,玩家数据[id].角色:取装备数据())
    玩家数据[id].角色:刷新信息("2")
    常规提示(id,"添加临时附魔成功")
end








function 装备处理类:装备开运(连接id,序号,id,内容)
    if not 内容.序列 then return end
    local 装备id = 玩家数据[id].角色.数据.道具[内容.序列]
    if not 装备id then  return end
    if 玩家数据[id].道具.数据[装备id]==nil or 玩家数据[id].道具.数据[装备id]==0 then
      常规提示(id,"你似乎并没有这样的道具")
      return 0
    elseif not 玩家数据[id].道具.数据[装备id].鉴定 then
        常规提示(id,"该物品无法开孔")
        return 0
    elseif 玩家数据[id].道具.数据[装备id].级别限制==nil then
        常规提示(id,"该物品无法开孔")
        return 0
    elseif 玩家数据[id].道具.数据[装备id].分类>=7 then
        常规提示(id,"该物品无法开孔")
        return 0
    end
    if 玩家数据[id].道具.数据[装备id].开运孔数 ==nil then
        玩家数据[id].道具.数据[装备id].开运孔数={上限=math.floor(玩家数据[id].道具.数据[装备id].级别限制/35+1),当前=0}
    end
  if  玩家数据[id].道具.数据[装备id].开运孔数.当前 < 玩家数据[id].道具.数据[装备id].开运孔数.上限 and 玩家数据[id].道具.数据[装备id].开运孔数.当前  <6 then
        self.扣除金钱=(玩家数据[id].道具.数据[装备id].开运孔数.当前+1)*100000
        self.消耗体力=(玩家数据[id].道具.数据[装备id].开运孔数.当前+1)*30
        if 玩家数据[id].角色.数据.银子 < self.扣除金钱 then
          常规提示(id,"你没那么多的银子")
          return 0
        elseif 玩家数据[id].角色.数据.体力<self.消耗体力 then
          常规提示(id,"你没那么多的体力")
          return 0
        end
        local 装备几率

        if  玩家数据[id].道具.数据[装备id].级别限制  == 100 then
          装备几率= 80
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 110  then
          装备几率= 65
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 120  then
          装备几率= 55
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 130  then
          装备几率= 45
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 140  then
          装备几率= 30
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 150  then
          装备几率= 20
        elseif 玩家数据[id].道具.数据[装备id].级别限制  == 160  then
          装备几率= 15
        else
          装备几率 =80
        end
        if 取随机数()<= 装备几率   then
          玩家数据[id].道具.数据[装备id].开运孔数.当前=玩家数据[id].道具.数据[装备id].开运孔数.当前+1
          玩家数据[id].角色:扣除银子(self.扣除金钱,"符石开孔",1)
          玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-self.消耗体力
          常规提示(id,"你消耗了:"..self.扣除金钱.."金钱"..self.消耗体力.."体力")
          常规提示(id,"装备开孔成功")
        else
          玩家数据[id].角色:扣除银子(self.扣除金钱,"符石开孔",1)
          玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-self.消耗体力
          常规提示(id,"你消耗了:"..self.扣除金钱.."金钱"..self.消耗体力.."体力")
          常规提示(id,"装备开孔失败")

        end
        self.发送信息 = {}
        self.发送信息.体力=玩家数据[id].角色.数据.体力
        self.发送信息.银子=玩家数据[id].角色.数据.银子
        发送数据(玩家数据[id].连接id,63,self.发送信息)
    else
      常规提示(id,"#r/装备已经无法再进行开孔了")
    end
end

-- function 装备处理类:添加符石(id,道具id,装备id)
--   道具id=分割文本(道具id,",")
--   local 临时id = {}
--   local 临时装备id = 玩家数据[id].角色.数据.道具[装备id]
--   for i=1,5 do
--     道具id[i] = 道具id[i]+0
--     临时id[i]=玩家数据[id].角色.数据.道具[道具id[i]]
--     if 玩家数据[id].道具.数据[临时id[i]]~=nil and 道具id[i]~= 0 then
--       if  玩家数据[id].道具.数据[临时id[i]].总类 == 55  then
--         if 玩家数据[id].道具.数据[临时装备id].符石 == nil then
--             玩家数据[id].道具.数据[临时装备id].符石 = {}
--         end
--         玩家数据[id].道具.数据[临时装备id].符石[i] =DeepCopy(玩家数据[id].道具.数据[临时id[i]])
--         常规提示(id,"镶嵌符石成功")
--         玩家数据[id].道具.数据[临时id[i]] =nil
--         玩家数据[id].角色.数据.道具[道具id[i]] =nil
--       else
--         常规提示(id,"只能镶嵌符石")
--         return
--       end
--     end
--   end
--   符石置对象(id,临时装备id)
-- end



function 装备处理类:合成宝石(连接id,序号,id,内容)
   if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil or 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].总类~=5 or 玩家数据[id].道具.数据[格子1].分类~=6 or 玩家数据[id].道具.数据[格子2].总类~=5 or 玩家数据[id].道具.数据[格子2].分类~=6 then
    常规提示(id,"只有宝石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].子类 ~= 玩家数据[id].道具.数据[格子2].子类 then
    常规提示(id,"只有相同类型宝石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].名称 =="钟灵石" or 玩家数据[id].道具.数据[格子2].名称 =="钟灵石" then
    常规提示(id,"该宝石无法合成！")
    return
  elseif 玩家数据[id].道具.数据[格子1].名称 =="星辉石" or 玩家数据[id].道具.数据[格子2].名称 =="星辉石" then
    常规提示(id,"该宝石无法合成！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 ~= 玩家数据[id].道具.数据[格子2].级别限制 then
    常规提示(id,"只有相同等级宝石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 >= 35 then
    常规提示(id,"该宝石等级已经达到上限！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].级别限制*10 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].级别限制*10
  体活刷新(id)
  if self:取宝石合成几率(id,玩家数据[id].道具.数据[格子1].级别限制) then
    玩家数据[id].道具.数据[格子1].级别限制 = 玩家数据[id].道具.数据[格子1].级别限制 + 1
    玩家数据[id].角色.数据.道具[内容.序列1] = nil
    玩家数据[id].道具.数据[格子2] = nil
    常规提示(id,"你获得了一颗更高等级的宝石")
  else
    玩家数据[id].角色.数据.道具[内容.序列1] = nil
    玩家数据[id].道具.数据[格子2] = nil
    常规提示(id,"合成失败，你因此损失了一颗宝石")
  end
  道具刷新(id)
end


function 装备处理类:合成星辉石(连接id,序号,id,内容)

  if not 内容.序列 or not 内容.序列1 or not 内容.序列2 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  local 格子3=玩家数据[id].角色.数据.道具[内容.序列2]
  if not 格子1 or not 格子2  or not 格子3 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil or 玩家数据[id].道具.数据[格子3]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 or 玩家数据[id].道具.数据[格子3]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].名称~="星辉石" or 玩家数据[id].道具.数据[格子2].名称~="星辉石" or 玩家数据[id].道具.数据[格子3].名称~="星辉石"  then
    常规提示(id,"只有星辉石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 or 玩家数据[id].道具.数据[格子3].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 ~= 玩家数据[id].道具.数据[格子2].级别限制 or 玩家数据[id].道具.数据[格子1].级别限制 ~= 玩家数据[id].道具.数据[格子3].级别限制 then
    常规提示(id,"只有相同等级星辉石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 >= 35 then
    常规提示(id,"该星辉石等级已经达到上限！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].级别限制*10 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].级别限制*10
  体活刷新(id)
  if self:取宝石合成几率(id,玩家数据[id].道具.数据[格子1].级别限制) then
      玩家数据[id].道具.数据[格子1].级别限制 = 玩家数据[id].道具.数据[格子1].级别限制 + 1
      玩家数据[id].角色.数据.道具[内容.序列1] = nil
      玩家数据[id].道具.数据[格子2] = nil
      玩家数据[id].角色.数据.道具[内容.序列2] = nil
      玩家数据[id].道具.数据[格子3] = nil
      常规提示(id,"你获得了一颗更高等级的星辉石")
  else
      玩家数据[id].角色.数据.道具[内容.序列1] = nil
      玩家数据[id].道具.数据[格子2] = nil
      常规提示(id,"合成失败，你因此损失了一颗星辉石")
  end
  道具刷新(id)
end

function 装备处理类:合成钟灵石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].名称~="钟灵石" or 玩家数据[id].道具.数据[格子2].名称~="钟灵石"then
    常规提示(id,"#Y/只有钟灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"#Y/请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 ~= 玩家数据[id].道具.数据[格子2].级别限制 then
    常规提示(id,"#Y/只有相同等级钟灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].附加特性 ~= 玩家数据[id].道具.数据[格子2].附加特性 then
    常规提示(id,"#Y/只有相同类型钟灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 >= 16 then
    常规提示(id,"#Y/该钟灵石等级已经达到上限！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].级别限制*10 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].级别限制*10
  体活刷新(id)
  if 取随机数() <= (76-5*玩家数据[id].道具.数据[格子1].级别限制) then
    玩家数据[id].道具.数据[格子1].级别限制=玩家数据[id].道具.数据[格子1].级别限制+1
    玩家数据[id].道具.数据[格子2]=nil
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    常规提示(id,"#Y/合成成功!你获得了"..玩家数据[id].道具.数据[格子1].级别限制.."级钟灵石!")
  else
    玩家数据[id].道具.数据[格子2]=nil
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    常规提示(id,"合成失败，你因此损失了一颗钟灵石")
  end
  道具刷新(id)
end





function 装备处理类:合成精魄灵石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].名称~="精魄灵石" or 玩家数据[id].道具.数据[格子2].名称~="精魄灵石"then
    常规提示(id,"#Y/只有精魄灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"#Y/请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 ~= 玩家数据[id].道具.数据[格子2].级别限制 then
    常规提示(id,"#Y/只有相同等级精魄灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].子类 ~= 玩家数据[id].道具.数据[格子2].子类 then
    常规提示(id,"#Y/只有相同类型精魄灵石才可以进行合成操作！")
    return
   elseif 玩家数据[id].道具.数据[格子1].属性 ~= 玩家数据[id].道具.数据[格子2].属性 then
    常规提示(id,"#Y/只有相同属性精魄灵石才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].级别限制 >= 35 then
    常规提示(id,"#Y/该精魄灵石等级已经达到上限！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].级别限制*10 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].级别限制*10
  体活刷新(id)
  if self:取宝石合成几率(id,玩家数据[id].道具.数据[格子1].级别限制) then
    玩家数据[id].道具.数据[格子1].级别限制=玩家数据[id].道具.数据[格子1].级别限制+1
    玩家数据[id].道具.数据[格子2]=nil
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    常规提示(id,"#Y/合成成功!你获得了"..玩家数据[id].道具.数据[格子1].级别限制.."级精魄灵石!")
  else
    玩家数据[id].道具.数据[格子2]=nil
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    常规提示(id,"合成失败，你因此损失了一颗精魄灵石")
  end
  道具刷新(id)
end




function 装备处理类:合成怪物卡片(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].名称~="怪物卡片" or 玩家数据[id].道具.数据[格子2].名称~="怪物卡片"then
    常规提示(id,"只有怪物卡片才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].造型 ~= 玩家数据[id].道具.数据[格子2].造型 then
    常规提示(id,"只有相同类造型卡片才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].等级 ~= 玩家数据[id].道具.数据[格子2].等级 then
    常规提示(id,"只有相同等级卡片才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].次数 >= 玩家数据[id].道具.数据[格子1].等级 or 玩家数据[id].道具.数据[格子2].次数 >= 玩家数据[id].道具.数据[格子2].等级 then
    常规提示(id,"满次卡无法进行合成操作！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].等级*10 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].等级*10
  体活刷新(id)
  玩家数据[id].道具.数据[格子1].次数 = 玩家数据[id].道具.数据[格子1].次数 + 玩家数据[id].道具.数据[格子2].次数
  if 玩家数据[id].道具.数据[格子1].次数 > 玩家数据[id].道具.数据[格子1].等级 then
    玩家数据[id].道具.数据[格子1].次数 = 玩家数据[id].道具.数据[格子1].等级
  end
  玩家数据[id].角色.数据.道具[内容.序列1] = nil
  玩家数据[id].道具.数据[格子2] = nil
  常规提示(id,"你卡片的次数增加了")
  道具刷新(id)
end

function 装备处理类:合成百炼精铁(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].名称~="百炼精铁" or 玩家数据[id].道具.数据[格子2].名称~="百炼精铁"then
    常规提示(id,"只有百炼精铁才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].子类 ~= 玩家数据[id].道具.数据[格子2].子类 then
    常规提示(id,"只有相同等级精铁才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].子类 > 110 then
    常规提示(id,"只有110级以下精铁才可以合成(包含110级)！")
    return
  elseif 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].子类 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].子类
  体活刷新(id)
  玩家数据[id].道具.数据[格子1].子类 = 玩家数据[id].道具.数据[格子1].子类 + 10
  玩家数据[id].角色.数据.道具[内容.序列1] = nil
  玩家数据[id].道具.数据[格子2] = nil
  常规提示(id,"你合成了一个更高等级的精铁")
  道具刷新(id)
end



function 装备处理类:合成暗器(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
    道具刷新(id)
    return
  end
  if 玩家数据[id].道具.数据[格子1].总类~=2000 or 玩家数据[id].道具.数据[格子2].总类~=2000 then
    常规提示(id,"只有暗器才可以进行合成操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].追加伤害 or 玩家数据[id].道具.数据[格子2].追加伤害 then
    常规提示(id,"已合成过的暗器无法继续合成操作！")
    return
  end

  local jq = 玩家数据[id].角色:取生活技能等级("打造技巧")
  if 玩家数据[id].道具.数据[格子2].分类 >= 玩家数据[id].道具.数据[格子1].分类 then
    玩家数据[id].道具.数据[格子1].分类 = 玩家数据[id].道具.数据[格子2].分类
  end
  if 玩家数据[id].角色.数据.体力 < 玩家数据[id].道具.数据[格子1].分类 then
    常规提示(id,"你没有这么多的体力！")
    return
  end
  if jq < 玩家数据[id].道具.数据[格子1].分类-20 then
    常规提示(id,"你当前打造技巧等级无法合成该等级的暗器！")
    return
  end
  local 消耗银子 = 玩家数据[id].道具.数据[格子2].分类*玩家数据[id].道具.数据[格子2].分类+玩家数据[id].道具.数据[格子1].分类*玩家数据[id].道具.数据[格子1].分类
  if 玩家数据[id].角色.数据.银子 < 消耗银子 then
     常规提示(id,"你没那么多的银子")
     return
  end
  玩家数据[id].角色.数据.体力=玩家数据[id].角色.数据.体力-玩家数据[id].道具.数据[格子1].分类
  玩家数据[id].角色:扣除银子(消耗银子,"暗器合成",1)
   体活刷新(id)
  local x = 玩家数据[id].道具.数据[格子1].分类+玩家数据[id].道具.数据[格子2].分类
  玩家数据[id].道具.数据[格子1].耐久 = 玩家数据[id].道具.数据[格子1].耐久 + 玩家数据[id].道具.数据[格子2].耐久
  if 玩家数据[id].道具.数据[格子1].耐久 >100 then 玩家数据[id].道具.数据[格子1].耐久 = 100 end
  玩家数据[id].道具.数据[格子1].追加伤害 = 取随机数(math.floor(x*0.2),math.floor(x*0.5))
  if 取随机数() <= jq/10+10 then
    玩家数据[id].道具.数据[格子1].追加伤害 = math.floor(玩家数据[id].道具.数据[格子1].追加伤害*1.5)
  end
  if 取随机数() <= jq/10 then
    玩家数据[id].道具.数据[格子1].追加状态 = "淬毒"
  end
  玩家数据[id].角色.数据.道具[内容.序列1] = nil
  玩家数据[id].道具.数据[格子2] = nil
  常规提示(id,"你合成了一个暗器")
  道具刷新(id)
end



function 装备处理类:修理装备(连接id,序号,id,内容)
  if not 内容.序列 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  if not 格子1 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子1]==0 then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  if 玩家数据[id].道具.数据[格子1].总类~=2 or 玩家数据[id].道具.数据[格子1].分类 > 9 then
    常规提示(id,"只有装备才可以进行修理！")
    return
  elseif 玩家数据[id].道具.数据[格子1].耐久 >= 500 then
    常规提示(id,"该装备无需修理！")
    return
  elseif 玩家数据[id].道具.数据[格子1].修理失败 and 玩家数据[id].道具.数据[格子1].修理失败 >= 7 then
    常规提示(id,"该装备失败次数过多无法再次修理了！")
    return
  end
  local dz = 0
  local jn = ""
  if 玩家数据[id].道具.数据[格子1].分类 <= 6 then
    if 玩家数据[id].道具.数据[格子1].分类 == 1 or 玩家数据[id].道具.数据[格子1].分类 == 4 then
      dz = 玩家数据[id].角色:取生活技能等级("裁缝技巧")
      jn = "裁缝技巧"
    elseif 玩家数据[id].道具.数据[格子1].分类 == 2 or 玩家数据[id].道具.数据[格子1].分类 == 5 or 玩家数据[id].道具.数据[格子1].分类 == 6 then
      dz = 玩家数据[id].角色:取生活技能等级("炼金术")
      jn = "炼金术"
    elseif 玩家数据[id].道具.数据[格子1].分类 == 3 then
      dz = 玩家数据[id].角色:取生活技能等级("打造技巧")
      jn = "打造技巧"
    end
    if dz < 玩家数据[id].道具.数据[格子1].级别限制-9 then
      常规提示(id,"你的"..jn.."等级不够修理此装备")
      return
    end
  end
  local tx = 0
  if 玩家数据[id].道具.数据[格子1].特效 == "易修理" or 玩家数据[id].道具.数据[格子1].第二特效 == "易修理" then
    tx = 10
  end
  if 玩家数据[id].角色:扣除体力(玩家数据[id].道具.数据[格子1].级别限制-20,"修理装备",1) then
    if 取随机数() <= 50-dz/10 - tx then
      if 玩家数据[id].道具.数据[格子1].修理失败==nil then
         玩家数据[id].道具.数据[格子1].修理失败 = 0
      end
      玩家数据[id].道具.数据[格子1].修理失败 = 玩家数据[id].道具.数据[格子1].修理失败+1
      玩家数据[id].道具.数据[格子1].耐久 = 500
      常规提示(id,"修理装备失败了。")
    else
      玩家数据[id].道具.数据[格子1].耐久 = 1000
      常规提示(id,"恭喜你修理装备成功。")
    end
    道具刷新(id)
  else
    常规提示(id,"你好像没有那么多的体力。")
  end
end




function 装备处理类:分解装备(连接id,序号,id,内容)
      if not 内容.序列 or not 内容.序列1 then return end
      local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
      local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
        if not 格子1 or not 格子2 then return end
      if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
        道具刷新(id)
        return
      elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
      elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
        常规提示(id,"请先解锁后物品再进行此操作！")
        return
      end
      local 分解格子=0
      local 装备格子=0
      if 玩家数据[id].道具.数据[格子1].名称=="分解符" then
        分解格子=格子1
        装备格子=格子2
      elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 <= 13 then
        分解格子=格子2
        装备格子=格子1
      end
    if 装备格子==0 or 宝石格子==0 then
          常规提示(id,"请放入正确的装备和分解符！")
          return
    elseif 玩家数据[id].道具.数据[装备格子].总类~=2 or 玩家数据[id].道具.数据[装备格子].分类 > 13 then
      常规提示(id,"只有装备才可以进行分解！")
      return
    elseif 玩家数据[id].道具.数据[装备格子].级别限制 < 60 then
      常规提示(id,"只有60级以上装备才可以进行分解！")
      return
    elseif 玩家数据[id].道具.数据[装备格子].级别限制 >= 150 then
      常规提示(id,"只有150级以下装备才可以进行分解！")
      return
    elseif 玩家数据[id].道具.数据[装备格子].制造者 == nil then
      常规提示(id,"只有打造的装备才可以进行分解！")
      return
    elseif not 玩家数据[id].道具.数据[分解格子].数量  or 玩家数据[id].道具.数据[分解格子].名称~="分解符" then
      常规提示(id,"材料错误无法分解！")
      return
    end
    local 分解 = self:取分解(玩家数据[id].道具.数据[装备格子].级别限制,玩家数据[id].道具.数据[装备格子].分类)
    if 玩家数据[id].道具.数据[分解格子].数量<分解[1] then
       常规提示(id,"你好像没有这么多的分解符(分解此装备需要消耗"..分解[1].."张分解符)")
        return
    end
    if  玩家数据[id].角色.数据.体力 < 分解[2] then
      常规提示(id,"你的体力好像不够哟！")
      return
    end

    玩家数据[id].道具:给予道具(id,"吸附石",分解[3])
    local 级别=玩家数据[id].道具.数据[装备格子].级别限制-10
    if 玩家数据[id].道具.数据[装备格子].分类 <= 6 and 玩家数据[id].道具.数据[装备格子].级别限制 >= 70 then
        玩家数据[id].道具:给予道具(id,"百炼精铁",级别)
    elseif 玩家数据[id].道具.数据[装备格子].分类 >= 10 and 玩家数据[id].道具.数据[装备格子].分类 <=13 and 玩家数据[id].道具.数据[装备格子].级别限制 >= 80 then
        级别=math.floor((玩家数据[id].道具.数据[装备格子].级别限制-20)/10)
        玩家数据[id].道具:给予道具(id,"元灵晶石",{级别})
    else
        玩家数据[id].道具:给予道具(id,"炼妖石",{级别})
    end
    玩家数据[id].角色.数据.体力 = 玩家数据[id].角色.数据.体力 - 分解[2]
    玩家数据[id].道具.数据[装备格子]=nil
    玩家数据[id].道具.数据[分解格子].数量=玩家数据[id].道具.数据[分解格子].数量-分解[1]
    if 装备格子==玩家数据[id].角色.数据.道具[内容.序列] then
        玩家数据[id].角色.数据.道具[内容.序列]=nil
        if 玩家数据[id].道具.数据[分解格子].数量<=0 then
            玩家数据[id].道具.数据[分解格子]=nil
            玩家数据[id].角色.数据.道具[内容.序列1]=nil
        end
    else
        玩家数据[id].角色.数据.道具[内容.序列1]=nil
        if 玩家数据[id].道具.数据[分解格子].数量<=0 then
            玩家数据[id].道具.数据[分解格子]=nil
            玩家数据[id].角色.数据.道具[内容.序列]=nil
        end
    end
    常规提示(id,"#Y/分解成功")
    体活刷新(id)
    道具刷新(id)
end


function 装备处理类:取分解(等级,类型)
  if 类型 <= 6 or (类型 >=10 and 类型 <= 13) then
    if 等级 == 60 then
      return {5,90,1}
    elseif 等级 == 70 then
      return {5,105,取随机数(1,2)}
    elseif 等级 == 80 then
      return {5,120,取随机数(2,3)}
    elseif 等级 == 90 then
      return {6,135,取随机数(2,4)}
    elseif 等级 == 100 then
      return {6,150,取随机数(3,5)}
    elseif 等级 == 110 then
      return {7,165,取随机数(3,5)}
    elseif 等级 == 120 then
      return {12,180,取随机数(4,6)}
    elseif 等级 == 130 then
      return {16,195,取随机数(5,6)}
    elseif 等级 == 140 then
      return {23,210,取随机数(5,6)}
    end
  else
    if 等级 == 65 then
      return {4,90,1}
    elseif 等级 == 75 then
      return {4,105,1}
    elseif 等级 == 85 then
      return {4,120,取随机数(1,2)}
    elseif 等级 == 95 then
      return {4,135,取随机数(1,2)}
    elseif 等级 == 105 then
      return {5,150,取随机数(2,3)}
    elseif 等级 == 115 then
      return {5,165,取随机数(2,3)}
    elseif 等级 == 125 then
      return {8,180,取随机数(3,5)}
    elseif 等级 == 135 then
      return {16,195,取随机数(4,6)}
    elseif 等级 == 145 then
      return {23,210,取随机数(5,6)}
    end
  end
end


function 装备处理类:熔炼装备(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
        常规提示(id,"请先解锁后物品再进行此操作！")
        return
  end


  local 宝石格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="钨金" then
    宝石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 <= 6 then
    宝石格子=格子2
    装备格子=格子1
  end
  if 装备格子==0 or 宝石格子==0 then
    常规提示(id,"请放入正确的装备和钨金！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 > 6 then
    常规提示(id,"请放入正确的装备和钨金！")
    return
  elseif 玩家数据[id].道具.数据[宝石格子].名称~="钨金" then
    常规提示(id,"请放入正确的装备和钨金！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].耐久<=20 then
    常规提示(id,"装备耐久度不足！")
    return
  end
  if not 玩家数据[id].道具.数据[装备格子].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  elseif 玩家数据[id].道具.数据[装备格子].级别限制 > 玩家数据[id].道具.数据[宝石格子].级别限制 then
    常规提示(id,"#Y/该装备需要使用#R"..(玩家数据[id].道具.数据[装备格子].级别限制).."#Y级的钨金进行熔炼。")
    return
  end
  local 消耗体力 = math.floor(玩家数据[id].道具.数据[装备格子].级别限制/10)
  local 总价 = math.ceil(玩家数据[id].道具.数据[装备格子].级别限制/60)*20000
  if 玩家数据[id].角色.数据.体力 < 消耗体力 then
    常规提示(id,"你好像没有这么多的体力")
    return
  elseif 取银子(id) <总价 then
    常规提示(id,"你好像没有这么多的银子")
    return
  end
  玩家数据[id].角色.数据.体力 = 玩家数据[id].角色.数据.体力 - 消耗体力
  玩家数据[id].角色:扣除银子(总价,"熔炼消耗",1)
  体活刷新(id)


  if 取随机数() <= 80 then
      local 基础属性 = {}
      local 三围属性 = {"体质","魔力","力量","耐力","敏捷"}
      if 玩家数据[id].道具.数据[装备格子].分类 == 3  then--武器
            table.insert(基础属性,{"伤害",math.floor(玩家数据[id].道具.数据[装备格子].伤害/30)})
            table.insert(基础属性,{"命中",math.floor(玩家数据[id].道具.数据[装备格子].命中/15)})
            for i,v in ipairs(三围属性) do
              if 玩家数据[id].道具.数据[装备格子][三围属性[i]]~=nil then
                 table.insert(基础属性,{三围属性[i],math.floor(玩家数据[id].道具.数据[装备格子][三围属性[i]]/20)})
              end
            end
      elseif 玩家数据[id].道具.数据[装备格子].分类 == 1 then --头盔
            table.insert(基础属性,{"防御",math.floor(玩家数据[id].道具.数据[装备格子].防御/30)})
            table.insert(基础属性,{"魔法",math.floor(玩家数据[id].道具.数据[装备格子].魔法/30)})
      elseif 玩家数据[id].道具.数据[装备格子].分类 == 5 then --腰带
            table.insert(基础属性,{"防御",math.floor(玩家数据[id].道具.数据[装备格子].防御/30)})
            table.insert(基础属性,{"气血",math.floor(玩家数据[id].道具.数据[装备格子].气血/30)})
      elseif 玩家数据[id].道具.数据[装备格子].分类 == 2 then --项链
            table.insert(基础属性,{"灵力",math.floor(玩家数据[id].道具.数据[装备格子].灵力/30)})
      elseif 玩家数据[id].道具.数据[装备格子].分类 == 4 then --衣服
              if string.find(玩家数据[id].道具.数据[装备格子].名称,"(坤)") and 玩家数据[id].道具.数据[装备格子].角色限制 and 玩家数据[id].道具.数据[装备格子].角色限制[1] =="影精灵" then
                    table.insert(基础属性,{"伤害",math.floor(玩家数据[id].道具.数据[装备格子].伤害/30)})
                    table.insert(基础属性,{"命中",math.floor(玩家数据[id].道具.数据[装备格子].命中/15)})
              else
                    table.insert(基础属性,{"防御",math.floor(玩家数据[id].道具.数据[装备格子].防御/30)})
              end
              for i,v in ipairs(三围属性) do
                  if 玩家数据[id].道具.数据[装备格子][三围属性[i]]~=nil then
                      table.insert(基础属性,{三围属性[i],math.floor(玩家数据[id].道具.数据[装备格子][三围属性[i]]/10)})
                  end
              end

      elseif 玩家数据[id].道具.数据[装备格子].分类 == 6 then --鞋子
            table.insert(基础属性,{"防御",math.floor(玩家数据[id].道具.数据[装备格子].防御/30)})
            table.insert(基础属性,{"敏捷",math.floor(玩家数据[id].道具.数据[装备格子].敏捷/20)})
      end
      if 玩家数据[id].道具.数据[装备格子].熔炼属性==nil then
         玩家数据[id].道具.数据[装备格子].熔炼属性={}
      end
      local 临时 = {}
        for i=1,#基础属性 do
            临时[i]=基础属性[i]
            临时[i][2]=取随机数(-临时[i][2],临时[i][2])
        end
        if  临时[1]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[1]==nil  then
           玩家数据[id].道具.数据[装备格子].熔炼属性[1]=临时[1]
        elseif 临时[1]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[1]~=nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]=临时[1][2]+玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]
        end
        if  临时[2]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[2]==nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[2]=临时[2]
        elseif 临时[2]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[2]~=nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]=临时[2][2]+玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]
        end
        if  临时[3]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[3]==nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[3]=临时[3]
        elseif 临时[3]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[3]~=nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]=临时[3][2]+玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]
        end
        if  临时[4]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[4]==nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[4]=临时[4]
        elseif 临时[4]~=nil and 玩家数据[id].道具.数据[装备格子].熔炼属性[4]~=nil then
           玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]=临时[4][2]+玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]
        end


        if  玩家数据[id].道具.数据[装备格子].熔炼属性[1]~=nil then
            if 玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]>0 then
               玩家数据[id].道具.数据[装备格子].熔炼属性[1][3]="+"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]>玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[1][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[1][1]]/20)
              end
            else
               玩家数据[id].道具.数据[装备格子].熔炼属性[1][3]="-"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]<玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[1][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[1][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[1][1]]/20)
              end
            end
        end
        if  玩家数据[id].道具.数据[装备格子].熔炼属性[2]~=nil then
            if 玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]>0 then
               玩家数据[id].道具.数据[装备格子].熔炼属性[2][3]="+"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]>玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[2][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[2][1]]/20)
              end
            else
               玩家数据[id].道具.数据[装备格子].熔炼属性[2][3]="-"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]<玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[2][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[2][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[2][1]]/20)
              end
            end
        end
        if  玩家数据[id].道具.数据[装备格子].熔炼属性[3]~=nil then
            if 玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]>0 then
               玩家数据[id].道具.数据[装备格子].熔炼属性[3][3]="+"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]>玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[3][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[3][1]]/20)
              end
            else
               玩家数据[id].道具.数据[装备格子].熔炼属性[3][3]="-"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]<玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[3][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[3][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[3][1]]/20)
              end
            end
        end
         if  玩家数据[id].道具.数据[装备格子].熔炼属性[4]~=nil then
            if 玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]>0 then
               玩家数据[id].道具.数据[装备格子].熔炼属性[4][3]="+"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]>玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[4][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[4][1]]/20)
              end
            else
               玩家数据[id].道具.数据[装备格子].熔炼属性[4][3]="-"
              if 玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]<玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[4][1]]/10 then
                 玩家数据[id].道具.数据[装备格子].熔炼属性[4][2]=math.floor(玩家数据[id].道具.数据[装备格子][玩家数据[id].道具.数据[装备格子].熔炼属性[4][1]]/20)
              end
            end
        end

  else
    玩家数据[id].道具.数据[装备格子].耐久 = 玩家数据[id].道具.数据[装备格子].耐久 - 5
    常规提示(id,"#Y/熔炼失败了，并损耗了5点装备耐久！")
  end
  if 宝石格子==玩家数据[id].角色.数据.道具[内容.序列] then
    玩家数据[id].角色.数据.道具[内容.序列]=nil
    玩家数据[id].道具.数据[宝石格子]=nil
  else
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    玩家数据[id].道具.数据[宝石格子]=nil
  end
  道具刷新(id)
end



function 装备处理类:还原装备(连接id,序号,id,内容)
  if not 内容.序列 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  if not 格子1 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子1]==0 then
    道具刷新(id)
    return
  elseif 玩家数据[id].道具.数据[格子1].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  if 格子1==0 or 玩家数据[id].道具.数据[格子1].总类 ~= 2 or 玩家数据[id].道具.数据[格子1].分类 > 6 then
    常规提示(id,"请放入正确的装备！")
    return
  end
  if not 玩家数据[id].道具.数据[格子1].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  elseif 玩家数据[id].道具.数据[格子1].熔炼属性 == nil then
    常规提示(id,"#Y/该装备没有需要还原的熔炼属性。")
    return
  end
  local 总价 = math.ceil(玩家数据[id].道具.数据[格子1].级别限制*10000)
  if 取银子(id)<总价 then
    常规提示(id,"你好像没有这么多的银子")
    return
  end
  玩家数据[id].角色:扣除银子(总价,"还原消耗",1)
  玩家数据[id].道具.数据[格子1].熔炼属性 = nil
  常规提示(id,"#Y/该装备熔炼属性已经被重置!")
  道具刷新(id)
end



function 装备处理类:镶嵌宝石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 宝石格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].总类==5 and 玩家数据[id].道具.数据[格子1].分类==6 then
    宝石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 <= 6 then
    宝石格子=格子2
    装备格子=格子1
  end

  if 装备格子==0 or 宝石格子==0 then
    常规提示(id,"请放入正确的宝石和人物装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 >6 then
    常规提示(id,"请放入正确的宝石和人物装备！")
    return
  elseif 玩家数据[id].道具.数据[宝石格子].总类 ~= 5 or 玩家数据[id].道具.数据[宝石格子].分类 ~=6 then
    常规提示(id,"请放入正确的宝石和人物装备！")
    return
  end
  if 玩家数据[id].道具.数据[宝石格子].名称=="珍珠"  then
     常规提示(id,"珍珠无法镶嵌装备")
     return
  end
  if self:取可以镶嵌(玩家数据[id].道具.数据[装备格子],玩家数据[id].道具.数据[宝石格子]) then-- 装备镶嵌
      if not 玩家数据[id].道具.数据[装备格子].鉴定 then
        道具刷新(id)
        常规提示(id,"该装备还未鉴定!")
        return
      end
      local 锻造级别=0
      if 玩家数据[id].道具.数据[装备格子].锻炼等级~=nil then
        锻造级别=玩家数据[id].道具.数据[装备格子].锻炼等级
      end
      锻造级别=锻造级别+1
      local 打造上限 = 35
      if  玩家数据[id].道具.数据[装备格子].特效 == "精致" or  玩家数据[id].道具.数据[装备格子].第二特效 == "精致" then
        打造上限 = 打造上限 + 1
      end

      if 锻造级别>=打造上限 then
        常规提示(id,"该装备已达到锻造等级上限")
        return
      end
      if 玩家数据[id].道具.数据[宝石格子].级别限制<锻造级别 then
        常规提示(id,"该装备目前只能用#R/"..锻造级别.."#Y/级宝石镶嵌")
        return
      end



      if 玩家数据[id].道具.数据[装备格子].镶嵌宝石==nil then
        玩家数据[id].道具.数据[装备格子].镶嵌宝石={}
        玩家数据[id].道具.数据[装备格子].镶嵌类型={}
      end
      if 玩家数据[id].道具.数据[装备格子].镶嵌宝石[1]==nil then
        玩家数据[id].道具.数据[装备格子].镶嵌宝石[1]=玩家数据[id].道具.数据[宝石格子].名称
      elseif 玩家数据[id].道具.数据[装备格子].镶嵌宝石[2]==nil and 玩家数据[id].道具.数据[装备格子].镶嵌宝石[1]~=玩家数据[id].道具.数据[宝石格子].名称  then
        玩家数据[id].道具.数据[装备格子].镶嵌宝石[2]=玩家数据[id].道具.数据[宝石格子].名称
      elseif 玩家数据[id].道具.数据[装备格子].镶嵌宝石[1]~=玩家数据[id].道具.数据[宝石格子].名称  and 玩家数据[id].道具.数据[装备格子].镶嵌宝石[2]~=玩家数据[id].道具.数据[宝石格子].名称 then
        常规提示(id,"装备最多只能镶嵌两种不同类型的宝石")
        return
      end


      玩家数据[id].道具.数据[装备格子].锻炼等级=锻造级别
      玩家数据[id].道具.数据[装备格子].镶嵌类型[锻造级别]=玩家数据[id].道具.数据[宝石格子].名称
      if 玩家数据[id].道具.数据[宝石格子].子类 == 1 then
        玩家数据[id].道具.数据[装备格子].气血 = (玩家数据[id].道具.数据[装备格子].气血 or 0) - math.floor(40*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].气血 = (玩家数据[id].道具.数据[装备格子].气血 or 0) + math.floor(40*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 2 then
        玩家数据[id].道具.数据[装备格子].防御 = (玩家数据[id].道具.数据[装备格子].防御 or 0) - math.floor(12*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].防御 = (玩家数据[id].道具.数据[装备格子].防御 or 0) + math.floor(12*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 3 then
        玩家数据[id].道具.数据[装备格子].伤害 = (玩家数据[id].道具.数据[装备格子].伤害 or 0) - math.floor(8*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].伤害 = (玩家数据[id].道具.数据[装备格子].伤害 or 0) + math.floor(8*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 4 then
        玩家数据[id].道具.数据[装备格子].灵力 = (玩家数据[id].道具.数据[装备格子].灵力 or 0) - math.floor(6*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].灵力 = (玩家数据[id].道具.数据[装备格子].灵力 or 0) + math.floor(6*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 5 then
        玩家数据[id].道具.数据[装备格子].命中 = (玩家数据[id].道具.数据[装备格子].命中 or 0) - math.floor(25*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].命中 = (玩家数据[id].道具.数据[装备格子].命中 or 0) + math.floor(25*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 6 then
        玩家数据[id].道具.数据[装备格子].速度 = (玩家数据[id].道具.数据[装备格子].速度 or 0) - math.floor(8*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].速度 = (玩家数据[id].道具.数据[装备格子].速度 or 0) + math.floor(8*玩家数据[id].道具.数据[宝石格子].级别限制)
      elseif 玩家数据[id].道具.数据[宝石格子].子类 == 7 then
        玩家数据[id].道具.数据[装备格子].躲避 = (玩家数据[id].道具.数据[装备格子].躲避 or 0) - math.floor(20*(玩家数据[id].道具.数据[宝石格子].级别限制-1))
        玩家数据[id].道具.数据[装备格子].躲避 = (玩家数据[id].道具.数据[装备格子].躲避 or 0) + math.floor(20*玩家数据[id].道具.数据[宝石格子].级别限制)
      end


      if 宝石格子==玩家数据[id].角色.数据.道具[内容.序列] then
        玩家数据[id].角色.数据.道具[内容.序列]=nil
        玩家数据[id].道具.数据[宝石格子]=nil
      else
        玩家数据[id].角色.数据.道具[内容.序列1]=nil
        玩家数据[id].道具.数据[宝石格子]=nil
      end
      常规提示(id,"镶嵌装备成功")
      道具刷新(id)
    else
      常规提示(id,"这种宝石无法镶嵌到此类装备上")
      return
    end
end


function 装备处理类:镶嵌星辉石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 宝石格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="星辉石" then
    宝石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 >= 10 and 玩家数据[id].道具.数据[格子1].分类 <= 13 then
    宝石格子=格子2
    装备格子=格子1
  end

  if 装备格子==0 or 宝石格子==0 then
    常规提示(id,"请放入正确的星辉石和灵饰！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 <10 or 玩家数据[id].道具.数据[装备格子].分类 > 13 then
    常规提示(id,"请放入正确的星辉石和灵饰！")
    return
  elseif 玩家数据[id].道具.数据[宝石格子].名称~="星辉石" then
    常规提示(id,"请放入正确的星辉石和灵饰！")
    return
  end

  if not 玩家数据[id].道具.数据[装备格子].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  end

  local 锻造级别=玩家数据[id].道具.数据[装备格子].幻化等级+1
  local 等级上限=35

  if 玩家数据[id].道具.数据[宝石格子].级别限制<锻造级别 then
    常规提示(id,"该灵饰目前只能用#R/"..(锻造级别).."#Y/级以上的星辉石进行强化")
    return
  -- elseif 锻造级别 >= 玩家数据[id].道具.数据[装备格子].级别限制 then
  --   常规提示(id,"#Y/该灵饰已经镶嵌到了顶级!")
  --   return
  elseif 锻造级别 >= 等级上限 then
    常规提示(id,"#Y/该灵饰强化等级已经达到上限!")
    return
  end

     玩家数据[id].道具.数据[装备格子].幻化等级=玩家数据[id].道具.数据[装备格子].幻化等级+1
     local 强化等级 = 玩家数据[id].道具.数据[装备格子].幻化等级
      for n=1,#玩家数据[id].道具.数据[装备格子].幻化属性.附加 do
        玩家数据[id].道具.数据[装备格子].幻化属性.附加[n].强化=0
        local 强化类型 = 玩家数据[id].道具.数据[装备格子].幻化属性.附加[n].类型
       -- 玩家数据[id].道具.数据[装备格子].幻化属性.附加[n].强化=math.floor(灵饰强化[强化类型]*强化等级)
        玩家数据[id].道具.数据[装备格子].幻化属性.附加[n].强化=取灵饰强化(强化类型,玩家数据[id].道具.数据[装备格子].级别限制,强化等级)
      end
      常规提示(id,"#Y/灵饰强化成功！")
     if 宝石格子==玩家数据[id].角色.数据.道具[内容.序列] then
        玩家数据[id].角色.数据.道具[内容.序列]=nil
        玩家数据[id].道具.数据[宝石格子]=nil
     else
        玩家数据[id].角色.数据.道具[内容.序列1]=nil
        玩家数据[id].道具.数据[宝石格子]=nil
     end
      道具刷新(id)
end
function 装备处理类:镶嵌钟灵石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 宝石格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].总类==5 and 玩家数据[id].道具.数据[格子1].名称=="钟灵石" then
    宝石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 >= 10 and 玩家数据[id].道具.数据[格子1].分类 <= 13 then
    宝石格子=格子2
    装备格子=格子1
  end
  if 装备格子==0 or 宝石格子==0 then
        常规提示(id,"请放入正确的钟灵石和灵饰！")
        return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 <10 or 玩家数据[id].道具.数据[装备格子].分类 > 13  then
      常规提示(id,"请放入正确的钟灵石和灵饰！")
      return
  elseif 玩家数据[id].道具.数据[宝石格子].名称~="钟灵石"  then
      常规提示(id,"请放入正确的钟灵石和灵饰！")
      return
  elseif not  玩家数据[id].道具.数据[宝石格子].附加特性 or not 玩家数据[id].道具.数据[宝石格子].级别限制 then
       玩家数据[id].道具.数据[宝石格子]=nil
      常规提示(id,"你的钟灵石有问题,已回收！")
      道具刷新(id)
      return
  end
  if 玩家数据[id].道具.数据[宝石格子].附加特性~="心源" and 玩家数据[id].道具.数据[宝石格子].附加特性~="固若金汤"
        and 玩家数据[id].道具.数据[宝石格子].附加特性~="锐不可当" and 玩家数据[id].道具.数据[宝石格子].附加特性~="通真达灵"
        and 玩家数据[id].道具.数据[宝石格子].附加特性~="气血方刚" and 玩家数据[id].道具.数据[宝石格子].附加特性~="健步如飞"
        and 玩家数据[id].道具.数据[宝石格子].附加特性~="心无旁骛" and 玩家数据[id].道具.数据[宝石格子].附加特性~="回春之术"
        and 玩家数据[id].道具.数据[宝石格子].附加特性~="风雨不动" and 玩家数据[id].道具.数据[宝石格子].附加特性~="气壮山河" then
       玩家数据[id].道具.数据[宝石格子]=nil
      常规提示(id,"你的钟灵石有问题,已回收！")
      道具刷新(id)
      return
  end
  if not 玩家数据[id].道具.数据[装备格子].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  end
  if 玩家数据[id].道具.数据[装备格子].级别限制 == nil then
    玩家数据[id].道具.数据[装备格子].级别限制 = 取物品数据(玩家数据[id].道具.数据[装备格子].名称)[5]
  end
  if 玩家数据[id].道具.数据[装备格子].附加特性 == nil then
    玩家数据[id].道具.数据[装备格子].附加特性 = {}
  end
  if 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 and 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 >=16 then
    常规提示(id,"该灵饰幻化等级已经达到上限!")
    return
  end
  if 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 and 玩家数据[id].道具.数据[宝石格子].级别限制 <= 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 then
    常规提示(id,"#Y/该灵饰当前需要使用#R"..(玩家数据[id].道具.数据[装备格子].附加特性.幻化等级+1).."#Y等级钟灵石进行幻化操作。")
    return
  elseif  玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 and  玩家数据[id].道具.数据[宝石格子].附加特性 ~= 玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 then
    常规提示(id,"#Y/该灵饰当前需要使用#R"..玩家数据[id].道具.数据[装备格子].附加特性.幻化类型.."#Y钟灵石进行幻化操作。")
    return
  end

  if 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 == nil then
    玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 = 1
    玩家数据[id].道具.数据[装备格子].附加特性.幻化类型 = 玩家数据[id].道具.数据[宝石格子].附加特性
  else
    玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 = 玩家数据[id].道具.数据[装备格子].附加特性.幻化等级 + 1
  end
  if 宝石格子==玩家数据[id].角色.数据.道具[内容.序列] then
    玩家数据[id].角色.数据.道具[内容.序列]=nil
    玩家数据[id].道具.数据[宝石格子]=nil
  else
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    玩家数据[id].道具.数据[宝石格子]=nil
  end
  常规提示(id,"#Y/灵饰幻化特性成功！")
  道具刷新(id)
end



 function 装备处理类:精魄灵石(连接id,序号,id,内容)
    if not 内容.序列 or not 内容.序列1 then return end
    local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
    local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
    if not 格子1 or not 格子2 then return end
    if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
        道具刷新(id)
        return
    elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
          道具刷新(id)
          return
    elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
      常规提示(id,"请先解锁后物品再进行此操作！")
      return
    end
    local 点化石格子 = 0
     local 装备格子 = 0
  if 玩家数据[id].道具.数据[格子1].名称=="精魄灵石" then
    点化石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 > 6 and 玩家数据[id].道具.数据[格子1].分类 <= 9 then
    点化石格子=格子2
    装备格子=格子1
  end
  if 装备格子==0 or 点化石格子==0 then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 < 7 or 玩家数据[id].道具.数据[装备格子].分类 > 9 then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  elseif 玩家数据[id].道具.数据[点化石格子].名称~="精魄灵石" then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  end
  local 锻造级别 = 1
  if 玩家数据[id].道具.数据[装备格子].镶嵌等级~=nil then
      锻造级别=玩家数据[id].道具.数据[装备格子].镶嵌等级+1
    else
     玩家数据[id].道具.数据[装备格子].镶嵌等级 = 0
  end
   if 玩家数据[id].道具.数据[点化石格子].级别限制<锻造级别 then
        常规提示(id,"该装备目前只能用#R/"..锻造级别.."#Y/级宝石镶嵌")
        return
    elseif 锻造级别>=35 then
        常规提示(id,"该装备镶嵌等级已达上限")
        return
    end
    local 装备类型 = 玩家数据[id].道具.数据[装备格子].分类
    local 灵石类型 = 玩家数据[id].道具.数据[点化石格子].子类



    if 装备类型==1 and 灵石类型~=2 then
      常规提示(id,"该装备无法镶嵌该石头")
      return
    elseif 装备类型==2 and 灵石类型 ~= 1 then
      常规提示(id,"该装备无法镶嵌该石头")
      return
     elseif 装备类型==3 and 灵石类型~=3 then
      常规提示(id,"该装备无法镶嵌该石头")
      return
    end




     if 玩家数据[id].道具.数据[装备格子].镶嵌宝石==nil then
        玩家数据[id].道具.数据[装备格子].镶嵌宝石 = 玩家数据[id].道具.数据[点化石格子].属性
     else
        if 玩家数据[id].道具.数据[装备格子].镶嵌宝石~=玩家数据[id].道具.数据[点化石格子].属性 then
              常规提示(id,"该装备无法镶嵌该石头")
            return
        end
     end


    玩家数据[id].道具.数据[装备格子].镶嵌等级=玩家数据[id].道具.数据[装备格子].镶嵌等级+1


    if 灵石类型 == 1 then
      if 玩家数据[id].道具.数据[点化石格子].属性== "伤害" then
           玩家数据[id].道具.数据[装备格子].伤害 = (玩家数据[id].道具.数据[装备格子].伤害 or 0) - math.floor(10*(玩家数据[id].道具.数据[点化石格子].级别限制-1))
           玩家数据[id].道具.数据[装备格子].伤害 = (玩家数据[id].道具.数据[装备格子].伤害 or 0) + math.floor(10*玩家数据[id].道具.数据[点化石格子].级别限制)
      else
           玩家数据[id].道具.数据[装备格子].灵力 = (玩家数据[id].道具.数据[装备格子].灵力 or 0) - math.floor(4*(玩家数据[id].道具.数据[点化石格子].级别限制-1))
           玩家数据[id].道具.数据[装备格子].灵力 = (玩家数据[id].道具.数据[装备格子].灵力 or 0) + math.floor(4*玩家数据[id].道具.数据[点化石格子].级别限制)
      end
    elseif 灵石类型 == 2 then
           玩家数据[id].道具.数据[装备格子].速度 = (玩家数据[id].道具.数据[装备格子].速度 or 0) - math.floor(6*(玩家数据[id].道具.数据[点化石格子].级别限制-1))
           玩家数据[id].道具.数据[装备格子].速度 = (玩家数据[id].道具.数据[装备格子].速度 or 0) + math.floor(6*玩家数据[id].道具.数据[点化石格子].级别限制)
    elseif 灵石类型 == 3 then
        if 玩家数据[id].道具.数据[点化石格子].属性== "气血" then
           玩家数据[id].道具.数据[装备格子].气血 = (玩家数据[id].道具.数据[装备格子].气血 or 0) - math.floor(30*(玩家数据[id].道具.数据[点化石格子].级别限制-1))
           玩家数据[id].道具.数据[装备格子].气血 = (玩家数据[id].道具.数据[装备格子].气血 or 0) + math.floor(30*玩家数据[id].道具.数据[点化石格子].级别限制)
        else
             玩家数据[id].道具.数据[装备格子].防御 = (玩家数据[id].道具.数据[装备格子].防御 or 0) - math.floor(8*(玩家数据[id].道具.数据[点化石格子].级别限制-1))
             玩家数据[id].道具.数据[装备格子].防御 = (玩家数据[id].道具.数据[装备格子].防御 or 0) + math.floor(8*玩家数据[id].道具.数据[点化石格子].级别限制)
        end
    end

  if 点化石格子==玩家数据[id].角色.数据.道具[内容.序列] then
    玩家数据[id].角色.数据.道具[内容.序列]=nil
    玩家数据[id].道具.数据[点化石格子]=nil
  else
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    玩家数据[id].道具.数据[点化石格子]=nil
  end
  常规提示(id,"镶嵌召唤兽装备成功")
  道具刷新(id)
end


function 装备处理类:镶嵌点化石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 点化石格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="点化石" then
    点化石格子=格子1
    装备格子=格子2
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 > 6 and 玩家数据[id].道具.数据[格子1].分类 <= 9 then
    点化石格子=格子2
    装备格子=格子1
  end
  if 装备格子==0 or 点化石格子==0 then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 < 7 or 玩家数据[id].道具.数据[装备格子].分类 > 9 then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  elseif 玩家数据[id].道具.数据[点化石格子].名称~="点化石" then
    常规提示(id,"请放入正确的点化石和召唤兽装备！")
    return
  end

  local 附加状态 = "附加状态"
    local 追加法术技能表 = {"善恶有报","力劈华山","壁垒击破","惊心一剑","剑荡四方","水攻","烈火","雷击","落岩","奔雷咒","水漫金山","地狱烈火","泰山压顶","月光","夜舞倾城","上古灵符"}
    for i=1,#追加法术技能表 do
      if 玩家数据[id].道具.数据[点化石格子].附带技能==追加法术技能表[i] then
        附加状态="追加法术"
      end
    end
    玩家数据[id].道具.数据[装备格子].套装效果={附加状态,玩家数据[id].道具.数据[点化石格子].附带技能}

  if 点化石格子==玩家数据[id].角色.数据.道具[内容.序列] then
    玩家数据[id].角色.数据.道具[内容.序列]=nil
    玩家数据[id].道具.数据[点化石格子]=nil
  else
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    玩家数据[id].道具.数据[点化石格子]=nil
  end
  常规提示(id,"点化召唤兽装备成功")
  道具刷新(id)
end



function 装备处理类:镶嵌珍珠(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  end
  local 珍珠格子=0
  local 装备格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="珍珠" then
    珍珠格子=tonumber(格子1)
    装备格子=tonumber(格子2)
  elseif 玩家数据[id].道具.数据[格子1].总类==2 and 玩家数据[id].道具.数据[格子1].分类 <= 9 then
    珍珠格子=tonumber(格子2)
    装备格子=tonumber(格子1)
  end
  if not 装备格子 or not 珍珠格子 or 装备格子==0 or 珍珠格子==0
    or not 玩家数据[id].道具.数据[装备格子]
    or not 玩家数据[id].道具.数据[珍珠格子]
    or 玩家数据[id].道具.数据[装备格子]==0
    or 玩家数据[id].道具.数据[珍珠格子]==0  then
    常规提示(id,"请放入正确的珍珠和装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 > 9 then
    常规提示(id,"请放入正确的珍珠和装备！")
    return
  elseif 玩家数据[id].道具.数据[珍珠格子].名称~="珍珠" then
    常规提示(id,"请放入正确的珍珠和装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].加锁 or 玩家数据[id].道具.数据[珍珠格子].加锁 then
    道具刷新(id)
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif not 玩家数据[id].道具.数据[装备格子].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  end
  if 玩家数据[id].道具.数据[装备格子].级别限制 > 玩家数据[id].道具.数据[珍珠格子].级别限制 then
    常规提示(id,"该装备只能使用>="..玩家数据[id].道具.数据[装备格子].级别限制.."的珍珠进行镶嵌。")
    return
  elseif 玩家数据[id].道具.数据[装备格子].耐久 > 700 then
    常规提示(id,"该装备目前耐久度较高,无需使用珍珠镶嵌!")
    return
  end
  玩家数据[id].道具.数据[装备格子].耐久 = 玩家数据[id].道具.数据[装备格子].耐久 + 100
  if 珍珠格子==玩家数据[id].角色.数据.道具[内容.序列] then
    玩家数据[id].角色.数据.道具[内容.序列]=nil
    玩家数据[id].道具.数据[珍珠格子]=nil
  else
    玩家数据[id].角色.数据.道具[内容.序列1]=nil
    玩家数据[id].道具.数据[珍珠格子]=nil
  end
  常规提示(id,"镶嵌装备成功")
  道具刷新(id)
end




function 装备处理类:转移宝石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 宝石格子=格子2
  local 装备格子=格子1
  if 装备格子==0 or 宝石格子==0 then
    常规提示(id,"请放入正确的人物主/副装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].总类 ~= 2 or 玩家数据[id].道具.数据[装备格子].分类 >6 then
    常规提示(id,"请放入正确的主装备！")
    return
  elseif 玩家数据[id].道具.数据[宝石格子].总类 ~= 2 or 玩家数据[id].道具.数据[宝石格子].分类 >6 then
    常规提示(id,"请放入正确的副装备！")
    return
  elseif 玩家数据[id].道具.数据[装备格子].分类 ~= 玩家数据[id].道具.数据[宝石格子].分类 then
    常规提示(id,"只有同样类型的主副装备才可相互转移！")
    return
  elseif 玩家数据[id].道具.数据[宝石格子].锻炼等级 == nil or 玩家数据[id].道具.数据[宝石格子].锻炼等级 < 1 then
    常规提示(id,"副装备没有可以转移的宝石！")
    return
  end
  local 锻造级别 = 玩家数据[id].道具.数据[宝石格子].锻炼等级
  local 消耗=玩家数据[id].道具.数据[装备格子].级别限制*锻造级别*10000
  if 取银子(id) < 消耗 then
    常规提示(id,"#Y/你没有这么多的银子！")
    return
  end

  玩家数据[id].角色:扣除银子(消耗,"宝石转移",1)
  local 打造上限 =  35
  if 锻造级别 > 打造上限 then 锻造级别 = 打造上限 end
  --清空主装备镶嵌属性
  if 玩家数据[id].道具.数据[装备格子].锻炼等级 and 玩家数据[id].道具.数据[装备格子].锻炼等级 >=1 then
    local 镶嵌类型 = {[1]={类型=玩家数据[id].道具.数据[装备格子].镶嵌类型[1],数量=1}}
    for i=2,玩家数据[id].道具.数据[装备格子].锻炼等级 do
      if 玩家数据[id].道具.数据[装备格子].镶嵌类型[i] == 镶嵌类型[1].类型 then
        镶嵌类型[1].数量 = 镶嵌类型[1].数量 +1
      else
        if 镶嵌类型[2] ~= nil then
          镶嵌类型[2].数量 = 镶嵌类型[2].数量 +1
        else
          镶嵌类型[2] = {类型=玩家数据[id].道具.数据[装备格子].镶嵌类型[i],数量=1}
        end
      end
    end
    for i=1,#镶嵌类型 do
      if 镶嵌类型[i].类型 == "太阳石" then
        玩家数据[id].道具.数据[装备格子].伤害 = math.floor(玩家数据[id].道具.数据[装备格子].伤害-镶嵌类型[i].数量*8)
      elseif 镶嵌类型[i].类型 == "红玛瑙" then
        玩家数据[id].道具.数据[装备格子].命中 = math.floor(玩家数据[id].道具.数据[装备格子].命中-镶嵌类型[i].数量*25)
      elseif 镶嵌类型[i].类型 == "月亮石" then
        玩家数据[id].道具.数据[装备格子].防御 = math.floor(玩家数据[id].道具.数据[装备格子].防御-镶嵌类型[i].数量*12)
      elseif 镶嵌类型[i].类型 == "黑宝石" then
        玩家数据[id].道具.数据[装备格子].速度 = math.floor(玩家数据[id].道具.数据[装备格子].速度-镶嵌类型[i].数量*8)
      elseif 镶嵌类型[i].类型 == "舍利子" then
        玩家数据[id].道具.数据[装备格子].灵力 = math.floor(玩家数据[id].道具.数据[装备格子].灵力-镶嵌类型[i].数量*6)
      elseif 镶嵌类型[i].类型 == "光芒石" then
        玩家数据[id].道具.数据[装备格子].气血 = math.floor(玩家数据[id].道具.数据[装备格子].气血-镶嵌类型[i].数量*40)
      elseif 镶嵌类型[i].类型 == "神秘石" then
        玩家数据[id].道具.数据[装备格子].躲避 = math.floor(玩家数据[id].道具.数据[装备格子].躲避-镶嵌类型[i].数量*20)
      end
    end
  end
  玩家数据[id].道具.数据[装备格子].锻炼等级 = nil
  玩家数据[id].道具.数据[装备格子].镶嵌宝石 = nil
  玩家数据[id].道具.数据[装备格子].镶嵌类型 = nil

  --获得副装备镶嵌属性
  镶嵌类型 = {[1]={类型=玩家数据[id].道具.数据[宝石格子].镶嵌类型[1],数量=1}}
  for i=2,玩家数据[id].道具.数据[宝石格子].锻炼等级 do
    if 玩家数据[id].道具.数据[宝石格子].镶嵌类型[i] == 镶嵌类型[1].类型 then
      镶嵌类型[1].数量 = 镶嵌类型[1].数量 +1
    else
      if 镶嵌类型[2] ~= nil then
        镶嵌类型[2].数量 = 镶嵌类型[2].数量 +1
      else
        镶嵌类型[2] = {类型=玩家数据[id].道具.数据[宝石格子].镶嵌类型[i],数量=1}
      end
    end
  end

  --将副装备镶嵌宝石转移到主装备上
  for i=1,#镶嵌类型 do
    if 镶嵌类型[i].类型 == "太阳石" then
      玩家数据[id].道具.数据[装备格子].伤害 = math.floor((玩家数据[id].道具.数据[装备格子].伤害 or 0)+镶嵌类型[i].数量*8)
    elseif 镶嵌类型[i].类型 == "红玛瑙" then
      玩家数据[id].道具.数据[装备格子].命中 = math.floor((玩家数据[id].道具.数据[装备格子].命中 or 0)+镶嵌类型[i].数量*25)
    elseif 镶嵌类型[i].类型 == "月亮石" then
      玩家数据[id].道具.数据[装备格子].防御 = math.floor((玩家数据[id].道具.数据[装备格子].防御 or 0)+镶嵌类型[i].数量*12)
    elseif 镶嵌类型[i].类型 == "黑宝石" then
      玩家数据[id].道具.数据[装备格子].速度 = math.floor((玩家数据[id].道具.数据[装备格子].速度 or 0)+镶嵌类型[i].数量*8)
    elseif 镶嵌类型[i].类型 == "舍利子" then
      玩家数据[id].道具.数据[装备格子].灵力 = math.floor((玩家数据[id].道具.数据[装备格子].灵力 or 0)+镶嵌类型[i].数量*6)
    elseif 镶嵌类型[i].类型 == "光芒石" then
      玩家数据[id].道具.数据[装备格子].气血 = math.floor((玩家数据[id].道具.数据[装备格子].气血 or 0)+镶嵌类型[i].数量*40)
    elseif 镶嵌类型[i].类型 == "神秘石" then
      玩家数据[id].道具.数据[装备格子].躲避 = math.floor((玩家数据[id].道具.数据[装备格子].躲避 or 0)+镶嵌类型[i].数量*20)
    end
  end

  玩家数据[id].道具.数据[装备格子].锻炼等级 = 玩家数据[id].道具.数据[宝石格子].锻炼等级
  玩家数据[id].道具.数据[装备格子].镶嵌宝石 = DeepCopy(玩家数据[id].道具.数据[宝石格子].镶嵌宝石)
  玩家数据[id].道具.数据[装备格子].镶嵌类型 = DeepCopy(玩家数据[id].道具.数据[宝石格子].镶嵌类型)

  --清空副装备镶嵌属性
  for i=1,#镶嵌类型 do
    if 镶嵌类型[i].类型 == "太阳石" then
      玩家数据[id].道具.数据[宝石格子].伤害 = math.floor(玩家数据[id].道具.数据[宝石格子].伤害-镶嵌类型[i].数量*8)
    elseif 镶嵌类型[i].类型 == "红玛瑙" then
      玩家数据[id].道具.数据[宝石格子].命中 = math.floor(玩家数据[id].道具.数据[宝石格子].命中-镶嵌类型[i].数量*25)
    elseif 镶嵌类型[i].类型 == "月亮石" then
      玩家数据[id].道具.数据[宝石格子].防御 = math.floor(玩家数据[id].道具.数据[宝石格子].防御-镶嵌类型[i].数量*12)
    elseif 镶嵌类型[i].类型 == "黑宝石" then
      玩家数据[id].道具.数据[宝石格子].速度 = math.floor(玩家数据[id].道具.数据[宝石格子].速度-镶嵌类型[i].数量*8)
    elseif 镶嵌类型[i].类型 == "舍利子" then
      玩家数据[id].道具.数据[宝石格子].灵力 = math.floor(玩家数据[id].道具.数据[宝石格子].灵力-镶嵌类型[i].数量*6)
    elseif 镶嵌类型[i].类型 == "光芒石" then
      玩家数据[id].道具.数据[宝石格子].气血 = math.floor(玩家数据[id].道具.数据[宝石格子].气血-镶嵌类型[i].数量*40)
    elseif 镶嵌类型[i].类型 == "神秘石" then
      玩家数据[id].道具.数据[宝石格子].躲避 = math.floor(玩家数据[id].道具.数据[宝石格子].躲避-镶嵌类型[i].数量*20)
    end
  end
  玩家数据[id].道具.数据[宝石格子].锻炼等级 = nil
  玩家数据[id].道具.数据[宝石格子].镶嵌宝石 = nil
  玩家数据[id].道具.数据[宝石格子].镶嵌类型 = nil
  常规提示(id,"宝石转移成功")
  道具刷新(id)
end





function 装备处理类:转移星辉石(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 or 格子1==0 or 格子2==0 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  elseif 玩家数据[id].道具.数据[格子1].总类 ~= 2 or 玩家数据[id].道具.数据[格子1].分类 <10 or 玩家数据[id].道具.数据[格子1].分类 > 13 then
    常规提示(id,"请放入正确的主灵饰！")
    return
  elseif 玩家数据[id].道具.数据[格子2].总类 ~= 2 or 玩家数据[id].道具.数据[格子2].分类 <10 or 玩家数据[id].道具.数据[格子2].分类 > 13 then
    常规提示(id,"请放入正确的副灵饰！")
    return
 elseif not 玩家数据[id].道具.数据[格子1].鉴定 or not 玩家数据[id].道具.数据[格子2].鉴定 then
    道具刷新(id)
    常规提示(id,"该装备还未鉴定!")
    return
  elseif not 玩家数据[id].道具.数据[格子2].幻化等级 or  玩家数据[id].道具.数据[格子2].幻化等级<=0 then
    道具刷新(id)
    常规提示(id,"副装备没有可以转移的星辉石!")
    return
  end

  local 消耗=玩家数据[id].道具.数据[格子1].级别限制*玩家数据[id].道具.数据[格子2].幻化等级*20000
  if 取银子(id) < 消耗 then
    常规提示(id,"#Y/你没有这么多的银子！")
    return
  end
  玩家数据[id].角色:扣除银子(消耗,"星辉石转移",1)
   玩家数据[id].道具.数据[格子1].幻化等级 = 玩家数据[id].道具.数据[格子2].幻化等级
  if 玩家数据[id].道具.数据[格子1].幻化等级>35 then
     玩家数据[id].道具.数据[格子1].幻化等级 = 35
  end
  玩家数据[id].道具.数据[格子2].幻化等级=0
  for n=1,#玩家数据[id].道具.数据[格子2].幻化属性.附加 do
      玩家数据[id].道具.数据[格子2].幻化属性.附加[n].强化=0
  end
  local 锻造级别= 玩家数据[id].道具.数据[格子1].幻化等级
  for n=1,#玩家数据[id].道具.数据[格子1].幻化属性.附加 do
      local 强化类型 = 玩家数据[id].道具.数据[格子1].幻化属性.附加[n].类型
      --玩家数据[id].道具.数据[格子1].幻化属性.附加[n].强化=math.floor(灵饰强化[强化类型]*锻造级别)

      玩家数据[id].道具.数据[格子1].幻化属性.附加[n].强化=取灵饰强化(强化类型,玩家数据[id].道具.数据[格子1].级别限制,锻造级别)
  end
  常规提示(id,"#Y/星辉石转移成功！")
  道具刷新(id)
end





 function 装备处理类:打造金钱公式(a)

  local 消耗 = 800000
  if a <= 20 then
    消耗 = 5000
  elseif a == 30 then
    消耗 = 10000
  elseif a == 40 then
    消耗 = 20000
  elseif a == 50 then
    消耗 = 30000
  elseif a == 60 then
    消耗 = 40000
  elseif a == 70 then
    消耗 = 80000
  elseif a == 80 then
    消耗 = 100000
  elseif a == 90 then
    消耗 = 200000
  elseif a == 100 then
    消耗 = 300000
  elseif a == 110 then
    消耗 = 400000
  elseif a == 120 then
    消耗 = 500000
  elseif a == 130 then
    消耗 = 600000
  elseif a == 140 then
    消耗 = 700000
  else
    消耗 = 800000
  end
return 消耗
end


function 装备处理类:打造人物装备处理(连接id,序号,id,内容)
   if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
    elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
      常规提示(id,"请先解锁后物品再进行此操作！")
      return
    end
    local 制造格子=0
    local 精铁格子=0
    if 玩家数据[id].道具.数据[格子1].名称=="制造指南书" and 玩家数据[id].道具.数据[格子2].名称=="百炼精铁" then
      制造格子=格子1
      精铁格子=格子2
    elseif 玩家数据[id].道具.数据[格子2].名称=="制造指南书" and 玩家数据[id].道具.数据[格子1].名称=="百炼精铁" then
      制造格子=格子2
      精铁格子=格子1
    end
    if 制造格子==0 or 精铁格子==0 then
      常规提示(id,"打造装备需要使用制造指南书和百炼精铁，你这给我的是啥玩意？？？")
      return
    end
    if 玩家数据[id].道具.数据[制造格子].子类>玩家数据[id].道具.数据[精铁格子].子类 then
      常规提示(id,"你的这块精铁等级太低了，配不上这本制造指南书")
      return
    end

    if 内容.分类 == "普通人物装备" then
      if 玩家数据[id].摊位购买打造~= nil  then
         常规提示(id,"你购买的打造还在处理中")
         return
      end
      if 玩家数据[id].角色:取任务(5)~=0 and 任务数据[玩家数据[id].角色:取任务(5)].购买~=nil then
        常规提示(id,"你购买的打造还在处理中")
         return
      end
    end

     if 内容.分类 == "强化人物装备"and 玩家数据[id].角色:取任务(5)~=0 then
        常规提示(id,"#Y/你已经领取了一个打造任务，赶快去完成吧")
        return
     end
      local 临时序列=玩家数据[id].道具.数据[制造格子].特效
      if 临时序列==25 then
        临时序列=23
      elseif 临时序列==24 then
        临时序列=22
      elseif 临时序列==23 or 临时序列==22 then
        临时序列=21
      elseif 临时序列==21 then
        临时序列=20
      elseif 临时序列==20 or 临时序列==19 then
        临时序列=19
      end

    if 玩家数据[id].摊位购买打造~= nil then
          if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].摊位购买打造.等级 then
              常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")

              return
          end

           if 临时序列 <= 18 then
                if 玩家数据[id].摊位购买打造.打造技能 ~= "打造技巧" then
                  常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/不是打造技巧,无法打造")

                  return
                end
            elseif 临时序列 == 21 or 临时序列 == 19 then
                if 玩家数据[id].摊位购买打造.打造技能 ~= "裁缝技巧" then
                  常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/不是裁缝技巧,无法打造")

                  return
                end
            elseif 临时序列 == 20 or 临时序列 == 22 or 临时序列 == 23 then
                if 玩家数据[id].摊位购买打造.打造技能 ~= "炼金术" then
                  常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/不是炼金术,无法打造")

                  return
                end
            else
                常规提示(id,"数据异常")
                return
            end

            玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 -玩家数据[id].摊位购买打造.费用
            常规提示(id,"你失去了#R/"..玩家数据[id].摊位购买打造.费用.."#Y/银子")
            if 玩家数据[玩家数据[id].摊位购买打造.对方id] ~= nil then
                玩家数据[玩家数据[id].摊位购买打造.对方id].角色.数据.银子 = 玩家数据[玩家数据[id].摊位购买打造.对方id].角色.数据.银子 + 玩家数据[id].摊位购买打造.费用
                常规提示(玩家数据[id].摊位购买打造.对方id,"你出售的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/获得了#R/"..玩家数据[id].摊位购买打造.费用.."#Y/银子")
            end
    else
             if 临时序列 <= 18 then
                if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].角色:取生活技能等级("打造技巧") then
                    常规提示(id,"你的#R/打造技巧#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")
                  return
                end

            elseif 临时序列 == 21 or 临时序列 == 19 then
                if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].角色:取生活技能等级("裁缝技巧") then
                    常规提示(id,"你的#R/裁缝技巧#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")
                  return
                end

            elseif 临时序列 == 20 or 临时序列 == 22 or 临时序列 == 23 then
                if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].角色:取生活技能等级("炼金术") then
                    常规提示(id,"你的#R/炼金术#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")
                   return
                end
            else
                  常规提示(id,"数据异常")
                  return
            end
            local  扣除体力 = (玩家数据[id].道具.数据[制造格子].子类 - 10)*2+50
            if 玩家数据[id].角色.数据.体力 < 扣除体力 then
              常规提示(id,"你好像没有这么多的体力")
              return
            end
            local 扣除金钱 = self:打造金钱公式(玩家数据[id].道具.数据[制造格子].子类)
            if 取银子(id) < 扣除金钱 then
              常规提示(id,"#Y你银子不够打造装备哦！")
               return
            end
          玩家数据[id].角色.数据.体力 = 玩家数据[id].角色.数据.体力 - 扣除体力
          玩家数据[id].角色:扣除银子(math.abs(扣除金钱),"打造装备",1)
          体活刷新(id)
    end


    local 临时等级=玩家数据[id].道具.数据[制造格子].子类/10
    -- 计算武器值
      if 临时序列<=18 and 临时等级>=9 then --是武器 10-12是普通光武
        if 临时等级<=11 then
          临时等级=取随机数(9,11)
        elseif 临时等级<=14 then
          临时等级=取随机数(12,14)
        end
        临时等级=临时等级+1
      else
        临时等级=临时等级+1
      end
    local 临时类型=self.打造物品[临时序列][临时等级]
    if type(临时类型)=="table" then
      if 玩家数据[id].道具.数据[制造格子].特效 ==23 then
       临时类型=临时类型[2]
      elseif 玩家数据[id].道具.数据[制造格子].特效 ==22 then
       临时类型=临时类型[1]
      elseif 玩家数据[id].道具.数据[制造格子].特效 ==20 then
       临时类型=临时类型[2]
      elseif 玩家数据[id].道具.数据[制造格子].特效 ==19 then
       临时类型=临时类型[1]
      else
        临时类型=临时类型[取随机数(1,2)]
      end
    end

     if 内容.分类 == "普通人物装备" then
          if 临时序列 <= 18 then
              self:添加普通打造装备(id,制造格子,临时序列,临时类型,玩家数据[id].角色.数据.打造熟练度.打造技巧)
              local 添加熟练度 = 取随机数(1,10)
              玩家数据[id].角色.数据.打造熟练度.打造技巧 = 玩家数据[id].角色.数据.打造熟练度.打造技巧 + 添加熟练度
              常规提示(id,"你的#R/打造技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          elseif 临时序列 == 21 or 临时序列 == 19 then
                  self:添加普通打造装备(id,制造格子,临时序列,临时类型,玩家数据[id].角色.数据.打造熟练度.裁缝技巧)
                  local 添加熟练度 = 取随机数(1,10)
                  玩家数据[id].角色.数据.打造熟练度.裁缝技巧 = 玩家数据[id].角色.数据.打造熟练度.裁缝技巧 + 添加熟练度
                  常规提示(id,"你的#R/裁缝技巧#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          elseif 临时序列 == 20 or 临时序列 == 22 or 临时序列 == 23 then
                  self:添加普通打造装备(id,制造格子,临时序列,临时类型,玩家数据[id].角色.数据.打造熟练度.炼金术)
                  local 添加熟练度 = 取随机数(1,10)
                  玩家数据[id].角色.数据.打造熟练度.炼金术 = 玩家数据[id].角色.数据.打造熟练度.炼金术 + 添加熟练度
                  常规提示(id,"你的#R/炼金术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")

          end


      玩家数据[id].道具.数据[精铁格子]=nil
      if 精铁格子==玩家数据[id].角色.数据.道具[内容.序列] then
        玩家数据[id].角色.数据.道具[内容.序列]=nil
      else
        玩家数据[id].角色.数据.道具[内容.序列1]=nil
      end
      常规提示(id,"制造装备成功")
      道具刷新(id)
    else
      任务处理类:设置打造装备任务(id,临时类型,玩家数据[id].道具.数据[制造格子].子类,临时序列,玩家数据[id].摊位购买打造)
      玩家数据[id].摊位购买打造=nil
      玩家数据[id].道具.数据[制造格子]=nil
      玩家数据[id].道具.数据[精铁格子]=nil
      if 精铁格子==玩家数据[id].角色.数据.道具[内容.序列] then
        玩家数据[id].角色.数据.道具[内容.序列]=nil
      else
        玩家数据[id].角色.数据.道具[内容.序列1]=nil
      end
      道具刷新(id)
    end
end



function 装备处理类:打造灵饰处理(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end

  local 制造格子=0
  local 精铁格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="灵饰指南书" and 玩家数据[id].道具.数据[格子2].名称=="元灵晶石" then
    制造格子=格子1
    精铁格子=格子2
  elseif 玩家数据[id].道具.数据[格子2].名称=="灵饰指南书" and 玩家数据[id].道具.数据[格子1].名称=="元灵晶石" then
    制造格子=格子2
    精铁格子=格子1
  end

    if 制造格子==0 or 精铁格子==0 then
      常规提示(id,"打造装备需要使用灵饰指南书和元灵晶石，你这给我的是啥玩意？？？")
      return
    end
    if 玩家数据[id].道具.数据[制造格子].子类>玩家数据[id].道具.数据[精铁格子].子类 then
      常规提示(id,"你的这块元灵晶石等级太低了，配不上这本制造指南书")
      return
    end


     if 内容.分类 == "灵饰淬灵" then
      if 玩家数据[id].摊位购买打造~= nil  then
         常规提示(id,"你购买的打造还在处理中")
         return
      end
      if 玩家数据[id].角色:取任务(5)~=0 and 任务数据[玩家数据[id].角色:取任务(5)].购买~=nil then
        常规提示(id,"你购买的打造还在处理中")
         return
      end
    end


     if 内容.分类 == "强化灵饰淬灵" and 玩家数据[id].角色:取任务(5)~=0 then
        常规提示(id,"#Y/你已经领取了一个打造任务，赶快去完成吧")
         return
      end

       local 临时名称=制造装备[玩家数据[id].道具.数据[制造格子].特效][玩家数据[id].道具.数据[制造格子].子类]
    if 玩家数据[id].摊位购买打造~= nil then
      if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].摊位购买打造.等级 then
        常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")

        return
      end
      if 玩家数据[id].摊位购买打造.打造技能 ~= "淬灵之术" then
        常规提示(id,"你购买的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/不是淬灵之术,无法打造")

        return
      end
      玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 -玩家数据[id].摊位购买打造.费用
      常规提示(id,"你失去了#R/"..玩家数据[id].摊位购买打造.费用.."#Y/银子")
      if 玩家数据[玩家数据[id].摊位购买打造.对方id] ~= nil then
        玩家数据[玩家数据[id].摊位购买打造.对方id].角色.数据.银子 = 玩家数据[玩家数据[id].摊位购买打造.对方id].角色.数据.银子 + 玩家数据[id].摊位购买打造.费用
        常规提示(玩家数据[id].摊位购买打造.对方id,"你出售的#R/"..玩家数据[id].摊位购买打造.打造技能.."#Y/获得了#R/"..玩家数据[id].摊位购买打造.费用.."#Y/银子")
      end
    else
         if 玩家数据[id].道具.数据[制造格子].子类 > 玩家数据[id].角色:取生活技能等级("淬灵之术") then
          常规提示(id,"你的#R/淬灵之术#Y/等级不够#R/"..玩家数据[id].道具.数据[制造格子].子类.."级#Y/无法打造")
          return
        end
        local 扣除体力 = (玩家数据[id].道具.数据[制造格子].子类 - 10)*2+50
        if 玩家数据[id].角色.数据.体力 < 扣除体力 then
            常规提示(id,"你好像没有这么多的体力")
            return
        end
        local 扣除金钱 = self:打造金钱公式(玩家数据[id].道具.数据[制造格子].子类)
        if 取银子(id) < 扣除金钱 then
          常规提示(id,"#Y你银子不够打造装备哦！")
          return
        end
        玩家数据[id].角色.数据.体力 = 玩家数据[id].角色.数据.体力 - 扣除体力
        玩家数据[id].角色:扣除银子(math.abs(扣除金钱),"打造装备",1)
        体活刷新(id)

    end

    if 内容.分类 == "灵饰淬灵" then
          local 道具 = 物品类()
          道具:置对象(临时名称)
          道具.级别限制 = 玩家数据[id].道具.数据[制造格子].子类
          道具.幻化等级=0
          local 临时类型=玩家数据[id].道具.数据[制造格子].特效
          道具.制造者 = 玩家数据[id].角色.数据.名称
          道具.灵饰=true
          道具.幻化属性={附加={},}
          道具.识别码=取唯一识别码(id)
          local 主属性 = 灵饰属性[临时类型].主属性[取随机数(1,#灵饰属性[临时类型].主属性)]
          local 主数值=取随机数(灵饰属性.基础[主属性][道具.级别限制].a,灵饰属性.基础[主属性][道具.级别限制].b)
          道具.幻化属性.基础={类型=主属性,数值=主数值,强化=0}
          for n=1,取随机数(1,3) do
                local 副属性=灵饰属性[临时类型].副属性[取随机数(1,#灵饰属性[临时类型].副属性)]
                local 副数值=math.floor(取随机数(灵饰属性.基础[副属性][道具.级别限制].a,灵饰属性.基础[副属性][道具.级别限制].b))
                道具.幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
          end
          玩家数据[id].道具.数据[制造格子]=nil
          玩家数据[id].道具.数据[制造格子]=道具
          玩家数据[id].道具.数据[制造格子].五行=取五行()
          玩家数据[id].道具.数据[制造格子].耐久度=500
          玩家数据[id].道具.数据[制造格子].部位类型=临时类型
          玩家数据[id].道具.数据[制造格子].鉴定=false
          local 添加熟练度 = 取随机数(1,10)
          玩家数据[id].角色.数据.打造熟练度.淬灵之术 = 玩家数据[id].角色.数据.打造熟练度.淬灵之术 + 添加熟练度
          常规提示(id,"你的#R/淬灵之术#Y/熟练度增加了#R/"..添加熟练度.."#Y/点")
          玩家数据[id].道具.数据[精铁格子]=nil
          if 精铁格子==玩家数据[id].角色.数据.道具[内容.序列] then
            玩家数据[id].角色.数据.道具[内容.序列]=nil
          else
            玩家数据[id].角色.数据.道具[内容.序列1]=nil
          end
          常规提示(id,"制造灵饰成功")
          道具刷新(id)
    else
          任务处理类:设置打造灵饰任务(id,临时名称,玩家数据[id].道具.数据[制造格子].子类,玩家数据[id].道具.数据[制造格子].特效,玩家数据[id].摊位购买打造)
          玩家数据[id].摊位购买打造=nil
          玩家数据[id].道具.数据[精铁格子]=nil
          玩家数据[id].道具.数据[制造格子]=nil
          玩家数据[id].角色.数据.道具[内容.序列1]=nil
          玩家数据[id].角色.数据.道具[内容.序列]=nil
          道具刷新(id)
   end


end




function 装备处理类:打造召唤兽装备处理(连接id,序号,id,内容)
  if not 内容.序列 or not 内容.序列1 then return end
  local 格子1=玩家数据[id].角色.数据.道具[内容.序列]
  local 格子2=玩家数据[id].角色.数据.道具[内容.序列1]
  if not 格子1 or not 格子2 then return end
  if 玩家数据[id].道具.数据[格子1]==nil or 玩家数据[id].道具.数据[格子2]==nil then
      道具刷新(id)
      return
  elseif 玩家数据[id].道具.数据[格子1]==0 or 玩家数据[id].道具.数据[格子2]==0 then
        道具刷新(id)
        return
  elseif 玩家数据[id].道具.数据[格子1].加锁 or 玩家数据[id].道具.数据[格子2].加锁 then
    常规提示(id,"请先解锁后物品再进行此操作！")
    return
  end
  local 制造格子=0
  local 石头格子=0
  if 玩家数据[id].道具.数据[格子1].名称=="上古锻造图策" and (玩家数据[id].道具.数据[格子2].名称=="天眼珠" or 玩家数据[id].道具.数据[格子2].名称=="三眼天珠" or 玩家数据[id].道具.数据[格子2].名称=="九眼天珠") then
    制造格子=格子1
    石头格子=格子2
  elseif 玩家数据[id].道具.数据[格子2].名称=="上古锻造图策" and (玩家数据[id].道具.数据[格子1].名称=="天眼珠" or 玩家数据[id].道具.数据[格子1].名称=="三眼天珠" or 玩家数据[id].道具.数据[格子1].名称=="九眼天珠") then
    制造格子=格子2
    石头格子=格子1
  end
  if 制造格子==0 or 石头格子==0 then
    常规提示(id,"打造装备需要使用上古锻造图册和天眼珠，你这给我的是啥玩意？？？")
    return
  elseif 玩家数据[id].道具.数据[制造格子].级别限制>玩家数据[id].道具.数据[石头格子].级别限制 then
    常规提示(id,"你的这块天眼珠等级太低了，配不上这本上古锻造图册")
    return
  end

  local 消耗体力 = 玩家数据[id].道具.数据[石头格子].级别限制-20
  local 消耗银子  = math.abs((玩家数据[id].道具.数据[石头格子].级别限制-65)*400+8950)
  if 玩家数据[id].角色.数据.体力 < 消耗体力 then
    常规提示(id,"你好像没有这么多的体力")
    return
  elseif 取银子(id) < 消耗银子 then
    return
  end
  玩家数据[id].角色.数据.体力 =玩家数据[id].角色.数据.体力 - 消耗体力
  玩家数据[id].角色:扣除银子(消耗银子,"打造装备",1)
  体活刷新(id)

    local 类型序列=0
    if 玩家数据[id].道具.数据[制造格子].种类=="护腕" then
       类型序列=24
    elseif 玩家数据[id].道具.数据[制造格子].种类=="项圈" then
       类型序列=25
    elseif 玩家数据[id].道具.数据[制造格子].种类=="铠甲" then
       类型序列=26
    end

    local 临时序列=math.floor((玩家数据[id].道具.数据[制造格子].级别限制+5)/10)
    local 临时名称=self.打造物品[类型序列][临时序列]
    local 道具 = 物品类()
    道具:置对象(临时名称)
    道具.级别限制 = 玩家数据[id].道具.数据[制造格子].级别限制
    local 灵气=玩家数据[id].道具.数据[石头格子].灵气
    if 灵气==nil then
         灵气=取随机数(20,100)
    end
    local 等级=道具.级别限制
    if 玩家数据[id].道具.数据[制造格子].种类=="护腕" then
          道具.命中=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
    elseif 玩家数据[id].道具.数据[制造格子].种类=="项圈" then
          道具.速度=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
    else
          道具.防御=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
    end


       local 附加范围={"伤害","灵力","敏捷","耐力","体质","力量","魔力","气血","魔法"}


       for n=1,9 do
          local 类型=附加范围[n]
          if 取随机数()<=5 then
                 if 道具[类型]==nil then
                        if 类型=="伤害" then
                        道具[类型]=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
                        elseif 类型=="灵力" then
                        道具[类型]=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
                        elseif 类型=="敏捷"  or 类型=="体质" or 类型=="力量" or 类型=="耐力" or 类型=="魔力"  then
                        道具[类型]=math.floor(取随机数(等级/20+2,等级/20+10)+(取随机数(灵气/40,灵气/20)))
                        elseif 类型=="气血" then
                        道具[类型]=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
                        elseif 类型=="魔法" then
                        道具[类型]=math.floor(取随机数(等级*0.25+5,等级+10)+(取随机数(灵气*0.15,灵气*0.5)))
                        end
                  end
            end
        end
      if 道具.第二特效==nil and 取随机数()<=1 then
         道具.第二特效="无级别限制"
      end
      道具.制造者 = 玩家数据[id].角色.数据.名称
      道具.五行=取五行()
      道具.耐久度=math.floor(500)
      道具.鉴定=true
      道具.识别码=取唯一识别码(id)
    玩家数据[id].道具.数据[制造格子]=道具
    玩家数据[id].道具.数据[石头格子]=nil
    if 石头格子==玩家数据[id].角色.数据.道具[内容.序列] then
      玩家数据[id].角色.数据.道具[内容.序列]=nil
    else
      玩家数据[id].角色.数据.道具[内容.序列1]=nil
    end
    常规提示(id,"制造装备成功")
    道具刷新(id)


end





function 装备处理类:添加强化打造灵饰(id,任务id,熟练度)
local 道具 = 物品类()
      道具:置对象(任务数据[任务id].名称)
      道具.级别限制 = 任务数据[任务id].级别
      道具.幻化等级=0
      local 制造格子=玩家数据[id].道具:取新编号()
      local 临时类型=任务数据[任务id].部位
      道具.幻化属性={附加={},}
      道具.识别码=取唯一识别码(id)
      local 主属性 = 灵饰属性[临时类型].主属性[取随机数(1,#灵饰属性[临时类型].主属性)]
      local 临时下限 =灵饰属性.基础[主属性][道具.级别限制].a+道具.级别限制/20
      local 临时上限 =灵饰属性.基础[主属性][道具.级别限制].b+道具.级别限制/10
      local 主数值=取随机数(临时下限,临时上限)
      主数值=math.floor(主数值*1.1)
      道具.幻化属性.基础={类型=主属性,数值=主数值,强化=0}
      local 数量上限 =取随机数(1,3)
      local 特效几率 = 5
      if 熟练度~=nil then
        if 熟练度 >=5000 and 熟练度<10000 then
          特效几率 = 8
        elseif 熟练度 >=10000 and 熟练度<25000 then
          特效几率 = 11
        elseif 熟练度 >=25000 and 熟练度<50000 then
          数量上限 =取随机数(2,3)
          特效几率 = 14
        elseif 熟练度 >=50000 and 熟练度<80000 then
          数量上限 =取随机数(2,4)
          特效几率 = 17
        elseif 熟练度 >=80000  then
          数量上限 =取随机数(3,4)
          特效几率 = 20
        end
      end
      if 取随机数(1,100)<= 5 then
          数量上限 = 4
      end
      for n=1,数量上限 do
            local 副属性=灵饰属性[临时类型].副属性[取随机数(1,#灵饰属性[临时类型].副属性)]
            local 下限 = 灵饰属性.基础[副属性][道具.级别限制].a+道具.级别限制/20
            local 上限 = 灵饰属性.基础[副属性][道具.级别限制].b+道具.级别限制/10
            local 副数值=math.floor(取随机数(下限,上限))
            道具.幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
      end

      道具.制造者 = 玩家数据[id].角色.数据.名称.."强化打造"
      if 任务数据[任务id].购买~=nil then
        道具.制造者 = 任务数据[任务id].购买.对方名称.."强化打造"
      end
      道具.灵饰=true
      local 通用特效 = {"无级别限制","简易","超级简易"}
      if 取随机数()<= 特效几率 then
          道具.特效 = 通用特效[取随机数(1,#通用特效)]
      end
      玩家数据[id].道具.数据[制造格子]=道具
      玩家数据[id].道具.数据[制造格子].五行=取五行()
      玩家数据[id].道具.数据[制造格子].耐久度=500
      玩家数据[id].道具.数据[制造格子].部位类型=临时类型
      玩家数据[id].道具.数据[制造格子].鉴定=false
      local 道具格子 = 玩家数据[id].角色:取道具格子()
      玩家数据[id].角色.数据.道具[道具格子]=制造格子
      常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[制造格子].名称)
end




function 装备处理类:添加普通打造装备(id,制造格子,临时序列,临时类型,熟练度)
  local 道具 = 物品类()
      道具:置对象(临时类型)
      道具.级别限制 = 玩家数据[id].道具.数据[制造格子].子类
      local 额外几率 = 0
      if 熟练度~= nil then
          if 熟练度 >= 5000 and 熟练度<10000 then
            额外几率 = 3
          elseif 熟练度 >= 10000 and 熟练度<25000 then
            额外几率 = 6
          elseif 熟练度 >= 25000 and 熟练度<50000 then
            额外几率 = 9
          elseif 熟练度 >= 50000 and 熟练度<80000 then
             额外几率 = 12
          elseif 熟练度 >= 80000  then
             额外几率 = 15
          end
      end
      if 临时序列 < 19 then -- 武器
          道具.命中 = 取随机数(打造属性.命中[道具.级别限制][1],打造属性.命中[道具.级别限制][2])
          道具.伤害 = 取随机数(打造属性.伤害[道具.级别限制][1],打造属性.伤害[道具.级别限制][2])
          if 取随机数(1,100) <= 10+额外几率 then
                local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
                local sx1 = 取随机数(1,#额外属性)
                local 属性1  = 额外属性[sx1]
                道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                if 取随机数(1,100) <= 5 + 额外几率 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                end
          end
      elseif 临时序列 == 19 then -- 帽子
          道具.防御 =  取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
          道具.魔法 =  取随机数(打造属性.魔法[道具.级别限制][1],打造属性.魔法[道具.级别限制][2])
      elseif 临时序列 == 20 then -- 项链
          道具.灵力 = 取随机数(打造属性.灵力[道具.级别限制][1],打造属性.灵力[道具.级别限制][2])
      elseif 临时序列 == 21 then -- 衣服
          道具.防御 = 取随机数(打造属性.防御.衣服[道具.级别限制][1],打造属性.防御.衣服[道具.级别限制][2])
          if 取随机数(1,100) <= 10+额外几率 then
                local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
                local sx1 = 取随机数(1,#额外属性)
                local 属性1  = 额外属性[sx1]
                道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                if 取随机数(1,100) <= 5+ 额外几率 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                end
          end
      elseif 临时序列 == 22 then -- 腰带
          道具.防御 = 取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
          道具.气血 = 取随机数(打造属性.气血[道具.级别限制][1],打造属性.气血[道具.级别限制][2])
      elseif 临时序列 == 23 then -- 鞋子
        道具.防御 = 取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
        道具.敏捷 = 取随机数(打造属性.敏捷[道具.级别限制][1],打造属性.敏捷[道具.级别限制][2])
      end
      local 特殊几率=5
      if 熟练度>=5000 and 熟练度<10000 then
        特殊几率 = 8
      elseif 熟练度>=10000 and 熟练度<25000  then
        特殊几率 = 11
      elseif 熟练度>=25000 and 熟练度<50000  then
        特殊几率 = 14
      elseif 熟练度>=50000 and 熟练度<80000  then
        特殊几率 = 17
      elseif 熟练度>=80000 then
        特殊几率 = 20
      end
      local 通用特效 = {"无级别限制","神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生","易修理","超级简易"}
      if 道具.分类 == 5 then
            table.insert(通用特效,"愤怒")
            table.insert(通用特效,"暴怒")
      end
      if 取随机数()<=特殊几率 then
          道具.特效 = 通用特效[取随机数(1,#通用特效)]
      end
      local 通用特技 = {"气疗术","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒",
        "起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼",
        "碎甲术","破甲术","破血狂攻","慈航普渡","笑里藏刀","罗汉金钟","破碎无双","圣灵之甲","野兽之力"
        ,"琴音三叠","菩提心佑","先发制人","身似菩提"}

      if 取随机数()<= 特殊几率 then
            道具.特技 = 通用特技[取随机数(1,#通用特技)]
      end
      道具.制造者 = 玩家数据[id].角色.数据.名称
      玩家数据[id].道具.数据[制造格子]=nil
      玩家数据[id].道具.数据[制造格子]=道具
      玩家数据[id].道具.数据[制造格子].五行=取五行()
      玩家数据[id].道具.数据[制造格子].耐久度=math.floor(500)
      玩家数据[id].道具.数据[制造格子].识别码=取唯一识别码(id)
      玩家数据[id].道具.数据[制造格子].鉴定=false



end

function 装备处理类:添加强化打造装备(id,任务id,特殊几率,熟练度)

  local 道具 = 物品类()
  道具:置对象(任务数据[任务id].名称)
  道具.级别限制 = 任务数据[任务id].级别
  local 额外几率 = 0
  if 熟练度~= nil then
      if 熟练度 >= 5000 and 熟练度<10000 then
        额外几率 = 3
      elseif 熟练度 >= 10000 and 熟练度<25000 then
        额外几率 = 6
      elseif 熟练度 >= 25000 and 熟练度<50000 then
        额外几率 = 9
      elseif 熟练度 >= 50000 and 熟练度<80000 then
         额外几率 = 12
      elseif 熟练度 >= 80000  then
         额外几率 = 15
      end
  end

  if 玩家数据[id].角色.数据.打造加成~=nil and 玩家数据[id].角色.数据.打造加成.双加~=nil and 玩家数据[id].角色.数据.打造加成.双加>0 and not 双加 and (任务数据[任务id].序列 < 19 or 任务数据[任务id].序列 == 21) then
      额外几率 = 额外几率 + 玩家数据[id].角色.数据.打造加成.双加
      local 显示几率= 5 + 额外几率
      常规提示(id,"#Y/扣除额外双加几率#R/"..玩家数据[id].角色.数据.打造加成.双加.."%#Y/,当前双加几率#R/"..显示几率.."%")
      玩家数据[id].角色.数据.打造加成.双加 = 0
  end
  if 任务数据[任务id].序列 < 19 then -- 武器
      道具.命中 = math.floor(取随机数(道具.级别限制*4+10,道具.级别限制*5.36+15))
      道具.伤害 = math.floor(取随机数(道具.级别限制*3.5+10,道具.级别限制*4.68+14))
      if 取随机数(1,100) <= 10+额外几率 then
          local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
          local sx1 = 取随机数(1,#额外属性)
          local 属性1  = 额外属性[sx1]
          道具[属性1] = math.floor(取随机数(道具.级别限制*0.15,道具.级别限制*0.4))
          if 取随机数(1,100) <= 5 + 额外几率 then
              table.remove(额外属性,sx1)
              local 属性2 = 额外属性[取随机数(1,#额外属性)]
              道具[属性2] = math.floor(取随机数(道具.级别限制*0.15,道具.级别限制*0.4))
          end
      end
  elseif 任务数据[任务id].序列 == 19 then -- 帽子
          道具.防御 = math.floor(取随机数(道具.级别限制*0.86+6,道具.级别限制*1.1+11))
          道具.魔法 =  math.floor(取随机数(道具.级别限制*1.5+5,道具.级别限制*1.85+8))
  elseif 任务数据[任务id].序列 == 20 then -- 项链
            道具.灵力 =  math.floor(取随机数(道具.级别限制*1.65+6,道具.级别限制*2.45+7))
  elseif 任务数据[任务id].序列 == 21 then -- 衣服
            道具.防御 = math.floor(取随机数(道具.级别限制*1.97+10,道具.级别限制*2.65+13))
            if 取随机数(1,100) <= 10+额外几率 then
                local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
                local sx1 = 取随机数(1,#额外属性)
                local 属性1  = 额外属性[sx1]
                道具[属性1] = math.floor(取随机数(道具.级别限制*0.15,道具.级别限制*0.4))
                if 取随机数(1,100) <= 5 + 额外几率 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.15,道具.级别限制*0.4))
                end
            end
  elseif 任务数据[任务id].序列 == 22 then -- 腰带
          道具.防御 = math.floor(取随机数(道具.级别限制*0.86+6,道具.级别限制*1.1+11))
          道具.气血 = math.floor(取随机数(道具.级别限制*3.6+10,道具.级别限制*4.85+17))
  elseif 任务数据[任务id].序列 == 23 then -- 鞋子
          道具.防御 = math.floor(取随机数(道具.级别限制*0.86+6,道具.级别限制*1.1+11))
          道具.敏捷 = math.floor(取随机数(道具.级别限制*0.55+7,道具.级别限制*0.8+7))
  end
  local 特技出现几率 = 特殊几率
  local 特效出现几率 = 特殊几率


  if 玩家数据[id].角色.数据.打造加成~=nil and 玩家数据[id].角色.数据.打造加成.特技~=nil and 玩家数据[id].角色.数据.打造加成.特技>0 and not 双加 then
     特技出现几率 = 特技出现几率 + 玩家数据[id].角色.数据.打造加成.特技
     常规提示(id,"#Y/扣除额外特技几率#R/"..玩家数据[id].角色.数据.打造加成.特技.."%#Y/,当前特技几率#R/"..特技出现几率.."%")
     玩家数据[id].角色.数据.打造加成.特技 = 0
  end
  if 玩家数据[id].角色.数据.打造加成~=nil and 玩家数据[id].角色.数据.打造加成.特效~=nil and 玩家数据[id].角色.数据.打造加成.特效>0 and not 双加 then
     特效出现几率 = 特效出现几率 + 玩家数据[id].角色.数据.打造加成.特效
     常规提示(id,"#Y/扣除额外特效几率#R/"..玩家数据[id].角色.数据.打造加成.特效.."%#Y/,当前特效几率#R/"..特效出现几率.."%")
     玩家数据[id].角色.数据.打造加成.特效 = 0
  end


    local 通用特效 = {"无级别限制","神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生","易修理","超级简易"}
    if 道具.分类 == 5 then
        table.insert(通用特效,"愤怒")
        table.insert(通用特效,"暴怒")
     end
    if 取随机数()<=特效出现几率  then
      道具.特效 = 通用特效[取随机数(1,#通用特效)]
    end


    --local 通用特技 = {"气疗术","菩提心佑","先发制人","身似菩提","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒","起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼","碎甲术","破甲术","破血狂攻"}
    local 通用特技 = {"气疗术","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒",
    "起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼",
    "碎甲术","破甲术","破血狂攻","慈航普渡","笑里藏刀","罗汉金钟","破碎无双","圣灵之甲","野兽之力"
    ,"琴音三叠","菩提心佑","先发制人","身似菩提"}

    if 取随机数()<= 特技出现几率 then
        道具.特技 = 通用特技[取随机数(1,#通用特技)]
    end
    local 仙人赐福几率=5
    if 玩家数据[id].角色.数据.打造加成~=nil and 玩家数据[id].角色.数据.打造加成.赐福~=nil and 玩家数据[id].角色.数据.打造加成.赐福>0 and not 双加 and 道具.级别限制>=160 then
       仙人赐福几率 = 仙人赐福几率 + 玩家数据[id].角色.数据.打造加成.赐福
       常规提示(id,"#Y/扣除额外赐福几率#R/"..玩家数据[id].角色.数据.打造加成.赐福.."%#Y/,当前赐福几率#R/"..仙人赐福几率.."%")
       玩家数据[id].角色.数据.打造加成.赐福 = 0
    end

    if 取随机数()<= 仙人赐福几率 and 道具.级别限制>=160 then
          道具.赐福={总类="",类型="",数值=0}
          local 随机总类={"基础","战斗"}
          道具.赐福.总类=随机总类[取随机数(1,2)]
          if 道具.赐福.总类=="基础" then
             local 随机类型 ={"气血","魔法","命中","伤害","防御","速度","躲避","灵力","体质","魔力","力量","耐力","敏捷",
                   "气血回复效果","抗法术暴击等级","格挡值","法术防御","抗物理暴击等级","封印命中等级","穿刺等级",
                    "抵抗封印等级","固定伤害","法术伤害","法术暴击等级","物理暴击等级","狂暴等级","法术伤害结果",
                    "治疗能力"}
             道具.赐福.类型= 随机类型[取随机数(1,#随机类型)]
             if 道具.赐福.类型=="体质" or 道具.赐福.类型=="魔力" or 道具.赐福.类型=="力量" or 道具.赐福.类型=="耐力" or 道具.赐福.类型=="敏捷" then
                  道具.赐福.数值=取随机数(5,20)
              elseif 道具.赐福.类型=="气血" or 道具.赐福.类型=="魔法" then
                  道具.赐福.数值=取随机数(50,200)
              elseif 道具.赐福.类型=="命中" or 道具.赐福.类型=="伤害" or 道具.赐福.类型=="防御" or 道具.赐福.类型=="速度" or 道具.赐福.类型=="躲避" or 道具.赐福.类型=="灵力" then
                  道具.赐福.数值=取随机数(20,50)
              else
                  道具.赐福.数值=取随机数(10,25)
              end
          else
             local 随机类型 ={"伤害结果","法伤结果","物伤结果","固伤结果","治疗结果","伤害减免","物伤减免","法伤减免","固伤减免","技能连击"}
             道具.赐福.类型= 随机类型[取随机数(1,#随机类型)]
             道具.赐福.数值=取随机数(1,3)
          end
    end

  -- 为装备赋予附加属性
  道具.制造者 = 玩家数据[id].角色.数据.名称.."强化打造"
  -- 生产附加
  -- if 道具.分类==
  if 任务数据[任务id].购买~=nil then
    道具.制造者 = 任务数据[任务id].购买.对方名称.."强化打造"
  end
  local 制造格子=玩家数据[id].道具:取新编号()
  玩家数据[id].道具.数据[制造格子]=道具
  玩家数据[id].道具.数据[制造格子].五行=取五行()
  玩家数据[id].道具.数据[制造格子].耐久度=取随机数(500,700)
  玩家数据[id].道具.数据[制造格子].识别码=取唯一识别码(id)
  玩家数据[id].道具.数据[制造格子].鉴定=false
  local 道具格子 = 玩家数据[id].角色:取道具格子()
  玩家数据[id].角色.数据.道具[道具格子]=制造格子
  常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[制造格子].名称)
end



function 装备处理类:生成指定装备(id,名称,级别,序列,公式)
  local 道具 = 物品类()
  道具:置对象(名称)
  道具.级别限制 = 级别
  if 序列 < 19 then -- 武器
          if 公式 then
              道具.命中=math.floor(取随机数(打造属性.命中[级别][1]*1.04,打造属性.命中[级别][2]*1.04))
              道具.伤害=math.floor(取随机数(打造属性.伤害[级别][1]*1.08,打造属性.伤害[级别][2]*1.08))
          else
              道具.命中 = 取随机数(打造属性.命中[级别][1],打造属性.命中[级别][2])
              道具.伤害 = 取随机数(打造属性.伤害[级别][1],打造属性.伤害[级别][2])
          end
          if 取随机数(1,100) <= 10 or 公式 then
                local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
                local sx1 = 取随机数(1,#额外属性)
                local 属性1  = 额外属性[sx1]
                道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                if 取随机数(1,100) <= 5 or 公式 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                end
          end
  elseif 序列 == 19 then -- 帽子
          if 公式 then
              道具.防御=math.floor(取随机数(打造属性.防御[级别][1]*1.12,打造属性.防御[级别][2]*1.12))
              道具.魔法=math.floor(取随机数(打造属性.魔法[级别][1]*1.1,打造属性.魔法[级别][2]*1.1))
          else
              道具.防御 = 取随机数(打造属性.防御[级别][1],打造属性.防御[级别][2])
              道具.魔法 = 取随机数(打造属性.魔法[级别][1],打造属性.魔法[级别][2])
          end

  elseif 序列 == 20 then -- 项链
          if 公式 then
              道具.灵力=math.floor(取随机数(打造属性.灵力[级别][1]*1.2,打造属性.灵力[级别][2]*1.2))
          else
              道具.灵力 = 取随机数(打造属性.灵力[级别][1],打造属性.灵力[级别][2])
          end

  elseif 序列 == 21 then -- 衣服
          if 公式 then
              道具.防御=math.floor(取随机数(打造属性.防御.衣服[级别][1]*1.12,打造属性.防御.衣服[级别][2]*1.12))
          else
              道具.防御 = 取随机数(打造属性.防御.衣服[级别][1],打造属性.防御.衣服[级别][2])
          end
          if 取随机数(1,100) <= 10 or 公式 then
              local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
              local sx1 = 取随机数(1,#额外属性)
              local 属性1  = 额外属性[sx1]
              道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
              if 取随机数(1,100) <= 5 or 公式 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
              end
          end
  elseif 序列 == 22 then -- 腰带
          if 公式 then
              道具.防御=math.floor(取随机数(打造属性.防御[级别][1]*1.2,打造属性.防御[级别][2]*1.2))
              道具.气血=math.floor(取随机数(打造属性.气血[级别][1]*1.3,打造属性.气血[级别][2]*1.3))
          else
              道具.防御 = 取随机数(打造属性.防御[级别][1],打造属性.防御[级别][2])
              道具.气血 = 取随机数(打造属性.气血[级别][1],打造属性.气血[级别][2])
          end
  elseif 序列 == 23 then -- 鞋子
        if 公式 then
            道具.防御=math.floor(取随机数(打造属性.防御[级别][1]*1.2,打造属性.防御[级别][2]*1.2))
            道具.敏捷=math.floor(取随机数(打造属性.敏捷[级别][1]*1.3,打造属性.敏捷[级别][2]*1.3))
        else
            道具.防御 = 取随机数(打造属性.防御[级别][1],打造属性.防御[级别][2])
            道具.敏捷 = 取随机数(打造属性.敏捷[级别][1],打造属性.敏捷[级别][2])
        end

  end

  local 特效几率 = 5
  if 公式 then
      特效几率 = 65
  end
  local 通用特效 = {"无级别限制","神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生","易修理","超级简易"}
  if 道具.分类 == 5 then
        table.insert(通用特效,"愤怒")
        table.insert(通用特效,"暴怒")
  end
  if 取随机数()<=特效几率 then
      道具.特效 = 通用特效[取随机数(1,#通用特效)]
  end
  local 通用特技 = {"气疗术","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒",
    "起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼",
    "碎甲术","破甲术","破血狂攻","慈航普渡","笑里藏刀","罗汉金钟","破碎无双","圣灵之甲","野兽之力"
    ,"琴音三叠","菩提心佑","先发制人","身似菩提"}

  if 取随机数()<= 特效几率 then
        道具.特技 = 通用特技[取随机数(1,#通用特技)]
  end
  local 制造格子=玩家数据[id].道具:取新编号()
  玩家数据[id].道具.数据[制造格子]=道具
  玩家数据[id].道具.数据[制造格子].五行=取五行()
  玩家数据[id].道具.数据[制造格子].耐久度=取随机数(500,700)
  玩家数据[id].道具.数据[制造格子].识别码=取唯一识别码(id)
  玩家数据[id].道具.数据[制造格子].鉴定=false
  玩家数据[id].角色.数据.道具[玩家数据[id].角色:取道具格子()]=制造格子
  玩家数据[id].角色:日志记录(format("生成装备：名称%s,级别%s,识别码%s",道具.名称,道具.级别限制,玩家数据[id].道具.数据[制造格子].识别码))
  常规提示(id,"#Y/你得到了#R/"..玩家数据[id].道具.数据[制造格子].名称)
  --table.print(玩家数据[id].道具.数据[制造格子])
  return 制造格子
end




function 装备处理类:指定等级物品(lv,id)
	local wps = self.打造物品[id]
	local 取随机数 = 引擎.取随机整数
	local ids = math.floor(lv/10+1)
	if id <= 18 and lv >= 90 and lv <= 140 then
		if ids <= 12 then
			ids = 取随机数(10,12)
		else
		    ids = 取随机数(13,15)
		end
	end
	wps = wps[ids]
	if type(wps) == "table" then
		wps = wps[取随机数(1,2)]
	end
	return wps
end



function 装备处理类:取可以镶嵌(装备,宝石)
	if 装备.分类 == 1 and (宝石.子类 == 2 or 宝石.子类 == 3 or 宝石.子类==5) then
		return true
	elseif 装备.分类 == 2 and 宝石.子类 == 4 then
		return true
	elseif 装备.分类 == 3 and (宝石.子类 == 3 or 宝石.子类 == 5 or 宝石.子类 == 7) then
		return true
	elseif 装备.分类 == 4 then
    if string.find(装备.名称,"(坤)") and 装备.角色限制 and 装备.角色限制[1]=="影精灵" then
        if  宝石.子类 == 3 or 宝石.子类 == 5 or 宝石.子类 == 7 then
              return true
        end
    else
        if  宝石.子类 == 1 or 宝石.子类 == 2 or 宝石.子类 == 4 then
            return true
        end
    end
	elseif 装备.分类 == 5 and (宝石.子类 == 1 or 宝石.子类 == 6 or 宝石.子类 == 7) then
		return true
	elseif 装备.分类 == 6 and (宝石.子类 == 6 or 宝石.子类 == 7) then
		return true
	end
end



function 装备处理类:取宝石合成几率(id,级别)

  if 级别<=5 then
    return true
  end
  local 几率加成=玩家数据[id].角色:取剧情技能等级("宝石工艺")*3
  if 级别>15 and 级别<20 then
    几率加成=玩家数据[id].角色:取剧情技能等级("宝石工艺")*2
  elseif 级别>=20 then
     几率加成=玩家数据[id].角色:取剧情技能等级("宝石工艺")
  end
  local 初始几率=100-级别*5 + 几率加成
  if 级别==21 then
    初始几率 = 6
  elseif 级别==22 then
    初始几率 = 5
  elseif 级别==23 then
    初始几率 = 4
  elseif 级别==24 then
    初始几率 = 3
  elseif 级别==25  then
    初始几率 = 2
  elseif 级别>=26 and 级别<35 then
    初始几率 = 1
  elseif 级别>=35 then
    return  false
  end

  if 取随机数()>初始几率 then
    return false
  else
    return true
  end
end













function 装备处理类:更新(dt) end
function 装备处理类:显示(x,y) end

return 装备处理类