/**
 * GM工具协议常量定义
 * 基于原始Lua代码分析的协议序号和常量
 */

// 数据分隔符
const SEPARATORS = {
    FGF: "12345*-*12345",
    FGC: "12345@+@12345"
};

// HP-Socket配置
const HP_SOCKET = {
    PACKET_HEADER_FLAG: 814,        // GGELUA_FLAG计算结果
    MAX_PACKET_SIZE: 4194303,       // 最大数据包大小 (0x3FFFFF)
    DEFAULT_PACKET_SIZE: 262144     // 默认数据包大小 (0x40000)
};

// 协议序号定义（基于原始代码分析）
const PROTOCOL_IDS = {
    // 系统协议
    LOGIN: 112345,                  // 登录验证（与原始GM工具一致）
    SYSTEM_READY: 1,               // 系统就绪
    TIPS: 7,                       // 提示信息
    FUNCTION_REFRESH: 8,           // 功能界面刷新
    MONITOR_JOIN: 9,               // 监控加入
    ROLE_INFO: 10,                 // 角色信息嵌入
    PET_INFO: 11,                  // 召唤兽信息获取
    ITEM_ADD: 12,                  // 道具加入
    ACCOUNT_GET: 13,               // 获取账号
    MOUNT_INFO: 14,                // 坐骑信息获取
    
    // 系统控制
    FORCE_LOGOUT: 999,             // 强制下线
    SYSTEM_LOGOUT: 998,            // 系统下线
    
    // 充值操作相关
    RECHARGE_JADE: 101,            // 充值仙玉
    RECHARGE_CARD: 102,            // 充值点卡
    RECHARGE_SILVER: 103,          // 充值银子
    RECHARGE_RESERVE: 104,         // 充值储备
    RECHARGE_EXP: 105,             // 充值经验
    RECHARGE_TOTAL: 106,           // 充值累充
    RECHARGE_GUILD: 107,           // 充值帮贡
    RECHARGE_SECT: 108,            // 充值门贡
    SKILL_CRAFT: 109,              // 打造熟练
    SKILL_TAILOR: 110,             // 裁缝熟练
    SKILL_ALCHEMY: 111,            // 炼金熟练
    SKILL_REFINE: 112,             // 淬灵熟练
    ACTIVE_POINTS: 113,            // 活跃积分
    PVP_POINTS: 114,               // 比武积分
    RECHARGE_RECORD: 115,          // 充值记录
    
    // 账号管理相关
    PLAYER_INFO: 201,              // 玩家信息
    KICK_BATTLE: 202,              // 踢出战斗
    FORCE_OFFLINE: 203,            // 强制下线
    BAN_ACCOUNT: 204,              // 封禁账号
    UNBAN_ACCOUNT: 205,            // 解封账号
    BAN_IP: 206,                   // 封禁IP
    UNBAN_IP: 207,                 // 解封IP
    ENABLE_ADMIN: 208,             // 开通管理
    DISABLE_ADMIN: 209,            // 关闭管理
    
    // 游戏管理相关
    TOMB_SPIRIT: 301,              // 四墓灵鼠
    OPEN_OTHERWORLD: 302,          // 开启异界
    OPEN_TREASURE: 303,            // 开启经宝
    HEAVEN_MONKEY: 304,            // 天降灵猴
    SPAWN_DEMON: 305,              // 刷出妖魔
    CONSTELLATION_28: 306,         // 二八星宿
    HEAVEN_REBEL: 307,             // 天庭叛逆
    SPAWN_STAR: 308,               // 刷出星宿
    
    // 装备管理相关
    SEND_EQUIPMENT: 401,           // 发送装备
    SEND_SPIRIT: 402,              // 发送灵饰
    CUSTOM_EQUIPMENT: 403,         // 定制装备
    
    // 其他功能
    SEND_TRAVEL_FEE: 501,          // 发送路费
    CHANGE_PASSWORD: 502,          // 修改密码
    GIVE_TITLE: 503                // 给予称谓
};

// 响应状态码
const RESPONSE_STATUS = {
    SUCCESS: 0,
    ERROR: 1,
    INVALID_PARAMS: 2,
    PERMISSION_DENIED: 3,
    ACCOUNT_NOT_FOUND: 4,
    ALREADY_ONLINE: 5,
    SERVER_ERROR: 6
};

// 用户权限级别
const USER_LEVELS = {
    NORMAL: 0,                     // 普通用户
    VIP: 1,                        // VIP用户
    ADMIN: 2,                      // 管理员
    SUPER_ADMIN: 3                 // 超级管理员
};

// 装备类型定义
const EQUIPMENT_TYPES = {
    WEAPON: 1,                     // 武器
    ARMOR: 2,                      // 防具
    ACCESSORY: 3,                  // 饰品
    SPIRIT: 4                      // 灵饰
};

// 宠物类型定义
const PET_TYPES = {
    NORMAL: 1,                     // 普通宠物
    DIVINE: 2,                     // 神兽
    VARIANT: 3,                    // 变异宠物
    SPECIAL: 4                     // 特殊宠物
};

// 游戏活动类型
const ACTIVITY_TYPES = {
    TOMB_SPIRIT: 1,                // 四墓灵鼠
    OTHERWORLD: 2,                 // 异界
    TREASURE_MAP: 3,               // 藏宝图
    HEAVEN_MONKEY: 4,              // 天降灵猴
    DEMON_INVASION: 5,             // 妖魔入侵
    CONSTELLATION: 6,              // 星宿
    HEAVEN_REBEL: 7                // 天庭叛逆
};

// 错误消息定义
const ERROR_MESSAGES = {
    INVALID_ACCOUNT: '账号格式不正确',
    INVALID_PASSWORD: '密码格式不正确',
    ACCOUNT_NOT_EXIST: '账号不存在',
    WRONG_PASSWORD: '密码错误',
    ACCOUNT_BANNED: '账号已被封禁',
    IP_BANNED: 'IP已被封禁',
    PERMISSION_DENIED: '权限不足',
    SERVER_BUSY: '服务器繁忙，请稍后重试',
    INVALID_PARAMS: '参数错误',
    OPERATION_FAILED: '操作失败'
};

// 成功消息定义
const SUCCESS_MESSAGES = {
    LOGIN_SUCCESS: '登录成功',
    OPERATION_SUCCESS: '操作成功',
    RECHARGE_SUCCESS: '充值成功',
    ACCOUNT_CREATED: '账号创建成功',
    ACCOUNT_UPDATED: '账号更新成功',
    EQUIPMENT_SENT: '装备发送成功',
    ACTIVITY_STARTED: '活动开启成功'
};

// 导出所有常量
module.exports = {
    SEPARATORS,
    HP_SOCKET,
    PROTOCOL_IDS,
    RESPONSE_STATUS,
    USER_LEVELS,
    EQUIPMENT_TYPES,
    PET_TYPES,
    ACTIVITY_TYPES,
    ERROR_MESSAGES,
    SUCCESS_MESSAGES
};
