name: CI

on: [push, pull_request]

jobs:
  build_and_test:
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Set up Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.9
      - uses: actions/setup-node@v2
        with:
          node-version: '14'
      - name: install dependencies and test
        shell: bash
        run: |
          npm install
          npm test
      - name: build with node-gyp
        shell: bash
        run: |
          npm install -g node-gyp
          node-gyp configure
          node-gyp rebuild
