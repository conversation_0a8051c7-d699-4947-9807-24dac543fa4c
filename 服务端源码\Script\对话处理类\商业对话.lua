
local 商业对话 = class()






function 商业对话:初始化() end

function 商业对话:购买商品(连接id,id,序号,内容)
  self.商品匹配=false
  if type(玩家数据[id].商品列表)~="table" then
    异常账号(id,"未触发商品界面却触发购买请求。请求购买的商品数据为："..内容.商品)
    return 0
  else
    self.组合商品=""
    for n=1,#玩家数据[id].商品列表 do
      self.组合商品=self.组合商品..玩家数据[id].商品列表[n]
      if 玩家数据[id].商品列表[n]==内容.商品 then
        self.商品匹配=true
      end
    end
    if self.商品匹配==false then
      异常账号(id,"所购买的商品不再列表之类。商品列表为"..self.组合商品.."购买商品为"..内容.商品)
      return 0
    end
  end
  if 内容.数量==nil then
    内容.数量=""
  end
  if type(内容.数量)~="number" or 内容.数量<=0 or 内容.数量>99 or math.floor(内容.数量)~=内容.数量 then
    异常账号(id,"所购买的商品数量异常，提交的商品数量为"..内容.数量)
    return 0
  end
  --先获取一次格子
  local 道具格子=玩家数据[id].角色:取道具格子() ----
  if 道具格子==0 then
    常规提示(id,"您的道具栏物品已经满啦")
    return 0
  end
  local 商品分割=分割文本(内容.商品,"*")
  local 商品名称=商品分割[1]
  local 商品单价=商品分割[2]
  local 商品数量=内容.数量
  local 商品道具=物品类()
  local 道具格子=0
  商品道具:置对象(商品名称)
  local 成功数量=0
  --先计算是否可重叠
  if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then
  else
    local 总价格=商品数量*商品单价
    道具格子=玩家数据[id].角色:取道具格子()
    if 道具格子==0 then
      常规提示(id,"您的道具栏物品已经满啦")
      return 0
    end
    if 总价格<=0 then
      常规提示(id,"操你妈还想刷钱!")
      return 0
    end
    if 商品道具.总类 == "跑商商品" then
        for k,v in pairs(玩家数据[id].角色.数据.道具) do
             if 玩家数据[id].道具.数据[v] and 玩家数据[id].道具.数据[v].名称 == "帮派银票" then
               if 玩家数据[id].道具.数据[v].初始金额 < 总价格 then
                  常规提示(id,"您身上的帮派银票似乎不够哟")
                  return 0
                else
                    玩家数据[id].道具.数据[v].初始金额 = 玩家数据[id].道具.数据[v].初始金额 - 总价格
                end
             end
        end
        道具格子=玩家数据[id].角色:取道具格子()
        if 道具格子==0 then
            常规提示(id,"您的道具栏物品已经满啦")
            return 0
        end
        道具编号=玩家数据[id].道具:取新编号()
        玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
        玩家数据[id].道具.数据[道具编号].数量=商品数量
        玩家数据[id].角色.数据.道具[道具格子]=道具编号
        常规提示(id,"您花费"..总价格.."两帮派银子购买了"..商品名称.."*"..商品数量)
        道具刷新(id)
        return 0
    elseif 玩家数据[id].角色:扣除银子(总价格,"购买系统商店商品["..商品名称.."]数量:"..商品数量) and 商品道具.总类 ~= "跑商商品" then
      if 商品名称=="高级魔兽要诀" then
        玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="灵饰指南书" then
        玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="元灵晶石" then
        玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="随机百炼精铁" then
        玩家数据[id].道具:给予书铁(id,{10,13},"精铁")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","百炼精铁"),频道="cw"})
      elseif 商品名称=="随机制造指南书" then
        玩家数据[id].道具:给予书铁(id,{10,13},"指南书")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","制造指南书"),频道="cw"})
      else
        道具编号=玩家数据[id].道具:取新编号()
        玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
        玩家数据[id].道具.数据[道具编号].数量=商品数量
        玩家数据[id].角色.数据.道具[道具格子]=道具编号
        self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
      end
      常规提示(id,"您花费"..总价格.."两银子购买了"..商品名称.."*"..商品数量)
      道具刷新(id)
	    金钱刷新(id)
      return 0
    else
      --重新计算可购买数量
      总价格=0
      for n=1,商品数量 do
        if (成功数量+1)*商品单价>取银子(id) then
          成功数量=成功数量+1
        else
          if 成功数量<=0 then
            常规提示(id,"您身上的银子似乎不够哟")
            return 0
          else
            总价格=成功数量*商品单价
            if 玩家数据[id].角色:扣除银子(总价格,"购买系统商店商品["..商品名称.."]数量:"..成功数量) then
              if 商品名称=="高级魔兽要诀" then
                玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="灵饰指南书" then
                玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10})
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="元灵晶石" then
                玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10})
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="随机百炼精铁" then
                玩家数据[id].道具:给予书铁(id,{10,13},"精铁")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","百炼精铁"),频道="cw"})
              elseif 商品名称=="随机制造指南书" then
                玩家数据[id].道具:给予书铁(id,{10,13},"指南书")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","制造指南书"),频道="cw"})
              else
                道具编号=玩家数据[id].道具:取新编号()
                玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
                玩家数据[id].道具.数据[道具编号].数量=成功数量
                玩家数据[id].角色.数据.道具[道具格子]=道具编号
                self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
              end
              常规提示(id,"您花费"..总价格.."两银子购买了"..商品名称.."*"..成功数量)
              道具刷新(id)
			        金钱刷新(id)
              return 0
            else
              常规提示(id,"您身上的银子似乎不够哟")
              return 0
            end
          end
        end
      end
    end
  end
  for n=1,商品数量 do
    道具格子=玩家数据[id].角色:取道具格子()
    if 道具格子==0 then
      常规提示(id,"您的道具栏物品已经满啦")
      return 0
    end
    if 玩家数据[id].角色:扣除银子(商品单价,"购买系统商店商品["..商品名称.."]") then
      if 商品名称=="高级魔兽要诀" then
        玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="灵饰指南书" then
        玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="元灵晶石" then
        玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="魔兽要诀" then
        玩家数据[id].道具:给予道具(id,"魔兽要诀")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="随机百炼精铁" then
        玩家数据[id].道具:给予书铁(id,{10,13},"精铁")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","百炼精铁"),频道="cw"})
      elseif 商品名称=="随机制造指南书" then
        玩家数据[id].道具:给予书铁(id,{10,13},"指南书")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","制造指南书"),频道="cw"})
      else
        道具编号=玩家数据[id].道具:取新编号()
        玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
        玩家数据[id].角色.数据.道具[道具格子]=道具编号
        self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
      end
      成功数量=成功数量+1
    else
      常规提示(id,"您身上的银子似乎不够哟")
      return 0
    end
  end
  常规提示(id,"您花费"..(商品单价*成功数量).."两银子购买了"..商品名称.."*"..成功数量)
  道具刷新(id)
  金钱刷新(id)
end
function 商业对话:购买商品1(连接id,id,序号,内容)
  self.商品匹配=false
  if type(玩家数据[id].商品列表)~="table" then
    异常账号(id,"未触发商品界面却触发购买请求。请求购买的商品数据为："..内容.商品)
    return 0
  else
    self.组合商品=""
    for n=1,#玩家数据[id].商品列表 do
      self.组合商品=self.组合商品..玩家数据[id].商品列表[n]
      if 玩家数据[id].商品列表[n]==内容.商品 then
        self.商品匹配=true
      end
    end
    if self.商品匹配==false then
      异常账号(id,"所购买的商品不再列表之类。商品列表为"..self.组合商品.."购买商品为"..内容.商品)
      return 0
    end
  end
  if 内容.数量==nil then
    内容.数量=""
  end
  if type(内容.数量)~="number" or 内容.数量<=0 or 内容.数量>99 or math.floor(内容.数量)~=内容.数量 then
    异常账号(id,"所购买的商品数量异常，提交的商品数量为"..内容.数量)
    return 0
  end
  --先获取一次格子
  local 道具格子=玩家数据[id].角色:取道具格子() ----
  if 道具格子==0 then
    常规提示(id,"您的道具栏物品已经满啦")
    return 0
  end
  local 商品分割=分割文本(内容.商品,"*")
  local 商品名称=商品分割[1]
  local 商品单价=商品分割[2]
  local 商品数量=内容.数量
  local 商品道具=物品类()
  local 道具格子=0
  商品道具:置对象(商品名称)
  local 成功数量=0
  --先计算是否可重叠
  if 商品道具.可叠加 == nil or 商品道具.可叠加 == false then

  else
    local 总价格=商品数量*商品单价
    道具格子=玩家数据[id].角色:取道具格子()
    if 道具格子==0 then
      常规提示(id,"您的道具栏物品已经满啦")
      return 0
    end
    if 总价格<=0 then
    常规提示(id,"操你妈还想刷钱!")
    return 0
  end
    if 商品道具.总类 == "跑商商品" then
          for k,v in pairs(玩家数据[id].角色.数据.道具) do
               if 玩家数据[id].道具.数据[v] and 玩家数据[id].道具.数据[v].名称 == "帮派银票" then
                 if 玩家数据[id].道具.数据[v].初始金额 < 总价格 then
                    常规提示(id,"您身上的帮派银票似乎不够哟")
                    return 0
                  else
                      玩家数据[id].道具.数据[v].初始金额 = 玩家数据[id].道具.数据[v].初始金额 - 总价格
                  end
               end
          end
          道具格子=玩家数据[id].角色:取道具格子()
          if 道具格子==0 then
            常规提示(id,"您的道具栏物品已经满啦")
            return 0
          end
          道具编号=玩家数据[id].道具:取新编号()
          玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
          玩家数据[id].道具.数据[道具编号].数量=商品数量
          玩家数据[id].角色.数据.道具[道具格子]=道具编号
          常规提示(id,"您花费"..总价格.."两帮派银子购买了"..商品名称.."*"..商品数量)
          道具刷新(id)
          return 0
        -- self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
    elseif 玩家数据[id].角色:扣除钓鱼积分(总价格,0,0,"购买系统商店商品["..商品名称.."]数量:"..商品数量) and 商品道具.总类 ~= "跑商商品" then
      if 商品名称=="高级魔兽要诀" then
        玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="灵饰指南书" then
        玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="元灵晶石" then
        玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10,12,14})
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
      elseif 商品名称=="随机百炼精铁" then
        玩家数据[id].道具:给予书铁(id,{10,13},"精铁")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","百炼精铁"),频道="cw"})
      elseif 商品名称=="随机制造指南书" then
        玩家数据[id].道具:给予书铁(id,{10,13},"指南书")
        广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","制造指南书"),频道="cw"})
      else
        道具编号=玩家数据[id].道具:取新编号()
        玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
        玩家数据[id].道具.数据[道具编号].数量=商品数量
        玩家数据[id].角色.数据.道具[道具格子]=道具编号
        self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
      end
      常规提示(id,"您花费"..总价格.."两银子购买了"..商品名称.."*"..商品数量)
      道具刷新(id)
      return 0
    else
      --重新计算可购买数量
      总价格=0
      for n=1,商品数量 do
        if (成功数量+1)*商品单价>玩家数据[id].角色.数据.钓鱼积分 then
          成功数量=成功数量+1
        else
          if 成功数量<=0 then
            常规提示(id,"你的钓鱼积分不够")
            return 0
          else
            总价格=成功数量*商品单价
            if 玩家数据[id].角色:扣除钓鱼积分(总价格,0,0,"购买系统商店商品["..商品名称.."]数量:"..成功数量) then
              if 商品名称=="高级魔兽要诀" then
                玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="灵饰指南书" then
                玩家数据[id].道具:给予道具(id,"灵饰指南书",{6,8,10})
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="元灵晶石" then
                玩家数据[id].道具:给予道具(id,"元灵晶石",{6,8,10})
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24",商品名称),频道="cw"})
              elseif 商品名称=="随机百炼精铁" then
                玩家数据[id].道具:给予书铁(id,{10,13},"精铁")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","百炼精铁"),频道="cw"})
              elseif 商品名称=="随机制造指南书" then
                玩家数据[id].道具:给予书铁(id,{10,13},"指南书")
                广播消息({内容=string.format("#G听说有人在傲来国的云游道人处花重金购买了#Y%s#24","制造指南书"),频道="cw"})
              -- elseif 商品名称=="点化石" then
              -- 玩家数据[id].道具:给予道具(id,"点化石")
              else
                道具编号=玩家数据[id].道具:取新编号()
                玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
                玩家数据[id].道具.数据[道具编号].数量=成功数量
                玩家数据[id].角色.数据.道具[道具格子]=道具编号
                self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
              end
              常规提示(id,"您花费"..总价格.."积分购买了"..商品名称.."*"..成功数量)
              道具刷新(id)
              return 0
            else
              常规提示(id,"您身上的银子似乎不够哟")
              return 0
            end
          end
        end
      end
    end
  end
  for n=1,商品数量 do
    道具格子=玩家数据[id].角色:取道具格子()
    if 道具格子==0 then
      常规提示(id,"您的道具栏物品已经满啦")
      return 0
    end
    if 玩家数据[id].角色:扣除钓鱼积分(商品单价,0,0,"购买系统商店商品["..商品名称.."]") then
    if 商品名称=="高级魔兽要诀" then
    玩家数据[id].道具:给予道具(id,"高级魔兽要诀")
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,商品名称),频道="cw"})
    elseif 商品名称=="魔兽要诀" then
    玩家数据[id].道具:给予道具(id,"魔兽要诀")
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"魔兽要诀"),频道="cw"})
    elseif 商品名称=="点化石" then
    玩家数据[id].道具:给予道具(id,"点化石")
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"点化石"),频道="cw"})
    elseif 商品名称=="附魔宝珠" then
    玩家数据[id].道具:给予道具(id,"附魔宝珠",{100,110,120,130,140,150})
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"附魔宝珠"),频道="cw"})
    elseif 商品名称=="神兜兜" then
    玩家数据[id].道具:给予道具(id,"神兜兜",1)
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"神兜兜"),频道="cw"})
    elseif 商品名称=="金柳露" then
    玩家数据[id].道具:给予道具(id,"金柳露",1)
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"金柳露"),频道="cw"})
    elseif 商品名称=="超级金柳露" then
    玩家数据[id].道具:给予道具(id,"超级金柳露",1)
    广播消息({内容=string.format("#G听说#R/#G%s#Y在傲来国的渔夫处花重金购买了#Y%s#24",玩家数据[id].角色.数据.名称,"超级金柳露"),频道="cw"})
      else
    道具编号=玩家数据[id].道具:取新编号()
    玩家数据[id].道具.数据[道具编号]=复制物品(商品道具)
    玩家数据[id].角色.数据.道具[道具格子]=道具编号
    self:商品参数补充(玩家数据[id].道具.数据[道具编号].名称,道具编号,id)
      end
      成功数量=成功数量+1
    else
      常规提示(id,"您的钓鱼积分不够")
      return 0
    end
  end
  常规提示(id,"您花费"..(商品单价*成功数量).."积分购买了"..商品名称.."*"..成功数量)
  道具刷新(id)
end


function 商业对话:购买仙缘物品(连接id,id,序号,内容)
        local 道具格子=玩家数据[id].角色:取道具格子() ----
        if 道具格子==0 then
            常规提示(id,"您的道具栏物品已经满啦")
            return
        end
      local 商品分割=分割文本(内容.商品,"*")
      local 商品名称=商品分割[1]
      local 商品单价=商品分割[2]
      local 商品数量=tonumber(内容.数量)
      if 商品数量 <=1 then
         商品数量 = 1
      end
      local 寻找物品 = 商品分割[1].."*"..商品分割[2]
      local 找到 = 0
      for i=1,#商店处理类.仙缘商店 do
          if 寻找物品==商店处理类.仙缘商店[i] then
              找到 = 1
          end
      end
      if 找到~=0 then
          local 扣除积分 = tonumber(商品单价)*商品数量
          if 玩家数据[id].角色.数据.仙缘积分< 扣除积分 then
             常规提示(id,"你的仙缘积分不够")
             return
          end
          玩家数据[id].角色.数据.仙缘积分 =玩家数据[id].角色.数据.仙缘积分 - 扣除积分
          玩家数据[id].道具:给予道具(id,商品名称,商品数量)
          常规提示(id,"您花费#R/"..扣除积分.."#Y/积分购买了#R/"..商品名称.."#Y/*#R/"..商品数量)
          道具刷新(id)
      else
           常规提示(id,"#Y/数据错误")
      end
end

function 商业对话:购买文韵物品(连接id,id,序号,内容)
        local 道具格子=玩家数据[id].角色:取道具格子() ----
        if 道具格子==0 then
            常规提示(id,"您的道具栏物品已经满啦")
            return
        end
      local 商品分割=分割文本(内容.商品,"*")
      local 商品名称=商品分割[1]
      local 商品单价=商品分割[2]
      local 商品数量=tonumber(内容.数量)
      if 商品数量 <=1 then
         商品数量 = 1
      end
      local 寻找物品 = 商品分割[1].."*"..商品分割[2]
      local 找到 = 0
      for i=1,#商店处理类.文韵商店 do
          if 寻找物品==商店处理类.文韵商店[i] then
              找到 = 1
          end
      end
      if 找到~=0 then
          local 扣除积分 = tonumber(商品单价)*商品数量
          if 玩家数据[id].角色.数据.文韵积分< 扣除积分 then
             常规提示(id,"你的文韵积分不够")
             return
          end
          玩家数据[id].角色.数据.文韵积分 =玩家数据[id].角色.数据.文韵积分 - 扣除积分
          玩家数据[id].道具:给予道具(id,商品名称,商品数量)
          常规提示(id,"您花费#R/"..扣除积分.."#Y/积分购买了#R/"..商品名称.."#Y/*#R/"..商品数量)
          道具刷新(id)
      else
           常规提示(id,"#Y/数据错误")
      end
end




function 商业对话:商品参数补充(名称,道具id,id)
  玩家数据[id].道具.数据[道具id].识别码=取唯一识别码(id)
  if 玩家数据[id].道具.数据[道具id].总类==1 then
    玩家数据[id].道具.数据[道具id].子类=20
  elseif 玩家数据[id].道具.数据[道具id].名称=="秘制红罗羹" or 玩家数据[id].道具.数据[道具id].名称=="秘制绿萝羹" then
    玩家数据[id].道具.数据[道具id].品质=100
elseif 玩家数据[id].道具.数据[道具id].总类==2 then
    if 玩家数据[id].道具.数据[道具id].级别限制 == nil then
      玩家数据[id].道具.数据[道具id].级别限制 = 0
    end
    local lv = 玩家数据[id].道具.数据[道具id].级别限制
    if 玩家数据[id].道具.数据[道具id].分类 == 3 then
      玩家数据[id].道具.数据[道具id].伤害 = math.floor((lv/10)*30+10)
      玩家数据[id].道具.数据[道具id].命中 = math.floor((lv/10)*35+10+2)
    elseif 玩家数据[id].道具.数据[道具id].分类 == 4  then
      玩家数据[id].道具.数据[道具id].防御 = math.floor((lv/10)*15+10)
    elseif 玩家数据[id].道具.数据[道具id].分类 == 1 then
      玩家数据[id].道具.数据[道具id].防御 = math.floor((lv/10)*5+5)
      玩家数据[id].道具.数据[道具id].魔法 = math.floor((lv/10)*10+5+2)
    elseif 玩家数据[id].道具.数据[道具id].分类 == 5 then
      玩家数据[id].道具.数据[道具id].防御 = math.floor((lv/10)*5+5)
      玩家数据[id].道具.数据[道具id].气血 = math.floor((lv/10)*20+10)
    elseif 玩家数据[id].道具.数据[道具id].分类 == 2 then
      玩家数据[id].道具.数据[道具id].灵力 = math.floor((lv/10)*12+5)
    elseif 玩家数据[id].道具.数据[道具id].分类 == 6 then
      玩家数据[id].道具.数据[道具id].防御 = math.floor((lv/10)*5+5)
      玩家数据[id].道具.数据[道具id].敏捷 = math.floor((lv/10)*3+5)
    end
end
end

function 商业对话:治疗召唤兽气血(连接id,id)
  local 气血=玩家数据[id].召唤兽:取气血差()
  local 魔法=玩家数据[id].召唤兽:取魔法差()
  local 银子=math.floor(气血*0.5)+魔法
  if 玩家数据[id].角色.数据.银子<银子 then
    发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
    return
  end
  玩家数据[id].角色:扣除银子(银子,"治疗召唤兽气血魔法",1)
  for n=1,#玩家数据[id].召唤兽.数据 do
    玩家数据[id].召唤兽.数据[n].气血=玩家数据[id].召唤兽.数据[n].最大气血
    玩家数据[id].召唤兽.数据[n].魔法=玩家数据[id].召唤兽.数据[n].最大魔法
  end
  发送数据(连接id,18,玩家数据[id].角色.数据.参战宝宝)
  发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("收您%s两银子，已将您的所有召唤兽气血和魔法全部恢复至最佳状态。",银子)})
end

function 商业对话:治疗召唤兽忠诚(连接id,id)
  local 忠诚=玩家数据[id].召唤兽:取忠诚差()
  local 银子=忠诚*100
  if 玩家数据[id].角色.数据.银子<银子 then
    发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
    return
  end
  玩家数据[id].角色:扣除银子(银子,"治疗召唤兽忠诚",1)
  for n=1,#玩家数据[id].召唤兽.数据 do
    玩家数据[id].召唤兽.数据[n].忠诚=100
  end
  发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("收您%s两银子，已将您的所有召唤兽忠诚恢复至最佳状态。",银子)})
end

function 商业对话:治疗召唤兽全体(连接id,id)
  local 忠诚=玩家数据[id].召唤兽:取忠诚差()
  local 银子=忠诚*100
  local 气血=玩家数据[id].召唤兽:取气血差()
  local 魔法=玩家数据[id].召唤兽:取魔法差()
  银子=银子+math.floor(气血*0.5)+魔法
  if 玩家数据[id].角色.数据.银子<银子 then
    发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("本次需要收费%s两银子，你没那么多的银子哟#24",银子)})
    return
  end
  玩家数据[id].角色:扣除银子(银子,"治疗召唤兽忠诚、气血、魔法",1)
  for n=1,#玩家数据[id].召唤兽.数据 do
    玩家数据[id].召唤兽.数据[n].气血=玩家数据[id].召唤兽.数据[n].最大气血
    玩家数据[id].召唤兽.数据[n].魔法=玩家数据[id].召唤兽.数据[n].最大魔法
    玩家数据[id].召唤兽.数据[n].忠诚=100
  end
  发送数据(连接id,1501,{名称="超级巫医",模型="男人_巫医",对话=string.format("收您%s两银子，已将您的所有召唤兽气血、魔法、忠诚恢复至最佳状态。",银子)})
end


return 商业对话