-- @作者: baidwwy
-- @邮箱:  <EMAIL>
-- @创建时间:   2015-11-13 10:12:10
-- @最后修改来自: baidwwy
-- @Last Modified time: 2018-04-05 23:37:43

local Client = class()

-- function Client:初始化()

-- end

function Client:连接(ip,port,async)--地址，端口，异步
    local result = self._hp:Start(ip,port,async and 1 or 0)

    if result == 1 then
        print('[Client] 连接请求已发送 -', ip .. ':' .. port)
    else
        local errorCode = self._hp:GetLastError()
        local errorDesc = self._hp:GetLastErrorDesc()
        print('[Client] 连接失败 - 错误:', errorCode, errorDesc)
    end

    return result == 1
end
function Client:断开()
    self._hp:Stop()
end
function Client:暂停()
    self._hp:PauseReceive(1)
end
function Client:恢复()
    self._hp:PauseReceive(0)
end
function Client:发送(pBuffer,iLength)
    return self._hp:Send(pBuffer,iLength,0) == 1
end
--向指定连接发送多组数据
function Client:发送_组(pBuffers,iCount)
    self._hp:SendPackets(pBuffers,iCount)
end

--准备连接通知
function Client:OnPrepareConnect(socket)
    print('[GM工具] Client:OnPrepareConnect - 准备连接，Socket:', socket)
    return 1
end
--连接完成通知
function Client:OnConnect()
    print('[GM工具] Client:OnConnect - 连接成功回调触发')
    
    -- 显示连接信息
    local connID = self._hp:GetConnectionID()
    print('[GM工具] Client:OnConnect - 连接ID:', connID)
    
    local localAddr = self._hp:GetLocalAddress()
    print('[GM工具] Client:OnConnect - 本地地址:', localAddr)
    
    -- 获取远程主机信息（某些版本可能不支持GetRemoteHost方法）
    local success, remoteHost = pcall(function() return self._hp:GetRemoteHost() end)
    if success then
        print('[GM工具] Client:OnConnect - 远程主机:', remoteHost)
    else
        print('[GM工具] Client:OnConnect - 远程主机信息获取失败（方法不存在）')
    end
    
    if self.连接成功 then
        print('[GM工具] Client:OnConnect - 调用用户连接成功回调')
        return __gge.safecall(self.连接成功,self) or 0
    end
    return 1
end
--已发送数据通知
function Client:OnSend(pData,iLength)
    print('[GM工具] Client:OnSend - 数据发送完成，长度:', iLength)
    
    -- 显示发送数据的十六进制（前32字节）
    if pData and iLength > 0 and type(pData) == 'string' then
        local hexStr = ""
        for i = 1, math.min(iLength, 32) do
            local byte = string.byte(pData, i)
            if byte then
                hexStr = hexStr .. string.format("%02X ", byte)
            end
        end
        print('[GM工具] Client:OnSend - 发送数据前32字节Hex:', hexStr)
    elseif pData then
        print('[GM工具] Client:OnSend - pData类型:', type(pData), '长度:', iLength)
    end
    
    if self.发送事件 then
        return __gge.safecall(self.发送事件,self) or 0
    end
    return 1
end
--通信错误通知
local EnSocketOperation={
    [0]='UNKNOWN'  , --   // Unknown
    [1]='ACCEPT'   , --   // Acccept
    [2]='CONNECT'  , --   // Connect
    [3]='SEND'     , --   // Send
    [4]='RECEIVE'  , --   // Receive
    [5]='CLOSE'    , --   // Close
}

-- HP-Socket错误代码说明
local ErrorCodes = {
    [0] = "SE_OK - 成功",
    [1] = "SE_ILLEGAL_STATE - 当前状态不允许操作",
    [2] = "SE_INVALID_PARAM - 非法参数",
    [3] = "SE_SOCKET_CREATE - 创建 SOCKET 失败",
    [4] = "SE_SOCKET_BIND - 绑定 SOCKET 失败",
    [5] = "SE_SOCKET_PREPARE - 设置 SOCKET 失败",
    [6] = "SE_SOCKET_LISTEN - 监听 SOCKET 失败",
    [7] = "SE_CP_CREATE - 创建完成端口失败",
    [8] = "SE_WORKER_THREAD_CREATE - 创建工作线程失败",
    [9] = "SE_DETECT_THREAD_CREATE - 创建监测线程失败",
    [10] = "SE_SOCKE_ATTACH_TO_CP - 绑定完成端口失败",
    [11] = "SE_CONNECT_SERVER - 连接服务器失败",
    [12] = "SE_NETWORK - 网络错误",
    [13] = "SE_DATA_PROC - 数据处理错误",
    [14] = "SE_DATA_SEND - 数据发送失败",
    [15] = "SE_DATA_RECV - 数据接收失败",
    [16] = "SE_DATA_TYPE - 数据类型错误"
}

function Client:OnClose(enOperation,iErrorCode)
    print('[GM工具] Client:OnClose - 连接断开回调触发')
    print('[GM工具] Client:OnClose - 操作类型:', enOperation, '(' .. (EnSocketOperation[enOperation] or 'UNKNOWN') .. ')')
    print('[GM工具] Client:OnClose - 错误代码:', iErrorCode, '(' .. (ErrorCodes[iErrorCode] or '未知错误') .. ')')
    
    -- 显示当前连接状态
    local state = self._hp:GetState()
    print('[GM工具] Client:OnClose - 当前状态:', state)
    
    -- 获取详细错误信息
    local lastError = self._hp:GetLastError()
    local lastErrorDesc = self._hp:GetLastErrorDesc()
    print('[GM工具] Client:OnClose - 最后错误代码:', lastError)
    print('[GM工具] Client:OnClose - 错误描述:', lastErrorDesc)
    
    -- 如果是数据处理错误(13)，可能是协议不匹配
    if iErrorCode == 13 then
        print('[GM工具] Client:OnClose - ⚠️ 数据处理错误，可能是协议不匹配！')
        print('[GM工具] Client:OnClose - 建议检查：')
        print('[GM工具] Client:OnClose - 1. 服务端PackServer包头标识是否为814')
        print('[GM工具] Client:OnClose - 2. 数据格式是否符合PackServer协议')
        print('[GM工具] Client:OnClose - 3. MessagePack数据是否正确封装')
    end
    
    if self.连接断开 then
        print('[GM工具] Client:OnClose - 调用用户连接断开回调')
        return __gge.safecall(self.连接断开,self,enOperation,iErrorCode) or 0
    end
	客户端连接断开()
    return 1
end

--数据到达通知
function Client:OnReceive()
    return 1
end
--===============================================
function Client:是否连接()
    return self._hp:HasStarted() ==1
end
-- enum EnServiceState
-- {
--  SS_STARTING = 0,    // 正在启动
--  SS_STARTED  = 1,    // 已经启动
--  SS_STOPPING = 2,    // 正在停止
--  SS_STOPPED  = 3,    // 已经停止
-- };
function Client:取状态()
    return self._hp:GetState()
end
function Client:取错误代码()
    return self._hp:GetLastError()
end
function Client:取错误描述()
    return self._hp:GetLastErrorDesc()
end
--/* 获取该组件对象的连接 ID */
function Client:取连接ID()
    return self._hp:GetConnectionID()
end
--  /* 获取 Client Socket 的地址信息 */
function Client:取本地地址信息()
    return self._hp:GetLocalAddress()
end
--/* 获取连接的远程主机信息 */
function Client:取远程地址信息()
    return self._hp:GetRemoteHost()
end
--/* 获取连接中未发出数据的长度 */
function Client:取未发出数据长度()
    return self._hp:GetPendingDataLength()
end
--/* 获取连接的数据接收状态 */
function Client:是否暂停()
    return self._hp:IsPauseReceive()
end
--/* 设置内存块缓存池大小（通常设置为 -> PUSH 模型：5 - 10；PULL 模型：10 - 20 ） */
function Client:置缓存池大小(dwFreeBufferPoolSize)
    self._hp:SetFreeBufferPoolSize(dwFreeBufferPoolSize)
    return self
end
--/* 设置内存块缓存池回收阀值（通常设置为内存块缓存池大小的 3 倍） */
function Client:置缓存池回收阀值(dwFreeBufferPoolHold)
    self._hp:SetFreeBufferPoolHold(dwFreeBufferPoolHold)
    return self
end
--/* 获取内存块缓存池大小 */
function Client:取缓存池大小()
    return self._hp:GetFreeBufferPoolSize()
end
--/* 获取内存块缓存池回收阀值 */
function Client:取缓存池回收阀值()
    return self._hp:GetFreeBufferPoolHold()
end
return Client