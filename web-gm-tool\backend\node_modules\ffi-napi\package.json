{"name": "ffi-napi", "version": "4.0.3", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "description": "A foreign function interface (FFI) for Node.js, N-API style", "keywords": ["foreign", "function", "interface", "ffi", "libffi", "binding", "c", "napi", "stable"], "homepage": "http://github.com/node-ffi-napi/node-ffi-napi", "engines": {"node": ">=10"}, "main": "./lib/ffi", "dependencies": {"debug": "^4.1.1", "get-uv-event-loop-napi-h": "^1.0.5", "node-addon-api": "^3.0.0", "node-gyp-build": "^4.2.1", "ref-napi": "^2.0.1 || ^3.0.2", "ref-struct-di": "^1.1.0"}, "devDependencies": {"fs-extra": "^9.0.0", "mocha": "^7.1.1", "nyc": "^15.0.0", "prebuildify": "^4.0.0", "prebuildify-ci": "^1.0.5", "ref-array-di": "^1.2.1"}, "scripts": {"install": "node-gyp-build", "prebuild": "prebuildify --napi --tag-armv --tag-uv", "prepack": "prebuildify-ci download && ([ $(ls prebuilds | wc -l) = '5' ] || (echo 'Some prebuilds are missing'; exit 1))", "test": "node-gyp rebuild --directory test && nyc mocha --expose-gc --reporter spec"}, "repository": {"type": "git", "url": "http://github.com/node-ffi-napi/node-ffi-napi.git"}, "bugs": {"url": "http://github.com/node-ffi-napi/node-ffi-napi/issues"}}