import React, { useState } from 'react';
import { Card, Form, Input, Button, Typography, Space, Divider, Alert } from 'antd';
import { UserOutlined, LockOutlined, GlobalOutlined, ApiOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import './Login.css';

const { Title, Text } = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      await login(values);
    } catch (error) {
      console.error('登录失败:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background"></div>
      <Card className="login-card" bordered={false}>
        <div className="login-header">
          <Title level={2} className="login-title">
            <ApiOutlined /> Web版GM工具
          </Title>
          <Text type="secondary">游戏管理工具 - 网页版</Text>
        </div>

        <Alert
          message="系统说明"
          description="请输入您的GM账号信息和游戏服务器地址进行登录。确保网络连接正常且服务器地址正确。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          name="login"
          onFinish={handleSubmit}
          layout="vertical"
          size="large"
          initialValues={{
            gameHost: 'localhost',
            gamePort: 8888
          }}
        >
          <Form.Item
            name="username"
            label="GM账号"
            rules={[
              { required: true, message: '请输入GM账号' },
              { min: 3, message: '账号长度至少3位' }
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder="请输入GM账号"
              autoComplete="username"
            />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码长度至少6位' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入密码"
              autoComplete="current-password"
            />
          </Form.Item>

          <Divider orientation="left">服务器配置</Divider>

          <Space.Compact style={{ width: '100%' }}>
            <Form.Item
              name="gameHost"
              label="服务器地址"
              style={{ width: '70%', marginBottom: 0 }}
              rules={[{ required: true, message: '请输入服务器地址' }]}
            >
              <Input
                prefix={<GlobalOutlined />}
                placeholder="localhost"
              />
            </Form.Item>
            <Form.Item
              name="gamePort"
              label="端口"
              style={{ width: '30%', marginBottom: 0 }}
              rules={[
                { required: true, message: '请输入端口' },
                { pattern: /^\d+$/, message: '端口必须是数字' }
              ]}
            >
              <Input placeholder="8888" />
            </Form.Item>
          </Space.Compact>

          <Form.Item style={{ marginTop: 32, marginBottom: 0 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
            >
              {loading ? '连接中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>

        <div className="login-footer">
          <Text type="secondary" style={{ fontSize: '12px' }}>
            Web版GM工具 v1.0.0 | 基于原生协议开发
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
