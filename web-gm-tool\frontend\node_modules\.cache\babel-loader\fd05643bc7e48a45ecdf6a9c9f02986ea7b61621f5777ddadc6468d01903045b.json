{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UserDeleteOutlinedSvg from \"@ant-design/icons-svg/es/asn/UserDeleteOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UserDeleteOutlined = function UserDeleteOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UserDeleteOutlinedSvg\n  }));\n};\n\n/**![user-delete](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UserDeleteOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UserDeleteOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "UserDeleteOutlinedSvg", "AntdIcon", "UserDeleteOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/UserDeleteOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport UserDeleteOutlinedSvg from \"@ant-design/icons-svg/es/asn/UserDeleteOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar UserDeleteOutlined = function UserDeleteOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: UserDeleteOutlinedSvg\n  }));\n};\n\n/**![user-delete](data:image/svg+xml;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(UserDeleteOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'UserDeleteOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,qBAAqB,MAAM,iDAAiD;AACnF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC/D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,kBAAkB,CAAC;AAC/D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,oBAAoB;AAC5C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}