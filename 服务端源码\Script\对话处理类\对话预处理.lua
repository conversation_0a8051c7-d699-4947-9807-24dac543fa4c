
--======================================================================--
local NPC对话预处理 = class()
local NPC商业栏 = require("script/对话处理类/商业对话")()

function NPC对话预处理:初始化() end


function NPC对话预处理:查找神兽编号(id,事件)
 local 选项={}
 for n=1,#玩家数据[id].召唤兽.数据 do
          if 玩家数据[id].召唤兽.数据[n].种类=="神兽" and 玩家数据[id].召唤兽.数据[n].天生技能~=nil and 玩家数据[id].召唤兽.数据[n].天生技能[1]~=nil and #玩家数据[id].召唤兽.数据[n].天生技能<=4  then
              选项[#选项+1]={对话=string.format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=玩家数据[id].召唤兽.数据[n].认证码}
      end
  end


 for n=1,#选项 do
     if 选项[n].对话==事件 then
         return 选项[n].编号
       end
   end
 添加最后对话(id,"你所选的这只召唤兽并不是对方想要的")
 return 0
end



function NPC对话预处理:查找判断宝宝编号(id,事件)
 local 选项={}
 for n=1,#玩家数据[id].召唤兽.数据 do
     选项[#选项+1]={对话=string.format("%s,等级:%s",玩家数据[id].召唤兽.数据[n].名称,玩家数据[id].召唤兽.数据[n].等级),编号=玩家数据[id].召唤兽.数据[n].认证码}
 end
   for n=1,#选项 do
     if 选项[n].对话==事件 then
         return 选项[n].编号
       end
   end
 添加最后对话(id,"你所选的这只召唤兽并不是对方想要的")
 return 0
end



function NPC对话预处理:选项解析(id,数字id,名称,事件)


        if 事件=="我同意当队长" and 玩家数据[数字id].队伍~=nil and 玩家数据[数字id].队伍~=0 and  队伍数据[玩家数据[数字id].队伍].新队长 and 队伍数据[玩家数据[数字id].队伍].新队长.开关 and 队伍数据[玩家数据[数字id].队伍].新队长.id==数字id  then
             常规提示(数字id,"#Y恭喜你已经升官发财了!现在已经是一名有担当的队长了!#83")
             队伍处理类:新任队长(玩家数据[数字id].队伍,数字id)
             return
        elseif 事件=="我同意当队长" and (玩家数据[数字id].队伍==nil or 玩家数据[数字id].队伍==0) then
              常规提示(数字id,"#Y你都没队伍,何来的当队长?#82")
              return
        elseif 事件=="我果断拒绝!" and 玩家数据[数字id].队伍~=nil and 玩家数据[数字id].队伍~=0 then
                队伍数据[玩家数据[数字id].队伍].新队长.开关=false
                队伍数据[玩家数据[数字id].队伍].新队长.id=0
                常规提示(数字id,"#Y"..玩家数据[数字id].角色.数据.名称.."拒绝任命队长职务!#108")
                return
        elseif 事件=="我已确认将等级提升至70" and 玩家数据[数字id].角色.数据.等级==69 then
               玩家数据[数字id].科举对话=nil
               玩家数据[数字id].角色:升级处理(玩家数据[数字id].连接id,1)
               return
        elseif 事件=="我已确认将等级提升至110" and 玩家数据[数字id].角色.数据.等级==109 then
              玩家数据[数字id].科举对话=nil
              玩家数据[数字id].角色:升级处理(玩家数据[数字id].连接id,1)
              return
        elseif 事件=="我已确认将等级提升至130" and 玩家数据[数字id].角色.数据.等级==129 then
                玩家数据[数字id].科举对话=nil
                玩家数据[数字id].角色:升级处理(玩家数据[数字id].连接id,1)
                return
        elseif 事件=="创建帮派" then
               发送数据(id,66,1)
               return
        elseif 事件=="加入帮派" then
              帮派处理类:加入帮派(数字id)
              return
        elseif 事件=="回到帮派" then
               if 帮派处理类:取是否有帮派(数字id) then
                  if 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号].成员数据[数字id].职务 == "帮主" then
                      帮派处理类:回到帮派(数字id)
                      return
                  end
                  if 玩家数据[数字id].角色.数据.本周已缴帮费 == nil then
                     玩家数据[数字id].角色.数据.本周已缴帮费 = false
                  end

                  if 玩家数据[数字id].角色.数据.帮费限时 == nil then
                     玩家数据[数字id].角色.数据.帮费限时 = os.time()
                  end

                  if 玩家数据[数字id].角色.数据.本周已缴帮费 == false or 玩家数据[数字id].角色.数据.帮费限时 < os.time() then
                    常规提示(数字id,"#Y本周还未缴纳帮费，请缴纳帮费")
                    return
                  else
                    帮派处理类:回到帮派(数字id)
                  end
               end
               return
        elseif 事件=="领取本周帮费" then
              帮派处理类:领取帮费(数字id)
              return
        elseif 事件=="帮费设置" then
              发送数据(id,137,1)  --do
              return
        elseif 事件=="帮派加成领取" then
            if 玩家数据[数字id].角色.数据.帮派加成~=nil and 玩家数据[数字id].角色.数据.帮派加成.开关 ==true then
               常规提示(数字id,"#Y您已经领取过了,请时间到期了再来领取吧")
               return
            else
              帮派处理类:帮派福利(数字id)
            end
            return
        elseif 事件== "领取帮战胜利称号" then
              if 帮派处理类:取是否有帮派(数字id) then
                  local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
                  if 帮派数据[帮派编号].帮战胜利 and os.time()<=帮派数据[帮派编号].帮战胜利 then
                      玩家数据[数字id].角色:添加称谓("帮战之星")
                  end
              end
              return
        elseif 事件== "查看当前加成" then
              if 帮派处理类:取是否有帮派(数字id) then
                  local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
                  local 帮派规模 = 帮派数据[帮派编号].帮派规模
                  local 消耗帮贡 = 帮派规模*500
                  添加最后对话(数字id,string.format("当前帮派规模为:#G/"..帮派规模.."#W/级,消耗帮贡:#G/"..消耗帮贡.."#W/点,各项加成为:\n气血:"..帮派属性加成[帮派规模].气血.."\n魔法:"..帮派属性加成[帮派规模].魔法.."\n命中:"..帮派属性加成[帮派规模].命中.."\n伤害:"..帮派属性加成[帮派规模].伤害.."\n防御:"..帮派属性加成[帮派规模].防御.."\n速度:"..帮派属性加成[帮派规模].速度.."\n法伤:"..帮派属性加成[帮派规模].法伤.."\n法防:"..帮派属性加成[帮派规模].法防))
              end
              return
        elseif 事件=="金库" then
               地图处理类:跳转地图(数字id,1815,31,25)
               return
        elseif 事件=="兽室" then
                地图处理类:跳转地图(数字id,1835,29,25)
                return
        elseif 事件=="药房" then
                地图处理类:跳转地图(数字id,1855,22,22)
                return
        elseif 事件=="聚义厅" then
                地图处理类:跳转地图(数字id,1875,44,34)
                return
        elseif 事件=="书院" then
                地图处理类:跳转地图(数字id,1825,29,25)
                return
        elseif 事件=="厢房" then
                地图处理类:跳转地图(数字id,1845,28,23)
                return
        elseif 事件=="仓库" then
                地图处理类:跳转地图(数字id,1865,28,22)
                return
        elseif 事件=="送我回长安" then
                地图处理类:跳转地图(数字id,1001,387,13)
                return
        elseif 事件=="非常喜欢这法宝，谢谢！" then
                -- if 玩家数据[数字id].角色:取任务(308)~=0 then
                --     添加最后对话(数字id,"你已经有法宝任务了")
                -- else
                --     任务处理类:开启法宝任务(数字id)
                -- end
               添加最后对话(数字id,"法宝任务由兜率宫童子开启")
               return
        elseif 事件=="欠收拾是吧！" and 取队长权限(数字id) and 玩家数据[数字id].角色:取任务(308)~=0 and 任务数据[玩家数据[数字id].角色:取任务(308)].分类==4  then
                  战斗准备类:创建战斗(数字id,100044,玩家数据[数字id].角色:取任务(308))
                  return
        elseif 事件=="退出门派" then
                添加最后对话(数字id,"退出门派需要扣除500W银子，真的确定要退出门派吗？\n#Y（会清空所有的门派技能等级！）",{"确定退出门派","我再考虑考虑！"})
                玩家数据[数字id].最后操作="退出门派"
                return
        elseif 事件=="确定退出门派" and 玩家数据[数字id].最后操作=="退出门派" then
                玩家数据[数字id].最后操作=nil
                玩家数据[数字id].角色:退出门派(数字id)
                return
        elseif 事件=="兑换乾元丹" then
                local 附加乾元丹 = 玩家数据[数字id].角色.数据.乾元丹.附加乾元丹
                local 乾元丹消耗 = 取乾元丹消耗(附加乾元丹+1)
                添加最后对话(数字id,"#Y当前兑换乾元丹需要消耗#R"..乾元丹消耗.经验.."#Y点经验和#R"..乾元丹消耗.金钱.."#Y两银子！",{"确定兑换乾元丹","我再考虑考虑！"})
                玩家数据[数字id].最后操作="兑换乾元丹"
                return
        elseif 事件=="确定兑换乾元丹" and 玩家数据[数字id].最后操作=="兑换乾元丹" then
                玩家数据[数字id].最后操作=nil
                玩家数据[数字id].经脉:兑换乾元丹(数字id)
                return
        elseif 事件=="我卖大海龟（250两银子/只或者300两储备金/只）给你" then
                添加最后对话(数字id,"请选择你要出售的方式#R（会将所有未参战状态下的大海龟全部出售！）",{"250两银子卖给你","300两储备银子卖给你"})
                玩家数据[数字id].最后操作="出售海产"
                return
        elseif 事件=="我卖巨蛙（350两银子/只或者400两储备金/只）给你" then
                添加最后对话(数字id,"请选择你要出售的方式#R（会将所有未参战状态下的巨蛙全部出售！）",{"350两银子卖给你","400两储备银子卖给你"})
                玩家数据[数字id].最后操作="出售海产"
                return
        elseif 事件=="我卖海毛虫（500两银子/只或者600两储备金/只）给你" then
                添加最后对话(数字id,"请选择你要出售的方式#R（会将所有未参战状态下的海毛虫全部出售！）",{"500两银子卖给你","600两储备银子卖给你"})
                玩家数据[数字id].最后操作="出售海产"
                return
        elseif 事件=="宠物修炼任务" then
                local 任务id=玩家数据[数字id].角色:取任务(13)
                if 任务id==0 then
                      添加最后对话(数字id,"你没有这样的任务")
                      return
                elseif 任务数据[任务id].分类==11 then
                        任务处理类:完成宠修任务(数字id,任务id)
                        return
                elseif 任务数据[任务id].分类==12 or 任务数据[任务id].分类==13 or 任务数据[任务id].分类==14 then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="宠修物品"}
                        发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                        玩家数据[数字id].最后操作="宠修物品"
                        return
                elseif 任务数据[任务id].分类==15  then
                        local 临时选项=取符合修炼召唤兽选项(数字id)
                        if #临时选项==0 then
                            添加最后对话(数字id,"你身上没有我需要的召唤兽")
                            return
                        else
                            添加最后对话(数字id,"请选择你要上交的召唤兽:",临时选项)
                            玩家数据[数字id].最后操作="宠修bb"
                            return
                        end
                end
        elseif 事件=="打开仓库" then
                玩家数据[数字id].道具仓库:数据处理({文本="打开仓库"})
                return
        elseif 事件=="取消师门任务" then
                local 师门id=玩家数据[数字id].角色:取任务(111)
                if 师门id==0 then
                    添加最后对话(数字id,"请先领取门派任务#4")
                    return
                elseif 任务数据[师门id].门派师傅~=名称 then
                      添加最后对话(数字id,"你的门派师傅是"..任务数据[师门id].门派师傅)
                      return
                else
                    if 玩家数据[数字id].角色.师门间隔==nil then
                      玩家数据[数字id].角色.师门间隔=os.time()
                    elseif os.time()<玩家数据[数字id].角色.师门间隔 then
                      添加最后对话(数字id,"两次取消任务的间隔时间不能低于一分钟。")
                      return
                    end
                    玩家数据[数字id].角色.师门间隔=os.time()+60
                    玩家数据[数字id].角色:取消任务(师门id)
                    玩家数据[数字id].角色.数据.师门次数=0
                    if 任务数据[师门id].分类==5 or 任务数据[师门id].分类==6 or 任务数据[师门id].分类==7 then
                      地图处理类:删除单位(任务数据[师门id].地图编号,任务数据[师门id].编号)
                    end
                    任务数据[师门id]=nil
                    添加最后对话(数字id,"你的门派任务已经帮你取消了，同时任务环数也一同被清空。")
                    return
                end
        elseif 事件=="确定购买仓库"  and 共享货币[玩家数据[数字id].账号] then
                  local 数额=(#玩家数据[数字id].道具仓库.数据-3)*20+20
                  if #玩家数据[数字id].道具仓库.数据 >=40 then 常规提示(数字id,"每个角色最多只能购买40个仓库哟！！！！") return end
                  if 共享货币[玩家数据[数字id].账号]:扣除仙玉(数额,"购买仓库"..(#玩家数据[数字id].道具仓库.数据+1),数字id) then
                    玩家数据[数字id].道具仓库.数据[#玩家数据[数字id].道具仓库.数据+1]={}
                    常规提示(数字id,"购买仓库成功！")
                    发送数据(玩家数据[数字id].连接id,3540,{道具=玩家数据[数字id].道具仓库:索取仓库数据(1),总数=#玩家数据[数字id].道具仓库.数据})
                  end
                  return
        elseif 事件=="确定购买召唤兽仓库" and 共享货币[玩家数据[数字id].账号] then
              local 数额=(#玩家数据[数字id].召唤兽仓库.数据-1)*20+20
              if #玩家数据[数字id].召唤兽仓库.数据 >=10 then 常规提示(数字id,"每个角色最多只能购买10个召唤兽仓库哟！！！！") return end
              if 共享货币[玩家数据[数字id].账号]:扣除仙玉(数额,"购买召唤兽仓库"..(#玩家数据[数字id].召唤兽仓库.数据+1),数字id) then
                玩家数据[数字id].召唤兽仓库.数据[#玩家数据[数字id].召唤兽仓库.数据+1]={}
                常规提示(数字id,"购买召唤兽仓库成功！")
                发送数据(玩家数据[数字id].连接id,3526,{召唤兽=玩家数据[数字id].召唤兽.数据,召唤兽仓库总数=#玩家数据[数字id].召唤兽仓库.数据,召唤兽仓库数据=玩家数据[数字id].召唤兽仓库:索取召唤兽仓库数据(数字id,1)})
              end
              return
        elseif 事件=="确定购买共享仓库" and 共享仓库[玩家数据[数字id].账号] and 共享货币[玩家数据[数字id].账号]  then
                local 数额=(#共享仓库[玩家数据[数字id].账号].数据-3)*40+40
                if #共享仓库[玩家数据[数字id].账号].数据 >=60 then 常规提示(数字id,"共享仓库最多只能购买60个仓库哟！！！！") return end
                if 共享货币[玩家数据[数字id].账号]:扣除仙玉(数额,"购买仓库"..(#共享仓库[玩家数据[数字id].账号]+1),数字id) then
                    共享仓库[玩家数据[数字id].账号].数据[#共享仓库[玩家数据[数字id].账号].数据+1]={}
                    常规提示(数字id,"购买仓库成功！")
                    for k,v in pairs(共享仓库[玩家数据[数字id].账号].使用玩家) do
                        if 玩家数据[k]~=nil then
                          发送数据(玩家数据[k].连接id,166,{道具=共享仓库[玩家数据[数字id].账号]:索取仓库数据(1),总数=#共享仓库[玩家数据[数字id].账号].数据})
                        end
                    end
                end
                 return
        elseif 事件=="确定购买共享召唤兽仓库" and 共享仓库[玩家数据[数字id].账号] and 共享货币[玩家数据[数字id].账号] then
              local 数额=(#共享仓库[玩家数据[数字id].账号].召唤兽-1)*40+40
              if #共享仓库[玩家数据[数字id].账号].召唤兽 >=20 then 常规提示(数字id,"每个角色最多只能购买20个召唤兽仓库哟！！！！") return end
              if 共享货币[玩家数据[数字id].账号]:扣除仙玉(数额,"购买召唤兽仓库"..(#共享仓库[玩家数据[数字id].账号].召唤兽+1),数字id) then
                  共享仓库[玩家数据[数字id].账号].召唤兽[#共享仓库[玩家数据[数字id].账号].召唤兽+1]={}
                  常规提示(数字id,"购买召唤兽仓库成功！")
                   for k,v in pairs(共享仓库[玩家数据[数字id].账号].使用玩家) do
                      if 玩家数据[k]~=nil then
                        发送数据(玩家数据[k].连接id,165,{宝宝列表=玩家数据[k].召唤兽.数据,召唤兽仓库总数=#共享仓库[玩家数据[数字id].账号].召唤兽,召唤兽仓库数据=共享仓库[玩家数据[数字id].账号]:索取召唤兽仓库数据(1)})
                      end
                   end
              end
              return
        elseif 事件=="挑战世界BOOS" then
              if not 玩家数据[数字id].队伍 or 玩家数据[数字id].队伍 == 0 or not 玩家数据[数字id].队长 then
                  常规提示(数字id,"#Y该活动要求组队并且队长进行挑战操作")
                  return
              elseif 取等级要求(数字id,69)==false then
                  常规提示(数字id,"玩家等级小于69级无法参与此活动!")
                  return
              elseif 世界挑战.开启 == false then
                  常规提示(数字id,"#Y活动已经结束")
                  return
              elseif not 队伍数据[玩家数据[数字id].队伍] then
                     return
              else
                  for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                      local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                       if 世界挑战[临时id] ~= nil then
                           常规提示(数字id,"#Y"..玩家数据[临时id].角色.数据.名称.."已经完成过该任务,请等待刷新后再次参与")
                            return
                       end
                  end
                  for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                      local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                      世界挑战[临时id] = {伤害=0,id=临时id,名称=玩家数据[临时id].角色.数据.名称,等级=玩家数据[临时id].角色.数据.等级,门派=玩家数据[临时id].角色.数据.门派}
                  end
                  战斗准备类:创建战斗(数字id,100308,0)
              end
              return
        elseif 事件 == "确定强行PK" and 玩家数据[数字id].强P对象 ~= nil  then
          if 玩家数据[玩家数据[数字id].强P对象] ~= nil and 玩家数据[玩家数据[数字id].强P对象].战斗~=nil and 玩家数据[玩家数据[数字id].强P对象].战斗 ~= 0 then
              常规提示(数字id,"该玩家正在战斗中！")
              玩家数据[数字id].强P对象 = nil
          elseif 玩家数据[玩家数据[数字id].强P对象] ~= nil then
                  local 对方id = 玩家数据[数字id].强P对象
                  local id组 = 取id组(对方id)
                  for i=1,#id组 do
                      if 玩家数据[id组[i]].观战 ~= nil then
                         if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                            战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
                         else
                                玩家数据[id组[i]].战斗=0
                                玩家数据[id组[i]].观战=nil
                                发送数据(玩家数据[id组[i]].连接id,5505)
                          end
                      end
                  end
                  -- if 国庆数据[对方id]==nil then
                  --    国庆数据[对方id]={累积=0,当前=0}
                  -- end
                  -- if 国庆数据[对方id].累积>=3 then
                  --   常规提示(数字id,"该玩家已被强杀三次,不能发起强杀了")
                  --   return
                  -- end
                  战斗准备类:创建玩家战斗(数字id, 200008, 对方id, 1501)
                  玩家数据[数字id].强P对象 = nil
          else
              常规提示(数字id,"该玩家已经下线！")
              玩家数据[数字id].强P对象 = nil
          end
        end











    if 名称 =="袁天罡" then
        if 玩家数据[数字id].神兽成长资质提升~= nil then
            local 召唤兽编号 = self:查找神兽编号(数字id,事件)
            if 召唤兽编号~=nil then
                 local 取编号 = 玩家数据[数字id].召唤兽:取编号(召唤兽编号)
                 if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil  then
                    玩家数据[数字id].神兽成长资质提升=nil
                    return
                  end
                  local 一代名字 = 取一代神兽()
                  local 二代名字 = 取二代神兽()
                  local 三代名字 = 取三代神兽()
                  local 四代名字 = 取四代神兽()
                  local  一代数据= 玩家数据[数字id].召唤兽:置神兽对象(一代名字,一代名字)
                  local  二代数据= 玩家数据[数字id].召唤兽:置神兽对象(二代名字,二代名字)
                  local  三代数据= 玩家数据[数字id].召唤兽:置神兽对象(三代名字,三代名字)
                  local  四代数据= 玩家数据[数字id].召唤兽:置神兽对象(四代名字,四代名字)
                  local  取出价格 = 0
                  if 玩家数据[数字id].召唤兽.数据[取编号].成长 >=一代数据.成长 and 玩家数据[数字id].召唤兽.数据[取编号].成长 <二代数据.成长 then
                     取出价格 = 1
                  elseif 玩家数据[数字id].召唤兽.数据[取编号].成长 >=二代数据.成长 and 玩家数据[数字id].召唤兽.数据[取编号].成长 <三代数据.成长 then
                     取出价格 = 2
                  elseif 玩家数据[数字id].召唤兽.数据[取编号].成长 >=三代数据.成长 and 玩家数据[数字id].召唤兽.数据[取编号].成长 <四代数据.成长  then
                     取出价格 = 3
                  end
                  if 取出价格~=0 and 自定义数据.神兽提升数据~=nil and 自定义数据.神兽提升数据[取出价格]~=nil then
                      if 自定义数据.神兽提升数据[取出价格].货币类型~=nil and 自定义数据.神兽提升数据[取出价格].数量~=nil and 自定义数据.神兽提升数据[取出价格].物品数量~=nil then
                         发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="提升该召唤兽需求#R/"..自定义数据.神兽提升数据[取出价格].数量.."#W/"..自定义数据.神兽提升数据[取出价格].货币类型.."和#R/"..自定义数据.神兽提升数据[取出价格].物品数量.."#W/个神兜兜",选项={"我确定提升该召唤兽的资质"}})
                          玩家数据[数字id].神兽成长提升={扣除编号=取出价格,认证码=玩家数据[数字id].召唤兽.数据[取编号].认证码}
                      else
                          添加最后对话(数字id,"你所选的这只召唤兽无法继续提升了")
                      end
                  else
                      添加最后对话(数字id,"你所选的这只召唤兽无法继续提升了")
                  end
              end
              玩家数据[数字id].神兽成长资质提升= nil
              return
        elseif 玩家数据[数字id].神兽天生操作~= nil then
                  if 玩家数据[数字id].交易信息~=nil or 交易数据[数字id]~=nil then
                      玩家数据[数字id].神兽天生操作=nil
                      发送数据(数字id,7,"#Y/交易中无法使用改功能")
                      return 0
                  end
                  if 玩家数据[数字id].摊位数据~=nil then 玩家数据[数字id].神兽天生操作=nil 常规提示(数字id,"#Y摆摊情况下无法进行此操作") return end
                  if 玩家数据[数字id].神兽天生操作.道具id ==nil then 玩家数据[数字id].神兽天生操作=nil return end
                  if 玩家数据[数字id].神兽天生操作.道具id[1] ==nil or 玩家数据[数字id].神兽天生操作.道具id[2] ==nil or 玩家数据[数字id].神兽天生操作.道具id[3] ==nil then
                      玩家数据[数字id].神兽天生操作=nil
                      return
                  end
                  if 玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[1]] ==nil or 玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[2]] ==nil or 玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[3]] ==nil then
                      玩家数据[数字id].神兽天生操作=nil
                      return
                  end
                  if 玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[1]].名称 ~="神兜兜" or
                      玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[2]].名称 ~= "特殊魔兽要诀" or
                      玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[3]].名称 ~= "特殊魔兽要诀"  then
                      玩家数据[数字id].神兽天生操作=nil
                      return
                  end
                  if 玩家数据[数字id].神兽天生操作.事件 then
                      if 玩家数据[数字id].神兽天生操作.认证==nil then
                          玩家数据[数字id].神兽天生操作=nil
                          return
                      end
                      local 取编号 = 玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].神兽天生操作.认证)
                      if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil  then
                         玩家数据[数字id].神兽天生操作=nil
                         return
                      end
                      local 道具id = 玩家数据[数字id].神兽天生操作.道具id[1]
                      if 玩家数据[数字id].神兽天生操作.神兽==1 then
                            if 玩家数据[数字id].道具.数据[道具id].数量 ==nil then
                                  玩家数据[数字id].神兽天生操作=nil
                               return
                            end
                            if 玩家数据[数字id].道具.数据[道具id].数量<188 then
                              玩家数据[数字id].神兽天生操作=nil
                                  添加最后对话(数字id,"#Y/你的道具数量不够")
                               return
                            end
                            local 选项={}
                            for i=1,2 do
                              for n = 1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                 选项[#选项+1]={
                                 对话=玩家数据[数字id].召唤兽.数据[取编号].天生技能[n].."(换成)"..玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[i+1]].附带技能,
                                 技能编号=n,
                                 道具编号=i+1}
                              end
                            end
                            local 天生编号 = 0
                            local 道具编号 = 0
                            for i=1,#选项 do
                              if 事件 ==选项[i].对话 then
                                天生编号 = 选项[i].技能编号
                                道具编号= 选项[i].道具编号
                              end
                            end
                            if 天生编号~=0 and 道具编号~=0 then
                                for n=1,#玩家数据[数字id].召唤兽.数据[取编号].技能 do
                                   for i=1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                      if 玩家数据[数字id].召唤兽.数据[取编号].技能[n] == 玩家数据[数字id].召唤兽.数据[取编号].天生技能[i] then
                                        table.remove(玩家数据[数字id].召唤兽.数据[取编号].技能,n)
                                      end
                                  end
                                end
                               玩家数据[数字id].召唤兽.数据[取编号].天生技能[天生编号]=玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[道具编号]].附带技能
                               for i=1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                 玩家数据[数字id].召唤兽.数据[取编号].技能[#玩家数据[数字id].召唤兽.数据[取编号].技能+1] = 玩家数据[数字id].召唤兽.数据[取编号].天生技能[i]
                               end
                               玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 188
                               if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                  玩家数据[数字id].道具.数据[道具id]=nil
                               end
                               玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[2]]=nil
                               玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[3]]=nil
                               道具刷新(数字id)
                               发送数据(玩家数据[数字id].连接id,16,玩家数据[数字id].召唤兽.数据)
                               添加最后对话(数字id,"#Y/你的神兽天生技能已更换")

                            end
                      elseif 玩家数据[数字id].神兽天生操作.神兽==2 then
                             if 玩家数据[数字id].道具.数据[道具id].数量 ==nil then
                                    玩家数据[数字id].神兽天生操作=nil
                                 return
                              end
                              if 玩家数据[数字id].道具.数据[道具id].数量<888 then
                                玩家数据[数字id].神兽天生操作=nil
                                 添加最后对话(数字id,"#Y/你的道具数量不够")
                                 return
                              end
                               if #玩家数据[数字id].召唤兽.数据[取编号].技能>=24 then
                                玩家数据[数字id].神兽天生操作=nil
                                 添加最后对话(数字id,"#Y/你的这只召唤兽技能已达最大上限")
                                 return
                              end

                              if #玩家数据[数字id].召唤兽.数据[取编号].天生技能>=4 then
                                 玩家数据[数字id].神兽天生操作=nil
                                 return
                              end
                              local 选项={}
                              for i=1,2 do
                                选项[#选项+1]={对话=玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[i+1]].附带技能,道具编号=i+1}
                              end
                              local 道具编号 = 0
                              for i=1,#选项 do
                                  if 事件 ==选项[i].对话 then
                                    道具编号 =选项[i].道具编号
                                  end
                              end
                              if 道具编号~=0 then
                                   for n=1,#玩家数据[数字id].召唤兽.数据[取编号].技能 do
                                       for i=1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                          if 玩家数据[数字id].召唤兽.数据[取编号].技能[n] == 玩家数据[数字id].召唤兽.数据[取编号].天生技能[i] then
                                            table.remove(玩家数据[数字id].召唤兽.数据[取编号].技能,n)
                                          end
                                      end
                                   end
                                    玩家数据[数字id].召唤兽.数据[取编号].天生技能[#玩家数据[数字id].召唤兽.数据[取编号].天生技能+1] =玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[道具编号]].附带技能
                                    for i=1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                       玩家数据[数字id].召唤兽.数据[取编号].技能[#玩家数据[数字id].召唤兽.数据[取编号].技能+1] = 玩家数据[数字id].召唤兽.数据[取编号].天生技能[i]
                                    end
                                   玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 888
                                   if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                      玩家数据[数字id].道具.数据[道具id]=nil
                                   end
                                   玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[2]]=nil
                                   玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[3]]=nil
                                   道具刷新(数字id)
                                   发送数据(玩家数据[数字id].连接id,16,玩家数据[数字id].召唤兽.数据)
                                   添加最后对话(数字id,"#Y/你的神兽添加了一个天生技能")
                              end
                        end
                        玩家数据[数字id].神兽天生操作=nil
                        return
                  else
                        local 召唤兽编号 = self:查找神兽编号(数字id,事件)
                        if 召唤兽编号~=nil then
                            local 取编号 = 玩家数据[数字id].召唤兽:取编号(召唤兽编号)
                            if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil  then
                                玩家数据[数字id].神兽天生操作=nil
                                return
                            end
                            if 玩家数据[数字id].神兽天生操作.神兽==1 then
                                local 选项={}
                                for i=1,2 do
                                    for n = 1,#玩家数据[数字id].召唤兽.数据[取编号].天生技能 do
                                      选项[#选项+1]=玩家数据[数字id].召唤兽.数据[取编号].天生技能[n].."(换成)"..玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[i+1]].附带技能
                                    end
                                end
                                玩家数据[数字id].神兽天生操作.事件 = true
                                玩家数据[数字id].神兽天生操作.认证 = 玩家数据[数字id].召唤兽.数据[取编号].认证码
                                发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择兑换的技能",选项=选项})
                                 return
                            elseif 玩家数据[数字id].神兽天生操作.神兽==2 then
                                  local 选项={}
                                  for i=1,2 do
                                    选项[#选项+1]=玩家数据[数字id].道具.数据[玩家数据[数字id].神兽天生操作.道具id[i+1]].附带技能
                                  end
                                  玩家数据[数字id].神兽天生操作.事件 = true
                                  玩家数据[数字id].神兽天生操作.认证 = 玩家数据[数字id].召唤兽.数据[取编号].认证码
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择添加的技能",选项=选项})
                                  return
                            end
                        end
                  end
        elseif 玩家数据[数字id].变异召唤兽添加格子~= nil then
                if 玩家数据[数字id].交易信息~=nil or 交易数据[数字id]~=nil then
                    发送数据(数字id,7,"#Y/交易中无法使用改功能")
                    玩家数据[数字id].变异召唤兽添加格子= nil
                    return 0
                end
                if 玩家数据[数字id].摊位数据~=nil then 玩家数据[数字id].变异召唤兽添加格子= nil 常规提示(数字id,"#Y摆摊情况下无法进行此操作") return end
                local 选项={}
                for n=1,#玩家数据[数字id].召唤兽.数据 do
                    if 玩家数据[数字id].召唤兽.数据[n].种类 =="变异" then
                      选项[#选项+1]={对话=string.format("%s,等级:%s",玩家数据[数字id].召唤兽.数据[n].名称,玩家数据[数字id].召唤兽.数据[n].等级),编号=n}
                    end
                end
                for i=1,#选项 do

                      if 事件 == 选项[i].对话 then

                          if #玩家数据[数字id].召唤兽.数据[选项[i].编号].技能>=16 then
                             常规提示(数字id,"#Y/你的这只召唤兽技能已达最大上限")
                             return
                          end
                          if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.更改变异格子.货币类型,自定义数据.更改变异格子.数量,"更换变异格子",1) then
                            local 临时技能 = 取低级要诀()
                            玩家数据[数字id].召唤兽.数据[选项[i].编号].技能[#玩家数据[数字id].召唤兽.数据[选项[i].编号].技能+1] = 临时技能
                            常规提示(数字id,"#Y/你的#R/"..玩家数据[数字id].召唤兽.数据[选项[i].编号].名称.."#Y/添加了一个技能格子")
                            玩家数据[数字id].变异召唤兽添加格子 = nil
                            道具刷新(数字id)
                          end
                          return
                      end
                end
        end



    elseif 名称=="门派传送人" or (名称=="新手门派传送人" and 玩家数据[数字id].角色.数据.门派=="无门派" ) then
            地图处理类:门派传送(数字id,事件)
            return
    elseif 名称=="超级巫医" then
          if 事件=="我的召唤兽受伤了，请帮我救治一下吧" then
              NPC商业栏:治疗召唤兽气血(玩家数据[数字id].连接id,数字id)
          elseif 事件=="我的召唤兽忠诚度降低了，请帮我驯养一下吧" then
                NPC商业栏:治疗召唤兽忠诚(玩家数据[数字id].连接id,数字id)
          elseif 事件=="我要同时补满召唤兽的气血、魔法和忠诚" then
                  NPC商业栏:治疗召唤兽全体(玩家数据[数字id].连接id,数字id)
          elseif 事件=="我要给召唤兽降低等级，请帮我降低一下吧" then
                  local 选项={}
                  for n=1,#玩家数据[数字id].召唤兽.数据 do
                        选项[#选项+1]=string.format("%s,等级:%s",玩家数据[数字id].召唤兽.数据[n].名称,玩家数据[数字id].召唤兽.数据[n].等级)
                  end
                  if #选项 < 1 then
                      添加最后对话(数字id,"你没有可以选择的召唤兽")
                      玩家数据[数字id].宝宝降级 = nil
                      return
                  end
                  发送数据(玩家数据[数字id].连接id,1501,{名称="超级巫医",模型="男人_巫医",对话="请选择需要降级的召唤兽,每次降低10级,每次需求费用50万",选项=选项})
                  玩家数据[数字id].宝宝降级 =  true
          elseif 事件=="召唤兽洗点" then
                  玩家数据[数字id].召唤兽:洗点处理(数字id)
          elseif 玩家数据[数字id].宝宝降级 then
                  local 认证码 = self:查找判断宝宝编号(数字id,事件)
                  if 认证码 then
                      玩家数据[数字id].召唤兽:降级处理(数字id,认证码)
                  end
                  玩家数据[数字id].宝宝降级 = nil
          end
          return
    elseif 名称 == "物件_打铁炉" then
            if 事件=="打造些东西"  then
                发送数据(id,14,玩家数据[数字id].道具:索要道具1(数字id))
            elseif 事件=="打造任务" then
                    if 玩家数据[数字id].角色:取任务(5)~=0 then
                       玩家数据[数字id].给予数据={类型=1,id=0,事件="打造任务提交"}
                       发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="物件_打铁炉",类型="NPC",等级="无"})
                    else
                        常规提示(数字id,"#Y/你还没有任务赶紧去领取吧")
                    end
            elseif 事件=="装备回收" then
                  玩家数据[数字id].给予数据={类型=1,id=0,事件="装备回收"}
                  发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="物件_打铁炉",类型="NPC",等级="无"})
            elseif 事件 =="购买熟练度" then
                    发送数据(id,1501,{名称="物件_打铁炉",模型="物件_打铁炉",对话="你可以在我这里花1亿购买1000点熟练度",选项={"打造技巧","裁缝技巧","淬灵之术","炼 金 术"}})
            elseif 事件 =="打造技巧" then
                    if 玩家数据[数字id].角色.数据.银子<100000000 then
                      常规提示(数字id,"#Y/你的银子不够1亿两")
                      return
                    end
                    玩家数据[数字id].角色.数据.银子 = 玩家数据[数字id].角色.数据.银子 -100000000
                    玩家数据[数字id].角色.数据.打造熟练度.打造技巧 = 玩家数据[数字id].角色.数据.打造熟练度.打造技巧 + 1000
                    常规提示(数字id,"你的#R/打造技巧#Y/熟练度增加了#R/1000#Y/点")
                    道具刷新(数字id)
            elseif 事件 =="裁缝技巧" then
                    if 玩家数据[数字id].角色.数据.银子<100000000 then
                      常规提示(数字id,"#Y/你的银子不够1亿两")
                      return
                    end
                    玩家数据[数字id].角色.数据.银子 = 玩家数据[数字id].角色.数据.银子 -100000000
                    玩家数据[数字id].角色.数据.打造熟练度.裁缝技巧 = 玩家数据[数字id].角色.数据.打造熟练度.裁缝技巧 + 1000
                    常规提示(数字id,"你的#R/裁缝技巧#Y/熟练度增加了#R/1000#Y/点")
                    道具刷新(数字id)
            elseif 事件 =="淬灵之术" then
                    if 玩家数据[数字id].角色.数据.银子<100000000 then
                      常规提示(数字id,"#Y/你的银子不够1亿两")
                      return
                    end
                    玩家数据[数字id].角色.数据.银子 = 玩家数据[数字id].角色.数据.银子 -100000000
                    玩家数据[数字id].角色.数据.打造熟练度.淬灵之术 = 玩家数据[数字id].角色.数据.打造熟练度.淬灵之术 + 1000
                    常规提示(数字id,"你的#R/淬灵之术#Y/熟练度增加了#R/1000#Y/点")
                    道具刷新(数字id)
            elseif 事件 =="炼 金 术" then
                    if 玩家数据[数字id].角色.数据.银子<100000000 then
                      常规提示(数字id,"#Y/你的银子不够1亿两")
                      return
                    end
                    玩家数据[数字id].角色.数据.银子 = 玩家数据[数字id].角色.数据.银子 -100000000
                    玩家数据[数字id].角色.数据.打造熟练度.炼金术 = 玩家数据[数字id].角色.数据.打造熟练度.炼金术 + 1000
                    常规提示(数字id,"你的#R/炼金术#Y/熟练度增加了#R/1000#Y/点")
                    道具刷新(数字id)
            elseif 事件 == "查看熟练度"  then
                   添加最后对话(数字id,"你当前技能熟练度为:\n\n打造技巧:"..玩家数据[数字id].角色.数据.打造熟练度.打造技巧.."\n裁缝技巧:"..玩家数据[数字id].角色.数据.打造熟练度.裁缝技巧.."\n淬灵之术:"..玩家数据[数字id].角色.数据.打造熟练度.淬灵之术.."\n炼 金 术:"..玩家数据[数字id].角色.数据.打造熟练度.炼金术)
                  -- 玩家数据[数字id].最后对话.名称 = "物件_打铁炉"
                  -- 玩家数据[数字id].最后对话.模型 = "物件_打铁炉"
            elseif 事件 == "查看额外加成"  then
                    添加最后对话(数字id,"你下次打造装备额外加成:\n\n双加加成:"..玩家数据[数字id].角色.数据.打造加成.双加.."%\n特技加成:"..玩家数据[数字id].角色.数据.打造加成.特技.."%\n特效加成:"..玩家数据[数字id].角色.数据.打造加成.特效.."%\n赐福加成:"..玩家数据[数字id].角色.数据.打造加成.赐福.."%")
            elseif 事件 == "神器相关" then
                --添加最后对话(数字id,"你想进行哪种操作呢？",{"炼制灵犀之屑","合成灵犀玉","更换神器五行"})
                    发送数据(id,1501,{名称="物件_打铁炉",模型="物件_打铁炉",对话="你想进行哪种操作呢？",选项={"炼制灵犀之屑","合成灵犀玉","更换神器五行"}})
            end
            return

     elseif 名称=="内丹自选礼包" and 玩家数据[数字id].内丹自选礼包 then
                if not 玩家数据[数字id].内丹自选礼包.认证码 or not 玩家数据[数字id].内丹自选礼包.道具id then 玩家数据[数字id].内丹自选礼包=nil return end
                local 取编号 = 玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].内丹自选礼包.认证码)
                if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil  then
                    玩家数据[数字id].内丹自选礼包=nil
                    return
                end
               if  玩家数据[数字id].内丹自选礼包.道具id==0 or 玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id] == nil then 玩家数据[数字id].内丹自选礼包=nil return end

               local 内丹事件 = {"迅敏","狂怒","阴伤","静岳","擅咒","灵身","矫健","深思","钢化","坚甲","慧心","撞击","无畏","愤恨","淬毒","狙刺","连环","圣洁","灵光","腾挪劲","玄武躯","龙胄铠","玉砥柱","碎甲刃","舍身击","通灵法","双星爆","催心浪","隐匿击","生死决","血债偿","神机步","电魂闪"}
               if not  玩家数据[数字id].内丹自选礼包.事件  then
                        local  选项 ={}
                        for i=1,玩家数据[数字id].召唤兽.数据[取编号].内丹.内丹上限 do
                              if 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i].技能~=nil then
                                  if 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i].等级<5 then
                                     选项[#选项+1]={事件="将"..玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i].技能.."升到满层",编号=i}
                                  end
                                  选项[#选项+1]={事件="更换内丹"..玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i].技能,编号=i}
                                  for n=1,#内丹事件 do
                                      if 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[i].名称==内丹事件[n] then
                                         table.remove(内丹事件,n)
                                      end
                                  end
                              else
                                 选项[#选项+1]={事件="选择内丹格子"..i,编号=i}
                              end
                        end
                        local 选择内丹 = 0
                        for i=1,#选项 do
                            if 事件== 选项[i].事件 then
                                  选择内丹=选项[i].编号
                            end
                        end
                        if 选择内丹~=0 then
                           if string.find(事件,"升到满层")~=nil and 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹]~=nil and 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹].等级~=nil and 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹].等级<5 then
                                  玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹].等级=5
                                  常规提示(数字id,"恭喜你的"..玩家数据[数字id].召唤兽.数据[取编号].名称.."#R/"..玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹].技能.."#Y/升到第#R/5#Y/层")
                                  if 玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量 then
                                          玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量=玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量-1
                                          if 玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量<=0 then
                                              玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id]=nil
                                              玩家数据[数字id].角色.数据[玩家数据[数字id].内丹自选礼包.包裹类型][玩家数据[数字id].内丹自选礼包.道具格子]=nil
                                          end
                                  else
                                        玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id]=nil
                                        玩家数据[数字id].角色.数据[玩家数据[数字id].内丹自选礼包.包裹类型][玩家数据[数字id].内丹自选礼包.道具格子]=nil
                                  end
                                 道具刷新(数字id)
                           else
                             if 选择内丹~=1 and not 玩家数据[数字id].召唤兽.数据[取编号].内丹数据[选择内丹-1] then
                                  常规提示(数字id,"请安顺序升级内丹")
                                  玩家数据[数字id].内丹自选礼包=nil
                                  return
                              end
                              玩家数据[数字id].内丹自选礼包.事件 = 选择内丹
                              发送数据(玩家数据[数字id].连接id,1501,{名称="内丹自选礼包",模型=玩家数据[数字id].角色.模型,对话="请选择更换的内丹",选项=内丹事件})
                             return
                           end
                        end
                else
                        if 玩家数据[数字id].内丹自选礼包.事件>玩家数据[数字id].召唤兽.数据[取编号].内丹.内丹上限 then 玩家数据[数字id].内丹自选礼包=nil return end
                        local 检测事件 =false
                        for i=1,#内丹事件 do
                            if 事件==内丹事件[i] then
                                检测事件 =true
                                break
                            end
                        end
                        if 检测事件 then
                               玩家数据[数字id].召唤兽.数据[取编号].内丹数据[玩家数据[数字id].内丹自选礼包.事件]={技能=事件,等级=5}
                               玩家数据[数字id].召唤兽.数据[取编号].内丹.可用内丹 = 玩家数据[数字id].召唤兽.数据[取编号].内丹.内丹上限-#玩家数据[数字id].召唤兽.数据[取编号].内丹数据
                                常规提示(数字id,"恭喜你的"..玩家数据[数字id].召唤兽.数据[取编号].名称.."#R/"..事件.."#Y/升到第#R/5#Y/层")
                               if 玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量 then
                                      玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量=玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量-1
                                      if 玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id].数量<=0 then
                                          玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id]=nil
                                          玩家数据[数字id].角色.数据[玩家数据[数字id].内丹自选礼包.包裹类型][玩家数据[数字id].内丹自选礼包.道具格子]=nil
                                      end
                               else
                                    玩家数据[数字id].道具.数据[玩家数据[数字id].内丹自选礼包.道具id]=nil
                                    玩家数据[数字id].角色.数据[玩家数据[数字id].内丹自选礼包.包裹类型][玩家数据[数字id].内丹自选礼包.道具格子]=nil
                               end
                             道具刷新(数字id)
                        end
                end

               玩家数据[数字id].内丹自选礼包=nil
               return
    elseif 名称 and  string.find(名称,"神兽自选礼包") and 玩家数据[数字id].神兽自选礼包 then
            if 玩家数据[数字id].召唤兽:是否携带上限() then
                 常规提示(数字id,"#Y/你的召唤兽已满无法携带更多")
                 玩家数据[数字id].神兽自选礼包=nil
                 return
            end
            local 道具id =玩家数据[数字id].神兽自选礼包.道具id
            if not 道具id  or 道具id==0 then  玩家数据[数字id].神兽自选礼包=nil return end
            if not 玩家数据[数字id].道具.数据[道具id] or 玩家数据[数字id].道具.数据[道具id] ==0 then  玩家数据[数字id].神兽自选礼包=nil return end
            if 玩家数据[数字id].神兽自选礼包.事件=="一代神兽" then
                  local 类型 = 取一代神兽()
                  if 事件=="一代神兽(物理型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(物理型)")
                  elseif 事件=="一代神兽(法术型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(法术型)")
                  end
                  常规提示(数字id,"#Y/你获得了一只#R/"..类型)
            elseif 玩家数据[数字id].神兽自选礼包.事件=="二代神兽" then
                  local 类型 = 取二代神兽()
                  if 事件=="二代神兽(物理型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(物理型)")
                  elseif 事件=="二代神兽(法术型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(法术型)")
                  end
                  常规提示(数字id,"#Y/你获得了一只#R/"..类型)
            elseif 玩家数据[数字id].神兽自选礼包.事件=="三代神兽" then
                  local 类型 = 取三代神兽()
                  if 事件=="三代神兽(物理型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(物理型)")
                  elseif 事件=="三代神兽(法术型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(法术型)")
                  end
                  常规提示(数字id,"#Y/你获得了一只#R/"..类型)
            elseif 玩家数据[数字id].神兽自选礼包.事件=="四代神兽" then
                  local 类型 = 取四代神兽()
                  if 事件=="四代神兽(物理型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(物理型)")
                  elseif 事件=="四代神兽(法术型)" then
                       玩家数据[数字id].召唤兽:添加召唤兽(类型,类型,"神兽","(法术型)")
                  end
                  常规提示(数字id,"#Y/你获得了一只#R/"..类型)

            end
            玩家数据[数字id].道具.数据[道具id].数量=玩家数据[数字id].道具.数据[道具id].数量-1
            if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
               玩家数据[数字id].道具.数据[道具id]=nil
            end
            道具刷新(数字id)
            玩家数据[数字id].神兽自选礼包=nil
             return
    elseif 名称 =="五行大师" and 玩家数据[数字id].更换宝宝五行~=nil then
              local 召唤兽编号 = self:查找判断宝宝编号(数字id,事件)
              if 召唤兽编号~=nil then
                  local 取编号 = 玩家数据[数字id].召唤兽:取编号(召唤兽编号)
                  if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil then 玩家数据[数字id].更换宝宝五行=nil return end
                  if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.更换宝宝五行.货币类型,自定义数据.更换宝宝五行.数量,"更换宝宝五行",1) then
                       玩家数据[数字id].召唤兽.数据[取编号].五行=取五行()
                       常规提示(数字id,"#Y你所选的这只召唤兽五行已经更换为【"..玩家数据[数字id].召唤兽.数据[取编号].五行.."】")
                  end
              end
              玩家数据[数字id].更换宝宝五行 = nil
              return
    elseif 名称=="皇宫护卫" and 玩家数据[数字id].护卫对话==1 and 事件=="是的，我要继续平定安邦" then
            玩家数据[数字id].护卫对话=nil
            if 玩家数据[数字id].角色:取任务(6)~=0 then
               发送数据(玩家数据[数字id].连接id,1501,{名称="皇宫护卫",模型="护卫",对话="你已经领取过赏金任务了#24"})
               return
            elseif 玩家数据[数字id].角色.数据.等级<15 then
               发送数据(玩家数据[数字id].连接id,1501,{名称="皇宫护卫",模型="护卫",对话="只有大于15级的玩家才可领取赏金任务，你就不要来捣乱了#4"})
               return
            end
            任务处理类:设置赏金任务(数字id)
            return
    elseif 名称=="赵捕头" and 玩家数据[数字id].赵捕头对话==1 and 事件=="是的，我要继续领取" then
            玩家数据[数字id].赵捕头对话=nil
            if 玩家数据[数字id].角色:取任务(66)~=0 then
              发送数据(玩家数据[数字id].连接id,1501,{名称="赵捕头",模型="男人_衙役",对话="你已经领取过赏金任务了#24"})
              return
            elseif 玩家数据[数字id].角色.数据.等级>=15 then
              发送数据(玩家数据[数字id].连接id,1501,{名称="赵捕头",模型="男人_衙役",对话="只有15级及以下的玩家才可领取新手任务，你就不要来捣乱了#4"})
              return
            end
           任务处理类:设置新手任务(数字id)
           return
    elseif 名称=="钟馗" and 玩家数据[数字id].钟馗对话==1 and 事件=="把我送回来" then
            玩家数据[数字id].钟馗对话=nil
            地图处理类:跳转地图(数字id,1122,56,63)
            return
    elseif 名称=="黑无常" and 玩家数据[数字id].黑无常对话==1 and 事件=="请送我回来" then
          玩家数据[数字id].黑无常对话=nil
          地图处理类:跳转地图(数字id,1125,30,24)
          return
    elseif 名称=="法宝灵气补充" and 玩家数据[数字id].补充灵气id~=nil then
            if 玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id]==nil then 玩家数据[数字id].补充灵气id=nil return end
            if 事件=="1级法宝" then
                if 玩家数据[数字id].角色.数据.银子<500000 then
                    玩家数据[数字id].补充灵气id = nil
                    添加最后对话(数字id,"你的银子不够")
                    return
                end
                玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-500000
                玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].魔法=取灵气上限(玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].分类)
                添加最后对话(数字id,"补充法宝灵气成功！")
            elseif 事件=="2级法宝" then
                    if 玩家数据[数字id].角色.数据.银子<1000000 then
                       玩家数据[数字id].补充灵气id = nil
                       添加最后对话(数字id,"你的银子不够")
                        return
                    end
                    玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-1000000
                    玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].魔法=取灵气上限(玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].分类)
                    添加最后对话(数字id,"补充法宝灵气成功！")
            elseif 事件=="3级法宝" then
                    if 玩家数据[数字id].角色.数据.银子<2000000 then
                       玩家数据[数字id].补充灵气id = nil
                       添加最后对话(数字id,"你的银子不够")
                       return
                    end
                    玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-2000000
                    玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].魔法=取灵气上限(玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].分类)
                    添加最后对话(数字id,"补充法宝灵气成功！")
            elseif 事件=="4级法宝" then
                    if 玩家数据[数字id].角色.数据.银子<4000000 then
                       玩家数据[数字id].补充灵气id = nil
                       添加最后对话(数字id,"你的银子不够")
                       return
                    end
                    玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-4000000
                    玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].魔法=取灵气上限(玩家数据[数字id].道具.数据[玩家数据[数字id].补充灵气id].分类)
                    添加最后对话(数字id,"补充法宝灵气成功！")
            else
               添加最后对话(数字id,"该法宝无法添加灵气。")
            end
            玩家数据[数字id].补充灵气id = nil
            道具刷新(数字id)
            玩家数据[数字id].道具:索要法宝(玩家数据[数字id].连接id,数字id)
            return
    elseif 名称=="激活符石" and 事件=="确定"  then
            玩家数据[数字id].道具:激活符石(数字id)
            玩家数据[数字id].激活符石=nil
            return
    elseif 名称=="翻转星石" and 事件=="消耗100体力进行翻转" then
             玩家数据[数字id].道具:装备翻转星石(数字id)
             return
    elseif 名称=="点化装备套装" and 事件~="我暂时先不点化了" then
            local 强化石 = {"青龙石","朱雀石","玄武石","白虎石"}
            local 套装类型 = 玩家数据[数字id].点化套装数据.套装
            local 宝珠id = 玩家数据[数字id].点化套装数据.宝珠数据
            local 装备编号 = 玩家数据[数字id].角色.数据.道具[玩家数据[数字id].点化套装数据.装备]
            local 装备等级 = 玩家数据[数字id].道具.数据[装备编号].级别限制
            local 消耗石头 = 玩家数据[数字id].道具.数据[装备编号].分类
            local 强化石数据 = {青龙石=0,朱雀石=0,玄武石=0,白虎石=0}
            if 消耗石头 == 5 or 消耗石头 == 6 then
              消耗石头 = 3
            end
            强化石数据[强化石[消耗石头]] = math.floor(装备等级/10)
            if 玩家数据[数字id].点化套装数据.装备 == nil or 玩家数据[数字id].点化套装数据.套装==0 or 玩家数据[数字id].点化套装数据.套装==nil then
              常规提示(数字id,"道具数据异常，请重新打开界面进行操作。")
              return
            end
            if 玩家数据[数字id].道具.数据[宝珠id] == nil and 玩家数据[数字id].道具.数据[宝珠id].名称~="附魔宝珠" then
              常规提示(数字id,"道具数据异常，请重新打开界面进行操作。")
              return
            end
            if 玩家数据[数字id].角色.数据.当前经验 < 装备等级*3000 then
              常规提示(数字id,"您的经验不足，无法进行点化。")
              return
            end
            if 玩家数据[数字id].角色.数据.银子 < 装备等级*5000 then
              常规提示(数字id,"您的银子不足，无法进行点化。")
              return
            end
            if 玩家数据[数字id].道具.数据[宝珠id].级别限制<装备等级 then
               常规提示(数字id,"你的附魔宝珠等级不够。")
              return
            end
            if 玩家数据[数字id].道具:判断强化石(强化石数据) == false then
              常规提示(数字id,"您的"..强化石[消耗石头].."不足，无法进行点化。")
              return
            end
            玩家数据[数字id].道具.数据[宝珠id] = nil
            玩家数据[数字id].道具:删除强化石(强化石数据)
            if 套装类型 == 1 then
              玩家数据[数字id].道具.数据[装备编号].套装效果={"变身术之",事件}
            elseif 套装类型 == 2 then
              玩家数据[数字id].道具.数据[装备编号].套装效果={"追加法术",事件}
            elseif 套装类型 == 4 then
              玩家数据[数字id].道具.数据[装备编号].套装效果={"附加状态",事件}
            end
            玩家数据[数字id].道具.数据[装备编号].祈福值 = nil
            玩家数据[数字id].角色:扣除经验(装备等级*3000,"点化套装",1)
            玩家数据[数字id].角色:扣除银子(装备等级*5000,"点化套装",1)
            玩家数据[数字id].点化套装数据 = nil
            常规提示(数字id,"恭喜你祈福成功。")
            发送数据(玩家数据[数字id].连接id,3549)
            道具刷新(数字id)
            return
    elseif 名称 ==  玩家数据[数字id].角色.数据.名称 then
           if 玩家数据[数字id].交易信息~=nil or 交易数据[数字id]~=nil then
                发送数据(id,7,"#Y/交易中无法使用改功能")
               return 0
           end
          if 玩家数据[数字id].摊位数据~=nil then 常规提示(数字id,"#Y摆摊情况下无法进行此操作") return end
          if 玩家数据[数字id].遗忘剧情技能~=nil and 共享货币[玩家数据[数字id].账号] then
              if 共享货币[玩家数据[数字id].账号]:扣除仙玉(5000,"遗忘剧情技能",数字id) then
                  local 剧情编号 = 0
                  for i=1,#玩家数据[数字id].角色.数据.剧情技能 do
                    if 事件 == 玩家数据[数字id].角色.数据.剧情技能[i].名称 then
                      剧情编号 = i
                    end
                  end
                  if 剧情编号~=0 and 玩家数据[数字id].角色.数据.剧情技能[剧情编号]~=nil then
                    local 给与剧情点 = 剧情点数量查找(事件) * 玩家数据[数字id].角色.数据.剧情技能[剧情编号].等级
                     table.remove(玩家数据[数字id].角色.数据.剧情技能,剧情编号)
                     玩家数据[数字id].角色.数据.剧情点 =玩家数据[数字id].角色.数据.剧情点+给与剧情点
                     常规提示(数字id,"#Y/你遗忘了#R/"..事件.."#Y/技能,返还剧情点#R/"..给与剧情点.."#Y/点")
                  end
              end
              玩家数据[数字id].遗忘剧情技能 =nil
              return 0
          elseif 玩家数据[数字id].设置飞行快捷键~=nil then
                 if 玩家数据[数字id].角色.数据.坐骑==nil then
                     常规提示(数字id,"#Y你没有乘骑坐骑")
                  elseif not 玩家数据[数字id].角色.数据.坐骑.祥瑞 then
                         常规提示(数字id,"#Y你的这只坐骑无法设置飞行技能")
                  else
                      local 快捷位置 = 0
                      if 事件=="F1" then
                            快捷位置 = 1
                      elseif 事件=="F2" then
                            快捷位置 = 2
                      elseif 事件=="F3" then
                            快捷位置 = 3
                      elseif 事件=="F4" then
                            快捷位置 = 4
                      elseif 事件=="F5" then
                            快捷位置 = 5
                      elseif 事件=="F6" then
                            快捷位置 = 6
                      end
                      if 快捷位置> 0 then
                          local  传入数据 = {名称="飞行技能",类型=4,位置=快捷位置}
                          玩家数据[数字id].角色:设置快捷技能(传入数据)
                      end
                  end
                  玩家数据[数字id].设置飞行快捷键 = nil
                  return 0
            elseif 玩家数据[数字id].制作家具 and 事件=="现在就制造"  then
                  if 玩家数据[数字id].道具.数据[玩家数据[数字id].制作家具.道具id].名称~="设计图" then
                        玩家数据[数字id].制作家具=nil
                        常规提示(数字id,"#Y数据错误")
                        return
                  elseif 玩家数据[数字id].道具.数据[玩家数据[数字id].制作家具.道具id].类型~=玩家数据[数字id].制作家具.名称 then
                         玩家数据[数字id].制作家具=nil
                         常规提示(数字id,"#Y数据错误")
                         return
                  end
                  if 玩家数据[数字id].角色.数据.体力< 家具消耗[玩家数据[数字id].制作家具.名称].体力 then
                      玩家数据[数字id].制作家具=nil
                      常规提示(数字id,"#Y少侠,当前体力不够，图纸制造失败")
                      return
                  end
                  玩家数据[数字id].角色.数据.体力 =玩家数据[数字id].角色.数据.体力 -  家具消耗[玩家数据[数字id].制作家具.名称].体力
                  体活刷新(数字id)
                  玩家数据[数字id].道具:给予道具(数字id,玩家数据[数字id].制作家具.名称)
                  常规提示(数字id,"#Y你获得了#R/"..玩家数据[数字id].制作家具.名称)
                  玩家数据[数字id].道具.数据[玩家数据[数字id].制作家具.道具id]=nil
                  玩家数据[数字id].角色.数据.道具[玩家数据[数字id].制作家具.格子]=nil
                  道具刷新(数字id)
                  玩家数据[数字id].制作家具=nil
                  return 0
              elseif 玩家数据[数字id].好友分组 then

                     local 分组 = 1
                     if 事件=="自定义分组2" then
                      分组 = 2
                     elseif 事件=="自定义分组3" then
                      分组 = 3
                     elseif 事件=="自定义分组4" then
                      分组 = 4
                     elseif 事件=="自定义分组5" then
                      分组 = 5
                     elseif 事件=="自定义分组6" then
                      分组 = 6
                     elseif 事件=="自定义分组7" then
                      分组 = 7
                     elseif 事件=="自定义分组8" then
                      分组 = 8
                     elseif 事件=="自定义分组9" then
                      分组 = 9
                     elseif 事件=="自定义分组10" then
                      分组 = 10
                     elseif 事件=="屏蔽名单" then
                      分组 = 11
                     end
                     if 分组==11 then
                         玩家数据[数字id].好友:加入黑名单(玩家数据[数字id].好友分组)
                     else
                        if 玩家数据[数字id].好友.数据.黑名单[玩家数据[数字id].好友分组] then
                           玩家数据[数字id].好友:拉出黑名单(玩家数据[数字id].好友分组)
                        end
                        玩家数据[数字id].好友:更换分组(玩家数据[数字id].好友分组,分组)
                     end
                    玩家数据[数字id].好友分组=nil

                    return 0
              elseif 玩家数据[数字id].提取词条 then
                      local 装备id  = 玩家数据[数字id].提取词条.装备id
                      local 道具id  = 玩家数据[数字id].提取词条.道具id
                      local 道具格子  = 玩家数据[数字id].提取词条.道具格子
                      if not 装备id or not 道具id  then 玩家数据[数字id].提取词条=nil return end
                      if 玩家数据[数字id].道具.数据[道具id]==nil or 玩家数据[数字id].道具.数据[装备id]==nil then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                      if 玩家数据[数字id].道具.数据[道具id].名称~=玩家数据[数字id].提取词条.名称 then 玩家数据[数字id].提取词条=nil  常规提示(数字id,"#Y/数据错误") return end
                      if 玩家数据[数字id].道具.数据[道具id].数量==nil then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                      if 玩家数据[数字id].道具.数据[道具id].数量<=0 then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误")  return end
                      if not 玩家数据[数字id].道具.数据[装备id].鉴定 then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/未鉴定的装备无法使用") return end
                      if 玩家数据[数字id].道具.数据[装备id].分类>6 then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/只有装备才可以使用该道具") return end
                      local 装备数据 =DeepCopy(玩家数据[数字id].道具.数据[装备id])
                      if 装备数据.装备境界 then
                          local 装备词条 = 装备数据.装备境界.词条
                          if 玩家数据[数字id].提取词条.名称=="鸿蒙仙宝" then
                              if (装备数据.装备境界.品质=="传说" or 装备数据.装备境界.品质=="神话") and 装备词条 and 境界属性[事件] then
                                 if not 境界属性[事件].分类[装备数据.分类] then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/该装备无法附魔这个词条") return end
                                   if 装备数据.装备境界.洗练值<900 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/附魔该装备需要900仙宝值") return end
                                   玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 900
                                   玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                   if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                       玩家数据[数字id].道具.数据[道具id]=nil
                                   end
                                   local 临时数额 = 境界属性[事件][装备数据.装备境界.品质][2]
                                   玩家数据[数字id].道具.数据[装备id].装备境界.词条={}
                                   玩家数据[数字id].道具.数据[装备id].装备境界.词条[1]={类型=事件,数额=临时数额}
                                   玩家数据[数字id].道具.数据[装备id].装备境界.词条[2]={类型=事件,数额=math.floor(临时数额/2)}
                                   玩家数据[数字id].道具.数据[装备id].装备境界.词条[3]={类型=事件,数额=math.floor(临时数额/2)}
                                   玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣 = 境界属性[事件].共鸣
                                   玩家数据[数字id].提取词条=nil
                                   道具刷新(数字id)
                              else
                                  玩家数据[数字id].提取词条=nil
                                  常规提示(数字id,"#Y/该装备无法使用")
                                  return

                              end
                          elseif 玩家数据[数字id].提取词条.名称=="鸿蒙神宝" then
                               if 装备词条 then
                                    if 装备词条[1] and 事件=="提取第一词条:"..装备词条[1].类型 then
                                        if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/提取词条需100仙宝值") return end
                                        玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                        玩家数据[数字id].道具.数据[装备id].装备境界.词条[1]=nil
                                        玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣=false
                                        玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                        if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                              玩家数据[数字id].道具.数据[道具id]=nil
                                        end
                                        local 临时词条 = 装备数据.装备境界.词条[1].类型
                                        local 临时数额 = 装备数据.装备境界.词条[1].数额
                                        玩家数据[数字id].道具:给予道具(数字id,"鸿蒙原石",1,{类型=临时词条,数额=临时数额})
                                        常规提示(数字id,"#Y/你获得了#R"..临时词条.."#Y词条数额为#G"..临时数额.."#Y的#R鸿蒙原石#Y1个,该装备词条已消失")
                                        玩家数据[数字id].提取词条=nil
                                        道具刷新(数字id)
                                    elseif 装备词条[2] and 事件=="提取第二词条:"..装备词条[2].类型 then
                                            if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/提取词条需100仙宝值") return end
                                            玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                            玩家数据[数字id].道具.数据[装备id].装备境界.词条[2]=nil
                                            玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣=false
                                            玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                            if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                                  玩家数据[数字id].道具.数据[道具id]=nil
                                            end
                                            local 临时词条 = 装备数据.装备境界.词条[2].类型
                                            local 临时数额 = 装备数据.装备境界.词条[2].数额*2
                                            玩家数据[数字id].道具:给予道具(数字id,"鸿蒙原石",1,{类型=临时词条,数额=临时数额})
                                            常规提示(数字id,"#Y/你获得了#R"..临时词条.."#Y词条数额为#G"..临时数额.."#Y的#R鸿蒙原石#Y1个,该装备词条已消失")
                                            玩家数据[数字id].提取词条=nil
                                            道具刷新(数字id)
                                    elseif 装备词条[3] and 事件=="提取第三词条:"..装备词条[3].类型 then
                                            if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/提取词条需100仙宝值") return end
                                            玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                            玩家数据[数字id].道具.数据[装备id].装备境界.词条[3]=nil
                                            玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣=false
                                            玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                            if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                                  玩家数据[数字id].道具.数据[道具id]=nil
                                            end
                                            local 临时词条 = 装备数据.装备境界.词条[3].类型
                                            local 临时数额 = 装备数据.装备境界.词条[3].数额*2
                                            玩家数据[数字id].道具:给予道具(数字id,"鸿蒙原石",1,{类型=临时词条,数额=临时数额})
                                            常规提示(数字id,"#Y/你获得了#R"..临时词条.."#Y词条数额为#G"..临时数额.."#Y的#R鸿蒙原石#Y1个,该装备词条已消失")
                                            玩家数据[数字id].提取词条=nil
                                            道具刷新(数字id)
                                    else
                                        玩家数据[数字id].提取词条=nil
                                        常规提示(数字id,"#Y/未获取到该装备词条")
                                        return
                                    end
                               else
                                    玩家数据[数字id].提取词条=nil
                                    常规提示(数字id,"#Y/未获取到该装备词条")
                                    return
                               end
                          elseif 玩家数据[数字id].提取词条.名称=="太初神石" then
                                  if 装备数据.装备境界.品质=="神话" and 装备数据.装备境界.神话词条 and 装备数据.装备境界.神话词条~="" and 事件=="确定吸附神话词条" then
                                      if 装备数据.装备境界.神话值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/吸附词条需100神话值") return end
                                      玩家数据[数字id].道具.数据[装备id].装备境界.神话值 = 玩家数据[数字id].道具.数据[装备id].装备境界.神话值 - 100
                                      玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                      if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                          玩家数据[数字id].道具.数据[道具id]=nil
                                      end
                                      玩家数据[数字id].道具:给予道具(数字id,"太初原石",1,{类型=装备数据.装备境界.神话词条,数额=0})
                                      玩家数据[数字id].道具.数据[装备id].装备境界.神话词条=nil
                                      常规提示(数字id,"#Y/你获得了#R"..装备数据.装备境界.神话词条.."#Y词条的#R太初原石#Y1个,该装备词条已消失")
                                      玩家数据[数字id].提取词条=nil
                                      道具刷新(数字id)
                                  else
                                      玩家数据[数字id].提取词条=nil
                                      常规提示(数字id,"#Y/未获取到该装备词条")
                                      return
                                  end

                           elseif 玩家数据[数字id].提取词条.名称=="太初原石" then
                                  if 装备数据.装备境界.品质=="神话" then
                                      local 附带词条 = 玩家数据[数字id].道具.数据[道具id].附带词条
                                      if not 附带词条 then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                                      if not 境界属性[附带词条] and not 神话属性[附带词条] then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                                      if 境界属性[附带词条] and not 境界属性[附带词条].分类[装备数据.分类] then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/该装备无法使用") return end
                                      if 神话属性[附带词条] and not 神话属性[附带词条][装备数据.分类] then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/该装备无法使用") return end
                                      local 使用成功 =false
                                      if 事件=="确定添加神话词条" then
                                          if 装备数据.装备境界.神话值<200 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/添加词条需200神话值") return end
                                          玩家数据[数字id].道具.数据[装备id].装备境界.神话值 = 玩家数据[数字id].道具.数据[装备id].装备境界.神话值 - 200
                                          常规提示(数字id,"#Y/你添加了神话词条#R"..附带词条)
                                          使用成功 =true
                                      elseif 事件=="确定更换神话词条"..装备数据.装备境界.神话词条  then
                                              if 装备数据.装备境界.神话值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/更换词条需100神话值") return end
                                              玩家数据[数字id].道具.数据[装备id].装备境界.神话值 = 玩家数据[数字id].道具.数据[装备id].装备境界.神话值 - 100
                                              常规提示(数字id,"#Y/你更换了神话词条为#R"..附带词条)
                                              使用成功 =true
                                      end
                                      if 使用成功 then
                                          玩家数据[数字id].道具.数据[装备id].装备境界.神话词条=附带词条
                                          玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                          if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                              玩家数据[数字id].道具.数据[道具id]=nil
                                          end
                                          道具刷新(数字id)
                                      end
                                      玩家数据[数字id].提取词条=nil
                                  else
                                      玩家数据[数字id].提取词条=nil
                                      常规提示(数字id,"#Y/未获取到该装备词条")
                                      return
                                  end
                            elseif 玩家数据[数字id].提取词条.名称=="太初仙石" then
                                  if 装备数据.装备境界.品质=="神话" and (境界属性[事件] or 神话属性[事件]) then
                                      local 使用成功=false
                                      if 境界属性[事件] and 境界属性[事件].分类[装备数据.分类] then
                                              使用成功=true
                                      elseif 神话属性[事件] and 神话属性[事件][装备数据.分类] then
                                              使用成功=true
                                      end
                                      if 使用成功 then
                                            玩家数据[数字id].道具.数据[装备id].装备境界.神话词条=事件
                                            玩家数据[数字id].道具.数据[装备id].装备境界.神话值 = 0
                                            常规提示(数字id,"#Y/你添加了神话词条#R"..事件)
                                            玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                            if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                                玩家数据[数字id].道具.数据[道具id]=nil
                                            end
                                            道具刷新(数字id)
                                      else
                                          常规提示(数字id,"#Y/该装备无法使用")
                                      end
                                      玩家数据[数字id].提取词条=nil
                                  else
                                      玩家数据[数字id].提取词条=nil
                                      常规提示(数字id,"#Y/该装备无法使用")
                                      return
                                  end
                          elseif 玩家数据[数字id].提取词条.名称=="鸿蒙原石" then
                                  local 附带词条 = 玩家数据[数字id].道具.数据[道具id].附带词条
                                  local 词条数额 = 玩家数据[数字id].道具.数据[道具id].数额
                                  if not 附带词条 or not 词条数额 then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                                  if not 境界属性[附带词条] then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/数据错误") return end
                                  if not 境界属性[附带词条].分类[装备数据.分类] then 玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/该装备无法使用") return end
                                  if 装备词条 then
                                      local 是否使用 = false
                                      if 事件=="添加第一词条" then
                                          if 装备数据.装备境界.洗练值<200 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/添加词条需200仙宝值") return end
                                          玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 200
                                          玩家数据[数字id].道具.数据[装备id].装备境界.词条[1]={
                                                      类型=附带词条,
                                                      数额=math.floor(词条数额)
                                                    }
                                          常规提示(数字id,"#Y/你对装备第一个词条添加了"..附带词条)
                                          是否使用 = true
                                      elseif 事件=="添加第二词条" then
                                          if 装备数据.装备境界.洗练值<200 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/添加词条需200仙宝值") return end
                                          玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 200
                                          玩家数据[数字id].道具.数据[装备id].装备境界.词条[2]={
                                                      类型=附带词条,
                                                      数额=math.floor(词条数额/2)
                                                    }
                                          常规提示(数字id,"#Y/你对装备第二个词条添加了"..附带词条)
                                          是否使用 = true
                                      elseif 事件=="添加第三词条" then
                                              if 装备数据.装备境界.洗练值<200 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/添加词条需200仙宝值") return end
                                              玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 200
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[3]={
                                                      类型=附带词条,
                                                      数额=math.floor(词条数额/2)
                                                    }
                                              常规提示(数字id,"#Y/你对装备第三个词条添加了"..附带词条)
                                              是否使用 = true
                                      elseif 装备词条[1] and 事件=="更换第一词条:"..装备词条[1].类型 then
                                              if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/更换词条需100仙宝值") return end
                                              玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[1]={
                                                      类型=附带词条,
                                                      数额=math.floor(词条数额)
                                                    }
                                              常规提示(数字id,"#Y/你对装备第一个词条附魔了"..附带词条)
                                              是否使用 = true
                                      elseif 装备词条[1] and 事件=="更换第二词条:"..装备词条[2].类型 then
                                              if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/更换词条需100仙宝值") return end
                                              玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[2]={
                                                        类型=附带词条,
                                                        数额=math.floor(词条数额/2)
                                                      }
                                              常规提示(数字id,"#Y/你对装备第二个词条附魔了"..附带词条)
                                              是否使用 = true
                                      elseif 装备词条[3] and 事件=="更换第三词条:"..装备词条[3].类型 then
                                              if 装备数据.装备境界.洗练值<100 then  玩家数据[数字id].提取词条=nil 常规提示(数字id,"#Y/更换词条需100仙宝值") return end
                                              玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 = 玩家数据[数字id].道具.数据[装备id].装备境界.洗练值 - 100
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[3]={
                                                        类型=附带词条,
                                                        数额=math.floor(词条数额/2)
                                                      }
                                              常规提示(数字id,"#Y/你对装备第三个词条附魔了"..附带词条)
                                              是否使用 = true
                                      end
                                      if 是否使用 then

                                          玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣=false
                                          玩家数据[数字id].道具.数据[道具id].数量 = 玩家数据[数字id].道具.数据[道具id].数量 - 1
                                          if 玩家数据[数字id].道具.数据[道具id].数量<=0 then
                                              玩家数据[数字id].道具.数据[道具id]=nil
                                          end
                                          local 临时境界 = 玩家数据[数字id].道具.数据[装备id].装备境界
                                          if 临时境界.词条 and 临时境界.词条[1] and 临时境界.词条[2] and 临时境界.词条[3]
                                            and 临时境界.词条[1].类型==临时境界.词条[2].类型 and 临时境界.词条[1].类型==临时境界.词条[3].类型 then
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[2].数额 =math.floor(临时境界.词条[1].数额/2)
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条[3].数额 =math.floor(临时境界.词条[1].数额/2)
                                              --if 境界属性[临时境界.词条[1].类型] then
                                              玩家数据[数字id].道具.数据[装备id].装备境界.词条共鸣=境界属性[临时境界.词条[1].类型].共鸣
                                              --end
                                          end
                                          玩家数据[数字id].提取词条=nil
                                          道具刷新(数字id)
                                      else
                                          玩家数据[数字id].提取词条=nil
                                          常规提示(数字id,"#Y/未获取到该道具数据")
                                          return
                                      end
                                  else
                                    玩家数据[数字id].提取词条=nil
                                    常规提示(数字id,"#Y/未获取到该装备词条")
                                    return
                                  end
                          else
                                玩家数据[数字id].提取词条=nil
                                常规提示(数字id,"#Y/请不要移动装备")
                                return
                          end
                      else
                          玩家数据[数字id].提取词条=nil
                          常规提示(数字id,"#Y/请不要移动装备")
                          return
                      end
              end

              if 事件 == "藏宝阁上架物品" then
                  藏宝阁处理类:获取玩家道具(数字id,3703)
              elseif 事件 == "藏宝阁上架召唤兽"  then
                     藏宝阁处理类:获取玩家召唤兽(数字id,3703)
              elseif 事件 == "藏宝阁上架角色"  then
                      常规提示(数字id,"#Y/藏宝阁上架角色")
              elseif 事件 == "藏宝阁上架货币"  then
                      发送数据(玩家数据[数字id].连接id,1501,{名称=玩家数据[数字id].角色.数据.名称,模型=玩家数据[数字id].角色.数据.模型,对话="清选择上架货币类型",选项={"藏宝阁上架银子","藏宝阁上架仙玉"}})
              elseif 事件 == "藏宝阁上架银子"  then
                      发送数据(id,3713,{类型="银子",数量=玩家数据[数字id].角色.数据.银子})
              elseif 事件 == "藏宝阁上架仙玉"  then
                      发送数据(id,3713,{类型="仙玉",数量=共享货币[玩家数据[数字id].账号].仙玉})
              elseif 事件 == "打开购买商品"  then
                      藏宝阁处理类:获取购买商品(数字id,3712)
              elseif 事件 == "打开购买召唤兽"  then
                      藏宝阁处理类:获取购买召唤兽(数字id,3712)
              elseif 事件 == "打开上架商品"  then
                      藏宝阁处理类:获取上架商品(数字id,3711)
              elseif 事件 == "打开上架召唤兽"  then
                      藏宝阁处理类:获取上架召唤兽(数字id,3711)
              elseif 事件 == "重置全部经脉"  then
                      玩家数据[数字id].经脉:清空奇经八脉()
              elseif 事件 == "重置当前经脉"  then
                      玩家数据[数字id].经脉:清空当前奇经八脉()
              elseif 事件 == "超级传送" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      发送数据(id,119)
                      玩家数据[数字id].最后操作 = "会员传送"
              elseif 事件 == "场景传送" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      发送数据(id,120)
                      玩家数据[数字id].最后操作 = "会员传送"
              elseif 事件 == "自动抓鬼" or 事件 == "自动鬼王" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      玩家数据[数字id].自动抓鬼={
                        进程=1,
                        时间=os.time()+2,
                        开启=true,
                        事件=事件
                    }

                     发送数据(id,101,{进程 = "开启",仙玉=玩家数据[数字id].角色.数据.自动抓鬼,事件=事件})
              elseif 事件 == "领取等级福利" then
                     if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                     local  对话内容 = "请选择等级奖励:"
                     local  对话选项 = {"10级奖励","20级奖励","30级奖励","40级奖励","50级奖励",
                                        "60级奖励","70级奖励","80级奖励","90级奖励","100级奖励",
                                        "110级奖励","120级奖励","130级奖励","140级奖励","150级奖励",
                                        "什么也不做"
                                      }
                     local 对话模型=玩家数据[数字id].角色.数据.模型
                     local 对话名称=玩家数据[数字id].角色.数据.名称
                    发送数据(id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              elseif string.find(事件, "级奖励")~=nil then
                     local 基础储备 = 1000000
                     local 分割数据= 分割文本(事件,"级奖")
                     local 事件等级=tonumber(分割数据[1])
                     if 玩家数据[数字id].角色.数据.等级<事件等级 then
                         添加最后对话(数字id,"等级达到"..事件等级.."级才可领取奖励。")
                      elseif 玩家数据[数字id].角色.数据.新手奖励[事件等级] then
                         添加最后对话(数字id,"你已经领取过奖励了。")
                     else
                          玩家数据[数字id].角色.数据.新手奖励[事件等级]=true
                          local  获得储备 = 基础储备+ math.floor(事件等级/10)*200000
                          玩家数据[数字id].角色:添加储备(获得储备,"会员卡等级奖励",1)
                     end
              elseif 事件 == "八卦炼丹" then
                     if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                     local  对话内容 = "少侠可是想要炼制极品丹药？带上你的东西到我这来炼丹吧！运气好的话你会获得价值不菲的灵丹妙药！"
                     local  对话选项 = {"我要炼丹","买点炼丹材料","领取寄存丹药","我什么都不想做"}
                     local 对话模型=玩家数据[数字id].角色.数据.模型
                     local 对话名称=玩家数据[数字id].角色.数据.名称
                     发送数据(id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              elseif 事件=="我要炼丹" then
                      炼丹查看[数字id] = 1
                      if 玩家数据[数字id].角色.数据.炼丹灵气 == nil then
                        玩家数据[数字id].角色.数据.炼丹灵气 = 0
                      end
                      if 炼丹炉.下注时间==nil then
                         炼丹炉.下注时间 = 90
                      end
                      if 炼丹炉.转盘时间==nil then
                         炼丹炉.转盘时间 = 30
                      end
                      if 炼丹炉.停止时间==nil then
                         炼丹炉.停止时间 = 10
                      end
                      local 临时炼丹 = DeepCopy(炼丹炉)
                      临时炼丹.开奖控制=nil
                      发送数据(玩家数据[数字id].连接id,108,{时间=临时炼丹,数据=临时炼丹[数字id],灵气=玩家数据[数字id].角色.数据.炼丹灵气,物品数据=玩家数据[数字id].道具:索要道具1(数字id),物品价格=自定义数据.炼丹炉})
                      发送数据(玩家数据[数字id].连接id,108.1,{数据=临时炼丹[数字id],灵气=玩家数据[数字id].角色.数据.炼丹灵气})
              elseif 事件=="买点炼丹材料" then
                     if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.炼丹材料.货币类型,自定义数据.炼丹材料.数量,"炼丹材料",1) then
                        玩家数据[数字id].道具:给予道具(数字id,"翡翠琵琶",1)
                        常规提示(数字id,"你获得了一个翡翠琵琶！")
                      else
                        常规提示(数字id,"你个穷鬼,没有钱还想买炼丹材料啊！")
                        return
                     end
                     道具刷新(数字id)
              elseif 事件=="领取寄存丹药" then
                      if 商品存放[数字id] == nil then
                          常规提示(数字id,"这里没有存放你的奖励哟！")
                          return
                      else
                          local 奖励数据 = 商品存放[数字id]
                          if 奖励数据[1] > 0 then
                            玩家数据[数字id].道具:给予道具(数字id,"金砂丹",奖励数据[1])
                          end
                          if 奖励数据[2] > 0 then
                            玩家数据[数字id].道具:给予道具(数字id,"银砂丹",奖励数据[2])
                          end
                          if 奖励数据[3] > 0 then
                            玩家数据[数字id].道具:给予道具(数字id,"铜砂丹",奖励数据[3])
                          end
                          常规提示(数字id,"#Y/恭喜你在炼丹中中得八卦位！")
                          广播消息({内容=string.format("#S(八卦炼丹)#Y恭喜:#G%s#Y在炼丹中获得了:#G%s颗#Y金丹 #G%s颗#Y银丹 #G%s颗#Y铜丹",玩家数据[数字id].角色.数据.名称,奖励数据[1],奖励数据[2],奖励数据[3]),频道="hd"})
                          商品存放[数字id] = nil
                      end
              elseif 事件=="领取每日福利" then
                   --   玩家数据[数字id].每日活动:数据处理({文本="查看会员"})
                    if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                    local 发送信息 = 自定义数据.月卡数据
                    if 发送信息.月卡货币 == "仙玉" then
                       发送信息.玩家货币 = 共享货币[玩家数据[数字id].账号].仙玉
                    else
                       发送信息.玩家货币 = 共享货币[玩家数据[数字id].账号].点卡
                    end
                    发送信息.月卡 =玩家数据[数字id].角色.数据.月卡
                    发送数据(玩家数据[数字id].连接id,104,发送信息)



              elseif 事件=="转换武器" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      local  对话内容 = "请选择要转换的武器造型:"
                      local  对话选项 = {"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","什么也不做"}
                      local 对话模型=玩家数据[数字id].角色.数据.模型
                      local 对话名称=玩家数据[数字id].角色.数据.名称
                      发送数据(id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              elseif 事件=="转换装备" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      local  对话内容 = "请选择要转换的装备造型:"
                      local  对话选项 = {"头盔","发钗","女衣","男衣","什么也不做"}
                      local 对话模型=玩家数据[数字id].角色.数据.模型
                      local 对话名称=玩家数据[数字id].角色.数据.名称
                      发送数据(id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              elseif 事件=="枪矛" or 事件=="斧钺" or 事件=="剑" or 事件=="双短剑" or 事件=="飘带" or 事件=="爪刺" or 事件=="扇" or 事件=="魔棒" or 事件=="锤" or 事件=="鞭" or 事件=="环圈" or 事件=="刀" or 事件=="法杖" or 事件=="弓弩" or 事件=="宝珠" or 事件=="巨剑" or 事件=="伞" or 事件=="灯笼" then
                            玩家数据[数字id].给予数据={类型=1,id=0,事件=事件}
                            发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="袁天罡",等级="无",子类=事件})
              elseif 事件=="头盔" or 事件=="发钗" or 事件=="女衣" or 事件=="男衣" then
                            玩家数据[数字id].给予数据={类型=1,id=0,事件=事件}
                            发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="袁天罡",等级="无",子类=事件})
              elseif 事件=="门派转换" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      local  对话内容 = "穿戴装备转换会让装备绑定角色,是否转换?"
                      local  对话选项 = {"确定角色门派转换","什么也不做"}
                      local 对话模型=玩家数据[数字id].角色.数据.模型
                      local 对话名称=玩家数据[数字id].角色.数据.名称
                      发送数据(id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})
              elseif 事件=="确定角色门派转换" then
                     发送数据(id,124,{模式=true})
                     玩家数据[数字id].转换门派模式=true
              elseif 事件=="送我回帮" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      if 帮派处理类:取是否有帮派(数字id) then
                        if 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号].成员数据[数字id].职务 == "帮主" then
                            帮派处理类:回到帮派(数字id)
                            return
                        end
                        if 玩家数据[数字id].角色.数据.本周已缴帮费 == nil then
                           玩家数据[数字id].角色.数据.本周已缴帮费 = false
                        end

                        if 玩家数据[数字id].角色.数据.帮费限时 == nil then
                           玩家数据[数字id].角色.数据.帮费限时 = os.time()
                        end

                        if 玩家数据[数字id].角色.数据.本周已缴帮费 == false or 玩家数据[数字id].角色.数据.帮费限时 < os.time() then
                          常规提示(数字id,"#Y本周还未缴纳帮费，请缴纳帮费")
                          return
                        else
                          帮派处理类:回到帮派(数字id)
                        end
                     end
              elseif 事件=="送我回家" then
                    if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                    if  玩家数据[数字id].角色.数据.地图数据.编号>100000 then
                        return
                    end
                    if 玩家数据[数字id].房屋.是否创建  then
                          if 玩家数据[数字id].队伍~=0 then
                               if 玩家数据[数字id].队长 then
                                  for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                      local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                      发送数据(玩家数据[临时id].连接id,1026,{玩家数据[数字id].房屋})
                                  end
                               else
                                   return
                               end
                          else
                              发送数据(玩家数据[数字id].连接id,1026,{玩家数据[数字id].房屋})
                          end
                          if 玩家数据[数字id].房屋.庭院地图 == 1420 then
                             地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,18,25)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1421 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,20,21)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1422 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,24,26)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1424 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,28,42)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1306 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,11,85)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1885 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,18,48)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1380 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,28,27)
                          elseif 玩家数据[数字id].房屋.庭院地图 == 1382 then
                                 地图处理类:跳转地图(数字id,玩家数据[数字id].房屋.庭院ID,74,81)
                          end
                        return
                   else
                       常规提示(数字id,"#Y你还没有房子！")
                   end
              elseif 事件=="靓号设置" then
                     if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      发送数据(id,145,{编号=玩家数据[数字id].角色.数据.靓号编号})
              elseif 事件=="确定增加上限" then
                    if 玩家数据[数字id].角色.数据.携带宠物>=10 then
                        添加最后对话(数字id,"宠物携带数量已达最大上限。")
                    else
                        local 数额=自定义数据.增加宠物上限.数量*(玩家数据[数字id].角色.数据.携带宠物-2)
                        if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.增加宠物上限.货币类型,数额,"宠物携带数量",1) then
                           玩家数据[数字id].角色.数据.携带宠物 = 玩家数据[数字id].角色.数据.携带宠物 + 1
                           发送数据(id,110,{数量=玩家数据[数字id].角色.数据.携带宠物})
                           添加最后对话(数字id,"你的召唤兽携带上限已增加")
                        end
                    end
              elseif 事件=="会员地图" then -----
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      任务处理类:添加会员地图(数字id)
              elseif 事件=="一键附魔" or 事件=="一键回收" then
                     if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                    if not 自定义数据.一键附魔配置 then
                        自定义数据.一键附魔配置={货币类型="仙玉",数量=2000,时间=1}
                    end
                    if not 自定义数据.一键回收配置 then
                        加载回收配置文档()
                    end

                    local  对话内容 = ""
                    local  对话选项 = {}
                    local 对话模型=玩家数据[数字id].角色.数据.模型
                    local 对话名称=玩家数据[数字id].角色.数据.名称
                    if 事件=="一键附魔" then
                        local 装备数据=玩家数据[数字id].角色:取装备数据()
                        local 装备名称= {"头盔","项链","武器","衣服","腰带","鞋子"}
                        local 装备说明=""
                        for i,v in ipairs(装备名称) do
                            if 装备数据[i] and 装备数据[i].名称 then
                                  装备说明=装备说明..v..","
                            end
                        end
                       对话内容 = "附魔装备单件装备需求#R/"..自定义数据.一键附魔配置.数量.."#W"..自定义数据.一键附魔配置.货币类型..",请穿戴好需要附魔的装备,如不需要附魔请不要穿戴,当前已穿戴#R"..装备说明
                       对话选项 = {"确定附魔装备","我再想想"}
                    elseif 事件=="一键回收" then
                            对话内容 = "你可以查看各种道具的回收价格,请将不需要回收的道具放到加锁或者行囊以及仓库,点击确定回收道具后会直接回收道具背包中的可回收的道具"
                            对话选项 = {"查看回收价格","确定回收道具","我再想想"}
                    end
                    发送数据(玩家数据[数字id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})


              elseif 事件=="确定附魔装备"  then
                        if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                        local 装备数据=玩家数据[数字id].角色:取装备数据()
                        local 数量=0
                        for k,v in pairs(装备数据) do
                            数量=数量+1
                        end
                        local 扣除数量 = 自定义数据.一键附魔配置.数量*数量
                        if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.一键附魔配置.货币类型,扣除数量,"一键附魔装备",1) then
                            玩家数据[数字id].装备:一键附魔(数字id)
                        end
              elseif 事件=="确定回收道具" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      玩家数据[数字id].道具:一键回收(数字id)

              elseif 事件=="查看回收价格" then
                      if not 自定义数据.回收配置文档 then
                            加载回收配置文档()
                      end
                      发送数据(玩家数据[数字id].连接id,90.1,{标题文字="回收价格",文本=自定义数据.回收配置文档})
               elseif 事件=="打开随身仓库" then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      玩家数据[数字id].道具仓库:数据处理({文本="打开仓库"})
              elseif 事件=="打开共享仓库" and 共享仓库[玩家数据[数字id].账号] then
                      if not 玩家数据[数字id].角色.数据.月卡.开通 then 常规提示(数字id,"#Y你还没有月卡无法使用该功能") return end
                      共享仓库[玩家数据[数字id].账号]:数据处理({文本="打开仓库",数字id=数字id})
              end
              return





      end




  --祈福值点化装备end

  --print(地图编号,事件,名称,玩家数据[数字id].最后操作,玩家数据[数字id].地图单位)


  if 玩家数据[数字id].喂养坐骑 then
          if tonumber(玩家数据[数字id].喂养坐骑)==nil or tonumber(玩家数据[数字id].喂养坐骑)==0 then 玩家数据[数字id].喂养坐骑 =nil return end
          if 玩家数据[数字id].角色.数据.坐骑列表[tonumber(玩家数据[数字id].喂养坐骑)]==nil then
               玩家数据[数字id].喂养坐骑 =nil
               常规提示(数字id,"#Y你没有这样的坐骑")
               return
          elseif 玩家数据[数字id].角色.数据.坐骑列表[tonumber(玩家数据[数字id].喂养坐骑)].忠诚 >= 100 then
              玩家数据[数字id].喂养坐骑 =nil
              常规提示(内容.数字id,"#Y/当前已达最高无需喂养!")
              return
          end
          if 事件=="喂养一次" then
                系统处理类:坐骑喂养处理(数字id,tonumber(玩家数据[数字id].喂养坐骑))
          elseif 事件=="喂养十次" then
               for i=1,10 do
                 系统处理类:坐骑喂养处理(数字id,tonumber(玩家数据[数字id].喂养坐骑))
               end
          end
          玩家数据[数字id].喂养坐骑 =nil

          return
  elseif 玩家数据[数字id].解除师徒 then
          玩家数据[数字id].解除师徒=nil

          local 徒弟id =0
          local 解除id=0
          for n, v in pairs(师徒数据[数字id].当前) do
              if 师徒数据[数字id].当前[n].名称==事件  then
                  徒弟id=师徒数据[数字id].当前[n].id
                  解除id=n
              end
          end
          if 徒弟id~=0 then
              师徒数据[数字id].当前[解除id]=nil
              添加最后对话(数字id,"解除师徒关系成功。")
              if 玩家数据[徒弟id]~=nil then
                玩家数据[徒弟id].角色.数据.师傅id=nil
              end
          end
        return
  elseif 玩家数据[数字id].科举对话 then
        if 名称=="礼部侍郎" then
            if 事件=="使用法宝" then
                游戏活动类:科举回答题目(id,数字id,事件)
                return
            end
            for n=1,3 do
                if 事件==玩家数据[数字id].科举数据.答案[n] then
                    游戏活动类:科举回答题目(id,数字id,事件)
                    return
                end
            end
        else
            玩家数据[数字id].科举对话=nil
        end
       return
  elseif 玩家数据[数字id].最后操作~=nil and 玩家数据[数字id].最后操作~="" then
        if 玩家数据[数字id].最后操作=="导标旗" and 事件=="请送我过去" then
              if 玩家数据[数字id].道具:取飞行限制(数字id)==false then
                  local 道具id=玩家数据[数字id].道具操作.id
                  地图处理类:跳转地图(数字id,玩家数据[数字id].道具.数据[道具id].地图,玩家数据[数字id].道具.数据[道具id].x,玩家数据[数字id].道具.数据[道具id].y)
                  玩家数据[数字id].道具.数据[道具id].次数=玩家数据[数字id].道具.数据[道具id].次数-1
                  if 玩家数据[数字id].道具.数据[道具id].次数<=0 then
                      玩家数据[数字id].道具:删除道具(玩家数据[数字id].连接id,数字id,玩家数据[数字id].道具操作.类型,玩家数据[数字id].道具操作.id,玩家数据[数字id].道具操作.编号,删除数量)
                  end
                  发送数据(玩家数据[数字id].连接id,3699)
                  玩家数据[数字id].最后操作=nil
                  return
              end
        elseif 玩家数据[数字id].最后操作=="师门召唤兽" and  事件=="上交召唤兽" then
                  local 对话="请选择要上交的召唤兽："
                  local 选项=取符合师门召唤兽选项(数字id)
                  if 选项==nil or #选项==0 then
                      对话="你身上没有我需要的召唤兽"
                      玩家数据[数字id].最后操作=nil
                  else
                      玩家数据[数字id].最后操作="上交师门召唤兽"
                  end
                  添加最后对话(数字id,对话,选项)
                  return
        elseif 玩家数据[数字id].最后操作=="文韵召唤兽" and  事件=="上交文韵墨香召唤兽"  then
                local 对话="请选择要上交的召唤兽："
                local 选项=取符合文韵召唤兽选项(数字id)
                if 选项==nil or #选项==0 then
                    对话="你身上没有我需要的召唤兽"
                    玩家数据[数字id].最后操作=nil
                else
                    玩家数据[数字id].最后操作="上交文韵召唤兽"
                end
                添加最后对话(数字id,对话,选项)
                return
        elseif 玩家数据[数字id].最后操作=="上交师门召唤兽" then
                判断师门召唤兽选项(数字id,事件)
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="上交文韵召唤兽" then---------远方文韵墨香
                判断文韵召唤兽选项(数字id,事件)
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="查看魔兽残卷" and 事件 == "上交魔兽残卷" then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="上交魔兽残卷"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="李世民",类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="宠修bb" then
                判断修炼召唤兽选项(数字id,事件)
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="出售装备" and 事件=="确认出售"  then
                玩家数据[数字id].道具:出售装备(id,数字id)
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="鉴定装备" and 事件=="确认鉴定"  then
                玩家数据[数字id].道具:鉴定装备(id,数字id)
                玩家数据[数字id].最后操作=nil
                return
        elseif 玩家数据[数字id].最后操作=="出售海产" then
              local 出售数量 = 0
              local 出售单价 = 0
              local 银子储备 = 1
              if 事件 == "250两银子卖给你" then
                  出售单价 = 250
                  for i=1,#玩家数据[数字id].召唤兽.数据 do
                      if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "大海龟" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                          出售数量 = 出售数量 +1
                          table.remove(玩家数据[数字id].召唤兽.数据,i)
                      end
                  end
              elseif 事件 == "300两储备银子卖给你" then
                      出售单价 = 300
                      银子储备 = 2
                      for i=1,#玩家数据[数字id].召唤兽.数据 do
                        if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "大海龟" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                            出售数量 = 出售数量 +1
                            table.remove(玩家数据[数字id].召唤兽.数据,i)
                        end
                      end
              elseif 事件 == "350两银子卖给你" then
                      出售单价 = 350
                      for i=1,#玩家数据[数字id].召唤兽.数据 do
                        if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "巨蛙" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                            出售数量 = 出售数量 +1
                            table.remove(玩家数据[数字id].召唤兽.数据,i)
                        end
                      end
              elseif 事件 == "400两储备银子卖给你" then
                      出售单价 = 400
                      银子储备 = 2
                      for i=1,#玩家数据[数字id].召唤兽.数据 do
                        if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "巨蛙" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                            出售数量 = 出售数量 +1
                            table.remove(玩家数据[数字id].召唤兽.数据,i)
                        end
                      end
              elseif 事件 == "500两银子卖给你" then
                      出售单价 = 500
                      for i=1,#玩家数据[数字id].召唤兽.数据 do
                        if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "海毛虫" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                            出售数量 = 出售数量 +1
                            table.remove(玩家数据[数字id].召唤兽.数据,i)
                        end
                      end
              elseif 事件 == "600两储备银子卖给你" then
                      出售单价 = 600
                      银子储备 = 2
                      for i=1,#玩家数据[数字id].召唤兽.数据 do
                        if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "海毛虫" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                            出售数量 = 出售数量 +1
                            table.remove(玩家数据[数字id].召唤兽.数据,i)
                        end
                      end
              end
              if 出售数量 == 0 then
                常规提示(数字id,"#Y你没有该类型的宠物可以出售")
              else
                  if 银子储备 == 1 then
                    玩家数据[数字id].角色:添加银子(出售数量*出售单价,"出售海产"..出售数量.."只",1)
                  else
                    玩家数据[数字id].角色:添加储备(出售数量*出售单价,"出售海产"..出售数量.."只",1)
                  end
                   玩家数据[数字id].最后操作=nil
                  return
              end
        elseif 玩家数据[数字id].最后操作=="合成旗1" then
                if 事件=="合成导标旗" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="合成旗"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="五色旗盒",类型="法宝",等级="无"})
                    玩家数据[数字id].最后操作="合成旗2"
                    return
                elseif 事件=="补充合成旗" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="合成旗4"}
                      发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="五色旗盒",类型="法宝",等级="无"})
                      玩家数据[数字id].最后操作="合成旗4"
                      return
                end
        elseif 玩家数据[数字id].最后操作=="合成旗3" and (事件=="绿色合成旗" or 事件=="蓝色合成旗" or 事件=="红色合成旗" or 事件=="白色合成旗" or 事件=="黄色合成旗")  then
                玩家数据[数字id].道具:生成合成旗(id,数字id,事件)
                return
        elseif 玩家数据[数字id].最后操作=="月光宝盒" and 玩家数据[数字id].法宝id ~=nil then
                local 道具id=玩家数据[数字id].角色.数据.法宝[玩家数据[数字id].法宝id]
                if 玩家数据[数字id].道具.数据[道具id].名称=="月光宝盒" then
                    if 事件=="送我过去" then
                        if 玩家数据[数字id].道具.数据[道具id].地图==nil then
                            常规提示(数字id,"#Y此法宝尚未标记地点，无法使用传送功能")
                            玩家数据[数字id].法宝id=nil
                        elseif 玩家数据[数字id].道具.数据[道具id].魔法<=5 then
                            常规提示(数字id,"#Y你的法宝灵气低于5点，已经无法使用了")
                            玩家数据[数字id].法宝id=nil
                        elseif 玩家数据[数字id].道具.数据[道具id].时间~=nil and 玩家数据[数字id].道具.数据[道具id].时间>os.time() then
                            常规提示(数字id,"#Y该法宝需要在#R"..时间转换(玩家数据[数字id].道具.数据[道具id].时间).."#Y后才可使用")
                            玩家数据[数字id].法宝id=nil
                        elseif 玩家数据[数字id].道具:取飞行限制(数字id) then
                            常规提示(数字id,"#Y你当前无法使用飞行道具")
                            玩家数据[数字id].法宝id=nil
                        else
                            玩家数据[数字id].道具.数据[道具id].魔法=玩家数据[数字id].道具.数据[道具id].魔法-5
                            发送数据(id,38,{内容="你的法宝#R/月光宝盒#W/灵气减少了5点"})
                            道具刷新(数字id)
                            玩家数据[数字id].道具.数据[道具id].时间=os.time()+1800-玩家数据[数字id].道具.数据[道具id].气血*60
                            常规提示(数字id,"#Y使用法宝成功,该法宝下次使用时间为#R"..时间转换(玩家数据[数字id].道具.数据[道具id].时间))
                            地图处理类:跳转地图(数字id,玩家数据[数字id].道具.数据[道具id].地图,玩家数据[数字id].道具.数据[道具id].x,玩家数据[数字id].道具.数据[道具id].y)
                            玩家数据[数字id].法宝id=nil
                        end
                         玩家数据[数字id].最后操作=nil
                          return
                    elseif 事件=="在这里定标" then
                            if 取正常地图(玩家数据[数字id].角色.数据.地图数据.编号) then
                                玩家数据[数字id].道具.数据[道具id].地图 = 玩家数据[数字id].角色.数据.地图数据.编号
                                玩家数据[数字id].道具.数据[道具id].x=math.floor(玩家数据[数字id].角色.数据.地图数据.x/20)
                                玩家数据[数字id].道具.数据[道具id].y=math.floor(玩家数据[数字id].角色.数据.地图数据.y/20)
                                if 玩家数据[数字id].道具.数据[道具id].时间 ~=nil then
                                    玩家数据[数字id].道具.数据[道具id].时间=玩家数据[数字id].道具.数据[道具id].时间
                                end
                                道具刷新(数字id)
                                常规提示(数字id,"#Y定标成功")
                                玩家数据[数字id].法宝id=nil
                            else
                                常规提示(数字id,"#Y此场景无法定标")
                                玩家数据[数字id].法宝id=nil
                            end
                              玩家数据[数字id].最后操作=nil
                              return
                    end
                end
        end

  end



















 if 玩家数据[数字id].角色.数据.多角色操作 then
       if 玩家数据[数字id].升级角色~=nil and 玩家数据[玩家数据[数字id].升级角色]~=nil and
            玩家数据[玩家数据[数字id].升级角色].子角色操作~=nil and 玩家数据[玩家数据[数字id].升级角色].子角色操作==数字id then
            if 事件=="我已确认将该角色等级提升至70" and 玩家数据[玩家数据[数字id].升级角色].角色.数据.等级==69 then
                 玩家数据[数字id].科举对话=nil
                 玩家数据[玩家数据[数字id].升级角色].角色:升级处理(玩家数据[数字id].连接id,1,数字id)
                 发送数据(玩家数据[数字id].连接id,6001,{角色=玩家数据[数字id].升级角色,角色属性=玩家数据[玩家数据[数字id].升级角色].角色:取总数据()})
                 玩家数据[数字id].升级角色=nil
                  return
            elseif 事件=="我已确认将该角色等级提升至110" and 玩家数据[玩家数据[数字id].升级角色].角色.数据.等级==109 then
                  玩家数据[数字id].科举对话=nil
                  玩家数据[玩家数据[数字id].升级角色].角色:升级处理(玩家数据[数字id].连接id,1,数字id)
                  发送数据(玩家数据[数字id].连接id,6001,{角色=玩家数据[数字id].升级角色,角色属性=玩家数据[玩家数据[数字id].升级角色].角色:取总数据()})
                  玩家数据[数字id].升级角色=nil
                  return
            elseif 事件=="我已确认将该角色等级提升至130" and 玩家数据[玩家数据[数字id].升级角色].角色.数据.等级==129 then
                  玩家数据[数字id].科举对话=nil
                  玩家数据[玩家数据[数字id].升级角色].角色:升级处理(玩家数据[数字id].连接id,1,数字id)
                  发送数据(玩家数据[数字id].连接id,6001,{角色=玩家数据[数字id].升级角色,角色属性=玩家数据[玩家数据[数字id].升级角色].角色:取总数据()})
                  玩家数据[数字id].升级角色=nil
                  return
            end
      elseif 玩家数据[数字id].角色经脉操作~=nil and 玩家数据[玩家数据[数字id].角色经脉操作]~=nil and
            玩家数据[玩家数据[数字id].角色经脉操作].子角色操作~=nil and 玩家数据[玩家数据[数字id].角色经脉操作].子角色操作==数字id then
            if 事件=="重置该角色当前经脉" then
                   玩家数据[玩家数据[数字id].角色经脉操作].经脉:清空当前奇经八脉(数字id)
                   发送数据(玩家数据[数字id].连接id,6002,{角色=玩家数据[数字id].角色经脉操作,角色属性=玩家数据[玩家数据[数字id].角色经脉操作].角色:取总数据()})
                   发送数据(玩家数据[数字id].连接id,6005,{角色=玩家数据[数字id].角色经脉操作})
            elseif 事件=="重置该角色全部经脉" then
                   玩家数据[玩家数据[数字id].角色经脉操作].经脉:清空奇经八脉(数字id)
                   发送数据(玩家数据[数字id].连接id,6002,{角色=玩家数据[数字id].角色经脉操作,角色属性=玩家数据[玩家数据[数字id].角色经脉操作].角色:取总数据()})
                   发送数据(玩家数据[数字id].连接id,6005,{角色=玩家数据[数字id].角色经脉操作})
            end
      elseif 玩家数据[数字id].仓库操作~=nil and 玩家数据[玩家数据[数字id].仓库操作]~=nil and 共享货币[玩家数据[玩家数据[数字id].仓库操作].账号] and
              玩家数据[玩家数据[数字id].仓库操作].子角色操作~=nil and 玩家数据[玩家数据[数字id].仓库操作].子角色操作==数字id then
              if 事件=="确定购买该角色道具仓库" then
                  local 数额=(#玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据-3)*20+20
                  if #玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据 >=40 then 常规提示(数字id,"每个角色最多只能购买40个仓库哟！！！！") return end
                  if  共享货币[玩家数据[玩家数据[数字id].仓库操作].账号]:扣除仙玉(数额,"购买仓库"..(#玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据+1),玩家数据[数字id].仓库操作) then
                    玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据[#玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据+1]={}
                    常规提示(数字id,"购买仓库成功！")
                    发送数据(玩家数据[数字id].连接id,6022,{角色=玩家数据[数字id].仓库操作,道具=玩家数据[玩家数据[数字id].仓库操作].道具仓库:索取仓库数据(1),总数=#玩家数据[玩家数据[数字id].仓库操作].道具仓库.数据})
                  end
             elseif 事件=="确定购买该角色召唤兽仓库" then
                    local 数额=(#玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据-1)*20+20
                    if #玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据 >=10 then 常规提示(数字id,"每个角色最多只能购买10个召唤兽仓库哟！！！！") return end
                    if 共享货币[玩家数据[玩家数据[数字id].仓库操作].账号]:扣除仙玉(数额,"购买召唤兽仓库"..(#玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据+1),玩家数据[数字id].仓库操作) then
                      玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据[#玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据+1]={}
                      常规提示(数字id,"购买召唤兽仓库成功！")
                      发送数据(玩家数据[数字id].连接id,6023,{角色=玩家数据[数字id].仓库操作,召唤兽=玩家数据[玩家数据[数字id].仓库操作].召唤兽.数据,召唤兽仓库总数=#玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库.数据,召唤兽仓库数据=玩家数据[玩家数据[数字id].仓库操作].召唤兽仓库:索取召唤兽仓库数据(玩家数据[数字id].仓库操作,1)})
                    end
              end
            玩家数据[数字id].仓库操作 =nil
      end
      return
end







end

return NPC对话预处理