/**
 * 测试luahp.dll绑定
 */

const { HPPackClient, isAvailable } = require('./src/native/luahp-binding');

async function testLuaHP() {
    console.log('=== 测试luahp.dll绑定 ===');
    
    // 检查是否可用
    if (!isAvailable()) {
        console.error('❌ luahp.dll不可用');
        return;
    }
    
    console.log('✅ luahp.dll加载成功');
    
    // 创建客户端
    const client = new HPPackClient();
    
    try {
        // 初始化
        console.log('\n--- 初始化客户端 ---');
        const initResult = client.initialize();
        if (!initResult) {
            console.error('❌ 客户端初始化失败');
            return;
        }
        
        console.log('✅ 客户端初始化成功');
        console.log('最大包大小:', client.getMaxPackSize());
        
        // 连接到服务器
        console.log('\n--- 连接到服务器 ---');
        const connectResult = client.connect('127.0.0.1', 6888);
        if (!connectResult) {
            console.error('❌ 连接服务器失败');
            return;
        }
        
        console.log('✅ 连接服务器成功');
        console.log('连接状态:', client.isConnected());
        
        // 准备发送数据（使用与GM工具相同的数据）
        console.log('\n--- 准备发送数据 ---');
        
        // 模拟GM工具的数据包
        const mp = require('./src/protocol/messagepack');
        const encryptedData = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';
        
        // MessagePack打包
        const packedData = mp.pack(encryptedData);
        console.log('数据包长度:', packedData.length);
        console.log('数据包前32字节:', Array.from(packedData.slice(0, 32)).map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
        
        // 发送数据包
        console.log('\n--- 发送数据包 ---');
        const sendResult = client.sendPack(packedData);
        if (!sendResult) {
            console.error('❌ 发送数据包失败');
            return;
        }
        
        console.log('✅ 数据包发送成功');
        
        // 等待响应
        console.log('\n--- 等待服务器响应 ---');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        console.log('连接状态:', client.isConnected());
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    } finally {
        // 清理
        console.log('\n--- 清理资源 ---');
        client.destroy();
        console.log('✅ 测试完成');
    }
}

// 运行测试
testLuaHP().catch(console.error);
