
local 幻化处理类 = class()
local floor=math.floor
local random = 取随机数
local remove = table.remove

local 附加范围={"力量","敏捷","体质","耐力","魔力"}

local function 绑定等级物品()
	local n = {}
	n[1] = {"红缨枪","曲尖枪","锯齿矛","乌金三叉戟","火焰枪","墨杆金钩","玄铁矛","金蛇信","丈八点钢矛","暗夜","梨花","梨花","刑天之逆","五虎断魂","飞龙在天","天龙破城","弑皇"}
	n[2] = {"青铜斧","开山斧","双面斧","双弦钺","精钢禅钺","黄金钺","乌金鬼头镰","狂魔镰","恶龙之齿","破魄","肃魂","无敌","五丁开山","元神禁锢","护法灭魔","碧血干戚","裂天"}
	n[3] = {"青铜短剑","铁齿剑","吴越剑","青锋剑","龙泉剑","黄金剑","游龙剑","北斗七星剑","碧玉剑","鱼肠","倚天","湛卢","魏武青虹","灵犀神剑","四法青云","霜冷九州","擒龙"}
	n[4] = {"双短剑","镔铁双剑","龙凤双剑","竹节双剑","狼牙双剑","鱼骨双剑","赤焰双剑","墨玉双剑","梅花双剑","阴阳","月光双剑","灵蛇","金龙双剪","连理双树","祖龙对剑","紫电青霜","浮犀"}
	n[5] = {"五色缎带","幻彩银纱","金丝彩带","无极丝","天蚕丝带","云龙绸带","七彩罗刹","缚神绫","九天仙绫","彩虹","流云","碧波","秋水落霞","晃金仙绳","此最相思","揽月摘星","九霄"}
	n[6] = {"铁爪","天狼爪","幽冥鬼爪","青龙牙","勾魂爪","玄冰刺","青刚刺","华光刺","龙鳞刺","撕天","毒牙","胭脂","九阴勾魂","雪蚕之刺","贵霜之牙","忘川三途","离钩"}
	n[7] = {"折扇","铁骨扇","精钢扇","铁面扇","百折扇","劈水扇","神火扇","阴风扇","风云雷电","太极","玉龙","秋风","画龙点睛","秋水人家","逍遥江湖","浩气长舒","星瀚"}
	n[8] = {"细木棒","金丝魔棒","玉如意","点金棒","云龙棒","幽路引魂","满天星","水晶棒","日月光华","沧海","红莲","盘龙","降魔玉杵","青藤玉树","墨玉骷髅","丝萝乔木","醍醐"}
	n[9] = {"松木锤","镔铁锤","八棱金瓜","狼牙锤","烈焰锤","破甲战锤","震天锤","巨灵神锤","天崩地裂","八卦","鬼牙","雷神","混元金锤","九瓣莲花","鬼王蚀日","狂澜碎岳","碎寂"}
	n[10] = {"牛皮鞭","牛筋鞭","乌龙鞭","钢结鞭","蛇骨鞭","玉竹金铃","青藤柳叶鞭","雷鸣嗜血鞭","混元金钩","龙筋","百花","吹雪","游龙惊鸿","仙人指路","血之刺藤","牧云清歌","霜陨"}
	n[11] = {"黄铜圈","精钢日月圈","离情环","金刺轮","风火圈","赤炎环","蛇形月","子母双月","斜月狼牙","如意","乾坤","月光双环","别情离恨","金玉双环","九天金线","无关风月","朝夕"}
	n[12] = {"柳叶刀","苗刀","夜魔弯刀","金背大砍刀","雁翅刀","破天宝刀","狼牙刀","龙鳞宝刀","黑炎魔刀","冷月","屠龙","血刃","偃月青龙","晓风残月","斩妖泣血","业火三灾","鸣鸿"}
	n[13] = {"曲柳杖","红木杖","白椴杖","墨铁拐","玄铁牛角杖","鹰眼法杖","腾云杖","引魂杖","碧玺杖","业焰","玉辉","鹿鸣","庄周梦蝶","凤翼流珠","雪蟒霜寒","碧海潮生","弦月"}
	n[14] = {"硬木弓","铁胆弓","紫檀弓","宝雕长弓","錾金宝弓","玉腰弯弓","连珠神弓","游鱼戏珠","灵犀望月","非攻","幽篁","百鬼","冥火薄天","龙鸣寒水","太极流光","九霄风雷","若木"}
	n[15] = {"琉璃珠","水晶珠","珍宝珠","翡翠珠","莲华珠","夜灵珠","如意宝珠","沧海明珠","无量玉璧","离火","飞星","月华","回风舞雪","紫金葫芦","裂云啸日","云雷万里","赤明"}
	n[16] = {"钝铁重剑","桃印铁刃","赭石巨剑","壁玉长铗","青铜古剑","金错巨刃","惊涛雪","醉浮生","沉戟天戊","鸦九","昆吾","弦歌","墨骨枯麟","腾蛇郁刃","秋水澄流","百辟镇魂","长息"}
	n[18] = {"素纸灯","竹骨灯","红灯笼","鲤鱼灯","芙蓉花灯","如意宫灯","玲珑盏","玉兔盏","冰心盏","蟠龙","云鹤","风荷","金风玉露","凰火燎原","月露清愁","夭桃秾李","荒尘"}
	n[17] = {"油纸伞","红罗伞","紫竹伞","锦绣椎","幽兰帐","琳琅盖","孔雀羽","金刚伞","落梅伞","鬼骨","云梦","枕霞","碧火琉璃","雪羽穿云","月影星痕","浮生归梦","晴雪"}
	n[19] = {{"方巾","簪子"},{"布帽","玉钗",},{"面具","梅花簪子"},{"纶巾","珍珠头带"},{"缨络丝带","凤头钗"},{"羊角盔","媚狐头饰"},{"水晶帽","玉女发冠"},{"乾坤帽","魔女发冠"},{"黑魔冠","七彩花环"},{"白玉龙冠","凤翅金翎"},{"水晶夔帽","寒雉霜蚕"},{"翡翠曜冠","曜月嵌星"},{"金丝黑玉冠","郁金流苏簪"},{"白玉琉璃冠","玉翼附蝉翎"},{"兽鬼珐琅面","鸾羽九凤冠"},{"紫金磐龙冠","金珰紫焰冠"},{"浑天玄火盔","乾元鸣凤冕"}}
	n[20] = {"护身符","五色飞石","珍珠链",{"骷髅吊坠","苍魂珠"},{"江湖夜雨","九宫坠"},{"荧光坠子","高速之星"},{"风月宝链","八卦坠"},{"碧水青龙","鬼牙攫魂"},{"万里卷云","疾风之铃"},"七彩玲珑","黄玉琉佩","鸾飞凤舞","衔珠金凤佩","七璜珠玉佩","鎏金点翠佩","紫金碧玺佩","落霞陨星坠"}
	n[21] = {{"布裙","布衣"},{"丝绸长裙","皮衣"},{"五彩裙","鳞甲"},{"龙鳞羽衣","锁子甲"},{"天香披肩","紧身衣"},{"金缕羽衣","钢甲"},{"霓裳羽衣","夜魔披风"},{"流云素裙","龙骨甲"},{"七宝天衣","死亡斗篷"},{"飞天羽衣","神谕披风"},{"穰花翠裙","珊瑚玉衣"},{"金蚕丝裙","金蚕披风"},{"紫香金乌裙","乾坤护心甲"},{"碧霞彩云衣","蝉翼金丝甲"},{"金丝蝉翼衫","金丝鱼鳞甲"},{"五彩凤翅衣","紫金磐龙甲"},{"鎏金浣月衣","混元一气甲"}}
	n[22] = {"腰带","缎带","银腰带",{"水晶腰带","玉树腰带"},{"琥珀腰链","白面狼牙"},{"乱牙咬","魔童大牙"},{"攫魂铃","双魂引"},{"兽王腰带","百窜云"},{"八卦锻带","圣王坠"},"幻彩玉带","珠翠玉环","金蟾含珠","乾坤紫玉带","琉璃寒玉带","蝉翼鱼佩带","磐龙凤翔带","紫霄云芒带"}
	n[23] = {"布鞋","牛皮靴","马靴","侠客履","神行靴","绿靴","追星踏月","九州履","万里追云履","踏雪无痕","平步青云","追云逐电","乾坤天罡履","七星逐月靴","碧霞流云履","金丝逐日履","辟尘分光履"}
	n[24] = {"竹编护腕","皮腕","针腕","骨镯","青铜护腕","玛瑙护腕","琉璃护腕","镂空银镯","笼玉镯","嵌宝金腕","玳瑁护腕","七星宝腕","缚龙筋","凤翎护腕","织锦彩带","冰蚕丝带"}
	n[25] = {"竹编脖环","钢圈","荆棘环","骨环","青铜颈环","玛瑙石环","琉璃环","九曲环","笼玉环","嵌宝金环","玳瑁环","七星宝环","缚龙圈","鸾尾环","织锦颈圈","冰蚕丝圈"}
	n[26] = {"皮甲","皮甲","刺甲","骨排甲","青铜披甲","玛瑙软甲","琉璃罩甲","连环铠甲","笼玉甲","嵌宝金甲","玳瑁衣","七星宝甲","缚龙甲","凤凰彩衣","织锦软褡","冰蚕织甲"}
	return n
end

local function 取装备序列(名字)
	local zb = {"天龙破城","碧血干戚","霜冷九州","紫电青霜","揽月摘星","忘川三途","浩气长舒","丝萝乔木","狂澜碎岳","牧云清歌","无关风月","业火三灾","碧海潮生","九霄风雷","云雷万里","百辟镇魂","夭桃秾李","浮生归梦","紫金磐龙冠","金珰紫焰冠","紫金碧玺佩","五彩凤翅衣","紫金磐龙甲","磐龙凤翔带","金丝逐日履"}
	for n=1,#zb do
		if 名字 == zb[n] then
			return n
		end
	end
end

local function 绑定元身物品()
	local n = {}
	n[1] = "枪·元身"
	n[2] = "斧·元身"
	n[3] = "剑·元身"
	n[4] = "双剑·元身"
	n[5] = "飘带·元身"
	n[6] = "爪刺·元身"
	n[7] = "扇·元身"
	n[8] = "魔棒·元身"
	n[9] = "锤·元身"
	n[10] = "长鞭·元身"
	n[11] = "双环·元身"
	n[12] = "刀·元身"
	n[13] = "长杖·元身"
	n[14] = "弓·元身"
	n[15] = "宝珠·元身"
	n[16] = "巨剑·元身"
	n[18] = "灯笼·元身"
	n[17] = "伞·元身"
	n[19] = "头盔·元身"
	n[20] = "冠冕·元身"
	n[21] = "挂坠·元身"
	n[22] = "纱衣·元身"
	n[23] = "坚甲·元身"
	n[24] = "束带·元身"
	n[25] = "鞋履·元身"
	return n
end

local function 获取幻化概率(次数)
	if 次数 <= 10 then
		return 100
	elseif 次数 <= 20 then
		return 90
	elseif 次数 <= 30 then
		return 85
	elseif 次数 <= 40 then
		return 80
	elseif 次数 <= 50 then
		return 75
	elseif 次数 <= 60 then
		return 70
	elseif 次数 <= 70 then
		return 65
	elseif 次数 <= 80 then
		return 60
	elseif 次数 <= 90 then
		return 55
	elseif 次数 <= 100 then
		return 50
	else
		return 40
	end
end

function 幻化处理类:初始化()
 self.打造物品 = 绑定等级物品()
 self.元身物品 = 绑定元身物品()
end

function 幻化处理类:数据处理(连接id,序号,id,内容)
	if 序号==4517 then
		if 玩家数据[id].角色:取任务(5)~=0 then
			常规提示(id,"#Y/你已经有一个打造任务在进行了")
			return
		end
		self:幻化类型处理(连接id,序号,id,内容)
	end
end


function 幻化处理类:幻化类型处理(连接id,序号,id,内容)
	local 物品序号 = 玩家数据[id].角色.数据.道具[内容.序列]
	local 道具 = 玩家数据[id].道具.数据[物品序号]
	local 强化石序列 = 内容.强化石序列
	local 道具属性 = 玩家数据[id].道具.数据
	if 道具 == nil then
		道具刷新(id)
		return
	end
	self:幻化处理(id,物品序号,道具,道具属性,强化石序列)
end


function 幻化处理类:幻化公式(制造种类) -- 制作书等级 制作种类
	local 打造属性 = {} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
	if 制造种类 == nil then
		return false
	end
	if 制造种类 < 19 then -- 武器
		打造属性[1] = 取随机数(750, 1250)
		打造属性[2] = 取随机数(600, 850)
		if 取随机数(1,100) <= 5 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(80, 160 )
			打造属性[x2] = 取随机数(80, 160 )
		elseif 取随机数(1,100) <= 10 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(80, 160 )
		end
	elseif 制造种类 == 19 or 制造种类 == 20 then -- 帽子
		打造属性[3] = 取随机数(100, 210)
		打造属性[4] = 取随机数(100, 300)
	elseif 制造种类 == 21 then -- 项链
		打造属性[5] = 取随机数(230, 380)
	elseif 制造种类 == 22 or 制造种类 == 23 then -- 衣服
		打造属性[3] = 取随机数(280, 580)
		if 取随机数(1,100) <= 5 then
			local 总属性编号 = {7,8,9,10,11} -- 命中 伤害 防御 魔法 灵力 气血 敏捷 体质 力量 耐力 魔力 附加特效 附加特技
			local 随机编号 = 取随机数(1,#总属性编号)
			local x1  = 总属性编号[随机编号]
			remove(总属性编号,随机编号)
			随机编号 = 取随机数(1,#总属性编号)
			local x2  = 总属性编号[随机编号]
			打造属性[x1] = 取随机数(80, 160)
			打造属性[x2] = 取随机数(80, 160)
		elseif 取随机数(1,100) <= 10 then
			local sx = 取随机数(7,11)
			打造属性[sx] = 取随机数(80, 160)
		end
	elseif 制造种类 == 24 then -- 腰带
		打造属性[3] = 取随机数(100, 210)
		打造属性[6] = 取随机数(350, 600)
	elseif 制造种类 == 25 then -- 鞋子
		打造属性[3] = 取随机数(100, 210)
		打造属性[7] = 取随机数(120, 180)
	end
	local 特效 = 取随机数()
	if 特效 <= 5 then
		打造属性[12] = 取随机数(1,20)
	end
	特技 = 取随机数()
	if 特技 <= 5 then
		打造属性[13] = 取随机数(1,20)
	end
	return 打造属性
end

function 幻化处理类:幻化处理(id,物品序号,道具,道具属性,强化石序列)
	if 道具.幻化次数==nil then
		道具.幻化次数=0
	end

	if 玩家数据[id].道具:判断强化石(强化石序列) ~= nil and 玩家数据[id].道具:判断强化石(强化石序列)==false then
		常规提示(id,"你背包的强化石不够哦！")
		return
	end

	local 判断陨铁 = 玩家数据[id].道具:判断是否有陨铁()

	if 判断陨铁 == false then
		常规提示(id,"你背包的陨铁不够哦！")
		return
	end

	if 道具.级别限制 < 150 then
		常规提示(id,"低于150级的装备无法进行幻化哦！")
		return
	end

	--技能
	-- if 玩家数据[id].角色.辅助技能[7].等级 < 160 then
	-- 	常规提示(id,"#Y打造武器，需要#R 打造技巧 #Y/达到所打造装备的等级/2哟！")
	-- 	return
	-- end

	--体力
	if 玩家数据[id].角色.数据.体力 < 20 or 玩家数据[id].角色.数据.活力 < 20 then
		常规提示(id,"#Y您的体力或者活力好像不够了！")
		return
	end

	--金钱
	 -- if 道具属性[制造书序号].子类 * 道具属性[制造书序号].子类 * 10 > 玩家数据[id].角色.银子 then
	 -- 	常规提示(id,"#Y您的银子不够打造的费用哦！")
	 -- 	return
	 -- end

	--获取元身名称
	local 临时序列 = 0
	if 道具.总类 == 2 then
		临时序列 = 取装备序列(道具.名称)
	else
	    临时序列 = 道具.元身序列
	end
	local 临时类型 = self.元身物品[临时序列]
	local 消耗体活 = 20
	local 幻化概率 = 获取幻化概率(道具.幻化次数)
	local 新增概率 = 0

	if 玩家数据[id].道具:判断强化石(强化石序列) ~= nil and 玩家数据[id].道具:判断强化石(强化石序列)~=false then
		新增概率 = math.floor((强化石序列.朱雀石 + 强化石序列.白虎石 + 强化石序列.青龙石 + 强化石序列.玄武石)/2)
	end

	if 取随机数() <= (幻化概率 + 新增概率) then
		if 道具.总类 == 2 then
			local 道具 = 物品类()
			道具:置对象(临时类型)
			道具.幻化元身属性={}
			道具.级别限制 = 160
			local dz = self:幻化公式(临时序列)
			local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
			for n = 1, #属性 do
				if dz[n] ~= nil then
					if n >= 7 and n <= 11 and 临时序列~=25 then
						if dz[n] < 0 then
							道具.幻化元身属性[属性[n]] = "减少"..属性[n]
						else
							道具.幻化元身属性[属性[n]] = "增加"..属性[n]
						end
					elseif n == 12 then
						道具.幻化元身属性.特效概率 = dz[n]
						道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
					elseif n == 13 then
						道具.幻化元身属性.特技概率 = dz[n]
						道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
					else
						道具.幻化元身属性[属性[n]] = math.floor(dz[n])
					end
				end
			end
			if 道具.幻化次数==nil then
				道具.幻化次数=0
			end
			道具.幻化次数 = 道具.幻化次数 + 1
			-- 生产附加
			-- if 道具.分类==
			道具.元身序列 = 临时序列
			玩家数据[id].道具:删除陨铁()
			道具属性[物品序号] = 道具
			道具属性[物品序号].识别码 = 取唯一识别码(id)
			常规提示(id,"幻化成功，你获得"..临时类型)
			道具刷新(id)
		else
			道具.幻化元身属性={}
			道具.级别限制 = 160
			local dz = self:幻化公式(临时序列)
			local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
			for n = 1, #属性 do
				if dz[n] ~= nil then
					if n >= 7 and n <= 11 and 道具.元身序列~=25 then
						if dz[n] < 0 then
							道具.幻化元身属性[属性[n]] = "减少"..属性[n]
						else
							道具.幻化元身属性[属性[n]] = "增加"..属性[n]
						end
					elseif n == 12 then
						道具.幻化元身属性.特效概率 = dz[n]
						道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
					elseif n == 13 then
						道具.幻化元身属性.特技概率 = dz[n]
						道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
					else
						道具.幻化元身属性[属性[n]] = math.floor(dz[n])
					end
				end
			end
			if 道具.幻化次数==nil then
				道具.幻化次数=0
			end
			道具.幻化次数 = 道具.幻化次数 + 1
			-- 生产附加
			-- if 道具.分类==
			if 新增概率 > 0 then
				玩家数据[id].道具:删除强化石(强化石序列)
			end
			道具.元身序列 = 临时序列
			玩家数据[id].道具:删除陨铁()
			道具属性[物品序号] = 道具
			道具属性[物品序号].识别码 = 取唯一识别码(id)
			常规提示(id,"幻化成功，你获得"..临时类型)
			道具刷新(id)
		end
	else
		常规提示(id,"#R/幻化失败，幻化材料消失。")
		玩家数据[id].道具:删除陨铁()
		道具属性[物品序号] = nil
		道具刷新(id)
	end
	玩家数据[id].角色:扣除体力(消耗体活,"幻化元身")
	玩家数据[id].角色:扣除活力(消耗体活,"幻化元身")
	玩家数据[id].角色:刷新信息()
end


function 幻化处理类:分解幻化处理(id,幻化名字,幻化序列)
	local 道具 = 物品类()
	道具:置对象(幻化名字)
	道具.幻化元身属性={}
	道具.级别限制 = 160
	local dz = self:幻化公式(幻化序列)
	local 属性 = {"命中", "伤害", "防御", "魔法", "灵力", "气血", "敏捷", "体质", "力量", "耐力", "魔力", "特效", "特技"}
	for n = 1, #属性 do
		if dz[n] ~= nil then
			if n >= 7 and n <= 11 and 道具.名称~="金丝逐日履" then
				if dz[n] < 0 then
					道具.幻化元身属性[属性[n]] = "减少"..属性[n]
				else
					道具.幻化元身属性[属性[n]] = "增加"..属性[n]
				end
			elseif n == 12 then
				道具.幻化元身属性.特效概率 = dz[n]
				道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
			elseif n == 13 then
				道具.幻化元身属性.特技概率 = dz[n]
				道具.幻化元身属性[属性[n]] = "增加"..属性[n].."概率 +"..math.floor(dz[n]).."%"
			else
				道具.幻化元身属性[属性[n]] = math.floor(dz[n])
			end
		end
	end
	if 道具.幻化次数==nil then
		道具.幻化次数=0
	end
	道具.幻化次数 = 道具.幻化次数 + 1
	道具.元身序列 = 幻化序列
	道具.识别码 = 取唯一识别码(id)
	return 道具
end


function 幻化处理类:更新(dt)

end


function 幻化处理类:显示(x,y)

end

return 幻化处理类