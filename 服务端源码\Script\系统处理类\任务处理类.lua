--======================================================================--

--======================================================================--
local 任务处理类 = class()
function 任务处理类:初始化()end

function 任务处理类:取任务说明(玩家id,任务id)
  local 说明={}
  if 任务数据[任务id]==nil then
    return {}
  else -- table.print(任务数据[任务id])

    if 任务数据[任务id].类型==999 then
    return self:取新手指引说明1(玩家id,任务id)
    elseif 任务数据[任务id].类型==998 then
    return self:取商人的鬼魂(玩家id,任务id)
    elseif 任务数据[任务id].类型==898 then
    return self:取妖风支线(玩家id,任务id)
    elseif 任务数据[任务id].类型==997 then
    return self:取玄奘身世(玩家id,任务id)
    elseif 任务数据[任务id].类型==996 then
    return self:大战心魔(玩家id,任务id)
    elseif 任务数据[任务id].类型==400 then
    return self:取新枯萎金莲支线(玩家id,任务id)
    elseif 任务数据[任务id].类型==401 then
    return self:取桃园浣熊支线(玩家id,任务id)
    elseif 任务数据[任务id].类型==402 then
    return self:取新手指引任务(玩家id,任务id)

    elseif 任务数据[任务id].类型==390 then
          return 嘉年华:任务说明(玩家id,任务id)
    elseif 任务数据[任务id].类型==398 then
          return 归墟活动:任务说明(任务id)

  	elseif 任务数据[任务id].类型>=7000 and 任务数据[任务id].类型<=7500 then --7000-7100留给副本
  	     return 副本处理类:取副本任务说明(玩家id,任务id)
    elseif 任务数据[任务id].类型==6666 then
            说明=彩虹争霸:任务说明(玩家id,任务id)




    end

    if 任务数据[任务id].类型==1 then
      说明={"变身卡","#G你的变身效果还可持续#R/"..取分(任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)).."#G/分钟。"}
    elseif 任务数据[任务id].类型==2 then
      说明={"双倍时间","#G你的双倍时间还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。","野外战斗、抓鬼、封妖、官职、种族等活动任务中所获得的经验、银子、储备翻倍。本效果可以与精修时间叠加。"}
    elseif 任务数据[任务id].类型==3 then
      说明={"精修时间","#G你的精修时间还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。","野外战斗、抓鬼、封妖、官职、种族等活动任务中所获得的经验、银子、储备翻倍。本效果可以与双倍时间叠加。"}
    elseif 任务数据[任务id].类型==4 then
      说明={"宝图任务",format("前往#Y/%s(%s,%s)#W/#S/处缉拿强盗#R/%s",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得少量金钱、几率获得藏宝图"}
    elseif 任务数据[任务id].类型==209 then
      说明={"悬赏任务",format("前往#Y/%s(%s,%s)#W/#S/处缉拿#R/%s#S/。此任务难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与高级宝石"}
    elseif 任务数据[任务id].类型==5 then
      说明={"打造任务",format("#S/你的#R/%s#S正在制作中，但还需要%s块%s才可完成。请赶紧去寻找吧。",任务数据[任务id].名称,任务数据[任务id].数量,任务数据[任务id].石头)}
   elseif 任务数据[任务id].类型==6 then
      说明={"赏金任务",format("在#R/大雁塔1-6层，花果山，大唐国境，大唐境外，长寿郊外#完成指定的战斗次数\n你还需要在这些场景中\n战斗#Y%s#次才可获得奖励。",任务数据[任务id].数量),"可获得经验、银子或储备"}
    elseif 任务数据[任务id].类型==66 then
      说明={"新手任务",format("在#R/江南野外，东海湾，东海岩洞，东海海底，沉船#完成指定的战斗次数\n你还需要在这些场景中\n战斗#Y%s#次才可获得奖励。",任务数据[任务id].数量),"可获得经验、银子或储备"}

    elseif 任务数据[任务id].类型==7 then
      if 科举数据[玩家id]==nil then
        说明={"科举活动","数据异常，已取消该任务，请重新打开任务界面刷新纪录"}
        玩家数据[玩家id].角色:取消任务(任务id)
        return 说明
      end
      说明={"科举活动",format("#S/你当前正在参加科举活动，请在#R/%s#S/分内完成本活动。逾期将自动失去本轮活动资格。当前已回答#R/%s#S/道题目，其中回答正确#R/%s#S/道,回答错误#R/%s#S/道",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))),科举数据[玩家id].总数,科举数据[玩家id].答对,科举数据[玩家id].答错),"可获得经验、银子、物品奖励"}
    elseif 任务数据[任务id].类型==8 then
      说明={"捉鬼任务",format("前往#Y/%s(%s,%s)#W/#S/处降服#G/%s#R(当前第%s次)",任务数据[任务id].地图名称,任务数据[任务id].显示x,任务数据[任务id].显示y,任务数据[任务id].名称,玩家数据[玩家id].角色.数据.捉鬼次数),"可获得经验、银子，第10次任务可几率获得物品"}
     elseif 任务数据[任务id].类型==211 then
      说明={"鬼王任务",format("前往#Y/%s(%s,%s)#W/#L/处缉拿#R/%s#L/。#R/(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得人物修炼"}
    elseif 任务数据[任务id].类型==9 then
      说明={"摄妖香","#G你的摄妖香效果还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。","在低于自身等级+10的场景中不会触发暗雷战斗。"}
    elseif 任务数据[任务id].类型==7755 then
      说明={"一倍经验丹","#G你的一倍经验丹效果还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。"}
    elseif 任务数据[任务id].类型==7756 then
      说明={"双倍经验丹","#G你的双倍经验丹效果还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。"}
    elseif 任务数据[任务id].类型==7757 then
      说明={"双倍掉宝符","#G你的双倍掉宝符效果还可持续#R/"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."#G/分钟。"}
    elseif 任务数据[任务id].类型==10 then
      说明={"罗羹效果",format("#G/在每场战斗结束后\n都将自动恢复气血和魔法\n当前还可恢复:\n#P/气血:#G/%s#P/次\n#P/魔法:#G/%s#P/次",任务数据[任务id].气血,任务数据[任务id].魔法)}
    elseif 任务数据[任务id].类型==11 then
       local sjx = {地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"初出江湖",format("前往#Y/%s(%s,%s)#W/处查明@的身份#R(当前第%s次)",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,玩家数据[玩家id].角色.数据.江湖次数),"可获得经验、银子、储备金",sjx}
    elseif 任务数据[任务id].类型==12 then
       local sjx = {地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"皇宫飞贼",format("去%s的%s,%s缉拿@,剩余时间%s分钟。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"可获得经验、银子、储备金",sjx}
    elseif 任务数据[任务id].类型==200 then
      说明={"降妖除魔",format("去#G%s的#Y%s,%s消灭\n#R%s\n#Z剩余时间%s分钟。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"可获得经验、银子、储备金"}
    elseif 任务数据[任务id].类型==13 then
      if 任务数据[任务id].分类==11  then
        说明={"修炼任务",format("#S找到%s的%s,当前第%s环，任务积分%s分。",任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].次数,任务数据[任务id].积分),"可获得召唤兽修炼经验，在完成第100次任务时将获得随机书铁"}
      elseif 任务数据[任务id].分类==12 or 任务数据[任务id].分类==13 or 任务数据[任务id].分类==14 then
        说明={"修炼任务",format("#S将#Y%s#S交给%s的%s,当前第%s环，任务积分%s分。",任务数据[任务id].物品,任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].次数,任务数据[任务id].积分),"可获得召唤兽修炼经验，在完成第100次任务时将获得随机书铁。本任务所需的物品可以在自身等级对应的场景暗雷战斗中几率获得。"}
      elseif 任务数据[任务id].分类==15 then
        说明={"修炼任务",format("#S将#Y变异%s#S交给%s的%s,当前第%s环，任务积分%s分。",任务数据[任务id].bb,任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].次数,任务数据[任务id].积分),"可获得召唤兽修炼经验，在完成第100次任务时将获得随机书铁。本任务所需的变异召唤兽可以在自身等级对应的场景暗雷战斗中几率遇见。"}
      end
    elseif 任务数据[任务id].类型==14 then
      说明={"点化任务","#S找到长安城五行大师进行装备点化。"}


    elseif 任务数据[任务id].类型==17 then
      local 进程 = 任务数据[任务id].进程
      说明={"天降晨星",format("请前往#Y/%s(%s,%s)#W/处挑战#R/%s#W分影!",取地图名称(Q_天降辰星[进程].地图),Q_天降辰星[进程].x,Q_天降辰星[进程].y,Q_天降辰星[进程].名称),"完成后可获得经验、银子、物品奖励"}
    elseif 任务数据[任务id].类型==18 then
      说明={"九耀星君",format("抓紧前往#Y/%s(%s,%s)#W/处接受星君#R/%s#W/的试炼",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得大量金钱、经验、物品"}
    elseif 任务数据[任务id].类型==22 then
      说明={"摇钱树",format("你在#Y/%s(%s,%s)#W/#S/附近种下了一颗摇钱树",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y),"完成后可获得随机物品、银子、仙玉奖励"}
    elseif 任务数据[任务id].类型==24 then
      说明={"江湖大盗",format("#Y/%s(%s,%s)#W/#S/附近的强盗蠢蠢欲动,意欲图谋你的摇钱树果实！",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y),"完成后增加一次摇晃次数！"}
    elseif 任务数据[任务id].类型==107 then
      说明={"门派闯关",format("你正在进行门派闯关活动，请立即前往#Y/%s#S/接受门派护法考验,截止到目前为止，你已成功完成了#R/%s#S/次考验。",任务数据[任务id].闯关序列[任务数据[任务id].当前序列],15-#任务数据[任务id].闯关序列),"可获得经验、银子、物品奖励"}
    elseif 任务数据[任务id].类型==109 then
       说明={"游泳比赛",format("请立即前往#Y/%s(%s,%s)#S/%s号裁判处报道。",取地图名称(Q_游泳数据[任务数据[任务id].序列].z),Q_游泳数据[任务数据[任务id].序列].x,Q_游泳数据[任务数据[任务id].序列].y,任务数据[任务id].序列),"可获得经验、储备奖励"}
     elseif 任务数据[任务id].类型==112 then---------远方文韵墨香
      if 任务数据[任务id].分类==1 then
        说明={"文韵墨香",format("文韵期间，替#G文韵使者送信给#Y%s的#S%s",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物),"完成后可获得道具文韵积分奖励"}
      elseif 任务数据[任务id].分类==2 then
        说明={"文韵墨香","科举大赛，最近考场飞贼出现,请前往本门派场景内巡逻，打败毛贼！","完成后可获得道具文韵积分奖励"}
      elseif 任务数据[任务id].分类==3 then
        if 任务数据[任务id].要求==nil then
          说明={"文韵墨香",format("考场建设，捕捉到#Y%s#G交给文韵使者",任务数据[任务id].bb),"完成后可获得道具文韵积分奖励"}
        else
          说明={"文韵墨香",format("考场建设，捕捉到#R%s#S的#Y%s#G交给文韵使者",任务数据[任务id].要求,任务数据[任务id].bb),"完成后可获得道具文韵积分奖励"}
        end
      elseif 任务数据[任务id].分类==4 then
        if 任务数据[任务id].品质==nil then
          说明={"文韵墨香",format("寻找到#Y%s#W交给#G文韵使者",任务数据[任务id].物品),"完成后可获得道具文韵积分奖励"}
        else
          说明={"文韵墨香",format("寻找到#Y%s#W交给#G文韵使者(如果上交的物品品质达#R%s#S可获得双倍奖励)",任务数据[任务id].物品,任务数据[任务id].品质),"完成后可获得道具文韵积分奖励"}
        end
      elseif 任务数据[任务id].分类==5 then
        if 任务数据[任务id].显示坐标==nil then
          说明={"文韵墨香",format("前往#Y%s#W教训#R%s",任务数据[任务id].地图名称,任务数据[任务id].名称),"完成后可获得道具文韵积分奖励"}
        else
          说明={"文韵墨香",format("前往#Y%s(%s,%s)#W教训#R%s",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得道具文韵积分奖励"}
        end
      elseif 任务数据[任务id].分类==6 then
        说明={"文韵墨香",format("请前往#Y/%s(%s,%s)#W/处支援本考场的帮手。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y),"完成后可获得道具文韵积分奖励"}
      elseif 任务数据[任务id].分类==7 then
        if 任务数据[任务id].乾坤袋==nil then
          说明={"文韵墨香",format("请前往#Y/%s(%s,%s)#S/处降服#R/%s#W，此战斗必须使用#G乾坤袋。。",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得道具文韵积分奖励"}
        else
          说明={"文韵墨香","将#G乾坤袋交给#R文韵使者","完成后可获得道具文韵积分奖励"}
        end
      end
      说明[2]=说明[2]..format("\n#G/(当前第%s次)",玩家数据[玩家id].角色.数据.文韵次数)
    elseif 任务数据[任务id].类型==120 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.乌鸡国.进行[副本id]==nil then
        说明={"乌鸡国","#S您的副本已经完成"}
      else
        local 进程=副本数据.乌鸡国.进行[副本id].进程
        local 序列=副本数据.乌鸡国.进行[副本id].序列
        if 进程==1 then
          说明={"乌鸡国",format("#S消灭15只芭蕉木妖\n#G(完成度：%s：15)",副本数据.乌鸡国.进行[副本id].木妖数量)}
        elseif 进程==2 then
          说明={"乌鸡国",format("#S寻求15个热心仙人的帮忙\n#G(完成度：%s：15)",副本数据.乌鸡国.进行[副本id].和尚数量)}
        elseif 进程==3 then
          local 名称={"囚神妖怪","拘灵妖怪","缚仙妖怪"}
          local 表述={}
          for n=1,3 do
            if 序列[n]~=true then
              表述[#表述+1]=名称[n]
            end
          end
          local 名称=""
          for n=1,#表述 do
            if n==#表述 then
              名称=名称..表述[n]
            else
              名称=名称..表述[n].."、"
            end
          end
          说明={"乌鸡国",format("#S消灭皇宫的#R%s#S",名称)}
        elseif 进程==4 then
          说明={"乌鸡国",format("#S消灭5个鬼祟小怪\n#G(完成度：%s：5)",副本数据.乌鸡国.进行[副本id].鬼祟数量)}
        elseif 进程==5 then
          说明={"乌鸡国",format("#S寻找出真正的乌鸡国王，与真正的国王战斗胜利后本副本将自动结束")}
        end
          说明[3]="完成后可获得经验、银子、物品奖励"
      end
    elseif 任务数据[任务id].类型==130 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.车迟斗法.进行[副本id]==nil then
        说明={"车迟斗法","#S您的副本已经完成"}
      else
        local 进程=副本数据.车迟斗法.进行[副本id].进程
        local 序列=副本数据.车迟斗法.进行[副本id].序列
        if 进程==1 then
          说明={"车迟斗法",format("#S通过找到有个道士回答问题获得木材完成道观建设\n#G(完成度：%s：5)",副本数据.车迟斗法.进行[副本id].车迟木材)}
        elseif 进程==2 then
          说明={"车迟斗法",format("#S找到有个和尚帮忙")}
        elseif 进程==3 then
          说明={"车迟斗法",format("#S帮助有个和尚干掉贡品\n#G(完成度：%s：15)",副本数据.车迟斗法.进行[副本id].车迟贡品)}
        elseif 进程==4 then
          local 名称={"道德天尊","灵宝天尊","元始天尊"}
          local 表述={}
          for n=1,3 do
            if 序列[n]~=true then
              表述[#表述+1]=名称[n]
            end
          end
          local 名称=""
          for n=1,#表述 do
            if n==#表述 then
              名称=名称..表述[n]
            else
              名称=名称..表述[n].."、"
            end
          end
          说明={"车迟斗法",format("#S消灭假扮的#R%s#S",名称)}
        elseif 进程==5 then
          说明={"车迟斗法",format("#S妖怪都跑到九霄云外，找到有个和尚问问")}
        elseif 进程==6 then
          说明={"车迟斗法",format("#S找到有个神仙帮忙")}
        elseif 进程==7 then
          local 名称={"雷公","电母","雨师"}
          if 副本数据.车迟斗法.进行[副本id].不动 then
            名称={"你不动","你不动","我不动"}
          end
          local 表述={}
          for n=1,3 do
            if 序列[n]~=true then
              表述[#表述+1]=名称[n]
            end
          end
          local 名称=""
          for n=1,#表述 do
            if n==#表述 then
              名称=名称..表述[n]
            else
              名称=名称..表述[n].."、"
            end
          end
          说明={"车迟斗法",format("#S消灭#R%s#S",名称)}
        elseif 进程==8 then
          说明={"车迟斗法",format("#S找到电母打听妖怪的下落")}
        elseif 进程==9 then
          说明={"车迟斗法",format("#S找到大圣进行变化")}
        elseif 进程==10 then
          说明={"车迟斗法",format("#S变化强化了，去找假道士算帐")}
        elseif 进程==11 then
          说明={"车迟斗法",format("#S恭喜你，完成了车迟斗法，去领取自己的奖励宝箱吧！！!")}
        elseif 进程==12 then
          说明={"车迟斗法",format("#S恭喜你，完成了车迟斗法，获得了更多的宝箱！！!")}
        end
          说明[3]="完成后可获得经验、银子、物品奖励"
      end
    elseif 任务数据[任务id].类型==150 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.水陆大会.进行[副本id]==nil then
        说明={"水陆大会","#S您的副本已经完成"}
      else
        local 进程=副本数据.水陆大会.进行[副本id].进程
        if 进程==1 then
          说明={"水陆大会",format("#S找到道场督僧，看看有什么需要帮忙的")}
        elseif 进程==2 then
          说明={"水陆大会",format("#S水陆大会的道场建设完成了，与玄奘法师对话")}
        elseif 进程==3 then
          说明={"水陆大会",format("#S程咬金似乎受伤了，赶紧去问问是怎么回事吧。")}
        elseif 进程==4 then
          说明={"水陆大会",format("#S原众人被突闪出现的妖魔所掳，请各位速去道场后院打探\n触发#R虎翼将军#和#R蝰蛇将军#\n两场战斗\n当前阶段#G两场战斗必输")}
        elseif 进程==5 then
          说明={"水陆大会",format("#S道场后院妖魔好是凶猛，听闻长安土地公公消息，观音幻化成#R%s#S和#R%s#S模样正寻经善之人，想必有破解之法，快去寻找寻找\n#G触发两个NPC对话后#\n就可以回来\n击杀#R虎翼#和#R蝰蛇将军#了!",副本数据.水陆大会.进行[副本id].人物[1].名称,副本数据.水陆大会.进行[副本id].人物[2].名称)}
        elseif 进程==6 then
          说明={"水陆大会",format("#S终于消灭了妖魔，找土地公公打听下其他妖魔的下落")}
        elseif 进程==7 then
          说明={"水陆大会",format("#S找到罪魁祸首，消灭所有妖魔，救出唐王和玄奘法师\n#G把妖魔巢穴的怪物全部清掉\n才能击杀#R魑魅魍魉")}
        elseif 进程==8 then
          说明={"水陆大会",format("#S终于救出了唐王和玄奘法师，好像唐王有话对你说")}
        end
      end
    elseif 任务数据[任务id].类型==160 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.通天河.进行[副本id]==nil then
        说明={"通天河","#S您的副本已经完成"}
      else
        local 进程=副本数据.通天河.进行[副本id].进程
        if 进程==1 then
          说明={"通天河",format("#S向小朋友们打听#R一秤金、陈关保#S的下落，找到后完成变身\n#G(完成度：%s：5)",副本数据.通天河.进行[副本id].通天答题)}
        elseif 进程==2 then
          说明={"通天河",format("#S找到#R一秤金、陈关保#S进行变身。(当前还有%s个玩家没变身)",(5-副本数据.通天河.进行[副本id].变身次数))}
        elseif 进程==3 then
          说明={"通天河",format("#S找到灵灯(23,176)，触摸等待灵感大王的到来。")}
        elseif 进程==4 then
          说明={"通天河",format("#S灵感大王恼羞成怒，派出河妖进行灭村，少侠快去找到唐僧")}
        elseif 进程==5 then
          说明={"通天河",format("#S保护陈家庄，消灭河妖\n#G(完成度：%s：5)",副本数据.通天河.进行[副本id].河妖)}
        elseif 进程==6 then
          说明={"通天河",format("#S唐僧被灵感大王掳走，去普陀山向观音姐姐求助，#R散财童子#S和#R黑熊精#S横加阻拦，打败他们！")}
        elseif 进程==7 then
          说明={"通天河",format("#S找到观音姐姐求助")}
        elseif 进程==8 then
          说明={"通天河",format("#S灵感大王原是潮音洞的鲤鱼所变。众人帮忙采集五色竹条，编织成降魔鱼篮对付妖怪\n#G(完成度：%s：50)",副本数据.通天河.进行[副本id].五色竹条)}
        elseif 进程==9 then
          说明={"通天河",format("#S降魔鱼篮已经完成，灵感大王出现在通天河底，去妖怪洞府查看下情况。")}
        elseif 进程==10 then
          说明={"通天河",format("#S进入了灵感大王的腹中，带着降魔鱼篮找到灵感大王的弱点，消灭它。")}
        end
      end
    elseif 任务数据[任务id].类型==191 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.齐天大圣.进行[副本id]==nil then
        说明={"齐天大圣","#您的副本已经完成"}
      else
        local 进程=副本数据.齐天大圣.进行[副本id].进程
        if 进程==1 then
          说明={"齐天大圣",format("#前往花果山山前安抚#R伤心的小猴#当前剩余: %s、#R临死的老猴#当前剩余: %s",副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴,副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴)}
        elseif 进程==2 then
          说明={"齐天大圣",format("#R黑白无常#前来拘魂濒死的老猴,前往(110,103)附近与其交涉")}
        elseif 进程==3 then
          说明={"齐天大圣",format("#得悉生死簿之秘,前往地府寻找崔判官。")}
        elseif 进程==4 then
          说明={"齐天大圣",format("#获知生死簿保存在阎王手中，向阎王索取生死簿！")}
        elseif 进程==5 then
          说明={"齐天大圣",format("#拿来生死簿,我改改改！天庭获悉后,派遣太白金星(110,103)前来招安！")}
        elseif 进程==6 then
          说明={"齐天大圣",format("#来到天宫面见玉帝,挑战四大天王,向玉帝展示实力！")}
        elseif 进程==7 then
          说明={"齐天大圣",format("#实力展示完毕,找玉帝授封吧！")}
        elseif 进程==8 then
          说明={"齐天大圣",format("#发现盗马贼,前往驱逐盗马贼吧！剩余盗马贼:#R%s",副本数据.齐天大圣.进行[副本id].盗马贼)}
        elseif 进程==9 then
          说明={"齐天大圣",format("#一匹调皮的小马跑了出去,抓紧快去巡回吧。")}
        elseif 进程==10 then
          说明={"齐天大圣",format("#近日闲来无事,初来天庭四处逛逛吧!(对话天蓬元帅)。")}
        elseif 进程==11 then
          说明={"齐天大圣",format("#玉帝大怒，命托塔天王率巨灵神下界降服你，百万天兵闯入花果山,喝退百万天兵,打退巨灵神后对话李靖。")}
        elseif 进程==12 then
          说明={"齐天大圣",format("#托塔天王使出七宝玲珑塔，进入塔中挑战镇塔神灵。")}
        end
      end
    elseif 任务数据[任务id].类型==180 then
      local 副本id=任务数据[任务id].副本id
      if 副本数据.大闹天宫.进行[副本id]==nil then
        说明={"大闹天宫","#您的副本已经结束"}
      else
        local 进程=副本数据.大闹天宫.进行[副本id].进程
        if 进程==1 then
          说明={"大闹天宫",format("#帮助蟠桃园中的#R锄树力士、云水力士、修桃力士#完成任务。(完成度：%s：15)",(任务数据[任务id].完成三大力士 or 0))}
        elseif 进程==2 then
          说明={"大闹天宫",format("#蟠桃园整修一新,竟唤来一阵清风,吹落些许蟠桃,各位请速速前往拾取(当前剩余：%s))",(任务数据[任务id].蟠桃))}
        elseif 进程==3 then
          说明={"大闹天宫",format("#恰巧七仙女奉王母之命前来蟠桃园取仙桃,竟然发现大圣你竟然在偷食蟠桃#R(需战斗))")}
        elseif 进程==4 then
          说明={"大闹天宫",format("#蟠桃会已经开始了,你变化为赤脚大仙前往蟠桃会欲要搅闹一番分别与#R造酒仙官、运水道人、烧火童子、盘槽力士#对话#R(部分需战斗))")}
        elseif 进程==5 then
          说明={"大闹天宫",format("#回到花果山的你将蟠桃宴中的美食一一分给饥饿的小猴。#R(剩余：%s)",任务数据[任务id].饥饿小猴)}
        elseif 进程==6 then
          说明={"大闹天宫",format("#玉帝派遣十万天兵天将下界捉你，正与崩芭二将打的难分难舍,见状你急忙前去帮忙。#R(需战斗)")}
        elseif 进程==7 then
          说明={"大闹天宫",format("#诸神虽然神通勇猛，但也拿你毫无办法，被一一击退。正在此时，来自普陀山的观音姐姐出现，并带来了两员救兵手下弟子惠岸行者以及二郎真君。#R(需战斗)")}
        elseif 进程==8 then
          说明={"大闹天宫",format("#艰难的打败了二郎真君，你来到了天宫外欲找玉帝理论一二。#R(需战斗)")}
        end
      end
    elseif 任务数据[任务id].类型==181 then
      说明={"大闹天宫",format("#帮助蟠桃园中的#R锄树力士#前往#R(%s,%s)#K处附近进行锄树清草吧！",任务数据[任务id].x,任务数据[任务id].y)}
    elseif 任务数据[任务id].类型==182 then
      说明={"大闹天宫",format("#帮助蟠桃园中的#R浇水力士#前往#R%s#处为其浇水吧！",任务数据[任务id].目标)}
    elseif 任务数据[任务id].类型==183 then
      说明={"大闹天宫",format("#帮助蟠桃园中的#R修桃力士#前往#R%s#处修整蟠桃树吧！",任务数据[任务id].目标)}


    elseif 任务数据[任务id].类型==208 then
      说明={"镖王大赛",format("请将这批%s镖银押送至#R%s(%s,%s)#S的镖师处，为了确保镖银的安全，押送过程中将无法使用飞行道具。",任务数据[任务id].难度,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y),"可获得经验、银子，可几率获得物品。押送高级镖银和珍贵镖银将几率获得物品鬼谷子。"}
    elseif 任务数据[任务id].类型==300 then
      说明={"押镖任务",format("请帮我把镖银送给#Y%s#S的#R%s，#S事出紧急，切勿在路上耽搁。当前#R第%s镖",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物,任务数据[任务id].次数,"#S可获得经验、银子，可几率获得物品。押送将几率获得物品鬼谷子。")}
    elseif 任务数据[任务id].类型==301 then
      if 任务数据[任务id].分类 == 1 then
          说明={"青龙任务",format("请帮我将这封书信送给#Y%s#S的#R%s#S，事出紧急，切勿在路上耽搁。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)}
      elseif 任务数据[任务id].分类 == 2 then
          说明={"青龙任务",format("近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].药品)}
      elseif 任务数据[任务id].分类 == 3 then
          说明={"青龙任务",format("近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].烹饪)}
      else
          说明={"青龙任务",format("最近常有敌对门派探子在长安城内出现，还请你在四周巡逻一番。")}
      end
    elseif 任务数据[任务id].类型==302 then
      if 任务数据[任务id].分类 == 1 then
          说明={"玄武任务",format("请帮我将这封书信送给#Y%s#S的#R%s#S，事出紧急，切勿在路上耽搁。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)}
      elseif 任务数据[任务id].分类 == 2 then
          说明={"玄武任务",format("近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].药品)}
      elseif 任务数据[任务id].分类 == 3 then
          说明={"玄武任务",format("近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].烹饪)}
      else
          说明={"玄武任务",format("最近常有敌对门派探子在长安城内出现，还请你在四周巡逻一番。")}
      end
    elseif 任务数据[任务id].类型==110 then
      if 任务数据[任务id].分类==1 then
        说明={"官职任务",format("缉拿在#Y%s(%s,%s)\n#S/处闹事的流氓\n",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y),"完成后可获得经验、储备奖励"}
      elseif 任务数据[任务id].分类==2 then
        说明={"官职任务",format("将物资交给#Y/%s(%s,%s)\n#S/处的军需官\n",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y),"完成后可获得经验、储备奖励"}
      elseif 任务数据[任务id].分类==3 then
        if 任务数据[任务id].情报==false then
          说明={"官职任务","在#R/大唐国境、大唐境外、长寿郊外、花果山、北俱芦洲\n#S/处收集情报\n正在进行此任务的玩家不会触发暗雷战斗\n","完成后可获得经验、储备奖励"}
        else
          说明={"官职任务","将得到的情报交给李将军\n","完成后可获得经验、储备奖励"}
        end
      elseif 任务数据[任务id].分类==4 then
        说明={"官职任务",format("找到交给#Y/%s\n#S/后交给李将军\n",任务数据[任务id].物品),"完成后可获得经验、银子奖励"}
      end
      说明[2]=说明[2]..format("#G/(当前第%s次)",玩家数据[玩家id].角色.数据.官职次数)
    elseif 任务数据[任务id].类型==111 then
      if 任务数据[任务id].分类==1 then
        说明={"门派任务",format("替师傅送信给\n#Y%s的\n#S%s\n",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物),"完成后可获得经验、银子、门派贡献度奖励"}
      elseif 任务数据[任务id].分类==2 then
        说明={"门派任务","在本门派场景内巡逻\n","完成后可获得经验、银子、门派贡献度奖励"}
      elseif 任务数据[任务id].分类==3 then
        if 任务数据[任务id].要求==nil then
          说明={"门派任务",format("捕捉到\n#Y%s\n#S交给师傅\n",任务数据[任务id].bb),"完成后可获得经验、储备奖励"}
        else
          说明={"门派任务",format("捕捉到\n#R%s\n#S的#Y%s\n#S交给师傅。",任务数据[任务id].要求,任务数据[任务id].bb),"完成后可获得经验、储备奖励"}
        end
      elseif 任务数据[任务id].分类==4 then
        if 任务数据[任务id].品质==nil then
          说明={"门派任务",format("寻找到\n#Y%s\n#S交给师傅\n",任务数据[任务id].物品),"完成后可获得经验、储备奖励"}
        else
          说明={"门派任务",format("寻找到#Y%s#S交给师傅(如果上交的物品品质达#R%s#S可获得双倍奖励)",任务数据[任务id].物品,任务数据[任务id].品质),"完成后可获得经验、储备奖励"}
        end
      elseif 任务数据[任务id].分类==5 then
        if 任务数据[任务id].显示坐标==nil then
          说明={"门派任务",format("前往#Y%s#S教训#R%s",任务数据[任务id].地图名称,任务数据[任务id].名称),"完成后可获得经验、银子、门派贡献度奖励"}
        else
          说明={"门派任务",format("前往#Y%s(%s,%s)#S教训#R%s",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得经验、银子、门派贡献度奖励"}
        end
      elseif 任务数据[任务id].分类==6 then
        说明={"门派任务",format("请前往#Y/%s(%s,%s)#S/处支援本门派弟子。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y),"完成后可获得经验、银子、门派贡献度奖励"}
      elseif 任务数据[任务id].分类==7 then
        if 任务数据[任务id].乾坤袋==nil then
          说明={"门派任务",format("请前往#Y/%s(%s,%s)#S/处降服#R/%s#S，此战斗必须使用乾坤袋。。",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称),"完成后可获得经验、银子、门派贡献度奖励"}
        else
          说明={"门派任务","将乾坤袋交给师傅","完成后可获得经验、银子、门派贡献度奖励"}
        end
      end
      说明[2]=说明[2]..format("#G/(当前第%s次)",玩家数据[玩家id].角色.数据.师门次数)
    elseif 任务数据[任务id].类型==307 then
      if 任务数据[任务id].分类==1 then
        说明={"坐骑剧情任务","快去找#Y长寿村#S的#R太白金星#S咨询下吧！"}
      elseif 任务数据[任务id].分类==2 then
        说明={"坐骑剧情任务",format("帮#Y/太白金星#S找到#R/%s",任务数据[任务id].烹饪)}
      elseif 任务数据[任务id].分类==3 then
        说明={"坐骑剧情任务","#Y天宫#S的#R千里眼#S能眼观天下，他也许知道天马的消息！"}
      elseif 任务数据[任务id].分类==4 then
        说明={"坐骑剧情任务","帮#R千里眼#S把书信送到#R大力神灵#S处(#R需战斗#S)"}
      elseif 任务数据[任务id].分类==5 then
        说明={"坐骑剧情任务","看看#R千里眼#S是不是查探到什么了"}
      elseif 任务数据[任务id].分类==6 then
        说明={"坐骑剧情任务","找朱紫国#R土地公公#S看看有没有发现"}
      elseif 任务数据[任务id].分类==7 then
        说明={"坐骑剧情任务","找到朱紫国皇宫附近的#R妖魔亲信#S(#R需战斗#S)"}
      elseif 任务数据[任务id].分类==8 then
        说明={"坐骑剧情任务","找到#R蜃妖元神#S(#R需战斗#S)"}
      elseif 任务数据[任务id].分类==9 then
        说明={"坐骑剧情任务","去找找#R太白金星#S看看什么情况。"}
      elseif 任务数据[任务id].分类==10 then
        说明={"坐骑剧情任务","天下间的动物何去何从都逃不过#Y狮驼岭#S的#R大大王#S的眼睛，去调查一番。"}
      elseif 任务数据[任务id].分类==11 then
        说明={"坐骑剧情任务",format("帮#Y/大大王#S找到#R/%s",任务数据[任务id].药品)}
      elseif 任务数据[任务id].分类==12 then
        说明={"坐骑剧情任务","马儿在#Y建业#S一带遗失，#R周猎户#S十分可疑，前去调查。"}
      elseif 任务数据[任务id].分类==13 then
        说明={"坐骑剧情任务","终于找到了马儿，可以去找#R百兽王#S复命了。"}
      end
    elseif 任务数据[任务id].类型==308 then
      if 任务数据[任务id].分类 == 1 then
        说明={"法宝任务",format("#Y%s#W的#R%s#W正需要帮忙，少侠可以去看看，或许有意外的收获。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)}
      elseif 任务数据[任务id].分类 == 2 then
        说明={"法宝任务",format("#G%s#S近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].法宝NPC,任务数据[任务id].物品)}
      elseif 任务数据[任务id].分类 == 3 then
        说明={"法宝任务",format("#G%s#S近日物资紧缺，请你前寻找#R%s#S，事出紧急，切勿在路上耽搁。",任务数据[任务id].法宝NPC,任务数据[任务id].物品)}
      else
        说明={"法宝任务",format("#Y%s#W的#R%s#W仗着自己会点武功，到处惹是生非，希望少侠能为名除害(#R需战斗#W)。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)}
      end
    elseif 任务数据[任务id].类型==309 then
      说明={"法宝任务",format("内丹是修炼者经过百年甚至千年的修炼在体内以灵气结成的丹丸，#Y%s#W正在#R%s#Y%s#W,#Y%s四处为祸人间，少侠可前往收服该妖魔获取内丹。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)}
    elseif 任务数据[任务id].类型==311 then
      if 任务数据[任务id].进程 == 1 then
        说明={"魔童哪吒","#Y大雁塔#W的#R太乙真人#W需要帮忙，少侠可以去看看，或许有意外的收获(#R需战斗#W)"}
      elseif 任务数据[任务id].进程 == 2 then
        说明={"魔童哪吒","#W师傅让你去找#Y大雁塔二层#W的#R敖丙#W问问魔童的情况！,事出紧急，切勿在路上耽搁(#R需战斗#W)"}
      elseif 任务数据[任务id].进程 == 3 then
        说明={"魔童哪吒","#W敖丙师傅#R申公豹#W在#R大雁塔三层#W我们去找他理论理论,事出紧急，切勿在路上耽搁(#R需战斗#W)"}
      elseif 任务数据[任务id].进程 == 4 then
        说明={"魔童哪吒","#R龙王#W可能知道，你们可以去#R大雁塔四层#W去找他,事出紧急，切勿在路上耽搁(#R需战斗#W)"}
      elseif 任务数据[任务id].进程 == 5 then
        说明={"魔童哪吒","#W所谓的天命魔丸转世的#R哪吒#W在#R大雁塔五层#W赶紧去找他,事出紧急，切勿在路上耽搁(#R需战斗#W)"}
      end
    elseif 任务数据[任务id].类型==346 then
      说明={"侠义任务",format("最近缺少#G/%s#R/%s#W，麻烦少侠帮我找来。",任务数据[任务id].物品类型,任务数据[任务id].物品)}
    elseif 任务数据[任务id].类型==350 then
      说明={"贼王的线索",format("前往#Y/%s(%s,%s)#W/#S/处缉拿#R/%s#S/。此任务难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与高级宝石"}
    elseif 任务数据[任务id].类型==352 then
      说明={"水陆大会",format("去道场的后院#R%s,%s#W附近的#R蟠桃树#W上查找一下泼猴的踪迹。",任务数据[任务id].x,任务数据[任务id].y)}
    elseif 任务数据[任务id].类型==353 then
      说明={"水陆大会",format("穿过道场前门，到繁华京城给#R%s（%s，%s）#W送上邀帖。",任务数据[任务id].人物,任务数据[任务id].x,任务数据[任务id].y)}
    elseif 任务数据[任务id].类型==409 then
        说明={"离线经验加成",format("#/离线时所获取的经验，在获取经验的时候进行额外加成，当前还剩余#R/%s#/点离线经验。",任务数据[任务id].离线经验)}
    elseif 任务数据[任务id].类型==410 then
        说明={"离线储备加成",format("#/离线时所获取的储备，在获取经验的时候进行额外加成，当前还剩余#R/%s#/点储备。",任务数据[任务id].离线储备)}
    elseif 任务数据[任务id].类型==212 then
      if 任务数据[任务id].分类==1  then
        说明={"春节任务",format("#L/前往#G/%s(%s,%s)#W/#L/处降服调皮的#G/小年兽#R/(剩余%s分钟)",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
      elseif 任务数据[任务id].分类==2  then
        说明={"春节任务",format("#L将收集到的#G/%s#L/交给我#R/(剩余%s分钟)",任务数据[任务id].物品,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}
      elseif 任务数据[任务id].分类==3 then
        local sjx = {地图=任务数据[任务id].地图,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
        说明={"春节任务",format("#L/前往#G/%s(%s,%s)#W/#L/找到@#L/代我问候#R/(剩余%s分钟)",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),nil,sjx}
      end
   elseif 任务数据[任务id].类型==374 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务",format("前往#Y/%s(%s,%s)#W/处查明@的身份#R(当前第%s次)",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,仙缘次数[玩家id]),"可获得经验、银子、储备金",NPC}

    elseif 任务数据[任务id].类型==375 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(一)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==376 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(二)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

      elseif 任务数据[任务id].类型==377 then
        local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(三)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==378 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(四)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==379 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(五)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==380 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(六)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==381 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(七)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==382 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(八)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==383 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(九)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}

     elseif 任务数据[任务id].类型==384 then
      local  NPC={地图=任务数据[任务id].地图编号,x=任务数据[任务id].x,y=任务数据[任务id].y,名称=任务数据[任务id].名称}
      说明={"仙缘任务(十)",format("前往#Y/%s(%s,%s)#W/#S/处找到@寻得机缘。仙缘难度较高，请尽量与一些实力超群的玩家组队完成。#R(剩余%s分钟)",取地图名称(任务数据[任务id].地图编号),任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称,取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始)))),"完成后可获得大量储备与物品奖励！",NPC}
    elseif 任务数据[任务id].类型==411 then
        说明={"会员地图",format("#G当前地图不可更换队伍,停留期间不要飞出地图,飞出地图后无法进入,#Y(剩余留存时间#R/%s#Y分钟)",取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))))}

    elseif 任务数据[任务id].类型==7760 then
      if 任务数据[任务id].分类==1 then
        说明={"雪人任务",format("替雪人给#R/%s#的#R%s#送上祝福。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)}
      elseif 任务数据[任务id].分类==2 then
        说明={"雪人任务",format("寻找到#R%s#交给雪人。",任务数据[任务id].物品)}
      end

    end
  end
  return 说明
end

function 任务处理类:更新(dt)
  for n, v in pairs(任务数据) do
          if 任务数据[n]~=nil then
                if 任务数据[n].类型==22 then --摇钱树
                      if 任务数据[n].阶段<=3 then
                            if os.time()-任务数据[n].记录时间>=120 then
                                任务数据[n].阶段=任务数据[n].阶段+1
                                任务数据[n].操作=true
                                任务数据[n].记录时间=os.time()
                                if 玩家数据[任务数据[n].玩家id]~=nil then
                                  常规提示(任务数据[n].玩家id,"#Y你种下的摇钱树需要照料了，请赶紧前往。")
                                  发送系统消息(任务数据[n].玩家id,"你种下的摇钱树需要照料了，请赶紧前往。","系统")
                                end
                            end
                      elseif 任务数据[n].阶段==4 then
                              任务数据[n].阶段=任务数据[n].阶段+1
                              任务数据[n].次数=取随机数(1,3)+任务数据[n].成功操作
                              任务数据[n].模型 = "摇钱树"
                              地图处理类:更改怪物模型(n)
                              if 玩家数据[任务数据[n].玩家id]~=nil then
                                常规提示(任务数据[n].玩家id,"#Y你种下的摇钱树已经长大了，现在你可以摘取它的果实了。")
                                发送系统消息(任务数据[n].玩家id,"你种下的摇钱树已经长大了，现在你可以摘取它的果实了。","系统")
                              end
                      end
                end

                if  任务数据[n].起始 and 任务数据[n].结束 and os.time()-任务数据[n].起始>=任务数据[n].结束 and (not 任务数据[n].战斗 or 任务数据[n].战斗==0) then
                      if 任务数据[n].类型==1  then
                           if 玩家数据[任务数据[n].玩家id]~=nil  then
                              玩家数据[任务数据[n].玩家id].角色:取消任务(n)
                              发送数据(玩家数据[任务数据[n].玩家id].连接id,39)
                              常规提示(任务数据[n].玩家id,"你的变身时间到期了！")
                              玩家数据[任务数据[n].玩家id].角色.数据.变身数据=nil
                              玩家数据[任务数据[n].玩家id].角色.数据.变异=nil
                              发送数据(玩家数据[任务数据[n].玩家id].连接id,37,{变身数据=玩家数据[任务数据[n].玩家id].角色.数据.变身数据,变异=玩家数据[任务数据[n].玩家id].角色.数据.变异})
                              地图处理类:更改模型(任务数据[n].玩家id,{[1]=玩家数据[任务数据[n].玩家id].角色.数据.变身数据,[2]=玩家数据[任务数据[n].玩家id].角色.数据.变异},1)
                              任务数据[n]=nil
                            end


                      else
                          local id组={}
                          if 任务数据[n].玩家id~=nil then
                              if type(任务数据[n].玩家id)=="table" then
                                  for i=1,#任务数据[n].玩家id do
                                      id组[#id组+1]=任务数据[n].玩家id[i]
                                  end
                              else
                                  id组[#id组+1]=任务数据[n].玩家id
                              end
                          end
                          if 任务数据[n].队伍组~=nil and type(任务数据[n].队伍组)=="table"  then
                               for i=1,#任务数据[n].队伍组 do
                                  id组[#id组+1]=任务数据[n].队伍组[i]
                               end
                          end
                          if not 判断是否为空表(id组) then
                              id组=删除重复(id组)
                              for i=1,#id组 do
                                      if 玩家数据[id组[i]]~=nil and (玩家数据[id组[i]].战斗==0 or not 玩家数据[id组[i]].战斗)  then
                                          玩家数据[id组[i]].角色:取消任务(n)
                                          发送数据(玩家数据[id组[i]].连接id,39)
                                          if 任务数据[n].类型==2 then
                                              常规提示(id组[i],"你的双倍时间到期了！")
                                          elseif 任务数据[n].类型==3  then
                                                 常规提示(id组[i],"你的精修时间到期了！")
                                          elseif 任务数据[n].类型==7  then
                                                常规提示(id组[i],"你的科举答题时间到期了！")
                                          elseif 任务数据[n].类型==9  then
                                                常规提示(id组[i],"你的摄妖香过期了！")
                                          elseif 任务数据[n].类型==22  then
                                                常规提示(id组[i],"你的摇钱树枯萎了消失了！")
                                          elseif 任务数据[n].类型==7755 then
                                                常规提示(id组[i],"你的一倍经验丹过期了！")
                                          elseif 任务数据[n].类型==7756 then
                                                常规提示(id组[i],"你的双倍经验丹过期了！")
                                          elseif 任务数据[n].类型==7757 then
                                                常规提示(id组[i],"你的双倍掉宝符过期了！")
                                          elseif 任务数据[n].类型==411 then
                                                常规提示(id组[i],"你的会员地图停留时间过期了！")
                                          end
                                      end
                              end
                          end
                          if 任务数据[n].类型==22 and 任务数据[n].刷出强盗 ~= nil and 任务数据[任务数据[n].刷出强盗]~=nil and
                            (not 任务数据[任务数据[n].刷出强盗].战斗 or 任务数据[任务数据[n].刷出强盗].战斗==0) then
                                地图处理类:删除单位(任务数据[任务数据[n].刷出强盗].地图编号,任务数据[任务数据[n].刷出强盗].编号)
                                任务数据[任务数据[n].刷出强盗]  = nil
                                if 任务数据[n].玩家id then
                                    玩家数据[任务数据[n].玩家id].角色:取消任务(任务数据[n].刷出强盗)
                                end
                          end
                          if 任务数据[n].地图编号 and 任务数据[n].编号 then
                              地图处理类:删除单位(任务数据[n].地图编号,任务数据[n].编号)
                          end
                          任务数据[n]=nil
                    end

                end
        end
    end
  end



function 任务处理类:触发条件(id,等级,任务,活动,队伍,人数)--id,等级,任务,活动,队伍,人数
          if 等级 then
              if type(等级)=="table" then
                  if 取队伍最低等级(id,等级[1]) then
                      添加最后对话(id,"此活动要求最低等级不能"..等级[1].."级")
                      return  true
                  end
                  if 取队伍最高等级(id,等级[2]) then
                      添加最后对话(id,"此活动要求最高等级不能超过"..等级[2].."级")
                      return  true
                  end
              else
                  if not 取等级要求(id,等级)  then
                     添加最后对话(id,"该活动必须"..等级.."级才可以触发")
                      return  true
                  end
              end
          end
          if 任务 and 取队伍任务(id,任务) then
              添加最后对话(id,"队伍中已有队员领取过此任务了")
              return true
          end
          if 活动 and not 活动次数查询(id,活动) then
              return true
          end
          if 队伍 and (not 玩家数据[id].队伍 or 玩家数据[id].队伍==0 or not 玩家数据[id].队长) then
              添加最后对话(id,"该任务必须组队并是队长才可以触发")
              return true
          end
          if 人数 and 取队伍人数(id)<人数 then
              添加最后对话(id,"该活动必须"..人数.."人才可以触发")
              return true
          end
          return false
end



-- function 任务处理类:取编号()
--   for n, v in pairs(任务数据) do
--     if n==nil and n>=1000 then
--       return n
--     end
--   end
--   local 临时id=#任务数据+1
--   if 任务数据[临时id]==nil then
--     return 临时id
--   else
--     for n=1000,100000 do
--       if 任务数据[n]==nil then return n end
--     end
--   end
-- end
function 任务处理类:设置哪吒副本(id)
      if self:触发条件(id,109,311,"魔童哪吒",1,5) then--id,等级,任务,活动,队伍,人数
        return
      end
      local 任务id=取唯一识别码(311)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        进程=1,
        类型=311
      }
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了魔童哪吒现世副本")
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
        添加活动次数(v,"魔童哪吒")
      end

  --发送数据(玩家数据[id].连接id,1501,{名称="游奕灵官",模型="超级红海儿",对话=format("你成功领取了天降辰星任务，请立即前往寻找#Y/%s#W/接受考验。")})
end





function 任务处理类:添加离线经验(id,数值)
        if 玩家数据[id].角色:取任务(409)~=0 then
          添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
          return
        end
        local 任务id=取唯一识别码(409)

        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          玩家id=id,
          离线经验=数值,
          类型=409
        }
        玩家数据[id].角色:添加任务(任务id)
        发送数据(玩家数据[id].连接id,39)
end
function 任务处理类:添加离线储备(id,数值)
      if 玩家数据[id].角色:取任务(410)~=0 then
        添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
        return
      end
      local 任务id=取唯一识别码(410)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        玩家id=id,
        离线储备=数值,
        类型=410
      }
      玩家数据[id].角色:添加任务(任务id)
      发送数据(玩家数据[id].连接id,39)
end



function 任务处理类:添加会员地图(id)
        if self:触发条件(id,69,nil,"会员地图",1,5) then--id,等级,任务,活动,队伍,人数
            return
        end
        for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
           if not 玩家数据[v] or not 玩家数据[v].角色.数据.月卡 or not 玩家数据[v].角色.数据.月卡.开通 then
              if 玩家数据[v] then
                常规提示(id,"#Y玩家#R"..玩家数据[v].角色.数据.名称.."#Y没有月卡,无法进入")
              end
              return
           end
        end
        local 任务id=取唯一识别码(411)
        任务数据[任务id]={
                id=任务id,
                起始=os.time(),
                结束=3600,
                玩家id=id,
                战斗时间=os.time()-40,
                类型=411
          }
          玩家数据[id].角色:添加任务(任务id,1)
          for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
              添加活动次数(v,"会员地图")
          end
          local xy=地图处理类.地图坐标[1193]:取随机点()
          地图处理类:跳转地图(id,1621,xy.x,xy.y)
end




function 任务处理类:设置幼儿园(id)
  local 临时数量=取随机数(3,5)
  local 随机参数=取随机数()
  for n=1,临时数量 do
    local 任务id=取唯一识别码(102)
    xy=地图处理类.地图坐标[地图]:取随机点()
    local 模型=取随机怪(105,180)
    模型=模型[2]
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=300,
      玩家id=id,
      名称=模型.."宝宝",
      模型=模型,
      等级=取随机数(5,180),
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1193,
      地图名称=取地图名称(1193),
      类型=102
    }
    if 取随机数()<=50 then
      任务数据[任务id].变异=true
      任务数据[任务id].名称="变异"..模型
    end
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#Y/%s#W/在挖宝时砸坏了#S/怪物幼儿园#W/，一群宝宝正在#Y/%s#w/哭闹,各路英雄快来领养#46",玩家数据[id].角色.数据.名称,取地图名称(1193)),频道="xt"})
end


function 任务处理类:添加皇宫贼王任务(id)
      if self:触发条件(id,30,nil,"皇宫飞贼贼王",1,3) then--id,等级,任务,活动,队伍,人数
          return
      end
      local 队伍id=玩家数据[id].队伍
      for n=1,#队伍数据[队伍id].成员数据 do
            local 临时id=队伍数据[队伍id].成员数据[n]
            if 皇宫飞贼.贼王[临时id]==nil or 皇宫飞贼.贼王[临时id]<4 then
              添加最后对话(id,format("缉拿贼王需要先完成4轮飞贼任务,#G%s#W尚未满足条件。",玩家数据[临时id].角色.数据.名称))
              return
            elseif 皇宫飞贼.贼王[临时id]==-1 then
              添加最后对话(id,format("#G%s#W在本次活动中已经成功缉拿过贼王，现在不可以再次领取缉拿贼王任务哟。贼王任务每次活动都只可缉拿一次哟。",玩家数据[临时id].角色.数据.名称))
              return
            end
      end
      战斗准备类:创建战斗(id,100023,0)
end

function 任务处理类:添加降妖除魔任务(id)
  if self:触发条件(id,109,200,"降妖伏魔",1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(200)
  if 降妖伏魔[id]==nil then
    降妖伏魔[id]=1
  end
  if 降妖伏魔[id]==1 then
    名称="巧智魔"
    模型="蜃气妖"
  elseif 降妖伏魔[id]==2 then
    名称="怯弱妖"
    模型="雾中仙"
  elseif 降妖伏魔[id]==3 then
    名称="迷幻妖"
    模型="曼珠沙华"
  elseif 降妖伏魔[id]==4 then
    名称="梦魇魔"
    模型="炎魔神"
  elseif 降妖伏魔[id]==5 then
    名称="万年魔灵"
    模型="九头精怪"
  end
  local 地图范围={1501,1092,1070,1208,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()

  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称,
    模型=模型,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=200,
    分类=降妖伏魔[id]
  }
  地图处理类:添加单位(任务id)
   for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
        if not 降妖伏魔[v] then
            降妖伏魔[v] = 1
        end
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,{名称="鬼谷道人",内容="男人_道士",对话=format("据可靠消息，一只#Y/%s#W/正躲藏#G/%s(%s,%s)#W/附近，请立即前去缉拿。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
end

function 任务处理类:添加皇宫飞贼任务(id)
      if self:触发条件(id,69,12,"皇宫飞贼",1,3) then--id,等级,任务,活动,队伍,人数
          return
      end
      local 任务id=取唯一识别码(12)
      if 皇宫飞贼[id]==nil then
        皇宫飞贼[id]=1
      end
      if 皇宫飞贼[id]>=5 then
        皇宫飞贼[id]=1
      end
      local 模型="护卫"
      local 名称 = "江湖大盗"
      if 皇宫飞贼[id]==1 then
        名称="毛贼"
        模型="强盗"
      elseif 皇宫飞贼[id]==2 then
        名称="销赃贼"
        模型="吸血鬼"
      elseif 皇宫飞贼[id]==3 then
        名称="宝贼"
        模型="夜罗刹"
      elseif 皇宫飞贼[id]==4 then
        名称="盗贼首领"
        模型="雨师"
      end
      local 地图=1110
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=id,
        名称=名称,
        模型=模型,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=12,
        分类=皇宫飞贼[id]
      }
      地图处理类:添加单位(任务id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
           if not 皇宫飞贼[v] then
              皇宫飞贼[v]=1
            end
      end
      玩家数据[id].角色:添加任务(任务id,1,nil,{名称="御林军左统领",模型="护卫",内容=format("据可靠消息，偷盗皇宫宝物的#Y/%s#W/正躲藏#G/%s(%s,%s)#W/附近，请立即前去缉拿。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})

end

function 任务处理类:添加江湖任务(id)
  if self:触发条件(id,30,11,"初出江湖",1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(11)
  local 地图范围={1501,1193,1506}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local 随机参数=取随机数()
  local 模型="护卫"
  local 名称 = "江湖大盗"
  local xy=地图处理类.地图坐标[地图]:取随机点()


  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称,
    模型=模型,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=11
  }
  地图处理类:添加单位(任务id)
  for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
      玩家数据[v].角色.数据.江湖次数=玩家数据[v].角色.数据.江湖次数+1
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,{名称="杜少海",模型="男人_店小二",内容=format("听说近日有#Y/%s#W/正在#G/%s(%s,%s)#W/处作恶，请立即前去查明真相。这些江湖大盗中有不少为侠客所扮，所以请少侠多仔细分辨他们的身份。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})


 end

function 任务处理类:开启乌鸡副本(id)
  if self:触发条件(id,30,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 检测通过 = true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]
    if 玩家数据[临时id].角色:取任务(120)~=0 then
        常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
        检测通过=false
    end
     if 副本数据.乌鸡国.完成[临时id] then
        if os.date("%m", os.time())==os.date("%m", 副本数据.乌鸡国.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.乌鸡国.完成[临时id]) then
            常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
            检测通过 = false
        else
            副本数据.乌鸡国.进行中[临时id] = nil
            副本数据.乌鸡国.完成[临时id] = nil
        end
    end
    if 副本数据.乌鸡国.进行中[临时id] then
       常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
       检测通过 = false
    end
  end
  if 检测通过 then
        副本数据.乌鸡国.进行[id]={进程=1}
        local 任务id=取唯一识别码(120)
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id=id,
          队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
          副本id=id,
          类型=120
        }
        副本数据.乌鸡国.进行[id].真实副本id = 任务id
        self:设置乌鸡国副本(id)
        玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了乌鸡国副本")
        for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
            副本数据.乌鸡国.进行中[v]=true
        end
  end
end

function 任务处理类:设置乌鸡国副本(id)
  if 副本数据.乌鸡国.进行[id]==nil then
    return
  end
  local fbid = 副本数据.乌鸡国.进行[id].真实副本id
  if 副本数据.乌鸡国.进行[id].进程==1 then
     副本数据.乌鸡国.进行[id].木妖数量=0
    for n=1,15 do
      local 任务id=取唯一识别码(121)
      local 地图=6001
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="芭蕉木妖",
        模型="树怪",
        变异=true,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=121,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.乌鸡国.进行[id].进程==2 then
    副本数据.乌鸡国.进行[id].和尚数量=0
    for n=1,15 do
      local 任务id=取唯一识别码(122)
      local 地图=6001
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="热心仙人",
        模型="热心仙人",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=122,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.乌鸡国.进行[id].进程==3 then
    for n=1,1 do
      local 任务id=取唯一识别码(123)
      local 地图=6001
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="乌鸡国王",
        模型="文伯",
        x=115,
        y=71,
        方向=1,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=123,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
    local 名称={"囚神妖怪","拘灵妖怪","缚仙妖怪"}
    副本数据.乌鸡国.进行[id].序列={false,false,false}
    for n=1,3 do
      local 任务id=取唯一识别码(124)
      local 地图=6002
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称=名称[n],
        模型="树怪",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        序列=n,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=124,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.乌鸡国.进行[id].进程==4 then
    副本数据.乌鸡国.进行[id].鬼祟数量=0
    local 随机模型={"牛妖","花妖","黑熊精","蛤蟆精"}
    for n=1,5 do
      local 任务id=取唯一识别码(125)
      local 地图=6002
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="鬼祟小怪",
        模型=随机模型[取随机数(1,#随机模型)],
        x=xy.x,
        y=xy.y,
        行走开关=true,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=125,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.乌鸡国.进行[id].进程==5 then
    副本数据.乌鸡国.进行[id].数量=2
    local xy={{x=50,y=47},{x=59,y=42}}
    local 方向={{2},{1}}
    local 真假={false,false}
    真假[取随机数(1,2)]=true
    for n=1,2 do
      local 任务id=取唯一识别码(126)
      local 地图=6002
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="乌鸡国王",
        模型="文伯",
        x=xy[n].x,
        y=xy[n].y,
        真假=真假[n],
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=126,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  end
end

function 任务处理类:开启一斛珠(id)
  if self:触发条件(id,30,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 检测通过 =true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]
    if 玩家数据[临时id].角色:取任务(7001)~=0 then
      常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
      检测通过 = false
    end

    if 玩家数据[临时id].角色.数据.看戏门票次数==nil or 玩家数据[临时id].角色.数据.看戏门票次数 < 1 then
      常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y没有购买门票，没法看戏哦！")
       检测通过 = false
    end
    if 副本数据.一斛珠.完成[临时id] then
        if os.date("%m", os.time())==os.date("%m", 副本数据.一斛珠.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.一斛珠.完成[临时id]) then
            常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y今日已完成该副本！")
            检测通过 = false
        else
            副本数据.一斛珠.进行中[临时id]=nil
            副本数据.一斛珠.完成[临时id]=nil
        end
    end
    if 副本数据.一斛珠.进行中[临时id] then
       常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
       检测通过 = false
    end
 end

if 检测通过 then
    local 任务id=取唯一识别码(7001)
    随机序列=随机序列+1
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=10800,
      玩家id=id,
      队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
      副本id=任务id,
      类型=7001
    }
    for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
        玩家数据[v].角色.数据.看戏门票次数 = 玩家数据[v].角色.数据.看戏门票次数 - 1
    end
    副本处理类:加载副本("一斛珠",任务id,id)
    --副本处理类.副本盒子[任务id]:设置副本进程(1)
 end
end

function 任务处理类:副本传送(id,类型)
  if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
    常规提示(id,"#Y/该任务必须组队完成且由队长领取")
    return
  elseif 取队伍人数(id)<1 then
    常规提示(id,"#Y此副本要求队伍人数不低于5人")
    return
  end
  if 类型==1 then --乌鸡国
    if 玩家数据[id].角色:取任务(120)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,120,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(120)].副本id
    local 进程=副本数据.乌鸡国.进行[副本id].进程
    local 地图=6001
    local x=0
    local y=0
    if 进程<3 then
      x,y=112,14
    else
      地图=6002
      x,y=29,56
    end
    地图处理类:跳转地图(id,地图,x,y)
  elseif 类型==2 then --车迟斗法副本
    if 玩家数据[id].角色:取任务(130)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,130,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(130)].副本id
    local 进程=副本数据.车迟斗法.进行[副本id].进程
    local 地图=6021
    local x=0
    local y=0
    if 进程<4 then
      x,y=10,133
    elseif 进程<5 then
      地图=6022
      x,y=38,32
    else
      地图=6023
      x,y=40,153
    end
    地图处理类:跳转地图(id,地图,x,y)
    玩家数据[id].角色:刷新任务跟踪()
  elseif 类型==3 then --水陆大会副本
    if 玩家数据[id].角色:取任务(150)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,150,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(150)].副本id
    local 进程=副本数据.水陆大会.进行[副本id].进程
    local 地图=6024
    local x=35
    local y=71
    地图处理类:跳转地图(id,地图,x,y)
  elseif 类型==4 then --通天河副本
    if 玩家数据[id].角色:取任务(160)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,160,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(160)].副本id
    local 进程=副本数据.通天河.进行[副本id].进程
    local 地图=6027
    local x=100
    local y=100
    if 进程<6 then
      x,y=100,100
    elseif 进程<9 then
      地图=6028
      x,y=27,22
    elseif 进程<10 then
      地图=6029
      x,y=103,59
    else
      地图=6030
      x,y=106,38
    end
    地图处理类:跳转地图(id,地图,x,y)

  elseif 类型==5 then --大闹天宫副本
    if 玩家数据[id].角色:取任务(180)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,180,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(180)].副本id
    local 进程=副本数据.大闹天宫.进行[副本id].进程
    local 地图=6031
    local x=97
    local y=43
    if 进程<4 then
      x,y=97,43
    elseif 进程<5 then
      地图=6032
      x,y=27,22
    elseif 进程<8 then
      地图=6033
      x,y=103,59
    else
      地图=6035
      x,y=106,38
    end
    地图处理类:跳转地图(id,地图,x,y)

elseif 类型==6 then --齐天大圣
    if 玩家数据[id].角色:取任务(191)==0 then
      常规提示(id,"#Y/你尚未开启此副本")
      return
    elseif not 取队员任务一致(id,191,1) then
        return
    end
    local 副本id=任务数据[玩家数据[id].角色:取任务(191)].副本id
    local 进程=副本数据.齐天大圣.进行[副本id].进程
    local 地图=6036
    local x=12
    local y=108
    if 进程 <3 then
      x,y=12,108
    elseif 进程 <= 4 then
      地图=6037
      x,y=100,101
    elseif 进程 == 5 then
      地图=6036
      x,y=12,108
    elseif 进程 <=10 then
      地图=6038
      x,y=203,133
    elseif 进程 == 11 then
      地图=6036
      x,y=12,108
    else
      地图=6039
      x,y=30,38
    end
    地图处理类:跳转地图(id,地图,x,y)
  end
end




function 任务处理类:添加抓鬼任务(id)
  if self:触发条件(id,30,8,"抓鬼任务",1) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(8)
  local 地图范围={1501,1092,1070,1193,1173,1146,1140,1208,1040,1226,1142}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local 随机参数=取随机数()
  local 模型="僵尸"
  if 随机参数<=15 then
    模型="野鬼"
  elseif 随机参数<=15 then
    模型="野鬼"
  elseif 随机参数<=30 then
    模型="牛头"
  elseif 随机参数<=50 then
    模型="马面"
  elseif 随机参数<=60 then
    模型="骷髅怪"
  end
  local 时辰库 = {"子","丑","寅","卯","辰","巳","午","未","申","酉","戌","亥"}
  local 时刻库 = {"一","二","三","四","五","六"}
  local 鬼名库 = {"诌鬼","假鬼","奸鬼","捣蛋鬼","冒失鬼","烟沙鬼","挖渣鬼","仔细鬼","讨吃鬼","醉死鬼","抠掏鬼","伶俐鬼","急突鬼","丢谎鬼","乜斜鬼","撩桥鬼","饿鬼","色鬼","穷鬼","刻山鬼","吸血鬼","惊鸿鬼","清明鬼"}
  local 名称 = 时辰库[取随机数(1,#时辰库)].."时"..时刻库[取随机数(1,#时刻库)].."刻"..鬼名库[取随机数(1,#鬼名库)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  -- xy={x=57,y=74}
  local 临时下限={x=xy.x-50,y=xy.y-50}
  if 临时下限.x<=0 then
    临时下限.x=1
  end
  if 临时下限.y<=0 then
    临时下限.y=1
  end
  local 临时上限={x=xy.x+50,y=xy.y+50}
  local 显示xy={x=取随机数(临时下限.x,临时上限.x),y=取随机数(临时下限.y,临时上限.y)}
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称,
    模型=模型,
    x=xy.x,
    y=xy.y,
    显示x=显示xy.x,
    显示y=显示xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=8
  }
  地图处理类:添加单位(任务id)
  for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
      if not 玩家数据[v].角色.数据.捉鬼次数 then  玩家数据[v].角色.数据.捉鬼次数=1 end
      if 玩家数据[v].角色.数据.捉鬼次数>10 then  玩家数据[v].角色.数据.捉鬼次数=1 end
  end
  local 对话 = nil
  if not 玩家数据[id].自动抓鬼 then
      对话 = {名称="钟馗",模型="男人_钟馗",内容=format("听说近日有#Y/%s#W/正在#G/%s(%s,%s)#W/处作恶，请立即前去降服。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].显示x,任务数据[任务id].显示y)}
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,对话)

end




function 任务处理类:设置鬼王任务(id)
  if self:触发条件(id,100,211,"抓鬼任务",1) then--id,等级,任务,活动,队伍,人数
      return
  end

  local 抓鬼名称1={"子时","丑时","寅时","卵时","辰时","巳时","午时","未时","申时","酉时","戌时","亥时"}
  local 抓鬼名称2={"一刻","二刻","三刻","四刻","五刻","六刻",}
  local 抓鬼名称3 = {"鬼王"}
 local 抓鬼地图={1233,1228,1229,1040,1041,1501,1140,1091,1070,1512,1131,1140}
 local 任务id=取唯一识别码(211)
 local 地图=抓鬼地图[取随机数(1,#抓鬼地图)]
 local xy=地图处理类.地图坐标[地图]:取随机点()
 local 抓鬼造型={"幽灵","鬼将","夜罗刹","吸血鬼","炎魔神",}
 local 模型=抓鬼造型[取随机数(1,#抓鬼造型)]
 local 名称=抓鬼名称1[取随机数(1,#抓鬼名称1)]..抓鬼名称2[取随机数(1,#抓鬼名称2)]..抓鬼名称3[取随机数(1,#抓鬼名称3)]
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称,
    模型=模型,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=211
  }
  地图处理类:添加单位(任务id)
  for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
      if not 玩家数据[v].角色.数据.捉鬼次数 then  玩家数据[v].角色.数据.捉鬼次数=1 end
      if 玩家数据[v].角色.数据.捉鬼次数>10 then  玩家数据[v].角色.数据.捉鬼次数=1 end
  end
  local 对话 = nil
  if not 玩家数据[id].自动抓鬼 then
      对话 = {名称="黑无常",模型="黑无常",对话=format("听说近日有#Y/%s#W/正在#G/%s(%s,%s)#W/处作恶，请立即前去降服。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)}
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,对话)


end




function 任务处理类:设置大雁塔怪(id)
  local 地图范围={1004,1005,1006,1007,1008,1090}
  for i=1,#地图范围 do
    local 临时数量=取随机数(30,50)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(103)
      随机序列=随机序列+1
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围=取明雷(地图)
      local 模型=模型范围[取随机数(1,#模型范围)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=600,
        玩家id=0,
        名称=模型,
        模型=模型,
        等级=取随机数(1,140),
        x=xy.x,
        y=xy.y,
        事件="明雷",
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=103
      }
      地图处理类:添加单位(任务id)
    end
  end

end

function 任务处理类:设置建邺东海小活动(id)
  local 地图范围={1501,1506}
  for i=1,#地图范围 do
    local 临时数量=取随机数(30,50)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(127)
      随机序列=随机序列+1
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围={"银两","食物","摄妖香","药品","海星","海毛虫","大海龟","巨蛙"}--"宠物口粮",
      local 模型=模型范围[取随机数(1,#模型范围)]
      local x称谓 = nil
      if 模型=="海星" or 模型=="海毛虫" or 模型=="大海龟" or 模型=="巨蛙" then
        x称谓="宝宝"
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=600,
        玩家id=0,
        名称=模型,
        模型=模型,
        等级=取随机数(1,140),
        称谓=x称谓,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=127
      }
      地图处理类:添加单位(任务id)
    end
  end

end

function 任务处理类:设置封妖任务(id)
  local 临时数量=取随机数(10,16)
  玩家数据[id].角色:添加储备(临时数量*500,"挖宝放妖")
  常规提示(id,"#Y/你不慎挖塌了妖怪家的房子，却意外的发现了#R/"..(临时数量*500).."#Y/点储备金")
  local 地图范围={1193,1110,1173,1092,1091,1174,1210}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local 封妖等级={[1193]=55,[1110]=55,[1173]=55,[1092]=55,[1091]=55,[1174]=55,[1210]=55}
  for n=1,临时数量 do
    local 任务id=取唯一识别码(101)
    随机序列=随机序列+1
    local 模型="黑山老妖"
    local xy=地图处理类.地图坐标[地图]:取随机点()
    if 取随机数()<=50 then
      模型="地狱战神"
    end
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=1800,
      玩家id=id,
      名称="远古魔王",
      模型=模型,
      等级=封妖等级[地图],
      x=xy.x,
      y=xy.y,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=101
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#Y/%s#W/在挖宝时不慎挖塌了妖怪家的房子，现在一群无家可归的妖怪正在#Y/%s#w/寻衅闹事，各路英雄赶快前往平乱啊！#35",玩家数据[id].角色.数据.名称,取地图名称(地图)),频道="xt"})
end




function 任务处理类:完成文韵任务(id,类型)---------远方文韵墨香
  local 任务id=玩家数据[id].角色:取任务(112)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=类型 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
   local 倍数=(1+玩家数据[id].角色.数据.文韵次数*0.1)
    if 类型==4 then
        倍数=倍数+1
    end
    玩家数据[id].角色:自定义银子添加("文韵任务",倍数)
    if 玩家数据[id].角色.数据.文韵次数==20  then
        玩家数据[id].角色:自定义银子添加("文韵任务")
    end
    玩家数据[id].角色:添加活跃积分(1,"文韵任务",1)
    添加活动次数(id,"文韵任务")
    玩家数据[id].角色.数据.文韵积分=玩家数据[id].角色.数据.文韵积分+1
    常规提示(id,"#Y/你获得了1点文韵积分\n(#G文韵使者处兑换)")

        local 获得物品={}
        for i=1,#自定义数据.文韵任务 do
          if 取随机数()<=自定义数据.文韵任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.文韵任务[i]
          end
        end

        获得物品=删除重复(获得物品)

        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(文韵任务)#R/%s#Y通过了文韵使者的考验，完成了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"文韵任务",获得物品[取编号].名称),频道="xt"})
            end
        end

    玩家数据[id].角色:取消任务(任务id)
    任务数据[任务id]=nil
    if 成就数据[id].文韵==nil then
        成就数据[id].文韵=0
    end
    if 成就数据[id].文韵<1001 then
        成就数据[id].文韵=成就数据[id].文韵+1
    end
    if 成就数据[id].文韵 == 1 then
        local 成就提示 = "文韵新手"
        local 成就提示1 = "完成1次了文韵"
        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
    elseif 成就数据[id].文韵==1000 then
        local 成就提示 = "文韵墨香"
        local 成就提示1 = "完成1000次了文韵"
        成就数据[id].成就点 = 成就数据[id].成就点 + 1
        常规提示(id,"#Y/恭喜你获得了1点成就")
        发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
        玩家数据[id].角色:添加称谓("文韵老手")
    end
end
-- 0914
function 任务处理类:完成门派任务(id,类型,双倍)
  local 任务id=玩家数据[id].角色:取任务(111)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=类型 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
  local 等级=玩家数据[id].角色.数据.等级
  local 基础经验=qjy(等级)*4
  local 基础银子=qyz(等级)*4
  添加活动次数(id,"师门任务")
  玩家数据[id].角色:添加活跃积分(1,"师门任务",1)
  玩家数据[id].角色.数据.师门积分 = 玩家数据[id].角色.数据.师门积分 + 1
  常规提示(id,"#Y/你获得了#R/1#Y/点师门积分")
  基础经验=qz(基础经验*(1+玩家数据[id].角色.数据.师门次数*0.1))
  基础银子=qz(基础银子*(1+玩家数据[id].角色.数据.师门次数*0.1))
  local 经验=基础经验
  local 银子=基础银子
  if 类型==4 then
    银子=银子*2
  end
  if 双倍~=nil then
    经验=经验+基础经验
    银子=银子+基础银子
  end
  if 师门数据[id]==nil then
    师门数据[id]=0
  end
  if 师门数据[id]<=55 then
    经验=经验+基础经验
    银子=银子+基础银子
    师门数据[id]=师门数据[id]+1
    if 师门数据[id]==20  then
      local 额外银子=qz(等级*10*2)
      local 额外经验=qz(等级*20*2)
      玩家数据[id].角色:添加经验(额外经验,"20次门派任务"..类型)
      玩家数据[id].角色:添加银子(额外银子,"20次门派任务"..类型,1)
    end
    if 玩家数据[id].角色.数据.师门次数==10 then
       玩家数据[id].角色.数据.师门积分 = 玩家数据[id].角色.数据.师门积分 + 5
       常规提示(id,"#Y/你获得了#R/5#Y/点师门积分")
       local 获得物品={}
       for i=1,#自定义数据.师门任务 do
           if 取随机数()<=自定义数据.师门任务[i].概率 then
              获得物品[#获得物品+1]=自定义数据.师门任务[i]
           end
       end
       获得物品=删除重复(获得物品)
       if 获得物品~=nil then
          local 取编号=取随机数(1,#获得物品)
          if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
             玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
             广播消息({内容=format("#S(师门任务)#R/%s#Y完成师门任务运气包爆棚，#Y因此获得了#G/%s#Y/",玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
       end

    end
  end
  if 玩家数据[id].角色:取任务(402)~=0 and 任务数据[玩家数据[id].角色:取任务(402)].进程 == 3 then
  任务数据[玩家数据[id].角色:取任务(402)].进程=4
  玩家数据[id].角色:刷新任务跟踪()
  end
  玩家数据[id].角色:添加经验(经验,"门派任务"..类型)
  玩家数据[id].角色:添加银子(银子,"门派任务"..类型,1)
  if 玩家数据[id].角色.数据.参战信息~=nil then
     玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"门派")
  end
  玩家数据[id].角色:取消任务(任务id)
  任务数据[任务id]=nil
  if 成就数据[id].师门==nil then
      成就数据[id].师门=0
  end
  if 成就数据[id].师门<1001 then
      成就数据[id].师门=成就数据[id].师门+1
  end
  if 成就数据[id].师门 == 1 then
      发送数据(玩家数据[id].连接id,149,{内容="首席小弟子",内容1="完成1次了师门"})
  elseif 成就数据[id].师门==1000 then
          成就数据[id].成就点 = 成就数据[id].成就点 + 1
          玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
          常规提示(id,"#Y/恭喜你获得了1点成就")
          发送数据(玩家数据[id].连接id,149,{内容="首席小弟子",内容1="完成1000次了师门"})
          玩家数据[id].角色:添加称谓("首席小弟子")
  end
end
function 任务处理类:完成官职任务(id,类型)
          local 任务id=玩家数据[id].角色:取任务(110)
          if 任务数据[任务id]==nil or 任务数据[任务id].分类~=类型 then
            常规提示(id,"#Y/你没有这样的任务")
            return
          end
          local 等级=玩家数据[id].角色.数据.等级
          local 经验=qjy(等级)*5
          local 银子=qyz(等级)*2
          添加活动次数(id,"官职任务")
          玩家数据[id].角色:添加活跃积分(1,"官职任务",1)

          玩家数据[id].角色:添加经验(经验,"官职")

          if 玩家数据[id].角色.数据.参战信息~=nil then
            玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"官职")
          end
          if 类型==4 then
            玩家数据[id].角色:添加银子(银子*2,"官职",1)
          else
            玩家数据[id].角色:添加银子(银子,"官职",1)
          end
          玩家数据[id].角色.数据.官职点=玩家数据[id].角色.数据.官职点+1
          if 玩家数据[id].角色.数据.官职次数>=10 then
             local 获得物品={}
             for i=1,#自定义数据.官职任务 do
                 if 取随机数()<=自定义数据.官职任务[i].概率 then
                     获得物品[#获得物品+1]=自定义数据.官职任务[i]
                 end
              end
              获得物品=删除重复(获得物品)
              if 获得物品~=nil then
                 local 取编号=取随机数(1,#获得物品)
                 if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                    玩家数据[临时id].道具:自定义给予道具(临时id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                    广播消息({内容=format("#S(官职任务)#R/%s#Y完成官职任务运气包爆棚，#Y因此获得了#G/%s#Y".."#"..取随机数(1,110),玩家数据[临时id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                 end
              end
          end
          if 玩家数据[id].角色.数据.官职次数>10 then
              玩家数据[id].角色.数据.官职次数=1
          end
          if 类型==2 then
              地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
          end
          玩家数据[id].角色:取消任务(任务id)
          任务数据[任务id]=nil
          if 成就数据[id].官职==nil then
              成就数据[id].官职=0
          end
          if 成就数据[id].官职<1001 then
              成就数据[id].官职=成就数据[id].官职+1
          end
          if 成就数据[id].官职 == 1 then
              local 成就提示 = "省委书记"
              local 成就提示1 = "完成1次了官职"
              发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
          elseif 成就数据[id].官职==1000 then
                  local 成就提示 = "省委书记"
                  local 成就提示1 = "完成1000次了官职"
                  成就数据[id].成就点 = 成就数据[id].成就点 + 1
                  玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
                  常规提示(id,"#Y/恭喜你获得了1点成就")
                  发送数据(玩家数据[id].连接id,149,{内容=成就提示,内容1=成就提示1})
                  玩家数据[id].角色:添加称谓("当代清官")
          end
end



function 任务处理类:添加文韵任务(id,门派)---------远方文韵墨香
    if not 玩家数据[id].最后对话 then return end
  if 玩家数据[id].队伍 ~= 0 then
        添加最后对话(id,"请单人来接取文韵任务！")
        return
  elseif 玩家数据[id].角色.数据.文韵次数 > 100 then
         添加最后对话(id,"今天已经完成100次文韵墨香任务了，休息一下！#1")
       return
  end
  if self:触发条件(id,20,112,"文韵任务") then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务参数={1,170} -- 30=送信 20巡逻 20 找bb 10 寻物 30示威  30门派支援 30 乾坤袋
  local 等级=玩家数据[id].角色.数据.等级
  if  等级>=90 then
   任务参数={1,80}
   end
  if  等级>=100 then
   任务参数={1,120}
   end
   if  等级>=120 then
   任务参数={1,170}
   end

  local 任务类型=取随机数(任务参数[1],任务参数[2])
  if 任务类型<=30 then
   任务类型=1
  elseif 任务类型<=50 then
   任务类型=2
   elseif 任务类型<=70 then
   任务类型=3
  elseif 任务类型<=80 then
   任务类型=4
  elseif 任务类型<=110 then
   任务类型=5
  elseif 任务类型<=140 then
   任务类型=6
   elseif 任务类型<=170 then
   任务类型=7
  end
  if 任务类型==5 and 取文韵示威对象(门派)==nil then
    任务类型=取随机数(1,2)
  end
  local 任务id=取唯一识别码(112)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    分类=任务类型,
    门派师傅=玩家数据[id].最后对话.名称,
    类型=112
  }
  if 任务类型==1 then
   local 序列=取随机数(1,#Q_文韵送信)
   任务数据[任务id].人物=Q_文韵送信[序列].名称
   任务数据[任务id].人物地图=Q_文韵送信[序列].地图
   添加最后对话(id,format("科举大赛临近，请帮我把这封送给#Y%s#W的%s",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物))
  elseif 任务类型==2 then
   任务数据[任务id].巡逻=0
   添加最后对话(id,"科举大赛临近，听说有不少飞贼试图前来盗窃试卷，烦请少侠到各地考点巡查一下。")
  elseif 任务类型==3 then
    self:设置文韵bb(id,任务id)
    if 任务数据[任务id].要求==nil then
      添加最后对话(id,format("既然你终日无所事事，考场建设，最近物资紧缺，那就去给我捉一只#Y%s",任务数据[任务id].bb))
    else
      添加最后对话(id,format("既然你终日无所事事，考场建设，最近物资紧缺，那就去给我捉一只#R%s#W的#Y%s",任务数据[任务id].要求,任务数据[任务id].bb))
    end
  elseif 任务类型==4 then
   self:设置文韵物品(id,任务id)
   if 任务数据[任务id].品质==nil then
     添加最后对话(id,format("考场内物资紧缺，你前去寻找到#Y%s#W交给我吧。",任务数据[任务id].物品))
   else
     添加最后对话(id,format("考场内物资紧缺，你前去寻找到#Y%s#W交给我吧。如果能找到品质达#R%s#W的，我会给你额外奖赏哟。",任务数据[任务id].物品,任务数据[任务id].品质))
     end
  elseif 任务类型==5 then
    local 临时对象=取文韵示威对象(门派)
    任务数据[任务id].模型=临时对象.模型
    任务数据[任务id].名称=临时对象.名称
    if 临时对象.武器~=nil then
      任务数据[任务id].武器=临时对象.武器.名称
      任务数据[任务id].武器等级=临时对象.武器.级别限制
    end
    local 地图=取文韵地图(玩家数据[临时对象.id].角色.数据.门派)
    地图=地图[1]
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id].地图编号=地图
    任务数据[任务id].地图名称=取地图名称(地图)
    任务数据[任务id].门派=玩家数据[临时对象.id].角色.数据.门派
    任务数据[任务id].x=xy.x
    任务数据[任务id].y=xy.y
    地图处理类:添加单位(任务id)
    添加最后对话(id,format("#Y%s的#R%s#W近日十分嚣张，不断来到本考场挑衅。你前去好好教训教训他，让他懂得为人要低调的道理。",玩家数据[临时对象.id].角色.数据.门派,任务数据[任务id].名称))
  elseif 任务类型==6 then
    local 人物=Q_随机模型[取随机数(1,5)]
    if 玩家数据[id].角色.数据.种族=="仙" then
      人物=Q_随机模型[取随机数(6,10)]
    elseif 玩家数据[id].角色.数据.种族=="魔" then
      人物=Q_随机模型[取随机数(11,15)]
    end
    local 地图范围={1070,1091,1514,1110,1174,1173}
    local 地图=地图范围[取随机数(1,#地图范围)]
    local xy=地图处理类.地图坐标[地图]:取随机点()
    --地图=1124
    --xy={x=35,y=27}
    任务数据[任务id].模型=人物
    任务数据[任务id].名称="苦战的文韵考生"
    任务数据[任务id].地图编号=地图
    任务数据[任务id].地图名称=取地图名称(地图)
    任务数据[任务id].x=xy.x
    任务数据[任务id].y=xy.y
    地图处理类:添加单位(任务id)
    添加最后对话(id,format("方才收到本考场的帮手紧急求救信号，请你立即赶往#Y%s#W进行支援。",任务数据[任务id].地图名称))
  elseif 任务类型==7 then
    local 种族=玩家数据[id].角色.数据.种族
    任务数据[任务id].名称=Q__乾坤袋[种族][取随机数(1,#Q__乾坤袋[种族])]
      local 模型={"强盗","山贼"}
    if 种族=="魔" then
      模型={"幽灵","吸血鬼"}
    elseif 种族=="仙" then
      模型={"灵符女娲","净瓶女娲"}
    end
    任务数据[任务id].模型=模型[取随机数(1,#模型)]
    local 地图范围={1070,1091,1514,1110,1174,1173}
    local 地图=地图范围[取随机数(1,#地图范围)]
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id].地图编号=地图
    任务数据[任务id].地图名称=取地图名称(地图)
    任务数据[任务id].x=xy.x
    任务数据[任务id].y=xy.y
    地图处理类:添加单位(任务id)
    添加最后对话(id,format("近日有#Y%s在#R%s#W附近作乱，请你携带#R乾坤袋#W前去将其降服。",任务数据[任务id].名称,任务数据[任务id].地图名称))
  end
  玩家数据[id].角色.数据.文韵次数=玩家数据[id].角色.数据.文韵次数+1
  if 玩家数据[id].角色.数据.文韵次数>100 then
     玩家数据[id].角色.数据.文韵次数=1
  end
  玩家数据[id].角色:添加任务(任务id)
end




function 任务处理类:添加门派任务(id,门派)
    if not 玩家数据[id].最后对话 then return end

    if self:触发条件(id,20,111,"师门任务") then--id,等级,任务,活动,队伍,人数
        return
    end
    local 任务参数={1,80} -- 30=送信 20巡逻 20 找bb 10 寻物 30示威  30门派支援 30 乾坤袋
    local 等级=玩家数据[id].角色.数据.等级
    if  等级>=90 then
     任务参数={1,80}
     end
    if  等级>=100 then
     任务参数={1,80}
     end
     if  等级>=120 then
     任务参数={1,80}
     end

    local 任务类型=取随机数(任务参数[1],任务参数[2])
    if 任务类型<=30 then
     任务类型=1
    elseif 任务类型<=50 then
     任务类型=2
     elseif 任务类型<=70 then
     任务类型=3
    elseif 任务类型<=80 then
     任务类型=4
    elseif 任务类型<=110 then
     任务类型=5
    elseif 任务类型<=140 then
     任务类型=6
     elseif 任务类型<=170 then
     任务类型=7
    end
    if 任务类型==5 and 取门派示威对象(门派)==nil then
      任务类型=取随机数(1,2)
    end
    local 任务id=取唯一识别码(111)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      玩家id=id,
      分类=任务类型,
      门派师傅=玩家数据[id].最后对话.名称,
      类型=111
    }
    if 任务类型==1 then
     local 序列=取随机数(1,#Q_师门送信)
     任务数据[任务id].人物=Q_师门送信[序列].名称
     任务数据[任务id].人物地图=Q_师门送信[序列].地图
     添加最后对话(id,format("请帮我把这封送给#Y%s#W的%s，事出紧急，切勿在路上耽搁。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物))
    elseif 任务类型==2 then
     任务数据[任务id].巡逻=0
     添加最后对话(id,"近日有妖魔鬼怪在本门派捣乱，请你在本门内进行巡逻，将那些前来捣乱的妖魔鬼怪清除掉。")
    elseif 任务类型==3 then
      self:设置师门bb(id,任务id)
      if 任务数据[任务id].要求==nil then
        添加最后对话(id,format("既然你终日无所事事，为师也不能让你就此荒废下去。那就去给我捉一只#Y%s",任务数据[任务id].bb))
      else
        添加最后对话(id,format("既然你终日无所事事，为师也不能让你就此荒废下去。那就去给我捉一只#R%s#W的#Y%s",任务数据[任务id].要求,任务数据[任务id].bb))
      end
    elseif 任务类型==4 then
     self:设置师门物品(id,任务id)
     if 任务数据[任务id].品质==nil then
       添加最后对话(id,format("门内物资紧缺，你前去寻找到#Y%s#W交给我吧。",任务数据[任务id].物品))
     else
       添加最后对话(id,format("门内物资紧缺，你前去寻找到#Y%s#W交给我吧。如果能找到品质达#R%s#W的，我会给你额外奖赏哟。",任务数据[任务id].物品,任务数据[任务id].品质))
       end
    elseif 任务类型==5 then
      local 临时对象=取门派示威对象(门派)
      任务数据[任务id].模型=临时对象.模型
      任务数据[任务id].名称=临时对象.名称
      任务数据[任务id].结束=3600
      if 临时对象.武器~=nil then
        任务数据[任务id].武器=临时对象.武器.名称
        任务数据[任务id].武器等级=临时对象.武器.级别限制
      end
      local 地图=取门派地图(玩家数据[临时对象.id].角色.数据.门派)
      地图=地图[1]
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id].地图编号=地图
      任务数据[任务id].地图名称=取地图名称(地图)
      任务数据[任务id].门派=玩家数据[临时对象.id].角色.数据.门派
      任务数据[任务id].x=xy.x
      任务数据[任务id].y=xy.y
      地图处理类:添加单位(任务id)
      添加最后对话(id,format("#Y%s的#R%s#W近日十分嚣张，不断来到本门派挑衅。你前去好好教训教训他，让他懂得为人要低调的道理。",玩家数据[临时对象.id].角色.数据.门派,任务数据[任务id].名称))
    elseif 任务类型==6 then
      local 人物=Q_随机模型[取随机数(1,5)]
      if 玩家数据[id].角色.数据.种族=="仙" then
        人物=Q_随机模型[取随机数(6,10)]
      elseif 玩家数据[id].角色.数据.种族=="魔" then
        人物=Q_随机模型[取随机数(11,15)]
      end
      local 地图范围={1070,1091,1514,1110,1174,1173}
      local 地图=地图范围[取随机数(1,#地图范围)]
      local xy=地图处理类.地图坐标[地图]:取随机点()
      --地图=1124
      --xy={x=35,y=27}
      任务数据[任务id].模型=人物
      任务数据[任务id].名称="苦战中的同门"
      任务数据[任务id].地图编号=地图
      任务数据[任务id].地图名称=取地图名称(地图)
      任务数据[任务id].x=xy.x
      任务数据[任务id].y=xy.y
      地图处理类:添加单位(任务id)
      添加最后对话(id,format("方才收到本门弟子紧急求救信号，请你立即赶往#Y%s#W进行支援。",任务数据[任务id].地图名称))
      任务数据[任务id].结束=3600
    elseif 任务类型==7 then
      local 种族=玩家数据[id].角色.数据.种族
      任务数据[任务id].名称=Q_乾坤袋[种族][取随机数(1,#Q_乾坤袋[种族])]
        local 模型={"强盗","山贼"}
      if 种族=="魔" then
        模型={"幽灵","吸血鬼"}
      elseif 种族=="仙" then
        模型={"灵符女娲","净瓶女娲"}
      end
      任务数据[任务id].模型=模型[取随机数(1,#模型)]
      local 地图范围={1070,1091,1514,1110,1174,1173}
      local 地图=地图范围[取随机数(1,#地图范围)]
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id].地图编号=地图
      任务数据[任务id].地图名称=取地图名称(地图)
      任务数据[任务id].x=xy.x
      任务数据[任务id].y=xy.y
      任务数据[任务id].结束=3600
      地图处理类:添加单位(任务id)
      添加最后对话(id,format("近日有#Y%s在#R%s#W附近作乱，请你携带乾坤袋前去将其降服。",任务数据[任务id].名称,任务数据[任务id].地图名称))
    end
    玩家数据[id].角色.数据.师门次数=玩家数据[id].角色.数据.师门次数+1
    if 玩家数据[id].角色.数据.师门次数==10 and 取随机数(1,250) >= 230 then
        发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].最后对话.名称,模型=玩家数据[id].最后对话.模型,对话="徒儿辛苦了，出门在外非常艰苦，为师特意为你准备了一件法宝，希望徒儿能够喜欢！",选项={"非常喜欢这法宝，谢谢！","我并不需要这个法宝"}})
    end
    if 玩家数据[id].角色.数据.师门次数>10 then
      玩家数据[id].角色.数据.师门次数=1
    end
    玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:设置师门物品(id,任务id)
  local 等级=玩家数据[id].角色.数据.等级
  local 临时等级=等级
  --if 临时等级>80 then 临时等级=80 end
  local 药品名称={}
  if 取随机数()<=50  then
    if 等级<=60 then
      药品名称={"四叶花","紫丹罗","佛手","鬼切草","女儿红"}
    elseif 等级<=90 then
      药品名称={"四叶花","紫丹罗","佛手","鬼切草","鹿茸","仙狐涎","地狱灵芝","六道轮回","紫石英","烤鸭","珍露酒","佛跳墙","臭豆腐","翡翠豆腐","豆斋果"}
    else
      药品名称={"豆斋果","佛跳墙","长寿面","醉生梦死","九转回魂丹","蛇胆酒","佛光舍利子","金香玉","小还丹","蛇蝎美人","风水混元丹","千年保心丹","定神香","五龙丹","十香返生丸"}
    end
    任务数据[任务id].物品=药品名称[取随机数(1,#药品名称)]
    if 等级>90 then
      local 临时品质=63
      if 等级>=120 then
        临时品质=96
      end
      if 等级>=140 then
        临时品质=129
      end
      任务数据[任务id].品质=临时品质
    end
  else  --装备
    local 临时限制={0,2}
    if 等级<=50 then

    elseif 等级<=70 then
      临时限制={2,4}
    elseif 等级<=100 then
      临时限制={3,5}
    elseif 等级<=130 then
      临时限制={5,7}
    else
      临时限制={6,8}
    end
    临时限制=取随机数(临时限制[1],临时限制[2])
    local 物品名称=玩家数据[id].装备.打造物品[取随机数(1,23)][临时限制+1]
    if type(物品名称)=="table" then
      物品名称=物品名称[取随机数(1,#物品名称)]
    end
    任务数据[任务id].物品= 物品名称
  end
end

function 任务处理类:设置文韵物品(id,任务id)
  local 等级=玩家数据[id].角色.数据.等级
  local 临时等级=等级
  --if 临时等级>80 then 临时等级=80 end
  local 药品名称={}
  if 取随机数()<=50  then
    if 等级<=60 then
      药品名称={"四叶花","紫丹罗","佛手","鬼切草","女儿红"}
    elseif 等级<=90 then
      药品名称={"四叶花","紫丹罗","佛手","鬼切草","鹿茸","仙狐涎","地狱灵芝","六道轮回","紫石英","烤鸭","珍露酒","佛跳墙","臭豆腐","翡翠豆腐","豆斋果"}
    else
      药品名称={"豆斋果","佛跳墙","长寿面","醉生梦死","九转回魂丹","蛇胆酒","佛光舍利子","金香玉","小还丹","蛇蝎美人","风水混元丹","千年保心丹","定神香","五龙丹","十香返生丸"}
    end
    任务数据[任务id].物品=药品名称[取随机数(1,#药品名称)]
    if 等级>90 then
      local 临时品质=63
      if 等级>=120 then
        临时品质=96
      end
      if 等级>=140 then
        临时品质=129
      end
      任务数据[任务id].品质=临时品质
    end
  else  --装备
    local 临时限制={0,2}
    if 等级<=50 then

    elseif 等级<=70 then
      临时限制={2,4}
    elseif 等级<=100 then
      临时限制={3,5}
    elseif 等级<=130 then
      临时限制={5,7}
    else
      临时限制={6,8}
    end
    临时限制=取随机数(临时限制[1],临时限制[2])
    local 物品名称=玩家数据[id].装备.打造物品[取随机数(1,23)][临时限制+1]
    if type(物品名称)=="table" then
      物品名称=物品名称[取随机数(1,#物品名称)]
    end
    任务数据[任务id].物品= 物品名称
  end
end

function 任务处理类:设置文韵bb(id,任务id)
  local 等级=玩家数据[id].角色.数据.等级
  local 临时等级=等级-20
  if 临时等级>85 then 临时等级=85 end
  local 临时等级1=等级-50
  if 临时等级1<1 then 临时等级1=0 end
  local 类型=取等级怪(取随机数(临时等级1,临时等级))
  类型=取敌人信息(类型[取随机数(1,#类型)])
  类型=类型[2]
  local 要求={"武艺超群","身强体壮","法力无边","身手敏捷","刀枪不入"}
  local 要求1=nil
  if 等级>=100 then
    要求1=要求[取随机数(1,#要求)]
  end
  任务数据[任务id].bb=类型
  任务数据[任务id].要求=要求1
end
function 任务处理类:设置师门bb(id,任务id)
  local 等级=玩家数据[id].角色.数据.等级
  local 临时等级=等级-20
  if 临时等级>85 then 临时等级=85 end
  local 临时等级1=等级-50
  if 临时等级1<1 then 临时等级1=0 end
  local 类型=取等级怪(取随机数(临时等级1,临时等级))
  类型=取敌人信息(类型[取随机数(1,#类型)])
  类型=类型[2]
  local 要求={"武艺超群","身强体壮","法力无边","身手敏捷","刀枪不入"}
  local 要求1=nil
  if 等级>=100 then
    要求1=要求[取随机数(1,#要求)]
  end
  任务数据[任务id].bb=类型
  任务数据[任务id].要求=要求1
end

function 任务处理类:添加官职任务(id)
  if 玩家数据[id].角色.数据.官职间隔~=nil and os.time()-玩家数据[id].角色.数据.官职间隔<=600 then
    发送数据(玩家数据[id].连接id,1501,{名称="李将军",模型="男人_马副将",对话="由于你取消的任务次数过多，请等待十分钟后再来。"})
    return
  end
  if self:触发条件(id,30,110,"官职任务") then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务类型=取随机数(1,10) -- 1=抓流氓 2送军粮 3巡逻 4索要物品
  if 任务类型<=5 then
    任务类型=1
  elseif 任务类型<=7 then
    任务类型=2
  elseif 任务类型<=9 then
    任务类型=3
  else
    任务类型=4
  end
  local 任务id=取唯一识别码(110)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    分类=任务类型,
    类型=110
  }
  发送数据(玩家数据[id].连接id,39)
  if 任务类型==1 then
   local 地图范围={1001,1501,1092,1070,1040,1226,1208}
   local 地图=地图范围[取随机数(1,#地图范围)]
   local xy=地图处理类.地图坐标[地图]:取随机点()
   任务数据[任务id].模型="赌徒"
   任务数据[任务id].名称="流氓"
   任务数据[任务id].地图编号=地图
   任务数据[任务id].地图名称=取地图名称(地图)
   任务数据[任务id].x=xy.x
   任务数据[任务id].y=xy.y
   任务数据[任务id].战斗次数=0
   地图处理类:添加单位(任务id)
   发送数据(玩家数据[id].连接id,1501,{名称="李将军",模型="男人_马副将",对话=format("目前有流氓正在#G/%s(%s,%s)#W/附近滋事，请你前去处理。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  elseif 任务类型==2 then
   local 地图范围={1193,1110}
   local 地图=地图范围[取随机数(1,#地图范围)]
   local xy=地图处理类.地图坐标[地图]:取随机点()
   任务数据[任务id].模型="护卫"
   任务数据[任务id].名称="军需官"
   任务数据[任务id].地图编号=地图
   任务数据[任务id].地图名称=取地图名称(地图)
   任务数据[任务id].x=xy.x
   任务数据[任务id].y=xy.y
   地图处理类:添加单位(任务id)
   发送数据(玩家数据[id].连接id,1501,{名称="李将军",模型="男人_马副将",对话=format("目前前线物资紧缺，请你将这批物资运输到#G/%s(%s,%s)#W/处，交给那里的军需官。为了保护物资的安全，请不要使用飞行道具。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  elseif 任务类型==3 then
    任务数据[任务id].情报=false
    发送数据(玩家数据[id].连接id,1501,{名称="李将军",模型="男人_马副将",对话="根据探子来报，突厥正在谋划些什么，请你前往边境搜集突厥的情报。"})
  elseif 任务类型==4 then
    任务数据[任务id].物品=取官职任务物品(id)
    发送数据(玩家数据[id].连接id,1501,{名称="李将军",模型="男人_马副将",对话=format("前线打仗的军备不足，请你帮我搜寻#R/%s#W/给我。",任务数据[任务id].物品)})
  end
  玩家数据[id].角色.数据.官职次数=玩家数据[id].角色.数据.官职次数+1
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:完成宠修任务(id,任务id)
  if 任务数据[任务id]==nil then return  end
  local 次数=任务数据[任务id].次数
  local 积分=3

  if 次数<=10 then
    积分=3
  elseif 次数<=20 then
    积分=5
  elseif 次数<=30 then
    积分=8
  elseif 次数<=40 then
    积分=10
  elseif 次数<=50 then
    积分=15
  elseif 次数<=60 then
    积分=20
  elseif 次数<=70 then
    积分=25
  elseif 次数<=80 then
    积分=30
  elseif 次数<=90 then
    积分=35
  else
    积分=40
  end
  积分=0
  if 任务数据[任务id].分类==11 then
    积分=1
  elseif 任务数据[任务id].分类==12 or 任务数据[任务id].分类==13 or 任务数据[任务id].分类==14 then
    积分=3
    if 任务数据[任务id].等级==7 then
      积分=4
    elseif 任务数据[任务id].等级==8 then
      积分=5
    end
  elseif 任务数据[任务id].分类==15 then
    积分=10
  end
  任务数据[任务id].积分=任务数据[任务id].积分+积分
  发送数据(玩家数据[id].连接id,38,{内容="你获得了"..积分.."点任务积分"})
  if 任务数据[任务id].积分>=100 then
    任务数据[任务id].积分=0
    玩家数据[id].角色:添加银子(500000,"宠修任务",1)
    local 奖励数据=玩家数据[id].道具:给予书铁(id,{9,11})
    常规提示(id,"#Y你得到了#R"..奖励数据[1])
    广播消息({内容=format("#S(跑环任务)#R/%s#Y在跑环任务中的积分达到了100，因此获得了马真人奖励的#G/%s#Y",玩家数据[id].角色.数据.名称,"90-110级书铁"),频道="xt"})
  end
  if 任务数据[任务id].次数>=100 then
      添加活动次数(id,"宠物跑环")
      local 任务id=玩家数据[id].角色:取任务(13)
      玩家数据[id].角色:取消任务(任务id)
      任务数据[任务id]=nil
      if 玩家数据[id].角色.数据.bb修炼[玩家数据[id].角色.数据.bb修炼.当前][1]<玩家数据[id].角色.数据.bb修炼[玩家数据[id].角色.数据.bb修炼.当前][3] then
         玩家数据[id].角色:添加bb修炼经验(id,100)
      end
      local 获得物品={}
      for i=1,#自定义数据.宠物跑环 do
           if 取随机数()<=自定义数据.宠物跑环[i].概率 then
              获得物品[#获得物品+1]=自定义数据.宠物跑环[i]
           end
      end
       获得物品=删除重复(获得物品)
       if 获得物品~=nil then
          local 取编号=取随机数(1,#获得物品)
          if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
             玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
             广播消息({内容=format("#S(跑环任务)#R/%s#Y在跑环任务中的次数达到了100，因此获得了马真人奖励的#G/%s#Y",玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
       end
    常规提示(id,"#Y恭喜你完成了全部的跑环任务")
  else
    任务数据[任务id].次数=任务数据[任务id].次数+1
    self:设置宠修任务(id,任务id)
  end
end

function 任务处理类:添加宠修任务(id)
  if 玩家数据[id].角色.数据.银子<500000 then
    添加最后对话(id,"你没有那么多的银子。")
    return
  end
  if self:触发条件(id,69,13,"宠物跑环") then--id,等级,任务,活动,队伍,人数
      return
  end
  玩家数据[id].角色:扣除银子(500000,"领取宠物修炼任务",1)
  local 任务id=取唯一识别码(13)

  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    名称=1,
    模型=1,
    地图编号=1001,
    地图名称=1,
    积分=0,
    次数=1,
    类型=13
  }
  玩家数据[id].角色:添加任务(任务id)
  self:设置宠修任务(id,任务id)
end

function 任务处理类:设置宠修任务(id,任务id)
  local 类型=取随机数(1,15)
  if 类型<=11 then
    类型=11
  end
  local 序列=取随机数(1,#Q_随机人物)
  local 地图=Q_随机人物[序列].地图
  local 名称=Q_随机人物[序列].人物[取随机数(1,#Q_随机人物[序列].人物)]
  --地图=1111
  --名称="马真人"
  任务数据[任务id].地图编号=地图
  任务数据[任务id].地图名称=取地图名称(地图)
  任务数据[任务id].名称=名称
  任务数据[任务id].分类=类型
  任务数据[任务id].传说=nil
  if 类型==11 then
   添加最后对话(id,format("前些日子%s的%s帮了我不少忙，请你前去替我感谢他吧。",任务数据[任务id].地图名称,任务数据[任务id].名称))
  elseif 类型==12 then --索要环装
    local 临时等级=取随机数(3,6)
    任务数据[任务id].等级=临时等级
    临时限制=临时等级
    local 物品名称=玩家数据[id].装备.打造物品[取随机数(1,23)][临时限制+1]
    if type(物品名称)=="table" then
      物品名称=物品名称[取随机数(1,#物品名称)]
    end
    任务数据[任务id].物品= 物品名称
    if 任务数据[任务id].物品==nil then
     任务数据[任务id].物品="追星踏月"
     任务数据[任务id].等级=6
    end
   添加最后对话(id,format("听闻%s的%s正在四处搜寻#Y%s#W，请你帮他寻找一个吧。",任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].物品))
  elseif 类型==13 then
   local  物品表={"烤肉","醉生梦死","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","烤鸭","虎骨酒"}
   任务数据[任务id].物品=物品表[取随机数(1,#物品表)]
   添加最后对话(id,format("听闻%s的%s正在四处搜寻#Y%s#W，请你帮他寻找一个吧。",任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].物品))
  elseif 类型==14 then
   local  物品表={"金香玉","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","佛光舍利子","十香返生丸","五龙丹"}
   任务数据[任务id].物品=物品表[取随机数(1,#物品表)]
   添加最后对话(id,format("听闻%s的%s正在四处搜寻#Y%s#W，请你帮他寻找一个吧。",任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].物品))
  elseif 类型==15 then
   local bb表={"巨蛙","野猪","强盗","野鬼","狼","僵尸","马面","牛头","古代瑞兽","天兵","树怪","赌徒","狐狸精","花妖","黑熊","老虎","牛妖","骷髅怪","羊头怪","蛤蟆精","兔子怪"}
   任务数据[任务id].bb=bb表[取随机数(1,#bb表)]
   添加最后对话(id,format("%s的%s沉迷于炼妖，现在他正缺少#Y变异%s#W，还请你想法子给他弄一只吧。",任务数据[任务id].地图名称,任务数据[任务id].名称,任务数据[任务id].bb))
  end
  玩家数据[id].角色:刷新任务跟踪()
end

function 任务处理类:添加宝图任务(id)
  if self:触发条件(id,30,4) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(4)
  local 地图范围={1501,1092,1193,1070,1142}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称=宝图名字.姓[取随机数(1,#宝图名字.姓)]..宝图名字.名[取随机数(1,#宝图名字.名)]
  if 取随机数()<=70 then
    名称=名称..宝图名字.氏[取随机数(1,#宝图名字.氏)]
  end
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称,
    模型="强盗",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=4
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="店小二",模型="男人_店小二",对话=format("听说近日有强盗#Y/%s#W/被官府通缉，现已逃到了#G/%s(%s,%s)#W/处。若是能找到他，肯定能搜出不少宝物。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end

function 任务处理类:添加三界悬赏任务(id)
  if self:触发条件(id,30,209) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(209)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="通缉犯"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    模型="强盗",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=209
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="铁无双",模型="男人_衙役",对话=format("据可靠情报，现有#Y/%s#W/逃到了#G/%s(%s,%s)#W/处，请你前往将其缉拿。此通缉犯实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end

function 任务处理类:完成游泳任务(id)
  local 队伍id=玩家数据[id].队伍
  local 等级=取队伍平均等级(玩家数据[id].队伍,id)
  local 任务id=玩家数据[id].角色:取任务(109)
  for n=1,#队伍数据[队伍id].成员数据 do
      local 临时id=队伍数据[队伍id].成员数据[n]
     -- local 任务序号=(1+任务数据[任务id].序列*0.1)
      -- local 经验=qz(qjy(等级)*15*(1+任务数据[任务id].序列*0.1))
      -- local 银子=qz(qcb(等级)*10)
      -- 添加活动次数(临时id,"游泳比赛")
      -- 玩家数据[临时id].角色:添加活跃积分(1,"游泳比赛",1)
      -- 玩家数据[临时id].角色:添加经验(经验,"游泳比赛",1)
      -- 玩家数据[临时id].角色:添加储备(银子,"游泳比赛",1)
      -- if 玩家数据[临时id].角色.数据.参战信息~=nil then
      --   玩家数据[临时id].召唤兽:获得经验(玩家数据[临时id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"游泳")
      -- end
      玩家数据[临时id].角色:自定义银子添加("游泳任务",(1+任务数据[任务id].序列*0.1))
      玩家数据[临时id].角色:添加活跃积分(1,"游泳比赛",1)
      添加活动次数(临时id,"游泳比赛")
        local 获得物品={}
        for i=1,#自定义数据.游泳任务 do
          if 取随机数()<=自定义数据.游泳任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.游泳任务[i]
          end
        end

        获得物品=删除重复(获得物品)

        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[临时id].道具:自定义给予道具(临时id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(游泳比赛)#R/%s#Y经过一番激烈的游水，最终完成了#R%s#Y，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[临时id].角色.数据.名称,"游泳比赛",获得物品[取编号].名称),频道="xt"})
            end
        end

        if 任务数据[任务id].序列==20 then
          玩家数据[临时id].角色:取消任务(玩家数据[临时id].角色:取任务(109))
          常规提示(临时id,"#Y/你完成了游泳比赛")
        end
      end

      if 任务数据[任务id].序列==20 then
          任务数据[任务id]=nil
      else
          任务数据[任务id].序列=任务数据[任务id].序列+1
          任务数据[任务id].战斗=false
          发送数据(玩家数据[id].连接id,1501,{名称=任务数据[任务id].序列.."号裁判",模型="雨师",对话=format("报道成功，现在请立即前往#Y/%s(%s,%s)#W/的%s号裁判报道。",取地图名称(Q_游泳数据[任务数据[任务id].序列].z),Q_游泳数据[任务数据[任务id].序列].x,Q_游泳数据[任务数据[任务id].序列].y,任务数据[任务id].序列)})
          玩家数据[id].角色:刷新任务跟踪()
      end
end

function 任务处理类:添加游泳任务(id)
          if not 游泳开关 then
              添加最后对话(id,"当前不是比赛时间。")
              return
          end
          if self:触发条件(id,30,109,"游泳比赛",1) then--id,等级,任务,活动,队伍,人数
              return
          end
          local 任务id=取唯一识别码(109)
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=7200,
            玩家id=id,
            队伍组={},
            序列=1,
            战斗=false,
            类型=109
          }
          玩家数据[id].角色:添加任务(任务id,1,nil,{名称="报名官",模型="雨师",对话=format("你成功参加了游泳比赛，现在请立即前往#Y/%s(%s,%s)#W/的%s号裁判报道。",取地图名称(Q_游泳数据[任务数据[任务id].序列].z),Q_游泳数据[任务数据[任务id].序列].x,Q_游泳数据[任务数据[任务id].序列].y,1)})
end

function 任务处理类:添加闯关任务(id)
          if 闯关参数.开关==false then
            添加最后对话(id,"当前不是门派闯关活动时间")
            return
          end
          if self:触发条件(id,30,107,"门派闯关",1,3) then--id,等级,任务,活动,队伍,人数
              return
          end
          local 任务id= 取唯一识别码(107)
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=18000,
            玩家id=id,
            类型=107
          }
          任务数据[任务id].闯关序列=DeepCopy(Q_门派编号)
          任务数据[任务id].当前序列=取随机数(1,#任务数据[任务id].闯关序列)
          玩家数据[id].角色:添加任务(任务id,1,nil,{名称="门派闯关使者",模型="男人_马副将",对话=format("你成功领取了门派闯关任务，请立即前往#Y/%s#W/接受考验。",任务数据[任务id].闯关序列[任务数据[任务id].当前序列])})
end

function 任务处理类:开启门派闯关()
      广播消息({内容="#G/十五门派闯关活动已经开启，各位玩家可以前往长安城#R/门派闯关活动使者#G/处开启闯关活动#32",频道="xt"})
      闯关参数={开关=true,起始=os.time(),记录={}}
end

function 任务处理类:开启皇宫飞贼()
  皇宫飞贼={开关=true,贼王={}}
  广播消息({内容="#G/皇宫飞贼活动已经开启，各位玩家可以前往长安城#R/御林军左统领#G/处领取任务#32",频道="xt"})
end

function 任务处理类:开启迷宫()
  for n=1,20 do
    local 真假={}
    if 取随机数()<=50 then
      真假={[1]=true,[2]=false}
    else
      真假={[1]=false,[2]=true}
    end
    for i=1,2 do
      local 任务id=取唯一识别码(201)
      local 地图=1600+n
      local xy=地图处理类.地图坐标[地图]:取随机点()
      if 地图==1601  then
        if i==1 then
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=82800,
            玩家id=id,
            名称="迷宫土地",
            称谓="迷宫传送员",
            模型="男人_土地",
            x=xy.x,
            y=xy.y,
            地图编号=地图,
            序列=n,
            真假=true,
            地图名称=取地图名称(地图),
            类型=201
          }
          任务数据[任务id].真假=true
          地图处理类:添加单位(任务id)
        end
      elseif 地图==1620  then
        if i==1 then
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=82800,
            玩家id=id,
            名称="迷宫守卫",
            称谓="迷宫奖励专员",
            模型="护卫",
            x=xy.x,
            y=xy.y,
            地图编号=地图,
            序列=n,
            地图名称=取地图名称(地图),
            类型=201
          }
          地图处理类:添加单位(任务id)
        end
      else
        任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=82800,
        玩家id=id,
        名称="迷宫土地",
        称谓="迷宫传送员",
        模型="男人_土地",
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        序列=n,
        真假=真假[i],
        地图名称=取地图名称(地图),
        类型=201
        }
        地图处理类:添加单位(任务id)
      end
    end
  end
  广播消息({内容="#G/幻域迷宫活动已经开启，各位玩家可以前往傲来国#R/金毛猿#G/处参加幻域迷宫活动#32",频道="xt"})
  迷宫数据={}
  迷宫数据.开关=true
  迷宫数据.事件=os.time()
  迷宫数据.奖励=0
  迷宫数据.起始=os.time()
  self:刷新迷宫小怪()
end

function 任务处理类:刷出万象福()
  local 地图范围={1092,1070,1208,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 数量=取随机数(5,10)
  local 地图=地图范围[取随机数(1,#地图范围)]
  for i=1,数量 do
    local 任务id=取唯一识别码(389)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="万象福",
      模型="超级神虎",
      x=xy.x,
      y=xy.y,
      行走开关=false,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=389
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W(万象福)#Y一群#G万象福#Y出现在#R%s#Y，请各位少侠赶紧去挑战，挑战成功后可获得丰厚的奖励。",取地图名称(地图)),频道="xt"})
end


function 任务处理类:刷出新春快乐()--
  local 地图范围={1004,1005,1006,1007,1008,1090}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 数量=取随机数(10,20)
  local 地图=地图范围[取随机数(1,#地图范围)]
  for i=1,数量 do
    local 任务id=取唯一识别码(399)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="蛇年大吉",
      模型="超级神蛇",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=399
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W(新春快乐)#Y一群#G蛇年大吉#Y出现在#R%s#Y，请各位少侠赶紧去挑战，挑战成功后可获得丰厚的奖励。",取地图名称(地图)),频道="xt"})
end



function 任务处理类:刷出小小盲僧()--
  local 地图范围={1004,1005,1006,1007,1008,1090}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 数量=取随机数(10,20)
  local 地图=地图范围[取随机数(1,#地图范围)]
  for i=1,数量 do
    local 任务id=取唯一识别码(336)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="盲僧的眼珠",
      模型="盲僧眼珠",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=336
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#S(小小盲僧)#Y一群#小小盲僧#Y出现在#R%s#Y，请各位少侠赶紧去挑战，挑战成功后可获得丰厚的奖励。",取地图名称(地图)),频道="xt"})
end



function 任务处理类:开启地煞星任务(id)
         local 任务等级={
                [69]={1506,1501},
                [89]={1193,1091},
                [109]={1110,1514},
                [129]={1174,1208},
                [149]={1040,1226},
                [169]={1201,1210},
                [189]={1173,1207}
            }


        local 显示地图 = ""
        local 称号 = {[1]="★",[2]="★★",[3]="★★★",[4]="★★★★",
               [5]="★★★★★",[6]="★★★★★★",[7]="★★★★★★★",
               [8]="★★★★★★★★",[9]="★★★★★★★★★",
               [10]="★★★★★★★★★★"}
        local 名字 ={"地魁星·陈继真","地煞星·黄景元","地勇星·贾成",  "地杰星·呼百颜","地雄星·鲁修德","地威星·须成",  "地英星·孙祥",
                       "地奇星·王平",  "地猛星·柏有患","地文星·革高",  "地正星·考鬲",  "地辟星·李燧",  "地阖星·刘衡",  "地强星·夏祥",
                       "地暗星·余惠",  "地辅星·鲍龙",  "地会星·鲁芝",  "地佐星·黄丙庆","地佑星·张奇",  "地灵星·郭巳",  "地兽星·金南道",
                       "地微星·陈元",  "地慧星·车坤",  "地暴星·桑成道","地默星·周庚",  "地猖星·齐公",  "地狂星·霍之元","地飞星·叶中",
                       "地走星·顾宗",  "地巧星·李昌",  "地明星·方吉",  "地进星·徐吉",  "地退星·樊焕",  "地满星·卓公",  "地遂星·孔成",
                       "地周星·姚金秀","地隐星·宁三益","地异星·余知",  "地理星·童贞",  "地俊星·袁鼎相","地乐星·汪祥",  "地捷星·耿颜",
                       "地速星·邢三鸾","地镇星·姜忠",  "地羁星·孔天兆","地魔星·李跃",  "地妖星·龚倩",  "地幽星·段清",  "地伏星·门道正",
                       "地僻星·祖林",  "地空星·萧电",  "地孤星·吴四玉","地全星·匡玉",  "地短星·蔡公",  "地角星·蓝虎",  "地囚星·宋禄",
                       "地藏星·关斌",  "地平星·龙成",  "地损星·黄乌",  "地奴星·孔道灵","地察星·张焕",  "地恶星·李信",  "地魂星·徐山",
                       "地数星·葛方",  "地阴星·焦龙",  "地刑星·秦祥",  "地壮星·武衍公","地劣星·范斌",  "地健星·叶景昌","地耗星·姚烨",
                       "地贼星·孙吉",  "地狗星·陈梦庚"
        }

      for k,v in pairs(任务等级) do
        local 地图=v[取随机数(1,#v)]
        显示地图 = 显示地图..取地图名称(地图).."，"
        for i=1,5 do
            local xy=地图处理类.地图坐标[地图]:取随机点()
            local 任务id=取唯一识别码(304)
            local 造型=Q_随机模型[取随机数(1,#Q_随机模型)]
            local 武器造型=取高级地煞星武器造型(造型)
            local 难度 = 取随机数(1,10)
            任务数据[任务id]={
              id=任务id,
              起始=os.time(),
              结束=3600,
              玩家id=id,
              名称=名字[取随机数(1,#名字)],
              模型=造型,
              武器=武器造型.武器,
              武器等级=武器造型.级别,
              等级=k,
              称谓=称号[难度],
              难度=难度,
              行走开关=true,
              x=xy.x,
              y=xy.y,
              地图编号=地图,
              地图名称=取地图名称(地图),
              类型=304
            }
            地图处理类:添加单位(任务id)
        end
    end
    广播消息({内容=format("#G据可靠消息，强大而神秘的地煞星带着天界宝物已经出现在#Y%s#G场景内。只有智勇双全的强者才有机缘获得宝物，少侠敢来挑战么？".."#"..取随机数(1,110),显示地图),频道="xt"})
end






function 任务处理类:开启天罡星任务(id)
          local 任务等级={
                [69]={1506,1501},
                [89]={1193,1091},
                [109]={1110,1514},
                [129]={1174,1208},
                [149]={1040,1226},
                [169]={1201,1210},
                [189]={1173,1207}
            }
          local 显示地图 = ""
          local 称号 = {[1]="★",[2]="★★",[3]="★★★",[4]="★★★★",
               [5]="★★★★★",[6]="★★★★★★",[7]="★★★★★★★",
               [8]="★★★★★★★★",[9]="★★★★★★★★★",
               [10]="★★★★★★★★★★"}
          local 名字 ={"天魁星·高衍","天罡星·黄真","天机星·卢昌",  "天闲星·纪丙",  "天勇星·姚公孝","天雄星·施桧",  "天猛星·孙乙","天威星·李豹","天英星·朱义",
                        "天贵星·陈坎","天富星·黎仙","天满星·方保",  "天孤星·詹秀",  "天伤星·李洪仁","天玄星·王龙茂","天健星·邓玉","天暗星·李新","天佑星·徐正道",
                        "天空星·典通","天速星·吴旭","天异星·吕自成","天煞星·任来聘","天微星·龚清",  "天究星·单百招","天退星·高可","天寿星·戚成","天剑星·王虎",
                        "天平星·卜同","天罪星·姚公","天损星·唐天正","天败星·申礼",  "天牢星·闻杰",  "天慧星·张智雄","天暴星·毕德","天哭星·刘达","天巧星·程三益"
                }
          for k,v in pairs(任务等级) do
              local 地图=v[取随机数(1,#v)]
              显示地图 = 显示地图..取地图名称(地图).."，"
              for i=1,5 do
                  local xy=地图处理类.地图坐标[地图]:取随机点()
                  local 任务id=取唯一识别码(314)
                  local 造型=Q_随机模型[取随机数(1,#Q_随机模型)]
                  local 武器造型=取天罡星武器造型(造型)
                  local 难度 = 取随机数(1,10)
                  任务数据[任务id]={
                        id=任务id,
                        起始=os.time(),
                        结束=3600,
                        玩家id=id,
                        名称=名字[取随机数(1,#名字)],
                        模型=造型,
                        武器=武器造型.武器,
                        武器等级=武器造型.级别,
                        等级=k,
                        称谓=称号[难度],
                        难度=难度,
                        行走开关=true,
                        x=xy.x,
                        y=xy.y,
                        地图编号=地图,
                        地图名称=取地图名称(地图),
                        类型=314
                  }
                  地图处理类:添加单位(任务id)
              end
          end
           广播消息({内容=format("#G据可靠消息，强大而神秘的天罡星带着天界宝物已经出现在#Y%s#G场景内。只有智勇双全的强者才有机缘获得宝物，少侠敢来挑战么？".."#"..取随机数(1,110),显示地图),频道="xt"})

end








function 任务处理类:开启妖王(id)
  local 地图=1092
  for n=1,取随机数(1,10) do
    local 任务id=取唯一识别码(205)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    local 名称="避世老妖"
    local 模型="炎魔神"
    local 随机参数=取随机数()
    if 随机参数<=30 then
      名称="冥府妖王"
      模型="鬼将"
    elseif 随机参数<=60 then
      名称="猪仙大圣"
      模型="净瓶女娲"
    end
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=1800,
      玩家id=id,
      名称=名称,
      模型=模型,
      x=xy.x,
      y=xy.y,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=205
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#Y%s#W挖宝时候一不小心在#R妖王#W脑袋上敲出了一个大包，妖王气得正在#G%s#W寻衅闹事，各路英雄快前往平乱啊！",玩家数据[id].角色.数据.名称,取地图名称(地图)),频道="xt"})
end


function 任务处理类:开启妖王1(id,地图数据)
  local 地图=1092
  if 地图数据~= nil then
    地图 = 地图数据
  end
  for n=1,取随机数(6,10) do
    local 任务id=取唯一识别码(205)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    local 名称="避世老妖"
    local 模型="炎魔神"
    local 随机参数=取随机数()
    if 随机参数<=30 then
      名称="冥府妖王"
      模型="鬼将"
    elseif 随机参数<=60 then
      名称="猪仙大圣"
      模型="净瓶女娲"
    end
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=1800,
      玩家id=id,
      名称=名称,
      模型=模型,
      x=xy.x,
      y=xy.y,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=205
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#Y/%s#W/在剿灭妖魔鬼怪时居然得罪了#R/妖王#W/，妖王气得正在#G/%s#W/寻衅闹事，各路英雄快前往平乱啊！",玩家数据[id].角色.数据.名称,取地图名称(地图)),频道="xt"})
end

function 任务处理类:开启宝藏山()
  宝藏山数据={开关=true,起始=os.time(),刷新=os.time(),间隔=10}
  广播消息({内容="#G/宝藏山活动已经开启，各位玩家可以前往宝象国#R/土地公公#G/处参加宝藏山活动#32",频道="xt"})
end

function 任务处理类:进入宝藏山(id)
  if 玩家数据[id].队伍~=0 then
    常规提示(id,"#Y宝藏山不允许组队进入")
    return
  elseif 玩家数据[id].角色.数据.等级<69 then
    常规提示(id,"#Y低于69级的玩家无法进入宝藏山")
    return
  end
  local xy=地图处理类.地图坐标[5001]:取随机点()
  地图处理类:跳转地图(id,5001,xy.x,xy.y)
  local 人数=地图处理类:取地图人数(5001)
  地图处理类:当前消息广播1(5001,"#G当前共有#R"..人数.."#G名玩家在宝藏山内寻宝")
  if 宝藏山数据[id]==nil then
    宝藏山数据[id]={小宝箱=0,大宝箱=0}
  end
end

function 任务处理类:宝藏山刷出宝箱()
  local 人数=地图处理类:取地图人数(5001)
  local 小宝箱=人数*4
  local 大宝箱=人数*2
  宝藏山数据.小宝箱=小宝箱
  宝藏山数据.大宝箱=大宝箱
  local 地图=5001
  for n=1,小宝箱 do
    local 任务id=取唯一识别码(203)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=120,
      玩家id=id,
      名称="小宝箱",
      模型="宝箱",
      x=xy.x,
      y=xy.y,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=203
    }
    地图处理类:添加单位(任务id)
  end
  for n=1,大宝箱 do
    local 任务id=取唯一识别码(204)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=120,
      玩家id=id,
      名称="大宝箱",
      模型="宝箱",
      x=xy.x,
      y=xy.y,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=204
    }
    地图处理类:添加单位(任务id)
  end
  地图处理类:当前消息广播1(5001,"#G当前共有#R"..人数.."#G名玩家在宝藏山内寻宝。")
  地图处理类:当前消息广播1(5001,format("#G一道金光闪过，#R%s#G个小宝箱和#R%s#G个大宝箱散落在场景内的各个角落。宝箱将在2分钟后自动消失，请赶快寻找哟。本次活动中已开启过10个小宝箱或5个大宝箱的玩家将无法再开启对应的宝箱。",小宝箱,大宝箱))
  if os.time()-宝藏山数据.起始>=3420 then
    宝藏山数据.间隔=999999
    地图处理类:当前消息广播1(5001,"#R本次活动宝箱已经全部投放，3分钟后场景内的所有玩家将自动被传送出去。")
  else
    地图处理类:当前消息广播1(5001,"#R3分钟后将投放下一批宝箱。")
  end
end

function 任务处理类:刷新迷宫小怪()
  for n=1,19 do
    local 地图=1600+n
    for i=1,20 do
      local 任务id=取唯一识别码(202)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型=取随机怪(1,60)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=600,
        玩家id=id,
        名称="迷宫小妖",
        事件="明雷",
        行走开关=true,
        模型=模型[2],
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        序列=n,
        地图名称=取地图名称(地图),
        类型=202
      }
      地图处理类:添加单位(任务id)
    end
  end
end



function 任务处理类:开启游泳比赛()
          for n=1,20 do
              local 任务id=取唯一识别码(108)
              local 地图=Q_游泳数据[n].z
              任务数据[任务id]={
                  id=任务id,
                  起始=os.time(),
                  结束=82800,
                  玩家id=id,
                  名称=n.."号裁判",
                  称谓="游泳比赛裁判",
                  模型="雨师",
                  x=Q_游泳数据[n].x,
                  y=Q_游泳数据[n].y,
                  方向=Q_游泳数据[n].f,
                  地图编号=地图,
                  序列=n,
                  地图名称=取地图名称(地图),
                  类型=108
              }
              地图处理类:添加单位(任务id)
          end
          广播消息({内容="#G/游泳比赛活动已经开启，各位玩家可以前往傲来国#R/报名官#G/处参加游泳比赛#32",频道="xt"})
          游泳开关=true

end

function 任务处理类:添加押镖任务(id)
  if 玩家数据[id].角色.数据.地图数据.编号~=1024 then
    __S服务:输出("玩家"..id.." 非法使用外挂警告！！！")
    写配置("./ip封禁.ini","ip",玩家数据[id].ip,1)
    写配置("./ip封禁.ini","ip",玩家数据[id].ip.." 非法使用外挂,玩家ID:"..id,1)
    发送数据(玩家数据[id].连接id,998,"请注意你的角色异常！已经对你进行封IP")
    return 0
  end
  if 玩家数据[id].队伍 ~= 0 then
      添加最后对话(id,"请单人来接取押镖任务！")
      return
  end
  local 总次数 = tonumber(自定义数据.活动次数.押镖) or 50
  总次数=总次数+1
  if not  押镖数据[id] then
      押镖数据[id]=1
  end
  if 押镖数据[id]>=总次数 then
      添加最后对话(id,"你已经完成了今日所有押镖活动!")
      return
  end
  local 任务id =取唯一识别码(300)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    次数=押镖数据[id],
    类型=300
  }
  local 序列=取随机数(1,#Q_押镖数据)
  任务数据[任务id].人物=Q_押镖数据[序列].名称
  任务数据[任务id].人物地图=Q_押镖数据[序列].地图
  添加最后对话(id,format("请帮我把镖银送给#Y%s#W的#R%s#W，事出紧急，切勿在路上耽搁。当前#R第%s镖",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物,任务数据[任务id].次数))
  玩家数据[id].角色:添加任务(任务id)
  玩家数据[id].角色.数据.跑镖 = os.time()+取随机数(80,130)

end

function 任务处理类:完成押镖任务(任务id,数字id,地图)
    if 任务数据[任务id]==nil then
      return
    end
    if 玩家数据[数字id].角色.数据.地图数据.编号~=地图 then
        __S服务:输出("玩家"..数字id.." 非法使用外挂警告！！！")
        写配置("./ip封禁.ini","ip",玩家数据[数字id].ip,1)
        写配置("./ip封禁.ini","ip",玩家数据[数字id].ip.." 非法使用外挂,玩家ID:"..数字id,1)
        发送数据(玩家数据[数字id].连接id,998,"请注意你的角色异常！已经对你进行封IP")
      return 0
    end
    广播消息({内容=format("#S(押镖任务)#R/%s#Y在押送#G%s镖银#Y途中表现突出获得了郑镖头的赏识",玩家数据[数字id].角色.数据.名称,"普通"),频道="xt"})
    玩家数据[数字id].角色:添加银子(50000,"押镖",1)
    玩家数据[数字id].角色:添加储备(50000,"押镖")
    if 取随机数(1,100)<=5  then
      玩家数据[数字id].道具:给予书铁(数字id,{9,11},"指南书")
      常规提示(数字id,"#Y/你获得了#G90-110级#指南书奖励")
      广播消息({内容=format("#S(押镖)#R/%s#Y完成押镖运气包爆棚，#Y因此获得了#G90-110级指南书",玩家数据[数字id].角色.数据.名称),频道="xt"})
    end
    if 取随机数(1,100)<=1 then
        local 名称="鬼谷子"
        玩家数据[数字id].道具:给予道具(数字id,名称)
        常规提示(数字id,"#Y/你获得了"..名称)
        广播消息({内容=format("#S(押镖任务)#R/%s#Y在押送#G%s镖银#Y途中表现突出获得了郑镖头的赏识，因此获得了郑镖头额外奖励的#G/%s#Y",玩家数据[数字id].角色.数据.名称,"普通",名称),频道="xt"})
    end
    if not 押镖数据[数字id] then
       押镖数据[数字id]=1
    end
    押镖数据[数字id]=押镖数据[数字id]+1
    任务数据[任务id]=nil
    玩家数据[数字id].角色:取消任务(任务id)
    玩家数据[数字id].角色.数据.跑镖 = nil
    if 成就数据[数字id].押镖==nil then
      成就数据[数字id].押镖=0
    end
    if 成就数据[数字id].押镖<1001 then
      成就数据[数字id].押镖=成就数据[数字id].押镖+1
    end
    if 成就数据[数字id].押镖 == 1 then
      local 成就提示 = "再世镖王"
      local 成就提示1 = "完成1次了押镖"
      发送数据(玩家数据[数字id].连接id,149,{内容=成就提示,内容1=成就提示1})
    elseif 成就数据[数字id].押镖==1000 then
      local 成就提示 = "再世镖王"
      local 成就提示1 = "完成1000次了押镖"
      成就数据[数字id].成就点 = 成就数据[数字id].成就点 + 1
      玩家数据[id].角色.数据.成就积分 = 玩家数据[id].角色.数据.成就积分 + 1
      常规提示(数字id,"#Y/恭喜你获得了1点成就")
      发送数据(玩家数据[数字id].连接id,149,{内容=成就提示,内容1=成就提示1})
      玩家数据[数字id].角色:添加称谓("快递小哥")
    end
end


function 任务处理类:添加镖王任务(id,难度)
  if 镖王活动.开关==false then
    添加最后对话(id,"当前不是比赛时间。")
    return
  end
  if self:触发条件(id,69,208,"镖王押镖",1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(208)
  local 地图=镖王数据[1].地图
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称="镖师",
    称谓="镖王活动",
    模型="护卫",
    x=镖王数据[1].x,
    y=镖王数据[1].y,
    方向=镖王数据[1].z,
    地图编号=地图,
    序列=1,
    难度=难度,
    完成={1,2,3,4,5,6},
    地图名称=取地图名称(地图),
    类型=208
  }
  for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
      添加活动次数(v,"镖王押镖")
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,{名称="郑镖头",模型="男人_镖头",内容=format("请带着这批#Y%s镖银#W立即前往#Y%s(%s,%s)#W处的镖师报道。",任务数据[任务id].难度,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})

end





function 任务处理类:开启镖王活动()
  for n=1,6 do
    local 任务id=取唯一识别码(207)
    local 地图=镖王数据[n].地图
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=82800,
      玩家id=id,
      名称="镖师",
      称谓="镖王活动",
      模型="护卫",
      x=镖王数据[n].x,
      y=镖王数据[n].y,
      方向=镖王数据[n].z,
      地图编号=地图,
      序列=n,
      地图名称=取地图名称(地图),
      类型=207
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容="#G/镖王活动已经开启，各位玩家可以前往长风镖局的#R/郑镖头#G/处报名参加#32",频道="xt"})
  镖王活动={开关=true}
end

function 任务处理类:刷出星宿()
  local 广播地图={}
  for i=1,5 do
    local 任务id=取唯一识别码(104)
    local 地图范围={1070,1208,1040,1092,1226,1135,1142,1198,1002,1122,1513,1131,1140,1512,1111,1146,1116}  -- 长寿村，朱紫国，西梁女国，傲来国，宝象国
    local 地图=地图范围[取随机数(1,#地图范围)]
    广播地图[#广播地图+1]=地图
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称=Q_星宿名称[取随机数(1,#Q_星宿名称)],
      模型="天兵",
      变异=true,
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=104
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W/听闻凡间多有能人异士，玉皇大帝特派二十八星君下凡至#G/%s,%s,%s,%s,%s#W/附近搜寻有仙缘之人。凡通过星君考验者，必将重重有赏#77",取地图名称(广播地图[1]),取地图名称(广播地图[2]),取地图名称(广播地图[3]),取地图名称(广播地图[4]),取地图名称(广播地图[5])),频道="xt"})
end

function 任务处理类:刷出妖魔鬼怪()
  local 地图范围={1091,1506}
  if 取随机数()<=50 then
    地图范围={1514,1110}
  end
  for n=1,2 do
    local 数量=取随机数(10,15)
    local 地图=地图范围[n]
    for i=1,数量 do
      local 任务id=取唯一识别码(105)
      local 名称="妖魔"
      local 模型={"吸血鬼","幽灵","鬼将"}
      if 取随机数()<=50 then
        名称="鬼怪"
        模型={"僵尸","野鬼"}
      end
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=1800,
        玩家id=id,
        名称=名称,
        模型=模型[取随机数(1,#模型)],
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=105
      }
      地图处理类:添加单位(任务id)
    end
    广播消息({内容=format("#R/一群妖魔鬼怪冲破了仙界封印，来到了#G/%s#R/作恶，还请各位英雄侠士赶紧去降服它们。",取地图名称(地图)),频道="xt"})
  end

end

function 任务处理类:刷出知了王()
  local 地图范围={1228,1210}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local 数量=取随机数(8,16)
  for i=1,数量 do
    local 任务id=取唯一识别码(210)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="知了王",
      模型="知了王",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=210
    }
    地图处理类:添加单位(任务id)
  end
广播消息({内容=format("#W/一批知了王出现在了#G/%s#W/处捣乱，还请各位英雄侠士赶紧去降服它们。",取地图名称(地图)),频道="xt"})
end

function 任务处理类:刷出知了先锋()
 local 地图范围={1110,1193}---大唐国境、江南野外、花果山、长寿郊外
  if 取随机数()<= 50 then
    地图范围={1514,1091}
  end
  local 地图广播={}
  for v=1,2 do
      local 地图=地图范围[v]
      地图广播[#地图广播+1] = 地图
      for i=1,15 do
        local 任务id=取唯一识别码(305)
        local xy=地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=3550,
          玩家id=id,
          名称="知了先锋",
          模型="百足将军",
          x=xy.x,
          y=xy.y,
          行走开关=true,
          地图编号=地图,
          地图名称=取地图名称(地图),
          类型=305
        }
        地图处理类:添加单位(任务id)
      end
  end
  广播消息({内容=format("#W/一批知了先锋出现在了#G/%s,%s#W/处捣乱，还请各位英雄侠士赶紧去降服它们。",取地图名称(地图广播[1]),取地图名称(地图广播[2])),频道="xt"})
end









function 任务处理类:刷出创世佛屠()
  local 数量=取随机数(2,5)
  for i=1,数量 do
    local 任务id=取唯一识别码(306)
    local xy=地图处理类.地图坐标[1204]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="创世佛屠",
      模型="吕布",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1204,
      地图名称=取地图名称(1204),
      类型=306
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W/创世佛屠出现在了#G/%s#W/处毁灭世界，还请各位英雄侠士赶紧去降服它们。",取地图名称(1204)),频道="xt"})
end




function 任务处理类:刷出善恶如来()
  local 数量=取随机数(1,3)
  for i=1,数量 do
    local 任务id=取唯一识别码(310)
    local xy=地图处理类.地图坐标[1205]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="善恶如来",
      模型="剑圣",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1205,
      地图名称=取地图名称(1205),
      类型=310
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W/善恶如来出现在了#G/%s#W/处蛊惑世人，还请各位英雄侠士赶紧去降服它们。",取地图名称(1205)),频道="xt"})
end





function 任务处理类:刷出门派入侵()
  local 称号 = {[1]="★",[2]="★★",[3]="★★★",[4]="★★★★",
       [5]="★★★★★",[6]="★★★★★★",[7]="★★★★★★★",
       [8]="★★★★★★★★",[9]="★★★★★★★★★",
       [10]="★★★★★★★★★★"}
  for i=1,2 do
    for n=1,15 do
      local 任务id=取唯一识别码(315)
      local 地图范围=取门派地图(Q_门派编号[n])
      local 地图=地图范围[1]
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 难度 = 取随机数(1,10)
      local 造型=Q_随机模型[取随机数(1,#Q_随机模型)]
      local 武器造型=取天罡星武器造型(造型)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=14400,
        玩家id=id,
        武器=武器造型.武器,
        武器等级=武器造型.级别,
        称谓=称号[难度],
        名称="门派入侵者",
        模型=造型,
        难度=难度,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=315
      }
      地图处理类:添加单位(任务id)
    end
end

  广播消息({内容="#R/门派入侵者#W/出现在了#G/各个门派#W/，还请各位英雄侠士赶紧去降服它们。",频道="xt"})

end





function 任务处理类:添加双倍(时间,类型,id) --1为领取 2为解冻
  if 双倍数据[id]==nil then
    双倍数据[id]={可领=4,冻结=0,间隔=os.time()}
  end
  if 双倍数据[id].冻结~=0 and 类型==1 then
    发送数据(玩家数据[id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="请先恢复已经被冻结的双倍时间。"})
    return
  end
  local 领取时间=0
  local 有效时间=0
  if 类型==1 then
      if 双倍数据[id].可领<=0 then
        发送数据(玩家数据[id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你本日已经没有可领取的双倍时间了，请明日再来。"})
        return
      elseif 双倍数据[id].可领<时间 then
        领取时间=双倍数据[id].可领
        双倍数据[id].可领=0
      else
        领取时间=时间
        双倍数据[id].可领=双倍数据[id].可领-时间
      end
      有效时间=领取时间*60
  else
      有效时间=时间
  end
  有效时间=有效时间*60
  if 玩家数据[id].角色:取任务(2)==0 then
    local 任务id=取唯一识别码(2)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=有效时间,
      玩家id=id,
      类型=2
    }
    玩家数据[id].角色:添加任务(任务id)
  else
      任务数据[玩家数据[id].角色:取任务(2)].结束=任务数据[玩家数据[id].角色:取任务(2)].结束+有效时间
      玩家数据[id].角色:刷新任务跟踪()
  end

  if 类型==1 then
    发送数据(玩家数据[id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你成功领取了"..领取时间.."小时的双倍时间，请赶快去杀敌吧。加油！"})
  else
    发送数据(玩家数据[id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="已经帮您恢复了冻结的时间，请赶快去杀敌吧。加油！"})
  end
end

function 任务处理类:添加精修时间(时间,类型,id) --1为领取 2为解冻
  if 三倍数据[id]==nil then
      三倍数据[id]={可领=2,冻结=0,间隔=os.time()}
  end
  if 玩家数据[id].角色.数据.等级<60 then
    发送数据(玩家数据[id].连接id,1501,{名称="御林军",模型="男人_马副将",对话="你的等级尚未达到60级，无法领取精修时间。"})
    return
  end
  local 领取时间=0
  local 有效时间=0
  if 类型==1 then
    if 三倍数据[id].可领<=0 then
      发送数据(玩家数据[id].连接id,1501,{名称="御林军",模型="男人_马副将",对话="你本日已经没有可领取的精修时间了，请明日再来。"})
      return
    elseif 三倍数据[id].可领<时间 then
      领取时间=三倍数据[id].可领
      三倍数据[id].可领=0
    else
      领取时间=时间
      三倍数据[id].可领=三倍数据[id].可领-时间
    end
    有效时间=领取时间*60
  end
  有效时间=有效时间*60
  if 玩家数据[id].角色:取任务(3)==0 then
    local 任务id=取唯一识别码(3)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=有效时间,
      玩家id=id,
      类型=3
    }
    玩家数据[id].角色:添加任务(任务id)
  else
      任务数据[玩家数据[id].角色:取任务(3)].结束=任务数据[玩家数据[id].角色:取任务(3)].结束+有效时间
      玩家数据[id].角色:刷新任务跟踪()
  end

  if 类型==1 then
    发送数据(玩家数据[id].连接id,1501,{名称="御林军",模型="男人_马副将",对话="你成功领取了"..领取时间.."小时的精修时间，请赶快去杀敌吧。加油！"})
  end
end

function 任务处理类:添加一倍经验(id)
  local 任务id=取唯一识别码(7755)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    类型=7755
  }
  玩家数据[id].角色:添加任务(任务id)

end

function 任务处理类:添加双倍经验(id)
  local 任务id=取唯一识别码(7756)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    类型=7756
  }
  玩家数据[id].角色:添加任务(任务id)

end


function 任务处理类:添加双倍银子(id)
  local 任务id=取唯一识别码(7757)
  local 结束时间=3600
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    类型=7757
  }
  玩家数据[id].角色:添加任务(任务id)

end

function 任务处理类:添加摇钱树任务(id)
  local 任务id=取唯一识别码(22)
 任务数据[任务id]={
 id=任务id,
 起始=os.time(),
 结束=3600,
 玩家id=id,
 名称="摇钱树",
 模型="摇钱树苗",
 x=math.floor(玩家数据[id].角色.数据.地图数据.x/20),
 y=math.floor(玩家数据[id].角色.数据.地图数据.y/20),
 地图编号=玩家数据[id].角色.数据.地图数据.编号,
 地图名称=取地图名称(玩家数据[id].角色.数据.地图数据.编号),
 类型=22,
 次数=0,
 除虫=0,
 浇水=0,
 施肥=0,
 计时=0,
 阶段=0,
 虫患=false,
 记录时间=os.time(),
 等待时间=os.time(),
 成功操作=0,
 操作=false

}
  地图处理类:添加单位(任务id)
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:刷新摇钱树强盗(id,任务)

  local 任务id=取唯一识别码(24)
  local 模型 = "强盗"
  local 名称 = "江湖大盗"
  local 地图=任务数据[任务].地图编号
  local xy={x=任务数据[任务].x+10,y=任务数据[任务].y+10}
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    名称=名称.."("..玩家数据[id].角色.数据.名称..")",
    模型=模型,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=24,
    主任务=任务,
    行走开关=true
  }
  任务数据[任务].刷出强盗 = 任务id
  地图处理类:添加单位(任务id)
  玩家数据[id].角色:添加任务(任务id)

end

function 任务处理类:添加摇钱树元宝任务(id,xy)
  for n=1,取随机数(1,2) do
    local 任务id=取唯一识别码(23)
    local 坐标=地图处理类.地图坐标[玩家数据[id].角色.数据.地图数据.编号].寻路:最近坐标(xy)
     任务数据[任务id]={
     id=任务id,
     起始=os.time(),
     结束=3600,
     玩家id=id,
     名称="元宝",
     模型="银子",
     x=math.floor(坐标.x),
     y=math.floor(坐标.y),
     地图编号=玩家数据[id].角色.数据.地图数据.编号,
     地图名称=取地图名称(玩家数据[id].角色.数据.地图数据.编号),
     类型=23,

    }
    地图处理类:添加单位(任务id)
  end
end

function 任务处理类:设置科举任务(id)
  if 玩家数据[id].角色:取任务(7)~=0 then
  添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
  return
  end
  local 任务id=取唯一识别码(7)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    类型=7
  }
  玩家数据[id].角色:添加任务(任务id)
  常规提示(id,"#Y/你参加了科举活动")
end

function 任务处理类:添加摄妖香(id)
  local 任务id=取唯一识别码(9)
  local 结束时间=3600
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    类型=9
  }
  玩家数据[id].角色:添加任务(任务id)
end



function 任务处理类:设置赏金任务(id)
    if 玩家数据[id].角色:取任务(6)~=0 then
    添加最后对话(id,"你有了该任务了")
    return
  end
  local 任务id=取唯一识别码(6)
  local 数量=10
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    数量=数量,
    类型=6
  }
  玩家数据[id].角色:添加任务(任务id)
  常规提示(id,"#Y/你获得了赏金任务")
  发送数据(玩家数据[id].连接id,1501,{名称="皇宫护卫",模型="护卫",对话="野外的怪物们十分不安分，老是袭击过往的村民，还请你去教训教训它们，让它们能安分守己些。"})
end

function 任务处理类:设置新手任务(id)
      if 玩家数据[id].角色:取任务(66)~=0 then
    添加最后对话(id,"你有了该任务了")
    return
  end
  local 任务id=取唯一识别码(66)
  local 数量=10
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    数量=数量,
    类型=66
  }
  玩家数据[id].角色:添加任务(任务id)
  常规提示(id,"#Y/你获得了新手任务")
  发送数据(玩家数据[id].连接id,1501,{名称="赵捕头",模型="男人_衙役",对话="野外的怪物们十分不安分，老是袭击过往的村民，还请你去教训教训它们，让它们能安分守己些。"})
end


-- function 任务处理类:设置打造160级装备任务(id,临时类型,级别,序列,属性)
--   local 任务id=取唯一识别码(5)
--   local 数量=1
--   if 级别<=20 then
--     数量=1
--   elseif 级别<=30 then
--     数量=2
--   elseif 级别<=40 then
--     数量=3
--   elseif 级别<=50 then
--     数量=5
--   elseif 级别<=60 then
--     数量=7
--   elseif 级别<=70 then
--     数量=9
--   elseif 级别<=80 then
--     数量=12
--   elseif 级别<=90 then
--     数量=16
--   elseif 级别<=100 then
--     数量=20
--   elseif 级别<=110 then
--     数量=24
--   elseif 级别 == 160 then
--     数量=50
--   else
--     数量=30
--   end

--   local 类型 = "装备"

--   if 级别 == 160 then
--     类型 = "160装备"
--   end

--   任务数据[任务id]={
--     id=任务id,
--     起始=os.time(),
--     玩家id=id,
--     名称=临时类型,
--     级别=级别,
--     序列=序列,
--     元身属性=属性,
--     石头=取强化石(),
--     数量=数量,
--     打造类型=类型,
--     类型=5
--   }

--   玩家数据[id].角色:添加任务(任务id)
--   常规提示(id,"#Y/你获得了打造任务")
--   发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].最后对话.名称,模型=玩家数据[id].最后对话.模型,对话=format("你的#R/%s#W/已经在制作中了，但还欠缺%s块%s，你赶紧去寻找吧。找到后回来交给我就可以完成了。",临时类型,数量,任务数据[任务id].石头)})
-- end


function 任务处理类:设置打造装备任务(id,临时类型,级别,序列,摊位购买)
  local 任务id=取唯一识别码(5)
  local 数量=1
  if 级别<=100 then
    数量=15
  elseif 级别<=110 then
    数量=20
  elseif 级别<=120 then
    数量=25
  elseif 级别<=130 then
    数量=30
  elseif 级别<=140 then
    数量=35
  elseif 级别<=150 then
    数量=40
  else
    数量=50
  end
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    名称=临时类型,
    级别=级别,
    序列=序列,
    石头=取强化石(),
    数量=数量,
    打造类型="装备",
    类型=5
  }
  if 摊位购买~=nil then
     任务数据[任务id].购买=摊位购买
  end
  玩家数据[id].角色:添加任务(任务id)
  常规提示(id,"#Y/你获得了打造任务")
  发送数据(玩家数据[id].连接id,1501,{名称="物件_打铁炉",模型="物件_打铁炉",对话=format("你的#R/%s#W/已经在制作中了，但还欠缺%s块%s，你赶紧去寻找吧。找到后回来交给我就可以完成了。",临时类型,数量,任务数据[任务id].石头),选项={"打造任务"}})
end



function 任务处理类:设置打造灵饰任务(id,临时类型,级别,部位,摊位购买)
  local 任务id=取唯一识别码(5)
  local 数量=1
   if 级别<=100 then
    数量=15
  elseif 级别<=120 then
    数量=30
  elseif 级别<=140 then
    数量=45
  else
    数量=80
  end
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    名称=临时类型,
    级别=级别,
    部位=部位,
    石头=取强化石(),
    数量=数量,
    打造类型="灵饰",
    类型=5
  }
  if 摊位购买~=nil then
    任务数据[任务id].购买=摊位购买
  end
  玩家数据[id].角色:添加任务(任务id)
  常规提示(id,"#Y/你获得了打造任务")
  发送数据(玩家数据[id].连接id,1501,{名称="物件_打铁炉",模型="物件_打铁炉",对话=format("你的#R/%s#W/已经在制作中了，但还欠缺%s块%s，你赶紧去寻找吧。找到后回来交给我就可以完成了。",临时类型,数量,任务数据[任务id].石头),选项={"打造任务"}})
end






function 任务处理类:添加罗羹(id,气血,魔法)
  local 任务id=取唯一识别码(10)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    气血=气血,
    魔法=魔法,
    类型=10
  }
  玩家数据[id].角色:添加任务(任务id)

end




function 任务处理类:添加变身(id,变化之术)
  local 任务id=取唯一识别码(1)
  local 结束时间=15*(1+变化之术)*60
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=结束时间,
    玩家id=id,
    类型=1
  }
  玩家数据[id].角色:添加任务(任务id)

end

function 任务处理类:添加点化任务(id)
  local 任务id=取唯一识别码(14)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=120,
    玩家id=id,
    类型=14
  }
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:设置天庭叛逆()
  local 地图范围={1208,1040,1092,1174}
  for i=1,#地图范围 do
    local 临时数量=取随机数(5,15)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(128)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围={"持国巡守","毗舍童子","灵灯侍者","般若天女","真陀护法","增长巡守","金身罗汉","曼珠沙华","红萼仙子","琴仙","金铙僧","灵鹤","雾中仙","大力金刚","净瓶女娲","律法女娲","灵符女娲","如意仙子","巡游天神","星灵仙子","雨师","天兵","风伯"}--"宠物口粮",
      local 模型=模型范围[取随机数(1,#模型范围)]
      local x称谓 = "天庭叛逆"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=1200,
        玩家id=0,
        名称=模型,
        模型=模型,
        等级=取随机数(1,140),
        称谓=x称谓,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        行走开关=true,
        地图名称=取地图名称(地图),
        显示饰品=true,
        类型=128
      }
      地图处理类:添加单位(任务id)

    end
  end
  广播消息({内容=format("#W/叛逆肆虐，一群可怕的天庭叛逆跑到了#Y/朱紫国、西梁女国、傲来国.北俱芦洲#W/各路英雄快去消灭它们吧#46"),频道="xt"})

end

function 任务处理类:糖果派对()
   local 数量=取随机数(20,50)
    for n=1,数量 do
      local 任务id=取唯一识别码(129)
      local xy=地图处理类.地图坐标[1501]:取随机点()
      local 模型范围={"泡泡仙灵·飞燕女","泡泡仙灵·骨精灵","泡泡仙灵·剑侠客","泡泡仙灵·龙太子","泡泡仙灵·杀破狼","泡泡仙灵·神天兵","泡泡仙灵·巫蛮儿","泡泡仙灵·羽灵神" }
     -- local 模型范围={"泡泡","超级泡泡","超级泡泡"}
      local 模型名称范围={"泡泡糖","棒棒糖","薄荷糖","彩虹糖"}
      local 名称模型=模型名称范围[取随机数(1,#模型名称范围)]
      local 模型=模型范围[取随机数(1,#模型范围)]
      -- local  染色范围={52,67,97,92,101}
      --local  染色方案=染色范围[取随机数(1,#染色范围)]
      local x称谓 = "糖果派对"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=1200,
       -- 染色方案= 染色方案,
        --染色组={1,0,nil},
        玩家id=0,
        名称=名称模型,
        模型=模型,
        称谓=x称谓,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=1501,
        地图名称=取地图名称(1501),
        显示饰品=true,
        类型=129
      }
      地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W/糖果派对，一群可爱的糖果跑到了#Y/建邺城#W各路英雄快去消灭它们吧#46"),频道="xt"})
end








function 任务处理类:设置青龙任务(id)
    if not 玩家数据[id].最后对话 then return end
  if 玩家数据[id].角色:取任务(301) ~= 0 then
      添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
      return
  elseif 活动次数查询(id,"青龙任务")==false then
    return
  elseif 玩家数据[id].队伍 ~= 0 then
      添加最后对话(id,"组队状态下无法接任务！")
      return
  end
  local 任务id = 取唯一识别码(301)
  self.随机数字 = 取随机数(1,100)
  local 任务分类=1
  if self.随机数字<=35 then
    任务分类=1
  elseif self.随机数字<=55 then
    任务分类=2
  elseif self.随机数字<=80 then
    任务分类=3
  else
    任务分类=4
  end
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    分类=任务分类,
    帮派总管=玩家数据[id].最后对话.名称,
    类型=301
  }
  if 任务分类==1 then
    local 序列=取随机数(1,#Q_青龙人物随机)
    任务数据[任务id].人物=Q_青龙人物随机[序列].名称
    任务数据[任务id].人物地图=Q_青龙人物随机[序列].地图
    添加最后对话(id,format("请帮我将这封书信送给#Y%s#W的#R%s#W，事出紧急，切勿在路上耽搁。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物))
  elseif 任务分类==2 then
    self.随机药品=Q_随机药品[取随机数(1,#Q_随机药品)]
    任务数据[任务id].药品=self.随机药品
    添加最后对话(id,format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",任务数据[任务id].药品))
  elseif 任务分类==3 then
    self.随机烹饪=Q_随机烹饪[取随机数(1,#Q_随机烹饪)]
    任务数据[任务id].烹饪=self.随机烹饪
    添加最后对话(id,format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",任务数据[任务id].烹饪))
  else
    任务数据[任务id].巡逻=0
    添加最后对话(id,format("最近常有敌对门派探子在长安城内出现，还请你在四周巡逻一番。"))
  end
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:完成青龙任务(任务id,数字id,类型)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=类型 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end

  local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
  if  帮派编号 ==nil or 帮派编号<=0 then
    常规提示(数字id,"#Y/你还没有帮派")
    return
  end
  帮派数据[帮派编号].成员数据[数字id].帮贡.当前 = 帮派数据[帮派编号].成员数据[数字id].帮贡.当前 + 40
  帮派数据[帮派编号].成员数据[数字id].帮贡.上限 = 帮派数据[帮派编号].成员数据[数字id].帮贡.上限 + 40
  玩家数据[数字id].角色.数据.帮贡 =帮派数据[帮派编号].成员数据[数字id].帮贡.当前
  帮派数据[帮派编号].安定度 = 帮派数据[帮派编号].安定度 + 20
  玩家数据[数字id].角色:自定义银子添加("青龙任务",1)
  玩家数据[数字id].角色:添加活跃积分(1,"青龙任务",1)
  常规提示(数字id,"#Y/你获得了40点帮派贡献度，帮派增加了20点安定度")
  添加活动次数(数字id,"青龙任务")
        local 获得物品={}
        for i=1,#自定义数据.青龙任务 do
          if 取随机数()<=自定义数据.青龙任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.青龙任务[i]
          end
        end
        获得物品=删除重复(获得物品)
        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
               广播消息({内容=format("#S(帮派青龙)#R/%s#Y完成了帮派青龙任务获得了#G%s奖励",玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
            end
        end

  local 当前内政 = 帮派数据[帮派编号].当前内政
  if 当前内政 ~= "无" and 帮派数据[帮派编号].帮派建筑[当前内政].数量 < 取帮派建筑数量(帮派数据[帮派编号].帮派规模) then
      帮派数据[帮派编号].帮派建筑[当前内政].当前经验 = 帮派数据[帮派编号].帮派建筑[当前内政].当前经验 + 2000
      帮派处理类:升级内政(帮派编号)
  end
  任务数据[任务id]=nil
  玩家数据[数字id].角色:取消任务(任务id)
end

function 任务处理类:设置玄武任务(id)
  if not 玩家数据[id].最后对话 then return end
  if self:触发条件(id,69,302,"玄武任务",1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id = 取唯一识别码(302)
  local 队伍id=玩家数据[id].队伍
  self.随机数字 = 取随机数(1,100)
  local 任务分类=1
  if self.随机数字<=35 then
    任务分类=1
  elseif self.随机数字<=55 then
    任务分类=2
  elseif self.随机数字<=80 then
    任务分类=3
  else
    任务分类=4
  end
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=0,
    分类=任务分类,
    帮派总管=玩家数据[id].最后对话.名称,
    类型=302
  }
  if 任务分类==1 then
    local 序列=取随机数(1,#Q_青龙人物随机)
    任务数据[任务id].人物=Q_青龙人物随机[序列].名称
    任务数据[任务id].人物地图=Q_青龙人物随机[序列].地图
    玩家数据[id].角色:添加任务(任务id,1,nil,{名称="厢房总管",模型="男人_兰虎",内容=format("请帮我将这封书信送给#Y%s#W的#R%s#W，事出紧急，切勿在路上耽搁。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物)})

  elseif 任务分类==2 then
    self.随机药品=Q_随机药品[取随机数(1,#Q_随机药品)]
    任务数据[任务id].药品=self.随机药品
    玩家数据[id].角色:添加任务(任务id,1,nil,{名称="厢房总管",模型="男人_兰虎",内容=format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",任务数据[任务id].药品)})

  elseif 任务分类==3 then
    self.随机烹饪=Q_随机烹饪[取随机数(1,#Q_随机烹饪)]
    任务数据[任务id].烹饪=self.随机烹饪
    玩家数据[id].角色:添加任务(任务id,1,nil,{名称="厢房总管",模型="男人_兰虎",内容=format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",任务数据[任务id].烹饪)})
  else
    任务数据[任务id].巡逻=0
    玩家数据[id].角色:添加任务(任务id,1,nil,{名称="厢房总管",模型="男人_兰虎",内容=format("最近常有敌对门派探子在长安城内出现，还请你在四周巡逻一番。")})
  end
end

function 任务处理类:完成玄武任务(任务id,id组,类型)
  if 任务数据[任务id]==nil or 任务数据[任务id].分类~=类型 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
  for n=1,#id组 do
      local id=id组[n]
      local 帮派编号 = 玩家数据[id].角色.数据.帮派数据.编号
       if  帮派编号 ==nil or 帮派编号<=0 then
        常规提示(id,"#Y/你还没有帮派")
            return
      end
      帮派数据[帮派编号].成员数据[id].帮贡.当前 = 帮派数据[帮派编号].成员数据[id].帮贡.当前 + 40
      帮派数据[帮派编号].成员数据[id].帮贡.上限 = 帮派数据[帮派编号].成员数据[id].帮贡.上限 + 40
      玩家数据[id].角色.数据.帮贡 = 帮派数据[帮派编号].成员数据[id].帮贡.当前
      帮派数据[帮派编号].安定度 = 帮派数据[帮派编号].安定度 + 20
      玩家数据[id].角色:自定义银子添加("玄武任务",1)
      玩家数据[id].角色:添加活跃积分(1,"玄武任务",1)
      常规提示(id,"#Y/你获得了40点帮派贡献度，帮派增加了20点安定度")
      添加活动次数(id,"玄武任务")
        local 获得物品={}
        for i=1,#自定义数据.玄武任务 do
          if 取随机数()<=自定义数据.玄武任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.玄武任务[i]
          end
        end
        获得物品=删除重复(获得物品)
        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
               广播消息({内容=format("#S(帮派玄武)#R/%s#Y完成了帮派玄武任务获得了#G%s奖励",玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
            end
        end

      任务数据[任务id]=nil
      玩家数据[id].角色:取消任务(任务id)
  end
end

function 任务处理类:开启坐骑任务(id)
  if 玩家数据[id].角色:取任务(307)~=0 then
    常规提示(id,"#Y/你当前已经获得坐骑任务了，快去完成吧！")
    return
  end
  local 坐骑费用 = 3000000
  if 取银子(id) < 坐骑费用 then
      常规提示(id,"#Y/你的银两不够接取任务哦")
      return
  end
  if 玩家数据[id].角色.数据.体力<100 then
      常规提示(id,"#Y/你当前没有那么体力，无法领取任务！")
      return
  end
  if 玩家数据[id].角色.数据.等级<39 then
      常规提示(id,"#Y/等级小于39无法开启")
      return
  end
  玩家数据[id].角色:扣除银子(坐骑费用,"坐骑费用",1)
  local 任务id= 取唯一识别码(307)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    气血=气血,
    魔法=魔法,
    烹饪=0,
    法宝=0,
    药品=0,
    分类=1,
    类型=307
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,1501,{名称="百兽王",模型="大大王",对话=format("快去找长寿村的太白金星咨询下吧！"),选项={"我这就去"}})
end

function 任务处理类:完成坐骑任务(任务id,id)
  if 任务数据[任务id]==nil then
    return
  end
  玩家数据[id].角色.数据.种族坐骑=true
  玩家数据[id].角色:增加种族坐骑(id)
  任务数据[任务id]=nil
  玩家数据[id].角色:取消任务(任务id)
end

function 任务处理类:开启法宝任务(id)
    if not 玩家数据[id].最后对话 then return end
  if self:触发条件(id,69,308,"法宝材料") then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id = 取唯一识别码(308)
  self.随机数字 = 取随机数(1,100)
  if self.随机数字 <= 35 then
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        玩家id=id,
        分类=1,
        法宝NPC=玩家数据[id].最后对话.名称,
        类型=308
      }
      local 序列=取随机数(1,#Q_青龙人物随机)
      任务数据[任务id].人物=Q_青龙人物随机[序列].名称
      任务数据[任务id].人物地图=Q_青龙人物随机[序列].地图
      添加最后对话(id,format("#Y%s#W的#R%s#W正需要帮忙，少侠可以去看看，或许有意外的收获。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物))
  elseif self.随机数字 <= 55 then
      self.随机药品=Q_随机药品[取随机数(1,#Q_随机药品)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        玩家id=id,
        分类=2,
        法宝NPC=玩家数据[id].最后对话.名称,
        物品=self.随机药品,
        类型=308
      }
      添加最后对话(id,format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",self.随机药品))
  elseif self.随机数字 <= 80 then
      self.随机烹饪=Q_随机烹饪[取随机数(1,#Q_随机烹饪)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        玩家id=id,
        法宝NPC=玩家数据[id].最后对话.名称,
        物品=self.随机烹饪,
        分类=3,
        类型=308
      }
      添加最后对话(id,format("近日物资紧缺，请你前寻找#Y%s#W，事出紧急，切勿在路上耽搁。",self.随机烹饪))
  else
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        玩家id=id,
        法宝NPC=玩家数据[id].最后对话.名称,
        分类=4,
        类型=308
      }
      local 序列=取随机数(1,#Q_青龙人物随机)
      任务数据[任务id].人物=Q_青龙人物随机[序列].名称
      任务数据[任务id].人物地图=Q_青龙人物随机[序列].地图
      添加最后对话(id,format("#Y%s#W的#R%s#W仗着自己会点武功，到处惹是生非，希望少侠能为名除害。",取地图名称(任务数据[任务id].人物地图),任务数据[任务id].人物))
  end
  if 任务数据[任务id].进程==nil then
      任务数据[任务id].进程=1
  else
      任务数据[任务id].进程=2
  end
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:完成法宝任务(任务id,id)
  if 任务数据[任务id]==nil then
    return
  end
  local 随机材料=Q_随机材料[取随机数(1,#Q_随机材料)]
  玩家数据[id].道具:给予道具(id,随机材料,1,nil,nil,"专用")
  常规提示(id,"恭喜少侠获得了法宝合成材料"..随机材料.."。")
  添加活动次数(id,"法宝材料")
  if 玩家数据[id].角色.法宝进程==nil then
      玩家数据[id].角色.法宝进程=1
      玩家数据[id].角色:取消任务(任务id)
      self:开启法宝任务(id)
      常规提示(id,"你获得了一个新的法宝任务。")
  else
      玩家数据[id].角色:取消任务(任务id)
      玩家数据[id].角色.法宝进程=2
  end
  if 玩家数据[id].角色.法宝进程==2 then
      发送数据(玩家数据[id].连接id,1501,{名称="金童子",模型="男人_道童",对话=format("少侠已经集齐法宝合成的材料了，可以来天宫找我领取内丹任务进行法宝合成哦")})
  end
end

function 任务处理类:开启法宝内丹任务(id)
    if self:触发条件(id,69,309,"法宝内丹") then--id,等级,任务,活动,队伍,人数
        return
    end
    local 地图范围={1501,1092,1070,1193,1173,1146,1140,1208,1040,1226,1142}
    local 地图=地图范围[取随机数(1,#地图范围)]
    local 任务id=取唯一识别码(309)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=2400,
      玩家id=id,
      名称="千年妖魔",
      模型="进阶吸血鬼",
      变异=true,
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=309
    }
    添加最后对话(id,format("内丹是修炼者经过百年甚至千年的修炼在体内以灵气结成的丹丸，#Y%s#W正在#R%s#Y%s#W，#Y%s四处为祸人间，少侠可前往收服该妖魔获取内丹。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y))
    地图处理类:添加单位(任务id)
    玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:完成法宝内丹任务(任务id,id)
  if 任务数据[任务id]==nil then
    return
  end
  玩家数据[id].道具:给予道具(id,"内丹",1,nil,nil,"专用")
  添加活动次数(id,"法宝内丹")
  if 玩家数据[id].角色.法宝进程==2 then
      玩家数据[id].角色.法宝进程=nil
  end
  常规提示(id,"恭喜少侠获得了法宝合成材料内丹，可以去金童子处合成法宝了。")
  地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  玩家数据[id].角色:取消任务(任务id)
end








-- function 任务处理类:加载首席单位()
--   for n=1,15 do
--     local 任务id=Q_门派编号[n]..999888
--     任务数据[任务id]={
--       id=任务id,
--       名称=首席争霸[Q_门派编号[n]].名称,
--       模型=首席争霸[Q_门派编号[n]].模型,
--       称谓="首席弟子",
--       染色组=首席争霸[Q_门派编号[n]].染色组,
--       染色方案=首席争霸[Q_门派编号[n]].染色方案,
--       武器=首席争霸[Q_门派编号[n]].武器,
--       武器等级=首席争霸[Q_门派编号[n]].武器等级,
--       武器染色方案=首席争霸[Q_门派编号[n]].武器染色方案,
--       武器染色组=首席争霸[Q_门派编号[n]].武器染色组,
--       x=Q_首席弟子[Q_门派编号[n]].x,
--       y=Q_首席弟子[Q_门派编号[n]].y,
--       门派=Q_门派编号[n],
--       方向=Q_首席弟子[Q_门派编号[n]].方向,
--       地图编号=Q_首席弟子[Q_门派编号[n]].地图,
--       地图名称=Q_门派编号[n],
--       序列=n,
--       类型=999888,
--     }
--     地图处理类:添加单位(任务id)
--   end
-- end


function 任务处理类:加载首席单位()
          for k,v in pairs(Q_首席弟子) do
              地图处理类.NPC列表[v.地图][v.编号].名称 = 首席争霸[k].名称
              地图处理类.NPC列表[v.地图][v.编号].模型 = 首席争霸[k].模型
              地图处理类.NPC列表[v.地图][v.编号].染色组=首席争霸[k].染色组
              地图处理类.NPC列表[v.地图][v.编号].染色方案=首席争霸[k].染色方案
              地图处理类.NPC列表[v.地图][v.编号].锦衣 = nil
              地图处理类.NPC列表[v.地图][v.编号].武器 = nil
              地图处理类.NPC列表[v.地图][v.编号].武器染色组 = nil
              地图处理类.NPC列表[v.地图][v.编号].武器染色方案 = nil
              if type(首席争霸[k].锦衣) =="table" and 首席争霸[k].锦衣[1] and 首席争霸[k].锦衣[1].名称  then
                  地图处理类.NPC列表[v.地图][v.编号].锦衣 =首席争霸[k].锦衣[1].名称
              end
              if type(首席争霸[k].武器) =="string" then
                    地图处理类.NPC列表[v.地图][v.编号].武器 = 首席争霸[k].武器
              elseif type(首席争霸[k].武器) =="table" then
                      地图处理类.NPC列表[v.地图][v.编号].武器 = 首席争霸[k].武器.名称
                      if 首席争霸[k].武器.染色方案~=0 and 首席争霸[k].武器.染色组 and 首席争霸[k].武器.染色组~=0 and #首席争霸[k].武器.染色组>0 then
                          地图处理类.NPC列表[v.地图][v.编号].染色组 =首席争霸[k].武器.染色组
                          地图处理类.NPC列表[v.地图][v.编号].武器染色组 =首席争霸[k].武器.染色方案
                      end
              end
          end
end



function 任务处理类:刷出天降辰星()
          for i,v in ipairs(Q_天降辰星) do
              local 任务id = 取唯一识别码(16)
              任务数据[任务id]={
                  id=任务id,
                  进程= i,
                  起始=os.time(),
                  结束=7300,
                  名称=v.名称,
                  模型=v.模型,
                  变异=true,
                  x=v.x,
                  y=v.y,
                  地图编号=v.地图,
                  地图名称=取地图名称(v.地图),
                  类型=16
              }
              地图处理类:添加单位(任务id)
          end
          辰星数据={开启=true,时间=os.time(),队伍={},排行={}}
          广播消息({内容="#G/天降辰星活动已经开启,各位玩家可以前往长安城#R/游奕灵官#G/处参与此活动#32",频道="xt"})
          发送公告("#G/天降辰星活动已经开启,各位玩家可以前往长安城#R/游奕灵官#G/处参与此活动！")
end

function 任务处理类:结束天降辰星()
  辰星数据.开启=false
  for i in pairs(战斗准备类.战斗盒子) do
      if 战斗准备类.战斗盒子[i].战斗类型==100059 then
          战斗准备类.战斗盒子[i]:结束战斗处理(0,0,1)
      end
      if 战斗准备类.战斗盒子[i].战斗类型==100061 then
          战斗准备类.战斗盒子[i]:结束战斗处理(0,0,1)
      end
  end
  for i in pairs(任务数据) do
        if 任务数据[i].类型==16 then
           地图处理类:删除单位(任务数据[i].地图编号,任务数据[i].编号)
           任务数据[i]=nil
        elseif 任务数据[i].类型==18 then
           地图处理类:删除单位(任务数据[i].地图编号,任务数据[i].编号)
           任务数据[i]=nil
        end
  end
  for k,v in pairs(辰星数据) do
      if type(k)=="number" and 玩家数据[k] then
          if 玩家数据[k].角色:取任务(17) ~= 0 then
              玩家数据[k].角色:取消任务(玩家数据[k].角色:取任务(17))
              常规提示(k,"#Y/天降辰星活动已经结束,你的任务已经被系统取消！")
          end
          if 玩家数据[k].角色:取任务(18) ~= 0 then
              玩家数据[k].角色:取消任务(玩家数据[k].角色:取任务(18))
              常规提示(k,"#Y/天降辰星活动已经结束,你的任务已经被系统取消！")
          end
      end
  end
  local 排行 = {}
  for k,v in pairs(辰星数据.队伍) do
      if v.时间 then
          table.insert(排行,v)
      end
  end
  table.sort(排行,function(a,b) return a.时间<b.时间 end)
  辰星数据.排行 = {}
  for i=1,15 do
      if 排行[i] then
          for k,v in pairs(排行[i]) do
              if type(k)=="number" and 辰星数据[k] then
                  辰星数据[k].排名 = i
              end
          end
          辰星数据.排行[i]=排行[i].队长
      end
  end
  广播消息({内容="#G/天降辰星活动已经结束,排行数据已经刷新,请玩家前往灵官处查看！#32",频道="xt"})
  发送公告("#G/天降辰星活动已经结束,排行数据已经刷新,请玩家前往灵官处查看！")
end


function 任务处理类:天降辰星(id)
          if self:触发条件(id,69,17,"天降辰星",1) then--id,等级,任务,活动,队伍,人数
              return
          end
          local 任务id=取唯一识别码(17)
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=7200,
            玩家id=id,
            进程=1,
            类型=17
          }
          辰星数据.队伍[id]={开始=os.time(),队长=id}
          for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
              辰星数据[v]={
                          id=v,
                          队长=id,
                          名称=玩家数据[v].角色.数据.名称,
                          门派=玩家数据[v].角色.数据.门派,
                          等级=玩家数据[v].角色.数据.等级,
                        }
              辰星数据.队伍[id][v]=true
              添加活动次数(v,"天降辰星")
          end
          玩家数据[id].角色:添加任务(任务id,1,nil,{名称="游奕灵官",模型="天兵",内容=format("你成功领取了天降辰星任务，请立即前往寻找#Y/%s#W/接受考验。",Q_天降辰星[1].名称)})
end


function 任务处理类:九耀星君(id)
    local 任务id=取唯一识别码(18)
    local 地图范围={1501,1092,1193,1070,1142}
    local 地图=地图范围[取随机数(1,#地图范围)]
    local xy=地图处理类.地图坐标[地图]:取随机点()
    local 名称={"羲和","望舒","计都","罗侯","紫央","月柏"}
    任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=id,
        名称=名称[取随机数(1,#名称)],
        模型="天将",
        变异=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=18
    }
    地图处理类:添加单位(任务id)
    玩家数据[id].角色:添加任务(任务id,1)
end


function 任务处理类:刷新混世魔王()
  local 地图范围={1092,1070,1040,1226,1208,1501}
  local 刷新地图={}
  for n=1,#地图范围 do
    local 地图=地图范围[n]
    for i=1,4 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(388)
      local 造型="进阶蜃气妖"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        称谓="上古魔神",
        名称="混世魔王",
        模型=造型,
        行走开关=false,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=388
      }
      地图处理类:添加单位(任务id)
    end
    刷新地图[#刷新地图+1] = 取地图名称(地图)
  end
  广播消息({内容=format("#G上古魔神复苏！一波混世魔王#Y%s#G已经出现在#Y%s、%s、%s、%s、%s、%s#G场景内","混世魔王",刷新地图[1],刷新地图[2],刷新地图[3],刷新地图[4],刷新地图[5],刷新地图[6]),频道="xt"})
end


function 任务处理类:刷新桐人()
  local 地图范围={1005,1006}
  local 刷新地图={}
  for n=1,#地图范围 do
    local 地图=地图范围[n]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(386)
      local 造型="金铙僧"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="大雁塔扫地僧",
        模型=造型,
        行走开关=false,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=386
      }
      地图处理类:添加单位(任务id)
    end
    刷新地图[#刷新地图+1] = 取地图名称(地图)
  end
  广播消息({内容=format("#Y又到了大雁塔日常清理时间，#R%s#Y出来闹腾了 ，他们出现在了#G%s、%s#Y处，还请各位英雄侠士赶紧去降服它们。","铜人",刷新地图[1],刷新地图[2]),频道="xt"})
end


function 任务处理类:刷新魔化桐人()
  local 地图范围={1008,1090}
  local 刷新地图={}
  for n=1,#地图范围 do
    local 地图=地图范围[n]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(387)
      local 造型="进阶金铙僧"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="魔化铜人",
        模型=造型,
        行走开关=false,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=387
      }
      地图处理类:添加单位(任务id)
    end
    刷新地图[#刷新地图+1] = 取地图名称(地图)
  end
  广播消息({内容=format("#Y又到了大雁塔日常清理时间，#R%s#Y出来闹腾了 ，他们出现在了#G%s、%s#Y处，还请各位英雄侠士赶紧去降服它们。","魔化铜人",刷新地图[1],刷新地图[2]),频道="xt"})
end


function 任务处理类:刷出新十二生肖()
  local 地图范围={1506,1514,1193,1091}
  local 生肖模型 = {"超级神狗","超级神鸡","超级神猴","超级神羊","超级神马","超级神蛇","超级神龙","超级神兔","超级神虎","超级神牛","超级神鼠","超级神猪"}
  local 刷新地图={}
  for n=1,#地图范围 do
    local 地图=地图范围[n]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(385)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="生肖兽",
      模型=生肖模型[取随机数(1,#生肖模型)],
      x=xy.x,
      y=xy.y,
      行走开关=false,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=385
    }
    地图处理类:添加单位(任务id)
  end
  刷新地图[#刷新地图+1] = 取地图名称(地图)
end
广播消息({内容=format("#W一群生肖闪着金色光芒正在#G%s、%s、%s、%s#W场景中四处游玩，各位少侠可以去消灭他们#24。",刷新地图[1],刷新地图[2],刷新地图[3],刷新地图[4]),频道="xt"})
end


function 任务处理类:添加仙缘任务(id)
          if self:触发条件(id,69,374,"仙缘活动",1) then--id,等级,任务,活动,队伍,人数
              return
          end
          local 任务id=取唯一识别码(374)
          local 地图范围={1501,1193,1506}
          local 地图=地图范围[取随机数(1,#地图范围)]
          local 随机参数=取随机数()
          local 模型="剑侠客"
          local 名称 = "白衣酒仙"
          local xy=地图处理类.地图坐标[地图]:取随机点()
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=3600,
            玩家id=id,
            名称=名称,
            模型=模型,
            染色方案=2,
            染色组={[1]=3,[2]=3,[3]=4},
            武器="四法青云",
            武器等级=140,
            称谓="仙缘",
            x=xy.x,
            y=xy.y,
            地图编号=地图,
            地图名称=取地图名称(地图),
            类型=374
          }
          地图处理类:添加单位(任务id)
          for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
              if not 仙缘次数[v] then
                 仙缘次数[v] = 1
              else
                  仙缘次数[v] = 仙缘次数[v] + 1
              end
              if 仙缘次数[v]>10 then
                  仙缘次数[v]=10
              end
          end
          玩家数据[id].角色:添加任务(任务id,1,nil,{名称="书生",模型="男人_书生",内容=format("听说近日有#Y/%s#W/正在#G/%s(%s,%s)#W/处作恶，请立即前去查明真相。这些白衣酒仙中有不少为侠客所扮，所以请少侠多仔细分辨他们的身份。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})

end
function 任务处理类:添加一级仙缘任务(id)
  local 任务id=取唯一识别码(375)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=375
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加二级仙缘任务(id)
  local 任务id=取唯一识别码(376)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=376
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
 发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加三级仙缘任务(id)
  local 任务id=取唯一识别码(377)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=377
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加四级仙缘任务(id)
  local 任务id=取唯一识别码(378)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=378
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加五级仙缘任务(id)
  local 任务id=取唯一识别码(379)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=379
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加六级仙缘任务(id)
  local 任务id=取唯一识别码(380)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=380
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加七级仙缘任务(id)
  local 任务id=取唯一识别码(381)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=381
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加八级仙缘任务(id)
  local 任务id=取唯一识别码(382)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=382
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加九级仙缘任务(id)
  local 任务id=取唯一识别码(383)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=383
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end
function 任务处理类:添加十级仙缘任务(id)
  local 任务id=取唯一识别码(384)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="神秘老者"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    称谓="仙缘",
    模型="男人_老书生",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=384
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  发送数据(玩家数据[id].连接id,1501,{名称="神秘老者",模型="男人_老书生",对话=format("据可靠情报，现有#Y/%s#W/出现在#G/%s(%s,%s)#W/处，请你前往寻此仙缘。仙缘任务实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end



function 任务处理类:刷新世界BOSS(id)
  local 广播地图 = {}
  local 地图范围={1506,1193,1514,1091}
  local 等级=60
  local 地图=地图范围[取随机数(1,#地图范围)]
  广播地图[#广播地图+1] = 地图
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(206)
  local 造型="进阶黑山老妖"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    称谓="世界BOSS("..等级.."级)",
    名称="万年妖王",
    模型=造型,
    等级=等级,
    行走开关=true,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=206
  }

  地图处理类:添加单位(任务id)

  --广播消息({内容=format("#G妖门大开，魔王现世。世界BOSS#Y%s#G已经出现在#Y%s#G场景内",任务数据[任务id].名称,任务数据[任务id].地图名称),频道="xt"})
  local 地图范围={1506,1193,1514,1091}
  local 等级=100
  local 地图=地图范围[取随机数(1,#地图范围)]
  广播地图[#广播地图+1] = 地图
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(206)
  造型="进阶鬼将"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    称谓="世界BOSS("..等级.."级)",
    名称="万年鬼王",
    模型=造型,
    等级=等级,
    x=xy.x,
    y=xy.y,
    行走开关=true,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=206
  }
  地图处理类:添加单位(任务id)

 -- 广播消息({内容=format("#G妖门大开，魔王现世。世界BOSS#Y%s#G已经出现在#Y%s#G场景内",任务数据[任务id].名称,任务数据[任务id].地图名称),频道="xt"})
  local 地图范围={1506,1193,1514,1091}
  local 等级=150
  local 地图=地图范围[取随机数(1,#地图范围)]
  广播地图[#广播地图+1] = 地图
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(206)
  造型="进阶夜罗刹"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=3600,
    玩家id=id,
    称谓="世界BOSS("..等级.."级)",
    名称="罗刹王",
    模型=造型,
    等级=等级,
    x=xy.x,
    y=xy.y,
    行走开关=true,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=206
  }
  地图处理类:添加单位(任务id)
  广播消息({内容=format("#G妖门大开，魔王现世。世界BOSS#G已经出现在#Y%s,%s,%s#G场景内",取地图名称(广播地图[1]),取地图名称(广播地图[2]),取地图名称(广播地图[3])),频道="xt"})
end






function 任务处理类:刷出财神爷()
  local 地图范围={1193,1506,1091,1110,1514}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 提示名称=""
  local 数量=取随机数(5,10)
  local 地图=地图范围[取随机数(1,#地图范围)]
  for i=1,数量 do
    local 任务id=取唯一识别码(373)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="三界财神爷",
      模型="小毛头",
      x=xy.x,
      y=xy.y,
      行走开关=false,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=373
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W三界财神爷，携带大量宝物，跑到#G%s#W处躲藏，请各位少侠们，前去抓住他获得宝物。",取地图名称(地图)),频道="xt"})
end



function 任务处理类:刷出新冠状病毒()
  local 地图范围={1506,1514,1193,1091}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 地图=地图范围[取随机数(1,#地图范围)]
  for i=1,20 do
    local 任务id=取唯一识别码(355)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="新冠状病毒",
      模型="进阶混沌兽",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=355
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W新冠状病毒，身上带有大量毒素，跑到#G%s#W处躲藏，请各位少侠们，前去消灭它们。",取地图名称(地图)),频道="xt"})
end


function 任务处理类:刷新影青龙()
  local 地图范围={1501,1193}
  local 地图=地图范围[取随机数(1,#地图范围)]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(368)
      随机序列=随机序列+1
      local 造型="蛟龙"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="影青龙",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=368
      }
      地图处理类:添加单位(任务id)
    end
  广播消息({内容=format("#Y青龙虽已经被封印，但其残存的魂魄已经化为#R%s#Y来到了#G%s#Y等地作恶，如有能战胜者，必定会得到其身上的宝物。","影青龙",取地图名称(地图)),频道="xt"})
end



function 任务处理类:刷新影朱雀()
    local 地图范围={1040,1091}
    local 地图=地图范围[取随机数(1,#地图范围)]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(369)
      local 造型="凤凰"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="影朱雀",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=369
      }
      地图处理类:添加单位(任务id)
    end

  广播消息({内容=format("#Y朱雀虽已经被封印，但其残存的魂魄已经化为#R%s#Y来到了#G%s#Y等地作恶，如有能战胜者，必定会得到其身上的宝物。","影朱雀",取地图名称(地图)),频道="xt"})
end

function 任务处理类:刷新影白虎()
  local 地图范围={1514,1174}
  local 地图=地图范围[取随机数(1,#地图范围)]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(370)
      local 造型="老虎"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="影白虎",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=370
      }
      地图处理类:添加单位(任务id)
    end

  广播消息({内容=format("#Y白虎虽已经被封印，但其残存的魂魄已经化为#R%s#Y来到了#G%s#Y等地作恶，如有能战胜者，必定会得到其身上的宝物。","影白虎",取地图名称(地图)),频道="xt"})
end


function 任务处理类:刷新影玄武()
  local 地图范围={1218,1092}
  local 地图=地图范围[取随机数(1,#地图范围)]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(371)
      local 造型="进阶龙龟"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="影玄武",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=371
      }
      地图处理类:添加单位(任务id)
    end

  广播消息({内容=format("#Y玄武虽已经被封印，但其残存的魂魄已经化为#R%s#Y来到了#G%s#Y等地作恶，如有能战胜者，必定会得到其身上的宝物。","影玄武",取地图名称(地图)),频道="xt"})
end


function 任务处理类:刷新影麒麟()
  local 地图范围={1232,1920}
  local 地图=地图范围[取随机数(1,#地图范围)]
    for i=1,3 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(372)
      local 造型="超级麒麟"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3550,
        玩家id=id,
        名称="影麒麟",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=372
      }
      地图处理类:添加单位(任务id)
    end
  广播消息({内容=format("#Y麒麟虽已经被封印，但其残存的魂魄已经化为#R%s#Y来到了#G%s#Y等地作恶，如有能战胜者，必定会得到其身上的宝物。","影麒麟",取地图名称(地图)),频道="xt"})
end

function 任务处理类:刷出星官()
          local 随机地图={1070,1092,1040,1226,1208}
          local 地图=随机地图[取随机数(1,#随机地图)]
           --地图=1092
          for n=1,10 do
               local xy=地图处理类.地图坐标[地图]:取随机点()
               local 任务id=取唯一识别码(367)
               任务数据[任务id]={

               id=任务id,
               起始=os.time(),
               结束=1800,
               玩家id=0,
               行走开关=true,
               名称="星官",
               模型="进阶天兵",
               x=xy.x,
               y=xy.y,
               地图编号=地图,
               地图名称=取地图名称(地图),
               类型=367
              }
              地图处理类:添加单位(任务id)
           end
           广播消息({内容=format("#R三界六道、芸芸众生，玉皇大帝见人间笼罩着祥和之气，特派星官下凡至#G%s#R赐福，凡有机缘之人必将获得仙缘。",取地图名称(地图)),频道="xt"})
 end



function 任务处理类:刷出倔强青铜()
  local 数量=取随机数(5,10)
  for i=1,数量 do
    local 任务id=取唯一识别码(361)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="青铜",
      模型="强盗",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=361
    }
    地图处理类:添加单位(任务id)
  end
    广播消息({内容=format("#W/异世界青铜怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end


function 任务处理类:刷出秩序白银()
  local 数量=取随机数(4,8)
  for i=1,数量 do
    local 任务id=取唯一识别码(362)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="白银",
      模型="山贼",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=362
    }
    地图处理类:添加单位(任务id)
  end
    广播消息({内容=format("#W/异世界白银怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end


function 任务处理类:刷出荣耀黄金()
  local 数量=取随机数(3,6)
  for i=1,数量 do
    local 任务id=取唯一识别码(363)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="黄金",
      模型="天兵",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=363
    }
    地图处理类:添加单位(任务id)
  end
    广播消息({内容=format("#W/异世界黄金怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end

function 任务处理类:刷出永恒钻石()
  local 数量=取随机数(2,4)
  for i=1,数量 do
    local 任务id=取唯一识别码(364)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="铂金",
      模型="天将",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=364
    }
    地图处理类:添加单位(任务id)
  end
    广播消息({内容=format("#W/异世界铂金怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end



function 任务处理类:刷出至尊星耀()
  local 数量=取随机数(1,2)
  for i=1,数量 do
    local 任务id=取唯一识别码(365)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="星耀",
      模型="进阶大力金刚",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=365
    }
    地图处理类:添加单位(任务id)
  end
    广播消息({内容=format("#W/异世界星耀怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end

function 任务处理类:刷出最强王者()
    local 任务id=取唯一识别码(366)
    local xy=地图处理类.地图坐标[1070]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3550,
      玩家id=id,
      名称="王者",
      模型="进阶毗舍童子",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1070,
      地图名称=取地图名称(1070),
      类型=366
    }
    地图处理类:添加单位(任务id)
    广播消息({内容=format("#W/异世界王者怪物在#G/%s#W/到处捣乱，还请各位英雄侠士赶紧去征服他们。",取地图名称(1070)),频道="xt"})
end


function 任务处理类:刷出经验宝宝()
  local 数量=取随机数(5,20)
  for i=1,数量 do
    local 任务id=取唯一识别码(360)
    local xy=地图处理类.地图坐标[1501]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=1800,
      玩家id=id,
      名称="经验宝宝",
      模型="海星",
      x=xy.x,
      y=xy.y,
      行走开关=true,
      地图编号=1501,
      地图名称=取地图名称(1501),
      类型=360
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#W一群经验小萌新出现在了#G%s#W处捣乱，还请各位英雄侠士赶紧去降服它们。",取地图名称(1501)),频道="xt"})
end






function 任务处理类:刷出天降灵猴()
  local 地图范围={1506,1193,1110,1514,1174,1210,1110}
  for i=1,#地图范围 do
    local 临时数量=取随机数(5,15)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(359)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=1800,
        玩家id=0,
        名称="下凡的灵猴",
        模型="超级六耳猕猴",
        称谓="天降灵猴",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        显示饰品=true,
        类型=359
      }
      地图处理类:添加单位(任务id)
    end
  end
  广播消息({内容=format("#W/雷霆一劈，天空中飞来许多灵猴，他们出现在了#Y/东海湾、江南野外、花果山、大唐国境#w/处捣乱，还请各位英雄侠土赶紧去降服它们。#46"),频道="xt"})
end




function 任务处理类:刷出四墓灵鼠()
          local 名称范围={"通天鼠","疾风鼠","烈焰鼠","噬金鼠","天机鼠"}
          for i,v in ipairs(名称范围) do
              local xy=地图处理类.地图坐标[1110]:取随机点()
              local 任务id=取唯一识别码(318)
              任务数据[任务id]={
                id=任务id,
                起始=os.time(),
                结束=1800,
                玩家id=id,
                名称=v,
                模型="鼠先锋",
                行走开关=true,
                x=xy.x,
                y=xy.y,
                地图编号=1110,
                地图名称=取地图名称(1110),
                类型=318
              }
              地图处理类:添加单位(任务id)
          end
          广播消息({内容=format("#G瞬间地震，一波#Y%s#G已经出现在#Y%s#G场景内!各位请去击杀!","四墓灵鼠",取地图名称(1110)),频道="xt"})
end




function 任务处理类:开启车迟副本(id)
  if self:触发条件(id,69,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 检测通过 =true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
      local 临时id=队伍数据[队伍id].成员数据[n]
      if 玩家数据[临时id].角色:取任务(130)~=0 then
         常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
         检测通过 = false
      end
      if 副本数据.车迟斗法.完成[临时id] then
          if os.date("%m", os.time())==os.date("%m", 副本数据.车迟斗法.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.车迟斗法.完成[临时id]) then
              常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
              检测通过 = false
          else
              副本数据.车迟斗法.进行中[临时id]=nil
              副本数据.车迟斗法.完成[临时id]=nil
          end
      end
      if 副本数据.车迟斗法.进行中[临时id] then
         常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
         检测通过 = false
      end
  end

  if 检测通过 then
      local 任务id=取唯一识别码(130)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        任务组 = DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
        副本id=id,
        类型=130,
      }
      副本数据.车迟斗法.进行[id]={进程=1}
      副本数据.车迟斗法.进行[id].副本任务id = 任务id
      副本数据.车迟斗法.进行[id].车迟木材=0
      local 队伍id=玩家数据[id].队伍
      self:设置车迟斗法副本(id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
          副本数据.车迟斗法.进行中[v]=true
      end
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了车迟斗法副本")
  end
end

function 任务处理类:设置车迟斗法副本(id,副本任务id)
  if 副本数据.车迟斗法.进行[id]==nil then
    return
  end
  if 副本数据.车迟斗法.进行[id].副本任务id==nil then
      常规提示(id,"#Y副本任务失败！")
      return
  end
  local fbid = 副本数据.车迟斗法.进行[id].副本任务id
  local 随机名称={}
  local 随机模型={}
  if 副本数据.车迟斗法.进行[id].进程==1 then
    for n=1,1 do
      local 任务id=取唯一识别码(131)
      local 地图=6021
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="有个道士",
        模型="男人_道士",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=131,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
      end
  elseif 副本数据.车迟斗法.进行[id].进程==2 then
    local 任务id=取唯一识别码(132)
    local 地图=6021
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id={},
      队伍组=任务数据[fbid].队伍组,
      名称="有个和尚",
      模型="和尚2",
      x=49,
      y=111,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=132,
      真实副本id = fbid
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.车迟斗法.进行[id].进程==3 then
    副本数据.车迟斗法.进行[id].车迟贡品=0
    随机名称={"贡品包子","贡品苹果","贡品桃子","贡品寿桃","贡品香梨","贡品香蕉","贡品烧饼"}
    for n=1,15 do
      local 任务id=取唯一识别码(133)
      local 地图=6021
      local xy=地图处理类.地图坐标[6021]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称=随机名称[取随机数(1,#随机名称)],
        模型="泡泡",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        副本id=id,
        地图编号=6021,
        地图名称=取地图名称(6021),
        类型=133,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.车迟斗法.进行[id].进程==4 then
    名称={"道德天尊","元始天尊","灵宝天尊"}
    副本数据.车迟斗法.进行[id].序列={false,false,false}
    for n=1,3 do
      local 任务id=取唯一识别码(134)
      local 地图=6022
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称=名称[n],
        模型="大力金刚",
        变异=true,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        副本id=id,
        序列=n,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=134,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.车迟斗法.进行[id].进程==5 then
    local 任务id=取唯一识别码(135)
    local 地图=6022
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id={},
      队伍组=任务数据[fbid].队伍组,
      名称="有个和尚",
      模型="和尚2",
      x=22,
      y=40,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=135,
      真实副本id = fbid
    }
    地图处理类:添加单位(任务id)
  elseif  副本数据.车迟斗法.进行[id].进程==6 then
    local 任务id=取唯一识别码(136)
    local 地图=6023
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id={},
      队伍组=任务数据[fbid].队伍组,
      名称="有个神仙",
      模型="和尚2",
      x=16,
      y=145,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=136,
      真实副本id = fbid
    }
    地图处理类:添加单位(任务id)
  elseif  副本数据.车迟斗法.进行[id].进程==7 then
    if 取随机数()<=50 then
      随机名称={"雷公","电母","雨师"}
      随机模型={"雷鸟人","星灵仙子","雨师"}
      副本数据.车迟斗法.进行[id].序列={false,false,false}
      for i=1,3 do
          local 任务id=取唯一识别码(137)
          local 地图=6023
          local xy=地图处理类.地图坐标[地图]:取随机点()
          任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id={},
          队伍组=任务数据[fbid].队伍组,
          名称=随机名称[i],
          模型=随机模型[i],
          变异=true,
          x=xy.x,
          y=xy.y,
          序列=i,
          副本id=id,
          地图编号=地图,
          地图名称=取地图名称(地图),
          类型=137,
          真实副本id = fbid
        }
        地图处理类:添加单位(任务id)
      end
    else
      随机名称={"你不动","你不动","我不动"}
      副本数据.车迟斗法.进行[id].不动=true
      副本数据.车迟斗法.进行[id].序列={false,false,false}
      for i=1,3 do
        local 任务id=取唯一识别码(138)
        local 地图=6023
        local xy=地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id={},
          队伍组=任务数据[fbid].队伍组,
          名称=随机名称[i],
          模型="净瓶女娲",
          变异=true,
          x=xy.x,
          y=xy.y,
          序列=i,
          副本id=id,
          地图编号=地图,
          地图名称=取地图名称(地图),
          类型=138,
          真实副本id = fbid
        }
        地图处理类:添加单位(任务id)
      end
    end
  elseif  副本数据.车迟斗法.进行[id].进程==8 then
    local 任务id=取唯一识别码(139)
    local 地图=6023
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id={},
      队伍组=任务数据[fbid].队伍组,
      名称="电母",
      模型="星灵仙子",
      变异=true,
      x=99,
      y=74,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=139,
      真实副本id = fbid
    }
    地图处理类:添加单位(任务id)
  elseif  副本数据.车迟斗法.进行[id].进程==9 then
    local 任务id=取唯一识别码(140)
    local 地图=6023
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id={},
      队伍组=任务数据[fbid].队伍组,
      名称="齐天大圣",
      模型="孙悟空",
      变异=true,
      x=85,
      y=69,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=140,
      真实副本id = fbid
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.车迟斗法.进行[id].进程==10 then
    随机名称={"羊力","鹿力","虎力"}
    随机模型={"炎魔神","吸血鬼","噬天虎"}
    for i=1,3 do
        local 任务id=取唯一识别码(141)
        local 地图=6023
        local xy=地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称=随机名称[i],
        模型=随机模型[i],
        变异=true,
        x=xy.x,
        y=xy.y,
        序列=i,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=141,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
    if 取随机数()<=33 then
      副本数据.车迟斗法.进行[id].真假={true,false,false}
    elseif 取随机数()<=66 then
      副本数据.车迟斗法.进行[id].真假={false,true,false}
    else
      副本数据.车迟斗法.进行[id].真假={false,false,true}
    end
  elseif 副本数据.车迟斗法.进行[id].进程==11 then
    local 宝箱数量 = 0
    for i,v in pairs(地图处理类.地图玩家[6023]) do
      if  玩家数据[i] and 玩家数据[i].角色:取任务(130)~=0 and 玩家数据[i].角色:取任务(130) == 副本任务id then
        local 任务id=取唯一识别码(142)
        local 地图=6023
        local xy=地图处理类.地图坐标[地图]:取随机点()
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id={},
          队伍组=任务数据[fbid].队伍组,
          名称=玩家数据[i].角色.数据.名称.."的宝箱",
          模型="宝箱",
          x=xy.x,
          y=xy.y,
          副本id=id,
          绑定id=i,
          地图编号=地图,
          地图名称=取地图名称(地图),
          类型=142,
          真实副本id = fbid,
          副本任务id=副本任务id
        }
        宝箱数量 = 宝箱数量 + 1
        地图处理类:添加单位(任务id)
      end
    end
    副本数据.车迟斗法.进行[id].宝箱数量 = 宝箱数量
  elseif 副本数据.车迟斗法.进行[id].进程==12 then
    for i=1,10 do
      local 任务id=取唯一识别码(143)
      local 地图=6023
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=任务数据[fbid].队伍组,
        名称="小宝箱",
        模型="宝箱",
        变异=true,
        x=xy.x,
        y=xy.y,
        序列=i,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=143,
        真实副本id = fbid
      }
      地图处理类:添加单位(任务id)
    end
    任务数据[副本任务id].起始=os.time()
    任务数据[副本任务id].结束 = 600
    地图处理类:当前消息广播2(6023,format("#G副本将于10分钟后正式结束,请及时找寻小宝箱哟！！！"),副本任务id)
  end
end


function 任务处理类:侠义任务(id)
  if 玩家数据[id].角色:取任务(346) ~= 0 then
      添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
      return
  end
  local 任务物品 = {"魔兽要诀","召唤兽内丹","怪物卡片","物品","鬼谷子"}
  local 任务id=取唯一识别码(346)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    玩家id=id,
    物品类型="物品",
    物品="金柳露",
    类型=346,
    完成=false
  }
  local 物品目标 = 取随机数(1,#任务物品)
  local 所需物品 = 任务物品[物品目标]
  任务数据[任务id].物品类型 = 所需物品
  if 所需物品 == "魔兽要诀" then
    任务数据[任务id].物品 = 取低级要诀()
  elseif 所需物品 == "召唤兽内丹" then
    任务数据[任务id].物品 = 取内丹()
  elseif 所需物品 == "怪物卡片" then
    local 数量 = 取随机数(1,8)
    任务数据[任务id].物品 = 变身卡范围[数量][取随机数(1,#变身卡范围[数量])]
  elseif 所需物品 == "鬼谷子" then
    local 阵法数据 = {"天覆阵","龙飞阵","风扬阵","虎翼阵","云垂阵","鸟翔阵","地载阵","蛇蟠阵","鹰啸阵","雷绝阵"}
    任务数据[任务id].物品 = 阵法数据[取随机数(1,#阵法数据)]
  else
    local 随机 = 取随机数(1,4)
    if 随机 == 1 then
      local 强化数据 = {"青龙石","白虎石","玄武石","朱雀石"}
      任务数据[任务id].物品 = 强化数据[取随机数(1,#强化数据)]
    elseif 随机 == 2 then
      任务数据[任务id].物品 = "九转金丹"
    elseif 随机 == 3 then
      任务数据[任务id].物品 = "修炼果"
    else
      任务数据[任务id].物品 = "金柳露"
    end
  end
  if 玩家数据[id].角色.数据.侠义次数==nil then
    玩家数据[id].角色.数据.侠义次数=1
  end
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,39)
  常规提示(id,"#Y/你获得了侠义任务")
  发送数据(玩家数据[id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=format("最近缺少#G/%s#R/%s#W，麻烦少侠帮我找来。",任务数据[任务id].物品类型,任务数据[任务id].物品)})
end

function 任务处理类:完成侠义任务(任务id,id)
  if 任务数据[任务id]==nil then
    return
  end
    玩家数据[id].角色:自定义银子添加("侠义任务",1)
     玩家数据[id].角色:添加活跃积分(1,"侠义任务",1)
  if 玩家数据[id].角色.数据.侠义次数==10 then
     玩家数据[id].角色.数据.侠义次数=0

     local 获得物品={}
     for i=1,#自定义数据.侠义任务 do
         if 取随机数()<=自定义数据.侠义任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.侠义任务[i]
         end
     end
     获得物品=删除重复(获得物品)
     if 获得物品~=nil then
        local 取编号=取随机数(1,#获得物品)
        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
           玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
        end
     end
  end
  玩家数据[id].角色.数据.侠义次数=玩家数据[id].角色.数据.侠义次数+1
  玩家数据[id].角色:取消任务(任务id)
  任务数据[任务id]=nil
  --常规提示(id,"#Y/完成侠义任务，获得了#R1#Y点侠义积分，当前侠义积分为：#R"..玩家数据[id].角色.数据.侠义积分)
end

function 任务处理类:福利宝箱(id)
  local 地图范围={1003,1506,1501,1876}
  for i=1,#地图范围 do
    local 地图=地图范围[i]
    for n=1,10 do
      local 任务id=取唯一识别码(347)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=300,
        玩家id=0,
        名称="福利宝箱",
        模型="宝箱",
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=347
      }
       地图处理类:添加单位(任务id)
    end
  end

  广播消息({内容=format("#G天降福缘、一大批福利宝箱掉落在桃源村、东海湾、建邺城、南岭山，请有缘人尽快去拾取。".."#"..取随机数(1,110)),频道="xt"})
end



function 任务处理类:倾国倾城()
  local 地图范围={1876,1041,1235}
  for i=1,#地图范围 do
    local 临时数量=取随机数(10,10)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(348)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围={"画魂"}
      local 模型名称范围={"貂蝉","甄宓","小乔","大乔","蔡文姬","王昭君","妲己","李清照","杨玉环","陈圆圆"}
      local 名称模型=模型名称范围[取随机数(1,#模型名称范围)]
      local 模型=模型范围[取随机数(1,#模型范围)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=0,
        名称=名称模型,
        模型=模型,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        变异=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        显示饰品=true,
        类型=348
      }
      地图处理类:添加单位(任务id)
    end
  end

  广播消息({内容=format("#G听附近的村民说，南岭山、子母河、丝绸之路附近传来了美妙动听的歌声，有兴趣的少侠可以前往一探究竟。"),频道="xt"})
end

function 任务处理类:美食专家()
  local 地图范围={1003,1506,1501,1092}
  for i=1,#地图范围 do
    local 临时数量=取随机数(20,20)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(349)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围={"赌徒","山贼","强盗"}
      local 模型名称范围={"贪吃的食客"}
      local 名称模型=模型名称范围[取随机数(1,#模型名称范围)]
      local 模型=模型范围[取随机数(1,#模型范围)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=0,
        名称=名称模型,
        模型=模型,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        显示饰品=true,
        类型=349
      }
      地图处理类:添加单位(任务id)
    end
  end
  广播消息({内容=format("#G一群贪吃的食客在各地抢掠美食，请各位少侠赶紧前去阻止。"),频道="xt"})
end








function 任务处理类:添加贼王的线索任务(id)
  local 任务id=取唯一识别码(350)
  local 地图范围={1501,1092,1208,1070,1040,1226}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 名称="贼王"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=7200,
    玩家id=id,
    名称=名称,
    模型="吸血鬼",
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=350
  }
  玩家数据[id].角色:添加任务(任务id)
  发送数据(玩家数据[id].连接id,1501,{名称="冷无情",模型="男人_衙役",对话=format("据可靠情报，现有#Y/%s#W/逃到了#G/%s(%s,%s)#W/处，请你前往将其缉拿。此通缉犯实力不容小窥，请尽量寻找一些高人前来协助。",任务数据[任务id].名称,任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)})
  地图处理类:添加单位(任务id)
end



function 任务处理类:貔貅的羁绊()
  local 地图范围={1003,1506,1876,1193,1514}
  for i=1,#地图范围 do
    local 临时数量=取随机数(7,7)
    local 地图=地图范围[i]
    for n=1,临时数量 do
      local 任务id=取唯一识别码(351)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 模型范围={"帮派妖兽"}
      local 模型名称范围={"貔貅"}
      local 名称模型=模型名称范围[取随机数(1,#模型名称范围)]
      local 模型=模型范围[取随机数(1,#模型范围)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=0,
        名称=名称模型,
        模型=模型,
        x=xy.x,
        y=xy.y,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        显示饰品=true,
        类型=351
      }
      地图处理类:添加单位(任务id)
    end
  end
  广播消息({内容=format("#G据说林子里出现里一堆野兽，正在袭扰村民，有胆量的少侠请帮忙前往平乱。"),频道="xt"})
end



function 任务处理类:开启水陆副本(id)
  if self:触发条件(id,69,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
    local 检测通过 =true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]

     if 玩家数据[临时id].角色:取任务(150)~=0 then
       常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
       检测通过 = false
    end
    if 副本数据.水陆大会.完成[临时id] then
        if os.date("%m", os.time())==os.date("%m", 副本数据.水陆大会.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.水陆大会.完成[临时id]) then
            常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
            检测通过 = false
        else
            副本数据.水陆大会.进行中[临时id]=nil
            副本数据.水陆大会.完成[临时id]=nil
        end
    end
    if 副本数据.水陆大会.进行中[临时id] then
       常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
       检测通过 = false
    end

  end

  if 检测通过 then
      local 任务id=取唯一识别码(150)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
        装潢=0,
        邀请=0,
        副本id=id,
        类型=150
      }
      副本数据.水陆大会.进行[id]={进程=1}
      副本数据.水陆大会.进行[id].副本任务id = 任务id
      self:设置水陆大会副本(id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
          副本数据.水陆大会.进行中[v]=true
      end
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了水陆大会副本")
  end
end

function 任务处理类:设置水陆大会副本(id)
  local 地图=0
  local x坐标=0
  local y坐标=0
  local 方向=0
  if 副本数据.水陆大会.进行[id]==nil then
    return
  end
  if 副本数据.水陆大会.进行[id].副本任务id==nil then
      常规提示(id,"#Y副本任务失败！")
      return
  end
  local fbid = 副本数据.水陆大会.进行[id].副本任务id
  local 随机名称={}
  local 随机模型={}
  if 副本数据.水陆大会.进行[id].毒虫触发==nil and 副本数据.水陆大会.进行[id].进程>=99 then --毒虫不触发开了
    --if 副本数据.水陆大会.进行[id].毒虫触发==nil and 副本数据.水陆大会.进行[id].进程>=3 and 副本数据.水陆大会.进行[id].进程<=7 and 取随机数()>=90 then
    local 地图范围={6024}
    local 地图=地图范围[取随机数(1,#地图范围)]
    副本数据.水陆大会.进行[id].毒虫触发=true
    for i=1,6 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      local 任务id=取唯一识别码(354)
      local 造型="海毛虫"
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=600,
        玩家id=id,
        名称="毒虫",
        模型=造型,
        变异=true,
        副本id=id,
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=354,
      }
      地图处理类:添加单位(任务id)
    end
    广播队伍消息(id,"#Y/水陆道场刷出了一群毒虫，少侠赶快去消灭他们！")
  end
  if 副本数据.水陆大会.进行[id].进程==1 then
    副本数据.水陆大会.进行[id].邀请=0
    副本数据.水陆大会.进行[id].装潢=0
    地图 = 6024
    local 任务id=取唯一识别码(151)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id=id,
      名称="蟠桃树",
      模型="桃树",
      x=110,
      y=12,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=151
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.水陆大会.进行[id].进程==2 then
    随机名称={"玄奘法师","唐太宗","小沙弥","魏征","殷丞相","杜如晦","尉迟敬德","程咬金","秦琼"}
    随机模型={"唐僧","皇帝","男人_胖和尚","考官2","考官2","考官2","秦琼","程咬金","秦琼"}
    for i=1,#随机模型 do
      任务id=取唯一识别码(152)
      地图=6024
      local 造型=随机模型[i]
      if 随机名称[i]=="玄奘法师" then
        x坐标=56
        y坐标=57
        方向=1
      elseif 随机名称[i]=="唐太宗" then
        x坐标=48
        y坐标=64
        方向=3
      elseif 随机名称[i]=="小沙弥" then
        x坐标=58
        y坐标=65
        方向=1
      elseif 随机名称[i]=="魏征" then
        x坐标=43
        y坐标=64
        方向=0
      elseif 随机名称[i]=="殷丞相" then
        x坐标=40
        y坐标=66
        方向=0
      elseif 随机名称[i]=="杜如晦" then
        x坐标=39
        y坐标=68
        方向=0
      elseif 随机名称[i]=="尉迟敬德" then
        x坐标=53
        y坐标=68
        方向=2
      elseif 随机名称[i]=="程咬金" then
        x坐标=50
        y坐标=70
        方向=2
      elseif 随机名称[i]=="秦琼" then
        x坐标=46
        y坐标=71
        方向=2
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=x坐标,
        y=y坐标,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=152,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.水陆大会.进行[id].进程==3 then
    地图 = 6024
    任务id=取唯一识别码(153)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id=id,
      名称="受伤的程咬金",
      模型="程咬金",
      x=52,
      y=66,
      方向=1,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=153,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.水陆大会.进行[id].进程==4 then---------------------------
    --地图 = 6024
    -- 随机名称={"翼虎将军","蝰蛇将军"}
    -- 随机模型={"噬天虎","律法女娲"}
    副本数据.水陆大会.进行[id].翼虎=false
    副本数据.水陆大会.进行[id].翼虎击败=false
    副本数据.水陆大会.进行[id].蝰蛇=false
    副本数据.水陆大会.进行[id].蝰蛇击败=false
    -- for i=1,#随机模型 do
    --     if 随机名称[i]=="翼虎将军" then
    --         x坐标=113
    --         y坐标=12
    --         方向=1
    --     else
    --         x坐标=122
    --         y坐标=17
    --         方向=1
    --     end
    --     任务id=id.."_154_"..os.time().."_"..随机序列.."_"..取随机数(88,99999999)..i
    --     任务数据[任务id]={
    --         id=任务id,
    --         起始=os.time(),
    --         结束=999999,
    --         玩家id=id,
    --         名称=随机名称[i],
    --         模型=随机模型[i],
    --         x=x坐标,
    --         y=y坐标,
    --         方向=方向,
    --         副本id=id,
    --         地图编号=地图,
    --         地图名称=取地图名称(地图),
    --         类型=154,
    --       }
    --     地图处理类:添加单位(任务id)
    -- end
  elseif 副本数据.水陆大会.进行[id].进程==5 then
    local 任务id = 取唯一识别码(155)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=600,
      玩家id=id,
      类型=155
    }
    local 水陆大会邀请人={
       [1]={名称="代笔师爷",x=483,y=125}
      ,[2]={名称="瓜农",x=478,y=119}
      ,[3]={名称="绣娘",x=459,y=162}
      ,[4]={名称="喜轿轿夫",x=483,y=147}
      ,[5]={名称="卖艺者",x=430,y=182}
      ,[6]={名称="乞丐乙",x=455,y=184}
      ,[7]={名称="阿咪",x=470,y=192}
      ,[8]={名称="马商",x=407,y=211}
      ,[9]={名称="小顽童",x=401,y=203}
      ,[10]={名称="卖花童",x=420,y=219}
      ,[11]={名称="丫鬟",x=424,y=222}
      ,[12]={名称="富家小姐",x=431,y=225}
      ,[13]={名称="卖鱼人",x=442,y=229}
      ,[14]={名称="面点师傅",x=443,y=223}
      ,[15]={名称="针线娘子",x=464,y=240}
      ,[16]={名称="樵夫",x=462,y=262}
      ,[17]={名称="大财主",x=427,y=228}
      ,[18]={名称="曾衙役",x=421,y=225}
      ,[19]={名称="布陈太医",x=370,y=243}
      ,[20]={名称="游方郎中",x=389,y=234}
    }

    local 序列=取随机数(1,#水陆大会邀请人)
    local 邀请人1=水陆大会邀请人[序列].名称
    table.remove(水陆大会邀请人,序列)
    local 邀请人2=水陆大会邀请人[取随机数(1,#水陆大会邀请人)].名称
    任务数据[任务id].人物地图=6025
    副本数据.水陆大会.进行[id].人物={[1]={名称=邀请人1,找到=false},[2]={名称=邀请人2,找到=false}}
  elseif 副本数据.水陆大会.进行[id].进程==6 then
      地图 = 6024
      任务id=取唯一识别码(156)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="土地公公",
        模型="男人_土地",
        x=116,
        y=11,
        方向=1,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=156,
      }
      地图处理类:添加单位(任务id)
  elseif 副本数据.水陆大会.进行[id].进程==7 then
    副本数据.水陆大会.进行[id].击败小妖=0
    副本数据.水陆大会.进行[id].击败头领=0
    副本数据.水陆大会.进行[id].击败将军=0
    随机名称={"巡山小妖","巡山小妖","巡山小妖","上古妖兽头领","妖将军"}
    随机模型={"鼠先锋","鼠先锋","鼠先锋","地狱战神","野猪精"}
    for i=1,#随机模型 do
      任务id=取唯一识别码(157)
      地图=6026
      local 造型=随机模型[i]
      if 随机名称[i]=="巡山小妖" then
        x坐标=取随机数(23,46)
        y坐标=取随机数(80,87)
      elseif 随机名称[i]=="上古妖兽头领" then
        x坐标=55
        y坐标=53
      elseif 随机名称[i]=="妖将军" then
        x坐标=16
        y坐标=43
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=x坐标,
        y=y坐标,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=157,
      }
      地图处理类:添加单位(任务id)
    end
    随机名称={"上古妖兽头领","妖将军"}
    随机模型={"地狱战神","野猪精"}
    for i=1,#随机模型 do
      任务id=取唯一识别码(157)
      地图=6026
      local 造型=随机模型[i]
      if 随机名称[i]=="上古妖兽头领" then
        x坐标=16
        y坐标=67
      elseif 随机名称[i]=="妖将军" then
        x坐标=26
        y坐标=12
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=x坐标,
        y=y坐标,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=157,
      }
      地图处理类:添加单位(任务id)
    end
    随机名称={"魑魅","魍魉"}
    随机模型={"帮派妖兽","帮派妖兽"}
    for i=1,#随机模型 do
      任务id=取唯一识别码(157)
      地图=6026
      local 造型=随机模型[i]
      if 随机名称[i]=="魑魅" then
        x坐标=40
        y坐标=16
        方向=1
      elseif 随机名称[i]=="魍魉" then
        x坐标=47
        y坐标=20
        方向=1
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=x坐标,
        y=y坐标,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=157,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.水陆大会.进行[id].进程==8 then
    随机名称={"唐太宗","玄奘法师"}
    随机模型={"皇帝","唐僧"}
    for i=1,#随机模型 do
      任务id=取唯一识别码(158)
      地图=6026
      local 造型=随机模型[i]
      if 随机名称[i]=="唐太宗" then
        x坐标=40
        y坐标=20
        方向=0
      elseif 随机名称[i]=="玄奘法师" then
        x坐标=47
        y坐标=20
        方向=1
      end
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=x坐标,
        y=y坐标,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=158,
      }
      地图处理类:添加单位(任务id)
    end
    发送数据(玩家数据[id].连接id,3532)
  end
end

function 任务处理类:驱逐泼猴(id)
  if 玩家数据[id].角色:取任务(352) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  elseif 玩家数据[id].队伍 ~= 0 then
    添加最后对话(id,"组队状态下无法接任务！")
    return
  end
  local 地图范围={6024}
  local 地图=地图范围[取随机数(1,#地图范围)]
  local xy=地图处理类.地图坐标[地图]:取随机点()
  xy.x = 取随机数(87,125)
  xy.y = 取随机数(3,40)
  local 任务id=取唯一识别码(352)
  local 造型="长眉灵猴"
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=600,
    玩家id=id,
    称谓="妄空是猪",
    名称="妄空泼猴",
    模型=造型,
    行走开关=true,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=352,
    副本id = 任务数据[玩家数据[id].角色:取任务(150)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  地图处理类:添加单位(任务id)
end

function 任务处理类:添加水陆邀请任务(id)
  if 玩家数据[id].角色:取任务(353) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  elseif 玩家数据[id].队伍 ~= 0 then
    添加最后对话(id,"组队状态下无法接任务！")
    return
  end
  local 任务id = 取唯一识别码(353)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=600,
    玩家id=id,
    类型=353
  }
  local 序列=取随机数(1,#Q_水陆大会邀请)
  任务数据[任务id].人物=Q_水陆大会邀请[序列].名称
  任务数据[任务id].x=Q_水陆大会邀请[序列].x
  任务数据[任务id].y=Q_水陆大会邀请[序列].y
  任务数据[任务id].人物地图=6025
  添加最后对话(id,format("穿过道场前门，到繁华京城给#R%s（%s，%s）#W送上邀帖。",任务数据[任务id].人物,任务数据[任务id].x,任务数据[任务id].y))
  玩家数据[id].角色:添加任务(任务id)
end

function 任务处理类:开启通天河副本(id)
  if self:触发条件(id,69,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 检测通过 = true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do

    local 临时id=队伍数据[队伍id].成员数据[n]
     if 玩家数据[临时id].角色:取任务(160)~=0 then
       常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
       检测通过 = false
    end
    if 副本数据.通天河.完成[临时id] then
        if os.date("%m", os.time())==os.date("%m", 副本数据.通天河.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.通天河.完成[临时id]) then
            常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
            检测通过 = false
        else
            副本数据.通天河.进行中[临时id]=nil
            副本数据.通天河.完成[临时id]=nil
        end
    end
    if 副本数据.通天河.进行中[临时id] then
       常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
       检测通过 = false
    end

  end

  if 检测通过 then
      local 任务id=取唯一识别码(160)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
        副本id=id,
        类型=160
      }
      副本数据.通天河.进行[id]={进程=1}
      副本数据.通天河.进行[id].副本任务id = 任务id
      副本数据.通天河.进行[id].通天答题=0
      副本数据.通天河.进行[id].通天取消=false
      self:设置通天河副本(id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
          副本数据.通天河.进行中[v]=true
      end
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了通天河副本")
  end
end

function 任务处理类:设置通天河副本(id)
  local x坐标=0
  local y坐标=0
  local 方向=0
  if 副本数据.通天河.进行[id]==nil then
    return
  end
  if 副本数据.通天河.进行[id].副本任务id==nil then
      常规提示(id,"#Y副本任务失败！")
      return
  end
  local fbid = 副本数据.通天河.进行[id].副本任务id
  local 随机名称={}
  local 随机模型={}
  if 副本数据.通天河.进行[id].进程==1 then
    随机名称={"丫丫","毛头","明明","豆包","嘟嘟"}
    随机模型={"小丫丫","小毛头"}
    for i=1,#随机名称 do
      local xy=地图处理类.地图坐标[6027]:取随机点()
      local 任务id=取唯一识别码(161)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[取随机数(1,#随机名称)],
        模型=随机模型[取随机数(1,#随机模型)],
        x=118,
        y=174,
        副本id=id,
        行走开关=true,
        地图编号=6027,
        地图名称=取地图名称(6027),
        类型=161
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.通天河.进行[id].进程==2 then
    随机名称={"一秤金","陈关保"}
    随机模型={"小丫丫","小毛头"}
    副本数据.通天河.进行[id].变身次数=0
    for i=1,#随机模型 do
      local 任务id=取唯一识别码(162)
      local xy=地图处理类.地图坐标[6027]:取随机点()
      local 造型=随机模型[i]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=造型,
        x=118,
        y=174,
        方向=方向,
        副本id=id,
        行走开关=true,
        地图编号=6027,
        地图名称=取地图名称(6027),
        类型=162,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.通天河.进行[id].进程==3 then
    local 任务id=取唯一识别码(163)
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7200,
      玩家id=id,
      名称="灵灯",
      模型="海星",
      x=23,
      y=176,
      方向=1,
      副本id=id,
      地图编号=6027,
      地图名称=取地图名称(6027),
      类型=163,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.通天河.进行[id].进程==4 then
          local 任务id=取唯一识别码(164)
          任务数据[任务id]={
            id=任务id,
            起始=os.time(),
            结束=7200,
            玩家id=id,
            名称="唐僧",
            模型="唐僧",
            x=118,
            y=174,
            方向=方向,
            副本id=id,
            地图编号=6027,
            地图名称=取地图名称(6027),
            类型=164,
          }
          地图处理类:添加单位(任务id)
  elseif 副本数据.通天河.进行[id].进程==5 then
    副本数据.通天河.进行[id].河妖=0
    随机模型={"虾兵","蟹将","蚌精","鲛人","碧水夜叉"}
    for i=1,5 do
      local 任务id=取唯一识别码(165)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="河妖",
        模型=随机模型[取随机数(1,#随机模型)],
        x=118,
        y=174,
        方向=方向,
        副本id=id,
        行走开关=true,
        地图编号=6027,
        地图名称=取地图名称(6027),
        类型=165,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.通天河.进行[id].进程==6 then
    随机名称={"散财童子","黑熊精"}
    随机模型={"小魔头","黑熊精"}
    for i=1,#随机模型 do
      local xy=地图处理类.地图坐标[6028]:取随机点()
      if 随机名称[i]=="散财童子" then
        xy.x=17
        xy.y=21
      else
        xy.x=22
        xy.y=17
      end
      local 任务id=取唯一识别码(166)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=随机模型[i],
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=6028,
        地图名称=取地图名称(6028),
        类型=166,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.通天河.进行[id].进程==7 then
      local 任务id=取唯一识别码(167)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="观音姐姐",
        模型="观音姐姐",
        x=9,
        y=9,
        方向=方向,
        副本id=id,
        地图编号=6028,
        地图名称=取地图名称(6028),
        类型=167,
      }
      地图处理类:添加单位(任务id)
  elseif 副本数据.通天河.进行[id].进程==8 then
    副本数据.通天河.进行[id].五色竹条=0
    for i=1,10 do
      local xy=地图处理类.地图坐标[6028]:取随机点()
      local 任务id=取唯一识别码(168)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="五色竹条",
        模型="树怪",
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=6028,
        地图名称=取地图名称(6028),
        类型=168,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.通天河.进行[id].进程==9 then
        local 任务id=取唯一识别码(169)
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id=id,
          名称="金鱼将军",
          模型="碧水夜叉",
          x=111,
          y=54,
          方向=方向,
          副本id=id,
          地图编号=6029,
          地图名称=取地图名称(6029),
          类型=169,
        }
        地图处理类:添加单位(任务id)

  elseif 副本数据.通天河.进行[id].进程==10 then
      local 任务id=取唯一识别码(170)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        角色=true,
        名称="灵感大王",
        模型="神天兵",
        武器="雷神",
        武器等级=100,
        x=103,
        y=55,
        方向=方向,
        副本id=id,
        地图编号=6030,
        地图名称=取地图名称(6030),
        类型=170,
         }
        地图处理类:添加单位(任务id)
  end
end


function 任务处理类:开启齐天大圣副本(id)
  if self:触发条件(id,69,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local  检测通过 = true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]
     if 玩家数据[临时id].角色:取任务(191)~=0 then
       常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
       检测通过 = false
    end
    if 副本数据.齐天大圣.完成[临时id] then
        if os.date("%m", os.time())==os.date("%m", 副本数据.齐天大圣.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.齐天大圣.完成[临时id]) then
            常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
            检测通过 = false
        else
            副本数据.齐天大圣.进行中[临时id]=nil
            副本数据.齐天大圣.完成[临时id]=nil
        end
    end
    if 副本数据.齐天大圣.进行中[临时id] then
       常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
       检测通过 = false
    end
  end
  if 检测通过 then
      local 任务id=取唯一识别码(191)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id={},
        队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
        副本id=id,
        类型=191
      }
      副本数据.齐天大圣.进行[id]={进程=1}
      副本数据.齐天大圣.进行[id].副本任务id = 任务id
      self:设置齐天大圣副本(id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
          副本数据.齐天大圣.进行中[v]=true
      end
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了齐天大圣副本")



  end
end

function 任务处理类:设置齐天大圣副本(id)
  --ID开启任务时队长ID
  --副本任务id 为171任务id
  local 地图=0
  local x坐标=0
  local y坐标=0
  local 方向=0
  if 副本数据.齐天大圣.进行[id]==nil then
    return
  end
  if 副本数据.齐天大圣.进行[id].副本任务id==nil then
      常规提示(id,"#Y副本任务失败！")
      return
  end
  local fbid = 副本数据.齐天大圣.进行[id].副本任务id
  local 随机名称={}
  local 随机模型={}
  if 副本数据.齐天大圣.进行[id].进程==1 then
    随机名称={"伤心的小猴","临死的老猴"}
    随机模型={"超级金猴","长眉灵猴"}
    副本数据.齐天大圣.进行[id].小猴老猴 = {伤心的小猴=5,临死的老猴=5}
    for i=1,2 do
      for n=1,5 do
        地图 = 6036
        local xy=地图处理类.地图坐标[地图]:取随机点()
        local 任务id=取唯一识别码(192)
        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=7200,
          玩家id=id,
          名称=随机名称[i],
          模型=随机模型[i],
          x=xy.x,
          y=xy.y,
          副本id=id,
          地图编号=地图,
          地图名称=取地图名称(地图),
          类型=192
        }
        地图处理类:添加单位(任务id)
      end
    end
  elseif 副本数据.齐天大圣.进行[id].进程==2 then
      任务id=取唯一识别码(193)
      地图=6036
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="黑白无常",
        模型="野鬼",
        变异=true,
        x=114,
        y=106,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=193,
      }
      地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程==5 then
      任务id=取唯一识别码(194)
      地图=6036
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="太白金星",
        模型="太白金星",
        x=114,
        y=106,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=194,
      }
      地图处理类:添加单位(任务id)

  elseif 副本数据.齐天大圣.进行[id].进程==8 then
    地图 = 6038
    随机名称={"盗马贼"}
    随机模型={"强盗"}
    副本数据.齐天大圣.进行[id].盗马贼=5
    for i=1,5 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务id=取唯一识别码(195)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="盗马贼",
        模型="强盗",
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=195,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.齐天大圣.进行[id].进程==9 then
      副本数据.齐天大圣.进行[id].盗马贼=nil
      任务id=取唯一识别码(196)
      地图=6038
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称="调皮的小马",
        模型="超级神马",
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=196,
      }
      地图处理类:添加单位(任务id)
  elseif 副本数据.齐天大圣.进行[id].进程==11 then
    地图 = 6036
    local xy=地图处理类.地图坐标[地图]:取随机点()
    随机名称={"百万天兵","巨灵神","李靖"}
    随机模型={"天兵","大力金刚","李靖"}
    副本数据.齐天大圣.进行[id].百万天兵={百万天兵=false,巨灵神=false}
    for i=1,#随机模型 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务id=取唯一识别码(197)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        名称=随机名称[i],
        模型=随机模型[i],
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=197,
      }
      地图处理类:添加单位(任务id)
    end
  end
end


function 任务处理类:开启大闹天宫副本(id)
  if self:触发条件(id,69,nil,nil,1,3) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 检测通过 = true
  local 队伍id=玩家数据[id].队伍
  for n=1,#队伍数据[队伍id].成员数据 do
      local 临时id=队伍数据[队伍id].成员数据[n]
       if 玩家数据[临时id].角色:取任务(180)~=0 then
         常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."正在进行副本任务，无法领取新的副本")
         检测通过 = false
      end
      if 副本数据.大闹天宫.完成[临时id] then
          if os.date("%m", os.time())==os.date("%m", 副本数据.大闹天宫.完成[临时id]) and os.date("%d", os.time())==os.date("%d", 副本数据.大闹天宫.完成[临时id]) then
              常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y本日已经完成过此副本了")
              检测通过 = false
          else
              副本数据.大闹天宫.进行中[临时id]=nil
              副本数据.大闹天宫.完成[临时id]=nil
          end
      end
      if 副本数据.大闹天宫.进行中[临时id] then
         常规提示(id,"#R"..玩家数据[临时id].角色.数据.名称.."#Y正在进行副本任务，无法领取新的副本！")
         检测通过 = false
      end
  end
  if 检测通过 then
      local 任务id=取唯一识别码(180)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7200,
        玩家id=id,
        队伍组=DeepCopy(队伍数据[玩家数据[id].队伍].成员数据),
        副本id=id,
        类型=180
      }
      副本数据.大闹天宫.进行[id]={进程=1}
      副本数据.大闹天宫.进行[id].副本任务id = 任务id
      self:设置大闹天宫副本(id)
      for i,v in ipairs(队伍数据[玩家数据[id].队伍].成员数据) do
          副本数据.大闹天宫.进行中[v]=true
      end
      玩家数据[id].角色:添加任务(任务id,1,"#Y你开启了大闹天宫副本")
  end
end

function 任务处理类:大闹锄树(id)
  if 玩家数据[id].角色:取任务(181) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  elseif 玩家数据[id].队伍 ~= 0 then
    添加最后对话(id,"组队状态下无法接任务！")
    return
  end
  local 地图=6031
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(181)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=600,
    玩家id=id,
    x=xy.x,
    y=xy.y,
    地图编号=地图,
    地图名称=取地图名称(地图),
    类型=181,
    副本id = 任务数据[玩家数据[id].角色:取任务(180)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  添加最后对话(id,format("那就请速速前往蟠桃园%s,%s处锄树清草吧！",任务数据[任务id].x,任务数据[任务id].y))
end

function 任务处理类:大闹浇水(id)
  if 玩家数据[id].角色:取任务(182) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  elseif 玩家数据[id].队伍 ~= 0 then
    添加最后对话(id,"组队状态下无法接任务！")
    return
  end
  local 目标 = {"一千年桃树","两千年桃树","三千年桃树","四千年桃树","五千年桃树","六千年桃树","七千年桃树","八千年桃树"}
  目标 = 目标[取随机数(1,#目标)]
  local 地图=6031
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(182)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=600,
    玩家id=id,
    类型=182,
    目标=目标,
    副本id = 任务数据[玩家数据[id].角色:取任务(180)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  添加最后对话(id,format("少侠拿赶快前往%s处进行浇水吧！",任务数据[任务id].目标))
end

function 任务处理类:大闹修桃(id)
  if 玩家数据[id].角色:取任务(183) ~= 0 then
    添加最后对话(id,"少侠身上已经任务，请先完成任务再来！")
    return
  elseif 玩家数据[id].队伍 ~= 0 then
    添加最后对话(id,"组队状态下无法接任务！")
    return
  end
  local 目标 = {"一千年桃树","两千年桃树","三千年桃树","四千年桃树","五千年桃树","六千年桃树","七千年桃树","八千年桃树"}
  目标 = 目标[取随机数(1,#目标)]
  local 地图=6031
  local xy=地图处理类.地图坐标[地图]:取随机点()
  local 任务id=取唯一识别码(183)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=600,
    玩家id=id,
    类型=183,
    目标=目标,
    副本id = 任务数据[玩家数据[id].角色:取任务(180)].副本id
  }
  玩家数据[id].角色:添加任务(任务id)
  添加最后对话(id,format("那就清速速前往%s处修整蟠桃树吧！",任务数据[任务id].目标))
end

function 任务处理类:设置大闹天宫副本(id)
  local 地图=0
  local x坐标=0
  local y坐标=0
  local 方向=0
  if 副本数据.大闹天宫.进行[id]==nil then
    return
  end
  if 副本数据.大闹天宫.进行[id].副本任务id==nil then
      常规提示(id,"#Y副本任务失败！")
      return
  end
  local fbid = 副本数据.大闹天宫.进行[id].副本任务id
  if 副本数据.大闹天宫.进行[id].进程==1 then
    任务数据[fbid].三大力士 = {锄树力士=5,浇水力士=5,修桃力士=5}
  elseif 副本数据.大闹天宫.进行[id].进程==2 then
    任务数据[fbid].蟠桃 = 15
    地图=6031
    for i=1,15 do
      任务id=取唯一识别码(184)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7201,
        玩家id=id,
        名称="蟠桃",
        模型="蟠桃",
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=184,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.大闹天宫.进行[id].进程==3 then
    地图 = 6031
    任务id=取唯一识别码(185)
    local xy=地图处理类.地图坐标[地图]:取随机点()
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=7201,
      玩家id=id,
      名称="七仙女",
      模型="芙蓉仙子",
      x=xy.x,
      y=xy.y,
      方向=1,
      行走开关=true,
      副本id=id,
      地图编号=地图,
      地图名称=取地图名称(地图),
      类型=185,
    }
    地图处理类:添加单位(任务id)
  elseif 副本数据.大闹天宫.进行[id].进程==4 then
    任务数据[fbid].战诸神={造酒仙官=false,运水道人=false,烧火童子=false,盘槽力士=false}
  elseif 副本数据.大闹天宫.进行[id].进程==5 then
    任务数据[fbid].饥饿小猴=10
    地图=6033
    for i=1,10 do
      任务id=取唯一识别码(186)
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7201,
        玩家id=id,
        名称="饥饿的小猴",
        模型="巨力神猿",
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        行走开关=true,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=186,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.大闹天宫.进行[id].进程==6 then
    任务数据[fbid].天兵天将 = {天兵=false,天将=false}
    地图 = 6033
    local xy=地图处理类.地图坐标[地图]:取随机点()
    随机名称={"崩将军","芭将军","天兵统领","天将统领"}
    随机模型={"巨力神猿","长眉灵猴","天兵","天将"}
    local 方向 = 0
    for i=1,#随机模型 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      if 随机名称[i]=="崩将军" then
        xy.x=83
        xy.y=19
      elseif 随机名称[i]=="芭将军" then
        xy.x=90
        xy.y=13
      elseif 随机名称[i]=="天兵统领" then
        xy.x=85
        xy.y=105
        方向 = 2
      else
        xy.x=137
        xy.y=108
        方向 = 2
      end
      任务id=取唯一识别码(187)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7201,
        玩家id=id,
        名称=随机名称[i],
        模型=随机模型[i],
        x=xy.x,
        y=xy.y,
        方向=方向,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=187,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.大闹天宫.进行[id].进程==7 then
    地图 = 6033
    local xy=地图处理类.地图坐标[地图]:取随机点()
    随机名称={"二郎真君"}
    随机模型={"二郎神"}
    for i=1,#随机模型 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务id=取唯一识别码(188)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7201,
        玩家id=id,
        名称=随机名称[i],
        模型=随机模型[i],
        x=xy.x,
        y=xy.y,
        方向=0,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=188,
      }
      地图处理类:添加单位(任务id)
    end
  elseif 副本数据.大闹天宫.进行[id].进程==8 then
    地图 = 6033
    local xy=地图处理类.地图坐标[地图]:取随机点()
    随机名称={"雷神"}
    随机模型={"二郎神"}
    for i=1,#随机模型 do
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务id=取唯一识别码(188)
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=7201,
        玩家id=id,
        名称=随机名称[i],
        模型=随机模型[i],
        x=xy.x,
        y=xy.y,
        方向=0,
        副本id=id,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=188,
      }
      地图处理类:添加单位(任务id)
    end


  end
end

function 任务处理类:完成齐天黑白无常(任务id,id组)
  if 任务数据[任务id]==nil then
    return
  end
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*10
      银子=平均等级*200
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)
     if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      local 获得物品={}
      for i=1,#自定义数据.齐天黑白无常 do
          if 取随机数()<=自定义数据.齐天黑白无常[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天黑白无常[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
            广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠打的#R黑白无常#Y，抱头鼠窜,慌忙献上#G/%s#Y以求活命。".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end
  end
  地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  任务数据[任务id]=nil
end

function 任务处理类:完成齐天阎王(任务id,id组)
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*10
      银子=平均等级*250
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)
      if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      local 获得物品={}
      for i=1,#自定义数据.齐天阎王 do
          if 取随机数()<=自定义数据.齐天阎王[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天阎王[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
           广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠打的#R十殿阎王#Y躲于岸下,将案台之上的#G/%s#Y漏了出来,顺手拿下。".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end
  end
end

function 任务处理类:完成齐天天王(任务id,id组)
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*12
      银子=平均等级*250
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)
      if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      local 获得物品={}
      for i=1,#自定义数据.齐天天王 do
          if 取随机数()<=自定义数据.齐天天王[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天天王[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
            广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠与#R四大天王#Y战的难分难舍,衬其一个分神,使出了一招亢龙有悔,天王不敌乖乖奉上#G/%s".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end

  end
end

function 任务处理类:完成齐天盗马贼(任务id,id组)
  if 任务数据[任务id]==nil then
    return
  end
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*10
      银子=平均等级*150
      玩家数据[id].角色:添加经验(经验,"盗马贼",1)
      玩家数据[id].角色:添加银子(银子,"盗马贼",1)
     if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
  end
  地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  任务数据[任务id]=nil
end

function 任务处理类:完成齐天百万天兵(任务id,id组)
  if 任务数据[任务id]==nil then
    return
  end
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*15
      银子=平均等级*400
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)
      if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      local 获得物品={}
      for i=1,#自定义数据.齐天天兵 do
          if 取随机数()<=自定义数据.齐天天兵[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天天兵[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
             广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠神勇异常将#R百万天兵#Y吓的仓皇逃跑,连#G/%s#Y遗落都没有发现。".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end
  end
  地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  任务数据[任务id]=nil
end

function 任务处理类:完成齐天巨灵神(任务id,id组)
  if 任务数据[任务id]==nil then
    return
  end
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*15
      银子=平均等级*400
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)

      if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      local 获得物品={}
      for i=1,#自定义数据.齐天巨灵 do
          if 取随机数()<=自定义数据.齐天巨灵[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天巨灵[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
             广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠艰难的战胜#R巨灵神#Y，最终略胜一筹并将#G/%s#Y抢了过来。".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end

  end
  地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  任务数据[任务id]=nil
end

function 任务处理类:完成齐天镇塔之神(任务id,id组)
  local 经验=0
  local 银子=0
  for n=1,#id组 do
    local id=id组[n]
      local 平均等级 = 取队伍平均等级(玩家数据[id].队伍,id)
      经验=平均等级*平均等级*15
      银子=平均等级*400
      玩家数据[id].角色:添加经验(经验,"齐天大圣",1)
      玩家数据[id].角色:添加银子(银子,"齐天大圣",1)

         if 玩家数据[id].角色.数据.副本积分==nil then
             玩家数据[id].角色.数据.副本积分=0
          end
          玩家数据[id].角色.数据.副本积分 = 玩家数据[id].角色.数据.副本积分 + 20
          玩家数据[id].角色:添加活跃积分(50,"齐天大圣副本",1)


      if 玩家数据[id].角色.数据.参战信息~=nil then
         玩家数据[id].召唤兽:获得经验(玩家数据[id].角色.数据.参战宝宝.认证码,qz(经验*0.35),id,"齐天大圣")
      end
      -- if 玩家数据[id].角色.数据.副本难度 ~= nil and 玩家数据[id].角色.数据.副本难度 < 3 then
      --   玩家数据[id].角色.数据.副本难度 = 3
      -- end
      local 获得物品={}
      for i=1,#自定义数据.齐天镇塔 do
          if 取随机数()<=自定义数据.齐天镇塔[i].概率 then
             获得物品[#获得物品+1]=自定义数据.齐天镇塔[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
         local 取编号=取随机数(1,#获得物品)
         if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
            玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
            广播消息({内容=format("#S(齐天大圣)#R/%s#Y少侠费劲九牛二虎之力,终是从#R七窍琉璃塔#Y中脱困而出,顺手将#G/%s#Y抢了过来。".."#"..取随机数(24,24),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
          end
      end
  end
end

function 任务处理类:捣乱的年兽()
  local 地图范围={1501,1070,1208,1092}
  for n=1,#地图范围 do
    for i=1,10 do
      local xy=地图处理类.地图坐标[地图范围[n]]:取随机点()
      local 任务id=取唯一识别码(356)
      local 造型={"古代瑞兽"}
      local 造型=造型[取随机数(1,#造型)]
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=3600,
        玩家id=id,
        名称="捣乱的年兽",
        模型=造型,
        行走开关=true,
        x=xy.x,
        y=xy.y,
        等级=100,
        显示饰品=true,
        地图编号=地图范围[n],
        地图名称=取地图名称(地图范围[n]),
        类型=356
      }
      地图处理类:添加单位(任务id)
    end
  end
  for n=1,#地图范围 do
    local xy=地图处理类.地图坐标[地图范围[n]]:取随机点()
    local 任务id=取唯一识别码(357)
    local 造型={"进阶古代瑞兽"}
    local 造型=造型[取随机数(1,#造型)]
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3600,
      玩家id=id,
      名称="年兽王",
      模型=造型,
      行走开关=true,
      等级=100,
      x=xy.x,
      y=xy.y,
      显示饰品=true,
      地图编号=地图范围[n],
      地图名称=取地图名称(地图范围[n]),
      类型=357
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#G在民间四处作乱的年兽出现在建邺城、长寿村、朱紫国、傲来国等地，玩家可以前去驱赶捣乱的年兽获得丰厚的奖励。".."#"..取随机数(24,24)),频道="xt"})
end

function 任务处理类:邪恶年兽()
  local 地图范围={1501}
  for n=1,#地图范围 do
    local xy=地图处理类.地图坐标[地图范围[n]]:取随机点()
    local 任务id=取唯一识别码(358)
    local 造型={"进阶古代瑞兽"}
    local 造型=造型[取随机数(1,#造型)]
    任务数据[任务id]={
      id=任务id,
      起始=os.time(),
      结束=3600,
      玩家id=id,
      名称="邪恶年兽·魔",
      模型=造型,
      行走开关=true,
      等级=100,
      x=xy.x,
      y=xy.y,
      显示饰品=true,
      地图编号=地图范围[n],
      地图名称=取地图名称(地图范围[n]),
      类型=358
    }
    地图处理类:添加单位(任务id)
  end
  广播消息({内容=format("#G由于大量猎杀年兽，引起了#R邪恶年兽·魔#G的注意，#R邪恶年兽·魔#G已经降临在建邺城，有实力的玩家可以前去挑战。".."#"..取随机数(24,24)),频道="xt"})
end








function 任务处理类:完成乌鸡国奖励(id)
          if 玩家数据[id].角色.数据.副本积分==nil then
             玩家数据[id].角色.数据.副本积分=0
          end
          玩家数据[id].角色.数据.副本积分 = 玩家数据[id].角色.数据.副本积分 + 20
          玩家数据[id].角色:添加活跃积分(50,"乌鸡国副本",1)
          副本数据.乌鸡国.完成[id]=os.time()
          常规提示(id,"你获得了20点副本积分)")
          玩家数据[id].道具:给予道具(id,"九转金丹",1,50)
          玩家数据[id].道具:给予道具(id,"修炼果",1)
          玩家数据[id].角色:添加经验(100000,"乌鸡")
          玩家数据[id].角色:添加银子(100000,"乌鸡",1)
          常规提示(id,"领取奖励成功获得九转金丹,修炼果")
          local 获得物品={}
          for i=1,#自定义数据.乌鸡国 do
              if 取随机数()<=自定义数据.乌鸡国[i].概率 then
                 获得物品[#获得物品+1]=自定义数据.乌鸡国[i]
              end
          end
          获得物品=删除重复(获得物品)
          if 获得物品~=nil then
             local 取编号=取随机数(1,#获得物品)
             if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(副本-乌鸡国)#R/%s#Y在#R乌鸡国#Y副本中成功完成，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="hd"})
              end
          end
end

function 任务处理类:完成车迟国奖励(id)
          if 玩家数据[id].角色.数据.副本积分==nil then
             玩家数据[id].角色.数据.副本积分=0
          end
          玩家数据[id].角色.数据.副本积分 = 玩家数据[id].角色.数据.副本积分 + 20
          玩家数据[id].角色:添加活跃积分(50,"车迟国副本",1)
          副本数据.车迟斗法.完成[id]=os.time()
          常规提示(id,"你获得了20点副本积分)")
          玩家数据[id].道具:给予道具(id,"九转金丹",1,50)
          玩家数据[id].道具:给予道具(id,"修炼果",1)
          玩家数据[id].角色:添加经验(150000,"车迟国")
          玩家数据[id].角色:添加银子(150000,"车迟国",1)
          常规提示(id,"领取奖励成功获得九转金丹,修炼果")
          local 获得物品={}
          for i=1,#自定义数据.车迟国 do
              if 取随机数()<=自定义数据.车迟国[i].概率 then
                 获得物品[#获得物品+1]=自定义数据.车迟国[i]
              end
          end
          获得物品=删除重复(获得物品)
          if 获得物品~=nil then
             local 取编号=取随机数(1,#获得物品)
             if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(副本-车迟国)#R/%s#Y在#R车迟国#Y副本中成功完成，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="hd"})
              end
          end
end

function 任务处理类:完成水路大会奖励(id)
          if 玩家数据[id].角色.数据.副本积分==nil then
             玩家数据[id].角色.数据.副本积分=0
          end
          玩家数据[id].角色.数据.副本积分 = 玩家数据[id].角色.数据.副本积分 + 20
          玩家数据[id].角色:添加活跃积分(50,"水路大会副本",1)
          副本数据.水陆大会.完成[id]=os.time()
          常规提示(id,"你获得了20点副本积分)")
          玩家数据[id].道具:给予道具(id,"九转金丹",1,50)
          玩家数据[id].道具:给予道具(id,"修炼果",1)
          玩家数据[id].角色:添加经验(200000,"水路大会")
          玩家数据[id].角色:添加银子(200000,"水路大会",1)
          常规提示(id,"领取奖励成功获得九转金丹,修炼果")
          local 获得物品={}
          for i=1,#自定义数据.水路大会 do
              if 取随机数()<=自定义数据.水路大会[i].概率 then
                 获得物品[#获得物品+1]=自定义数据.水路大会[i]
              end
          end
          获得物品=删除重复(获得物品)
          if 获得物品~=nil then
             local 取编号=取随机数(1,#获得物品)
             if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(副本-水路大会)#R/%s#Y在#R水路大会#Y副本中成功完成，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="hd"})
              end
          end
end

function 任务处理类:完成通天河奖励(id)
          if 玩家数据[id].角色.数据.副本积分==nil then
             玩家数据[id].角色.数据.副本积分=0
          end
          玩家数据[id].角色.数据.副本积分 = 玩家数据[id].角色.数据.副本积分 + 20
           玩家数据[id].角色:添加活跃积分(50,"通天河副本",1)
          副本数据.通天河.完成[id]=os.time()
          常规提示(id,"你获得了20点副本积分)")
          玩家数据[id].道具:给予道具(id,"九转金丹",1,50)
          玩家数据[id].道具:给予道具(id,"修炼果",1)
          玩家数据[id].角色:添加经验(250000,"通天河")
          玩家数据[id].角色:添加银子(250000,"通天河",1)
          常规提示(id,"领取奖励成功获得九转金丹,修炼果")
          local 获得物品={}
          for i=1,#自定义数据.通天河 do
              if 取随机数()<=自定义数据.通天河[i].概率 then
                 获得物品[#获得物品+1]=自定义数据.通天河[i]
              end
          end
          获得物品=删除重复(获得物品)
          if 获得物品~=nil then
             local 取编号=取随机数(1,#获得物品)
             if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S(副本-通天河)#R/%s#Y在#R通天河#Y副本中成功完成，因此获得了其奖励的#G/%s#Y".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="hd"})
              end
          end
end





-- function 任务处理类:全服蚩尤(任务id,id组)
-- 开启帮派竞赛()
-- 发送公告("#S(全服活动)#G雪人材料#Y收集完成#G感谢大家努力,现在可以去周杰伦处,开启挑战!")
-- end


function 任务处理类:取npc坐标(地图,名称)
          if 地图处理类.NPC列表[地图] then
             for k,v in pairs(地图处理类.NPC列表[地图]) do
                 if v.名称 == 名称 then
                    return math.floor(v.x),math.floor(v.y)
                end
             end
          end
          return 0,0
end



function 任务处理类:取新手指引说明(id,任务id)
 local 名称="初入桃源村"
 local 说明=""
 local 备注="第一章节，桃园回忆"
 local 进程=任务数据[任务id].进程
 if 进程==1 then
   说明="欢迎来到#G/"..服务端参数.名称.."#W/\n#Z/剧情加强版#W/\n由我来带你开启\n#Y/全新的梦幻之旅吧\n先去旁边找到\n#S/新手接待师#W/进行福利领取吧!"
  elseif 进程==2 then
   说明="去找#G狼宝#领取#G助战伙伴\n然后去找#G/霞姑娘#W/正式的进行剧情任务吧，她就在#Y/水池右下方不远处"
  elseif 进程==3 then
   说明="你已经观看完回忆#S/\n现在可以去找#W/旁边的#G/刘大婶\n#W进行桃源村之旅"
  elseif 进程==4 then
   说明="你获得了一套装备\n但是#S大侠们#是不能#G饿肚子的\n去找#Y孙厨娘#拿点吃的吧"
  elseif 进程==5 then
   说明="现在吃的也有了\n但是风吹日晒的\n万一有个#G感冒发烧#怎么办\n桃源村有个#Y玄大夫\n找他要点#G药品#啥的\n以备不时之需"
  elseif 进程==6 then
   说明="万事俱备\n但你还差一件最重要的东西\n行走江湖怎么能没#R武器#呢\n去问问#Y桃园村长\n有没有一点趁手的\n#G兵器#让你使用"
  elseif 进程==7 then
    说明="现在什么东西都齐了\n你感觉你浑身都充满了#S力量\n总想找一个人#Y干一架\n完全没问题,你可以到\n#G孙猎户#那里练练手\n先#Z升级#自己#S等级"
   end
 return {名称,说明,备注}
  end
  function 任务处理类:取新手指引说明1(id,任务id)
 local 名称="新手礼包"
 local 说明=""
 local 备注="新手礼包"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = 0,0
 local sjx = {地图=1501,x=0,y=0,名称="宠物仙子"}

 if 进程==1 then
     真实坐标x,真实坐标y = self:取npc坐标(1501,"宠物仙子")
     sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="宠物仙子"}

   说明="欢迎来到#G/"..服务端参数.名称.."#W/\n由我来带你开启\n#Y/全新的梦幻之旅吧\n先去旁边找到\n@进行福利领取吧!"
elseif 进程==2 then
      真实坐标x,真实坐标y = self:取npc坐标(1501,"升级")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="升级"}
   说明="赶快升级到#R/10级\n#W去拜师吧.点击人物界面@\n或者去长安城门派传送\n可以拜入的门派哟!"
 elseif 进程==3 then
  --说明="点击活动面板\n#G/通过新手任务\n#升级吧!您可以在野外使用\n自动挂机功能在野外场景挂机\n打开系统设置里面\n勾选自动遇怪即可!"
    真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
    说明="恭喜你完成了\n桃源村的#S新手任务\n现在你正式踏上了\n#Z西行取经剧情之旅\n最近#G建邺城#老是\n传出闹鬼的事情\n去找@问问清楚"
  end




 return {名称,说明,备注,sjx}
  end






function 任务处理类:取商人的鬼魂(id,任务id)
 local 名称="商人的鬼魂"
 local 说明=""
 local 备注="第二章节，沉船降妖"
 local 进程=任务数据[任务id].进程

 local 真实坐标x,真实坐标y = 0,0
 local sjx = {地图=1501,x=0,y=0,名称="老孙头"}

 if 进程==1 then

   说明="欢迎来到#G/"..服务端参数.名称.."\n#W/由我来带你开启\n全新的梦幻之旅吧!\n最近#G建邺城#W/老是传出闹鬼的事情去找@#W/问问清楚"
  -- 说明="恭喜你完成了\n桃源村的#S新手任务\n现在你正式踏上了\n#Z西行取经剧情之旅\n最近#G建邺城#老是\n传出闹鬼的事情\n取找#Y老孙头#问问清楚"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
  elseif 进程==2 then
   说明="你得去找#G@\n问问如何操办一场#Y法事\n给海底的亡魂们进行超度"
    真实坐标x,真实坐标y = self:取npc坐标(1501,"牛大胆")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="牛大胆"}
  elseif 进程==3 then
   说明="牛大胆叫你去给他弄\n一只#Y烤鸭#来\n而且必须还是#G@#做的鸭\n赶紧出发去找#S鸭#吧#29"
    真实坐标x,真实坐标y = self:取npc坐标(1501,"王大嫂")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="王大嫂"}
  elseif 进程==4 then
   说明="@需要你帮他找#Y熊胆\n给他的孩子治病\n但是熊胆挺贵的\n如果你#G钱不够\n可以选择去抓点海鲜\n卖个#S海鲜收购商#换取银两"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"王大嫂")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="王大嫂"}
  elseif 进程==5 then
   说明="你现在已经拿到#Y烤鸭#了\n千万别自己#R吃掉#或#S扔掉哦\n不然无法进行剧情任务\n废话不多说把烤鸭\n交给#G@#吧"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"牛大胆")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="牛大胆"}
  elseif 进程==6 then
   说明="牛大胆已经\n开始筹办法事了\n他现在叫你去找#Y@\n把#G安魂草#给他\n让他免受鬼魂骚扰\n(#S该道具无需上交\n#S直接找对应NPC就行#)"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
  elseif 进程==7 then
    说明="听闻海底沉船\n每年都会#G闹鬼\n所以老孙头叫你\n去找#S李善人的@\n看看他那边有\n什么需要帮忙的"
    真实坐标x,真实坐标y = self:取npc坐标(1501,"管家")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="管家"}
  elseif 进程==8 then
    说明="去建邺城找找\n#G@#想办法看\n能不能弄到#Y地狱灵芝\n然后交给李善人治好他的病"
    真实坐标x,真实坐标y = self:取npc坐标(1501,"马全有")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="马全有"}
  elseif 进程==9 then
    说明="@说叫你\n给他抓一只#Y海毛虫\n他才肯将#G地狱灵芝#给你\n为了治好李善人的病\n你得抓紧了#84\n(#S请不要把上交的宠物\n#G设置为参战状态#)"
      真实坐标x,真实坐标y = self:取npc坐标(1501,"马全有")
    sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="马全有"}
  elseif 进程==10 then
    说明="你已经获得了#Y地狱灵芝\n现在可以将该物品\n交给#G@#了\n治好了他的病\n他才会给你#R钱#1\n请不要把该物品\n自己#S使用#或者#G扔掉\n以免无法触发\n下面的剧情任务"
    真实坐标x,真实坐标y = self:取npc坐标(1534,"李善人")
    sjx = {地图=1534,x=真实坐标x,y=真实坐标y,名称="李善人"}
  elseif 进程==11 then
    说明="李善人给你讲述了\n事情的缘由,现在你可以\n去#Y海底沉船#调查一番\n找到那个#G@\n然后尝试超度它\n(#G建议组队进行#)"
    真实坐标x,真实坐标y = self:取npc坐标(1509,"商人的鬼魂")
    sjx = {地图=1509,x=真实坐标x,y=真实坐标y,名称="商人的鬼魂"}
  elseif 进程==12 then
    说明="你已成功超度#Y商人的鬼魂\n现在回建邺城\n找#G@#领取酬劳吧#1"
    真实坐标x,真实坐标y = self:取npc坐标(1534,"李善人")
    sjx = {地图=1534,x=真实坐标x,y=真实坐标y,名称="李善人"}
   end

 return {名称,说明,备注,sjx}
  end
function 任务处理类:取妖风支线(id,任务id)
 local 名称="支线彩蛋-妖风"
 local 说明=""
 local 备注="支线彩蛋-妖风"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
 local sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
 if 进程==1 then
   说明="你打开宝箱发现来自一个\n神秘男子的留言,说:\n#G建邺城#的#Y雷黑子\n其实这沉船鬼怪\n#S妖风#所变化而来\n赶紧找到#Y雷黑子\n问问详情!"
  elseif 进程==2 then
   说明="找到#S妖风#干掉他\n为死去的#G海难者#报仇\n为#Y雷黑子#报仇!#4\n(#G建议等级20级#)"
   end
 return {名称,说明,备注}
  end

function 任务处理类:取新枯萎金莲支线(id,任务id)
 local 名称="新枯萎金莲"
 local 说明=""
 local 备注="新枯萎金莲"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
 local sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
 if 进程==1 then
   说明="去问问#G@\n有没有#Y虎子#的线索"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"吹牛王")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="吹牛王"}
  elseif 进程==2 then
   说明="去问问#G@\n关于#Y虎子#失踪的事情"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"马全有")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="马全有"}
  elseif 进程==3 then
   说明="要到东海湾找虎子\n先找#G@#借狗一用"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"雷黑子")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="雷黑子"}
  elseif 进程==4 then
   说明="旺财跑到#G@\n那里去了,去找她吧"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"小花")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="小花"}
  elseif 进程==5 then
   说明="旺财往东海湾跑了\n到东海湾找#G@#问一问"
    真实坐标x,真实坐标y = self:取npc坐标(1506,"云游神医")
   sjx = {地图=1506,x=真实坐标x,y=真实坐标y,名称="云游神医"}
  elseif 进程==6 then
   说明="和@对话\n开启#S僵尸虎子#战斗\n(#G建议等级10级#)"
   真实坐标x,真实坐标y = self:取npc坐标(1506,"云游神医")
   sjx = {地图=1506,x=真实坐标x,y=真实坐标y,名称="云游神医"}
  elseif 进程==7 then
   说明="找#G@\n问个究竟!"
   真实坐标x,真实坐标y = self:取npc坐标(1506,"云游神医")
   sjx = {地图=1506,x=真实坐标x,y=真实坐标y,名称="云游神医"}
  elseif 进程==8 then
   说明="找#G@\n询问治疗虎子的办法"
   真实坐标x,真实坐标y = self:取npc坐标(1506,"楚恋依")
   sjx = {地图=1506,x=真实坐标x,y=真实坐标y,名称="楚恋依"}
  elseif 进程==9 then
   说明="去哪里找莲花呢\n还是先把旺财还给\n建邺的#G@#吧"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"雷黑子")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="雷黑子"}
  elseif 进程==10 then
   说明="找#G@\n问问关于金莲的事情"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"小花")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="小花"}
  elseif 进程==11 then
   说明="把#Y枯萎的金莲\n交给东海湾@"
   真实坐标x,真实坐标y = self:取npc坐标(1506,"楚恋依")
   sjx = {地图=1506,x=真实坐标x,y=真实坐标y,名称="楚恋依"}
  elseif 进程==12 then
   说明="回去告诉#G@\n虎子已经没事了"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"王大嫂")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="王大嫂"}
   end
 return {名称,说明,备注,sjx }
  end

function 任务处理类:取桃园浣熊支线(id,任务id)
 local 名称="干掉狸猫"
 local 说明=""
 local 备注="干掉狸猫"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
 local sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
 if 进程==1 then
   说明="可恶的狸猫\n老是#Y性骚扰#帅气的郭大哥\n教训他们一下"
  elseif 进程==2 then
   说明="你已经打败了狸猫\n找#G郭大哥#领取报酬吧!"
   end
 return {名称,说明,备注}
  end

function 任务处理类:取新手指引任务(id,任务id)
 local 名称="新手指引"
 local 说明=""
 local 备注="新手指引"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = self:取npc坐标(1501,"老孙头")
 local sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="老孙头"}
 if 进程==1 then
   说明="去建邺衙门\n找#G赵捕头\n他似乎有些事情给你做\n#Y@#也有支线任务给你做"
   真实坐标x,真实坐标y = self:取npc坐标(1501,"王大嫂")
   sjx = {地图=1501,x=真实坐标x,y=真实坐标y,名称="王大嫂"}
  elseif 进程==2 then
   说明="去长安城\n找#G@\n他会指引你下一步\n(#Y20级后开启)"
    真实坐标x,真实坐标y = self:取npc坐标(1001,"长安导游")
   sjx = {地图=1001,x=真实坐标x,y=真实坐标y,名称="长安导游"}
  elseif 进程==3 then
   说明="回到自己的门派\n找师傅完成一次\n#G师门任务"
    sjx =nil
  elseif 进程==4 then
   说明="在长安双处\n找到#G@#\n完成一次赏金任务\n上交一轮#Y心魔宝珠"
   真实坐标x,真实坐标y = self:取npc坐标(1001,"皇宫护卫")
   sjx = {地图=1001,x=真实坐标x,y=真实坐标y,名称="皇宫护卫"}
  elseif 进程==5 then
   说明="在长安书店\n找到#G@#\n完成#Y一次打工"
   真实坐标x,真实坐标y = self:取npc坐标(1019,"颜如玉")
   sjx = {地图=1019,x=真实坐标x,y=真实坐标y,名称="颜如玉"}
  elseif 进程==6 then
   说明="在铁匠铺\n找到#G@#\n完成#Y一次装备打造\n如果#G打造技能等级#不够\n请先提升#Y打造技能等级"
   真实坐标x,真实坐标y = self:取npc坐标(1025,"打铁炉")
   sjx = {地图=1025,x=真实坐标x,y=真实坐标y,名称="打铁炉"}

  elseif 进程==7 then
   说明="回到长@\n领取#G新手指引任务#奖励"
   真实坐标x,真实坐标y = self:取npc坐标(1001,"长安导游")
   sjx = {地图=1001,x=真实坐标x,y=真实坐标y,名称="长安导游"}
   end
 return {名称,说明,备注,sjx}
  end











function 任务处理类:取玄奘身世(id,任务id)
 local 名称="玄奘的身世"
 local 说明=""
 local 备注="第三章节，身世之谜"
 local 进程=任务数据[任务id].进程
 local 真实坐标x,真实坐标y = 0,0
 local sjx = nil
 if 进程==1 then
   说明="#Y@#最近似乎\n在寻找着什么!\n去完成,你可以先:\n#S拜师,#Y刷塔,#G练练宝宝,#R升升级\n该剧情建议#S25级"
   真实坐标x,真实坐标y = self:取npc坐标(1070,"南极仙翁")
   sjx = {地图=1070,x=真实坐标x,y=真实坐标y,名称="南极仙翁"}
  elseif 进程==2 then
   说明="去#G大唐境外#找一只#Y@\n找到后将它带回给#R南极仙翁\n#S(需进行战斗)"
   真实坐标x,真实坐标y = self:取npc坐标(1173,"白鹿精")
   sjx = {地图=1173,x=真实坐标x,y=真实坐标y,名称="白鹿精"}
  elseif 进程==3 then
   说明="#Y白鹿精#已经被你\n成功收服了\n现在快将它带回给\n#G@#吧"
   真实坐标x,真实坐标y = self:取npc坐标(1070,"南极仙翁")
   sjx = {地图=1070,x=真实坐标x,y=真实坐标y,名称="南极仙翁"}
  elseif 进程==4 then
   说明="只能自认倒霉\n#Y@#让你去\n找#G百色花#来给他的鹿儿医治\n这个药品应该有些\n#S药店#会有卖"
   真实坐标x,真实坐标y = self:取npc坐标(1070,"南极仙翁")
   sjx = {地图=1070,x=真实坐标x,y=真实坐标y,名称="南极仙翁"}
  elseif 进程==5 then
   说明="事情告一段落\n南极仙翁叫你去\n去大唐国境的\n#Y金山寺#找一个叫\n#G@#的法师\n了解他的#S身世之谜\n他会带你踏上\n#G西行取经#之旅\n(#S建议等级30级#)"
   真实坐标x,真实坐标y = self:取npc坐标(1153,"玄奘")
   sjx = {地图=1153,x=真实坐标x,y=真实坐标y,名称="玄奘"}
  elseif 进程==6 then
   说明="身为出家人\n#Y居然品行低劣#\n嘲笑#G玄奘法师#的身世\n这种疯和尚应该\n#S教训一番\n#Y(需进行战斗)"
   sjx=nil
  elseif 进程==7 then
   说明="经过一番战斗\n你终于教训了那该死的\n#Y酒肉和尚#,一旁的#G玄奘\n表示这样打打杀杀不好\n他有话给你说,去#S找他问问"
   sjx=nil
  elseif 进程==8 then
   说明="想要了解#G玄奘的身世\n你得前往#Y化生寺#跑一趟了\n找到#S@\n他或许知道些什么"
   真实坐标x,真实坐标y = self:取npc坐标(1528,"法明长老")
   sjx = {地图=1528,x=真实坐标x,y=真实坐标y,名称="法明长老"}
  elseif 进程==9 then
   说明="你有些灰头土脸的\n没想到一来就吃了#G闭门羹\n你决定#S找个人问问\n看有没有办法尽快\n治好#G法明长老#的伤势\n#R门口#那个#Y小胖和尚#挺好的"
   sjx=nil
  elseif 进程==10 then
   说明="为了治好#G法明长老#的伤势\n你需要前往#Y花果山\n找到#S@\n让它给你制作#R定神香"
   真实坐标x,真实坐标y = self:取npc坐标(1514,"猴医仙")
   sjx = {地图=1514,x=真实坐标x,y=真实坐标y,名称="猴医仙"}
  elseif 进程==11 then
   说明="@叫你去找\n#G餐风饮露#给他\n只有这样他才能\n制作出#R定神香\n你可以去#Y药店#\n看看有卖得没"
   真实坐标x,真实坐标y = self:取npc坐标(1514,"猴医仙")
   sjx = {地图=1514,x=真实坐标x,y=真实坐标y,名称="猴医仙"}
  elseif 进程==12 then
   说明="你已成功拿到\n#G定神香#赶紧\n回到#Y化生寺\n把它交给#S@#吧!"
   真实坐标x,真实坐标y = self:取npc坐标(1528,"法明长老")
   sjx = {地图=1528,x=真实坐标x,y=真实坐标y,名称="法明长老"}
  elseif 进程==13 then
   说明="为了找回#G化生寺\n#被白琉璃盗取的\n#Y佛光舍利子\n法明长老叫你去#S天宫@问问这\n#G白琉璃#的来历\n#R(需进行战斗)"
   真实坐标x,真实坐标y = self:取npc坐标(1111,"执法天兵")
   sjx = {地图=1111,x=真实坐标x,y=真实坐标y,名称="执法天兵"}
  elseif 进程==14 then
   说明="你从执法天兵话中\n得知了#S@#的由来\n也打听到她现在的下落\n是时候去#Y大唐国境\n找她归还#G佛光舍利#了"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"白琉璃")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="白琉璃"}
  elseif 进程==15 then
   说明="白琉璃已经将\n#Y佛光舍利#交还给你了\n听了她的诉说\n发现她其实也是\n一个#G可怜之人\n回#S化生寺#吧\n把东西还给#R@"
   真实坐标x,真实坐标y = self:取npc坐标(1528,"法明长老")
   sjx = {地图=1528,x=真实坐标x,y=真实坐标y,名称="法明长老"}
  elseif 进程==16 then
   说明="你已拿到@法师的\n#Y身世证明\n#R血书#一份\n去#G金山寺#交于他吧\n#S该任务无需道具\n#Y直接找人就行"
   真实坐标x,真实坐标y = self:取npc坐标(1153,"玄奘")
   sjx = {地图=1153,x=真实坐标x,y=真实坐标y,名称="玄奘"}
  elseif 进程==17 then
   说明="玄奘法师#G神志有点不清\n去问问旁边的#Y@\n到底是怎么回事儿!\n#R(需进行战斗)"
   真实坐标x,真实坐标y = self:取npc坐标(1153,"酒肉和尚")
   sjx = {地图=1153,x=真实坐标x,y=真实坐标y,名称="酒肉和尚"}
  elseif 进程==18 then
   说明="这该死的#Y酒肉和尚\n居然没有#G解药#,看来得\n去一趟#S普陀山#求取\n#R九转回魂丹#了"
  elseif 进程==19 then
   说明="你得知想要#G解毒\n还得需要#S孟婆汤#\n一起服用才行\n看来得去#R地府#走一趟了\n#Y找到@"
   真实坐标x,真实坐标y = self:取npc坐标(1122,"孟婆")
   sjx = {地图=1122,x=真实坐标x,y=真实坐标y,名称="孟婆"}
  elseif 进程==20 then
   说明="孟婆暂时还不能把汤给你\n你得去找到#G@\n进行一番超度后\n才能获得#Y孟婆汤\n(#G需要等级到达40级#)"
   真实坐标x,真实坐标y = self:取npc坐标(1127,"幽冥鬼")
   sjx = {地图=1127,x=真实坐标x,y=真实坐标y,名称="幽冥鬼"}
  elseif 进程==21 then
   说明="#Y幽冥鬼#不想转世的\n原因是还未惦记着一人\n为了帮他完成心愿\n你得去#S大唐国境#找一个\n叫#G@#的女孩"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"文秀")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="文秀"}
  elseif 进程==22 then
   说明="#Y了解了凡尘俗世后\n文秀让你把他的\n#G信物#带给\n#Y@#\n#S该任务无需道具\n#Y直接找人就行"
   真实坐标x,真实坐标y = self:取npc坐标(1127,"幽冥鬼")
   sjx = {地图=1127,x=真实坐标x,y=真实坐标y,名称="幽冥鬼"}
  elseif 进程==23 then
   说明="#Y幽冥鬼#终于看破红尘\n准备轮回投胎了\n你也得回去找#G@#\n兑现她的承诺了"
   真实坐标x,真实坐标y = self:取npc坐标(1122,"孟婆")
   sjx = {地图=1122,x=真实坐标x,y=真实坐标y,名称="孟婆"}
  elseif 进程==24 then
   说明="现在#Y孟婆汤#和\n#G九转回魂丹#都齐了\n回到金山寺\n快去给@#S解毒#吧\n#R该任务只需要提交\n#G九转回魂丹#即可"
   真实坐标x,真实坐标y = self:取npc坐标(1153,"玄奘")
   sjx = {地图=1153,x=真实坐标x,y=真实坐标y,名称="玄奘"}
  elseif 进程==25 then
   说明="原来#Y玄奘的身世#中\n藏着这样的#R深仇大恨\n你自告奋勇\n决定帮玄奘法师#S复仇\n去#G大唐国境\n#看能不能找到#Y@"
   真实坐标x,真实坐标y = self:取npc坐标(1168,"殷温娇")
   sjx = {地图=1168,x=真实坐标x,y=真实坐标y,名称="殷温娇"}
  elseif 进程==26 then
   说明="想要进入江州府\n找到#Y殷温娇#\n你的取得衙役的信任才行\n所以去留香阁\n帮衙役搞来#G@#的东西\n作为#S信物#骗取他的信任"
   真实坐标x,真实坐标y = self:取npc坐标(1033,"小桃红")
   sjx = {地图=1033,x=真实坐标x,y=真实坐标y,名称="小桃红"}
  elseif 进程==27 then
   说明="快跑吧\n#Y老鸨#已经叫人来打你了\n回#S大唐国境\n把你顺到的#G手帕\n交给#R衙役\n#S该任务无需道具\n#Y直接找人就行"
  elseif 进程==28 then
   说明="你进入了#Y江州府\n赶紧去找#G@#吧"
   真实坐标x,真实坐标y = self:取npc坐标(1168,"殷温娇")
   sjx = {地图=1168,x=真实坐标x,y=真实坐标y,名称="殷温娇"}
  elseif 进程==29 then
   说明="听了#G殷温娇#\n十八年前的#Y往事\n你得找个#S@的家伙#问问\n当年的#R陈光蕊#\n是否被水族所救"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"虾兵")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="虾兵"}
  elseif 进程==30 then
   说明="这个虾兵并\n不了解当年之事\n你得照着他说的那样\n先去@哪里拿到#S避水珠\n方可进入#G龙宫"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"山神")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="山神"}
  elseif 进程==31 then
   说明="给@找来\n#G特别的美食\n和他换取#S避水珠\n这样才可以进入#Y龙宫"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"山神")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="山神"}
  elseif 进程==32 then
   说明="你已经拿到了#S避水珠\n快前往#G龙宫#继续帮\n殷温娇问问#Y光蕊之事#吧\n你现在可以将\n#R(需进行战斗)"
  elseif 进程==33 then
   说明="他妈的,又被人#G错打一顿\n算了算了,还是去找#Y@\n办#S正事#儿吧"
   真实坐标x,真实坐标y = self:取npc坐标(1116,"龟千岁")
   sjx = {地图=1116,x=真实坐标x,y=真实坐标y,名称="龟千岁"}
  elseif 进程==34 then
   说明="原来#Y陈光蕊#还活着\n虽然不知道去向\n但也算一件欣慰之事\n回江州府,跟#G@#交代一吧"
   真实坐标x,真实坐标y = self:取npc坐标(1168,"殷温娇")
   sjx = {地图=1168,x=真实坐标x,y=真实坐标y,名称="殷温娇"}
  elseif 进程==35 then
   说明="去国境#Y找@#Y问问\n看有没有#G殷温娇婆婆#的下落"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"小二")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="小二"}
  elseif 进程==36 then
   说明="@已经#S饿得没有力气了\n我需要去给他找点#G食物\n但我不知道她需要吃什么\n什么东西能一下#Y吃饱#呢?"
   真实坐标x,真实坐标y = self:取npc坐标(1110,"婆婆")
   sjx = {地图=1110,x=真实坐标x,y=真实坐标y,名称="婆婆"}
  elseif 进程==37 then
   说明="把婆婆带回给#G@#吧"
   真实坐标x,真实坐标y = self:取npc坐标(1168,"殷温娇")
   sjx = {地图=1168,x=真实坐标x,y=真实坐标y,名称="殷温娇"}
  elseif 进程==38 then
   说明="将这封#G书信#带给\n#R长安城内#的#Y@"
   真实坐标x,真实坐标y = self:取npc坐标(1001,"殷丞相")
   sjx = {地图=1001,x=真实坐标x,y=真实坐标y,名称="殷丞相"}
  elseif 进程==39 then
   说明="把此事通知#G@#大人\n让他即刻出兵\n讨伐贼人#Y刘洪\n为朝廷出去祸害!"
   真实坐标x,真实坐标y = self:取npc坐标(1044,"魏征")
   sjx = {地图=1044,x=真实坐标x,y=真实坐标y,名称="魏征"}
  elseif 进程==40 then
   说明="魏征已经同意#R剿贼\n并且给你了#G一定的兵马\n助你斩杀#Y@"
   真实坐标x,真实坐标y = self:取npc坐标(1168,"刘洪")
   sjx = {地图=1168,x=真实坐标x,y=真实坐标y,名称="刘洪"}
  elseif 进程==41 then
   说明="经过一番激战后\n你现在得去追击\n#Y真正的@#了\n快去#G大唐境外#看看吧!"
   真实坐标x,真实坐标y = self:取npc坐标(1173,"刘洪")
   sjx = {地图=1173,x=真实坐标x,y=真实坐标y,名称="刘洪"}
  elseif 进程==42 then
   说明="这一系列事情\n终于尘埃落定\n回#Y金山寺\n向#G@法师\n诉说这一切"
   真实坐标x,真实坐标y = self:取npc坐标(1153,"玄奘")
   sjx = {地图=1153,x=真实坐标x,y=真实坐标y,名称="玄奘"}
  elseif 进程==43 then
   说明="你现在可以去\n#S大唐境外\n找到#G@\n触发剧情战斗"
   真实坐标x,真实坐标y = self:取npc坐标(1173,"天兵飞剑")
   sjx = {地图=1173,x=真实坐标x,y=真实坐标y,名称="天兵飞剑"}
   end
    return {名称,说明,备注,sjx}
  end








function 任务处理类:设置初入桃源村(id)
    if 玩家数据[id].角色:取任务(999)~=0 then
    添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
    return
  end
 local 任务id=id.."_999_"..os.time()

   任务数据[任务id]={
   id=任务id,
   起始=os.time(),
   玩家id=id,
   进程=1,
   类型=999
}
 玩家数据[id].角色:添加任务(任务id)

 end




 function 任务处理类:设置商人的鬼魂(id)
    if 玩家数据[id].角色:取任务(998)~=0 then
      添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
      return
    end
   local 任务id=id.."_998_"..os.time()
     任务数据[任务id]={
     id=任务id,
     起始=os.time(),
     玩家id=id,
     进程=1,
     类型=998
    }
   玩家数据[id].角色:添加任务(任务id)
   发送数据(玩家数据[id].连接id,39)
 end



function 任务处理类:设置妖风支线(id)
      if 玩家数据[id].角色:取任务(898)~=0 then
    添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
    return
  end
 local 任务id=id.."_898_"..os.time()
 任务数据[任务id]={
 id=任务id,
 起始=os.time(),
 玩家id=id,
 进程=1,
 类型=898
}
 玩家数据[id].角色:添加任务(任务id)
 发送数据(玩家数据[id].连接id,39)
 end
 function 任务处理类:设置新枯萎金莲支线(id)
  if 玩家数据[id].角色:取任务(400)~=0 then
  添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
  return
  end
 local 任务id=id.."_400_"..os.time()

 任务数据[任务id]={
 id=任务id,
 起始=os.time(),
 玩家id=id,
 进程=1,
 类型=400
}
 玩家数据[id].角色:添加任务(任务id)
 发送数据(玩家数据[id].连接id,39)
 end

function 任务处理类:设置桃园浣熊支线(id)
if 玩家数据[id].角色:取任务(401)~=0 then
添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
return
end
local 任务id=id.."_401_"..os.time()
任务数据[任务id]={
id=任务id,
起始=os.time(),
玩家id=id,
进程=1,
类型=401
}
玩家数据[id].角色:添加任务(任务id)
发送数据(玩家数据[id].连接id,39)
end

function 任务处理类:设置新手指引任务(id)
if 玩家数据[id].角色:取任务(402)~=0 then
添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
return
end
local 任务id=id.."_402_"..os.time()
任务数据[任务id]={
id=任务id,
起始=os.time(),
玩家id=id,
进程=1,
类型=402
}
玩家数据[id].角色:添加任务(任务id)
发送数据(玩家数据[id].连接id,39)
end


 function 任务处理类:设置玄奘的身世(id)
      if 玩家数据[id].角色:取任务(997)~=0 then
    添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
    return
  end
 local 任务id=id.."_997_"..os.time()
 任务数据[任务id]={
 id=任务id,
 起始=os.time(),
 玩家id=id,
 进程=1,
 类型=997
}
 玩家数据[id].角色:添加任务(任务id)
 发送数据(玩家数据[id].连接id,39)
 end
function 任务处理类:大战心魔(id,任务id)
 local 名称="大战心魔"
 local 说明=""
 local 备注="大战心魔"
 local 进程=任务数据[任务id].进程
 if 进程==1 then
   说明="找到#R天兵飞剑#开启战斗"
  elseif 进程==2 then
   说明="搞定了天兵,看看#G卷帘大将#有什么苦衷"
  elseif 进程==3 then
   说明="一场激战后,卷帘大将身上散落一串念珠,大将的神色忽然大变,赶紧问问他怎么回事?"
  elseif 进程==4 then
   说明="向长安城的#G袁守城#请教阵法之理,不然无法击败卷帘大将的心魔"
  elseif 进程==5 then
   说明="去#G五庄观#找到#Y清风\n询问#S天心星#的下落"
  elseif 进程==6 then
   说明="去#G普陀山#找到#Y青莲仙女"
  elseif 进程==7 then
   说明="帮#Y青莲仙女#找到#Y火凤之睛\n并交予她"
  elseif 进程==8 then
   说明="回到#Y清风#那里将#Y仙露\n并交予他"
  elseif 进程==9 then
   说明="去#Y长寿郊外#找到#G路人甲"
  elseif 进程==10 then
   说明="你已获得天心星\n去#G天宫#找#Y水兵统领#\n询问#S天英星#的下落"
  elseif 进程==11 then
   说明="去地府找到#G判官#询问情况"
  elseif 进程==12 then
   说明="去天宫找到#G杨戬#询问情况"
  elseif 进程==13 then
   说明="你已经获得#G天英星\n袁守城被龙族所困扰\n找到#Y龙孙#解决他"
  elseif 进程==14 then
   说明="找#G袁守城#触发剧情"
  elseif 进程==15 then
   说明="找#G卷帘大将#触发战斗"
   end
 return {名称,说明,备注}
  end
function 任务处理类:设置大战心魔(id)
  if 成就数据[id].大战心魔==nil then
     成就数据[id].大战心魔=0
  end
  if 玩家数据[id].角色:取任务(996)~=0 then
  添加最后对话(id,"你已经领取过该任务抓紧去完成吧！")
  return
  elseif 成就数据[id].大战心魔~=nil and 成就数据[id].大战心魔==1 then
  添加最后对话(id,"你已经完成了该任务了")
  return
  end
 local 任务id=id.."_996_"..os.time()
 任务数据[任务id]={
 id=任务id,
 起始=os.time(),
 玩家id=id,
 进程=1,
 类型=996
}
 玩家数据[id].角色:添加任务(任务id)
 发送数据(玩家数据[id].连接id,39)
 end




function 任务处理类:添加春节任务(id)
 --------找人，--------打怪，----------上交物品


  if self:触发条件(id,69,212,"春节任务",1) then--id,等级,任务,活动,队伍,人数
      return
  end
  local 任务id=取唯一识别码(212)
  随机序列=随机序列+1
  local 分类 =取随机数(1,3)
  if 分类 == 1 then
      local 地图范围={1501,1092,1070,1193,1173,1146,1140,1208,1040,1226,1142}
      local 地图=地图范围[取随机数(1,#地图范围)]
      local xy=地图处理类.地图坐标[地图]:取随机点()
      任务数据[任务id]={
        id=任务id,
        起始=os.time(),
        结束=1800,
        玩家id=id,
        名称="调皮的小年兽",
        模型="进阶古代瑞兽",
        x=xy.x,
        y=xy.y,
        地图编号=地图,
        地图名称=取地图名称(地图),
        类型=212,
        分类=分类
      }
      地图处理类:添加单位(任务id)
  elseif 分类 == 2 then
        local 物品名称={"天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑",
                        "火凤之睛","星辉石","红玛瑙","太阳石","舍利子","黑宝石","月亮石","光芒石",
                        "金香玉","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","十香返生丸","五龙丹",
                        "烤肉","醉生梦死","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","珍露酒","烤鸭","豆斋果","臭豆腐","虎骨酒","女儿红"}

        任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=1800,
          玩家id=id,
          分类=分类,
          物品=物品名称[取随机数(1,#物品名称)],
          类型=212
        }
    elseif 分类 == 3 then
          local 寻找地图 ={1001,1501,1040,1208,1110,1122,1173,1092,1070}
          local 地图 = 寻找地图[取随机数(1,#寻找地图)]
          local npc编号 = 取随机数(1,5)
          if 地图 == 1001 then
            npc编号 = 取随机数(1,54)
          elseif 地图 == 1501 then
            npc编号 = 取随机数(1,29)
          elseif 地图 == 1040 then
            npc编号 = 取随机数(1,9)
          elseif 地图 == 1208 or 地图 == 1122 then
            npc编号 = 取随机数(1,7)
          elseif 地图 == 1110 then
            npc编号 = 取随机数(1,17)
          elseif 地图 == 1173 then
            npc编号 = 取随机数(1,23)
          elseif 地图 == 1092 then
            npc编号 = 取随机数(1,15)
          elseif 地图 == 1070 then
            npc编号 = 取随机数(1,20)
          end
          任务数据[任务id]={
          id=任务id,
          起始=os.time(),
          结束=1800,
          玩家id=id,
          名称=地图处理类.NPC列表[地图][npc编号].名称,
          地图 = 地图,
          地图名称=取地图名称(地图),
          x=地图处理类.NPC列表[地图][npc编号].x,
          y=地图处理类.NPC列表[地图][npc编号].y,
          NPC名称 = 地图处理类.NPC列表[地图][npc编号].名称,
          分类=分类,
          类型=212
        }
  end
  local 对话 = {名称="福禄童子",模型="小仙女"}
  if 任务数据[任务id].分类 == 1 then
     对话.内容 = format("#G/调皮的小年兽#W/正在#G/%s(%s,%s)#W/捣乱，请前去相劝。",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y)
  elseif 任务数据[任务id].分类 == 2 then
          对话.内容 = format("听说#G/%s#W/甚是神异,还请少侠找来交给我吧。",任务数据[任务id].物品)
  elseif 任务数据[任务id].分类 == 3 then
          对话.内容 =format("我无法移动,还忘少侠代我前往#G/%s(%s,%s)#W/问候#G/%s",任务数据[任务id].地图名称,任务数据[任务id].x,任务数据[任务id].y,任务数据[任务id].名称)
  end
  玩家数据[id].角色:添加任务(任务id,1,nil,对话)

end


function 任务处理类:完成春节任务(id)
  local 队伍id=玩家数据[id].队伍
  local 等级=取队伍平均等级(玩家数据[id].队伍,id)
  local 任务id = 玩家数据[id].角色:取任务(212)
  for n=1,#队伍数据[队伍id].成员数据 do
    local 临时id=队伍数据[队伍id].成员数据[n]
      玩家数据[临时id].角色:自定义银子添加("春节任务",1)
      玩家数据[临时id].角色:添加活跃积分(2,"春节任务",1)
      添加活动次数(临时id,"春节任务")
      local 获得物品={}
      for i=1,#自定义数据.春节任务 do
          if 取随机数()<=自定义数据.春节任务[i].概率 then
             获得物品[#获得物品+1]=自定义数据.春节任务[i]
          end
      end
      获得物品=删除重复(获得物品)
      if 获得物品~=nil then
        local 取编号=取随机数(1,#获得物品)
        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
           玩家数据[临时id].道具:自定义给予道具(临时id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
          广播消息({内容=format("#S(春节任务)#R/%s#Y完成#R春节任务#Y，因此获得了奖励#G/%s#Y".."#"..取随机数(1,110),玩家数据[临时id].角色.数据.名称,获得物品[取编号].名称),频道="hd"})
        end
      end

      玩家数据[临时id].角色:取消任务(任务id)
      发送数据(玩家数据[临时id].连接id,39)
  end
  任务数据[任务id] = nil

end





function 任务处理类:开启三界书院()
  三界书院 = {
    答案 = "",
    开关 = true,
    结束 = 65,
    起始 = os.time(),
    间隔 = 取随机数(30, 90) * 60,
    名单 = {}
  }
  local 题目序号 = 取随机数(1, #三界题库 - 1)
  三界书院.答案 = 三界题库[题目序号].答案
  广播消息({内容="#Y/大家好，又到了考验大家知识水平的时间了。60秒内在世界频道中回答正确的玩家可以获得丰厚奖励噢。问题如下：#W/" .. 三界题库[题目序号].问题 .. "？",频道="xt"})
end



-- 1506  东海湾  1110   大唐国境  1193  江南野外
-- 1514  花果山  1091   长寿郊外  1201  女娲神迹
-- 1070  长寿村  1208   朱紫国    1040  西梁女国
-- 1092  傲来国  1226   宝象国    1207  蓬莱仙岛
-- 1173  小西天  1174  北俱芦洲   1235  丝绸之路
-- 1210  麒麟山







return 任务处理类