
----------------不要放到战斗数据文件里边--------------

---------------默认气血数额----------------------
--等级:69 气血:6209 
--等级:89 气血:8328
--等级:109 气血:10497
--等级:129 气血:12564 
--等级:175 气血:17651 
-------------------------------------------------------------------------

function 战斗准备类:哪吒之敖丙(任务id,玩家id)
  local 战斗单位 = {}
  
--------------------怪物属性调整范围(必写否则无法加载计算)---------------------------------------------------------
  战斗单位.等级 = 取队伍最高等级数(玩家数据[玩家id].队伍,玩家id)
  战斗单位.系数 ={
        -----------------------最高----困难----高级----中级---新手
        气血 = 30,  ---30------20------15------10-----5------2---默认*系数
        伤害 = 95,  ---95      70      55      35     17-----8---等级*系数
        法伤 = 90,  ---90      65      50      30     12-----6---等级*系数
        防御 = 32,  ---32      22      15      10     3------2---等级*系数
        法防 = 28,  ---28      18      13       8     2------1---等级*系数
        速度 = 25,  ---25      15      10       7     4------2---等级*系数 
        修炼 = 1.8  ---1.8     1.5     1.2     1.2    1.2----1---取人物修炼等级上限(等级) * 系数
   }

--------------------------以下为怪物模型及其他添加--------------------------------------------
----战斗单位.不加修炼 =true ---这个命令为怪物不添加修炼
----------------------以前的模型可以直接复制过来 属性不用管-----------------------------------
  战斗单位[1] = {
      名称 = "敖丙",
      模型 = "龙太子",
      饰品 = true,
      角色 = true,
      武器 = 取武器数据("飞龙在天",120),
      锦衣 = {[1]={名称="冰寒绡月白"}},
      技能 = {"魔之心", "法术连击"},
      主动技能 = 取随机法术(5)
  }
  local 模型池 = {"蟹将","巨蛙","龙龟","龟丞相","鲛人"}
  local 名称范围={"敖丙左护法","敖丙右护法"}
  local 临时加载={"点杀","法术","群杀","固伤","封印","辅助"} ---循环随机怪物机制
  ---local 数量=???  ---添加多少个战斗单位
  ---for i=2,数量 do ----和数量对应 
-------i=2就是从战斗单位[2]开始添加,',10'就是截至到战斗单位10------------------------------
  for i=2,10 do    
      战斗单位[i] ={
            名称 = 名称范围[取随机数(1, #名称范围)],
            模型 = 模型池[取随机数(1, #模型池)],
            饰品 = true,
	   -- 加载 = 临时加载[取随机数(1, #临时加载)], ---循环随机添加怪物的机制
        }
  end

--------------不走循环单独设定----------------------
 --  战斗单位[2] ={
 --            名称 = 名称范围[取随机数(1, #名称范围)],
 --            模型 = 模型池[取随机数(1, #模型池)],
 --            饰品 = true,
 --        }
 -- 战斗单位[3] ={
 --            名称 = 名称范围[取随机数(1, #名称范围)],
 --            模型 = 模型池[取随机数(1, #模型池)],
 --            饰品 = true,
 --        }

-------------------------单独个别属性调整,也可放入循环-------------------------
   战斗单位[6].治疗能力=3000
   战斗单位[7].封印命中等级= 3000
   战斗单位[9].固定伤害=3000
-------------单独设定某个怪物的机制--------------------
  战斗单位[2].加载= "法术"
  战斗单位[3].加载= "群杀"
  战斗单位[4].加载= "群杀"
  战斗单位[6].加载= "辅助"
  战斗单位[7].加载= "封印"
  战斗单位[9].加载= "固伤"
----------------加载机制可以循环可以单独写,只需保留一次就行----------------------------
  return 战斗单位
end




--------------------------------------------必须添加属性
--               名称="xxx",
--               模型="xxxx",
--------------------------------------其他可添加,可不添加
--               气血 = math.floor(等级*2.3),
--               魔法 = math.floor(等级*2.3),
--               伤害 = math.floor(等级*2.3),
--               防御 = math.floor(等级*2.3),
--               法伤 = math.floor(等级*2.3),
--               法防 = math.floor(等级*2.3),
--               速度 = math.floor(等级*2.3),
--               躲闪=等级*2,
--               等级=等级,
--               技能={},
--               主动技能={},
--               愤怒=9999,
--               不可封印=true,
--               饰品=true,
--               物伤减少=0.01,  ------伤害*数额=伤害结果  1000*0.01 = 10
--               法伤减少=0.01,  ------法伤*数额=法伤结果  1000*0.01 = 10
--               躲避减少=1-100, ----------取1-100随机 普攻会多半 100是百分百躲避普攻
--               攻击修炼=10,
--               防御修炼=10,
--		 法术修炼=10,
--		 抗法修炼=10,
		 --------人物修炼0-35 宠物修炼0-30--------------------------
--               人物更多属性---固定伤害  治疗能力等 没有法术防御和法术伤害  暴击等级0-2000  抵抗暴击0-2000 封印抵抗封印0-2000
--               门派="xxx",
--               奇经八脉={xx=1,xx=1},
--               经脉流派="xxx" ,
--               奇经特效={xx=1,xx=1},
                 --自行查看客户端奇经八脉界面经脉技能组合添加  其中门派和奇经八脉必须一起添加 最好配合<<类型="角色">> 使用
--               武器伤害=1000,
--               附加状态={[1]={名称="xxx",等级=185}},
--               追加法术={[1]={名称="xxx",等级=185}},
--               内丹数据={[1]={技能="xxx",等级=5},[2]={技能="xxx",等级=5}},
--               五维属性={体质=0,魔力=0,力量=0,耐力=0,敏捷=0},
--               类型="角色", ---"角色" "系统角色"
--               角色=true,  ---类型变成系统角色
--               武器="xxx", ---配合角色模型 <<角色=true 类型="角色">> 显示角色类型佩戴武器
--               锦衣={[1]=??}, --使用--<<类型="角色">> 可查看玩家角色数据添加锦衣
--	           ---使用角色后某些经脉技能会失效 某些经脉技能会生效

------------------------------------------以上添加记得查看标点符号等是否有问题

---------添加阵法

--战斗单位[1].附加阵法 ="天赋阵"  ----或直接不写

----------------------技能={}添加方法
技能 = {"xxx","xxx","xxx","xxx"}
技能 = 取随机兽决被动(数量)

----------------------所有被动
"高级反震","高级进击必杀","高级进击法暴","高级吸血",
"高级反击","高级连击","高级飞行","高级夜战","高级隐身","高级感知",
"高级再生","高级冥思","高级慧根","高级必杀","高级幸运","高级神迹",
"高级招架","高级永恒","高级敏捷","高级强力","高级防御","高级偷袭","高级驱怪",
"高级毒","高级驱鬼","高级鬼魂术","高级魔之心","高级神佑复生","高级精神集中",
"高级否定信仰","高级法术连击","凭风借力","出其不意","风起龙游","虎虎生威","气贯长虹","狂莽一击",
"高级法术暴击","高级法术波动","高级雷属性吸收","高级土属性吸收","高级火属性吸收",
"高级水属性吸收"

----------------------主动技能={}添加方法
主动技能={"xxx","xxx","xxx","xxx"}


主动技能=取随机法术(数量)
---------随机法术
"荆棘舞","水攻","雷击","烈火","落岩","奔雷咒","碎星诀","推气过宫","金刚护体","落叶萧萧","裂石",
"推拿","夺魄令","地涌金莲","催眠符","失心符","含情脉脉","似玉生香","横扫千军","唧唧歪歪","金刚护法",
"五雷咒","龙卷雨击","浪涌","龙吟","龙腾","天雷斩","烟雨剑法","五雷轰顶","飞砂走石","三昧真火","狮搏",
"鹰击","连环击","反间之计"



主动技能=取随机物理法术(数量)
----------随机物理法术
"裂石","横扫千军","浪涌","天雷斩","烟雨剑法","满天花雨","后发制人",
"破釜沉舟","翻江搅海","天崩地裂","善恶有报","剑荡四方","力劈华山"



主动技能=取随机法术新(数量)
----------随机法术新
"水漫金山","地狱烈火","奔雷咒","泰山压顶","超级奔雷咒","超级地狱烈火","超级水漫金山",
"超级泰山压顶","八凶法阵","叱咤风云","天降灵葫","流沙轻音","食指大动","扶摇万里",
"龙卷雨击","飞砂走石","落叶萧萧","三昧真火","龙腾","烈火","水攻","落岩","雷击","月光"
	


主动技能=取随机固伤法术(数量)
----------随机固伤法术
"天罗地网","阎罗令","夺命咒","判官令","靛沧海","日光华","紧箍咒",
"地裂火","苍茫树","巨岩破","雨落寒沙","龙吟","地涌金莲"


主动技能=取随机加血法术(数量)
----------随机加血法术
"普渡众生","四海升平","晶清诀","推气过宫","地涌金莲","金刚护体",
"金刚护法","一苇渡江","罗汉金钟","救死扶伤","太极护法"



主动技能=取随机封印法术(数量)
----------随机封印法术
"反间之计","催眠符","失心符","落魄符","失忆符","追魂符","离魂符",
"失魂符","定身符","莲步轻舞","如花解语","似玉生香","镇妖","错乱","日月乾坤",
"含情脉脉","锢魂术","落花成泥"






-----------------------------------------------------奇经特效

    if  self.门派== "大唐官府" then
      if  self.当前经脉 == "浴血豪侠" then
        return  {翩鸿一击=1}
        elseif self.当前经脉 == "无双战神" then
        return {翩鸿一击=1,连破=1}
        elseif self.当前经脉 == "虎贲上将" then
        return {披坚执锐=1}
      end
    elseif  self.门派== "方寸山" then
      if  self.当前经脉 == "拘灵散修" then
        return {}
        elseif self.当前经脉 == "伏魔天师" then
        return{悲恸=1,奔雷=1,}
        elseif self.当前经脉 == "五雷正宗" then
        return{五雷正法=1,雷法崩裂=1,雷法震煞=1,雷法坤伏=1,咒符=1}
      end
    elseif  self.门派== "女儿村" then
      if  self.当前经脉 == "绝代妖娆" then
        return {自矜=1}
        elseif self.当前经脉 == "花雨伊人" then
        return{}
        elseif self.当前经脉 == "妙舞佳人" then--------------------官服已改
        return{踏歌=1,乐韵=1,轻歌飘舞=1,翩跃飞舞=1}
      end



    elseif  self.门派== "化生寺" then
      if  self.当前经脉 == "杏林妙手" then
        return{明光=1,佛眷=1,}
        elseif self.当前经脉 == "护法金刚" then
        return{明光=1,聚气=1}
        elseif self.当前经脉 == "无量尊者" then
        return {度厄=1}
      end
    elseif  self.门派== "盘丝洞" then
      if  self.当前经脉 == "风华舞圣" then
        return{神迷=1}
        elseif self.当前经脉 == "迷情妖姬" then
        return{神迷=1}
        elseif self.当前经脉 == "百媚魔姝" then
        return{神迷=1,千蛛噬魂=1,蛛丝缠绕=1}
      end
    elseif  self.门派== "阴曹地府" then
      if  self.当前经脉 == "勾魂阎罗" then
        return{六道无量=1}
        elseif self.当前经脉 == "六道魍魉" then
        return{六道无量=1}
        elseif self.当前经脉 == "诛刑毒师" then
        return{血影蚀心=1,百鬼噬魂=1}
      end
    elseif  self.门派== "魔王寨" then
      if  self.当前经脉 == "平天大圣" then
        return{魔冥=1}
        elseif self.当前经脉 == "盖世魔君" then
        return{魔冥=1}
        elseif self.当前经脉 == "风火妖王" then
        return{}
      end
    elseif  self.门派== "狮驼岭" then
      if  self.当前经脉 == "嗜血狂魔" then
        return{}
        elseif self.当前经脉 == "万兽之王" then
        return{驯兽幼狮=1,幼狮之搏=1,鹰击长空=1,狮魂=1}
        elseif self.当前经脉 == "狂怒斗兽" then
        return{狂怒=1}
      end
    elseif  self.门派== "天宫" then
      if  self.当前经脉 == "镇妖神使" then
        return{}
        elseif self.当前经脉 == "踏雷天尊" then
        return{电芒=1}
        elseif self.当前经脉 == "霹雳真君" then
        return{风雷斩=1,霹雳弦惊=1,雷怒霆激=1,返璞=1}
      end
    elseif  self.门派== "普陀山" then
      if  self.当前经脉 == "莲台仙子" then
        return{}
        elseif self.当前经脉 == "五行咒师" then
        return{}
        elseif self.当前经脉 == "落伽神女" then
        return{五行珠=1,剑意莲心=1}
      end
    elseif  self.门派== "五庄观" then
      if  self.当前经脉 == "清心羽客" then
        return{}
        elseif self.当前经脉 == "乾坤力士" then
        return{骤雨=1}
        elseif self.当前经脉 == "万寿真仙" then
        return{敲金击玉=1,还丹=1,金击式=1}
      end
    elseif  self.门派== "龙宫" then
      if  self.当前经脉 == "海中蛟虬" then
        return{龙魂=1,龙骇=1}
        elseif self.当前经脉 == "云龙真身" then
        return{龙魂=1,龙骇=1}
        elseif self.当前经脉 == "沧海潜龙" then
        return{龙魂=1,龙骇=1}
      end
    elseif  self.门派== "神木林" then
      if  self.当前经脉 == "通天法王" then
        return{风灵=1,鞭挞=1}
        elseif self.当前经脉 == "巫影祭司" then
        return{风灵=1,鞭挞=1,蛊树迷瘴=1,催化=1}
        elseif self.当前经脉 == "灵木药宗" then
        return{百草诀=1,药灵=1,百草神木复苏=1,滋养=1}
      end
    elseif self.门派== "凌波城" then
      if  self.当前经脉 == "九天武圣" then
        return{吞山=1,饮海=1}
        elseif self.当前经脉 == "灵霄斗士" then
        return{超级战意=1}
        elseif self.当前经脉 == "风云战将" then
        return{天眼神通=1}
      end
    elseif self.门派== "无底洞" then
      if  self.当前经脉 == "妙谛金莲" then
        return{金莲=1,由己渡人=1,焕生咒=1,燃血术=1}
        elseif self.当前经脉 == "摄魂迷影" then
        return{燃血术=1}
        elseif self.当前经脉 == "幽冥巫煞" then
        return{裂魂=1,燃血术=1,追魂刺=1}
      end
    elseif self.门派== "九黎城" then
        return {}

