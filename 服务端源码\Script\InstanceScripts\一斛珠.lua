-- @作者: baidwwy
-- @邮箱:  <EMAIL>
-- @创建时间:   2022-10-17 20:12:06
-- @最后修改来自: baidwwy
-- @Last Modified time: 2025-01-02 21:38:18
local 一斛珠 = class()


function 一斛珠:初始化(副本ID,队长id)
	self.进程=0
	self.副本名="一斛珠"
	self.地图编号=7001 --关联地图编号
	self.任务类型=7001 --任务类型
	self.副本玩家={}
	self.副本ID=副本ID
	self.开始时间=os.time()
	self.副本所有者id=队长id
	self.对话进程=1
	self.夜影迷踪数量=3
end

function 一斛珠:添加NPC(任务类型,NPC名称,NPC模型,NPC方向,xy,行走开关,临时编号,地图编号,武器信息)
	local 编号=临时编号
	if 编号==nil then
		编号=取随机数(1,10000)
	end
	local mapid=地图编号
	if mapid==nil then
		mapid=self.地图编号
	end
	local 任务id=取唯一识别码(任务类型)
	随机序列=随机序列+1
	任务数据[任务id]={
		id=任务id,
		起始=os.time(),
		结束=10800,
		玩家id={},
		队伍组=任务数据[self.副本ID].队伍组,
		名称=NPC名称,
		模型=NPC模型,
		方向=NPC方向,
		变异=true,
		行走开关=行走开关,
		x=xy.x,
		y=xy.y,
		副本id=self.副本ID,
		地图编号=mapid,
		地图名称=取地图名称(self.地图编号),
		类型=任务类型,
		真实副本id = self.副本ID
	}
	if 武器信息~=nil then
      任务数据[任务id].武器=武器信息.武器
      任务数据[任务id].武器等级=武器信息.武器等级
	end
	地图处理类:添加单位(任务id)
end

function 一斛珠:触发事件(事件名,参数)
	if 事件名=="夜影迷踪" then
		self.夜影迷踪数量 = self.夜影迷踪数量 - 1
		if self.夜影迷踪数量 <= 0 then
			self.夜影迷踪数量 = 0
		end
	end
	副本处理类:刷新玩家任务追踪(self.副本玩家)
end

function 一斛珠:设置副本进程(进程)

	if self.进程>=进程 then
		return
	end

	if 进程==nil then
		self.进程=self.进程+1
	else
		self.进程=进程
	end

	if self.进程==1 then
		self:添加NPC(7002,"胡郎中","男人_道士",1,生成XY(164,46),false)
		self:添加NPC(7002,"海老人","男人_老伯",0,生成XY(100,68),false)
		self:添加NPC(7002,"偷看的小妞妞","小丫丫",2,生成XY(36,73),false)
	elseif self.进程==2 then
		self:添加NPC(7003,"鸾儿","狐美人",1,生成XY(34,47),false)
	elseif self.进程==3 then
		self:添加NPC(7003,"叶夫人","飞燕女",1,生成XY(21,51),false)
	elseif self.进程==4 then
		副本处理类:删除NPC(7001,7003,self.副本ID)
		self:添加NPC(7005,"崔明珠","英女侠",0,生成XY(29,20),false,nil,7002)
		self:添加NPC(7005,"沈唐","逍遥生",1,生成XY(33,22),false,nil,7002,{武器="星瀚",武器等级="160"})
	elseif self.进程==5 then
		self:添加NPC(7006,"看门狗儿","狼",1,生成XY(47,75),false)
	elseif self.进程==6 then
		self:添加NPC(7007,"翠珠梳妆台","家具_桌子",1,生成XY(63,25),false,nil,7003)
	elseif self.进程==7 then
		self:添加NPC(7007,"官差","男人_衙役",3,生成XY(54,30),false,nil,7003)
	elseif self.进程==10 then
		self:添加NPC(7008,"崔明珠","英女侠",1,生成XY(34,47),false)
		self:添加NPC(7008,"沈唐","逍遥生",1,生成XY(21,51),false,nil,nil,{武器="星瀚",武器等级="160"})
	elseif self.进程==11 then
		副本处理类:删除NPC(7001,7008,self.副本ID)
		self:添加NPC(7009,"夜影迷踪","蛟龙",1,生成XY(45,107),false)
		self:添加NPC(7009,"夜影迷踪","蛟龙",1,生成XY(221,21),false)
		self:添加NPC(7009,"夜影迷踪","蛟龙",1,生成XY(209,14),false)
		self:添加NPC(7009,"垫脚的箱子","宝箱",1,生成XY(32,101),false)
		self:添加NPC(7009,"袁天罡","袁天罡",1,生成XY(216,10),false)
	elseif self.进程==12 then
		self:添加NPC(7009,"沈唐真身","鲛人",1,生成XY(70,62),false,nil,7004)
	elseif self.进程==13 then
		self.结束时间=os.time()
		self:添加NPC(7009,"戏班老板","男人_老伯",3,生成XY(62,67),false,nil,7004)
	end

	副本处理类:刷新玩家任务追踪(self.副本玩家)
end

function 一斛珠:取任务说明()
	local 名称="一斛珠"
	local 说明=""
	local 备注="一斛珠"
	local 进程=self.进程
    local sjx = {地图=1501,x=84,y=31,名称="戏班班主"}
	if 进程==0 then
		sjx = {地图=1501,x=84,y=31,名称="戏班班主"}
		说明=format("戏快要开始了，赶紧找@进入#G四方城#W吧!剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==1 then
		sjx =nil
		说明=format("这便是大名鼎鼎的“商都”四方城了，先四处逛逛吧！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==2 then
		sjx = {地图=6001,x=34,y=47,名称="鸾儿"}
		说明=format("怎么@突然发起狂来！赶紧保护新娘，制服她！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==3 then
		sjx =nil
		说明=format("#R叶夫人(21,51)#W怎么也……看我挡住她！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==4 then
		sjx =nil
		说明=format("崔博衢先生被官差带走，已三天了，问问#Y沈唐#W现在到底是什么情形，剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==5 then
		sjx =nil
		说明=format("给#Y看门狗儿#W随便喂个什么。剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==6 then
		sjx =nil
		说明=format("一斛珠香粉，应该就在#Y翠珠梳妆台#W里放着，赶紧把它找出来，速战速决！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==7 then
		sjx =nil
		说明=format("哎呀，被#Y官差#W发现啦，这下只有硬来了！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==8 then
		sjx =nil
		说明=format("将#Y一斛珠香粉#W拿给#Y崔明珠#W看看，是否有问题。剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==9 then
		sjx =nil
		说明=format("向#Y海老人#W讨点胭脂草。剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==10 then
		sjx =nil
		说明=format("#R明珠姑娘#W怕是有危险，快点阻止#R沈唐（20，51）#W。剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==11 then
		sjx =nil
		说明=format("#Y沈唐#W竟然跃上城墙跑了，看来只能沿着城墙找找看了！路上顺手铲除作乱的#R夜影迷踪#W吧（剩余数量：#G%s#W）。剩余时间#R%s#W分钟。（可通过四方城#Y（32，101）#W处#Y垫脚的箱子#W爬上去）",self.夜影迷踪数量,取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==12 then
		sjx =nil
		说明=format("这里是什么地方，#R沈唐怎么变成了这个样子！剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	elseif 进程==13 then
		sjx =nil
		说明=format("满纸荒唐言,一把辛酸泪。都云作者痴,谁解其中味？戏已落幕，找#Y戏班老板#W回去吧。剩余时间#R%s#W分钟",取分((任务数据[self.副本ID].结束-(os.time()-任务数据[self.副本ID].起始))))
	end
	return {名称,说明,备注,sjx}
end

function 一斛珠:副本传送(id)
	if self.进程 == 4 or self.进程== 8 then
		地图处理类:跳转地图(id,7002,27,26)
	elseif self.进程 == 6 or self.进程 == 7 then
		地图处理类:跳转地图(id,7003,27,41)
	elseif self.进程 == 12 or self.进程 == 13 then
		地图处理类:跳转地图(id,7004,62,67)
	else
		地图处理类:跳转地图(id,self.地图编号,173,41)
 	end
end

function 一斛珠:NPC对话(连接id,玩家id,模型,名称,任务类型编号,标识)
    -- if 任务数据[标识].战斗==nil then
    --   对话数据.对话="是你们肆意的猎杀我的伙伴的，杀~杀~杀"
    --   对话数据.选项={"休要猖狂","路过~路过"}
    -- else
    --   对话数据.对话="我正在战斗中，请勿打扰。"
    -- end
    local 进程=self.进程
	local 对话数据={}
	对话数据.模型=模型
	对话数据.名称=名称
	对话数据.对话="我认识你么？"
    if 名称=="胡郎中" then
    	对话数据.对话="我们也觉得吴彪爷说的对，今朝有酒今朝醉！看您几位兴致挺高，再加上几个小菜送送酒？"
    elseif 名称=="偷看的小妞妞" then
    	对话数据.对话="崔家小姐要结婚啦，大哥哥你也是来参加婚礼的嘛？"
    	if 进程==1 then
	    	对话数据.选项={"过去凑凑热闹","这……还是算了吧"}
    	end
    elseif 名称=="鸾儿" then
    	对话数据.模型="狐美人"
    	对话数据.对话="..."
    	if 进程==2 then
    		对话数据.对话="田四海你这个死胖子！自从嫁给你之后，我就没有一天是开心的！现在，我要杀光这里所有的宾客！"
	    	对话数据.选项={"鸾儿姑娘！休要如此鲁莽！","这……真是有点尴尬"}
    	end
    elseif 名称=="叶夫人" then
    	对话数据.模型="飞燕女"
    	对话数据.对话="..."
    	if 进程==3 then
    		对话数据.对话="嘿嘿嘿，崔家明珠小姐，今天是你的大喜日子，那我们就热闹一下吧！"
	    	对话数据.选项={"叶夫人！你不要冲动","这……真是有点尴尬"}
    	end
    elseif 名称=="沈唐" or 名称=="崔明珠" then
    	对话数据.模型="逍遥生"
    	对话数据.对话="……"
    	if 进程==4 and self.对话进程 == 1 then
    		self.对话进程 = 2
	    	对话数据.模型="逍遥生"
	    	对话数据.名称="沈唐"
			对话数据.对话="少侠，你们可来啦！岳父大人已经被官差带走三天了，我们的孩子，仓库，也尽皆被查封。如今官府咬死了是我们家出售的“一斛珠”有问题，你说，这可如何是好#52"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="英女侠"
	    	对话数据.名称="崔明珠"
			对话数据.对话="不可能！我爹爹为人正直,乐善好施，一辈子最是珍爱自己的名声，怎么可能在“一斛珠”里掺假！说不定是她们二人所用的，根本不是我们崔氏所售的“一斛珠”！"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="那我们首先要找到他们所用的脂粉……"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="逍遥生"
	    	对话数据.名称="沈唐"
			对话数据.对话="可是，她们二人的住所，早已被查封……"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="这个不难，我有办法#18这几日官差们都已经甚是疲惫了，所以都在轮班休息，遇到晌午吃饭时，干脆就只在门前放了只狗看守门……我们只要趁着这时候去……还是很容易得手的……"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="英女侠"
	    	对话数据.名称="崔明珠"
			对话数据.对话="少侠，实在是辛苦您了……"
			对话数据.选项={"这就出发！","待我稍作准备，这便出发。"}
			副本处理类:发送全队对话信息(玩家id,对话数据)
    		return
    	elseif 进程<=7 then
	    	对话数据.模型="英女侠"
	    	对话数据.名称="崔明珠"
			对话数据.对话="少侠，实在是辛苦您了……"
			对话数据.选项={"这就出发！","待我稍作准备，这便出发。"}
		elseif 进程==8 then
	    	对话数据.模型="英女侠"
	    	对话数据.名称="崔明珠"
			对话数据.对话="少侠，实在是辛苦您了……"
			对话数据.选项={"拿香粉给崔明珠看看","这……还是等等吧。"}
		elseif 进程==10 then
			if self.对话进程 == 4 then
				self.对话进程=5
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="鬼鲸涎明明是这几年才出现的药材，根本不可能来自沈家的祖传秘方！沈唐根本就是设计陷害崔先生！如今，崔先生已经陷入狱中，现在他要对你下手了！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="你说……什么……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="一定沈唐觊觎崔氏巨富，故此才对你下手！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="我觊觎崔氏巨富？哈哈哈哈哈！！这怕是当世最可笑的故事吧！！！不过，你倒是很聪明哦……这么快，便看出了我的破绽！"
				对话数据.选项={"废话少说！看招！","待我休息一下，再将你斩于马下！"}
				副本处理类:发送全队对话信息(玩家id,对话数据)
				return
			else
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="我觊觎崔氏巨富？哈哈哈哈哈！！这怕是当世最可笑的故事吧！！！不过，你倒是很聪明哦……这么快，便看出了我的破绽！"
				对话数据.选项={"废话少说！看招！","待我休息一下，再将你斩于马下！"}
			end
    	end
    elseif 名称=="看门狗儿" then
    	对话数据.对话="汪汪汪，汪汪汪……（看上去是饿了）"
    	if 进程==5 then
	    	对话数据.选项={"给它一个包子","这……还是算了吧"}
    	end
    elseif 名称=="翠珠梳妆台" then
    	if 进程==6 then
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="呼呼……终于找到啦！"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="男人_衙役"
	    	对话数据.名称="官差"
			对话数据.对话="什么人！竟敢跑来偷盗证物！"
    		副本处理类:发送全队对话信息(玩家id,对话数据)
    		self:设置副本进程(7)
    		return
    	end
    elseif 名称=="官差" then
    	对话数据.对话="你胆子好大啊，竟然私自闯入，偷盗证物，真是活腻歪了！"
    	if 进程==7 then
	    	对话数据.选项={"受人所托，实在得罪了！","那还你好咯"}
    	end
    elseif 名称=="海老人" then
    	对话数据.对话="今天是崔家小姐大婚的日子，整个四方城都热闹起来了。"
    	if 进程==9 then
    		if self.对话进程 == 3 then
    			self.对话进程 = 4
		    	对话数据.模型="男人_老伯"
		    	对话数据.名称="海老人"
				对话数据.对话="唔……谁吵我睡觉……真是的……#2"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="老先生……您这儿有胭脂草卖吗？我听说十里八乡，就只有您这间店子有这稀奇玩意儿#80"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="男人_老伯"
		    	对话数据.名称="海老人"
				对话数据.对话="嘿嘿嘿，胭脂草算什么！我最近弄了些鬼鲸涎，那才真真儿稀奇呢！这东西，来自汪洋深处，甚是昂贵，擦在脸上，可以使得肌肤润泽。不过也怪，这东西好像是这一两年才出现在世面上的东西，我遍查典籍，都没找到有关这个东西的记载。晤……天下间竟然还有老夫我不知道的药材……不过也……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="您说，这东西，是这几年才出现的新药材？！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="男人_老伯"
		    	对话数据.名称="海老人"
				对话数据.对话="可不是！前几前我们才能造出可远洋的大船，这才能得到这些来自远洋的宝贝#80"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="糟了！什么家传的秘方！#Y沈唐在骗人！"
				对话数据.选项={"这就赶回四方城！","等等，我还要思考一下！"}
				副本处理类:发送全队对话信息(玩家id,对话数据)
				return
	    	else
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="糟了！什么家传的秘方！#Y沈唐在骗人！"
				对话数据.选项={"这就赶回四方城！","等等，我还要思考一下！"}
    	    end
    	end
    elseif 名称=="垫脚的箱子" then
    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
    	对话数据.对话="少侠，从我这里就可以爬到四方城的第二层！"
	    对话数据.选项={"这就出发！","好害怕，不去了。"}
	elseif 名称=="袁天罡" then
		if self.夜影迷踪数量==0 then
	    	对话数据.对话="妖魔已除！就让老夫来助少侠一臂之力，追上沈唐！"
	    	对话数据.选项={"快带我过去！","好害怕，不去了。"}
	    else
	    	对话数据.对话="#Y夜影迷踪#W还在作乱，待除去妖魔后老夫可助你追上沈唐！"
		end
    elseif 名称=="夜影迷踪" then
		战斗准备类:创建战斗(玩家id,100305,标识)
		return
    elseif 名称=="沈唐真身" then
    	if self.对话进程 == 5 and 进程==12 then
	    	self.对话进程 = 6
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="这……是哪里？你怎么变成了这个样子？"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="鲛人"
	    	对话数据.名称="沈唐真身"
			对话数据.对话="这里是沧浪墟，它本来的名字，叫做沧浪海……这茫茫汪洋中的蚌精与鲛人，在此安居乐业千余年……从来与世无争……"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="那现在，怎么会是这般满目苍夷？难道……"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="鲛人"
	    	对话数据.名称="沈唐真身"
			对话数据.对话="哼哼，这一切，可就要先讲个故事听听看咯……沧浪墟中有一个可爱的小蚌精也叫明珠，她既美丽又善良，对大海之外的世界充满了好奇。有一次，海上的大风足足刮了三天三夜，海上的船只尽皆沉没。明珠费尽力气，也只救下一名瘦弱的少年。足足守护他九天九夜，终于将他唤醒。说来也是缘分，这一对情窦初开的少男少女，就这杨彼此爱慕，倾心相许……本以为，他们就可以这样长相厮守，再也不分开……"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="可是后来，这个少年不告而别？"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="鲛人"
	    	对话数据.名称="沈唐真身"
			对话数据.对话="哼！若是不告而别就好了！！他离开后不久，便有成批的采珠人来到沧浪海！蚌精与鲛人祥和的生活就这样被你们的贪欲所毁灭！这个少年，倒成了海内闻名的珍珠商人，家财万贯，富甲一方！最可笑的是，他竟然还给自己的女儿取名明珠！更因为乐善好施，成了人人称颂的大善人！"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
	    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
			对话数据.对话="于是你蛰伏二十余年……"
			副本处理类:发送全队对话信息(玩家id,对话数据)
	    	对话数据.模型="鲛人"
	    	对话数据.名称="沈唐真身"
			对话数据.对话="是啊，我用了二十多年的时间，接近他，取得他的信任，令他身陷囹圄，家财散尽。还要用他心爱女儿的鲜血，祭奠我的沧浪墟的族人，祭奠被他害死的明珠妹妹！"
			对话数据.选项={"住手！","可恶，还要准备一下！"}
			副本处理类:发送全队对话信息(玩家id,对话数据)
			return
		elseif 进程==12 then
	    	对话数据.模型="鲛人"
	    	对话数据.名称="沈唐真身"
			对话数据.对话="是啊，我用了二十多年的时间，接近他，取得他的信任，令他身陷囹圄，家财散尽。还要用他心爱女儿的鲜血，祭奠我的沧浪墟的族人，祭奠被他害死的明珠妹妹！"
			对话数据.选项={"住手！","可恶，还要准备一下！"}
		else
			对话数据.对话="明珠……我这一……生……究竟是对是错……"
		end
	elseif 名称=="戏班老板" then
    	对话数据.对话="戏已落幕，不知后事如何发展。就让我送少侠出去吧。"
	    对话数据.选项={"哎……送我出去吧！","我还想再呆一会……"}
    end
	副本处理类:发送对话信息(连接id,玩家id,对话数据)
end

function 一斛珠:对话处理(地图编号,名称,事件,玩家id)
	if 玩家数据[玩家id].队伍==0 or 玩家数据[玩家id].队长==false  then
		常规提示(玩家id,"#Y/该任务必须组队完成且由队长领取")
		return
	end
	--用于选项后的二级对话
	local 对话数据={}

	if 事件=="这就赶回四方城！" then
		self:设置副本进程(10)
		地图处理类:跳转地图(玩家id,self.地图编号,25,51)
		副本处理类:播放剧情动画(玩家id,7002)
	elseif 名称=="胡郎中" then
		if 事件=="测试1" then
		end
	elseif 名称=="偷看的小妞妞" and 事件=="过去凑凑热闹" then
		副本处理类:播放剧情动画(玩家id,7001)
		self:设置副本进程(2)
	elseif 名称=="鸾儿" then
		if 事件=="鸾儿姑娘！休要如此鲁莽！" and self.进程==2 then
			战斗准备类:创建战斗(玩家id,100301,0)
		end
	elseif 名称=="叶夫人" then
		if 事件=="叶夫人！你不要冲动" and self.进程==3 then
			战斗准备类:创建战斗(玩家id,100302,0)
		end
	elseif 名称=="崔明珠" or 名称=="沈唐" then
		if 事件=="这就出发！" then
			地图处理类:跳转地图(玩家id,self.地图编号,26,46)
			if self.进程==4 then
				self:设置副本进程(5)
			end
		elseif self.进程==8 and 事件=="拿香粉给崔明珠看看" then
			if self.对话进程 == 2 then
				self.对话进程 = 3
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="这味道……确实是我家的“一斛珠”香粉没错……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="可是……不对……还是有些微妙的不同！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="你拿来我看看！这……怎么会这样！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="都怪我……都怪我……是我不该……把这个旧时方子告诉岳父大人啊#52"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型=玩家数据[玩家id].角色.数据.造型
		    	对话数据.名称=玩家数据[玩家id].角色.数据.名称
				对话数据.对话="#24？！"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="在下的父亲，本是个郎中。我十岁那年，我的父母一道上山采药，竟双双跌落悬崖，命殒当场。只余我一人，孤苦无依。幸得机缘巧合得岳父大人收留。在下家中本就贫寒，迎娶明珠时，也拿不出什么聘礼，只有这“一斛珠”香粉的方子，倒是我父亲苦心钻研多年的成果，只是……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="只是什么？"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="这香粉乃是以珍珠所制，其效果很是依赖珍珠的品相。今年货源有限，珍珠品相并不好，故此配成的香粉效果大不如前，销量一落千丈。岳父大人为此甚是着急，我突然想到，父亲曾告诉我，若是将配方中的鬼鲸涏换为白银散，不仅可以大大降低成本，还可以让香粉效果更好。但是……这个方法并不成熟，对于部分体质特殊的人来说，就会引起皮肤溃烂，乃至神智失常……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="鸾儿姑娘和叶夫人症状……正是如此……."
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
		    	对话数据.模型="逍遥生"
		    	对话数据.名称="沈唐"
				对话数据.对话="你们看，加了白银散的‘一斛珠’，粉末没那么细腻，味道也稍稍浓郁些……"
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="无论如何，我也要见父亲一面！少侠，可否请您前往城里的医馆，帮我去海老人的医馆买些胭脂草吧。我想给父亲做些红花糕……"
				对话数据.选项={"嗯，必不负所托！","待我准备一番"}
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    		return
	    	else
				对话数据.模型="英女侠"
		    	对话数据.名称="崔明珠"
				对话数据.对话="无论如何，我也要见父亲一面！少侠，可否请您前往城里的医馆，帮我去海老人的医馆买些胭脂草吧。我想给父亲做些红花糕……"
				对话数据.选项={"嗯，必不负所托！","待我准备一番"}
	    		副本处理类:发送全队对话信息(玩家id,对话数据)
	    		return
    		end
    	elseif self.进程==8 and 事件=="嗯，必不负所托！" then
    		self:设置副本进程(9)
    		地图处理类:跳转地图(玩家id,self.地图编号,110,72)
    	elseif self.进程==10 and 事件=="废话少说！看招！" then
    		战斗准备类:创建战斗(玩家id,100304,0)
		end
	elseif 名称=="看门狗儿" then
		if 事件=="给它一个包子" then
			地图处理类:跳转地图(玩家id,7003,27,41)
			self:设置副本进程(6)
		end
	elseif 名称=="官差" then
		if 事件=="受人所托，实在得罪了！" then
			战斗准备类:创建战斗(玩家id,100303,0)
		end
	elseif 名称=="垫脚的箱子" then
		if 事件=="这就出发！" then
			地图处理类:跳转地图(玩家id,self.地图编号,224,23)
		end
	elseif 名称=="袁天罡" then
		if 事件=="快带我过去！" then
			地图处理类:跳转地图(玩家id,7004,62,67)
			self:设置副本进程(12)
		end
	elseif 名称=="沈唐真身" then
		if 事件=="住手！" and self.进程==12 then
			战斗准备类:创建战斗(玩家id,100306,0)
		end
	elseif 名称=="戏班老板" then
		if 事件=="哎……送我出去吧！" then
			副本处理类:结束副本("一斛珠",self.副本ID,玩家id)
		end
	end
end

function 一斛珠:添加玩家(id)
	self.副本玩家[#self.副本玩家 + 1]=id
end

function 一斛珠:完成副本(玩家id)
	副本数据.一斛珠.完成[玩家id] = os.time()
	地图处理类:跳转地图(玩家id,1501,87,29)
end

function 一斛珠:更新(dt)

end

function 一斛珠:显示()

end

function 一斛珠:获取地图ID()
	return self.地图编号
end

return 一斛珠