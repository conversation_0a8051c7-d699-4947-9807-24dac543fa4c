
local 管理工具类 = class()

function 管理工具类:初始化()
  self.玩家账号 = ""
  self.读入数据 =0

end



 function 管理工具类:发送数据(id,序号,内容)
   if id~=nil then
      if 内容==nil then
        内容="绝世修改"
      end
      if id == nil then
        return
      end
     local 组合内容={序号=序号,内容=内容}

     __S服务:发送(id,网络处理类:jm(table.tostring(组合内容)))
   end
end

function 管理工具类:数据处理(id,aaa)
--aaa=table.loadstring(aaa)
local 内容 =网络处理类:jm1(aaa[1])
if 内容==nil or 内容=="" then
    return
end
local 数据=分割文本(内容,fgf)

if 数据[1]==nil or  tonumber(数据[1])==nil or tonumber(数据[1])<=0 then
   self:发送数据(id,999,"滚你麻痹")
   __S服务:断开连接(id)
   __C客户信息[id]=nil
     return
end
local 序号=数据[1]+0
local 临时数据=table.loadstring(数据[2])

if 序号~=1 then
      if 数据[3]==nil or 数据[3]=="" or  f函数.读配置(程序目录..[[data\]]..数据[3]..[[\账号信息.txt]],"账号配置","管理")~="16888" then
            self:发送数据(id,999,"你的账号尚未开通此工具的使用权限")
            __S服务:断开连接(id)
            __C客户信息[id]=nil
            return
      end
      -- if 服务端参数.授权账号 and 服务端参数.授权账号~="" and 服务端参数.授权账号~=self.数据[3] then
      --       self:发送数据(self.连接id,999,"你的账号尚未开通此工具的使用权限")
      --       __S服务:断开连接(self.连接id)
      --       __C客户信息[self.连接id]=nil
      --       return
      -- end
end




  if __C客户信息[id].管理==nil or 序号==1 then --认证账号
    local 账号 = 临时数据.账号
    local 密码 = 临时数据.密码
    if f函数.文件是否存在([[data/]]..账号)==false then
        self:发送数据(id,999,"这样的账号并不存在")
      __S服务:断开连接(id)
       __C客户信息[id]=nil
      return
    elseif 密码~=f函数.读配置(程序目录..[[data\]]..账号..[[\账号信息.txt]],"账号配置","密码") then
          self:发送数据(id,999,"密码错误")
          __S服务:断开连接(id)
          __C客户信息[id]=nil
          return
    elseif f函数.读配置(程序目录..[[data\]]..账号..[[\账号信息.txt]],"账号配置","管理")~="16888" then
          self:发送数据(id,999,"你的账号尚未开通此工具的使用权限")
          __S服务:断开连接(id)
          __C客户信息[id]=nil
          return
    -- elseif 服务端参数.授权账号 and 服务端参数.授权账号~="" and 服务端参数.授权账号~=账号 then
    --       self:发送数据(self.连接id,999,"你的账号尚未开通此工具的使用权限")
    --       __S服务:断开连接(self.连接id)
    --       __C客户信息[self.连接id]=nil
    --       return
    else
        __C客户信息[id].认证=nil
        __C客户信息[id].管理=1
        __C客户信息[id].点数=f函数.读配置(程序目录..[[data\]]..账号..[[\账号信息.txt]],"账号配置","点数")+0
        __C客户信息[id].账号=账号
        self:发送数据(id,1,__C客户信息[id].管理.."@-@"..__C客户信息[id].点数.."@-@"..__C客户信息[id].账号)
        self:发送数据(id,7,"#Y/登录成功")
        self:发送数据(id,13,__C客户信息[id].账号)
        return
    end
    return 0
  end

  if __C客户信息[id].管理~=nil and __C客户信息[id].管理>0  then
    print(id)
    table.print(临时数据)
      self.工具账号=__C客户信息[id].账号
      if 序号==2  then
        self:充值操作(id,临时数据)
      elseif 序号==3 then
        self:账号操作(id,临时数据)
      elseif 序号==4 then
        self:定制装备(id,临时数据)
      elseif 序号==5 then
        self:定制灵饰(id,临时数据)
      elseif 序号==6 then
        self:游戏管理(id,临时数据)
      elseif 序号==7 then
        self:角色管理(id,临时数据)
      elseif 序号==8 then
        self:宝宝管理(id,临时数据)
      elseif 序号==9 then
        self:给予道具(id,临时数据)
      elseif 序号==10 then
        self:修改词条(id,临时数据)
      end

  end

end


function 管理工具类:给予道具(连接id,数据)
  if 数据.文本=="给予道具" or 数据.文本=="给予宝石" then
        if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
            self:发送数据(连接id,7,"#Y/玩家id错误")
            return
        end
        local 玩家id = tonumber(数据.玩家id)
        if 玩家数据[玩家id] == nil then
          self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
          return
        end
        local 临时格子=玩家数据[玩家id].角色:取道具格子()
        if 临时格子==0 then
            self:发送数据(连接id,7,"#Y/该玩家没有足够的空间存放物品")
            return
        end
      if 数据.文本=="给予道具" then
            if 数据.给予数据.数量 ==nil then 数据.给予数据.数量 = 1 end
            if 数据.给予数据.名称 == "制造指南书" or 数据.给予数据.名称 == "百炼精铁"  then
                  local 道具参数 = 数据.给予数据.参数
                  if tonumber(数据.给予数据.参数) ==nil then
                      local 临时范围 = {"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
                      for i=1,#临时范围 do
                        if 数据.给予数据.参数 == 临时范围[i] then
                          道具参数 = i
                        end
                      end
                  else
                      道具参数 = tonumber(数据.给予数据.参数)
                  end

                  if 道具参数==nil or 道具参数=="" or 道具参数 == 0 or  道具参数 =="0" or 道具参数 > 25 then
                      self:发送数据(连接id,7,"#Y/参数输入错误")
                      return
                  end
                  玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,tonumber(数据.给予数据.数量),道具参数)
                  常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."级#R/"..数据.给予数据.名称)
            elseif 数据.给予数据.名称 == "灵饰指南书" or 数据.给予数据.名称 == "元灵晶石"   then
                    local 道具参数 = "手镯"
                    if tonumber(数据.给予数据.参数)==nil then
                        local 临时范围 = {"手镯","佩饰","戒指","耳饰"}
                        for i=1,#临时范围 do
                           if 数据.给予数据.参数 == 临时范围[i] then
                              道具参数 = 临时范围[i]
                           end
                        end
                    else
                          if tonumber(数据.给予数据.参数) == 1 then
                              道具参数 = "手镯"
                          elseif tonumber(数据.给予数据.参数) == 2 then
                                道具参数 = "佩饰"
                          elseif tonumber(数据.给予数据.参数) == 3 then
                                道具参数 = "戒指"
                          elseif tonumber(数据.给予数据.参数) == 4 then
                                道具参数 = "耳饰"
                          end
                    end

                    玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,{math.floor(tonumber(数据.给予数据.数量)/10),math.floor(tonumber(数据.给予数据.数量)/10)},道具参数)
                    常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."级#R/"..数据.给予数据.名称)
             elseif 数据.给予数据.名称 == "鬼谷子"   then
                    local 道具参数 =  "天覆阵"
                    if tonumber(数据.给予数据.参数)==nil then
                        local 临时范围={"风扬阵","虎翼阵","天覆阵","云垂阵","鸟翔阵","地载阵","龙飞阵","蛇蟠阵","鹰啸阵","雷绝阵"}
                        for i=1,#临时范围 do
                           if 数据.给予数据.参数 == 临时范围[i] then
                              道具参数 = 临时范围[i]
                           end
                        end
                    else
                       if tonumber(数据.给予数据.参数) == 1 then
                          道具参数 = "天覆阵"
                        elseif tonumber(数据.给予数据.参数) == 2 then
                          道具参数 = "虎翼阵"
                        elseif tonumber(数据.给予数据.参数) == 3 then
                          道具参数 = "风扬阵"
                        elseif tonumber(数据.给予数据.参数) == 4 then
                          道具参数 = "云垂阵"
                        elseif tonumber(数据.给予数据.参数) == 5 then
                          道具参数 = "鸟翔阵"
                        elseif tonumber(数据.给予数据.参数) == 6 then
                          道具参数 = "地载阵"
                        elseif tonumber(数据.给予数据.参数) == 7 then
                          道具参数 = "龙飞阵"
                        elseif tonumber(数据.给予数据.参数) == 8 then
                          道具参数 = "蛇蟠阵"
                        elseif tonumber(数据.给予数据.参数) == 9 then
                          道具参数 = "鹰啸阵"
                        elseif tonumber(数据.给予数据.参数) == 10 then
                          道具参数 = "雷绝阵"
                        end
                    end
                    玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,nil,道具参数)
                    常规提示(玩家id,"#Y/你获得了"..道具参数)
             elseif 数据.给予数据.名称=="炼妖石" or 数据.给予数据.名称=="上古锻造图策" then
                      local 道具参数 =  "项圈"
                      if tonumber(数据.给予数据.参数)==nil then
                         local 临时范围={"项圈","护腕","铠甲"}
                          for i=1,#临时范围 do
                             if 数据.给予数据.参数 == 临时范围[i] then
                                道具参数 = 临时范围[i]
                             end
                           end
                      else
                         if tonumber(数据.给予数据.参数) == 1 then
                            道具参数 = "项圈"
                          elseif tonumber(数据.给予数据.参数) == 2 then
                            道具参数 = "护腕"
                          elseif tonumber(数据.给予数据.参数) == 3 then
                            道具参数 = "铠甲"
                          end
                      end
                      玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,{math.floor(tonumber(数据.给予数据.数量))},道具参数)
                      常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."级#R/"..数据.给予数据.名称)
            elseif 数据.给予数据.名称=="钨金" or 数据.给予数据.名称=="附魔宝珠" or 数据.给予数据.名称=="天眼珠"  or 数据.给予数据.名称=="超级附魔宝珠" then
                     玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,{math.floor(tonumber(数据.给予数据.数量))},tonumber(数据.给予数据.参数))
                     常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."级#R/"..数据.给予数据.名称)
            elseif 数据.给予数据.名称=="魔兽要诀" or 数据.给予数据.名称=="高级魔兽要诀" or 数据.给予数据.名称=="超级魔兽要诀" or 数据.给予数据.名称=="特殊魔兽要诀"  or 数据.给予数据.名称=="召唤兽内丹"  or 数据.给予数据.名称=="高级召唤兽内丹" then
                    for i=1,tonumber(数据.给予数据.数量) do
                        玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,1,数据.给予数据.参数)
                    end
                    常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."个#R/"..数据.给予数据.名称)
           else
                  local 道具参数 =  数据.给予数据.参数
                  if tonumber(数据.给予数据.参数)==nil then
                     道具参数 =  数据.给予数据.参数
                  else
                     道具参数 =  tonumber(数据.给予数据.参数)
                  end
                  玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,tonumber(数据.给予数据.数量),道具参数)
                  常规提示(玩家id,"#Y/你获得了"..数据.给予数据.数量.."个#R/"..数据.给予数据.名称)
           end
      elseif 数据.文本=="给予宝石" then
                if 数据.给予数据.最大等级 == nil or tonumber(数据.给予数据.最大等级) == nil or tonumber(数据.给予数据.最大等级) == 0   then
                    数据.给予数据.最大等级 = tonumber(数据.给予数据.最小等级)
                end
                local 循环开始 =  tonumber(数据.给予数据.最小等级)
                local 循环结束 =  tonumber(数据.给予数据.最大等级)
                local 道具格子 = 0
                for i=1,100 do
                   if 玩家数据[玩家id].角色.数据.道具[i]==nil then
                      道具格子=道具格子+1
                   end
                end

                if 道具格子<循环结束 then
                  self:发送数据(连接id,7,"#Y/该玩家没有足够的空间存放物品")
                  return
                end

                for i=循环开始,循环结束 do
                  玩家数据[玩家id].道具:给予道具(玩家id,数据.给予数据.名称,i)
                end
                常规提示(玩家id,"#Y/你获得了"..循环开始.."到"..循环结束.."级的#R/"..数据.给予数据.名称)
      end
      self:发送数据(连接id,7,"#Y/给予玩家"..数据.给予数据.名称.."成功")
      self:发送数据(连接id,8,"给予玩家"..数据.给予数据.名称.."成功")
      local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具给予了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,数据.给予数据.名称,self.工具账号)
      添加充值日志(添加语句)
      共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)
  else
         if 数据.文本=="获取充值类型" then
            local 发送内容 = 取文件夹的所有名 (程序目录..[[\自动充值]])
            self:发送数据(连接id,12,发送内容)
            self:发送数据(连接id,7,"#Y/CDK充值类型获取成功")
            发送内容=nil
          elseif 数据.文本=="获取充值卡号" then
                  local 发送内容 = 取文件的所有名 (程序目录..[[\自动充值\]]..数据.生成文件..[[\]])
                  发送内容.卡号 = 11111111
                  self:发送数据(连接id,12,发送内容)
                  发送内容=nil
                  self:发送数据(连接id,7,"#Y/CDK充值卡号获取成功")
          elseif 数据.文本=="生成CDK卡号" then
                  if tonumber(数据.生成数据.数量) == nil then 数据.生成数据.数量 = 1 end
                  if tonumber(数据.生成数据.位数) == nil then 数据.生成数据.位数 = 1 end
                  local 范围1={"1","2","3","4","5","6","7","8","9","0","q","w","e","r","t","y","u","i","p","a","s","d","f","g","h","j","k","z","x","c","v","b","n","m","Q","W","E","R","T","Y","U","I","P","A","S","D","F","G","H","J","K","L","Z","X","C","V","B","N","M"}
                  local 生成CDK = ""
                  local 生成CDK文本 =""
                  local 生成CDK组={}
                  for n=1,tonumber(数据.生成数据.数量) do--数量
                        for i=1,tonumber(数据.生成数据.位数)do---位数
                            local x临时数据 = 范围1[取随机数(1,#范围1)]
                            生成CDK = 生成CDK..x临时数据
                        end
                        生成CDK组[#生成CDK组+1] = 生成CDK
                        生成CDK = ""
                  end
                  for n=1,#生成CDK组 do--个数
                    写出文件("自动充值/"..数据.生成文件.."/"..生成CDK组[n]..".txt",生成CDK组[n])
                    生成CDK文本 = 生成CDK文本.."\n"..生成CDK组[n]
                  end
                  写出文件("自动充值/"..数据.生成文件.."/"..取年月日(os.time()).."生成CDK.txt", 生成CDK文本)
                  self:发送数据(连接id,7,"#Y/CDK生成完毕请自行查看")
                  local 发送内容 = 取文件的所有名 (程序目录..[[\自动充值\]]..数据.生成文件..[[\]])
                  发送内容.卡号 = 11111111
                  self:发送数据(连接id,12,发送内容)
                  发送内容=nil
            elseif 数据.文本=="生成自定义CDK卡号" then
                   写出文件("自动充值/"..数据.生成文件.."/"..数据.生成数据.自定义..".txt",数据.生成数据.自定义)
                 --  写出文件("自动充值/"..数据.生成文件.."/"..取年月日(os.time()).."生成CDK.txt",数据.生成数据.自定义)
                  self:发送数据(连接id,7,"#Y/自定义CDK生成完毕请自行查看")
                  local 发送内容 = 取文件的所有名 (程序目录..[[\自动充值\]]..数据.生成文件..[[\]])
                  发送内容.卡号 = 11111111
                  self:发送数据(连接id,12,发送内容)
                  发送内容=nil
             elseif 数据.文本=="新建充值类型" then
                    if f函数.文件是否存在([[自动充值/]]..数据.生成文件)==false then
                        创建目录([[自动充值/]]..数据.生成文件)
                        self:发送数据(连接id,7,"#Y/创建完成请重新获取类型查看")
                    else
                        self:发送数据(连接id,7,"#Y/该目录已存在无需重复创建")
                    end
            elseif 数据.文本=="删除充值卡号" then
                    if f函数.文件是否存在([[自动充值/]]..数据.生成文件..[[/]]..数据.生成卡号..[[.txt]])==false then
                        self:发送数据(连接id,7,"#Y/未找到该卡号无法删除")
                    else
                        os.remove(程序目录..[[\自动充值\]]..数据.生成文件..[[\]]..数据.生成卡号..[[.txt]])
                        self:发送数据(连接id,7,"#Y/该卡号已删除请重新获取充值类型")
                        local 发送内容 = 取文件的所有名 (程序目录..[[\自动充值\]]..数据.生成文件..[[\]])
                        发送内容.卡号 = 11111111
                        self:发送数据(连接id,12,发送内容)
                        发送内容=nil
                    end
         end
  end




end

function 管理工具类:宝宝管理(连接id,数据)
        if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
            self:发送数据(连接id,7,"#Y/玩家id错误")
            return
        end
        local 玩家id = tonumber(数据.玩家id)
        if 玩家数据[玩家id] == nil then
            self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
            return
        end
        if 数据.文本=="获取宝宝信息" then
              self:发送数据(连接id,11,玩家数据[玩家id].召唤兽.数据)
              self:发送数据(连接id,7,"#Y/获取玩家召唤兽数据成功")

        elseif 数据.文本=="激活功德录" then
                if not  玩家数据[玩家id].角色.数据.功德录 then
                      玩家数据[玩家id].角色.数据.功德录={激活=false,九珠副={[1]={类型="伤害",数值=20},[2]={类型="气血",数值=98},[3]={类型="防御",数值=20},[4]={类型="速度",数值=20},[5]={类型="法术伤害",数值=20},[6]={类型="法术防御",数值=20}}}
                end
                if 玩家数据[玩家id].角色.数据.功德录.激活 then
                    self:发送数据(连接id,7,"#Y/该玩家功德录已激活")
                else
                       玩家数据[玩家id].角色.数据.功德录.激活=true
                       常规提示(玩家id,"你的功德录激活了")
                       self:发送数据(连接id,7,"#Y/激活该玩家功德录成功")
                end

        elseif 数据.文本=="修改功德录" then
                if not  玩家数据[玩家id].角色.数据.功德录 then
                      玩家数据[玩家id].角色.数据.功德录={激活=false,九珠副={[1]={类型="伤害",数值=20},[2]={类型="气血",数值=98},[3]={类型="防御",数值=20},[4]={类型="速度",数值=20},[5]={类型="法术伤害",数值=20},[6]={类型="法术防御",数值=20}}}
                end
                if not 玩家数据[玩家id].角色.数据.功德录.激活 then
                    self:发送数据(连接id,7,"#Y/该玩家功德录未激活")
                    return
                end
                if 数据.修改数据 == nil then
                    self:发送数据(连接id,7,"#Y/未修改玩家功德录数据")
                    return
                else
                    local 功德录类型={气血=600,伤害=180,防御=180,速度=60,穿刺等级=32,治疗能力=60,固定伤害=180,法术伤害=180,法术防御=180,气血回复效果=60,封印命中等级=60,抵抗封印等级=60,法术暴击等级=60,物理暴击等级=60,抗法术暴击等级=32,抗物理暴击等级=32}
                    local 日子数据="功德录:"
                    for i=1,6 do
                       if 数据.修改数据[i] and 数据.修改数据[i].类型 and 数据.修改数据[i].数额 and 功德录类型[数据.修改数据[i].类型] and tonumber(数据.修改数据[i].数额) and tonumber(数据.修改数据[i].数额)~=0 then
                          玩家数据[玩家id].角色.数据.功德录.九珠副[i]={类型=数据.修改数据[i].类型,数值=math.floor(数据.修改数据[i].数额)}
                          日子数据=日子数据.."修改第"..i.."条,类型:"..数据.修改数据[i].类型..",数值:"..数据.修改数据[i].数额..","
                       end
                    end
                    常规提示(玩家id,"你的功德录发生了变化,清重新打开看看")
                    self:发送数据(连接id,7,"#Y/玩家功德录修改完成")
                    local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具修改了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,日子数据,self.工具账号)
                    添加充值日志(添加语句)
                    共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)
                end
        elseif 数据.文本=="获取坐骑" then
                 self:发送数据(连接id,14,玩家数据[玩家id].角色.数据.坐骑列表)
                self:发送数据(连接id,7,"#Y/获取玩家坐骑数据成功")
        elseif 数据.文本=="坐骑修改" then
              if 数据.修改数据 == nil then
                  self:发送数据(连接id,7,"#Y/未修改玩家坐骑数据")
                  return
              end
              if not 数据.修改数据.编号  or not 玩家数据[玩家id].角色.数据.坐骑列表[tonumber(数据.修改数据.编号)] then
                 self:发送数据(连接id,7,"#Y/该玩家这个坐骑未找到")
                  return
              end

              local 技能数据={反震=1,吸血=1,反击=1,连击=1,飞行=1,感知=1,再生=1,冥思=1,慧根=1,必杀=1,幸运=1,神迹=1,招架=1,永恒=1,偷袭=1,毒=1,驱鬼=1,鬼魂术=1,魔之心=1,神佑复生=1,精神集中=1,法术连击=1,法术暴击=1,法术波动=1,土属性吸收=1,火属性吸收=1,水属性吸收=1}
              local 坐骑序列 = tonumber(数据.修改数据.编号)
              local 添加技能 = {}
              if 数据.修改数据.技能数据 then
                 for i,v in ipairs(数据.修改数据.技能数据) do
                     if 技能数据[v] then
                       table.insert(添加技能, v)
                     end
                 end
                 添加技能=删除重复(添加技能)
              end
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].技能 = 添加技能
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].技能等级={}
              if not 数据.修改数据.成长 or not tonumber(数据.修改数据.成长) or 数据.修改数据.成长=="" or 数据.修改数据.成长=="0" or 数据.修改数据.成长==0 then
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].成长 = 1.05
              else
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].成长 = tonumber(数据.修改数据.成长)
              end
              if not 数据.修改数据.等级 or not tonumber(数据.修改数据.等级) or 数据.修改数据.等级=="" or 数据.修改数据.等级=="0" or 数据.修改数据.等级==0 then
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级 = 0
              else
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级 = tonumber(数据.修改数据.等级)
              end
              if not 数据.修改数据.技能点 or not tonumber(数据.修改数据.技能点) or 数据.修改数据.技能点=="" or 数据.修改数据.技能点=="0" or 数据.修改数据.技能点==0 then
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].技能点 = math.ceil(玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级/20)
              else
                  玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].技能点 = tonumber(数据.修改数据.技能点)
              end
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].潜力 = (玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级 + 1)*5
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].体质 = 10+ 玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].魔力 = 10+ 玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].力量 = 10+ 玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].耐力 = 10+ 玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级
              玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].敏捷 = 10+ 玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].等级
              玩家数据[玩家id].角色:坐骑刷新(坐骑序列)
              常规提示(玩家id,"你的"..玩家数据[玩家id].角色.数据.坐骑列表[坐骑序列].名称.."技能已修改")
              self:发送数据(连接id,7,"#Y/玩家坐骑修改完成")
             local 日志技能=""
             for i,v in ipairs(添加技能) do
               日志技能=日志技能..v..","
             end
             local 日子数据 = "坐骑技能,坐骑编号:"..坐骑序列.."技能点:"..数据.修改数据.技能点..",修改技能:"..日志技能
             local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具修改了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,日子数据,self.工具账号)
             添加充值日志(添加语句)
             共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)


        elseif 数据.文本=="定制宝宝装备" then
                if 数据.装备数据 == nil then
                  self:发送数据(连接id,7,"#Y/未修改玩家召唤兽数据")
                  return
                end
                local 道具格子 = 玩家数据[玩家id].角色:取道具格子()
                if 道具格子 == 0 then
                     self:发送数据(连接id,7,"#Y/道具栏已经满了,保留至少一格以上的位置哦")
                    return
                end
                local 装备等级 = tonumber(数据.装备数据.等级) or 0
                if 装备等级<10 then
                    装备等级 = 装备等级 + 5
                elseif 装备等级/10 == math.floor(装备等级/10) then
                        装备等级 = 装备等级+5
                end
                if 装备等级>=155 then
                    装备等级=155
                end
                local 类型序列=0
                if 数据.装备数据.主类型=="护腕" then
                        类型序列=24
                elseif 数据.装备数据.主类型=="项圈" then
                        类型序列=25
                elseif 数据.装备数据.主类型=="铠甲" then
                        类型序列=26
                end
                local 临时序列=math.ceil(装备等级/10)
                local 临时名称 = 绑定等级物品()
                local 道具 = 物品类()
                道具:置对象(临时名称[类型序列][临时序列])
                道具.级别限制 = 装备等级
                if 数据.装备数据.主类型=="护腕" then
                        道具.命中=math.floor(取随机数(装备等级*0.25+5,装备等级+10)+取随机数(10,40))
                        if 数据.装备数据.属性 and tonumber(数据.装备数据.属性) and tonumber(数据.装备数据.属性)~=0 then
                            道具.命中=tonumber(数据.装备数据.属性)
                        end
                elseif 数据.装备数据.主类型=="项圈" then
                        道具.速度=math.floor(取随机数(装备等级*0.25+5,装备等级+10)+取随机数(10,40))
                        if 数据.装备数据.属性 and tonumber(数据.装备数据.属性) and tonumber(数据.装备数据.属性)~=0 then
                            道具.速度=tonumber(数据.装备数据.属性)
                        end
                elseif 数据.装备数据.主类型=="铠甲" then
                        道具.防御=math.floor(取随机数(装备等级*0.25+5,装备等级+10)+取随机数(10,40))
                        if 数据.装备数据.属性 and tonumber(数据.装备数据.属性) and tonumber(数据.装备数据.属性)~=0 then
                            道具.防御=tonumber(数据.装备数据.属性)
                        end
                end
                if 数据.装备数据.类型1 and 数据.装备数据.类型1 ~="" and 数据.装备数据.属性1 and  tonumber(数据.装备数据.属性1) and tonumber(数据.装备数据.属性1)~=0 then
                    道具[数据.装备数据.类型1]=tonumber(数据.装备数据.属性1)
                end
                if 数据.装备数据.类型2 and 数据.装备数据.类型2 ~="" and 数据.装备数据.属性2 and  tonumber(数据.装备数据.属性2) and tonumber(数据.装备数据.属性2)~=0 then
                    道具[数据.装备数据.类型2]=tonumber(数据.装备数据.属性2)
                end
                if 数据.装备数据.特效 and 数据.装备数据.特效 ~= "" then
                        if 数据.装备数据.特效 == 1 or 数据.装备数据.特效 =="1" or 数据.装备数据.特效 =="无级别" then
                            道具.第二特效="无级别限制"
                            道具.制造者 = 服务端参数.名称.."定制"
                        elseif 数据.装备数据.特效 == 2 or 数据.装备数据.特效 =="2" or 数据.装备数据.特效 =="专用" or 数据.装备数据.特效 =="绑定" then
                              道具.专用=玩家id
                              道具.不可交易=true
                              道具.制造者 = 玩家数据[玩家id].角色.数据.名称.."专用"
                        elseif 数据.装备数据.特效 == 3 or 数据.装备数据.特效 =="3" or 数据.装备数据.特效 =="无级别绑定" or 数据.装备数据.特效 =="无级别专用"  then
                                道具.第二特效="无级别限制"
                                道具.专用=玩家id
                                道具.不可交易=true
                                道具.制造者 = 玩家数据[玩家id].角色.数据.名称.."专用"
                        else
                             道具.第二特效="无级别限制"
                             道具.制造者 = 玩家数据[玩家id].角色.数据.名称.."强化打造"
                        end
                else
                    道具.制造者 = 玩家数据[玩家id].角色.数据.名称
                end
                道具.五行=取五行()
                道具.耐久度=1000
                道具.鉴定=true
                道具.识别码=取唯一识别码(玩家id)
                local 道具id=玩家数据[玩家id].道具:取新编号()
                玩家数据[玩家id].道具.数据[道具id]=道具
                玩家数据[玩家id].角色.数据.道具[道具格子]=道具id
                常规提示(玩家id,"你获得了#R"..道具.名称)
                道具刷新(玩家id)
                self:发送数据(连接id,7,"#Y/宝宝装备#R"..道具.名称.."#Y发送成功")
                local 日子数据 = "宝宝装备"..数据.装备数据.主类型..":"..道具.名称..",等级"..装备等级
                if 数据.装备数据.属性 and tonumber(数据.装备数据.属性) and tonumber(数据.装备数据.属性)~=0 then
                            日子数据=日子数据..",主属性:"..数据.装备数据.属性
                end
                if 数据.装备数据.类型1 and 数据.装备数据.类型1 ~="" and 数据.装备数据.属性1 and tonumber(数据.装备数据.属性1) and tonumber(数据.装备数据.属性1)~=0 then
                    日子数据=日子数据..",附加1:"..数据.装备数据.类型1..":数值"..数据.装备数据.属性1
                end
                if 数据.装备数据.类型2 and 数据.装备数据.类型2 ~="" and 数据.装备数据.属性2 and tonumber(数据.装备数据.属性2) and tonumber(数据.装备数据.属性2)~=0 then
                    日子数据=日子数据..",附加2:"..数据.装备数据.类型2..":数值"..数据.装备数据.属性2
                end
                local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具给予了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,日子数据,self.工具账号)
                添加充值日志(添加语句)
                共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)

        elseif 数据.文本=="确定修改" then
                if 数据.修改数据 == nil then
                    self:发送数据(连接id,7,"#Y/未修改玩家召唤兽数据")
                    return
                end
                if tonumber(数据.召唤兽编号) == nil or tonumber(数据.召唤兽编号)  == 0  then
                    self:发送数据(连接id,7,"#Y/未修改玩家召唤兽数据")
                    return
                end
                local 编号 =  tonumber(数据.召唤兽编号)
                if 玩家数据[玩家id].召唤兽.数据[编号] ==nil then
                    self:发送数据(连接id,7,"#Y/未修改玩家召唤兽数据")
                    return
                end
                local 修改内容=""
                if 数据.修改数据.属性~=nil then
                      for k,v in pairs(数据.修改数据.属性) do
                            if k ~= "种类" and k ~= "模型" then
                                if tonumber(v)==nil then
                                  self:发送数据(连接id,7,"#Y/数据输入错误")
                                  return
                                end
                                玩家数据[玩家id].召唤兽.数据[编号][k] = tonumber(v)
                            else
                                玩家数据[玩家id].召唤兽.数据[编号][k] = v
                                if v == "神兽" and 玩家数据[玩家id].召唤兽.数据[编号].天生技能==nil then
                                  玩家数据[玩家id].召唤兽.数据[编号].天生技能={}
                                end
                            end
                            修改内容=修改内容..k.."数值:"..v..","
                     end
                end

                if 数据.修改数据.技能~=nil then
                    for i = 1 ,20 do
                        if 数据.修改数据.技能[i]~= nil then
                           玩家数据[玩家id].召唤兽.数据[编号].技能[i] = 数据.修改数据.技能[i]
                           修改内容=修改内容.."技能:"..i.."修改为:"..数据.修改数据.技能[i]..","
                        end
                    end
                end

                if 数据.修改数据.天生~=nil and 玩家数据[玩家id].召唤兽.数据[编号].天生技能~=nil then
                    for i = 1 ,4 do
                        if 数据.修改数据.天生[i]~= nil then
                           玩家数据[玩家id].召唤兽.数据[编号].天生技能[i] = 数据.修改数据.天生[i]
                           玩家数据[玩家id].召唤兽.数据[编号].技能[#玩家数据[玩家id].召唤兽.数据[编号].技能+1] = 数据.修改数据.天生[i]
                           修改内容=修改内容.."天生技能:"..i.."修改为:"..数据.修改数据.天生[i]..","
                        end
                    end
                end

                玩家数据[玩家id].召唤兽.数据[编号].技能=删除重复(玩家数据[玩家id].召唤兽.数据[编号].技能)
                self:发送数据(连接id,7,"#Y/修改玩家召唤兽数据完成")
                local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具修改了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,修改内容,self.工具账号)
                添加充值日志(添加语句)
                共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)




        end



end






function 管理工具类:角色管理(连接id,数据)
  if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
      self:发送数据(连接id,7,"#Y/玩家id错误")
      return
  end
  local 玩家id = tonumber(数据.玩家id)
  if 玩家数据[玩家id] == nil then
    self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
    return
  end

if  数据.文本=="获取角色信息" then
  local 发送数据 ={}
  发送数据.修炼 = 玩家数据[玩家id].角色.数据.修炼
  发送数据.bb修炼 = 玩家数据[玩家id].角色.数据.bb修炼
  发送数据.生活技能 = {}
  发送数据.bb修炼.玩家等级 ={[1]=玩家数据[玩家id].角色.数据.等级}
  for n=1,#玩家数据[玩家id].角色.数据.辅助技能 do
    发送数据.生活技能[玩家数据[玩家id].角色.数据.辅助技能[n].名称] = 玩家数据[玩家id].角色.数据.辅助技能[n].等级
  end
  发送数据.强化技能 = {}
  for n=1,#玩家数据[玩家id].角色.数据.强化技能 do
    发送数据.强化技能[玩家数据[玩家id].角色.数据.强化技能[n].名称] = 玩家数据[玩家id].角色.数据.强化技能[n].等级
  end

 self:发送数据(连接id,10,发送数据)

elseif 数据.文本=="恢复角色道具" then
        local 恢复数量 =  玩家数据[玩家id].道具:取恢复物品数量(玩家id)
        if 恢复数量 == 0 then
            self:发送数据(连接id,7,"#Y/玩家没有可恢复的道具")
            return
        end
        local 格子数量 =  玩家数据[玩家id].道具仓库:取剩余格子数量()
        if 恢复数量>格子数量 then
             self:发送数据(连接id,7,"#Y/玩家仓库剩余格子数量不足,仓库剩余格子:#R/"..格子数量.."#Y,本次恢复需求格子:#R/"..恢复数量)
        else
            玩家数据[玩家id].道具:恢复物品(玩家id)
            self:发送数据(连接id,7,"#Y/恢复完成请告知玩家注意查看聊天栏")
        end
      return
elseif  数据.文本=="确定修改" then
 if 数据.修改数据 == nil then
    self:发送数据(连接id,7,"#Y/未修改玩家数据")
    return
  end

    local 修改内容=""
      if 数据.修改数据.角色修炼~=nil then
         for k,v in pairs(数据.修改数据.角色修炼) do
          if tonumber(v)==nil  then
            self:发送数据(连接id,7,"#Y/数据输入错误")
            return
          end
          玩家数据[玩家id].角色.数据.修炼[k][1] = tonumber(v)
          if 玩家数据[玩家id].角色.数据.修炼[k][3]<=tonumber(v) then
            玩家数据[玩家id].角色.数据.修炼[k][3] = tonumber(v)
          end
          修改内容=修改内容..k.."数值:"..tonumber(v)..","
         end
      end

      if 数据.修改数据.角色生活~=nil then
         for k,v in pairs(数据.修改数据.角色生活) do
            if tonumber(v)==nil  then
              self:发送数据(连接id,7,"#Y/数据输入错误")
              return
            end
            for n=1,#玩家数据[玩家id].角色.数据.辅助技能 do
              if 玩家数据[玩家id].角色.数据.辅助技能[n].名称 == k then
                 玩家数据[玩家id].角色.数据.辅助技能[n].等级 = tonumber(v)
              end
            end
            修改内容=修改内容..k.."数值:"..tonumber(v)..","
         end
      end

    if 数据.修改数据.角色强化~=nil then
       for k,v in pairs(数据.修改数据.角色强化) do
          if tonumber(v)==nil then
            self:发送数据(连接id,7,"#Y/数据输入错误")
            return
          end
          for n=1,#玩家数据[玩家id].角色.数据.强化技能 do
            if 玩家数据[玩家id].角色.数据.强化技能[n].名称 == k then
               玩家数据[玩家id].角色.数据.强化技能[n].等级 = tonumber(v)
            end
          end
          修改内容=修改内容..k.."数值:"..tonumber(v)..","
       end
    end

    if 数据.修改数据.召唤兽修炼~=nil then
       for k,v in pairs(数据.修改数据.召唤兽修炼) do
        if tonumber(v)==nil then
          self:发送数据(连接id,7,"#Y/数据输入错误")
          return
        end
        if k == "玩家等级" then
            玩家数据[玩家id].角色.数据.等级 = tonumber(v)
            玩家数据[玩家id].角色.数据.加点记录 = {体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
            if  玩家数据[玩家id].角色.数据.飞升 then
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.等级*5+100
            else
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.等级*5+5
            end
            if 玩家数据[玩家id].角色.数据.五虎上将 ~= nil then
              if 玩家数据[玩家id].角色.数据.五虎上将 == 1 then
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 10
              elseif 玩家数据[玩家id].角色.数据.五虎上将 == 2 then
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 30
              elseif 玩家数据[玩家id].角色.数据.五虎上将 == 3 then
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 60
              elseif self.数据.五虎上将 >= 4 then
                玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 100
              end
            end
            if 玩家数据[玩家id].角色.数据.潜能果 ~= nil then
              玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 玩家数据[玩家id].角色.数据.潜能果
            end
            if 玩家数据[玩家id].角色.数据.月饼 ~= nil then
               玩家数据[玩家id].角色.数据.潜力 = 玩家数据[玩家id].角色.数据.潜力 + 玩家数据[玩家id].角色.数据.月饼*2
            end
            玩家数据[玩家id].角色:刷新信息("1")
        else
            玩家数据[玩家id].角色.数据.bb修炼[k][1] = tonumber(v)
            if 玩家数据[玩家id].角色.数据.bb修炼[k][3]<=tonumber(v) then
              玩家数据[玩家id].角色.数据.bb修炼[k][3] = tonumber(v)
            end
         end
        修改内容=修改内容..k.."数值:"..tonumber(v)..","
       end

    end
    self:发送数据(连接id,7,"#Y/修改玩家数据完成")
    local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具修改了 %s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,修改内容,self.工具账号)
    添加充值日志(添加语句)
    共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)







end

end


function 管理工具类:充值操作(连接id,内容)
    if 内容.文本=="八卦设置" then
    if 炼丹炉 and  炼丹炉.转盘时间<=6 then
       self:发送数据(连接id,7,"#Y/奖励已固定无法更改")
       return
    end
     if 炼丹炉 then
        炼丹炉.开奖控制=内容.数额
        self:发送数据(连接id,7,"#Y/本次八卦炉开奖已改为#R/"..内容.数额)
      end
    return
  end

  if 内容.文本=="充值记录" then
      self:发送数据(连接id,7,"#Y/充值记录自行上服务器查看")

  else
      if 内容.玩家id==nil or  tonumber(内容.玩家id)==nil or tonumber(内容.玩家id)<=0 then
            self:发送数据(连接id,7,"#Y/玩家id错误")
             return
      end
      local 玩家id = tonumber(内容.玩家id)
      if 内容.数额==nil or  tonumber(内容.数额)==nil or tonumber(内容.数额)<=0 then
            self:发送数据(连接id,7,"#Y/充值金额错误")
             return
      end
      local 充值金额= tonumber(内容.数额)
      if 玩家数据[玩家id] == nil then
        self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
        return
      end
      if 内容.文本=="充值仙玉" then
           if 共享货币[玩家数据[玩家id].账号] then
              共享货币[玩家数据[玩家id].账号]:添加仙玉(充值金额,玩家id,"GM工具")
          end
      elseif 内容.文本=="充值点卡" then
          if 共享货币[玩家数据[玩家id].账号] then
              共享货币[玩家数据[玩家id].账号]:添加点卡(充值金额,玩家id,"GM工具")
          end
      elseif 内容.文本=="充值银子" then
        玩家数据[玩家id].角色:添加银子(充值金额,"GM工具",1)
      elseif 内容.文本=="充值储备" then
        玩家数据[玩家id].角色.数据.储备 = 玩家数据[玩家id].角色.数据.储备 +充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的储备")
      elseif 内容.文本=="充值经验" then
        玩家数据[玩家id].角色.数据.当前经验 = 玩家数据[玩家id].角色.数据.当前经验 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的经验")
      elseif 内容.文本=="充值累充" then
          if 共享货币[玩家数据[玩家id].账号] then
              共享货币[玩家数据[玩家id].账号]:添加累充(充值金额,玩家id,"GM工具")
          end
      elseif 内容.文本=="充值帮贡" then
        添加帮贡(玩家id,充值金额)
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的帮贡")
      elseif 内容.文本=="充值门贡" then
         玩家数据[玩家id].角色.数据.门贡=玩家数据[玩家id].角色.数据.门贡 + 充值金额
         常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的门贡")
      elseif 内容.文本=="打造熟练" then
        玩家数据[玩家id].角色.数据.打造熟练度.打造技巧=玩家数据[玩家id].角色.数据.打造熟练度.打造技巧 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的打造熟练度")
      elseif 内容.文本=="裁缝熟练" then
        玩家数据[玩家id].角色.数据.打造熟练度.裁缝技巧=玩家数据[玩家id].角色.数据.打造熟练度.裁缝技巧 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的裁缝熟练度")
      elseif 内容.文本=="炼金熟练" then
        玩家数据[玩家id].角色.数据.打造熟练度.炼金术=玩家数据[玩家id].角色.数据.打造熟练度.炼金术 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的炼金熟练度")

      elseif 内容.文本=="淬灵熟练" then
          玩家数据[玩家id].角色.数据.打造熟练度.淬灵之术=玩家数据[玩家id].角色.数据.打造熟练度.淬灵之术 + 充值金额
          常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的淬灵之术熟练度")

      elseif 内容.文本=="活跃积分" then
        玩家数据[玩家id].角色.数据.活跃积分 = 玩家数据[玩家id].角色.数据.活跃积分 + 充值金额
        if 活跃数据[玩家id]==nil then
           活跃数据[玩家id]={活跃度=0}
        end
        活跃数据[玩家id].活跃度 = 活跃数据[玩家id].活跃度 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的活跃积分")
      elseif 内容.文本=="比武积分" then
        玩家数据[玩家id].角色.数据.比武积分.当前积分 = 玩家数据[玩家id].角色.数据.比武积分.当前积分 + 充值金额
        玩家数据[玩家id].角色.数据.比武积分.总积分 = 玩家数据[玩家id].角色.数据.比武积分.总积分 + 充值金额
        常规提示(玩家id,"#Y/你获得了#R/"..充值金额.."#Y/数额的比武积分")
      end
      self:发送数据(连接id,8,"#H/ID为#R/"..玩家id.."#H/的玩家添加#R/"..充值金额.."#H/数额的#R/"..内容.文本.."#H/成功")
      self:发送数据(连接id,7,"#Y/ID为#R/"..玩家id.."#Y/的玩家添加#R/"..充值金额.."#Y/数额的#R/"..内容.文本.."#Y/成功")
      local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具充值了%s数额的%s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,充值金额,内容.文本,self.工具账号)
      添加充值日志(添加语句)
      共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)
  end
end


function 管理工具类:账号操作(连接id,内容)
  if 内容.文本=="玩家信息" or  内容.文本=="踢出战斗" or  内容.文本=="强制下线" or  内容.文本=="给予称谓"   then
    if 内容.玩家id==nil or  tonumber(内容.玩家id)==nil or tonumber(内容.玩家id)<=0 then
            self:发送数据(连接id,7,"#Y/玩家id错误")
             return
    end
    local 玩家id = tonumber(内容.玩家id)
    if 玩家数据[玩家id] == nil then
      self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
      return
    end
      if 内容.文本=="玩家信息" then
        local  发送信息 = "玩家信息"
        发送信息=发送信息.."\n数字ID:"..玩家id
        发送信息=发送信息.."\n账号:"..玩家数据[玩家id].角色.数据.账号
        发送信息=发送信息.."\n名称:"..玩家数据[玩家id].角色.数据.名称
        发送信息=发送信息.."\n造型:"..玩家数据[玩家id].角色.数据.造型
        发送信息=发送信息.."\n仙玉:"..共享货币[玩家数据[玩家id].账号].仙玉
        发送信息=发送信息.."\n点卡:"..共享货币[玩家数据[玩家id].账号].点卡
        发送信息=发送信息.."\n当前累充:"..共享货币[玩家数据[玩家id].账号].充值当前
        发送信息=发送信息.."\n累计充值:"..共享货币[玩家数据[玩家id].账号].充值累计
        发送信息=发送信息.."\n比武积分:"..玩家数据[玩家id].角色.数据.比武积分.当前积分
        发送信息=发送信息.."\n活跃积分:"..玩家数据[玩家id].角色.数据.活跃积分
        发送信息=发送信息.."\n银子:"..玩家数据[玩家id].角色.数据.银子
        发送信息=发送信息.."\n存银:"..玩家数据[玩家id].角色.数据.存银
        发送信息=发送信息.."\n储备:"..玩家数据[玩家id].角色.数据.储备
        发送信息=发送信息.."\n体质:"..玩家数据[玩家id].角色.数据.体质
        发送信息=发送信息.."\n魔力:"..玩家数据[玩家id].角色.数据.魔力
        发送信息=发送信息.."\n力量:"..玩家数据[玩家id].角色.数据.力量
        发送信息=发送信息.."\n耐力:"..玩家数据[玩家id].角色.数据.耐力
        发送信息=发送信息.."\n敏捷:"..玩家数据[玩家id].角色.数据.敏捷
        if 玩家数据[玩家id].角色.数据.飞升 then
          发送信息=发送信息.."\n飞升:已飞升"
          else
          发送信息=发送信息.."\n飞升:未飞升"
        end
        if 玩家数据[玩家id].角色.数据.渡劫 then
          发送信息=发送信息.."\n渡劫:已渡劫"
          else
          发送信息=发送信息.."\n渡劫:未渡劫"
        end
        if 玩家数据[玩家id].角色.数据.化圣 then
          发送信息=发送信息.."\n化圣:已化圣"
          else
          发送信息=发送信息.."\n化圣:未化圣"
        end
        if 玩家数据[玩家id].角色.数据.多角色操作 then
          发送信息=发送信息.."\n多角色操作:已开启多角色"
        else
          发送信息=发送信息.."\n多角色操作:未开启多角色"
        end
        发送信息=发送信息.."\n当前称谓:"..玩家数据[玩家id].角色.数据.当前称谓
        发送信息=发送信息.."\n出生日期:"..时间转换(玩家数据[玩家id].角色.数据.出生日期)
      --  发送信息=发送信息.."\nIP:"..玩家数据[玩家id].ip
        if 玩家数据[玩家id].子角色操作 ~= nil then
           发送信息=发送信息.."\n#R/该玩家现处于无人操作，多角色虚拟上号状态,当前队长ID:"..玩家数据[玩家id].队伍
        end
      self:发送数据(连接id,8,发送信息)
      elseif 内容.文本=="踢出战斗" then
          if 玩家数据[玩家id].战斗~=0 and 玩家数据[玩家id].观战~=nil then
              local id组 = 取id组(玩家id)
              for i=1,#id组 do
                  if 玩家数据[id组[i]].观战 ~= nil then
                     if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                        战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
                     else
                            玩家数据[id组[i]].战斗=0
                            玩家数据[id组[i]].观战=nil
                            发送数据(玩家数据[id组[i]].连接id,5505)
                      end
                  end
              end
             self:发送数据(连接id,8,"#R/"..玩家数据[玩家id].角色.数据.名称.."#H/踢出观战成功")
            return
          end

          local  玩家名称 = 玩家数据[玩家id].角色.数据.名称
          if 玩家数据[玩家id].战斗~=0 then

            if 玩家数据[玩家id].队伍~=0 and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗]~=nil and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗].执行等待<0 and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:取玩家战斗()==false then
              战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:结束战斗处理(0,玩家数据[玩家id].队伍,1)
            else
              战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:结束战斗处理(0,玩家id,1)
            end
            self:发送数据(连接id,8,"#R/"..玩家名称.."#H/踢出战斗成功")
          else
            self:发送数据(连接id,8,"#R/"..玩家名称.."#H/踢出战斗失败,该玩家没有在战斗中")
          end


       elseif 内容.文本=="强制下线" then
                if 玩家数据[玩家id].战斗~=0 then
                  if 玩家数据[玩家id].队伍~=0 and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗]~=nil and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗].执行等待<0 and 战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:取玩家战斗()==false then
                    战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:结束战斗处理(0,玩家数据[玩家id].队伍,1)
                  else
                    战斗准备类.战斗盒子[玩家数据[玩家id].战斗]:结束战斗处理(0,玩家id,1)
                  end
                end
                local 玩家姓名 = 玩家数据[玩家id].角色.数据.名称
                 __S服务:发送(服务端参数.网关id,玩家数据[玩家id].连接id,table.tostring({序号=999,内容="您的该角色已被管理员强制下线，具体原因请联系管理员！"}))
                系统处理类:断开游戏(玩家id)
                玩家数据[玩家id]=nil
                self:发送数据(连接id,8,"#R/"..玩家姓名.."#H/强制下线成功")
       elseif 内容.文本=="给予称谓" then
              玩家数据[玩家id].角色:添加称谓(内容.坐骑名称)
              self:发送数据(连接id,8,"给予玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#H/称谓#R/"..内容.坐骑名称.."#H/成功")
              local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具充值了%s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,内容.坐骑名称,self.工具账号)
              添加充值日志(添加语句)
              共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)



      end
      self:发送数据(连接id,7,"#Y/ID为#R/"..玩家id.."#Y/的玩家操作#R/"..内容.文本.."#Y/成功")
  else

     self.玩家账号 = 内容.账号
    if f函数.文件是否存在([[data\]]..self.玩家账号)==false and 内容.文本~="封禁 I P" and 内容.文本~="解封 I P"  then
       self:发送数据(连接id,7,"#Y/未找到该账号")
      return
    end

        if 内容.文本=="封禁账号" then

        f函数.写配置(程序目录..[[data\]].. self.玩家账号..[[\账号信息.txt]],"账号配置","封禁","1")
        self.读入数据=读入文件([[data/]].. self.玩家账号..[[/信息.txt]])
        self.临时数据=table.loadstring(self.读入数据)
        for n=1,#self.临时数据 do
          local 下线id=self.临时数据[n]+0
          if 玩家数据[下线id]~=nil then
            if 玩家数据[下线id].战斗~=0 then
              if 玩家数据[下线id].队伍~=0 and 战斗准备类.战斗盒子[玩家数据[下线id].战斗]~=nil and 战斗准备类.战斗盒子[玩家数据[下线id].战斗].执行等待<0 and 战斗准备类.战斗盒子[玩家数据[下线id].战斗]:取玩家战斗()==false then
                战斗准备类.战斗盒子[玩家数据[下线id].战斗]:结束战斗处理(0,玩家数据[下线id].队伍,1)
              else
                战斗准备类.战斗盒子[玩家数据[下线id].战斗]:结束战斗处理(0,下线id,1)
              end
            end
            __S服务:发送(服务端参数.网关id,玩家数据[下线id].连接id,table.tostring({序号=999,内容="您的该角色已被管理员强制下线，具体原因请联系管理员！"}))
            系统处理类:断开游戏(下线id)
          end
        end
        self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/已被封禁,在线角色已强制下线")
        elseif 内容.文本=="解封账号" then
              local 临时文件=读入文件([[data/]]..self.玩家账号..[[/信息.txt]])
              local 写入信息=table.loadstring(临时文件)
              for n=1,#写入信息 do
                  local 读取文件=table.loadstring(读入文件([[data/]]..self.玩家账号..[[/]]..写入信息[n]..[[/角色.txt]]))
                  if 读取文件 and 读取文件.地图数据 and 读取文件.地图数据.编号 and tonumber(读取文件.地图数据.编号)==1003 then
                      读取文件.地图数据={编号=1001,x=191,y=104}
                       写出文件([[data/]]..self.玩家账号..[[/]]..写入信息[n]..[[/角色.txt]],table.tostring(读取文件))
                  end
              end
              f函数.写配置(程序目录..[[data\]].. self.玩家账号..[[\账号信息.txt]],"账号配置","封禁","0")
              self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/已解封,该玩家可以正常进入游戏")



        elseif 内容.文本=="封禁 I P" then
          ip封禁表[#ip封禁表+1]={ip=self.玩家账号,时间=时间转换(os.time()),关联账号={[1]="GM工具禁封"}}
          self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/已被封禁")
        elseif 内容.文本=="解封 I P" then
          local 序列=0
          for n=1,#ip封禁表 do
            if ip封禁表[n].ip==self.玩家账号 then
              序列=n
            end
          end
          if 序列==0 then
            self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/该ip地址并未在黑名单中。")
          else
            table.remove(ip封禁表,序列)
            self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/该ip地址已成功从服务器黑名单中移除。")
          end
        elseif 内容.文本=="开通管理" then
          f函数.写配置(程序目录..[[data\]].. self.玩家账号..[[\账号信息.txt]],"账号配置","管理","16888")
          self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/账号已添加管理权限")
        elseif 内容.文本=="关闭管理" then
          f函数.写配置(程序目录..[[data\]].. self.玩家账号..[[\账号信息.txt]],"账号配置","管理","0")
          self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/账号已解除管理权限")
        elseif 内容.文本=="修改密码" then
          if 内容.密码==nil or 内容.密码 =="" then
             self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/密码不能为空")
             return
          end
          f函数.写配置(程序目录..[[data\]].. self.玩家账号..[[\账号信息.txt]],"账号配置","密码",内容.密码)
          self:发送数据(连接id,8,"#R/"..self.玩家账号.."#H/密码已修改为:"..内容.密码)

        elseif 内容.文本=="发送路费" then
              local 发送信息 = 自定义数据.路费数据
              local 临时文件=读入文件([[data/]]..self.玩家账号..[[/信息.txt]])
              local 写入信息=table.loadstring(临时文件)
              local 已发id=""
              for n=1,#写入信息 do
                   local 发送id = 写入信息[n]
                   if 发送id and 玩家数据[发送id] then
                      if 发送信息.经验>0 then
                        玩家数据[发送id].角色.数据.当前经验 = 玩家数据[发送id].角色.数据.当前经验 + 发送信息.经验
                        常规提示(发送id,"#Y/你获得了#R/"..发送信息.经验.."#Y/点经验")
                      end
                      if 发送信息.储备>0 then
                        玩家数据[发送id].角色.数据.储备 = 玩家数据[发送id].角色.数据.储备 + 发送信息.储备
                        常规提示(发送id,"#Y/你获得了#R/"..发送信息.储备.."#Y/两储备")
                      end
                      if 发送信息.银子>0 then
                        玩家数据[发送id].角色.数据.银子 = 玩家数据[发送id].角色.数据.银子 + 发送信息.银子
                        常规提示(发送id,"#Y/你获得了#R/"..发送信息.银子.."#Y/两银子")
                      end
                      if 发送信息.抓鬼>0 then
                        玩家数据[发送id].角色.数据.自动抓鬼 = 玩家数据[发送id].角色.数据.自动抓鬼 + 发送信息.抓鬼
                        常规提示(发送id,"#Y/你获得了#R/"..发送信息.抓鬼.."#Y/次抓鬼")
                      end
                      if 发送信息.仙玉>0 then
                          共享货币[玩家数据[发送id].账号]:添加仙玉(发送信息.仙玉,发送id,"发送路费")
                      end
                      if 发送信息.点卡>0 then
                          共享货币[玩家数据[发送id].账号]:添加点卡(发送信息.点卡,发送id,"发送路费")
                      end
                      if 发送信息.物品数量>0 then
                          for i=1,发送信息.物品数量 do
                              local 物品名称 = 发送信息.物品[i].名称
                              local 数量 = tonumber(发送信息.物品[i].数量)
                              仙玉商城类:仙玉商城商品处理(发送id,物品名称,数量)
                              常规提示(发送id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
                          end
                      end
                      已发id=已发id..发送id..","
                   end
              end
              self:发送数据(连接id,8,"#R/给予账号"..self.玩家账号.."，#H/已给ID:"..已发id.."发送路费成功")



        end
        self:发送数据(连接id,7,"#Y/账号#R/"..self.玩家账号.."#Y/操作#R/"..内容.文本.."#Y/成功")
  end

end


function 管理工具类:定制装备(连接id,数据)
  if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
      self:发送数据(连接id,7,"#Y/玩家id错误")
      return
  end
  local 玩家id = tonumber(数据.玩家id)
  if 玩家数据[玩家id] == nil then
    self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
    return
  end

local  装备类型名称  = 数据.装备数据.类型
local  等级 = tonumber(数据.装备数据.等级)
local  内容 = 数据.装备数据


  if 装备类型名称==nil then
    self:发送数据(连接id,7,"#Y/添加失败!")
    return
  end
  if 等级 ==nil then
    self:发送数据(连接id,7,"#Y/添加失败!")
    return
  end

  local 临时格子=玩家数据[玩家id].角色:取道具格子()
  if 临时格子==0 then
    self:发送数据(连接id,7,"#Y/该玩家没有足够的空间存放物品")
    return 0
  end

  local 临时id = nil
  local 添加是否成功 = false
  if 等级>=100 then
    等级=等级/10
    等级=math.floor(等级)
  else
    等级=等级/10
    等级=math.floor(等级)
  end
  等级=math.floor(等级)
  local 模型=玩家数据[玩家id].角色.数据.模型

  if 装备类型名称=="武器" then
    local 武器序列=角色武器类型[模型][取随机数(1,#角色武器类型[模型])]
    local 武器名称=玩家数据[玩家id].装备.打造物品[武器序列][等级+1]
    if 等级>=9 and 等级<12 then
      武器名称=玩家数据[玩家id].装备.打造物品[武器序列][取随机数(10,12)]
    elseif 等级>=12 and 等级<15 then
      武器名称=玩家数据[玩家id].装备.打造物品[武器序列][取随机数(13,15)]
    end
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,武器名称,等级*10,武器序列)--武器
    添加是否成功 = true
  elseif 装备类型名称=="衣服" then
    local 衣服类型=2
    if 玩家数据[玩家id].角色.数据.性别=="女" then 衣服类型=1 end
    local 衣服序列=21
    local 衣服名称= 玩家数据[玩家id].装备.打造物品[衣服序列][等级+1][衣服类型]
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,衣服名称,等级*10,衣服序列)--衣服
    添加是否成功 = true

  elseif 装备类型名称=="头盔" then
    local 头盔类型=1
    if 玩家数据[玩家id].角色.数据.性别=="女" then 头盔类型=2 end
    local 头盔序列=19
    local 头盔名称= 玩家数据[玩家id].装备.打造物品[头盔序列][等级+1][头盔类型]
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,头盔名称,等级*10,头盔序列)--头盔
    添加是否成功 = true

  elseif 装备类型名称=="项链" then
    local 项链序列=20
    local 项链名称= 玩家数据[玩家id].装备.打造物品[项链序列][等级+1][取随机数(1,2)]
    if type(玩家数据[玩家id].装备.打造物品[项链序列][等级+1])=="table" then
      项链名称= 玩家数据[玩家id].装备.打造物品[项链序列][等级+1][取随机数(1,2)]
    else
      项链名称= 玩家数据[玩家id].装备.打造物品[项链序列][等级+1]
    end
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,项链名称,等级*10,项链序列)--项链
    添加是否成功 = true

  elseif 装备类型名称=="腰带" then
    local 腰带序列=22
    local 腰带名称= 玩家数据[玩家id].装备.打造物品[腰带序列][等级+1][取随机数(1,2)]
    if type(玩家数据[玩家id].装备.打造物品[腰带序列][等级+1])=="table" then
      腰带名称= 玩家数据[玩家id].装备.打造物品[腰带序列][等级+1][取随机数(1,2)]
    else
      腰带名称= 玩家数据[玩家id].装备.打造物品[腰带序列][等级+1]
    end
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,腰带名称,等级*10,腰带序列)--腰带
    添加是否成功 = true

  elseif 装备类型名称=="鞋子" then
    local 鞋子序列=23
    local 鞋子名称= 玩家数据[玩家id].装备.打造物品[鞋子序列][等级+1]
    临时id = 玩家数据[玩家id].装备:生成指定装备(玩家id,鞋子名称,等级*10,鞋子序列)--鞋子
    添加是否成功 = true
    if 玩家数据[玩家id].道具.数据[临时id].敏捷 ~= nil and 内容.敏捷~=nil then
      玩家数据[玩家id].道具.数据[临时id].敏捷 = tonumber(内容.敏捷)
    end
  end
   if 装备类型名称~="鞋子" then
      玩家数据[玩家id].道具.数据[临时id].敏捷 =nil
    end
    玩家数据[玩家id].道具.数据[临时id].体质 =nil
    玩家数据[玩家id].道具.数据[临时id].力量 =nil
    玩家数据[玩家id].道具.数据[临时id].耐力 =nil
    玩家数据[玩家id].道具.数据[临时id].魔力 = nil
    玩家数据[玩家id].道具.数据[临时id].特效=nil
    玩家数据[玩家id].道具.数据[临时id].专用=nil
    玩家数据[玩家id].道具.数据[临时id].不可交易=nil
    玩家数据[玩家id].道具.数据[临时id].制造者 = 服务端参数.名称
    玩家数据[玩家id].道具.数据[临时id].特技 = nil
  if 添加是否成功 and 临时id ~= nil then
    if 玩家数据[玩家id].道具.数据[临时id].命中 ~= nil and 内容.命中~=nil then
      玩家数据[玩家id].道具.数据[临时id].命中 = tonumber(内容.命中)
    end
    if 玩家数据[玩家id].道具.数据[临时id].伤害 ~= nil and 内容.伤害~=nil then
      玩家数据[玩家id].道具.数据[临时id].伤害 = tonumber(内容.伤害)
    end
    if 玩家数据[玩家id].道具.数据[临时id].防御 ~= nil and 内容.防御~=nil then
      玩家数据[玩家id].道具.数据[临时id].防御 = tonumber(内容.防御)
    end
    if 玩家数据[玩家id].道具.数据[临时id].魔法 ~= nil and 内容.魔法~=nil then
      玩家数据[玩家id].道具.数据[临时id].魔法 = tonumber(内容.魔法)
    end
    if 玩家数据[玩家id].道具.数据[临时id].灵力 ~= nil and 内容.灵力~=nil then
      玩家数据[玩家id].道具.数据[临时id].灵力 = tonumber(内容.灵力)
    end
    if 玩家数据[玩家id].道具.数据[临时id].气血 ~= nil and 内容.气血~=nil then
      玩家数据[玩家id].道具.数据[临时id].气血 = tonumber(内容.气血)
    end
    if 装备类型名称=="武器" or 装备类型名称=="衣服" then
      if 内容.敏捷~=nil then
        玩家数据[玩家id].道具.数据[临时id].敏捷 = tonumber(内容.敏捷)
      end
      if 内容.体质~=nil then
        玩家数据[玩家id].道具.数据[临时id].体质 = tonumber(内容.体质)
      end
      if 内容.力量~=nil then
        玩家数据[玩家id].道具.数据[临时id].力量 = tonumber(内容.力量)
      end
      if 内容.耐力~=nil then
        玩家数据[玩家id].道具.数据[临时id].耐力 = tonumber(内容.耐力)
      end
      if 内容.魔力~=nil then
        玩家数据[玩家id].道具.数据[临时id].魔力 = tonumber(内容.魔力)
      end
    end
    玩家数据[玩家id].道具.数据[临时id].耐久度 = 取随机数(400,800)
    玩家数据[玩家id].道具.数据[临时id].识别码=取唯一识别码(玩家id)


    if 内容.特效~=nil then
      玩家数据[玩家id].道具.数据[临时id].特效=内容.特效
    end
    if 内容.特效2~=nil then
      玩家数据[玩家id].道具.数据[临时id].第二特效=内容.特效2
    end
    if 内容.专用~=nil then
      玩家数据[玩家id].道具.数据[临时id].专用=玩家id
      玩家数据[玩家id].道具.数据[临时id].不可交易=true
    else
      玩家数据[玩家id].道具.数据[临时id].专用=nil
      玩家数据[玩家id].道具.数据[临时id].不可交易=nil
    end
    if 内容.制造~=nil then
      玩家数据[玩家id].道具.数据[临时id].制造者 = 内容.制造
    end
    if 内容.特技~=nil then
      玩家数据[玩家id].道具.数据[临时id].特技 = 内容.特技
    end
    常规提示(玩家id,"#Y/你获得了定制的装备:"..玩家数据[玩家id].道具.数据[临时id].名称)
    self:发送数据(连接id,7,"#Y/给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#Y/添加#R/"..玩家数据[玩家id].道具.数据[临时id].名称.."#Y/成功!")
    self:发送数据(连接id,8,"给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#H/添加#R/"..玩家数据[玩家id].道具.数据[临时id].名称.."#H/成功!")
    道具刷新(玩家id)
  else
    self:发送数据(连接id,7,"#Y/添加定制装备失败!")
    self:发送数据(连接id,8,"添加定制装备失败!")
  end
  local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具充值了%s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,玩家数据[玩家id].道具.数据[临时id].名称,self.工具账号)
  添加充值日志(添加语句)
  共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)
end




function 管理工具类:修改词条(连接id,数据)
  if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
      self:发送数据(连接id,7,"#Y/玩家id错误")
      return
  end
  local 玩家id = tonumber(数据.玩家id)
  if 玩家数据[玩家id] == nil then
    self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
    return
  end
  if not 数据.修改数据 then
     self:发送数据(连接id,7,"#Y/数据错误")
      return
  end
  local 装备类型 =  数据.修改数据.类型
   if not 装备类型 then
    self:发送数据(连接id,7,"#Y/添加失败!")
    return
  end
  local 装备格子 = 0
  local 装备id = 0
  if 装备类型=="武器" then
      装备格子=3
  elseif 装备类型=="铠甲" then
        装备格子=4
  elseif 装备类型=="头盔" then
          装备格子=1
  elseif 装备类型=="项链" then
          装备格子=2
  elseif 装备类型=="腰带" then
          装备格子=5
  elseif 装备类型=="鞋子" then
          装备格子=6
  end


  if 装备格子~=0 then
      装备id = 玩家数据[玩家id].角色.数据.装备[装备格子]
  else
      self:发送数据(连接id,7,"#Y/玩家为佩戴该类型装备!")
      return
  end

  if 装备id and 装备id~=0 then
      local 装备数据 = 玩家数据[玩家id].道具.数据[装备id]
      local 修改境界 = 数据.修改数据.境界
      if 装备数据 and 装备数据~=0 then
          local 词条1 =数据.修改数据.词条1
          local 词条2 =数据.修改数据.词条2
          local 词条3 =数据.修改数据.词条3
          local 数值1 =数据.修改数据.数值1
          local 数值2 =数据.修改数据.数值2
          local 数值3 =数据.修改数据.数值3

          if not 玩家数据[玩家id].道具.数据[装备id].装备境界 then
              玩家数据[玩家id].道具.数据[装备id].装备境界={品质="普通",升级值=0,洗练值=0,神话值=0,词条={},词条共鸣=false}
          end
          local 是否修改 = ""
          if (修改境界=="传说" or 修改境界=="神话") and 词条1 and 词条1~="" and 词条1==词条2 and 词条1==词条3 and 数值1 and 数值1~="" then
                if not 境界属性[词条1] then
                     是否修改 = "#Y/修改词条失败,词条1输入类型错误"
                elseif not 境界属性[词条1].分类[装备数据.分类] then
                          是否修改 = "#Y/修改词条失败,词条1输入类型错误"
                end
                if not tonumber(数值1) or tonumber(数值1)==0 then
                      是否修改 = "#Y/修改词条失败,数额1输入类型错误"
                end
                if tonumber(数值1)>境界属性[词条1][修改境界][2] then
                    是否修改 = "#Y/修改词条失败,数额1输入数额过大"
                end
          else
              if 词条1 and 词条1~="" and 数值1 and 数值1~="" then
                  if not 境界属性[词条1] then
                     是否修改 = "#Y/修改词条失败,词条1输入类型错误"
                  elseif not 境界属性[词条1].分类[装备数据.分类] then
                          是否修改 = "#Y/修改词条失败,词条1输入类型错误"
                  end
                  if not tonumber(数值1) or tonumber(数值1)==0 then
                      是否修改 = "#Y/修改词条失败,数额1输入类型错误"
                  end
                  if tonumber(数值1)>境界属性[词条1][修改境界][2] then
                      是否修改 = "#Y/修改词条失败,数额1输入数额过大"
                  end
              end
              if 词条2 and 词条2~="" and 数值2 and 数值2~="" then
                  if not 境界属性[词条2] then
                     是否修改 = "#Y/修改词条失败,词条2输入类型错误"

                  elseif not 境界属性[词条2].分类[装备数据.分类] then
                          是否修改 = "#Y/修改词条失败,词条2输入类型错误"
                  end
                  if not tonumber(数值2) or tonumber(数值2)==0 then
                      是否修改 = "#Y/修改词条失败,数额2输入类型错误"
                  end
                  if tonumber(数值2)>境界属性[词条2][修改境界][2] then
                    是否修改 = "#Y/修改词条失败,数额2输入数额过大"
                  end
              end
              if 词条3 and 词条3~="" and 数值3 and 数值3~="" then
                  if not 境界属性[词条3] then
                     是否修改 = "#Y/修改词条失败,词条3输入类型错误"
                  elseif not 境界属性[词条3].分类[装备数据.分类] then
                          是否修改 = "#Y/修改词条失败,词条3输入类型错误"
                  end
                  if not tonumber(数值3) or tonumber(数值3)==0 then
                      是否修改 = "#Y/修改词条失败,数额3输入类型错误"
                  end
                  if tonumber(数值3)>境界属性[词条3][修改境界][2] then
                    是否修改 = "#Y/修改词条失败,数额3输入数额过大"
                  end
              end
          end
          if 修改境界=="神话" and  数据.修改数据.神话词条 and 数据.修改数据.神话词条~="" then
                if not 神话属性[数据.修改数据.神话词条] then
                      是否修改 = "#Y/修改词条失败,神话词条输入类型错误"
                elseif  not 神话属性[数据.修改数据.神话词条][装备数据.分类] then
                         是否修改 = "#Y/修改词条失败,神话词条输入类型错误"
                end
          end
          if 是否修改~= "" then
              self:发送数据(连接id,7,是否修改)
              return
          else
              玩家数据[玩家id].道具.数据[装备id].装备境界.词条={}
              玩家数据[玩家id].道具.数据[装备id].装备境界.神话词条=nil
              玩家数据[玩家id].道具.数据[装备id].装备境界.词条共鸣=false
              local 修改内容 = "境界:".. 修改境界.." 修改词条:"
               if 修改境界=="优秀" then
                      if 词条1 and 词条1~="" and 数值1 and 数值1~=""  then
                          玩家数据[玩家id].道具.数据[装备id].装备境界.词条[1]={类型=词条1,数额=math.floor(数值1)}
                          修改内容=修改内容..词条1.."数额:"..数值1..","
                      end

              elseif 修改境界=="优秀" then
                    if 词条1 and 词条1~="" and 数值1 and 数值1~=""  then
                          玩家数据[玩家id].道具.数据[装备id].装备境界.词条[1]={类型=词条1,数额=math.floor(数值1)}
                          修改内容=修改内容..词条1.."数额:"..数值1..","
                    end
                    if 词条2 and 词条2~="" and 数值2 and 数值2~=""  then
                        玩家数据[玩家id].道具.数据[装备id].装备境界.词条[2]={类型=词条2,数额=math.floor(数值2/2)}
                        修改内容=修改内容..词条2.."数额:"..数值2
                    end
              elseif 修改境界=="传说" or 修改境界=="神话" then
                      if 词条1 and 词条1~="" and 词条1==词条2 and 词条1==词条3 and 数值1 and 数值1~="" then
                              玩家数据[玩家id].道具.数据[装备id].装备境界.词条[1]={类型=词条1,数额=math.floor(数值1)}
                              玩家数据[玩家id].道具.数据[装备id].装备境界.词条[2]={类型=词条1,数额=math.floor(数值1/2)}
                              玩家数据[玩家id].道具.数据[装备id].装备境界.词条[3]={类型=词条1,数额=math.floor(数值1/2)}
                               修改内容=修改内容..词条1.."数额:"..数值1.."(三同词条)"
                      else
                          if 词条1 and 词条1~="" and 数值1 and 数值1~="" then
                                玩家数据[玩家id].道具.数据[装备id].装备境界.词条[1]={类型=词条1,数额=math.floor(数值1)}
                                修改内容=修改内容..词条1.."数额:"..数值1..","
                          end
                          if 词条2 and 词条2~="" and 数值2 and 数值2~=""  then
                             玩家数据[玩家id].道具.数据[装备id].装备境界.词条[2]={类型=词条2,数额=math.floor(数值2/2)}
                             修改内容=修改内容..词条2.."数额:"..数值2..","
                          end
                          if 词条3 and 词条3~="" and 数值3 and 数值3~="" then
                               玩家数据[玩家id].道具.数据[装备id].装备境界.词条[3]={类型=词条3,数额=math.floor(数值3/2)}
                               修改内容=修改内容..词条3.."数额:"..数值3
                          end
                      end
                      local 临时境界 = 玩家数据[玩家id].道具.数据[装备id].装备境界
                      if 临时境界.词条 and 临时境界.词条[1] and 临时境界.词条[2] and 临时境界.词条[3]
                          and 临时境界.词条[1].类型==临时境界.词条[2].类型 and 临时境界.词条[1].类型==临时境界.词条[3].类型 then
                          玩家数据[玩家id].道具.数据[装备id].装备境界.词条[2].数额 =math.floor(临时境界.词条[1].数额/2)
                          玩家数据[玩家id].道具.数据[装备id].装备境界.词条[3].数额 =math.floor(临时境界.词条[1].数额/2)
                          玩家数据[玩家id].道具.数据[装备id].装备境界.词条共鸣=境界属性[临时境界.词条[1].类型].共鸣
                      end
                      if 修改境界=="神话" and  数据.修改数据.神话词条 and 数据.修改数据.神话词条~="" then
                          玩家数据[玩家id].道具.数据[装备id].装备境界.神话词条 =数据.修改数据.神话词条
                          修改内容=修改内容.." 神话词条:"..数据.修改数据.神话词条
                      end
              end
              玩家数据[玩家id].道具.数据[装备id].装备境界.品质=修改境界
              常规提示(玩家id,"#Y/你的装备:"..装备类型.."境界及词条已改变,请重新佩戴装备即可")
              self:发送数据(连接id,7,"#Y/给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#Y/修改#R/"..玩家数据[玩家id].道具.数据[装备id].名称.."#Y/境界词条成功!")
              self:发送数据(连接id,8,"给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#H/添加#R/"..玩家数据[玩家id].道具.数据[装备id].名称.."#H/境界词条成功!")
              道具刷新(玩家id)
              local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具修改了%s:%s境界词条,识别码:%s,修改数据:,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,装备类型,装备数据.名称,装备数据.识别码,修改内容,self.工具账号)
              添加充值日志(添加语句)
              共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)
          end
      else
          self:发送数据(连接id,7,"#Y/装备数据错误,修改失败!")
          self:发送数据(连接id,8,"装备数据错误,修改失败!")
      end
  else
        self:发送数据(连接id,7,"#Y/未佩戴该装备,修改失败!")
        self:发送数据(连接id,8,"未佩戴该装备,修改失败!")
  end





end




function 管理工具类:定制灵饰(连接id,数据)
  if 数据.玩家id==nil or  tonumber(数据.玩家id)==nil or tonumber(数据.玩家id)<=0 then
      self:发送数据(连接id,7,"#Y/玩家id错误")
      return
  end
  local 玩家id = tonumber(数据.玩家id)
  if 玩家数据[玩家id] == nil then
    self:发送数据(连接id,7,"#Y/玩家不在线无法操作")
    return
  end

  local 临时格子=玩家数据[玩家id].角色:取道具格子()
  if 临时格子==0 then
    self:发送数据(连接id,7,"#Y/该玩家没有足够的空间存放物品")
    return
  end

    local 等级 = tonumber(数据.灵饰数据.等级)
    if 等级 ==nil then
      self:发送数据(连接id,7,"#Y/添加失败!")
      return
    end
    if 等级<60 then
      等级 = 60
    elseif 等级>=60 and 等级<80  then
      等级 = 60
    elseif 等级>=80 and 等级<100  then
      等级 = 80
    elseif 等级>=100 and 等级<120  then
      等级 = 100
    elseif 等级>=120 and 等级<140  then
      等级 = 120
    elseif 等级>=140 and 等级<160  then
      等级 = 140
    else
      等级 = 160
    end
    local 临时名称=制造装备[数据.灵饰数据.部位][等级]
    if 临时名称==nil then
      self:发送数据(连接id,7,"#Y/添加失败!")
      return
    end
    local 内容 = 数据.灵饰数据
    local 道具 = 物品类()
    道具:置对象(临时名称)
    道具.级别限制 = 等级
    道具.幻化等级=0
    local 制造格子=玩家数据[玩家id].道具:取新编号()
    local 临时类型=内容.部位
    道具.幻化属性={附加={},}
    道具.识别码=取唯一识别码(玩家id)
    local 主属性 = 灵饰属性[临时类型].主属性[取随机数(1,#灵饰属性[临时类型].主属性)]
    local 临时下限 =灵饰属性.基础[主属性][道具.级别限制].a+道具.级别限制/20
    local 临时上限 =灵饰属性.基础[主属性][道具.级别限制].b+道具.级别限制/10
    local 主数值=取随机数(临时下限,临时上限)
    主数值=math.floor(主数值*1.1)
    道具.幻化属性.基础={类型=主属性,数值=主数值,强化=0}
    local 数量上限 =取随机数(1,3)
    for n=1,数量上限 do
        local 副属性=灵饰属性[临时类型].副属性[取随机数(1,#灵饰属性[临时类型].副属性)]
        local 下限 = 灵饰属性.基础[副属性][道具.级别限制].a+道具.级别限制/20
        local 上限 = 灵饰属性.基础[副属性][道具.级别限制].b+道具.级别限制/10
        local 副数值=math.floor(取随机数(下限,上限))
        道具.幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
    end

    if 内容.主属 ~= nil and tonumber(内容.属性)~=nil then
      道具.幻化属性.基础={类型=内容.主属,数值=tonumber(内容.属性),强化=0}
    end
    if 内容.附加1 ~= nil and tonumber(内容.数值1)~=nil  then
      道具.幻化属性.附加[1]={类型=内容.附加1,数值=tonumber(内容.数值1),强化=0}
    end
    if 内容.附加2 ~= nil and tonumber(内容.数值2)~=nil and  道具.幻化属性.附加[1]~=nil then
      道具.幻化属性.附加[2]={类型=内容.附加2,数值=tonumber(内容.数值2),强化=0}
    end
    if 内容.附加3 ~= nil and tonumber(内容.数值3)~=nil and  道具.幻化属性.附加[2]~=nil then
      道具.幻化属性.附加[3]={类型=内容.附加3,数值=tonumber(内容.数值3),强化=0}
    end
    if 内容.附加4 ~= nil and tonumber(内容.数值4)~=nil and  道具.幻化属性.附加[3]~=nil then
      道具.幻化属性.附加[4]={类型=内容.附加4,数值=tonumber(内容.数值4),强化=0}
    end

      道具.制造者 = 服务端参数.名称
      if 内容.制造 ~= nil then
        道具.制造者 =内容.制造
      end
      道具.灵饰=true
      if 内容.特效 ~= nil then
        道具.特效 =内容.特效
      end
      玩家数据[玩家id].道具.数据[制造格子]=道具
      玩家数据[玩家id].道具.数据[制造格子].五行=取五行()
      玩家数据[玩家id].道具.数据[制造格子].耐久度=500
      玩家数据[玩家id].道具.数据[制造格子].部位类型=临时类型
      玩家数据[玩家id].道具.数据[制造格子].鉴定=false
      玩家数据[玩家id].角色.数据.道具[临时格子]=制造格子
      常规提示(玩家id,"#Y/你得到了#R/"..玩家数据[玩家id].道具.数据[制造格子].名称)
      道具刷新(玩家id)
      self:发送数据(连接id,7,"#Y/给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#Y/添加#R/"..玩家数据[玩家id].道具.数据[制造格子].名称.."#Y/成功!")
      self:发送数据(连接id,8,"给玩家:#R/"..玩家数据[玩家id].角色.数据.名称.."#H/添加#R/"..玩家数据[玩家id].道具.数据[制造格子].名称.."#H/成功!")
      local 添加语句=format("玩家:%s,ID:%s,账号%s使用GM工具充值了%s,工具登陆账号:%s",玩家数据[玩家id].角色.数据.名称,玩家id,玩家数据[玩家id].账号,玩家数据[玩家id].道具.数据[制造格子].名称,self.工具账号)
      添加充值日志(添加语句)
      共享货币[玩家数据[玩家id].账号]:充值记录(添加语句)




end


function 管理工具类:游戏管理(连接id,内容)
  if 内容.文本=="皇宫飞贼" then
    if   皇宫飞贼.开关 then
      皇宫飞贼.开关=false
      self:发送数据(连接id,8,"#H/关闭#R/皇宫飞贼#H/成功")
    else
       任务处理类:开启皇宫飞贼()
       self:发送数据(连接id,8,"#H/开启#R/皇宫飞贼#H/成功")
    end
elseif  内容.文本== "刷出桐人" then
      任务处理类:刷新桐人()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")

 elseif  内容.文本== "魔化桐人" then
      任务处理类:刷新魔化桐人()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")

 elseif  内容.文本== "混世魔王" then
      任务处理类:刷新混世魔王()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本== "开启万象" then
      任务处理类:刷出万象福()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
 elseif  内容.文本== "新春活动" then
      任务处理类:刷出新春快乐()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
 elseif  内容.文本== "小小盲僧" then
      任务处理类:刷出小小盲僧()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
elseif  内容.文本== "开启生肖" then
      任务处理类:刷出新十二生肖()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
 elseif 内容.文本== "天降辰星" then
        if 辰星数据 and 辰星数据.开启 then
             任务处理类:结束天降辰星()
             self:发送数据(连接id,8,"#H/结束#R/"..内容.文本.."#H/成功")
        else
            任务处理类:刷出天降辰星()
            self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
        end
 elseif  内容.文本== "开启财神" then
        任务处理类:刷出财神爷()
  self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本== "开启病毒" then
      任务处理类:刷出新冠状病毒()
    self:发送数据(连接id,8,"#H/开启#R/冠状病毒#H/成功")
  elseif  内容.文本=="天降灵猴" then
    任务处理类:刷出天降灵猴()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出星宿" then
    任务处理类:刷出星宿()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出年兽" then
    任务处理类:捣乱的年兽()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出妖魔" then
     任务处理类:刷出妖魔鬼怪()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷新商品" then
     商店处理类:刷新珍品()
     商店处理类:刷新跑商商品买入价格()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="大雁塔怪" then
    任务处理类:设置大雁塔怪(nil)
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="天庭叛逆" then
    任务处理类:设置天庭叛逆(nil)
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="糖果派对" then
      任务处理类:糖果派对()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出天罡" then
    任务处理类:开启天罡星任务()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出地煞" then
    任务处理类:开启地煞星任务()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出知了" then
    任务处理类:刷出知了王()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
   elseif  内容.文本=="知了先锋" then
    任务处理类:刷出知了先锋()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="创世佛屠" then
    任务处理类:刷出创世佛屠()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="善恶如来" then
    任务处理类:刷出善恶如来()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="门派入侵" then
    任务处理类:刷出门派入侵()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="四墓灵鼠" then
    任务处理类:刷出四墓灵鼠()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="邪恶年兽" then
    任务处理类:邪恶年兽()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")

  elseif  内容.文本=="所有活动" then
    任务处理类:开启地煞星任务(id)
    任务处理类:刷出妖魔鬼怪()
    任务处理类:刷出创世佛屠()
    任务处理类:刷出善恶如来()
    任务处理类:刷出三国武圣()
    任务处理类:刷出四墓灵鼠()
    任务处理类:刷出星宿()
    任务处理类:开启天罡星任务()
    任务处理类:刷出知了王()
    任务处理类:刷出知了先锋()
    任务处理类:设置天庭叛逆(id)
    任务处理类:邪恶年兽()
    self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本== "嘉 年 华" then
       if not 嘉年华时间 then
            嘉年华:开启活动()
            嘉年华时间=os.time()
            self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
       else
            嘉年华:关闭活动()
            self:发送数据(连接id,8,"#H/关闭#R/"..内容.文本.."#H/成功")
            嘉年华时间=nil
       end
  elseif  内容.文本=="门派开关" then
   if  闯关参数.开关  then
        闯关参数.开关=false
        广播消息({内容="#G/十五门闯关活动已经圆满结束，所有处于战斗中的玩家将被强制退出战斗。",频道="xt"})
        for n, v in pairs(战斗准备类.战斗盒子) do
          if 战斗准备类.战斗盒子[n].战斗类型==100011 then
            战斗准备类.战斗盒子[n]:结束战斗处理(0,0,1)
          end
        end
        for n, v in pairs(玩家数据) do
          if 玩家数据[n].管理==nil and 玩家数据[n].角色:取任务(107)~=0 then
            玩家数据[n].角色:取消任务(玩家数据[n].角色:取任务(107))
            常规提示(n,"你的闯关任务已经被自动取消")
          end
        end
        for n, v in pairs(任务数据) do
          if 任务数据[n]~=nil and 任务数据[n].类型 == 107 then
            任务数据[n]=nil
          end
    end
       self:发送数据(连接id,8,"#H/关闭#R/门派闯关#H/成功")
    else
        任务处理类:开启门派闯关()
        self:发送数据(连接id,8,"#H/开启#R/门派闯关#H/成功")
    end
  elseif  内容.文本=="宝藏开关" then

    if 宝藏山数据.开关 then
        宝藏山数据.开关=false
        广播消息({内容="#G/宝藏山活动已经结束，处于场景内的玩家将被自动传送出场景。",频道="xt"})
        地图处理类:清除地图玩家(5001,1226,115,15)
        self:发送数据(连接id,8,"#H/关闭#R/宝藏山#H/成功")
    else
        任务处理类:开启宝藏山()
        self:发送数据(连接id,8,"#H/开启#R/宝藏山#H/成功")
    end
  elseif  内容.文本=="镖王开关" then
     if  镖王活动.开关 then
       for n, v in pairs(战斗准备类.战斗盒子) do
        if 战斗准备类.战斗盒子[n].战斗类型==100025 then
          战斗准备类.战斗盒子[n]:结束战斗处理(0,0,1)
        end
      end
      for n, v in pairs(玩家数据) do
        if 玩家数据[n].角色:取任务(208)~=0 then
          玩家数据[n].角色:取消任务(玩家数据[n].角色:取任务(208))
          常规提示(n,"#Y你的镖王任务已经被自动取消")
        end
      end
      for n, v in pairs(任务数据) do
        if 任务数据[n].类型==208 then
          local id=任务数据[n].玩家id
          任务数据[n]=nil
        end
      end
      镖王活动.开关=false
      self:发送数据(连接id,8,"#H/关闭#R/镖王活动#H/成功")
    else
        任务处理类:开启镖王活动()
        self:发送数据(连接id,8,"#H/开启#R/镖王活动#H/成功")
    end
  elseif  内容.文本=="游泳开关" then
    if 游泳开关 then
        游泳开关=false
        广播消息({内容="#G/游泳比赛已经结束，所有处于战斗中的玩家将被强制退出战斗。",频道="xt"})
        for n, v in pairs(战斗准备类.战斗盒子) do
          if 战斗准备类.战斗盒子[n].战斗类型==100012 then
            战斗准备类.战斗盒子[n]:结束战斗处理(0,0,1)
          end
        end
        self:发送数据(连接id,8,"#H/关闭#R/游泳比赛#H/成功")
      else
      任务处理类:开启游泳比赛()
      self:发送数据(连接id,8,"#H/开启#R/游泳比赛#H/成功")
    end
elseif  内容.文本=="跨服关闭" then
        跨服连接:断开()
      self:发送数据(连接id,8,"#H/关闭#R/跨服#H/成功")
      发送公告("#G(跨服争霸)#P已经关闭，请到大家迅速离场.")
      广播消息({内容=format("#G(跨服争霸)#P已经关闭，请到大家迅速离场."),频道="hd"})
 elseif  内容.文本=="跨服报名" then
     self:发送数据(连接id,8,"#H/开启#R/跨服#H/成功")
     发送公告("#G(跨服争霸)#P已经开启，请到长安跨服争霸主持人处报名参赛！准备战斗")
      广播消息({内容=format("#G(跨服争霸)#P已经开启，请到长安跨服争霸主持人处报名参赛！准备战斗"),频道="hd"})
 elseif  内容.文本=="跨服开启" then
      跨服连接:连接处理()
     self:发送数据(连接id,8,"#H/开启#R/跨服#H/成功")
     发送公告("#G(跨服争霸)#P已经开启，请少侠到长安跨服争霸主持人进入战场")
      广播消息({内容=format("#G(跨服争霸)#P已经开启，请少侠到长安跨服争霸主持人进入战场"),频道="hd"})
 elseif  内容.文本=="开启帮战" then
     帮战活动类:活动开启()
     self:发送数据(连接id,8,"#H/开启#R/帮战#H/成功")
  elseif  内容.文本=="结束帮战" then
     --帮战活动类:结束比赛()
    if 帮战活动类.活动开关 then
          帮战活动类:刷出宝箱处理()
          帮战活动类.活动计时=os.time()
          self:发送数据(连接id,8,"#H/结束#R/帮战#H/成功")
    else
        self:发送数据(连接id,8,"#H/结束#R/帮战#H/失败")
    end
  elseif  内容.文本=="迷宫开关" then
    if 迷宫数据.开关 then
      迷宫数据.开关=false
      self:发送数据(连接id,8,"#H/关闭#R/迷宫#H/成功")
    else
      任务处理类:开启迷宫()
      self:发送数据(连接id,8,"#H/开启#R/迷宫#H/成功")
    end
  elseif  内容.文本=="开启比武" then
         英雄大会:开启活动()
    -- 发送公告("#Y/英雄比武大会活动已经进入报名阶段，请打算参加活动的玩家在19点前前往长安城比武大会主持人处进行报名。")
    -- 广播消息({内容=format("英雄比武大会活动已经进入报名阶段，请打算参加活动的玩家在19点前前往长安城比武大会主持人处进行报名。"),频道="xt"})
   self:发送数据(连接id,8,"#H/开启#R/比武大会比赛#H/成功")
  elseif  内容.文本=="比武入场" then
    -- 发送公告("#Y/英雄比武大会活动已经开放入场，请参加比武大会的玩家前往长安城比武大会主持人处进行入场。")
    英雄大会:开启入场()
    self:发送数据(连接id,8,"#H/开启#R/比武大会入场#H/成功")
   elseif  内容.文本=="开始比武" then
    英雄大会:比武开始()
    self:发送数据(连接id,8,"#H/开启#R/比武大会比赛#H/成功")
  elseif  内容.文本=="结束比武" then
      英雄大会:结束活动()
    self:发送数据(连接id,8,"#H/结束#R/比武大会比赛#H/成功")
  elseif  内容.文本=="首席报名" then
    -- 开启首席争霸报名()
    -- self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")

  elseif  内容.文本=="开启首席" then
    -- 开启首席争霸()
    -- self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
   elseif  内容.文本=="开启剑会" then
     游戏活动类:开启剑会天下()
     self:发送数据(连接id,8,"#H/开启#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="结束剑会" then
     游戏活动类:关闭剑会天下()
     self:发送数据(连接id,8,"#H/关闭#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="更新商城" then
    刷新商城商品()
    __S服务:输出("商城商品数据更新成功")
    self:发送数据(连接id,8,"#H/操作#R/"..内容.文本.."#H/成功")

  elseif  内容.文本=="商城处理" then
    刷新商城购买处理()
    __S服务:输出("商城购买处理已刷新")
    self:发送数据(连接id,8,"#R/商城购买处理#H/已更新")
  elseif  内容.文本=="保存数据" then
     保存系统数据()
     保存所有玩家数据()
    self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="关闭游戏" then
    服务器关闭={开关=true,计时=61,起始=os.time()}
    发送公告("#R各位玩家请注意，服务器将在1分钟后关闭，请所有玩家注意提前下线。")
    广播消息({内容=format("#R各位玩家请注意，服务器将在1分钟后关闭，请所有玩家提前下线。"),频道="xt"})
    保存系统数据()
    保存所有玩家数据()
    self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="圣兽残魂" then
      任务处理类:刷新影青龙()
      任务处理类:刷新影朱雀()
      任务处理类:刷新影白虎()
      任务处理类:刷新影玄武()
      任务处理类:刷新影麒麟()
   self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="刷出星官" then
      任务处理类:刷出星官()
 self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="二八星宿" then
       任务处理类:刷出星宿()
   self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
 elseif  内容.文本=="开启异界" then
   任务处理类:刷出倔强青铜()
   任务处理类:刷出秩序白银()
   任务处理类:刷出荣耀黄金()
   任务处理类:刷出永恒钻石()
   任务处理类:刷出至尊星耀()
   任务处理类:刷出最强王者()
 self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")

 elseif  内容.文本=="开启经宝" then
      任务处理类:刷出经验宝宝()
      self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")

elseif  内容.文本== "世界挑战" then
    if 世界挑战~=nil and 世界挑战.开启~=nil and 世界挑战.开启 then
          结束世界挑战()
        self:发送数据(连接id,7,"#Y/关闭#R/世界挑战#Y/成功")
    else
          刷新世界挑战()
        self:发送数据(连接id,7,"#Y/开启#R/世界挑战#Y/成功")
    end

  elseif  内容.文本== "彩虹争霸" then
    if 彩虹争霸.活动开关 then
        彩虹争霸:关闭活动()
        self:发送数据(连接id,7,"#Y/关闭#R/彩虹争霸#Y/成功")
    else
       彩虹争霸:开启报名()
        self:发送数据(连接id,7,"#Y/开启#R/彩虹争霸#Y/成功")
    end
  elseif  内容.文本== "长安保卫" then
    if 长安保卫战.活动开关 then
        长安保卫战:关闭活动()
        self:发送数据(连接id,7,"#Y/关闭#R/长安保卫战#Y/成功")
    else
       长安保卫战:开启活动()
        self:发送数据(连接id,7,"#Y/开启#R/长安保卫战#Y/成功")
    end

  elseif  内容.文本=="假人走动" then
          if 走动假人 then
              走动假人  = false
               self:发送数据(连接id,7,"#Y/关闭#R/假人#Y/成功")
          else
              走动假人  = true
               self:发送数据(连接id,7,"#Y/开启#R/假人#Y/成功")
          end
          假人玩家类:功能开关(走动假人)
  elseif 内容.文本== "假人摆摊" then
          if 假人摆摊 then
              假人摆摊 = false
              self:发送数据(连接id,7,"#Y/关闭#R/假人摆摊#Y/成功")
          else
              假人摆摊 = true
              self:发送数据(连接id,7,"#Y/开启#R/假人摆摊#Y/成功")
          end
          if 摆摊假人类 then
              摆摊假人类:功能开关(假人摆摊)
          end
  elseif  内容.文本== "假人聊天" then
    --self:发送数据(连接id,7,"#Y/该功能还在完善")
    if 假人说话 then
        假人说话  = false
          self:发送数据(连接id,7,"#Y/关闭#R/假人说话#Y/成功")
    else
        假人说话  = true
        self:发送数据(连接id,7,"#Y/开启#R/假人说话#Y/成功")
    end


  elseif  内容.文本=="发送广播" then
    广播消息({内容=format(内容.数据),频道="xt"})
    self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="发送公告" then
    发送公告("#Y/"..内容.数据)
    self:发送数据(连接id,8,"#R/"..内容.文本.."#H/成功")
  elseif  内容.文本=="经验倍率" then
     if tonumber(内容.数据) ==nil then return  end
     服务端参数.经验获得率=tonumber(内容.数据)
     f函数.写配置(程序目录.."配置文件.ini","主要配置","经验",服务端参数.经验获得率)
     广播消息({内容=format("当前获取经验倍率:"..内容.数据.."倍"),频道="xt"})
    self:发送数据(连接id,8,"#H/经验获取倍率已修改为#R/"..内容.数据.."#H/倍")
  elseif  内容.文本=="游戏难度" then
    if tonumber(内容.数据) ==nil then return  end
     服务端参数.难度 = tonumber(内容.数据)
     f函数.写配置(程序目录.."配置文件.ini","主要配置","难度",服务端参数.难度)
     广播消息({内容=format("当前服务器难度:"..内容.数据.."倍"),频道="xt"})
    self:发送数据(连接id,8,"#H/服务器难度已修改为#R/"..内容.数据.."#H/倍")
  elseif  内容.文本=="等级上限" then
     if tonumber(内容.数据) ==nil then return  end
     服务端参数.等级上限 =  tonumber(内容.数据)
     f函数.写配置(程序目录.."配置文件.ini","主要配置","等级上限",服务端参数.等级上限)
     广播消息({内容=format("当前服务器等级上限:"..内容.数据.."级"),频道="xt"})
    self:发送数据(连接id,8,"#H/服务器等级上限已修改为#R/"..内容.数据.."#H/级")


  elseif 内容.文本=="聊天监控开关" then
    if 聊天监控[连接id] then
        聊天监控[连接id]=nil
        self:发送数据(连接id,7,"#Y/关闭聊天监控成功")
        return
     else
       聊天监控[连接id]=true
        self:发送数据(连接id,7,"#Y/开启聊天监控成功")
         return
    end
  end
    self:发送数据(连接id,7,"#Y/操作#R/"..内容.文本.."#Y/成功")



end




function 管理工具类:更新(dt) end
function 管理工具类:显示(x,y) end
return 管理工具类