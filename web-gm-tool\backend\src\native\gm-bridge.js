/**
 * GM工具桥接器
 * 通过调用GM工具的Lua环境来实现通信
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');

// GM工具路径
const GM_TOOL_PATH = path.resolve('c:\\Users\\<USER>\\Desktop\\mh\\GM工具源码');
const GM_EXE_PATH = path.join(GM_TOOL_PATH, 'g2d.exe');

/**
 * GM工具桥接客户端
 */
class GMBridgeClient {
    constructor() {
        this.connected = false;
        this.process = null;
        this.callbacks = {};
    }

    /**
     * 检查GM工具是否可用
     */
    static isAvailable() {
        return fs.existsSync(GM_EXE_PATH);
    }

    /**
     * 创建临时Lua脚本
     */
    createTempScript(data) {
        const scriptContent = `
-- 临时通信脚本
print('[GM桥接] 开始执行临时脚本')

-- 加载必要的模块
local mp = require("msgpack")

-- 创建客户端（复制GM工具的逻辑）
local client = {}
client._new = require("luahp.client")
client._hp = client._new()

-- 初始化
client._hp:Create_TcpPackClient(client)

-- 设置包头标识
local Flag = 0
for i,v in ipairs{string.byte("GGELUA_FLAG", 1, 11)} do
    Flag = Flag+v
end
client._hp:SetPackHeaderFlag(Flag)
print('[GM桥接] 包头标识:', Flag)

-- 连接回调
function client:OnConnect()
    print('[GM桥接] 连接成功')
    
    -- 发送数据
    local encryptedData = "${data}"
    local packed_data = mp.pack{encryptedData}
    
    print('[GM桥接] 发送数据包 - 长度:', #packed_data)
    self._hp:SendPack(packed_data)
    
    return 1
end

-- 接收回调
function client:OnReceive(pData, iLength)
    print('[GM桥接] 接收数据包 - 长度:', iLength)
    
    -- 解包数据
    local data1 = mp.unpack(pData)
    if data1 and data1[1] then
        print('[GM桥接] 接收到数据:', string.sub(data1[1], 1, 50) .. '...')
    end
    
    return 1
end

-- 断开回调
function client:OnClose(so, ec)
    print('[GM桥接] 连接断开 - 错误码:', ec)
    return 1
end

-- 发送完成回调
function client:OnSend(pData, iLength)
    print('[GM桥接] 发送完成 - 长度:', iLength)
    return 1
end

-- 连接到服务器
print('[GM桥接] 连接到服务器...')
local result = client._hp:Start('127.0.0.1', 6888, 0)
if result == 1 then
    print('[GM桥接] 连接请求已发送')
    
    -- 等待处理
    for i = 1, 50 do
        os.execute("timeout /t 1 /nobreak > nul")
    end
else
    print('[GM桥接] 连接失败')
end

print('[GM桥接] 脚本执行完成')
`;

        const tempPath = path.join(GM_TOOL_PATH, 'temp_bridge.lua');
        fs.writeFileSync(tempPath, scriptContent, 'utf8');
        return tempPath;
    }

    /**
     * 通过GM工具发送数据
     */
    async sendData(data) {
        return new Promise((resolve, reject) => {
            if (!GMBridgeClient.isAvailable()) {
                reject(new Error('GM工具不可用'));
                return;
            }

            try {
                // 创建临时脚本
                const scriptPath = this.createTempScript(data);
                console.log('[GM桥接] 创建临时脚本:', scriptPath);

                // 执行GM工具
                const process = spawn(GM_EXE_PATH, [], {
                    cwd: GM_TOOL_PATH,
                    stdio: ['pipe', 'pipe', 'pipe']
                });

                let output = '';
                let errorOutput = '';

                process.stdout.on('data', (data) => {
                    const text = data.toString();
                    output += text;
                    console.log('[GM桥接输出]', text.trim());
                });

                process.stderr.on('data', (data) => {
                    const text = data.toString();
                    errorOutput += text;
                    console.error('[GM桥接错误]', text.trim());
                });

                process.on('close', (code) => {
                    console.log('[GM桥接] 进程结束 - 退出码:', code);
                    
                    // 清理临时文件
                    try {
                        fs.unlinkSync(scriptPath);
                    } catch (e) {
                        // 忽略清理错误
                    }

                    if (code === 0) {
                        resolve({
                            success: true,
                            output: output,
                            error: errorOutput
                        });
                    } else {
                        reject(new Error(`GM工具执行失败 - 退出码: ${code}`));
                    }
                });

                process.on('error', (error) => {
                    console.error('[GM桥接] 进程错误:', error.message);
                    reject(error);
                });

                // 设置超时
                setTimeout(() => {
                    if (!process.killed) {
                        process.kill();
                        reject(new Error('GM工具执行超时'));
                    }
                }, 30000); // 30秒超时

            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 发送登录数据
     */
    async sendLoginData(username, password) {
        // 使用与GM工具相同的加密数据
        const encryptedData = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';
        
        console.log('[GM桥接] 发送登录数据...');
        return await this.sendData(encryptedData);
    }
}

module.exports = {
    GMBridgeClient
};
