{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GoldTwoToneSvg from \"@ant-design/icons-svg/es/asn/GoldTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GoldTwoTone = function GoldTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GoldTwoToneSvg\n  }));\n};\n\n/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQzNS43IDU1OC43Yy0uNi0zLjktNC02LjctNy45LTYuN0gxNjYuMmMtMy45IDAtNy4zIDIuOC03LjkgNi43bC00MC4yIDI0OGMtLjEuNC0uMS45LS4xIDEuMyAwIDQuNCAzLjYgOCA4IDhoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDh6TTE5Ni41IDc0OGwyMC43LTEyOGgxNTkuNWwyMC43IDEyOEgxOTYuNXptNzA5LjQgNTguN2wtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDU5Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjMtLjcgNy4zLTQuOCA2LjYtOS4yek02MjYuNSA3NDhsMjAuNy0xMjhoMTU5LjVsMjAuNyAxMjhINjI2LjV6TTM0MiA0NzJoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDM4Mi4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOHptOTEuMi0xOTZoMTU5LjVsMjAuNyAxMjhoLTIwMWwyMC44LTEyOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTU5Mi43IDI3Nkg0MzMuMmwtMjAuOCAxMjhoMjAxek0yMTcuMiA2MjBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHptNDMwIDBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GoldTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GoldTwoTone';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "GoldTwoToneSvg", "AntdIcon", "GoldTwoTone", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/GoldTwoTone.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport GoldTwoToneSvg from \"@ant-design/icons-svg/es/asn/GoldTwoTone\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar GoldTwoTone = function GoldTwoTone(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: GoldTwoToneSvg\n  }));\n};\n\n/**![gold](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQzNS43IDU1OC43Yy0uNi0zLjktNC02LjctNy45LTYuN0gxNjYuMmMtMy45IDAtNy4zIDIuOC03LjkgNi43bC00MC4yIDI0OGMtLjEuNC0uMS45LS4xIDEuMyAwIDQuNCAzLjYgOCA4IDhoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDh6TTE5Ni41IDc0OGwyMC43LTEyOGgxNTkuNWwyMC43IDEyOEgxOTYuNXptNzA5LjQgNTguN2wtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDU5Ni4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOGgzNDJjLjQgMCAuOSAwIDEuMy0uMSA0LjMtLjcgNy4zLTQuOCA2LjYtOS4yek02MjYuNSA3NDhsMjAuNy0xMjhoMTU5LjVsMjAuNyAxMjhINjI2LjV6TTM0MiA0NzJoMzQyYy40IDAgLjkgMCAxLjMtLjEgNC40LS43IDcuMy00LjggNi42LTkuMmwtNDAuMi0yNDhjLS42LTMuOS00LTYuNy03LjktNi43SDM4Mi4yYy0zLjkgMC03LjMgMi44LTcuOSA2LjdsLTQwLjIgMjQ4Yy0uMS40LS4xLjktLjEgMS4zIDAgNC40IDMuNiA4IDggOHptOTEuMi0xOTZoMTU5LjVsMjAuNyAxMjhoLTIwMWwyMC44LTEyOHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTU5Mi43IDI3Nkg0MzMuMmwtMjAuOCAxMjhoMjAxek0yMTcuMiA2MjBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHptNDMwIDBsLTIwLjcgMTI4aDIwMC45bC0yMC43LTEyOHoiIGZpbGw9IiNlNmY0ZmYiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(GoldTwoTone);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'GoldTwoTone';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACjD,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,WAAW,CAAC;AACxD,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,aAAa;AACrC;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}