/**
 * 调试字符替换过程
 */

const { CHAR_KEY_MAP } = require('./src/protocol/encryption');

// 测试Base64字符串的最后几个字符
const testBase64 = 'MTEyMzQ1Ki0qMTIzNDVkbyBsb2NhbCByZXQ9e1si6LSm5Y+3Il09Ijg4ODg4OCIsWyLlr4bnoIEiXT0iODg4ODg4In0gcmV0dXJuIHJldCBlbmQxMjM0NSotKjEyMzQ1';

console.log('=== 字符替换调试 ===');
console.log('Base64字符串长度:', testBase64.length);
console.log('Base64最后10字符:', testBase64.slice(-10));

// 逐字符处理
let result = '';
for (let i = 0; i < testBase64.length; i++) {
    const char = testBase64.charAt(i);
    if (char !== '' && CHAR_KEY_MAP[char]) {
        result += CHAR_KEY_MAP[char];
        console.log(`位置${i}: '${char}' -> '${CHAR_KEY_MAP[char]}'`);
    } else {
        result += char;
        console.log(`位置${i}: '${char}' -> '${char}' (无映射)`);
    }
}

console.log('\n最终结果长度:', result.length);
console.log('最终结果最后50字符:', result.slice(-50));

// 分析结果
const parts = result.split(',');
console.log('\n分割后部分数:', parts.length);
console.log('最后10部分:', parts.slice(-10));

// 检查是否有空部分
const emptyParts = parts.filter((part, index) => {
    if (part === '') {
        console.log(`空部分在位置: ${index}`);
        return true;
    }
    return false;
});
console.log('空部分数量:', emptyParts.length);

// 检查最后几个字符的映射
console.log('\n最后几个字符的映射检查:');
const lastChars = testBase64.slice(-5);
for (let i = 0; i < lastChars.length; i++) {
    const char = lastChars[i];
    const mapping = CHAR_KEY_MAP[char];
    console.log(`'${char}' -> ${mapping ? `'${mapping}'` : '无映射'}`);
}
