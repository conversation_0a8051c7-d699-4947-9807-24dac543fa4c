--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-04-19 00:38:26
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解所以资源
--======================================================================--
local 礼包奖励类 = class()
function 礼包奖励类:初始化()end

function 刷新新手使者奖励()
    local 代码函数=loadstring(读入文件([[出生仙子领取.ini]]))
    代码函数()
end
function 礼包奖励类:新手使者奖励(id,等级)
 --  玩家数据[id].角色:添加储备(5000000,"宠物仙子新手奖励",1)
 --  玩家数据[id].角色:添加经验(10000000,"宠物仙子新手奖励",1)
 --  玩家数据[id].角色:添加银子(50000,"宠物仙子新手奖励",1)
 --  玩家数据[id].道具:给予道具(id,"洞冥草",10,nil,nil,"专用")
 --  玩家数据[id].道具:给予道具(id,"摄妖香",10,nil,nil,"专用")
 --  玩家数据[id].道具:给予道具(id,"飞行符",10,nil,nil,"专用")
 --  玩家数据[id].道具:给予道具(id,"秘制红罗羹",1,nil,nil,"专用")
 --  玩家数据[id].道具:给予道具(id,"秘制绿罗羹",1,nil,nil,"专用")
 --  玩家数据[id].道具:给予道具(id,"彩果",20,nil,nil,"专用")
 -- -- 玩家数据[id].道具:给予道具(id,"测试会员卡",1,nil,nil,"专用")
 --  for i=1,2 do
 --      玩家数据[id].道具:给予道具(id,"神兵图鉴",1,160)
 --  end
 --  for i=1,4 do
 --      玩家数据[id].道具:给予道具(id,"灵宝图鉴",1,160)
 --  end
 --  local 临时格子 = 玩家数据[id].角色:取道具格子()
 --  local 道具编号 = 玩家数据[id].道具:取新编号()
 --  local 长安 = {可叠加=false,总类=11,xy={[2]={y=11,x=495},[5]={y=273,x=12},[3]={y=273,x=12},[7]={y=105,x=192},[1]={y=7,x=312},[4]={y=167,x=362},[6]={y=240,x=277},[8]={y=68,x=413},[9]={y=124,x=465},[10]={y=19,x=379},[11]={y=85,x=290},[12]={y=181,x=481},[13]={y=276,x=528},[14]={y=81,x=145},[15]={y=48,x=106}},地图=1001,子类=20,识别码="4000094_1558708786_4149556_90",y=7,x=312,价格=1,分类=2,总次数=200,次数=200,名称="红色合成旗"}
 --  玩家数据[id].道具.数据[道具编号] = DeepCopy(长安)
 --  玩家数据[id].道具.数据[道具编号].数量=1
 --  玩家数据[id].角色.数据.道具[临时格子]=道具编号
 --  玩家数据[id].召唤兽:添加召唤兽("蛟龙","蛟龙","宝宝",nil,nil,{"魔之心","法术连击","泰山压顶","法术暴击","法术波动","感知","高级驱鬼"},1,1)
 --  玩家数据[id].召唤兽:添加召唤兽("天兵","天兵","宝宝",nil,nil,{"理直气壮","必杀","吸血","偷袭","夜战","驱鬼","高级敏捷"},1,1)
 --  self:全套装备(id,60,"无级别限制",1,1)
 --  常规提示(id,"#Y/你获得了#R新手物品奖励")
end







function 礼包奖励类:设置拜师奖励(id)
  玩家数据[id].角色:添加储备(10000,"拜师奖励",1)
  发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].最后对话.名称,模型=玩家数据[id].最后对话.模型,对话="既然你诚心要拜入本门派，那我就收你为徒。从即日起，我就是你的师傅了。你可要好好得尊师重道。这点礼物是为师的一点心意。"})
  if not 玩家数据[id].召唤兽:是否携带上限() then
    local 种类="宝宝"
    if 取随机数()<=20 then
      种类="变异"
    end
    玩家数据[id].召唤兽:添加召唤兽("海毛虫","海毛虫","宝宝")
    常规提示(id,"#Y/你获得了一只海毛虫")
  end
end



function 礼包奖励类:升级奖励40(id)
  玩家数据[id].角色:添加经验(200000,"40级奖励")
  玩家数据[id].角色:添加银子(50000,"40级奖励",1)
  玩家数据[id].角色:添加储备(200000,"40级奖励",1)
  if not 玩家数据[id].召唤兽:是否携带上限() then
  玩家数据[id].召唤兽:添加召唤兽("马面","马面","宝宝",nil,35)
  常规提示(id,"#Y/你获得了一只马面")
  end
end

function 礼包奖励类:升级奖励50(id)
  玩家数据[id].角色:添加经验(400000,"50级奖励")
  玩家数据[id].角色:添加银子(100000,"50级奖励",1)
  玩家数据[id].角色:添加储备(500000,"50级奖励",1)
  if not 玩家数据[id].召唤兽:是否携带上限() then
  玩家数据[id].召唤兽:添加召唤兽("古代瑞兽","古代瑞兽","宝宝",nil,45)
  常规提示(id,"#Y/你获得了一只古代瑞兽")
  end
end

function 礼包奖励类:升级奖励55(id)
  玩家数据[id].角色:添加经验(800000,"55级奖励")
  玩家数据[id].角色:添加银子(100000,"55奖励",1)
  玩家数据[id].角色:添加储备(500000,"55级奖励",1)
  if not 玩家数据[id].召唤兽:是否携带上限() then
  玩家数据[id].召唤兽:添加召唤兽("天兵","天兵","宝宝",nil,55)

  常规提示(id,"#Y/你获得了一只天兵")
  end
end

function 礼包奖励类:升级奖励65(id)
  玩家数据[id].角色:添加经验(1600000,"65级奖励")
  玩家数据[id].角色:添加银子(100000,"65奖励",1)
  玩家数据[id].角色:添加储备(500000,"65级奖励",1)
  if not 玩家数据[id].召唤兽:是否携带上限() then
  玩家数据[id].召唤兽:添加召唤兽("蛟龙","蛟龙","宝宝",nil,65)
  常规提示(id,"#Y/你获得了一只蛟龙")
  end
end

function 礼包奖励类:升级奖励25(id)
  玩家数据[id].角色:添加经验(100000,"25级奖励")
  玩家数据[id].角色:添加银子(10000,"25级奖励",1)
  玩家数据[id].角色:添加储备(50000,"25级奖励",1)
  if not 玩家数据[id].召唤兽:是否携带上限() then
  玩家数据[id].召唤兽:添加召唤兽("狼","狼","宝宝",nil,25)
  常规提示(id,"#Y/你获得了一只狼")
  end
end



function 礼包奖励类:全套灵饰(id,等级,无级别,专用)

  for i,v in ipairs(随机灵饰) do
    local 名称=制造装备[v][等级]
    local 临时id=玩家数据[id].道具:取新编号()
    local 临时格子=玩家数据[id].角色:取道具格子()
    local 道具 = 物品类()
    道具:置对象(名称)
    道具.级别限制 = 等级
    道具.幻化等级=0
    道具.幻化属性={附加={},}
    道具.识别码=取唯一识别码(id)
    local 主属性 = 灵饰属性[v].主属性[取随机数(1,#灵饰属性[v].主属性)]
    local 主数值=取随机数(灵饰属性.基础[主属性][等级].a,灵饰属性.基础[主属性][等级].b)
    主数值=math.floor(主数值*1.1)
    道具.幻化属性.基础={类型=主属性,数值=主数值,强化=0}
    for n=1,4 do
        local 副属性=灵饰属性[v].副属性[取随机数(1,#灵饰属性[v].副属性)]
        local 副数值=math.floor(取随机数(灵饰属性.基础[副属性][等级].a,灵饰属性.基础[副属性][等级].b))
        道具.幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
    end
    玩家数据[id].道具.数据[临时id]=道具
    玩家数据[id].道具.数据[临时id].部位类型=v
    玩家数据[id].道具.数据[临时id].灵饰=true
    玩家数据[id].道具.数据[临时id].鉴定=false
    玩家数据[id].道具.数据[临时id].耐久=500
    玩家数据[id].道具.数据[临时id].特效=无级别
    if 专用~=nil then
      玩家数据[id].道具.数据[临时id].专用=id
      玩家数据[id].道具.数据[临时id].不可交易=true
    end
    玩家数据[id].角色.数据.道具[临时格子]=临时id
    常规提示(id,"#Y/你得到了#R/"..名称)
  end
end


function 礼包奖励类:随机灵饰(id,等级,无级别,专用)
      local 部位=随机灵饰[取随机数(1,#随机灵饰)]
      local 名称=制造装备[部位][等级]
      local 临时id=玩家数据[id].道具:取新编号()
      local 临时格子=玩家数据[id].角色:取道具格子()
      local 道具 = 物品类()
      道具:置对象(名称)
      道具.级别限制 = 等级
      道具.幻化等级=0
      道具.幻化属性={附加={},}
      道具.识别码=取唯一识别码(id)
      local 主属性 = 灵饰属性[部位].主属性[取随机数(1,#灵饰属性[部位].主属性)]
      local 主数值=取随机数(灵饰属性.基础[主属性][等级].a,灵饰属性.基础[主属性][等级].b)
      主数值=math.floor(主数值*1.1)
      道具.幻化属性.基础={类型=主属性,数值=主数值,强化=0}
      for n=1,4 do
            local 副属性=灵饰属性[部位].副属性[取随机数(1,#灵饰属性[部位].副属性)]
            local 副数值=math.floor(取随机数(灵饰属性.基础[副属性][等级].a,灵饰属性.基础[副属性][等级].b))
            道具.幻化属性.附加[n]={类型=副属性,数值=副数值,强化=0}
      end
      玩家数据[id].道具.数据[临时id]=道具
      玩家数据[id].道具.数据[临时id].部位类型=部位
      玩家数据[id].道具.数据[临时id].灵饰=true
      玩家数据[id].道具.数据[临时id].鉴定=false
      玩家数据[id].道具.数据[临时id].耐久=500
      玩家数据[id].道具.数据[临时id].特效=无级别
      玩家数据[id].角色.数据.道具[临时格子]=临时id
      常规提示(id,"#Y/你得到了#R/"..名称)
end


function 礼包奖励类:全套装备(id,等级,无级别,专用,公式)
  等级=等级/10
  等级=math.floor(等级)
  local 模型=玩家数据[id].角色.数据.模型
  local 武器序列=角色武器类型[模型][取随机数(1,#角色武器类型[模型])]
  local 武器名称=玩家数据[id].装备.打造物品[武器序列][等级+1]
  if 等级>=9 and 等级<12 then
    武器名称=玩家数据[id].装备.打造物品[武器序列][取随机数(10,12)]
  elseif 等级>=12 and 等级<15 then
    武器名称=玩家数据[id].装备.打造物品[武器序列][取随机数(13,15)]
  end
  local 衣服类型=2
  if 玩家数据[id].角色.数据.性别=="女" then
    衣服类型=1
  end
  local 衣服序列=21
  local 衣服名称= 玩家数据[id].装备.打造物品[衣服序列][等级+1][衣服类型]
  local 头盔类型=1
  if 玩家数据[id].角色.数据.性别=="女" then
    头盔类型=2
  end
  local 头盔序列=19
  local 头盔名称= 玩家数据[id].装备.打造物品[头盔序列][等级+1][头盔类型]
  local 项链序列=20
  local 项链名称= 玩家数据[id].装备.打造物品[项链序列][等级+1][取随机数(1,2)]
  if type(玩家数据[id].装备.打造物品[项链序列][等级+1])=="table" then
    项链名称= 玩家数据[id].装备.打造物品[项链序列][等级+1][取随机数(1,2)]
  else
    项链名称= 玩家数据[id].装备.打造物品[项链序列][等级+1]
  end
  local 腰带序列=22
  local 腰带名称= 玩家数据[id].装备.打造物品[腰带序列][等级+1][取随机数(1,2)]
  if type(玩家数据[id].装备.打造物品[腰带序列][等级+1])=="table" then
    腰带名称= 玩家数据[id].装备.打造物品[腰带序列][等级+1][取随机数(1,2)]
  else
    腰带名称= 玩家数据[id].装备.打造物品[腰带序列][等级+1]
  end
  local 鞋子序列=23
  local 鞋子名称= 玩家数据[id].装备.打造物品[鞋子序列][等级+1]
  local 临时id=玩家数据[id].装备:生成指定装备(id,武器名称,等级*10,武器序列,公式)--武器
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end

  local 临时id=玩家数据[id].装备:生成指定装备(id,衣服名称,等级*10,衣服序列,公式)--衣服
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end

  local 临时id=玩家数据[id].装备:生成指定装备(id,头盔名称,等级*10,头盔序列,公式)--头盔
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end

  local 临时id=玩家数据[id].装备:生成指定装备(id,项链名称,等级*10,项链序列,公式)--项链
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end

  local 临时id=玩家数据[id].装备:生成指定装备(id,腰带名称,等级*10,腰带序列,公式)--腰带
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end

  local 临时id=玩家数据[id].装备:生成指定装备(id,鞋子名称,等级*10,鞋子序列,公式)--鞋子
  if 无级别~=nil then
    玩家数据[id].道具.数据[临时id].特效=无级别
  end
  if 专用~=nil then
    玩家数据[id].道具.数据[临时id].专用=id
    玩家数据[id].道具.数据[临时id].不可交易=true
  end
  常规提示(id,"#Y/你获得了"..(等级*10).."级全套装备")
end





function 礼包奖励类:更新(dt) end
function 礼包奖励类:显示(x,y) end

return 礼包奖励类