
function 引擎召唤兽头像(jp)--梦战
	local jps = {}
	if jp == "巨蛙" then
		jps = {小图标="0xAD983A7D",大图标="0x2D8CFBA2",对话头像="0x3F9317BB",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=900,防御资质=500,体力资质=1500,法力资质=1000,速度资质=1000,躲闪资质=800,寿命=10000,成长=55,携带技能表=nil}
		return jps
	elseif jp == "大海龟" then
		jps = {小图标="0xA1D1788D",大图标="0xC39E889A",对话头像="0x12F27887",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=800,防御资质=500,体力资质=2000,法力资质=800,速度资质=500,躲闪资质=500,寿命=10000,成长=55,携带技能表=nil}
		return jps
	elseif jp == "护卫" then
		jps = {小图标="0xB3000CA6",大图标="0x1B6CEF18",对话头像="0xA8B839FB",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=900,防御资质=1000,体力资质=2000,法力资质=1000,速度资质=1000,躲闪资质=800,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "树怪" then
		jps = {小图标="0x30283E25",大图标="0x7D06D3B0",对话头像="0xE3B6336F",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=900,防御资质=1200,体力资质=2400,法力资质=1200,速度资质=300,躲闪资质=0,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "赌徒" then
		jps = {小图标="0x1DF9E861",大图标="0x6B0A60D",对话头像="0xFC840EA4",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=1000,防御资质=900,体力资质=1500,法力资质=800,速度资质=1000,躲闪资质=1000,寿命=10000,成长=55,携带技能表=nil}
		return jps
	elseif jp == "强盗" then
		jps = {小图标="0x90B8EAEA",大图标="0x326CC5C6",对话头像="0xD206038F",造型变异染色点表=nil,装饰品编号表={0},参战等级=15,攻击资质=1100,防御资质=1000,体力资质=2300,法力资质=1000,速度资质=1000,躲闪资质=1000,寿命=10000,成长=65,携带技能表=nil}
		return jps
	elseif jp == "海毛虫" then
		jps = {小图标="0x82B64797",大图标="0x5786AD5A",对话头像="0xC9D5AC2C",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=1200,防御资质=700,体力资质=1500,法力资质=900,速度资质=1200,躲闪资质=800,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "大蝙蝠" then
		jps = {小图标="0xCD03B9B4",大图标="0x90ECA422",对话头像="0x6A827437",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=1100,防御资质=1000,体力资质=2000,法力资质=900,速度资质=1200,躲闪资质=1000,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "山贼" then
		jps = {小图标="0xA5D4465C",大图标="0xED08D563",对话头像="0x670D2012",造型变异染色点表=nil,装饰品编号表={0},参战等级=5,攻击资质=1100,防御资质=1000,体力资质=2000,法力资质=900,速度资质=1000,躲闪资质=1000,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "野猪" then
		jps = {小图标="0xDF608E1D",大图标="0x87F188A",对话头像="0xBD2DBF43",造型变异染色点表=nil,装饰品编号表={0,557},参战等级=5,攻击资质=1200,防御资质=1200,体力资质=3000,法力资质=800,速度资质=1100,躲闪资质=1200,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "骷髅怪" then
		jps = {小图标="0x7D0A4C59",大图标="0xE358B4CA",对话头像="0xFB588E74",造型变异染色点表=nil,装饰品编号表={0},参战等级=15,攻击资质=1300,防御资质=1400,体力资质=2000,法力资质=1000,速度资质=1000,躲闪资质=1000,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "羊头怪" then
		jps = {小图标="0xB6D11E31",大图标="0xCD722FCC",对话头像="0xA3D8CF41",造型变异染色点表=nil,装饰品编号表={0,560},参战等级=15,攻击资质=1300,防御资质=1300,体力资质=2000,法力资质=1100,速度资质=1000,躲闪资质=1000,寿命=10000,成长=60,携带技能表=nil}
		return jps
	elseif jp == "蛤蟆精" then
		jps = {小图标="0xEA73096F",大图标="0xFF46A6F0",对话头像="0xABB5D563",造型变异染色点表=nil,装饰品编号表={0},参战等级=15,攻击资质=1300,防御资质=1200,体力资质=2300,法力资质=1000,速度资质=1100,躲闪资质=1000,寿命=10000,成长=65,携带技能表=nil}
		return jps
	elseif jp == "老虎" then
		jps = {小图标="0xABF4C334",大图标="0x6B92734",对话头像="0x2BBE6961",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1400,防御资质=1200,体力资质=2800,法力资质=1000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=70,携带技能表=nil}
		return jps
	elseif jp == "黑熊" then
		jps = {小图标="0x391491B5",大图标="0x716CAC43",对话头像="0x6F826A9A",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1200,防御资质=1300,体力资质=3000,法力资质=1000,速度资质=800,躲闪资质=800,寿命=10000,成长=70,携带技能表=nil}
		return jps
	elseif jp == "花妖" then
		jps = {小图标="0x8B6165CA",大图标="0x866ABD8C",对话头像="0xC9829481",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=700,防御资质=1200,体力资质=2200,法力资质=1200,速度资质=1000,躲闪资质=500,寿命=10000,成长=70,携带技能表=nil}
		return jps
	elseif jp == "牛妖" then
		jps = {小图标="0xC12CEA44",大图标="0xA969C5A",对话头像="0xF5108507",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1300,防御资质=1200,体力资质=3000,法力资质=1100,速度资质=1000,躲闪资质=1000,寿命=10000,成长=70,携带技能表=nil}
		return jps
	elseif jp == "小龙女" then
		jps = {小图标="0x3C45F1BC",大图标="0x4CE7BFFF",对话头像="0x3AE8E649",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1300,防御资质=1500,体力资质=3500,法力资质=2000,速度资质=1200,躲闪资质=1300,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "野鬼" then
		jps = {小图标="0x6F35B36B",大图标="0x62663668",对话头像="0x5F37AE22",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1100,防御资质=1200,体力资质=2500,法力资质=1200,速度资质=800,躲闪资质=800,寿命=10000,成长=70,携带技能表=nil}
		return jps
	elseif jp == "狼" then
		jps = {小图标="0x6242A2EC",大图标="0x3C920EEA",对话头像="0x6EA6C37F",造型变异染色点表=nil,装饰品编号表={0},参战等级=25,攻击资质=1300,防御资质=700,体力资质=2000,法力资质=800,速度资质=1300,躲闪资质=1300,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "虾兵" then
		jps = {小图标="0xDC7C4250",大图标="0xFF844C07",对话头像="0xA309B1F5",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1200,防御资质=1200,体力资质=2500,法力资质=1100,速度资质=1000,躲闪资质=1000,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "蟹将" then
		jps = {小图标="0x24D3F0A1",大图标="0x4970522D",对话头像="0xC15BCAD3",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1300,防御资质=1300,体力资质=3000,法力资质=1100,速度资质=1200,躲闪资质=100,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "龟丞相" then
		jps = {小图标="0xEC1CEB45",大图标="0xAA3785D1",对话头像="0x935EEC21",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=500,防御资质=1300,体力资质=3500,法力资质=1200,速度资质=800,躲闪资质=500,寿命=10000,成长=85,携带技能表=nil}
		return jps
	elseif jp == "兔子怪" then
		jps = {小图标="0x3A19399F",大图标="0xC9B56A98",对话头像="0x2F144964",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1200,防御资质=1000,体力资质=2300,法力资质=1000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=90,携带技能表=nil}
		return jps
	elseif jp == "蜘蛛精" then
		jps = {小图标="0xE91A957",大图标="0xE67F149E",对话头像="0xE148FC17",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1200,防御资质=1000,体力资质=2500,法力资质=1100,速度资质=1100,躲闪资质=1200,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "黑熊精" then
		jps = {小图标="0x57BDB8F9",大图标="0x79CEA1EA",对话头像="0x3DAAEC8C",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1400,防御资质=1300,体力资质=3500,法力资质=1000,速度资质=800,躲闪资质=1000,寿命=10000,成长=90,携带技能表=nil}
		return jps
	elseif jp == "僵尸" then
		jps = {小图标="0xE079A778",大图标="0xF481072B",对话头像="0x12F1D6D1",造型变异染色点表=nil,装饰品编号表={0},参战等级=35,攻击资质=1100,防御资质=1200,体力资质=4000,法力资质=1000,速度资质=700,躲闪资质=200,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "牛头" then
		jps = {小图标="0x6DB89271",大图标="0xCDD130C4",对话头像="0x1777351E",造型变异染色点表=nil,装饰品编号表={0,577},参战等级=45,攻击资质=1100,防御资质=1200,体力资质=3800,法力资质=1100,速度资质=1000,躲闪资质=1000,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "马面" then
		jps = {小图标="0x37AB66AF",大图标="0xA5C7D24B",对话头像="0xA8737330",造型变异染色点表=nil,装饰品编号表={0,579},参战等级=45,攻击资质=1300,防御资质=1100,体力资质=3800,法力资质=1100,速度资质=1000,躲闪资质=1000,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "雷鸟人" then
		jps = {小图标="0x24359F3E",大图标="0xA7EE2B7B",对话头像="0x682F5D07",造型变异染色点表=nil,装饰品编号表={0,581},参战等级=45,攻击资质=1000,防御资质=1200,体力资质=3000,法力资质=1700,速度资质=1000,躲闪资质=1000,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "进阶雷鸟人" then
		jps = {小图标="0x889ADB49",大图标="0xF3DC6441",对话头像="0x9412D9A5",造型变异染色点表=nil,装饰品编号表={0,583,584},参战等级=45,攻击资质=1000,防御资质=1200,体力资质=3000,法力资质=1700,速度资质=1000,躲闪资质=1000,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "蝴蝶仙子" then
		jps = {小图标="0x63FB33ED",大图标="0xF3307792",对话头像="0x99F2D6C7",造型变异染色点表=nil,装饰品编号表={0,586},参战等级=45,攻击资质=1200,防御资质=1000,体力资质=2400,法力资质=2500,速度资质=1000,躲闪资质=1000,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "进阶蝴蝶仙子" then
		jps = {小图标="0x34E3FA38",大图标="0xAE5074AB",对话头像="0x74DE4984",造型变异染色点表=nil,装饰品编号表={0,588},参战等级=45,攻击资质=1200,防御资质=1000,体力资质=2400,法力资质=2500,速度资质=1000,躲闪资质=1000,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "古代瑞兽" then
		jps = {小图标="0xA50A1D29",大图标="0x5E04219",对话头像="0xDBE940E7",造型变异染色点表=nil,装饰品编号表={0,590},参战等级=45,攻击资质=1000,防御资质=1200,体力资质=2800,法力资质=2000,速度资质=1200,躲闪资质=1100,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "进阶古代瑞兽" then
		jps = {小图标="0x7470E5F",大图标="0x169DF68B",对话头像="0x7AB4492A",造型变异染色点表=nil,装饰品编号表={0,592},参战等级=45,攻击资质=1000,防御资质=1200,体力资质=2800,法力资质=2000,速度资质=1200,躲闪资质=1100,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "白熊" then
		jps = {小图标="0xA03A62ED",大图标="0x49DCC4AE",对话头像="0xA8AE532E",造型变异染色点表=nil,装饰品编号表={0,594},参战等级=45,攻击资质=1200,防御资质=1400,体力资质=4000,法力资质=1000,速度资质=1000,躲闪资质=1000,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "进阶白熊" then
		jps = {小图标="0xB47F2149",大图标="0xDDB1C111",对话头像="0x14FA4D9B",造型变异染色点表=nil,装饰品编号表={0,596,597},参战等级=45,攻击资质=1200,防御资质=1400,体力资质=4000,法力资质=1000,速度资质=1000,躲闪资质=1000,寿命=10000,成长=95,携带技能表=nil}
		return jps
	elseif jp == "黑山老妖" then
		jps = {小图标="0xC55C704D",大图标="0xF447F3CB",对话头像="0xBEE87DE0",造型变异染色点表=nil,装饰品编号表={0},参战等级=45,攻击资质=1200,防御资质=1200,体力资质=3000,法力资质=1300,速度资质=1100,躲闪资质=1000,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶黑山老妖" then
		jps = {小图标="0xD58C883C",大图标="0x4F942965",对话头像="0x17979901",造型变异染色点表=nil,装饰品编号表={0,600},参战等级=45,攻击资质=1200,防御资质=1200,体力资质=3000,法力资质=1300,速度资质=1100,躲闪资质=1000,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "天兵" then
		jps = {小图标="0xE3A356D4",大图标="0xF7159B8A",对话头像="0xB836DAF9",造型变异染色点表=nil,装饰品编号表={0,602},参战等级=55,攻击资质=1200,防御资质=1200,体力资质=3000,法力资质=1300,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶天兵" then
		jps = {小图标="0xB56ECBA9",大图标="0x338A1A07",对话头像="0xF14B1666",造型变异染色点表=nil,装饰品编号表={0,604,605},参战等级=55,攻击资质=1200,防御资质=1200,体力资质=3000,法力资质=1300,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "天将" then
		jps = {小图标="0x2B1FC562",大图标="0x78F204D8",对话头像="0xF333F834",造型变异染色点表=nil,装饰品编号表={0},参战等级=55,攻击资质=1300,防御资质=1200,体力资质=3000,法力资质=1200,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶天将" then
		jps = {小图标="0xFE5AED33",大图标="0x1BA73176",对话头像="0x4C12C540",造型变异染色点表=nil,装饰品编号表={0,608,609},参战等级=55,攻击资质=1300,防御资质=1200,体力资质=3000,法力资质=1200,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "地狱战神" then
		jps = {小图标="0x8CA33C58",大图标="0x2C7C0D0E",对话头像="0xE65B948A",造型变异染色点表=nil,装饰品编号表={0},参战等级=55,攻击资质=1300,防御资质=1000,体力资质=3200,法力资质=1500,速度资质=1000,躲闪资质=800,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶地狱战神" then
		jps = {小图标="0xDB8EDC6F",大图标="0xAF801569",对话头像="0xB9FB91B0",造型变异染色点表=nil,装饰品编号表={0,612,613},参战等级=55,攻击资质=1300,防御资质=1000,体力资质=3200,法力资质=1500,速度资质=1000,躲闪资质=800,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "风伯" then
		jps = {小图标="0xC19E85CA",大图标="0x2AE672A0",对话头像="0x382053A7",造型变异染色点表=nil,装饰品编号表={0},参战等级=65,攻击资质=1000,防御资质=1200,体力资质=3300,法力资质=2000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶风伯" then
		jps = {小图标="0xA081F972",大图标="0x1562D6BB",对话头像="0xD747F00",造型变异染色点表=nil,装饰品编号表={0,616,617},参战等级=65,攻击资质=1000,防御资质=1200,体力资质=3300,法力资质=2000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "凤凰" then
		jps = {小图标="0xA9E3213B",大图标="0x23870ED4",对话头像="0xCE1D8427",造型变异染色点表=nil,装饰品编号表={0,619},参战等级=65,攻击资质=1200,防御资质=1300,体力资质=4000,法力资质=2800,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶凤凰" then
		jps = {小图标="0xC2C70463",大图标="0x5F75075C",对话头像="0x8217B8DC",造型变异染色点表=nil,装饰品编号表={0,621,622},参战等级=65,攻击资质=1200,防御资质=1300,体力资质=4000,法力资质=2800,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "蛟龙" then
		jps = {小图标="0xD11D19BB",大图标="0xF40767A3",对话头像="0xE092C49C",造型变异染色点表=nil,装饰品编号表={0,624},参战等级=65,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2700,速度资质=1300,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶蛟龙" then
		jps = {小图标="0x3DE0EC5F",大图标="0x1B5CA10",对话头像="0x6562E4B2",造型变异染色点表=nil,装饰品编号表={0,626,627},参战等级=65,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2700,速度资质=1300,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "雨师" then
		jps = {小图标="0x8326B8FA",大图标="0x1265B89",对话头像="0x5BAB5E62",造型变异染色点表=nil,装饰品编号表={0},参战等级=65,攻击资质=1200,防御资质=1000,体力资质=3200,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶雨师" then
		jps = {小图标="0x970D69AB",大图标="0x27D380F8",对话头像="0x67E3B916",造型变异染色点表=nil,装饰品编号表={0,630},参战等级=65,攻击资质=1200,防御资质=1000,体力资质=3200,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "如意仙子" then
		jps = {小图标="0xF5A6C5EE",大图标="0x1D5EF06C",对话头像="0xCDE92089",造型变异染色点表=nil,装饰品编号表={0},参战等级=75,攻击资质=1300,防御资质=1200,体力资质=3400,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶如意仙子" then
		jps = {小图标="0x5A7B589D",大图标="0x61631DC2",对话头像="0x6FC80832",造型变异染色点表=nil,装饰品编号表={0,633},参战等级=75,攻击资质=1300,防御资质=1200,体力资质=3400,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "芙蓉仙子" then
		jps = {小图标="0xF5AF49BD",大图标="0xA686FF51",对话头像="0x9F3CAA62",造型变异染色点表=nil,装饰品编号表={0,635},参战等级=75,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2400,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶芙蓉仙子" then
		jps = {小图标="0x5629025",大图标="0xE02D2612",对话头像="0xD8D7F589",造型变异染色点表=nil,装饰品编号表={0,637},参战等级=75,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2400,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "巡游天神" then
		jps = {小图标="0xEDB096B7",大图标="0x7D314BCB",对话头像="0x79D8BFB0",造型变异染色点表=nil,装饰品编号表={0,639},参战等级=75,攻击资质=1400,防御资质=1200,体力资质=4500,法力资质=1500,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶巡游天神" then
		jps = {小图标="0x2287BA3A",大图标="0x3D23BB40",对话头像="0xD5CCD377",造型变异染色点表=nil,装饰品编号表={0,641,642},参战等级=75,攻击资质=1400,防御资质=1200,体力资质=4500,法力资质=1500,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "星灵仙子" then
		jps = {小图标="0x2D07ACA5",大图标="0xB4B2CE74",对话头像="0x79053E58",造型变异染色点表=nil,装饰品编号表={0,644},参战等级=75,攻击资质=1100,防御资质=1300,体力资质=4000,法力资质=2600,速度资质=1200,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶星灵仙子" then
		jps = {小图标="0xD637B35",大图标="0xE0798D11",对话头像="0x6A0687F4",造型变异染色点表=nil,装饰品编号表={0,646,647},参战等级=75,攻击资质=1100,防御资质=1300,体力资质=4000,法力资质=2600,速度资质=1200,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "幽灵" then
		jps = {小图标="0x2B7B7347",大图标="0x4E257A3",对话头像="0xB9FAFFE0",造型变异染色点表=nil,装饰品编号表={0,649},参战等级=95,攻击资质=1200,防御资质=1400,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶幽灵" then
		jps = {小图标="0x49C7656D",大图标="0x99C9B06E",对话头像="0x58E84AA3",造型变异染色点表=nil,装饰品编号表={0,651},参战等级=95,攻击资质=1200,防御资质=1400,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "鬼将" then
		jps = {小图标="0xE3DB5D8A",大图标="0x4647B146",对话头像="0xE84A1BD5",造型变异染色点表=nil,装饰品编号表={0,653},参战等级=105,攻击资质=1500,防御资质=1200,体力资质=3800,法力资质=1800,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶鬼将" then
		jps = {小图标="0x7166C4B0",大图标="0x9B5056BC",对话头像="0x7EC96CE3",造型变异染色点表=nil,装饰品编号表={0,655,656},参战等级=105,攻击资质=1500,防御资质=1200,体力资质=3800,法力资质=1800,速度资质=1200,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "吸血鬼" then
		jps = {小图标="0x523C25F2",大图标="0xE276EEEE",对话头像="0x4847A23B",造型变异染色点表=nil,装饰品编号表={0,658},参战等级=95,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=1300,速度资质=1100,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶吸血鬼" then
		jps = {小图标="0x104B7ED0",大图标="0x7A6D1196",对话头像="0x8F07C67F",造型变异染色点表=nil,装饰品编号表={0,660,661},参战等级=95,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=1300,速度资质=1100,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "净瓶女娲" then
		jps = {小图标="0xE2FB23F9",大图标="0xF371BECC",对话头像="0xE0038C5B",造型变异染色点表=nil,装饰品编号表={0},参战等级=105,攻击资质=1400,防御资质=1400,体力资质=4300,法力资质=2000,速度资质=1300,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶净瓶女娲" then
		jps = {小图标="0x575F5122",大图标="0x4DAF7A6B",对话头像="0x4F8AF53E",造型变异染色点表=nil,装饰品编号表={0,664},参战等级=105,攻击资质=1400,防御资质=1400,体力资质=4300,法力资质=2000,速度资质=1300,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "律法女娲" then
		jps = {小图标="0x49E3AF4D",大图标="0x9F5BB257",对话头像="0xA2DFFD74",造型变异染色点表=nil,装饰品编号表={0,666},参战等级=95,攻击资质=1400,防御资质=1300,体力资质=4100,法力资质=1700,速度资质=1400,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶律法女娲" then
		jps = {小图标="0xFDF5DD4E",大图标="0x33E14781",对话头像="0xB0A284B9",造型变异染色点表=nil,装饰品编号表={0,668},参战等级=95,攻击资质=1400,防御资质=1300,体力资质=4100,法力资质=1700,速度资质=1400,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "灵符女娲" then
		jps = {小图标="0xE04223AA",大图标="0x9BDF74F8",对话头像="0x7107B2E",造型变异染色点表=nil,装饰品编号表={0,670},参战等级=95,攻击资质=1000,防御资质=1300,体力资质=4200,法力资质=2700,速度资质=1200,躲闪资质=1100,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶灵符女娲" then
		jps = {小图标="0x18F4D8E6",大图标="0xAFA2C78F",对话头像="0x2ABCBBB1",造型变异染色点表=nil,装饰品编号表={0,672},参战等级=95,攻击资质=1000,防御资质=1300,体力资质=4200,法力资质=2700,速度资质=1200,躲闪资质=1100,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "大力金刚" then
		jps = {小图标="0xBAB53438",大图标="0x803AC4BA",对话头像="0x936F0B3F",造型变异染色点表=nil,装饰品编号表={0,674},参战等级=125,攻击资质=1500,防御资质=1400,体力资质=5000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶大力金刚" then
		jps = {小图标="0x5E3A7173",大图标="0x23491143",对话头像="0xA3319022",造型变异染色点表=nil,装饰品编号表={0,676,677},参战等级=125,攻击资质=1500,防御资质=1400,体力资质=5000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "雾中仙" then
		jps = {小图标="0x1DEE9FAF",大图标="0xFD7CCBD",对话头像="0x70A11319",造型变异染色点表=nil,装饰品编号表={0,679},参战等级=125,攻击资质=1200,防御资质=1400,体力资质=4500,法力资质=3000,速度资质=1100,躲闪资质=1500,寿命=10000,成长=125,携带技能表=nil}
		return jps
	elseif jp == "进阶雾中仙" then
		jps = {小图标="0x61634205",大图标="0xC59FEED1",对话头像="0x52822010",造型变异染色点表=nil,装饰品编号表={0,681},参战等级=125,攻击资质=1200,防御资质=1400,体力资质=4500,法力资质=3000,速度资质=1100,躲闪资质=1500,寿命=10000,成长=125,携带技能表=nil}
		return jps
	elseif jp == "灵鹤" then
		jps = {小图标="0x8C05FBA0",大图标="0x4A9A53AC",对话头像="0xBF49EAA2",造型变异染色点表=nil,装饰品编号表={0,683},参战等级=125,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2800,速度资质=1300,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶灵鹤" then
		jps = {小图标="0x9B5EFDA5",大图标="0x2EB49559",对话头像="0xF856DDA8",造型变异染色点表=nil,装饰品编号表={0,685},参战等级=125,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2800,速度资质=1300,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "夜罗刹" then
		jps = {小图标="0xDCB316A5",大图标="0x2FF488AD",对话头像="0x3483A111",造型变异染色点表=nil,装饰品编号表={0,687},参战等级=125,攻击资质=1400,防御资质=1300,体力资质=4400,法力资质=2300,速度资质=1400,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶夜罗刹" then
		jps = {小图标="0xCEA91403",大图标="0x63642CBA",对话头像="0x77DC22AF",造型变异染色点表=nil,装饰品编号表={0,689,690},参战等级=125,攻击资质=1400,防御资质=1300,体力资质=4400,法力资质=2300,速度资质=1400,躲闪资质=1400,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "炎魔神" then
		jps = {小图标="0xDEE937E2",大图标="0xC0DF0BB3",对话头像="0xB70F9CFF",造型变异染色点表=nil,装饰品编号表={0},参战等级=125,攻击资质=1300,防御资质=1300,体力资质=4300,法力资质=2700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶炎魔神" then
		jps = {小图标="0x51AA95E8",大图标="0xFFB30D11",对话头像="0x189C10BE",造型变异染色点表=nil,装饰品编号表={0,693,694},参战等级=125,攻击资质=1300,防御资质=1300,体力资质=4300,法力资质=2700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "噬天虎" then
		jps = {小图标="0xBC672A71",大图标="0x1A739A",对话头像="0xF72EA501",造型变异染色点表=nil,装饰品编号表={0,696},参战等级=125,攻击资质=1500,防御资质=1400,体力资质=4800,法力资质=2500,速度资质=1400,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶噬天虎" then
		jps = {小图标="0xF2C12D8C",大图标="0x664D0D94",对话头像="0xE8D35A3",造型变异染色点表=nil,装饰品编号表={0,698,699},参战等级=125,攻击资质=1500,防御资质=1400,体力资质=4800,法力资质=2500,速度资质=1400,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "野猪精" then
		jps = {小图标="0xA05B822",大图标="0x6FF278EE",对话头像="0x91818070",造型变异染色点表=nil,装饰品编号表={0},参战等级=85,攻击资质=1300,防御资质=1200,体力资质=3000,法力资质=1800,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶野猪精" then
		jps = {小图标="0x8356BCC8",大图标="0x8CFBF7CA",对话头像="0xBB7354EB",造型变异染色点表=nil,装饰品编号表={0,702,703},参战等级=85,攻击资质=1300,防御资质=1200,体力资质=3000,法力资质=1800,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "鼠先锋" then
		jps = {小图标="0xC01B0A96",大图标="0x2FFD0CF6",对话头像="0x4FFE22D8",造型变异染色点表=nil,装饰品编号表={0},参战等级=85,攻击资质=1100,防御资质=1100,体力资质=2300,法力资质=2500,速度资质=1400,躲闪资质=1500,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶鼠先锋" then
		jps = {小图标="0x4058F6F2",大图标="0xE8A2F73",对话头像="0x7F884D37",造型变异染色点表=nil,装饰品编号表={0,706,707},参战等级=85,攻击资质=1100,防御资质=1100,体力资质=2300,法力资质=2500,速度资质=1400,躲闪资质=1500,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "百足将军" then
		jps = {小图标="0x96AFF285",大图标="0x8F377E02",对话头像="0x18886385",造型变异染色点表=nil,装饰品编号表={0,709},参战等级=85,攻击资质=1400,防御资质=1300,体力资质=3000,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶百足将军" then
		jps = {小图标="0xD0C8C11A",大图标="0xA56931AA",对话头像="0xE6721E60",造型变异染色点表=nil,装饰品编号表={0,711,712},参战等级=85,攻击资质=1400,防御资质=1300,体力资质=3000,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "踏云兽" then
		jps = {小图标="0xFB368388",大图标="0x24C4114C",对话头像="0x7AC3A8B7",造型变异染色点表=nil,装饰品编号表={0},参战等级=135,攻击资质=1500,防御资质=1400,体力资质=5000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶踏云兽" then
		jps = {小图标="0xCABBCC20",大图标="0xAA349A7E",对话头像="0x9FEA2B83",造型变异染色点表=nil,装饰品编号表={0,715,716},参战等级=135,攻击资质=1500,防御资质=1400,体力资质=5000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "红萼仙子" then
		jps = {小图标="0xAD0EA485",大图标="0x678D3BEF",对话头像="0xC772C514",造型变异染色点表=nil,装饰品编号表={0,718},参战等级=135,攻击资质=1400,防御资质=1400,体力资质=4800,法力资质=3000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=125,携带技能表=nil}
		return jps
	elseif jp == "进阶红萼仙子" then
		jps = {小图标="0x4A1D90BB",大图标="0x19800FE4",对话头像="0x180576C5",造型变异染色点表=nil,装饰品编号表={0},参战等级=135,攻击资质=1400,防御资质=1400,体力资质=4800,法力资质=3000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=125,携带技能表=nil}
		return jps
	elseif jp == "龙龟" then
		jps = {小图标="0x93916DC7",大图标="0x661758CD",对话头像="0x29A4A68B",造型变异染色点表=nil,装饰品编号表={0,721},参战等级=135,攻击资质=1300,防御资质=1500,体力资质=5500,法力资质=2700,速度资质=1000,躲闪资质=800,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶龙龟" then
		jps = {小图标="0x771BE319",大图标="0xE0656D44",对话头像="0x3798DEC5",造型变异染色点表=nil,装饰品编号表={0,723},参战等级=135,攻击资质=1300,防御资质=1500,体力资质=5500,法力资质=2700,速度资质=1000,躲闪资质=800,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "阴阳伞" then
		jps = {小图标="0x745485EC",大图标="0xFABAA53C",对话头像="0x29FADBA2",造型变异染色点表=nil,装饰品编号表={0},参战等级=95,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶阴阳伞" then
		jps = {小图标="0x424AC904",大图标="0xD40FD213",对话头像="0x1F63B17E",造型变异染色点表=nil,装饰品编号表={0,726},参战等级=95,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "机关兽" then
		jps = {小图标="0xB2768F45",大图标="0x9344867F",对话头像="0x18662B0C",造型变异染色点表=nil,装饰品编号表={0,728},参战等级=115,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶机关兽" then
		jps = {小图标="0x356C7A50",大图标="0x18A2200A",对话头像="0x811DF376",造型变异染色点表=nil,装饰品编号表={0,730,731},参战等级=115,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "机关鸟" then
		jps = {小图标="0x6853513E",大图标="0xCE2E6380",对话头像="0x6238418C",造型变异染色点表=nil,装饰品编号表={0,733},参战等级=115,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1400,躲闪资质=1300,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶机关鸟" then
		jps = {小图标="0x90563C9F",大图标="0xCC60E5F8",对话头像="0xF588F5AD",造型变异染色点表=nil,装饰品编号表={0,735,736},参战等级=115,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1400,躲闪资质=1300,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "机关人" then
		jps = {小图标="0xC9A17DF0",大图标="0x72AA73A3",对话头像="0x22D979E5",造型变异染色点表=nil,装饰品编号表={0,738},参战等级=115,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶机关人" then
		jps = {小图标="0x29820FC",大图标="0xA14FE521",对话头像="0x74C8DA04",造型变异染色点表=nil,装饰品编号表={0,740,741},参战等级=115,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "连弩车" then
		jps = {小图标="0x6311F6B5",大图标="0x64A508B",对话头像="0xB2B06815",造型变异染色点表=nil,装饰品编号表={0},参战等级=115,攻击资质=1400,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1200,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶连弩车" then
		jps = {小图标="0x5B266F16",大图标="0x46474602",对话头像="0x64D4DCE4",造型变异染色点表=nil,装饰品编号表={0,744,745},参战等级=115,攻击资质=1400,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1200,躲闪资质=1000,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "巴蛇" then
		jps = {小图标="0x7211A04B",大图标="0x7228F80D",对话头像="0x5B2272A2",造型变异染色点表=nil,装饰品编号表={0,747},参战等级=115,攻击资质=1500,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "进阶巴蛇" then
		jps = {小图标="0x4830D9AC",大图标="0x1FA85CCB",对话头像="0xDB6BC86C",造型变异染色点表=nil,装饰品编号表={0,749},参战等级=115,攻击资质=1500,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "蚌精" then
		jps = {小图标="0xDAAED84D",大图标="0x3D61BEBA",对话头像="0xB1979CB5",造型变异染色点表=nil,装饰品编号表={0},参战等级=65,攻击资质=1100,防御资质=1300,体力资质=2400,法力资质=2300,速度资质=800,躲闪资质=0,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶蚌精" then
		jps = {小图标="0x8E7FDE48",大图标="0x9C4F4F05",对话头像="0x8D65D1C6",造型变异染色点表=nil,装饰品编号表={0,752},参战等级=65,攻击资质=1100,防御资质=1300,体力资质=2400,法力资质=2300,速度资质=800,躲闪资质=0,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "鲛人" then
		jps = {小图标="0xB288965B",大图标="0xDCA5D6A5",对话头像="0x5A7D1D79",造型变异染色点表=nil,装饰品编号表={0,754},参战等级=65,攻击资质=1300,防御资质=1200,体力资质=2800,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶鲛人" then
		jps = {小图标="0x76D75D59",大图标="0x2C03DE10",对话头像="0x28958C68",造型变异染色点表=nil,装饰品编号表={0,756,757},参战等级=65,攻击资质=1300,防御资质=1200,体力资质=2800,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "碧水夜叉" then
		jps = {小图标="0x45FB4FA3",大图标="0x1987AF6",对话头像="0xE8E3E7EF",造型变异染色点表=nil,装饰品编号表={0,759},参战等级=65,攻击资质=1300,防御资质=1400,体力资质=2400,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶碧水夜叉" then
		jps = {小图标="0x78263198",大图标="0x448C6BD1",对话头像="0xD7EC25CB",造型变异染色点表=nil,装饰品编号表={0,761,762},参战等级=65,攻击资质=1300,防御资质=1400,体力资质=2400,法力资质=1700,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "锦毛貂精" then
		jps = {小图标="0xE49549D0",大图标="0x2CBD658C",对话头像="0x35C88896",造型变异染色点表=nil,装饰品编号表={0},参战等级=75,攻击资质=1000,防御资质=1300,体力资质=2000,法力资质=2500,速度资质=1400,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶锦毛貂精" then
		jps = {小图标="0xFBE50E8E",大图标="0x6882B54D",对话头像="0x20A129D9",造型变异染色点表=nil,装饰品编号表={0,765,766},参战等级=75,攻击资质=1000,防御资质=1300,体力资质=2000,法力资质=2500,速度资质=1400,躲闪资质=1400,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "千年蛇魅" then
		jps = {小图标="0x39F807E5",大图标="0xAB2ED147",对话头像="0x879899EC",造型变异染色点表=nil,装饰品编号表={0},参战等级=75,攻击资质=1300,防御资质=1200,体力资质=2400,法力资质=1800,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶千年蛇魅" then
		jps = {小图标="0x5B3E9AC6",大图标="0x389718FA",对话头像="0x9CEE85F2",造型变异染色点表=nil,装饰品编号表={0,769,770},参战等级=75,攻击资质=1300,防御资质=1200,体力资质=2400,法力资质=1800,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "犀牛将军人形" then
		jps = {小图标="0xB0F27967",大图标="0xE87D7BE8",对话头像="0xBAE75C40",造型变异染色点表=nil,装饰品编号表={0,772},参战等级=85,攻击资质=1400,防御资质=1200,体力资质=3000,法力资质=1900,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶犀牛将军人形" then
		jps = {小图标="0xDB8B5108",大图标="0x87CCFBC2",对话头像="0x4045D281",造型变异染色点表=nil,装饰品编号表={0,774,775},参战等级=85,攻击资质=1400,防御资质=1200,体力资质=3000,法力资质=1900,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "犀牛将军兽形" then
		jps = {小图标="0x3ACA3DA0",大图标="0x40391D43",对话头像="0xD905862",造型变异染色点表=nil,装饰品编号表={0},参战等级=85,攻击资质=1000,防御资质=1200,体力资质=3000,法力资质=2400,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶犀牛将军兽形" then
		jps = {小图标="0x67F3C026",大图标="0x104306B5",对话头像="0x6B1D603C",造型变异染色点表=nil,装饰品编号表={0,778},参战等级=85,攻击资质=1000,防御资质=1200,体力资质=3000,法力资质=2400,速度资质=1200,躲闪资质=1100,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "机关人车" then
		jps = {小图标="0xC9A17DF0",大图标="0x72AA73A3",对话头像="0x22D979E5",造型变异染色点表=nil,装饰品编号表={0,780},参战等级=115,攻击资质=1400,防御资质=1200,体力资质=4000,法力资质=2000,速度资质=1300,躲闪资质=1200,寿命=10000,成长=115,携带技能表=nil}
		return jps
	elseif jp == "葫芦宝贝" then
		jps = {小图标="0x2B6361F6",大图标="0x3A745390",对话头像="0xDDFFBC85",造型变异染色点表=nil,装饰品编号表={782,783},参战等级=125,攻击资质=1100,防御资质=1200,体力资质=3000,法力资质=2500,速度资质=1300,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶葫芦宝贝" then
		jps = {小图标="0xED21E5BD",大图标="0xE4D1C361",对话头像="0x1BE9CEAF",造型变异染色点表=nil,装饰品编号表={785},参战等级=125,攻击资质=1100,防御资质=1200,体力资质=3000,法力资质=2500,速度资质=1300,躲闪资质=1300,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "猫灵人形" then
		jps = {小图标="0x8DC67AD2",大图标="0xC5B785D6",对话头像="0x59A1378C",造型变异染色点表=nil,装饰品编号表={787,788},参战等级=155,攻击资质=1500,防御资质=1500,体力资质=5000,法力资质=4000,速度资质=1700,躲闪资质=1700,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶猫灵人形" then
		jps = {小图标="0xB1CE2D78",大图标="0xF808B6DE",对话头像="0x55F596EF",造型变异染色点表=nil,装饰品编号表={790},参战等级=155,攻击资质=1500,防御资质=1500,体力资质=5000,法力资质=4000,速度资质=1700,躲闪资质=1700,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "猫灵兽形" then
		jps = {小图标="0x4877C0E7",大图标="0xDCF4C65B",对话头像="0x43D79120",造型变异染色点表=nil,装饰品编号表={792,793,794,795},参战等级=145,攻击资质=1300,防御资质=1400,体力资质=4000,法力资质=2400,速度资质=1500,躲闪资质=1900,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "狂豹兽形" then
		jps = {小图标="0x7411E9",大图标="0x51DD0C75",对话头像="0xB58A25B2",造型变异染色点表=nil,装饰品编号表={0,797},参战等级=145,攻击资质=1500,防御资质=1400,体力资质=5500,法力资质=2400,速度资质=1700,躲闪资质=1900,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶狂豹兽形" then
		jps = {小图标="0x7DE142E6",大图标="0x9CE69D03",对话头像="0xA7154AB2",造型变异染色点表=nil,装饰品编号表={0,799},参战等级=145,攻击资质=1500,防御资质=1400,体力资质=5500,法力资质=2400,速度资质=1700,躲闪资质=1900,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "狂豹人形" then
		jps = {小图标="0xD17D382",大图标="0x5ADE76FE",对话头像="0xE544351B",造型变异染色点表=nil,装饰品编号表={801,802},参战等级=155,攻击资质=1600,防御资质=1500,体力资质=5000,法力资质=2700,速度资质=1500,躲闪资质=1600,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶狂豹人形" then
		jps = {小图标="0x2E77E22D",大图标="0x299E9267",对话头像="0xAED39D9A",造型变异染色点表=nil,装饰品编号表={804},参战等级=155,攻击资质=1600,防御资质=1500,体力资质=5000,法力资质=2700,速度资质=1500,躲闪资质=1600,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "蝎子精" then
		jps = {小图标="0x22FC262A",大图标="0x455347A8",对话头像="0xE2C0E97B",造型变异染色点表=nil,装饰品编号表={806,807},参战等级=155,攻击资质=1600,防御资质=1500,体力资质=5500,法力资质=3800,速度资质=1400,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶蝎子精" then
		jps = {小图标="0xBEBE2DE2",大图标="0x7E4C42CB",对话头像="0xA9492BBC",造型变异染色点表=nil,装饰品编号表={809},参战等级=155,攻击资质=1600,防御资质=1500,体力资质=5500,法力资质=3800,速度资质=1400,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "混沌兽" then
		jps = {小图标="0xFD5D97F4",大图标="0xFD5D97F4",对话头像="0x966AD1A1",造型变异染色点表=nil,装饰品编号表={0,811},参战等级=155,攻击资质=1000,防御资质=1600,体力资质=6000,法力资质=4000,速度资质=1100,躲闪资质=1000,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶混沌兽" then
		jps = {小图标="0x75271272",大图标="0x7AE84C6F",对话头像="0x780FB38A",造型变异染色点表=nil,装饰品编号表={0,813},参战等级=155,攻击资质=1000,防御资质=1600,体力资质=6000,法力资质=4000,速度资质=1100,躲闪资质=1000,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "超级人参娃娃" then
		jps = {小图标="0xECBF79F6",大图标="0x9FA1385",对话头像="0x93D56020",造型变异染色点表=nil,装饰品编号表={0},参战等级=75,攻击资质=1400,防御资质=1400,体力资质=7000,法力资质=3000,速度资质=1300,躲闪资质=1000,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶超级人参娃娃" then
		jps = {小图标="0xECBF79F6",大图标="0x9FA1385",对话头像="0x93D56020",造型变异染色点表=nil,装饰品编号表={0},参战等级=75,攻击资质=1400,防御资质=1400,体力资质=7000,法力资质=3000,速度资质=1300,躲闪资质=1000,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "画魂" then
		jps = {小图标="0x5FF60C89",大图标="0xD0D5C17E",对话头像="0x2565C19F",造型变异染色点表=nil,装饰品编号表={0,817},参战等级=105,攻击资质=1100,防御资质=1500,体力资质=3000,法力资质=2700,速度资质=1000,躲闪资质=1000,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶画魂" then
		jps = {小图标="0x8EBBBD08",大图标="0xBEC6885C",对话头像="0x76554054",造型变异染色点表=nil,装饰品编号表={0,819},参战等级=105,攻击资质=1100,防御资质=1500,体力资质=3000,法力资质=2700,速度资质=1000,躲闪资质=1000,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "长眉灵猴" then
		jps = {小图标="0x6CD0A68C",大图标="0xED53371C",对话头像="0xD3A40262",造型变异染色点表=nil,装饰品编号表={0,821},参战等级=95,攻击资质=900,防御资质=1000,体力资质=3000,法力资质=2800,速度资质=1000,躲闪资质=800,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶长眉灵猴" then
		jps = {小图标="0x9FA88D38",大图标="0x60B8DB4",对话头像="0x990F4826",造型变异染色点表=nil,装饰品编号表={0,823,824},参战等级=95,攻击资质=900,防御资质=1000,体力资质=3000,法力资质=2800,速度资质=1000,躲闪资质=800,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "巨力神猿" then
		jps = {小图标="0xC8DA49A9",大图标="0x520D9528",对话头像="0x78647E78",造型变异染色点表=nil,装饰品编号表={0,826},参战等级=95,攻击资质=1400,防御资质=1200,体力资质=3000,法力资质=1200,速度资质=1300,躲闪资质=1300,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶巨力神猿" then
		jps = {小图标="0x18EA183",大图标="0x4A4A014E",对话头像="0x137F323A",造型变异染色点表=nil,装饰品编号表={0,828,829},参战等级=95,攻击资质=1400,防御资质=1200,体力资质=3000,法力资质=1200,速度资质=1300,躲闪资质=1300,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "修罗傀儡鬼" then
		jps = {小图标="0xF1738910",大图标="0xA0FA8D38",对话头像="0xA79D1138",造型变异染色点表=nil,装饰品编号表={0,831},参战等级=165,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=2400,速度资质=1500,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶修罗傀儡鬼" then
		jps = {小图标="0xF939992C",大图标="0x1D5E937A",对话头像="0x1C18BBC4",造型变异染色点表=nil,装饰品编号表={0,833},参战等级=165,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=2400,速度资质=1500,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "蔓藤妖花" then
		jps = {小图标="0xB13B2CE",大图标="0x7D0DC47",对话头像="0xD782089",造型变异染色点表=nil,装饰品编号表={0,835},参战等级=165,攻击资质=1400,防御资质=1700,体力资质=8000,法力资质=4200,速度资质=1200,躲闪资质=1000,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "进阶蔓藤妖花" then
		jps = {小图标="0xCF8279AC",大图标="0xF5A58149",对话头像="0xB01BAE4E",造型变异染色点表=nil,装饰品编号表={0,837},参战等级=165,攻击资质=1400,防御资质=1700,体力资质=8000,法力资质=4200,速度资质=1200,躲闪资质=1000,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "蜃气妖" then
		jps = {小图标="0xC13F272A",大图标="0xDD34FDF2",对话头像="0x3382B99C",造型变异染色点表=nil,装饰品编号表={0,839},参战等级=165,攻击资质=1600,防御资质=1400,体力资质=7000,法力资质=3000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶蜃气妖" then
		jps = {小图标="0xD08C301F",大图标="0x5C2D7FA",对话头像="0xA7EB4FFE",造型变异染色点表=nil,装饰品编号表={0,841},参战等级=165,攻击资质=1600,防御资质=1400,体力资质=7000,法力资质=3000,速度资质=1400,躲闪资质=1400,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "修罗傀儡妖" then
		jps = {小图标="0xFF6EB365",大图标="0xEBC029C",对话头像="0xAD07D8A2",造型变异染色点表=nil,装饰品编号表={0,843},参战等级=165,攻击资质=1700,防御资质=1500,体力资质=8000,法力资质=4000,速度资质=1500,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶修罗傀儡妖" then
		jps = {小图标="0x21808A04",大图标="0x10C6D8CE",对话头像="0xA87B7E1C",造型变异染色点表=nil,装饰品编号表={0,845},参战等级=165,攻击资质=1700,防御资质=1500,体力资质=8000,法力资质=4000,速度资质=1500,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "金身罗汉" then
		jps = {小图标="0x6701D105",大图标="0xB3A2A9E0",对话头像="0xD00C9DA9",造型变异染色点表=nil,装饰品编号表={847,848},参战等级=165,攻击资质=1500,防御资质=1800,体力资质=8000,法力资质=3000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶金身罗汉" then
		jps = {小图标="0x73C81E6",大图标="0x44C0FAB2",对话头像="0xFEA664CC",造型变异染色点表=nil,装饰品编号表={850,851},参战等级=165,攻击资质=1500,防御资质=1800,体力资质=8000,法力资质=3000,速度资质=1300,躲闪资质=1400,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "曼珠沙华" then
		jps = {小图标="0xC0EC59C",大图标="0x3AEAC686",对话头像="0xA9BF2A70",造型变异染色点表=nil,装饰品编号表={0,853},参战等级=165,攻击资质=1500,防御资质=1700,体力资质=8000,法力资质=4500,速度资质=1400,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶曼珠沙华" then
		jps = {小图标="0x10F50864",大图标="0x917F4983",对话头像="0x3290F1E8",造型变异染色点表=nil,装饰品编号表={0,855,856},参战等级=165,攻击资质=1500,防御资质=1700,体力资质=8000,法力资质=4500,速度资质=1400,躲闪资质=1500,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "超级泡泡" then
		jps = {小图标="0x6EC5F31C",大图标="0x5D49C79F",对话头像="0xD6DBEF71",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=3000,速度资质=1500,躲闪资质=1200,寿命=10000,成长=150,携带技能表=nil}
		return jps
	elseif jp == "进阶超级泡泡" then
		jps = {小图标="0x6EC5F31C",大图标="0x5D49C79F",对话头像="0xD6DBEF71",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=3000,速度资质=1500,躲闪资质=1200,寿命=10000,成长=150,携带技能表=nil}
		return jps
	elseif jp == "超级大熊猫" then
		jps = {小图标="0x74E05925",大图标="0x37FA7B7E",对话头像="0x15CC9BB5",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1300,防御资质=1000,体力资质=6000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶超级大熊猫" then
		jps = {小图标="0x74E05925",大图标="0x37FA7B7E",对话头像="0x15CC9BB5",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1300,防御资质=1000,体力资质=6000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "超级金猴" then
		jps = {小图标="0x237D8B3E",大图标="0x84F9623A",对话头像="0x450785A4",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶超级金猴" then
		jps = {小图标="0x237D8B3E",大图标="0x84F9623A",对话头像="0x450785A4",装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "超级大象" then
		jps = {小图标="0xC284AEC7",大图标="0x32E7F885",对话头像="0x479D3DAE",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶超级大象" then
		jps = {小图标="0xC284AEC7",大图标="0x32E7F885",对话头像="0x479D3DAE",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "超级白泽" then
		jps = {小图标="0x178DEE01",大图标="0x6875C546",对话头像="0x5DD2DF02",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶超级白泽" then
		jps = {小图标="0x178DEE01",大图标="0x6875C546",对话头像="0x5DD2DF02",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "超级灵鹿" then
		jps = {小图标="0xD4BBF19C",大图标="0x43FA1FC",对话头像="0x36D8B919",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "进阶超级灵鹿" then
		jps = {小图标="0xD4BBF19C",大图标="0x43FA1FC",对话头像="0x36D8B919",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1700,防御资质=1500,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "超级赤焰兽" then
		jps = {小图标="0xF8D24976",大图标="0x66DFAFB",对话头像="0x97D49726",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "进阶超级赤焰兽" then
		jps = {小图标="0xF8D24976",大图标="0x66DFAFB",对话头像="0x97D49726",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "超级大鹏" then
		jps = {小图标="0xE01F8A25",大图标="0xD7282D50",对话头像="0xE54449AD",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶超级大鹏" then
		jps = {小图标="0xE01F8A25",大图标="0xD7282D50",对话头像="0xE54449AD",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "善财童子" then
		jps = {小图标=0,大图标="0x12685356",对话头像=0,造型变异染色点表=nil,装饰品编号表={874,875,876,877},参战等级=300,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "超级筋斗云" then
		jps = {小图标="0xA69F6B2F",大图标="0xA69F6B2F",对话头像="0x448EC476",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1400,防御资质=1500,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶超级筋斗云" then
		jps = {小图标="0xA69F6B2F",大图标="0xA69F6B2F",对话头像="0x448EC476",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1400,防御资质=1500,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "超级神牛" then
		jps = {小图标="0xA520757C",大图标="0x881F84",对话头像="0x53EAB6E1",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神牛" then
		jps = {小图标="0xA520757C",大图标="0x881F84",对话头像="0x53EAB6E1",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "超级神虎" then
		jps = {小图标="0x3017595B",大图标="0xDC24F5D8",对话头像="0x9CED4F42",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神虎" then
		jps = {小图标="0x3017595B",大图标="0xDC24F5D8",对话头像="0x9CED4F42",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "超级海豚" then
		jps = {小图标="0xAABF9D5E",大图标="0xC960684E",对话头像="0xEF2AEBB7",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=3500,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶超级海豚" then
		jps = {小图标="0xAABF9D5E",大图标="0xC960684E",对话头像="0xEF2AEBB7",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=3500,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "超级神兔" then
		jps = {小图标="0xA3ADD383",大图标="0xFB291A",对话头像="0xF3881D27",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2500,速度资质=1600,躲闪资质=1600,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神兔" then
		jps = {小图标="0xA3ADD383",大图标="0xFB291A",对话头像="0xF3881D27",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2500,速度资质=1600,躲闪资质=1600,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "小毛头" then
		jps = {小图标="0xF1CF62EE",大图标="0xC8D41161",对话头像="0x4ADB4261",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小毛头" then
		jps = {小图标="0x703162DD",大图标="0x9E67990",对话头像="0xDB560413",造型变异染色点表=nil,装饰品编号表={890,891},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小丫丫" then
		jps = {小图标="0x9E313CA0",大图标="0xFBE6FF99",对话头像="0xA974BA37",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小丫丫" then
		jps = {小图标="0x986B4305",大图标="0x1128B477",对话头像="0xE5F9DD2E",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小仙灵" then
		jps = {小图标="0x160BAC92",大图标="0xBBFFA377",对话头像="0xCC5FD880",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小仙灵" then
		jps = {小图标="0x4EA6BA89",大图标="0x1E213199",对话头像="0xFB036AE2",造型变异染色点表=nil,装饰品编号表={896,897},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小仙女" then
		jps = {小图标="0xF02B3FE7",大图标="0x778895A7",对话头像="0x8641F7A6",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小仙女" then
		jps = {小图标="0x57E3BF6D",大图标="0x996A3EAD",对话头像="0xCC1C8AFB",造型变异染色点表=nil,装饰品编号表={900,901},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小精灵" then
		jps = {小图标="0x3560646D",大图标="0xD294A300",对话头像="0x8C9213B6",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小精灵" then
		jps = {小图标="0x30F11894",大图标="0x680B983",对话头像="0xD4179DFD",造型变异染色点表=nil,装饰品编号表={904,905},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小魔头" then
		jps = {小图标="0xC424DB10",大图标="0xE168C387",对话头像="0xBDAA9353",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶小魔头" then
		jps = {小图标="0xE0B6D373",大图标="0x1906B5CA",对话头像="0xA40A6C4",造型变异染色点表=nil,装饰品编号表={0,908,909},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "孙悟空" then
		jps = {小图标=0,大图标="0xCD603016",对话头像="0x3FA3472C",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1800,防御资质=1600,体力资质=7000,法力资质=3000,速度资质=1400,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "沙和尚" then
		jps = {小图标=0,大图标="0x69042416",对话头像="0x40488B18",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "蚩尤" then
		jps = {小图标=0,大图标=0,对话头像="0x5822CC97",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1800,防御资质=1700,体力资质="0x3A98",法力资质=4000,速度资质=1500,躲闪资质=1500,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "猪八戒" then
		jps = {小图标=0,大图标="0xBAFDAD60",对话头像="0x93557CA2",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "狐狸精" then
		jps = {小图标="0xAFAD55A4",大图标="0x9AB2C1B6",对话头像="0xE2BDA627",造型变异染色点表=nil,装饰品编号表={0},参战等级=15,攻击资质=1100,防御资质=1300,体力资质=3000,法力资质=1500,速度资质=1300,躲闪资质=1200,寿命=10000,成长=80,携带技能表=nil}
		return jps
	elseif jp == "知了王" then
		jps = {小图标=0,大图标=0,对话头像="0xE300F089",造型变异染色点表=nil,装饰品编号表={0},参战等级=300,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "泡泡" then
		jps = {小图标="0xF1706FCB",大图标="0x35617B0",对话头像="0xFF17FA58",造型变异染色点表=nil,装饰品编号表={0,917},参战等级=0,攻击资质=1300,防御资质=1300,体力资质=4000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "赛太岁" then
		jps = {小图标=0,大图标=0,对话头像="0x49A7562B",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "周杰伦" then
		jps = {小图标=0,大图标=0,对话头像="0x962BD68A",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "超级神龙" then
		jps = {小图标="0x34766E90",大图标="0xAF34F9DE",对话头像="0xF9F462EF",造型变异染色点表=nil,装饰品编号表={0},参战等级=300,攻击资质=1500,防御资质=1600,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神龙" then
		jps = {小图标="0x34766E90",大图标="0xAF34F9DE",对话头像="0xF9F462EF",造型变异染色点表=nil,装饰品编号表={0},参战等级=300,攻击资质=1500,防御资质=1600,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "章鱼" then
		jps = {小图标="0x8CB06DED",大图标="0xC701081D",对话头像="0x4111B83B",造型变异染色点表=nil,装饰品编号表={0,923},参战等级=15,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "小浣熊" then
		jps = {小图标="0x20ED8F8F",大图标="0xBD62D8A1",对话头像="0xFDDAB4FC",造型变异染色点表=nil,装饰品编号表={0,925},参战等级=15,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "海星" then
		jps = {小图标="0xF903F3A5",大图标="0xF6FC4C5E",对话头像="0xDDB1800D",造型变异染色点表=nil,装饰品编号表={0},参战等级=15,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "火星人" then
		jps = {小图标=0,大图标=0,对话头像=0,造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "转轮王" then
		jps = {小图标=0,大图标=0,对话头像="0x266D7180",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "九头精怪" then
		jps = {小图标=0,大图标=0,对话头像="0xFB43BB92",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "腾蛇" then
		jps = {小图标="0x1554198D",大图标="0xB118ECE8",对话头像="0x66F2BEB4",造型变异染色点表=nil,装饰品编号表={0},参战等级=115,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "东海龙王" then
		jps = {小图标=0,大图标=0,对话头像="0xECF36B2F",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "孙婆婆" then
		jps = {小图标=0,大图标=0,对话头像="0xEEB6DF92",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "牛魔王" then
		jps = {小图标="0x44829CE3",大图标=0,对话头像="0x83547F15",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=9000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "镇元大仙" then
		jps = {小图标=0,大图标=0,对话头像="0x648449CE",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "菩提祖师" then
		jps = {小图标=0,大图标=0,对话头像="0x8D656188",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "白晶晶" then
		jps = {小图标=0,大图标=0,对话头像="0xDA313059",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "幽萤娃娃" then
		jps = {小图标="0x85A4CDA2",大图标="0x74988FAA",对话头像="0x9CAD68C1",造型变异染色点表=nil,装饰品编号表={0,938},参战等级=105,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶幽萤娃娃" then
		jps = {小图标="0x9C1DCFE6",大图标="0xFF1A6ACE",对话头像="0x9AE1E54D",造型变异染色点表=nil,装饰品编号表={0,940},参战等级=105,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "李靖" then
		jps = {小图标=0,大图标=0,对话头像="0xDAE855F8",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "程咬金" then
		jps = {小图标=0,大图标=0,对话头像="0x58A56B36",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "地藏王" then
		jps = {小图标=0,大图标="0xDABD9C04",对话头像="0xE9F0579D",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1400,防御资质=1400,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "观音姐姐" then
		jps = {小图标=0,大图标=0,对话头像="0x8C4AEEA9",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1500,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "空度禅师" then
		jps = {小图标=0,大图标=0,对话头像="0xA248A88A",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1000,防御资质=1400,体力资质=7000,法力资质=3000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "大大王" then
		jps = {小图标=0,大图标=0,对话头像="0x1CDF6FF5",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "啸天犬" then
		jps = {小图标=0,大图标=0,对话头像="0x6F7E5529",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "地涌夫人" then
		jps = {小图标=0,大图标=0,对话头像="0x4B343272",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "二郎神" then
		jps = {小图标=0,大图标=0,对话头像="0x57CCD052",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "烟花占卜师" then
		jps = {小图标="0xD5941BB1",大图标=0,对话头像="0x2F728796",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "巫奎虎" then
		jps = {小图标=0,大图标=0,对话头像="0x7ABE5099",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "啸天犬2" then
		jps = {小图标=0,大图标=0,对话头像="0x6F7E5529",造型变异染色点表=nil,装饰品编号表={0},参战等级=100,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=110,携带技能表=nil}
		return jps
	elseif jp == "超级孔雀" then
		jps = {小图标="0x4AC8F5DE",大图标="0x443DEFFC",对话头像="0xF0085B15",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1400,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "进阶超级孔雀" then
		jps = {小图标="0x4AC8F5DE",大图标="0x443DEFFC",对话头像="0xF0085B15",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1400,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=120,携带技能表=nil}
		return jps
	elseif jp == "超级灵狐" then
		jps = {小图标="0x3B5A1A91",大图标="0xA84A8DA4",对话头像="0xF44C4B78",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "进阶超级灵狐" then
		jps = {小图标="0x3B5A1A91",大图标="0xA84A8DA4",对话头像="0xF44C4B78",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "超级神蛇" then
		jps = {小图标="0x5C1C53DF",大图标="0x13F12535",对话头像="0xBB449983",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1800,防御资质=1500,体力资质=7000,法力资质=3000,速度资质=1000,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神蛇" then
		jps = {小图标="0x5C1C53DF",大图标="0x13F12535",对话头像="0xBB449983",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1800,防御资质=1500,体力资质=7000,法力资质=3000,速度资质=1000,躲闪资质=1200,寿命=10000,成长=140,携带技能表=nil}
		return jps
	elseif jp == "超级神马" then
		jps = {小图标="0x212B8B39",大图标="0xFB118648",对话头像="0x3AB6C3F3",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神马" then
		jps = {小图标="0x212B8B39",大图标="0xFB118648",对话头像="0x3AB6C3F3",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "超级青鸾" then
		jps = {小图标="0xD085CF36",大图标="0x9C4A188",对话头像="0xC0A7E9B1",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶超级青鸾" then
		jps = {小图标="0xD085CF36",大图标="0x9C4A188",对话头像="0xC0A7E9B1",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "超级麒麟" then
		jps = {小图标="0xCD8DC32B",大图标="0xF0CF707B",对话头像="0x2AFEF029",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "进阶超级麒麟" then
		jps = {小图标="0xCD8DC32B",大图标="0xF0CF707B",对话头像="0x2AFEF029",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=130,携带技能表=nil}
		return jps
	elseif jp == "九色鹿" then
		jps = {小图标="0xDD000E7B",大图标="0x7E77B841",对话头像="0x851ED1C6",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "持国巡守" then
		jps = {小图标="0x8F038D77",大图标="0xDC166219",对话头像="0xA684CDBD",造型变异染色点表=nil,装饰品编号表={967,968},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "进阶持国巡守" then
		jps = {小图标="0xD5DF37EC",大图标="0x3C633096",对话头像="0x8E0E0A44",造型变异染色点表=nil,装饰品编号表={970},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "超级神羊" then
		jps = {小图标="0x47FA6C7C",大图标="0x29C2068F",对话头像="0xDEE48C77",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "进阶超级神羊" then
		jps = {小图标="0x47FA6C7C",大图标="0x29C2068F",对话头像="0xDEE48C77",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "六耳猕猴" then
		jps = {小图标="0x47FA6C7C",大图标="0x29C2068F",对话头像="0xDEE48C77",造型变异染色点表=nil,装饰品编号表={0},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=100,携带技能表=nil}
		return jps
	elseif jp == "毗舍童子" then
		jps = {小图标="0x47FA6C7C",大图标="0x29C2068F",对话头像="0xDEE48C77",造型变异染色点表=nil,装饰品编号表={975},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "进阶毗舍童子" then
		jps = {小图标="0x898D54DA",大图标="0xCE1979FF",对话头像="0x10B40E5B",造型变异染色点表=nil,装饰品编号表={977,978},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "真陀护法" then
		jps = {小图标="0xFD534DBF",大图标="0xD76F4018",对话头像="0xFFBCDA90",造型变异染色点表=nil,装饰品编号表={980,981},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "进阶真陀护法" then
		jps = {小图标="0x3BF2EE43",大图标="0xD9E7E041",对话头像="0x448482C6",造型变异染色点表=nil,装饰品编号表={983,984},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps
	elseif jp == "恶魔泡泡" then
		jps = {小图标="恶魔泡泡小",大图标="恶魔泡泡中",对话头像="恶魔泡泡大",造型变异染色点表=nil,装饰品编号表={983,984},参战等级=0,攻击资质=1500,防御资质=1300,体力资质=7000,法力资质=2000,速度资质=1200,躲闪资质=1200,寿命=10000,成长=135,携带技能表=nil}
		return jps



	end
	return nil
end