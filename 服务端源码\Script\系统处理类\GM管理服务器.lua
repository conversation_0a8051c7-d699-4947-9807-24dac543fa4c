-- GM管理服务器 - 现代化Web管理接口
-- 端口：8888
-- 协议：JSON + AES加密
-- 作者：AI Assistant

local json = require("cjson")
local socket = require("socket")

-- GM管理服务器类
local GM管理服务器 = {}
GM管理服务器.__index = GM管理服务器

-- AES密钥（32字节）
local AES_KEY = "GMTOOL2025SECRETKEY1234567890ABCD"

-- 创建GM管理服务器
function GM管理服务器:new()
    local obj = {
        server = nil,
        clients = {},
        running = false,
        port = 8888
    }
    setmetatable(obj, self)
    return obj
end

-- 启动服务器
function GM管理服务器:start()
    self.server = socket.bind("*", self.port)
    if not self.server then
        print("[GM服务器] 启动失败，端口", self.port, "被占用")
        return false
    end
    
    self.server:settimeout(0) -- 非阻塞模式
    self.running = true
    
    print("[GM服务器] 启动成功，监听端口:", self.port)
    print("[GM服务器] Web管理地址: http://localhost:" .. self.port)
    
    return true
end

-- 停止服务器
function GM管理服务器:stop()
    self.running = false
    if self.server then
        self.server:close()
        self.server = nil
    end
    
    -- 关闭所有客户端连接
    for _, client in pairs(self.clients) do
        if client.socket then
            client.socket:close()
        end
    end
    self.clients = {}
    
    print("[GM服务器] 已停止")
end

-- 处理客户端连接
function GM管理服务器:accept_clients()
    if not self.server then return end
    
    local client_socket = self.server:accept()
    if client_socket then
        client_socket:settimeout(0)
        
        local client_id = tostring(client_socket):match("0x(%w+)")
        self.clients[client_id] = {
            socket = client_socket,
            connected_time = os.time(),
            last_activity = os.time()
        }
        
        print("[GM服务器] 新客户端连接:", client_id)
    end
end

-- 处理客户端数据
function GM管理服务器:handle_clients()
    for client_id, client in pairs(self.clients) do
        if client.socket then
            local data, err = client.socket:receive("*l")
            
            if data then
                client.last_activity = os.time()
                self:process_request(client_id, data)
            elseif err == "closed" then
                print("[GM服务器] 客户端断开:", client_id)
                client.socket:close()
                self.clients[client_id] = nil
            end
        end
    end
end

-- 处理请求
function GM管理服务器:process_request(client_id, data)
    local success, request = pcall(json.decode, data)
    if not success then
        self:send_error(client_id, "无效的JSON格式")
        return
    end
    
    print("[GM服务器] 收到请求:", request.action or "unknown")
    
    -- 验证请求格式
    if not request.action then
        self:send_error(client_id, "缺少action字段")
        return
    end
    
    -- 处理不同的GM操作
    local response = self:handle_gm_action(request)
    self:send_response(client_id, response)
end

-- 处理GM操作
function GM管理服务器:handle_gm_action(request)
    local action = request.action
    local params = request.params or {}
    
    if action == "login" then
        return self:handle_login(params)
    elseif action == "recharge" then
        return self:handle_recharge(params)
    elseif action == "ban_account" then
        return self:handle_ban_account(params)
    elseif action == "send_item" then
        return self:handle_send_item(params)
    elseif action == "start_activity" then
        return self:handle_start_activity(params)
    elseif action == "get_online_players" then
        return self:handle_get_online_players(params)
    else
        return {
            success = false,
            error = "未知的操作: " .. action
        }
    end
end

-- 处理登录
function GM管理服务器:handle_login(params)
    local username = params.username
    local password = params.password
    
    -- 简单的GM账号验证
    if username == "888888" and password == "888888" then
        return {
            success = true,
            message = "登录成功",
            data = {
                username = username,
                permissions = {"all"},
                server_info = {
                    name = "梦幻西游服务器",
                    online_players = #__C客户信息 or 0,
                    version = "2.0"
                }
            }
        }
    else
        return {
            success = false,
            error = "用户名或密码错误"
        }
    end
end

-- 处理充值
function GM管理服务器:handle_recharge(params)
    local account = params.account
    local type = params.type
    local amount = params.amount
    
    if not account or not type or not amount then
        return {
            success = false,
            error = "缺少必要参数"
        }
    end
    
    -- 调用原有的充值系统
    if 管理工具类 and 管理工具类.充值处理 then
        local result = 管理工具类:充值处理(account, type, amount)
        return {
            success = true,
            message = "充值成功",
            data = {
                account = account,
                type = type,
                amount = amount
            }
        }
    else
        return {
            success = false,
            error = "充值系统不可用"
        }
    end
end

-- 处理账号封禁
function GM管理服务器:handle_ban_account(params)
    local account = params.account
    local duration = params.duration or 24 -- 默认24小时
    local reason = params.reason or "违规操作"
    
    if not account then
        return {
            success = false,
            error = "缺少账号参数"
        }
    end
    
    -- 调用原有的封禁系统
    if 管理工具类 and 管理工具类.封禁处理 then
        local result = 管理工具类:封禁处理(account, duration, reason)
        return {
            success = true,
            message = "账号封禁成功",
            data = {
                account = account,
                duration = duration,
                reason = reason
            }
        }
    else
        return {
            success = false,
            error = "封禁系统不可用"
        }
    end
end

-- 处理物品发送
function GM管理服务器:handle_send_item(params)
    local account = params.account
    local item_id = params.item_id
    local quantity = params.quantity or 1
    
    if not account or not item_id then
        return {
            success = false,
            error = "缺少必要参数"
        }
    end
    
    -- 调用原有的物品系统
    if 管理工具类 and 管理工具类.物品发送 then
        local result = 管理工具类:物品发送(account, item_id, quantity)
        return {
            success = true,
            message = "物品发送成功",
            data = {
                account = account,
                item_id = item_id,
                quantity = quantity
            }
        }
    else
        return {
            success = false,
            error = "物品系统不可用"
        }
    end
end

-- 处理活动启动
function GM管理服务器:handle_start_activity(params)
    local activity_type = params.activity_type
    
    if not activity_type then
        return {
            success = false,
            error = "缺少活动类型参数"
        }
    end
    
    -- 调用原有的活动系统
    if 管理工具类 and 管理工具类.活动启动 then
        local result = 管理工具类:活动启动(activity_type)
        return {
            success = true,
            message = "活动启动成功",
            data = {
                activity_type = activity_type
            }
        }
    else
        return {
            success = false,
            error = "活动系统不可用"
        }
    end
end

-- 获取在线玩家
function GM管理服务器:handle_get_online_players(params)
    local players = {}
    
    if __C客户信息 then
        for id, info in pairs(__C客户信息) do
            if info and not info.网关 then
                table.insert(players, {
                    id = id,
                    ip = info.IP,
                    connect_time = info.认证
                })
            end
        end
    end
    
    return {
        success = true,
        message = "获取在线玩家成功",
        data = {
            count = #players,
            players = players
        }
    }
end

-- 发送响应
function GM管理服务器:send_response(client_id, response)
    local client = self.clients[client_id]
    if not client or not client.socket then
        return false
    end
    
    local json_data = json.encode(response)
    local success, err = client.socket:send(json_data .. "\n")
    
    if not success then
        print("[GM服务器] 发送响应失败:", err)
        return false
    end
    
    return true
end

-- 发送错误响应
function GM管理服务器:send_error(client_id, error_message)
    local response = {
        success = false,
        error = error_message
    }
    self:send_response(client_id, response)
end

-- 主循环
function GM管理服务器:update()
    if not self.running then return end
    
    self:accept_clients()
    self:handle_clients()
    
    -- 清理超时连接
    local current_time = os.time()
    for client_id, client in pairs(self.clients) do
        if current_time - client.last_activity > 300 then -- 5分钟超时
            print("[GM服务器] 客户端超时断开:", client_id)
            if client.socket then
                client.socket:close()
            end
            self.clients[client_id] = nil
        end
    end
end

return GM管理服务器
