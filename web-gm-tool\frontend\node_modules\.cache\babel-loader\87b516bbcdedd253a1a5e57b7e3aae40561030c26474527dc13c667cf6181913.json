{"ast": null, "code": "import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    measureColumnWidth = props.measureColumnWidth;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns', 'getRowKey', 'expandedKeys', 'childrenColumnName', 'emptyNode', 'expandedRowOffset', 'fixedInfoList', 'colWidths']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    childrenColumnName = _useContext.childrenColumnName,\n    emptyNode = _useContext.emptyNode,\n    _useContext$expandedR = _useContext.expandedRowOffset,\n    expandedRowOffset = _useContext$expandedR === void 0 ? 0 : _useContext$expandedR,\n    colWidths = _useContext.colWidths;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n  var rowKeys = React.useMemo(function () {\n    return flattenData.map(function (item) {\n      return item.rowKey;\n    });\n  }, [flattenData]);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ===================== Expanded =====================\n  // `expandedRowOffset` data is same for all the rows.\n  // Let's calc on Body side to save performance.\n  var expandedRowInfo = React.useMemo(function () {\n    var expandedColSpan = flattenColumns.length - expandedRowOffset;\n    var expandedStickyStart = 0;\n    for (var i = 0; i < expandedRowOffset; i += 1) {\n      expandedStickyStart += colWidths[i] || 0;\n    }\n    return {\n      offset: expandedRowOffset,\n      colSpan: expandedColSpan,\n      sticky: expandedStickyStart\n    };\n  }, [flattenColumns.length, expandedRowOffset, colWidths]);\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index,\n        rowKey = item.rowKey;\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: rowKey,\n        rowKey: rowKey,\n        rowKeys: rowKeys,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        indent: indent\n        // Expanded row info\n        ,\n\n        expandedRowInfo: expandedRowInfo\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nif (process.env.NODE_ENV !== 'production') {\n  Body.displayName = 'Body';\n}\nexport default responseImmutable(Body);", "map": {"version": 3, "names": ["useContext", "React", "PerfContext", "TableContext", "responseImmutable", "useFlattenRecords", "devRenderTimes", "getColumnsKey", "BodyRow", "ExpandedRow", "MeasureRow", "Body", "props", "process", "env", "NODE_ENV", "data", "measureColumnWidth", "_useContext", "prefixCls", "getComponent", "onColumnResize", "flattenColumns", "getRowKey", "expandedKeys", "childrenColumnName", "emptyNode", "_useContext$expandedR", "expandedRowOffset", "col<PERSON><PERSON><PERSON>", "flattenData", "row<PERSON>eys", "useMemo", "map", "item", "<PERSON><PERSON><PERSON>", "perfRef", "useRef", "renderWithProps", "expandedRowInfo", "expandedColSpan", "length", "expandedStickyStart", "i", "offset", "colSpan", "sticky", "WrapperComponent", "trComponent", "tdComponent", "thComponent", "rows", "idx", "record", "indent", "renderIndex", "index", "createElement", "key", "rowComponent", "cellComponent", "scopeCellComponent", "expanded", "className", "concat", "component", "isEmpty", "columnsKey", "Provider", "value", "current", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-table/es/Body/index.js"], "sourcesContent": ["import { useContext } from '@rc-component/context';\nimport * as React from 'react';\nimport PerfContext from \"../context/PerfContext\";\nimport TableContext, { responseImmutable } from \"../context/TableContext\";\nimport useFlattenRecords from \"../hooks/useFlattenRecords\";\nimport devRenderTimes from \"../hooks/useRenderTimes\";\nimport { getColumnsKey } from \"../utils/valueUtil\";\nimport BodyRow from \"./BodyRow\";\nimport ExpandedRow from \"./ExpandedRow\";\nimport MeasureRow from \"./MeasureRow\";\nfunction Body(props) {\n  if (process.env.NODE_ENV !== 'production') {\n    devRenderTimes(props);\n  }\n  var data = props.data,\n    measureColumnWidth = props.measureColumnWidth;\n  var _useContext = useContext(TableContext, ['prefixCls', 'getComponent', 'onColumnResize', 'flattenColumns', 'getRowKey', 'expandedKeys', 'childrenColumnName', 'emptyNode', 'expandedRowOffset', 'fixedInfoList', 'colWidths']),\n    prefixCls = _useContext.prefixCls,\n    getComponent = _useContext.getComponent,\n    onColumnResize = _useContext.onColumnResize,\n    flattenColumns = _useContext.flattenColumns,\n    getRowKey = _useContext.getRowKey,\n    expandedKeys = _useContext.expandedKeys,\n    childrenColumnName = _useContext.childrenColumnName,\n    emptyNode = _useContext.emptyNode,\n    _useContext$expandedR = _useContext.expandedRowOffset,\n    expandedRowOffset = _useContext$expandedR === void 0 ? 0 : _useContext$expandedR,\n    colWidths = _useContext.colWidths;\n  var flattenData = useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey);\n  var rowKeys = React.useMemo(function () {\n    return flattenData.map(function (item) {\n      return item.rowKey;\n    });\n  }, [flattenData]);\n\n  // =================== Performance ====================\n  var perfRef = React.useRef({\n    renderWithProps: false\n  });\n\n  // ===================== Expanded =====================\n  // `expandedRowOffset` data is same for all the rows.\n  // Let's calc on Body side to save performance.\n  var expandedRowInfo = React.useMemo(function () {\n    var expandedColSpan = flattenColumns.length - expandedRowOffset;\n    var expandedStickyStart = 0;\n    for (var i = 0; i < expandedRowOffset; i += 1) {\n      expandedStickyStart += colWidths[i] || 0;\n    }\n    return {\n      offset: expandedRowOffset,\n      colSpan: expandedColSpan,\n      sticky: expandedStickyStart\n    };\n  }, [flattenColumns.length, expandedRowOffset, colWidths]);\n\n  // ====================== Render ======================\n  var WrapperComponent = getComponent(['body', 'wrapper'], 'tbody');\n  var trComponent = getComponent(['body', 'row'], 'tr');\n  var tdComponent = getComponent(['body', 'cell'], 'td');\n  var thComponent = getComponent(['body', 'cell'], 'th');\n  var rows;\n  if (data.length) {\n    rows = flattenData.map(function (item, idx) {\n      var record = item.record,\n        indent = item.indent,\n        renderIndex = item.index,\n        rowKey = item.rowKey;\n      return /*#__PURE__*/React.createElement(BodyRow, {\n        key: rowKey,\n        rowKey: rowKey,\n        rowKeys: rowKeys,\n        record: record,\n        index: idx,\n        renderIndex: renderIndex,\n        rowComponent: trComponent,\n        cellComponent: tdComponent,\n        scopeCellComponent: thComponent,\n        indent: indent\n        // Expanded row info\n        ,\n        expandedRowInfo: expandedRowInfo\n      });\n    });\n  } else {\n    rows = /*#__PURE__*/React.createElement(ExpandedRow, {\n      expanded: true,\n      className: \"\".concat(prefixCls, \"-placeholder\"),\n      prefixCls: prefixCls,\n      component: trComponent,\n      cellComponent: tdComponent,\n      colSpan: flattenColumns.length,\n      isEmpty: true\n    }, emptyNode);\n  }\n  var columnsKey = getColumnsKey(flattenColumns);\n  return /*#__PURE__*/React.createElement(PerfContext.Provider, {\n    value: perfRef.current\n  }, /*#__PURE__*/React.createElement(WrapperComponent, {\n    className: \"\".concat(prefixCls, \"-tbody\")\n  }, measureColumnWidth && /*#__PURE__*/React.createElement(MeasureRow, {\n    prefixCls: prefixCls,\n    columnsKey: columnsKey,\n    onColumnResize: onColumnResize\n  }), rows));\n}\nif (process.env.NODE_ENV !== 'production') {\n  Body.displayName = 'Body';\n}\nexport default responseImmutable(Body);"], "mappings": "AAAA,SAASA,UAAU,QAAQ,uBAAuB;AAClD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,OAAOC,YAAY,IAAIC,iBAAiB,QAAQ,yBAAyB;AACzE,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,aAAa,QAAQ,oBAAoB;AAClD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,UAAU,MAAM,cAAc;AACrC,SAASC,IAAIA,CAACC,KAAK,EAAE;EACnB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCT,cAAc,CAACM,KAAK,CAAC;EACvB;EACA,IAAII,IAAI,GAAGJ,KAAK,CAACI,IAAI;IACnBC,kBAAkB,GAAGL,KAAK,CAACK,kBAAkB;EAC/C,IAAIC,WAAW,GAAGlB,UAAU,CAACG,YAAY,EAAE,CAAC,WAAW,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,oBAAoB,EAAE,WAAW,EAAE,mBAAmB,EAAE,eAAe,EAAE,WAAW,CAAC,CAAC;IAC9NgB,SAAS,GAAGD,WAAW,CAACC,SAAS;IACjCC,YAAY,GAAGF,WAAW,CAACE,YAAY;IACvCC,cAAc,GAAGH,WAAW,CAACG,cAAc;IAC3CC,cAAc,GAAGJ,WAAW,CAACI,cAAc;IAC3CC,SAAS,GAAGL,WAAW,CAACK,SAAS;IACjCC,YAAY,GAAGN,WAAW,CAACM,YAAY;IACvCC,kBAAkB,GAAGP,WAAW,CAACO,kBAAkB;IACnDC,SAAS,GAAGR,WAAW,CAACQ,SAAS;IACjCC,qBAAqB,GAAGT,WAAW,CAACU,iBAAiB;IACrDA,iBAAiB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,qBAAqB;IAChFE,SAAS,GAAGX,WAAW,CAACW,SAAS;EACnC,IAAIC,WAAW,GAAGzB,iBAAiB,CAACW,IAAI,EAAES,kBAAkB,EAAED,YAAY,EAAED,SAAS,CAAC;EACtF,IAAIQ,OAAO,GAAG9B,KAAK,CAAC+B,OAAO,CAAC,YAAY;IACtC,OAAOF,WAAW,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAE;MACrC,OAAOA,IAAI,CAACC,MAAM;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACL,WAAW,CAAC,CAAC;;EAEjB;EACA,IAAIM,OAAO,GAAGnC,KAAK,CAACoC,MAAM,CAAC;IACzBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA;EACA;EACA,IAAIC,eAAe,GAAGtC,KAAK,CAAC+B,OAAO,CAAC,YAAY;IAC9C,IAAIQ,eAAe,GAAGlB,cAAc,CAACmB,MAAM,GAAGb,iBAAiB;IAC/D,IAAIc,mBAAmB,GAAG,CAAC;IAC3B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,iBAAiB,EAAEe,CAAC,IAAI,CAAC,EAAE;MAC7CD,mBAAmB,IAAIb,SAAS,CAACc,CAAC,CAAC,IAAI,CAAC;IAC1C;IACA,OAAO;MACLC,MAAM,EAAEhB,iBAAiB;MACzBiB,OAAO,EAAEL,eAAe;MACxBM,MAAM,EAAEJ;IACV,CAAC;EACH,CAAC,EAAE,CAACpB,cAAc,CAACmB,MAAM,EAAEb,iBAAiB,EAAEC,SAAS,CAAC,CAAC;;EAEzD;EACA,IAAIkB,gBAAgB,GAAG3B,YAAY,CAAC,CAAC,MAAM,EAAE,SAAS,CAAC,EAAE,OAAO,CAAC;EACjE,IAAI4B,WAAW,GAAG5B,YAAY,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC;EACrD,IAAI6B,WAAW,GAAG7B,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAI8B,WAAW,GAAG9B,YAAY,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC;EACtD,IAAI+B,IAAI;EACR,IAAInC,IAAI,CAACyB,MAAM,EAAE;IACfU,IAAI,GAAGrB,WAAW,CAACG,GAAG,CAAC,UAAUC,IAAI,EAAEkB,GAAG,EAAE;MAC1C,IAAIC,MAAM,GAAGnB,IAAI,CAACmB,MAAM;QACtBC,MAAM,GAAGpB,IAAI,CAACoB,MAAM;QACpBC,WAAW,GAAGrB,IAAI,CAACsB,KAAK;QACxBrB,MAAM,GAAGD,IAAI,CAACC,MAAM;MACtB,OAAO,aAAalC,KAAK,CAACwD,aAAa,CAACjD,OAAO,EAAE;QAC/CkD,GAAG,EAAEvB,MAAM;QACXA,MAAM,EAAEA,MAAM;QACdJ,OAAO,EAAEA,OAAO;QAChBsB,MAAM,EAAEA,MAAM;QACdG,KAAK,EAAEJ,GAAG;QACVG,WAAW,EAAEA,WAAW;QACxBI,YAAY,EAAEX,WAAW;QACzBY,aAAa,EAAEX,WAAW;QAC1BY,kBAAkB,EAAEX,WAAW;QAC/BI,MAAM,EAAEA;QACR;QAAA;;QAEAf,eAAe,EAAEA;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,MAAM;IACLY,IAAI,GAAG,aAAalD,KAAK,CAACwD,aAAa,CAAChD,WAAW,EAAE;MACnDqD,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC7C,SAAS,EAAE,cAAc,CAAC;MAC/CA,SAAS,EAAEA,SAAS;MACpB8C,SAAS,EAAEjB,WAAW;MACtBY,aAAa,EAAEX,WAAW;MAC1BJ,OAAO,EAAEvB,cAAc,CAACmB,MAAM;MAC9ByB,OAAO,EAAE;IACX,CAAC,EAAExC,SAAS,CAAC;EACf;EACA,IAAIyC,UAAU,GAAG5D,aAAa,CAACe,cAAc,CAAC;EAC9C,OAAO,aAAarB,KAAK,CAACwD,aAAa,CAACvD,WAAW,CAACkE,QAAQ,EAAE;IAC5DC,KAAK,EAAEjC,OAAO,CAACkC;EACjB,CAAC,EAAE,aAAarE,KAAK,CAACwD,aAAa,CAACV,gBAAgB,EAAE;IACpDgB,SAAS,EAAE,EAAE,CAACC,MAAM,CAAC7C,SAAS,EAAE,QAAQ;EAC1C,CAAC,EAAEF,kBAAkB,IAAI,aAAahB,KAAK,CAACwD,aAAa,CAAC/C,UAAU,EAAE;IACpES,SAAS,EAAEA,SAAS;IACpBgD,UAAU,EAAEA,UAAU;IACtB9C,cAAc,EAAEA;EAClB,CAAC,CAAC,EAAE8B,IAAI,CAAC,CAAC;AACZ;AACA,IAAItC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,IAAI,CAAC4D,WAAW,GAAG,MAAM;AAC3B;AACA,eAAenE,iBAAiB,CAACO,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}