
local 聊天处理类 = class()
local ceil = math.ceil
function 聊天处理类:初始化()end


function 聊天处理类:数据处理(id,序号,数据)
  if 序号==6001 then
    self:频道发言处理(玩家数据[id].连接id,id,数据)
  end
end



function 聊天处理类:监控聊天处理(id,名称,频道,文本)
  if 频道 == 1 then
    频道 = "当前发言"
  elseif 频道==2 then
    频道 = "队伍发言"
  elseif 频道==3 then
   频道 = "世界发言"
  elseif 频道==4 then
    频道 = "门派发言"
  elseif 频道==5 then
    频道 = "传闻发言"
  elseif 频道==6 then
    频道 = "帮派发言"
  elseif 频道==7 then
    频道 = "交易发言"
  elseif 频道==8 then
    频道 = "夫妻发言"
  elseif 频道==9 then
    频道 = "GM发言"
  elseif 频道==10 then
    频道 = "私聊发言"
  elseif 频道==11 then
    频道 = "团队发言"
  elseif 频道==12 then
    频道 = "传音发言"
  end
  local 组合语句=时间转换2(时间).." 姓名:"..名称.."("..id..")  "..频道..":"..文本
  for n, v in pairs(聊天监控) do
    --发送数据1(n,8,组合语句)
    管理工具类:发送数据(n,9,组合语句)
  end
end

function 聊天处理类:频道发言处理(连接id,id,数据)
  local 临时超链接数据 = {}
  if 数据 ~= nil then
    临时超链接数据 = 数据[1]
  end
  local 频道=数据.频道
  local 文本=数据.文本
  self:监控聊天处理(id,玩家数据[id].角色.数据.名称,频道,文本)
  文本=敏感字判断(文本)
  if string.find(文本,"安全码",1)~=nil then
    local 临时数组=分割文本(文本,"安全码")
    local 密码=临时数组[2]
    if 密码==nil or string.len(密码)~=6 or tonumber(密码)==nil then
      常规提示(id,"#R安全码必须由6位数字组成，请重新设置")
      return
    end
    local 原码=f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","安全码")
    if 原码=="0" then
      f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","安全码",密码)
      f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","安全码修改时间",时间转换(os.time()))
      常规提示(id,"#Y设置安全码成功，请牢记您的安全码，这将作为您账号归属者的凭证")
      f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","安全码修改ip",玩家数据[id].ip)
      return
    else
      常规提示(id,"#Y你的安全码已经设置过了，无法进行更改")
      return
    end
  end
  if 全服禁言 and 频道~=1 and 频道~=2 then return end
  --local 管理命令 = 分割文本(数据.文本,"@")
  if 频道==1 then --当前发言
    if 数据.文本=="退出战斗" or 数据.文本=="tczd" then
        if 玩家数据[id].战斗~=0 and 玩家数据[id].观战~=nil then
              local id组 = 取id组(id)
              for i=1,#id组 do
                    if 玩家数据[id组[i]].观战 ~= nil then
                        if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                            战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
                        else
                            玩家数据[id组[i]].战斗=0
                            玩家数据[id组[i]].观战=nil
                            发送数据(玩家数据[id组[i]].连接id,5505)
                        end
                    end
              end
            return
        end
        if 玩家数据[id].退出战斗 and os.time()-玩家数据[id].退出战斗<=120 then
            常规提示(id,"#Y/请勿频繁使用该命令")
            return
        else
            玩家数据[id].退出战斗 = os.time()
        end
        if 玩家数据[id].队伍~=0 and 玩家数据[id].队伍~=id then
          常规提示(id,"#Y/只有队长才可以使用该命令")
          return
        elseif 玩家数据[id].战斗 and not 战斗准备类.战斗盒子[玩家数据[id].战斗] then
                常规提示(id,"#Y/你并没有在战斗中,往哪退出战斗！")
                return
        elseif 战斗准备类.战斗盒子[玩家数据[id].战斗] and 战斗准备类.战斗盒子[玩家数据[id].战斗]:取玩家战斗() then
                常规提示(id,"#Y/无法使用该功能")
                return
        elseif 战斗准备类.战斗盒子[玩家数据[id].战斗] and 战斗准备类.战斗盒子[玩家数据[id].战斗]:取副本战斗() then
                  常规提示(id,"#Y/该副本禁止使用该功能")
                  return
        end
        if 玩家数据[id].战斗~=0 then
            if 玩家数据[id].队伍~=0 and 战斗准备类.战斗盒子[玩家数据[id].战斗] and 战斗准备类.战斗盒子[玩家数据[id].战斗].执行等待<0 and 战斗准备类.战斗盒子[玩家数据[id].战斗]:取玩家战斗()==false then
                战斗准备类.战斗盒子[玩家数据[id].战斗]:结束战斗处理(0,玩家数据[id].队伍,1)
            else
                战斗准备类.战斗盒子[玩家数据[id].战斗]:结束战斗处理(0,0,1)
            end
            if 玩家数据[id].自动抓鬼 and type(玩家数据[id].自动抓鬼)=="table" and 玩家数据[id].自动抓鬼.进程==5 and not 玩家数据[id].自动抓鬼.开启 then
                发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件="自动抓鬼"})
                常规提示(id,"#Y/自动抓鬼已关闭需要请重新开启")
                玩家数据[id].自动抓鬼=nil
            end
            return
        end
    elseif 数据.文本=="@cxjz" or 数据.文本=="@重新加载" then
          if 玩家数据[id].重新加载~=nil and os.time()-玩家数据[id].重新加载<=60 then
              常规提示(id,"#Y/请勿频繁使用该命令")
              return
          else
              玩家数据[id].重新加载 = os.time()
          end
          地图处理类:重连加入(id,玩家数据[id].角色.数据.地图数据.编号,玩家数据[id].角色.数据.地图数据.x,玩家数据[id].角色.数据.地图数据.y)
          常规提示(id,"#Y/地图数据已重新加载,每次加载需间隔60秒")
          return

    end

    玩家数据[id].当前频道=os.time()+2
    if  数据.文本~="tczd"then
      地图处理类:当前消息广播(玩家数据[id].角色.数据.地图数据,玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号,文本,临时超链接数据,id,玩家数据[id].角色.数据.发言特效)
   end

   if 玩家数据[id].战斗==0 and 数据.文本~="tczd" then
      发送数据(连接id,1017,{文本=文本,特效=玩家数据[id].角色.数据.发言特效})
    else
      local 编号=战斗准备类.战斗盒子[玩家数据[id].战斗]:取参战编号(id,"角色")
      if 编号==nil then
        return
      end
      for n=1,#战斗准备类.战斗盒子[玩家数据[id].战斗].参战玩家 do
        发送数据(战斗准备类.战斗盒子[玩家数据[id].战斗].参战玩家[n].连接id,5512,{id=编号,文本=文本,特效=玩家数据[id].角色.数据.发言特效})
      end
    end

  elseif 频道==2 then  --队伍发言
    if 玩家数据[id].队伍==0 then
      常规提示(id,"#Y/你似乎还没有加入任何队伍")
      return
    end


     广播队伍消息(玩家数据[id].队伍,"["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."]"..文本,临时超链接数据)
    if  玩家数据[id].战斗==0 then
      --发送数据(连接id,1017,{文本=文本,特效=玩家数据[id].角色.数据.发言特效})
      发送数据(连接id,1017,{文本=文本,队伍=true,特效=玩家数据[id].角色.数据.发言特效})
      for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
        if 队伍数据[玩家数据[id].队伍].成员数据[n]~=id then
          --发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,1018,{id=id,文本=文本,特效=玩家数据[id].角色.数据.发言特效})

          发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,1018,{id=id,文本=文本,特效=玩家数据[id].角色.数据.发言特效})
        end
      end
    else
      local 编号=战斗准备类.战斗盒子[玩家数据[id].战斗]:取参战编号(id,"角色")
      if 编号==nil then return  end
      for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
         -- 发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,5512,{id=编号,文本=文本,特效=玩家数据[id].角色.数据.发言特效})
          发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].连接id,5512,{id=编号,文本=文本,特效=玩家数据[id].角色.数据.发言特效})
      end
    end

  elseif 频道==3 then  --世界发言
    local 发言限制=100-玩家数据[id].角色.数据.等级
    if 发言限制<20 then
      发言限制=10
    end
    -- if os.time()-玩家数据[id].世界频道<=发言限制 then
    --   常规提示(id,"#Y/您说话的速度有点快哟！")
    --   return
    if 玩家数据[id].角色.数据.等级<30 then
      常规提示(id,"#Y/等级达到30级才可在世界频道发言")
      return
    end
    玩家数据[id].世界频道=os.time()
    广播消息({内容="["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."] "..文本,超链=临时超链接数据,频道="sj"})

    if 三界书院.开关 and 三界书院.答案 == 文本 then
    local 名单重复 = false
    for n = 1, #三界书院.名单 do
    if 三界书院.名单[n].id == id then
        名单重复 = true
      end
    end
    if 名单重复 == false then
      三界书院.名单[#三界书院.名单 + 1] = {
        id = id,
        名称 = 玩家数据[id].角色.数据.名称,
        用时 = os.time() - 三界书院.起始
      }
    end
  end

 elseif 频道==4 then  --门派
    if 玩家数据[id].角色.数据.等级<30 then
      常规提示(id,"#Y/等级达到30级才可在世界频道发言")
      return
    elseif 玩家数据[id].角色.数据.门派 == nil or 玩家数据[id].角色.数据.门派 == "无门派" then
      常规提示(id,"#Y/请加入门派后再在此频道发言。")
      return
    end
    self.门派代号 = 门派代号(玩家数据[id].角色.数据.门派)
    广播门派消息({内容="["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."] "..文本,超链=临时超链接数据,频道=self.门派代号},玩家数据[id].角色.数据.门派) --要修改素材为门派
 elseif 频道==9 then  --GM
    if f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","管理")~="1" then  --账号是否异常
      常规提示(id,"#Y/你没有GM权限！")
      return
    end
    广播消息({内容="["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."] "..文本,超链=临时超链接数据,频道="gm"})
  elseif 频道==5 then  --世界发言
    -- if os.time()-玩家数据[id].传闻频道<=30 then
    --   常规提示(id,"#Y/您说话的速度有点快哟！")
    --   return
    if 玩家数据[id].角色.数据.等级<30 then
      常规提示(id,"#Y/等级达到20级才可在世界频道发言")
      return
    elseif 玩家数据[id].角色.数据.银子<5000 then
      常规提示(id,"#Y/本频道发言需要消耗5000两银子")
      return
    end
    玩家数据[id].传闻频道=os.time()
    玩家数据[id].角色:扣除银子(5000,"传闻频道发言",1)
    if 取随机数()<=10 then
      广播消息({内容="["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."] "..文本,超链=临时超链接数据,频道="cw"})
    else
      广播消息({内容="[某人]"..文本,超链=临时超链接数据,频道="cw"})
    end
  elseif 频道==6 then  --世界发言
    if 玩家数据[id].角色.数据.帮派 == "无帮派" or 玩家数据[id].角色.数据.帮派数据 == nil or 玩家数据[id].角色.数据.帮派数据.编号<=0 then
      常规提示(id,"#Y/请加入帮派后再在此频道发言。")
      return
    end
    广播帮派消息({内容="["..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."] "..文本,超链=临时超链接数据,频道="bp"},玩家数据[id].角色.数据.帮派数据.编号)
    elseif 频道==12 then  --摩托修改增加传音发言
    if 玩家数据[id].角色.数据.传音纸鹤 < 1 then
     常规提示(id,"#Y/好像没有剩余的传音纸鹤可使用")
     else
      玩家数据[id].角色.数据.传音纸鹤=玩家数据[id].角色.数据.传音纸鹤-1
      发送传音(玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号,数据.文本,取随机数(1,4),临时超链接数据)
     end
  end
end


function 聊天处理类:更新(dt)end
function 聊天处理类:显示(x,y)end

return 聊天处理类