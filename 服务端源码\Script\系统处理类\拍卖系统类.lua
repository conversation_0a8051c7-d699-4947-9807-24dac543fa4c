local 拍卖系统类 = class()
function 拍卖系统类:初始化()
end

function 拍卖系统类:数据处理(连接id,序号,id,内容)
	if 序号==6301 then
		self:打开拍卖系统(连接id,序号,id,内容)
	elseif 序号==6302 then

		-- self:拍卖系统竞价处理(连接id,序号,id,内容)
	elseif 序号==6303 then
		self:缴纳保证金(连接id,序号,id,内容)
	elseif 序号==6304 then
		self:拍卖系统竞价处理(连接id,序号,id,内容)
	elseif 序号==6305 then
		self:刷新拍卖数据(id,内容.编号)
	elseif 序号==6306 then
		self:竞价数据处理(连接id,序号,id,内容)
	elseif 序号==6307 then
		self:拍卖结束处理(连接id,序号,id,内容)
	elseif 序号==6388 then
		self:拍卖系统管理(连接id,序号,id,内容)
	end
end


function 拍卖系统类:更新拍卖数据()
  for n, v in pairs(拍卖系统数据.当前拍品) do
    if 拍卖系统数据.当前拍品[n] ~= nil then
      if os.time() > 拍卖系统数据.当前拍品[n].结束时间 then
        for i, v in pairs(拍卖系统数据.竞拍数据) do
          for k, v in pairs(拍卖系统数据.竞拍数据[i]) do
            if 拍卖系统数据.竞拍数据[i][k].编号 == n then
              拍卖系统数据.竞拍数据[i][k].结束 = true
              if 拍卖系统数据.当前拍品[n].当前价 == 拍卖系统数据.竞拍数据[i][k].当前出价 then
                拍卖系统数据.竞拍数据[i][k].状态 = "成交"
              else
                拍卖系统数据.竞拍数据[i][k].状态 = "出局"
              end
            end
          end
        end
      end
    end
  end
end

function 拍卖系统类:打开拍卖系统(连接id,序号,id,内容)
  if 拍卖系统数据.拍卖开关==true then
    发送数据(玩家数据[id].连接id,148,拍卖系统数据.当前拍品)
    发送数据(玩家数据[id].连接id,148.5,{数据=拍卖系统数据.竞价记录})
  else
    发送数据(玩家数据[id].连接id,7,"#Y/当前不是拍卖时间，请留意拍卖系统开启时间")
  end
end

function 拍卖系统类:拍卖系统竞价处理(连接id,序号,id,内容)
  local 编号=内容.编号
  local 名称=内容.名称
  local 出价=tonumber(内容.出价)
  local 加价 = 拍卖系统数据.当前拍品[编号].加价
  local 购买时间=os.time()

  if 拍卖系统数据.拍卖开关~=true then
    发送数据(玩家数据[id].连接id,7,"#Y/拍卖已经关闭，无法参与竞价")
    return
  end
  if 拍卖系统数据.保证金数据[编号][id] == nil then
    发送数据(玩家数据[id].连接id,7,"#Y/你还未缴纳保证金，无法参与竞价")
    return
  end

  if 拍卖系统数据.当前拍品[编号] == nil and 拍卖系统数据.当前拍品[编号].名称 ~= 名称 then
    发送数据(玩家数据[id].连接id,7,"#Y/拍卖数据错误，请重新打开拍卖系统")
  -- elseif 出价 > f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","仙玉")+0 then
  --   发送数据(玩家数据[id].连接id,7,"#Y/你没有这么多仙玉哦")
  --elseif 出价 > 取银子(id) then
   elseif 出价 >玩家数据[id].角色.数据.比武积分.当前积分 then
    发送数据(玩家数据[id].连接id,7,"#Y/你没有这么多比武积分哦")

  elseif 购买时间 < 拍卖系统数据.当前拍品[编号].起拍时间 then
    发送数据(玩家数据[id].连接id,7,"#Y/该拍品未到拍卖时间，无法参与竞价")

  elseif 购买时间 > 拍卖系统数据.当前拍品[编号].结束时间 then
    发送数据(玩家数据[id].连接id,7,"#Y/该拍品已到结束时间，无法参与竞价")

  elseif 出价 <= 拍卖系统数据.当前拍品[编号].当前价 then
    发送数据(玩家数据[id].连接id,7,"#Y/你的出价已经低于拍品的当前价，请检查后重新输入")

  -- elseif 出价 - 拍卖系统数据.当前拍品[编号].当前价 < 加价 then
  --   发送数据(玩家数据[id].连接id,7,"#Y/当前拍品最低加价为"..加价.."点仙玉")
  elseif 出价 - 拍卖系统数据.当前拍品[编号].当前价 < 加价 then
    发送数据(玩家数据[id].连接id,7,"#Y/当前拍品最低加价为"..加价.."点银子")
  else
    if 拍卖系统数据.当前拍品[编号].结束时间-os.time()<300 then
      拍卖系统数据.当前拍品[编号].结束时间=os.time()+300
    end
    拍卖系统数据.当前拍品[编号].当前价=出价
    拍卖系统数据.当前拍品[编号].竞价记录=拍卖系统数据.当前拍品[编号].竞价记录+1
    for n, v in pairs(拍卖系统数据.竞拍数据[id]) do
      if 拍卖系统数据.竞拍数据[id][n].编号 == 编号 then
        拍卖系统数据.竞拍数据[id][n].当前出价 = 出价
        break
      end
    end
   -- print(id,编号,出价)
    self:增加出价记录(id,编号,出价)
    发送数据(玩家数据[id].连接id,7,"#Y/恭喜你以"..出价.."点银子的当前最高出价成为领先竞价者")
  end
  self:刷新拍卖数据(id,编号)
  return
end

function 拍卖系统类:缴纳保证金(连接id,序号,id,内容)
  local 编号 = 内容.编号
  local 保证金 = 拍卖系统数据.当前拍品[编号].保证金

  if 拍卖系统数据.保证金数据[编号][id] == nil then
    -- if 玩家数据[id].角色:扣除仙玉(保证金,"拍卖保证金",id) == false then
    --   常规提示(id,"#Y/你没有那么多的仙玉")
     --if 取银子(id)<保证金 then
      if 玩家数据[id].角色.数据.比武积分.当前积分<保证金 then
        -- if 玩家数据[id].角色:扣除银子(保证金,"拍卖保证金",id) == false then
      常规提示(id,"#Y/你没有那么多的比武积分")
      return
    else
      玩家数据[id].角色:扣除积分(保证金,0,0,"拍卖保证金",1)
      local table = {[id]={金额=保证金,缴纳=true,取回=false,时间=os.time()}}
      MergeTable(拍卖系统数据.保证金数据[编号],table)
      拍卖系统数据.当前拍品[编号].报名人数=拍卖系统数据.当前拍品[编号].报名人数+1
      if 拍卖系统数据.竞拍数据[id]==nil then
        拍卖系统数据.竞拍数据[id]={}
      end
      table = {[#拍卖系统数据.竞拍数据[id]+1]={编号=编号,当前出价=0,保证金=保证金,结束=false}}
      MergeTable(拍卖系统数据.竞拍数据[id],table)
      self:刷新拍卖数据(id,编号)
      发送数据(玩家数据[id].连接id,7,"#Y/你已经成功缴纳物品保证金，可以出价参与竞拍！")
    end
  else
    发送数据(玩家数据[id].连接id,7,"#Y/你已经缴纳过该拍品的保证金了，无须重复缴纳！")
  end
end

function 拍卖系统类:增加出价记录(id,编号,出价)
   __gge.print(false,6,时间转换(os.time()).."     玩家 ")
  __gge.print(false,11,玩家数据[id].角色.数据.名称)
  __gge.print(false,10," 出价\n")
  local 名称 = 玩家数据[id].角色.数据.名称
  __S服务:发送(id,编号,出价)
  local table = {[#拍卖系统数据.竞价记录[编号]+1]={id=名称,出价=出价,时间=os.time()}}
  if 拍卖系统数据.竞价记录[编号]~=nil then
    MergeTable(拍卖系统数据.竞价记录[编号],table)
  end
  for n, v in pairs(玩家数据) do
    发送数据(玩家数据[n].连接id,148.5,{数据=拍卖系统数据.竞价记录,编号=编号})
  end
end

function 拍卖系统类:刷新拍卖数据(id,编号)
  for n, v in pairs(玩家数据) do
    发送数据(玩家数据[n].连接id,148.1,拍卖系统数据.当前拍品[编号])
  end
end

function 拍卖系统类:竞价数据处理(连接id,序号,id,内容)
  if 拍卖系统数据.竞拍数据[id] ~= nil then
    发送数据(玩家数据[id].连接id,148.2,拍卖系统数据.竞拍数据[id])
  else
    发送数据(玩家数据[id].连接id,7,"#Y/你目前没有参与任何竞拍哦")
  end
end


function 拍卖系统类:拍卖结束处理(连接id,序号,id,内容)
  local 编号1=内容.编号1
  local 编号2=内容.编号2--拍品编号
  if 拍卖系统数据.竞拍数据[id][编号1].结束 == true then
    if 拍卖系统数据.竞拍数据[id][编号1].状态 == "出局" then
      if 拍卖系统数据.竞拍数据[id][编号1].保证金~=0 then
        玩家数据[id].角色:添加银子(拍卖系统数据.竞拍数据[id][编号1].保证金,玩家数据[id].账号,id,"退回保证金")
        拍卖系统数据.竞拍数据[id][编号1].保证金=0
        发送数据(玩家数据[id].连接id,7,"#Y/保证金已经退还")
        return
      else
        发送数据(玩家数据[id].连接id,7,"#Y/你没有保证金存放在我这里")
        return
      end
    elseif 拍卖系统数据.竞拍数据[id][编号1].状态 == "成交" then
      if 拍卖系统数据.竞拍数据[id][编号1].保证金~=0 then
        local 临时格子,道具编号
        if 拍卖系统数据.当前拍品[编号2].分类=="物品" then
          临时格子 = 玩家数据[id].角色:取道具格子()
          if 临时格子 == 0 then
            发送数据(玩家数据[id].连接id, 7, "#R/您身上似乎没有多余的道具存放空间")
            return
          end
        elseif 拍卖系统数据.当前拍品[编号2].分类=="召唤兽" then
          if 玩家数据[id].召唤兽:是否携带上限() then
            发送数据(玩家数据[id].连接id, 7, "#R/您当前无法携带更多的召唤兽了")
            return
          end
        end
        local 差价 = 拍卖系统数据.当前拍品[编号2].当前价-拍卖系统数据.竞拍数据[id][编号1].保证金
             --if 取银子(id)< 差价 then
              if 玩家数据[id].角色.数据.比武积分.当前积分< 差价 then
      常规提示(id,"#Y/你的比武积分不足缴纳剩余费用，还需缴纳#R/"..差价.."点#Y/比武积分")
      return

        else
          玩家数据[id].角色:扣除积分(差价,0,0,"拍卖成交补差价",1)
          拍卖系统数据.竞拍数据[id][编号1].保证金=0
          if 拍卖系统数据.当前拍品[编号2].分类=="物品" then
            local 道具编号 = 玩家数据[id].道具:取新编号()
            玩家数据[id].道具.数据[道具编号] = DeepCopy(拍卖系统数据.当前拍品[编号2].数据)
            玩家数据[id].角色.数据.道具[临时格子]=道具编号
            道具刷新(id)
            local 物品1 = 拍卖系统数据.当前拍品[编号2].名称
            发送数据(玩家数据[id].连接id, 7, "#R/恭喜您成功的取出了拍卖所获得的物品")
            广播消息({内容=format("#Y/恭喜玩家#S"..玩家数据[id].角色.数据.名称.."#Y在拍卖系统中以#G"..拍卖系统数据.当前拍品[编号2].当前价.."#Y拍到#P"..物品1),频道="xt"})
            return
          elseif 拍卖系统数据.当前拍品[编号2].分类=="召唤兽" then
            玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据+1]=DeepCopy(拍卖系统数据.当前拍品[编号2].数据)
            发送数据(玩家数据[id].连接id,16,玩家数据[id].召唤兽.数据)
            发送数据(玩家数据[id].连接id, 7, "#R/恭喜您成功的取出了拍卖所获得的召唤兽")
            return
          end
        end
      else
        发送数据(玩家数据[id].连接id,7,"#Y/你没有拍卖品存放在我这里")
        return
      end
    end
  end
end


function 拍卖系统类:拍卖系统管理(连接id,序号,id,内容)
  local 事件=内容.事件
  if 事件=="开启" then
    拍卖系统数据.拍卖开关=true
    发送数据(玩家数据[id].连接id,7,"#Y/拍卖系统已开启")
    广播消息({内容=format("#Y/各位玩家请注意，拍卖系统已经开启，想捡漏的玩家可以点开拍卖系统查看是否有心仪的物品。"),频道="xt"})
  elseif 事件=="关闭" then
    拍卖系统数据.拍卖开关=false
    发送数据(玩家数据[id].连接id,7,"#Y/拍卖系统已关闭")
    广播消息({内容=format("#Y/各位玩家请注意，拍卖系统已经关闭，请耐心等候下一次拍卖活动的开启(每周周日晚上8点开启)。"),频道="xt"})
  elseif 事件=="清空" then
    拍卖系统数据={拍卖开关=false,当前拍品={},竞拍数据={},竞价记录={},保证金数据={}}
    发送数据(玩家数据[id].连接id,7,"#Y/拍卖系统已经全部清空并已关闭")
  elseif 事件=="查询" then
    发送数据(玩家数据[id].连接id,7,"#Y/当前共有拍品数量为：#R/"..#拍卖系统数据.当前拍品.."个")
  elseif 事件=="添加" then
    local 拍品数据,拍品名称
    local 道具id=内容.选中
    local 拍品数量=#拍卖系统数据.当前拍品
    if 内容.分类 == "物品" then
      if 玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[道具id]] == nil then
        发送数据(玩家数据[id].连接id,7,"#Y/请不要移动道具")
        发送数据(玩家数据[id].连接id,148.4,玩家数据[id].道具:索要道具2(id))
        return
      end
      拍品名称=玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[道具id]].名称
      拍品数据=DeepCopy(玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[道具id]])
      玩家数据[id].道具.数据[玩家数据[id].角色.数据.道具[道具id]]=nil
      玩家数据[id].角色.数据.道具[道具id]=nil
    elseif 内容.分类 == "召唤兽" then
      拍品名称 = 玩家数据[id].召唤兽.数据[道具id].名称
      拍品数据 = DeepCopy(玩家数据[id].召唤兽.数据[道具id])
      table.remove(玩家数据[id].召唤兽.数据,道具id)
    end
    拍卖系统数据.当前拍品[拍品数量+1]={
      名称=拍品名称,
      编号=拍品数量+1,
      分类=内容.分类,
      起拍价=内容.起拍价,
      当前价=内容.起拍价,
      保证金=内容.保证金,
      报名人数=0,
      竞价记录=0,
      类型=内容.类型,
      起拍时间=内容.起拍时间,
      结束时间=内容.结束时间,
      数据=拍品数据,
      加价=内容.加价
    }
    拍卖系统数据.保证金数据[拍品数量+1]={}
    拍卖系统数据.竞价记录[拍品数量+1]={}
    发送数据(玩家数据[id].连接id,16,玩家数据[id].召唤兽.数据)
    发送数据(玩家数据[id].连接id,148.4,玩家数据[id].道具:索要道具2(id))
    发送数据(玩家数据[id].连接id,7,"#Y/添加拍品成功！")
  end
  return
end


function 拍卖系统类:更新(dt) end

function 拍卖系统类:显示(x,y) end

return 拍卖系统类
