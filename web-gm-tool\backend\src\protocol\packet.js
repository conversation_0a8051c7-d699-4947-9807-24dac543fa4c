/**
 * GM工具数据包处理模块
 * 实现HP-Socket协议和MessagePack封装
 */

const msgpack = require('msgpack-lite');
const { jm, jm1, tableToString } = require('./encryption');

// HP-Socket包头标识 (GGELUA_FLAG计算结果)
const PACKET_HEADER_FLAG = 814;

// 数据分隔符
const FGF = "12345*-*12345";
const FGC = "12345@+@12345";

/**
 * 创建HP-Socket数据包
 * @param {Buffer} data - 要封装的MessagePack数据
 * @returns {Buffer} HP-Socket格式的数据包
 */
function createHPSocketPacket(data) {
    const headerSize = 6; // HP-Socket头部大小：2+4
    const totalSize = headerSize + data.length;

    const packet = Buffer.allocUnsafe(totalSize);

    // HP-Socket结构：
    // uint16_t flag;    // 包头标识：814
    // uint32_t len;     // 包体长度

    let offset = 0;

    // flag (uint16_t) - 814
    packet.writeUInt16LE(PACKET_HEADER_FLAG, offset);
    offset += 2;

    // len (uint32_t) - 包体长度
    packet.writeUInt32LE(data.length, offset);
    offset += 4;

    // 复制MessagePack数据
    data.copy(packet, offset);

    console.log('[HP-Socket] 包头标识:', PACKET_HEADER_FLAG);
    console.log('[HP-Socket] 包体长度:', data.length);
    console.log('[HP-Socket] 总包长度:', totalSize);

    return packet;
}

/**
 * 创建发送数据包
 * @param {number} id - 序号
 * @param {object|string} content - 内容
 * @param {string} account - 账号（实际上对应原始代码中的获取账号变量）
 * @returns {Buffer} 完整的数据包
 */
function createSendPacket(id, content, account = '') {
    try {
        // 1. 格式化内容
        let formattedContent = content;
        if (typeof content === 'object') {
            formattedContent = tableToString(content);
        }

        // 2. 组合数据：序号 + 分隔符 + 内容 + 分隔符 + 获取账号
        // 注意：获取账号在登录时应该是空字符串，只有在收到序号13后才会有值
        const 获取账号 = ''; // 登录时获取账号为空
        const combinedData = `${id}${FGF}${formattedContent}${FGF}${获取账号}`;
        console.log('[数据包] 组合数据:', combinedData);
        
        // 3. 加密数据
        const encryptedData = jm(combinedData);
        console.log('[数据包] 加密后数据长度:', encryptedData.length);
        console.log('[数据包] 加密后数据前100字符:', encryptedData.substring(0, 100));

        // 4. MessagePack封装
        const packedData = msgpack.encode([encryptedData]);
        console.log('[数据包] MessagePack封装后长度:', packedData.length);
        console.log('[数据包] MessagePack前32字节Hex:', Array.from(packedData.slice(0, 32)).map(b => b.toString(16).padStart(2, '0').toUpperCase()).join(' '));
        console.log('[数据包] MessagePack首字节:', packedData[0], '(0x' + packedData[0].toString(16).toUpperCase() + ')');

        // 5. 创建HP-Socket数据包
        const packet = createHPSocketPacket(packedData);
        console.log('[数据包] 最终数据包长度:', packet.length);
        console.log('[数据包] === 数据包创建完成 ===');

        return packet;
        
    } catch (error) {
        console.error('[数据包] 创建发送数据包失败:', error);
        throw error;
    }
}

/**
 * 解析接收到的数据包
 * @param {Buffer} buffer - 接收到的数据包
 * @returns {object|null} 解析后的数据对象
 */
function parseReceivePacket(buffer) {
    try {
        console.log('[数据包] 收到数据包，长度:', buffer.length);

        // 1. 解析HP-Socket包头
        const packetInfo = parseHPSocketPacket(buffer);
        if (!packetInfo) {
            console.error('[数据包] HP-Socket包头解析失败');
            return null;
        }

        console.log('[数据包] 包头标识:', packetInfo.flag, '数据长度:', packetInfo.dataLength);

        // 2. MessagePack解包
        const unpackedData = msgpack.decode(packetInfo.data);
        if (!Array.isArray(unpackedData) || unpackedData.length === 0) {
            console.error('[数据包] MessagePack解包失败或数据为空');
            return null;
        }

        const encryptedData = unpackedData[0];
        console.log('[数据包] 解包后加密数据长度:', encryptedData.length);

        // 3. 解密数据
        const decryptedData = jm1(encryptedData);
        if (!decryptedData) {
            console.error('[数据包] 数据解密失败');
            return null;
        }

        console.log('[数据包] 解密后数据:', decryptedData);

        // 4. 解析数据结构
        const parsedData = parseDataStructure(decryptedData);
        console.log('[数据包] 解析后数据结构:', parsedData);

        return parsedData;

    } catch (error) {
        console.error('[数据包] 解析接收数据包失败:', error);
        return null;
    }
}

/**
 * 创建HP-Socket数据包
 * @param {Buffer} data - 要封装的数据
 * @returns {Buffer} HP-Socket数据包
 */
function createHPSocketPacket(data) {
    // HP-Socket包格式：[包头标识(2字节)] + [数据长度(4字节)] + [数据内容]
    const headerBuffer = Buffer.allocUnsafe(6);
    
    // 写入包头标识 (小端序)
    headerBuffer.writeUInt16LE(PACKET_HEADER_FLAG, 0);
    
    // 写入数据长度 (小端序)
    headerBuffer.writeUInt32LE(data.length, 2);
    
    // 合并包头和数据
    return Buffer.concat([headerBuffer, data]);
}

/**
 * 解析HP-Socket数据包
 * @param {Buffer} buffer - 数据包缓冲区
 * @returns {object|null} 解析结果
 */
function parseHPSocketPacket(buffer) {
    if (buffer.length < 6) {
        console.error('[数据包] 数据包长度不足，至少需要6字节');
        return null;
    }
    
    // 读取包头标识
    const flag = buffer.readUInt16LE(0);
    
    // 读取数据长度
    const dataLength = buffer.readUInt32LE(2);
    
    // 验证包头标识
    if (flag !== PACKET_HEADER_FLAG) {
        console.error('[数据包] 包头标识不匹配，期望:', PACKET_HEADER_FLAG, '实际:', flag);
        return null;
    }
    
    // 验证数据长度
    if (buffer.length < 6 + dataLength) {
        console.error('[数据包] 数据长度不匹配，期望:', dataLength, '实际:', buffer.length - 6);
        return null;
    }
    
    // 提取数据部分
    const data = buffer.slice(6, 6 + dataLength);
    
    return {
        flag: flag,
        dataLength: dataLength,
        data: data
    };
}

/**
 * 解析数据结构（模拟table.loadstring）
 * @param {string} dataStr - 数据字符串
 * @returns {object} 解析后的数据对象
 */
function parseDataStructure(dataStr) {
    try {
        // 尝试解析为JSON格式
        if (dataStr.startsWith('{') && dataStr.endsWith('}')) {
            return JSON.parse(dataStr);
        }
        
        // 解析key=value格式
        const result = {};
        const parts = dataStr.split(',');
        
        for (const part of parts) {
            const [key, value] = part.split('=');
            if (key && value !== undefined) {
                // 尝试转换为数字
                const numValue = Number(value);
                result[key.trim()] = isNaN(numValue) ? value.trim() : numValue;
            }
        }
        
        return result;
        
    } catch (error) {
        console.error('[数据包] 数据结构解析失败:', error);
        return { raw: dataStr };
    }
}

/**
 * 验证数据包完整性
 * @param {Buffer} buffer - 数据包缓冲区
 * @returns {boolean} 是否完整
 */
function isPacketComplete(buffer) {
    if (buffer.length < 6) {
        return false;
    }

    const dataLength = buffer.readUInt32LE(2);
    return buffer.length >= 6 + dataLength;
}

/**
 * 获取数据包所需的总长度
 * @param {Buffer} buffer - 数据包缓冲区
 * @returns {number} 所需的总长度，如果无法确定则返回-1
 */
function getRequiredPacketLength(buffer) {
    if (buffer.length < 6) {
        return 6; // 至少需要包头
    }

    const dataLength = buffer.readUInt32LE(2);
    return 6 + dataLength;
}

module.exports = {
    createSendPacket,
    parseReceivePacket,
    createHPSocketPacket,
    parseHPSocketPacket,
    isPacketComplete,
    getRequiredPacketLength,
    PACKET_HEADER_FLAG,
    FGF,
    FGC
};
