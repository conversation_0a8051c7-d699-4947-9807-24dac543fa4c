import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Statistic, Badge, Typography, Space, Button, Alert, Descriptions } from 'antd';
import { 
  WifiOutlined, 
  UserOutlined, 
  ClockCircleOutlined, 
  ReloadOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const StatusPanel = () => {
  const [serverStats, setServerStats] = useState(null);
  const [loading, setLoading] = useState(false);
  const { userInfo, gameConnected } = useAuth();
  const socket = useSocket();
  const [connectionTime] = useState(new Date());

  // 获取服务器统计信息
  const fetchServerStats = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/stats');
      const data = await response.json();
      if (data.success) {
        setServerStats(data.data);
      }
    } catch (error) {
      console.error('获取服务器统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServerStats();
    // 每30秒刷新一次统计信息
    const interval = setInterval(fetchServerStats, 30000);
    return () => clearInterval(interval);
  }, []);

  // 获取连接状态
  const getConnectionStatus = () => {
    if (gameConnected) {
      return {
        status: 'success',
        text: '已连接',
        icon: <CheckCircleOutlined style={{ color: '#52c41a' }} />
      };
    } else {
      return {
        status: 'error',
        text: '未连接',
        icon: <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />
      };
    }
  };

  const connectionStatus = getConnectionStatus();

  return (
    <div>
      <Title level={3} style={{ marginBottom: 24 }}>
        系统状态监控
      </Title>

      {/* 连接状态警告 */}
      {!gameConnected && (
        <Alert
          message="游戏连接断开"
          description="当前与游戏服务器的连接已断开，请检查网络连接或重新登录。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />
      )}

      <Row gutter={[24, 24]}>
        {/* 连接状态 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="连接状态"
              value={connectionStatus.text}
              prefix={connectionStatus.icon}
              valueStyle={{ 
                color: gameConnected ? '#52c41a' : '#ff4d4f',
                fontSize: '20px'
              }}
            />
          </Card>
        </Col>

        {/* 在线时长 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="在线时长"
              value={dayjs().diff(connectionTime, 'minute')}
              suffix="分钟"
              prefix={<ClockCircleOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>

        {/* 服务器在线用户 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="在线用户"
              value={serverStats?.authenticatedClients || 0}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>

        {/* 游戏连接数 */}
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="游戏连接"
              value={serverStats?.gameConnections || 0}
              prefix={<WifiOutlined />}
              valueStyle={{ color: '#13c2c2' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
        {/* 用户信息 */}
        <Col xs={24} lg={12}>
          <Card 
            title="当前用户信息" 
            extra={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={fetchServerStats}
                loading={loading}
                size="small"
              >
                刷新
              </Button>
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="用户名">
                <Text strong>{userInfo?.username || '未知'}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="服务器地址">
                <Text code>{userInfo?.gameHost || 'localhost'}:{userInfo?.gamePort || 8888}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="连接时间">
                <Text>{dayjs(connectionTime).format('YYYY-MM-DD HH:mm:ss')}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="连接状态">
                <Badge 
                  status={connectionStatus.status} 
                  text={connectionStatus.text}
                />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {/* 服务器信息 */}
        <Col xs={24} lg={12}>
          <Card title="服务器信息">
            <Descriptions column={1} size="small">
              <Descriptions.Item label="总连接数">
                <Text>{serverStats?.totalClients || 0}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="认证用户">
                <Text>{serverStats?.authenticatedClients || 0}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="游戏连接">
                <Text>{serverStats?.gameConnections || 0}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="最后更新">
                <Text>
                  {serverStats?.timestamp ? 
                    dayjs(serverStats.timestamp).format('HH:mm:ss') : 
                    '未知'
                  }
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>

      {/* 功能说明 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card title="功能说明">
            <Space direction="vertical" size="middle" style={{ width: '100%' }}>
              <div>
                <Text strong>充值操作：</Text>
                <Text> 支持仙玉、点卡、银子等各种充值类型，可批量操作</Text>
              </div>
              <div>
                <Text strong>账号管理：</Text>
                <Text> 提供账号封禁、解封、踢下线等管理功能</Text>
              </div>
              <div>
                <Text strong>装备管理：</Text>
                <Text> 支持发送装备、灵饰等道具给指定玩家</Text>
              </div>
              <div>
                <Text strong>游戏活动：</Text>
                <Text> 可开启四墓灵鼠、异界、天降灵猴等各种游戏活动</Text>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default StatusPanel;
