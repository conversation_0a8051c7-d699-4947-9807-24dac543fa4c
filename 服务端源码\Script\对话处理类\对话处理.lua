
--======================================================================--
local 对话处理 = class()
local 活动处理 = require("script/对话处理类/活动处理")()
local 对话预处理= require("script/对话处理类/对话预处理")()

function 对话处理:初始化() end

function 对话处理:地图任务其他预处理(id,数字id,名称,事件,地图编号)
      if not 名称 or not 事件  then return  end



      if 地图编号==1001 and 名称=="袁天罡" and 事件=="给予侠义任务物品" and 玩家数据[数字id].角色:取任务(346)~=0  then
            玩家数据[数字id].给予数据={类型=1,id=0,事件="侠义任务物品"}
            发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
            return
      elseif 地图编号==1815 and  名称=="金库总管" and 事件=="完成跑商任务"  and 帮派处理类:取是否有帮派(数字id) then

              玩家数据[数字id].给予数据={类型=1,id=0,事件="给予银票"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="金库总管",类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
      elseif 地图编号==1815 and 名称=="金库总管" and 事件=="上交金银锦盒" and   帮派处理类:取是否有帮派(数字id) then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="给予金银锦盒"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="金库总管",类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
      elseif 地图编号==1001 and 名称=="李将军" and  事件=="上交物品" and 玩家数据[数字id].最后操作=="官职物品" and 玩家数据[数字id].角色:取任务(110)~=0
              and (任务数据[玩家数据[数字id].角色:取任务(110)].分类==3 or 任务数据[玩家数据[数字id].角色:取任务(110)].分类==4)
              then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="官职任务上交物品"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="李将军",类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
        elseif 名称=="福禄童子" and 事件=="上交物品" and 玩家数据[数字id].最后操作=="新春任务" and   玩家数据[数字id].角色:取任务(212)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(212)].分类==2  then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="新春活动任务"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return
        elseif 事件=="上交物品" and 玩家数据[数字id].最后操作=="师门物品" and 玩家数据[数字id].角色:取任务(111)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(111)].分类==4 and 名称==任务数据[玩家数据[数字id].角色:取任务(111)].门派师傅 then

              玩家数据[数字id].给予数据={类型=1,id=0,事件="门派任务上交物品"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
        elseif 事件=="上交文韵墨香物品" and 玩家数据[数字id].最后操作=="文韵物品" and 玩家数据[数字id].角色:取任务(112)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(112)].分类==4 and 名称==任务数据[玩家数据[数字id].角色:取任务(112)].门派师傅  then

              玩家数据[数字id].给予数据={类型=1,id=0,事件="文韵任务上交物品"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
        elseif 事件=="给予药品" and 玩家数据[数字id].最后操作=="帮派青龙药品" and 玩家数据[数字id].角色:取任务(301)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(301)].分类==2 and 名称==任务数据[玩家数据[数字id].角色:取任务(301)].帮派总管 then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="青龙任务给予药品"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
        elseif 事件=="给予烹饪" and 玩家数据[数字id].最后操作=="帮派青龙烹饪" and 玩家数据[数字id].角色:取任务(301)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(301)].分类==3 and 名称==任务数据[玩家数据[数字id].角色:取任务(301)].帮派总管 then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="青龙任务给予烹饪"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return
        elseif 事件=="给予药品" and 玩家数据[数字id].最后操作=="帮派玄武药品" and 玩家数据[数字id].角色:取任务(302)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(302)].分类==2  and 名称==任务数据[玩家数据[数字id].角色:取任务(302)].帮派总管 then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="玄武任务给予药品"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return

        elseif 事件=="给予烹饪" and 玩家数据[数字id].最后操作=="帮派玄武烹饪" and 玩家数据[数字id].角色:取任务(302)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(302)].分类==3 and 名称==任务数据[玩家数据[数字id].角色:取任务(302)].帮派总管  then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="玄武任务给予烹饪"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return

        elseif 名称=="太白金星" and 事件=="给予烹饪" and 玩家数据[数字id].最后操作=="坐骑任务烹饪" and 玩家数据[数字id].角色:取任务(307)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(307)].分类==2  then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="坐骑任务给予烹饪"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return

        elseif 名称=="大大王" and 事件=="给予药品" and 玩家数据[数字id].最后操作=="坐骑任务药品" and 玩家数据[数字id].角色:取任务(307)~=0
           and 任务数据[玩家数据[数字id].角色:取任务(307)].分类==11  then
            玩家数据[数字id].给予数据={类型=1,id=0,事件="坐骑任务给予药品"}
            发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
            玩家数据[数字id].最后操作=nil
            return

        elseif 名称=="道观" and 事件=="上交木材" and 玩家数据[数字id].角色:取任务(130)~=0
            and 副本数据.车迟斗法.进行[任务数据[玩家数据[数字id].角色:取任务(130)].副本id].进程==1 then
              if 玩家数据[数字id].队伍==0 then
                添加最后对话(数字id,"#Y必须组队才能触发该活动")
                return
              end
              玩家数据[数字id].给予数据={类型=1,id=0,事件="道观建设"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
              玩家数据[数字id].最后操作=nil
              return

        elseif 事件=="给予法宝需求药品" and 玩家数据[数字id].最后操作=="法宝合成药品" and 玩家数据[数字id].角色:取任务(308)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(308)].分类==2 and 名称==任务数据[玩家数据[数字id].角色:取任务(308)].法宝NPC then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="法宝任务给予药品"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return

        elseif 事件=="给予法宝需求烹饪" and 玩家数据[数字id].最后操作=="法宝合成烹饪" and 玩家数据[数字id].角色:取任务(308)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(308)].分类==3 and 名称==任务数据[玩家数据[数字id].角色:取任务(308)].法宝NPC then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="法宝任务给予烹饪"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return

        elseif 事件=="上交乾坤袋" and 玩家数据[数字id].最后操作=="师门乾坤袋" and 玩家数据[数字id].角色:取任务(111)~=0
            and 任务数据[玩家数据[数字id].角色:取任务(111)].分类==7 and 名称==任务数据[玩家数据[数字id].角色:取任务(111)].门派师傅 then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="门派任务上交乾坤袋"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return


        elseif 事件=="上交文韵墨香乾坤袋" and 玩家数据[数字id].最后操作=="文韵乾坤袋" and 玩家数据[数字id].角色:取任务(112)~=0
              and 任务数据[玩家数据[数字id].角色:取任务(112)].分类==7 and 名称==任务数据[玩家数据[数字id].角色:取任务(112)].门派师傅 then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="文韵任务上交乾坤袋"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                玩家数据[数字id].最后操作=nil
                return

       elseif  string.find(名称, "护法")~=nil and  事件=="请出招吧" and 玩家数据[数字id].角色:取任务(107) ~=0 then
                if 活动次数查询(数字id,"门派闯关")==false then return  end
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<3 then 常规提示(数字id,"#Y/进行门派闯关活动最少必须由三人进行") return  end
                if 取等级要求(数字id,69)==false then 常规提示(数字id,"#Y/此任务至少要达到69级") return  end
                if 取队员任务一致(数字id,107,1) then
                    战斗准备类:创建战斗(数字id+0,100011,玩家数据[数字id].角色:取任务(107))
                    return
                end

      elseif  事件=="我要挑战你" and 地图处理类.NPC列表[地图编号] and (名称=="首席大弟子" or (首席争霸[玩家数据[数字id].角色.数据.门派]
              and 首席争霸[玩家数据[数字id].角色.数据.门派].名称==名称)) then
                if 判断是否为空表(玩家数据[数字id].角色.数据.参战宝宝) then
                  常规提示(数字id,"宝宝需要参战才能进入战斗")
                  return
                end
                local 临时门派 = 玩家数据[数字id].角色.数据.门派
                if not 首席争霸[临时门派] then 常规提示(数字id,"#Y/你不是该门派的弟子") return  end
                if 首席争霸[临时门派].历史数据 and 首席争霸[临时门派].历史数据.数字id == 数字id then 常规提示(数字id,"#Y/你已经是首席大弟子了") return  end
                if 取队伍人数(数字id)>1 then 常规提示(数字id,"#Y/该活动只能单人进行") return  end
                local 战斗单位={}
                战斗单位.阵法 = 首席争霸[临时门派].阵法
                战斗单位[1]={
                    位置 = 1,
                    角色分类="角色",
                    名称 = 首席争霸[临时门派].名称,
                    模型 = 首席争霸[临时门派].模型,
                    锦衣 = 首席争霸[临时门派].锦衣,
                    副武器 = 首席争霸[临时门派].副武器,
                    攻击修炼=首席争霸[临时门派].攻击修炼,
                    防御修炼=首席争霸[临时门派].防御修炼,
                    法术修炼=首席争霸[临时门派].法术修炼,
                    抗法修炼=首席争霸[临时门派].抗法修炼,
                    主动技能=Q_门派法术[临时门派]
                }
                if 首席争霸[临时门派].武器 then
                    if type(首席争霸[临时门派].武器)=="string" and 首席争霸[临时门派].武器等级 then
                          战斗单位[1].武器=取武器数据(首席争霸[临时门派].武器,首席争霸[临时门派].武器等级)
                    elseif  type(首席争霸[临时门派].武器)=="table" then
                              战斗单位[1].武器=首席争霸[临时门派].武器
                    end
                end
                local 临时属性 = {"等级","气血","魔法","愤怒","命中","伤害","防御","法伤","法防","速度","躲避","躲闪","体质","魔力","力量","耐力","敏捷","武器伤害","经脉流派","奇经特效","奇经八脉"}
                for i,v in ipairs(临时属性) do
                    if 首席争霸[临时门派][v] then
                        战斗单位[1][v]=首席争霸[临时门派][v]
                    end
                    if v=="躲避" then
                         战斗单位[1].躲闪=首席争霸[临时门派][v]
                    end
                end
                for i,v in ipairs(灵饰战斗属性) do
                    if 首席争霸[临时门派][v] then
                        战斗单位[1][v]=首席争霸[临时门派][v]
                    end
                end
                if 首席争霸[临时门派].宝宝数据 and 首席争霸[临时门派].宝宝数据.名称 then
                    战斗单位[2]={
                          位置 = 6,
                          名称 = 首席争霸[临时门派].宝宝数据.名称,
                          模型 = 首席争霸[临时门派].宝宝数据.模型,
                          攻击修炼=首席争霸[临时门派].BB攻击,
                          防御修炼=首席争霸[临时门派].BB防御,
                          法术修炼=首席争霸[临时门派].BB法术,
                          抗法修炼=首席争霸[临时门派].BB抗法,
                          内丹数据=首席争霸[临时门派].宝宝数据.内丹数据,
                          技能=首席争霸[临时门派].宝宝数据.技能,
                          主动技能={},
                    }
                    for i,v in ipairs(临时属性) do
                        if 首席争霸[临时门派].宝宝数据[v] then
                            战斗单位[2][v]=首席争霸[临时门派].宝宝数据[v]
                        end
                    end
                    for i,v in ipairs(灵饰战斗属性) do
                        if 首席争霸[临时门派].宝宝数据[v] then
                            战斗单位[2][v]=首席争霸[临时门派].宝宝数据[v]
                        end
                    end

                end
                战斗准备类:创建战斗(数字id+0,100222,0,地图编号,战斗单位)

      end






    if 地图编号>100000 then
         if 玩家数据[数字id].房屋.是否创建  then
            if 玩家数据[数字id].房屋.地契ID==数字id then
                 if 名称 == "管家" then
                    if 事件 == "使用厨房" then
                       if 玩家数据[数字id].角色.数据.银子>100000 then
                          玩家数据[数字id].房屋:烹饪处理()
                       else
                           添加最后对话(数字id,"#Y/厨房清洁费得10万银子,主人你的银子不够了")
                       end
                    elseif 事件 == "使用丹房" then
                            if 玩家数据[数字id].角色.数据.银子>100000 then
                          玩家数据[数字id].房屋:炼药处理()
                       else
                           添加最后对话(数字id,"#Y/丹房清洁费得10万银子,主人你的银子不够了")
                       end
                    elseif 事件 == "使用卧室" then
                           玩家数据[数字id].角色.数据.气血上限=玩家数据[数字id].角色.数据.最大气血
                           玩家数据[数字id].角色.数据.气血=玩家数据[数字id].角色.数据.最大气血
                           玩家数据[数字id].角色.数据.魔法=玩家数据[数字id].角色.数据.最大魔法
                           发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
                           添加最后对话(数字id,"#Y/休息的还好么,听说丫鬟的活不错主人可以去试试!!!!")
                    end
                elseif 名称 == "佣人" then
                       if 事件 == "侍奉睡觉" then
                          local 需求银子 =(玩家数据[数字id].角色.数据.最大活力-玩家数据[数字id].角色.数据.活力)*75
                                需求银子 = math.ceil(需求银子+(玩家数据[数字id].角色.数据.最大体力-玩家数据[数字id].角色.数据.体力)*75)
                          if 玩家数据[数字id].角色.数据.银子>需求银子 then
                             玩家数据[数字id].角色.数据.活力= 玩家数据[数字id].角色.数据.最大活力
                             玩家数据[数字id].角色.数据.体力= 玩家数据[数字id].角色.数据.最大体力
                             玩家数据[数字id].角色:扣除银子(需求银子,"房屋休息",1)
                             体活刷新(数字id)
                             添加最后对话(数字id,"#Y/起床了,主人的体力活力已经满了,改干活了!!!!")
                          else
                             添加最后对话(数字id,"#Y/陪主人睡觉是个体力活,得加钱,每回复1点体力或活力需要75两哦")
                          end
                       elseif 事件 == "养儿育女" then
                       end
                elseif 名称 == "牧场看守" and 事件 == "打开牧场界面"  then

                elseif 名称 == "牧场管理员" and 事件 == "进入牧场" then
                         地图处理类:npc传送(数字id,玩家数据[数字id].房屋.牧场ID,16,28)

                end
            else
                添加最后对话(数字id,"#Y/你想干什么!!!!")
            end
         else
               添加最后对话(数字id,"#Y/你都没房子乱点什么,小心我放狗咬你!!!!")
         end
       return
    end


   if 玩家数据[数字id].角色:取任务(390)~=0 then
        嘉年华:NPC对话处理(数字id,名称,事件,地图编号,玩家数据[数字id].角色:取任务(390))
        return
    end






end

function 对话处理:选项解析(id,数字id,序号,内容)
  local 地图编号=玩家数据[数字id].角色.数据.地图数据.编号
  local 事件=内容[1]
  local 名称=内容[3]
  local 对话=""
  local xx = {}
  --优先处理副本对话
  if 地图编号>=7000 and 地图编号<=7100 then --7000-7100是副本地图ID
    副本处理类:对话处理(地图编号,名称,事件,数字id)
    return
  end

   if 地图编号>=10000 and 地图编号<=10018 then --彩虹争霸
       彩虹争霸:对话事件处理(数字id,名称,事件,地图编号)
       return
    end




  -- if 地图编号== 1226 and 地图编号== 1040 and 地图编号== 1131 and 地图编号== 1514 and 地图编号== 1173 and 地图编号== 1091 and 地图编号== 1142 and 地图编号== 1146 and 地图编号== 1116 and 地图编号== 1506 and 地图编号== 1208   and 地图编号== 1110 then                       ----长安保卫
  --     长安保卫战:怪物对话处理(数字id,名称,事件,地图编号)
  --      return
  --   end

  if 玩家数据[数字id].地图单位~=nil then
      活动处理:活动选项解析(id,数字id,序号,内容)
      玩家数据[数字id].地图单位=nil
    return
  end


  对话预处理:选项解析(id,数字id,名称,事件)

  if 玩家数据[数字id].通天对话 then
      游戏活动类:通天回答题目(数字id,事件,玩家数据[数字id].通天数据.任务id)
      return
  end

  if 名称 == "帮派总管" and 内容 ~= nil and 内容[4] ~= nil and (事件 == "帮主" or 事件 == "副帮主" or 事件 == "左护法" or 事件=="右护法"  or 事件 == "长老" or 事件 == "堂主" or 事件 == "帮众") then
            帮派处理类:任命处理(数字id+0,内容[4],事件)
  elseif 名称 == "钱庄老板" then
       if 事件=="我要存钱" then
          发送数据(玩家数据[数字id].连接id,132,{操作="存钱",说明="你当前身上银子为:"..玩家数据[数字id].角色.数据.银子..",输入你要存入的数额",回调={[1]=玩家数据[数字id].角色.数据.银子}})
       elseif 事件=="我要取钱" then
          发送数据(玩家数据[数字id].连接id,132,{操作="取钱",说明="你当前身上存银为:"..玩家数据[数字id].角色.数据.存银..",输入你要取出的数额",回调={[1]=玩家数据[数字id].角色.数据.存银}})
       end
    end


  --=========================================#########=====================进入地图编号事件处理===================================###################################=========================
  if 内容[2]==地图编号 then
    self:地图任务其他预处理(id,数字id,名称,事件,地图编号)
    --对话处理



    if 地图编号==1003 then --桃源村
        -- if 名称=="新手接待师" then
        --           if 事件=="我想要领取新手福利" then

        --                   if 玩家数据[数字id].角色.数据.出生奖励 then
        --                     礼包奖励类:新手使者奖励(数字id,20)
        --                     -- 礼包奖励类:全套灵饰(数字id,60,"无级别限制",1)
        --                     玩家数据[数字id].角色.数据.出生奖励=false
        --                     xsjc(数字id,2)
        --                     return
        --                   else
        --                     常规提示(数字id,"#Y/对不起!你已经领取过奖励了!")
        --                     return
        --                   end
        --           elseif 事件=="剧情任务补领" then
        --                  任务处理类:设置初入桃源村(数字id)
        --           elseif 事件=="领取玄奘剧情系列任务" then
        --               if 玩家数据[数字id].角色:取任务(997)~=0 then
        --                 常规提示(数字id,"#Y/对不起!你已经领取过了!")
        --               else
        --                 任务处理类:设置玄奘的身世(数字id)
        --                end
        --           elseif 事件=="取消桃园剧情系列任务" then
        --             local 任务id=玩家数据[数字id].角色:取任务(999)
        --             玩家数据[数字id].角色:取消任务(任务id)
        --           elseif 事件=="取消商人鬼魂剧情系列任务" then
        --             local 任务id=玩家数据[数字id].角色:取任务(998)
        --             玩家数据[数字id].角色:取消任务(任务id)
        --           elseif 事件=="取消玄奘剧情系列任务" then
        --             local 任务id=玩家数据[数字id].角色:取任务(997)
        --             玩家数据[数字id].角色:取消任务(任务id)
        --           elseif 事件=="重新剧情任务需点一下" then
        --                 xsjc(数字id,2)
        --           elseif 事件=="我要领新手宝宝（无限领取）" then
        --               if 玩家数据[id].召唤兽:是否携带上限() then  --大于数量不操作
        --               常规提示(数字id,"#y/你只能一次领1个宠物")
        --               return
        --               end
        --               玩家数据[数字id].召唤兽:添加召唤兽("龙马","龙马","宝宝",nil,nil,nil,1,1)

        --               常规提示(数字id,"#Y/恭喜您获得了一只新手宝宝!")
        --           elseif 事件=="我要领物理系宝宝（无限领取）" then
        --              -- 玩家数据[id].召唤兽:是否携带上限() then  --大于数量不操作
        --               常规提示(数字id,"#y/您当前无法携带更多的召唤兽了")
        --               -- return
        --               -- end
        --      --         玩家数据[数字id].召唤兽:添加新手召唤兽("超级神狗","宝宝",nil,true,0,nil,nil,1)
        --             ---  常规提示(数字id,"#Y/恭喜您获得了一只物理宝宝!")
        --           end



        --   elseif 名称=="霞姑娘" then
        --            if 事件=="观看回忆" and 玩家数据[数字id].队伍== 0  then
        --                战斗准备类:创建战斗(数字id,110002,0)
        --              elseif 事件=="跳过观看回忆(建议别跳过,非常精彩的战斗)" and 玩家数据[数字id].队伍== 0 then
        --                xsjc(数字id,3)
        --             else
        --                常规提示(数字id,"#Y/该剧情战斗只能#R/单人#Y/进行！")
        --            end
        --   elseif 名称=="刘大婶" then
        --            if 事件=="好的谢谢" then

        --               xsjc(数字id,4)
        --           end
        --   elseif 名称=="孙厨娘" or 名称==玩家数据[数字id].角色.数据.名称 then
        --              if 事件=="我才不会说呢" then
        --                 发送数据(玩家数据[数字id].连接id,1501,{名称=玩家数据[数字id].角色.数据.名称,模型=玩家数据[数字id].角色.数据.模型,
        --                 对话="想让我背着良心说这种话,她喵的有毛病吧",选项={"孙厨娘鄙视了你一眼"}})
        --               elseif 事件=="孙厨娘鄙视了你一眼" then
        --                 发送数据(玩家数据[数字id].连接id,1501,{名称="孙厨娘",模型="女人_染色师",
        --                 对话="不说就不说嘛,这么凶人家干嘛\n本来是想送你#R100#个包子的\n现在你只能获得#Y20#个馊包子了#4",选项={"我宁愿要馊包子也不说"}})
        --               elseif 事件=="我宁愿要馊包子也不说" then
        --               玩家数据[数字id].道具:给予道具(数字id,"包子",20,nil,nil,"专用")
        --               常规提示(数字id,"#Y/你获得了#R20个包子")
        --               xsjc(数字id,5)
        --             end
        --  elseif 名称=="玄大夫" then
        --           if 事件=="这尼玛不会是兽医吧" then
        --             玩家数据[数字id].道具:给予道具(数字id,"四叶花",10,nil,nil,"专用")
        --             玩家数据[数字id].道具:给予道具(数字id,"紫丹罗",10,nil,nil,"专用")
        --             常规提示(数字id,"#Y/你获得了一些#R四叶花")
        --             常规提示(数字id,"#Y/你获得了一些#R紫丹罗")
        --             xsjc(数字id,6)
        --           end
        -- elseif 名称=="谭村长" then
        --           if 事件=="传家宝还外送的?" then
        --               if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 6 then
        --                 礼包奖励类:取随机武器(数字id,0,"无级别限制",1)
        --                 玩家数据[数字id].角色:添加经验(1000,"无级别限制",1)
        --                 for n=1,#玩家数据[数字id].召唤兽.数据 do
        --                   玩家数据[数字id].召唤兽:获得经验(玩家数据[数字id].召唤兽.数据[n].认证码,1000,数字id,"无级别限制",1)
        --                 end
        --                 xsjc(数字id,7)
        --               else
        --                 print("玩家："..玩家数据[数字id].角色.."想利用潭村长漏洞刷经验")
        --               end
        --           end
        -- elseif 名称=="孙猎户" then
        --           if 事件=="开启剧情战斗" then
        --           战斗准备类:创建战斗(数字id,110003,0)
        --           end
        -- elseif 名称=="郭大哥" then
        --       if 事件=="帮帮郭大哥" then
        --           任务处理类:设置桃园浣熊支线(数字id)
        --           发送数据(玩家数据[数字id].连接id,3532.3)
        --           添加最后对话(数字id,"你已获得#G桃园狸猫支线任务#去帮#Y郭大哥#教训一下#S狸猫#吧")
        --       elseif 事件=="领取报酬" then
        --           if 支线奖励[数字id].狸猫奖励==false then
        --             常规提示(数字id,"你都领过报酬了,还领个毛啊!")
        --             return
        --           end
        --             支线奖励[数字id].狸猫奖励=false
        --             local 任务id=玩家数据[数字id].角色:取任务(401)
        --             玩家数据[数字id].角色:取消任务(任务id)

        --       end
        -- elseif 名称=="狸" then
        --       if 事件=="开启剧情战斗" then
        --       战斗准备类:创建战斗(数字id,100226,0)
        --       end
        -- elseif 名称=="狼宝宝" then

        -- end
    elseif 地图编号==1501 then --建邺城
            -- if 名称=="宠物仙子" then
            --     if 事件=="我要更换当前的宠物" then
            --       发送数据(id,6)
            --     elseif 事件=="我想要领取新手福利" then
            --       if 玩家数据[数字id].角色.数据.出生奖励  then
            --         礼包奖励类:新手使者奖励(数字id,20)
            --         玩家数据[数字id].角色.数据.出生奖励=false
            --         xsjc(数字id,2)
            --         return
            --       else
            --         常规提示(数字id,"#Y/对不起!你已经领取过奖励了!")
            --         return
            --       end
            --     end

            if 名称=="宠物仙子" then
                  if 事件=="领取新手礼包" then
                       if 玩家数据[数字id].角色:取任务(999)~=0  then
                           任务数据[玩家数据[数字id].角色:取任务(999)].进程=2
                           玩家数据[数字id].角色:刷新任务跟踪()
                       end
                      if 玩家数据[数字id].角色.数据.出生奖励  then
                          礼包奖励类:新手使者奖励(数字id,20)
                          玩家数据[数字id].角色.数据.出生奖励=false
                      else
                          常规提示(数字id,"#Y/对不起!你已经领取过奖励了!")
                          return
                      end
                  elseif 事件=="我要更换当前的宠物" then
                        发送数据(id,6)
                  end
            elseif 名称=="戏班老板" then
                    if 事件=="我要听一听一斛珠" then
                        if 玩家数据[数字id].队伍==0 or 玩家数据[数字id].队长==false  then
                            常规提示(数字id,"#Y/该任务必须组队完成且由队长领取")
                          elseif 取队伍人数(数字id)<3 then
                              常规提示(数字id,"#Y此副本要求队伍人数不低于3人")
                          elseif 取队伍最低等级(玩家数据[数字id].队伍,50) then
                              常规提示(数字id,"#Y/队伍里有等级小于50级的玩家")
                          else
                              任务处理类:开启一斛珠(数字id)
                          end
                      elseif 事件=="送我进去听戏吧" then
                              副本处理类:副本传送(数字id,"一斛珠")
                      elseif 事件=="查询剩余看戏次数" then
                              local 看戏次数 = 玩家数据[数字id].角色.数据.看戏门票次数 or 0
                              添加最后对话(数字id,"你剩余的看戏次数为：#R"..看戏次数)
                      elseif 事件=="购买戏票（100副本积分）" then
                              if 玩家数据[数字id].角色.数据.副本积分==nil then
                                玩家数据[数字id].角色.数据.副本积分=0
                              end
                              local 副本积分 = 玩家数据[数字id].角色.数据.副本积分 or 0
                              if 副本积分>= 100 then
                                  玩家数据[数字id].角色.数据.副本积分 = 玩家数据[数字id].角色.数据.副本积分 - 100
                                  if 玩家数据[数字id].角色.数据.看戏门票次数 == nil then
                                    玩家数据[数字id].角色.数据.看戏门票次数 = 1
                                  else
                                    玩家数据[数字id].角色.数据.看戏门票次数 = 玩家数据[数字id].角色.数据.看戏门票次数 + 1
                                  end
                                  常规提示(数字id,"#Y/你成功购买了#R戏票!#Y，剩余副本积分为"..玩家数据[数字id].角色.数据.副本积分.."。")
                              else
                                  常规提示(数字id,"#Y/你当前的副本积分为#R"..副本积分.."#Y，不够购买戏票，攒攒再来吧。")
                                  return
                              end
                      end
            elseif 名称==服务端参数.名称 then
                -- if 事件=="领取推广奖励" then
                --   添加最后对话(数字id,"你都没推广哪来的奖励!")
                -- elseif 事件=="领取银子" then
                --   if f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子")=="0" then
                --     添加最后对话(数字id,"你在我这里没有可领取的银子。")
                --     return
                --   else
                --     玩家数据[数字id].角色:添加银子(f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子")+0,"推广员领取",1)
                --     f函数.写配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子","0")
                --   end
                -- elseif 事件=="领取储备" then
                --   if f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备")=="0" then
                --     添加最后对话(数字id,"你在我这里没有可领取的储备。")
                --     return
                --   else
                --     玩家数据[数字id].角色:添加储备(f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备")+0,"推广员领取",1)
                --     f函数.写配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备","0")
                --   end
                -- end
                    if 事件=="领取推广奖励" then
                        添加最后对话(数字id,"你都没推广哪来的奖励!")
                    elseif 事件=="领取银子" then
                         if f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子")=="0" then
                            添加最后对话(数字id,"你在我这里没有可领取的银子。")
                            return
                         else
                            玩家数据[数字id].角色:添加银子(f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子")+0,"推广员领取",1)
                            f函数.写配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","银子","0")
                         end
                    elseif 事件=="领取储备" then
                        if f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备")=="0" then
                           添加最后对话(数字id,"你在我这里没有可领取的储备。")
                           return
                        else
                          玩家数据[数字id].角色:添加储备(f函数.读配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备")+0,"推广员领取",1)
                          f函数.写配置(程序目录..[[data\]]..玩家数据[数字id].账号..[[\账号信息.txt]],"账号配置","储备","0")
                        end
                    elseif 事件=="领取60级新手奖励" then
                          添加最后对话(数字id,"为了帮助你更好的成长，你的等级达到60级后可以在我这里在我这里领取一套80级无级别装备，领取前请先确认身上是否有足够的空间。",{"我要领取60级新手奖励","取消"})
                    elseif 事件=="我要领取60级新手奖励" then
                            if 玩家数据[数字id].角色.数据.等级<60 then
                                添加最后对话(数字id,"领取此奖励要求等级必须达到60级。")
                                return
                           elseif 玩家数据[数字id].角色.数据.新手奖励[1] then
                                添加最后对话(数字id,"你已经领取过此类型的奖励了。")
                                return
                           else
                               玩家数据[数字id].角色.数据.新手奖励[1]=true
                               玩家数据[数字id].角色:添加储备(2000000,"新手奖励",1)
                               礼包奖励类:全套装备(数字id,80,"无级别限制",1,1)
                                常规提示(数字id,"#Y/你获得了#R新手物品奖励")
                               -- local 任务id=玩家数据[数字id].角色:取任务(999)
                               -- 玩家数据[数字id].角色:取消任务(任务id)
                            end
                    elseif 事件=="领取90级新手奖励" then
                          添加最后对话(数字id,"为了帮助你更好的成长，你的等级达到90级后可以在我这里随机领取一套60级灵饰,领取前请先确认身上是否有足够的空间。",{"我要领取90级新手奖励","取消"})
                    elseif 事件=="我要领取90级新手奖励" then
                        if 玩家数据[数字id].角色.数据.等级<90 then
                            添加最后对话(数字id,"领取此奖励要求等级必须达到90级。")
                            return
                        elseif 玩家数据[数字id].角色.数据.新手奖励[2] then
                           添加最后对话(数字id,"你已经领取过此类型的奖励了。")
                            return
                        else
                           玩家数据[数字id].角色.数据.新手奖励[2]=true
                           礼包奖励类:全套灵饰(数字id,60,"无级别限制",1)
                       end
                    end
            elseif 名称=="老孙头" then
                      if 事件=="我愿意帮忙" then
                        xsjc1(数字id,2)
                      elseif 事件=="行吧我去找管家" then
                        常规提示(数字id,"#Y/你失去了一捆#G安魂草")
                        xsjc1(数字id,7)
                      end
            elseif 名称=="牛大胆" then
                  if 事件=="我这就去帮你找烤鸭" then
                      xsjc1(数字id,3)
                  elseif 事件=="上交烤鸭" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="上交烤鸭"}
                        发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="牛大胆",类型="NPC",等级="无"})
                  end
            elseif 名称=="王大嫂" then
                  if 事件=="妈的熊胆可是要2500两银子" then
                      xsjc1(数字id,4)
                    elseif 事件=="上交熊胆" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="上交熊胆"}
                      发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="王大嫂",类型="NPC",等级="无"})
                    elseif 事件=="新枯萎的金莲(支线)" then
                      任务处理类:设置新枯萎金莲支线(数字id)
                      发送数据(玩家数据[数字id].连接id,3532.1)
                    elseif 事件=="给王大嫂讲讲事情的经过" then
                      if 玩家数据[数字id].角色:取道具格子()>=14 then
                      常规提示(数字id,"你的背包空间不足,请留出大于7个格子")
                      return
                      end
                      local 任务id=玩家数据[数字id].角色:取任务(400)
                      玩家数据[数字id].角色:取消任务(任务id)
                      支线奖励[数字id].虎子奖励=false
                      玩家数据[数字id].角色:添加经验(10000,"金莲")
                      玩家数据[数字id].角色:添加储备(10000,"金莲",1)
                      玩家数据[数字id].道具:给予道具(数字id,"月华露",5,100,nil,"专用")
                      玩家数据[数字id].道具:给予道具(数字id,"神兜兜",1,nil,nil,"专用")
                      local 名称="高级魔兽要诀"
                      玩家数据[数字id].道具:给予道具(数字id,名称)
                      常规提示(数字id,"#Y/你获得了高级魔兽要诀,月华露,神兜兜\n#G这次均为专用道具\n#Y无法交易")
                      添加最后对话(数字id,"#Y你获得了高级魔兽要诀,月华露,神兜兜\n#G这次均为专用道具\n#Y无法交易")
                  end
            elseif 名称=="管家" and 事件=="我试试看吧"  then
                  xsjc1(数字id,8)
            elseif 名称=="吹牛王" and 事件=="询问虎子下落"  and 玩家数据[数字id].角色:取任务(400)~=0 then
                  任务数据[玩家数据[数字id].角色:取任务(400)].进程=2
                  玩家数据[数字id].角色:刷新任务跟踪()
                  发送数据(玩家数据[数字id].连接id,3532.2)
            elseif 名称=="马全有" then
                if 事件=="好的我去给你抓" then
                    xsjc1(数字id,9)
                elseif 事件=="海毛虫我给你抓来了" then
                        local 出售数量 = 0
                        for i=1,#玩家数据[数字id].召唤兽.数据 do
                            if 玩家数据[数字id].召唤兽.数据[i]~=nil and 玩家数据[数字id].召唤兽.数据[i].模型 == "海毛虫" and 玩家数据[数字id].召唤兽.数据[i].参战信息 == nil then
                              出售数量 = 出售数量 +1
                              table.remove(玩家数据[数字id].召唤兽.数据,i)
                            end
                        end
                        if 出售数量 == 0 then
                            常规提示(数字id,"#Y你身上有他喵的#G海毛虫#?\n另外#Y别把#上交的#G海毛虫#设置为参战状态")
                        else
                            xsjc1(数字id,10)
                            玩家数据[数字id].道具:给予道具(数字id,"地狱灵芝",1,nil,nil,"专用")
                            常规提示(数字id,"#Y你获得了一个#G地狱灵芝")
                        end
                  elseif 事件=="询问虎子下落" then
                        任务数据[玩家数据[数字id].角色:取任务(400)].进程=3
                        玩家数据[数字id].角色:刷新任务跟踪()
                        发送数据(玩家数据[数字id].连接id,3532.4)
                  end
            elseif 名称=="小花" then
                    if 事件=="小花姑娘,我来找旺财" then
                        任务数据[玩家数据[数字id].角色:取任务(400)].进程=5
                        玩家数据[数字id].角色:刷新任务跟踪()
                        发送数据(玩家数据[数字id].连接id,3532.6)
                      elseif 事件=="美女,你见过金色莲花吗?" then
                        任务数据[玩家数据[数字id].角色:取任务(400)].进程=11
                        玩家数据[数字id].角色:刷新任务跟踪()
                        发送数据(玩家数据[数字id].连接id,3533.1)
                        常规提示(数字id,"#Y你获得了一个#Y枯萎的金莲")
                        玩家数据[数字id].道具:给予道具(数字id,"枯萎的金莲",1)
                        添加最后对话(数字id,"你获得了#Y枯萎的金莲#,去交给#G楚恋依#吧\n如果背包满了没获取到,请去建邺药店购买!")
                    end
            elseif 名称=="建邺守卫" and 事件=="传送江南野外"  then
                    if 取等级要求(数字id,10)==false then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="建邺守卫",模型="男人_衙役",对话="你或队伍中有玩家等级尚未达到10级，无法使用此传送功能。"})
                      return
                    elseif 取队长权限(数字id) then
                      地图处理类:npc传送(数字id,1193,149,65)
                    end
            elseif 名称=="雷黑子" then
                if 事件=="可恶,我一定要干吊你!" then
                      xsjc11(数字id,2)
                      if 取等级要求(数字id,10)==false then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="雷黑子",模型="小孩_雷黑子",对话="你或队伍中有玩家等级尚未达到10级，无法使用此传送功能。"})
                          return
                      elseif 取队长权限(数字id) then
                          地图处理类:npc传送(数字id,1508,49,33)
                      end
                elseif 事件=="黑子哥,借狗一用" then
                        任务数据[玩家数据[数字id].角色:取任务(400)].进程=4
                        玩家数据[数字id].角色:刷新任务跟踪()
                        发送数据(玩家数据[数字id].连接id,3532.5)
                elseif 事件=="谢谢黑子哥,旺财还你" then
                      任务数据[玩家数据[数字id].角色:取任务(400)].进程=10
                      玩家数据[数字id].角色:刷新任务跟踪()
                      发送数据(玩家数据[数字id].连接id,3532.9)
                end
            elseif 名称=="赵捕头" and 事件=="我来领取新手任务"  then
                      if 玩家数据[数字id].角色:取任务(66)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="赵捕头",模型="男人_衙役",对话="你已经领取过赏金任务了#24"})
                        return
                      end
                      if 玩家数据[数字id].角色.数据.等级>=15 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="赵捕头",模型="男人_衙役",对话="只有15级及以下的玩家才可新手任务，你就不要来捣乱了#4"})
                        return
                      end
                      if 玩家数据[数字id].角色:取任务(402)~=0 and 任务数据[玩家数据[数字id].角色:取任务(402)].进程 == 1 then
                        任务处理类:设置新手任务(数字id)
                        任务数据[玩家数据[数字id].角色:取任务(402)].进程=2
                        玩家数据[数字id].角色:刷新任务跟踪()
                        else
                        任务处理类:设置新手任务(数字id)
                      end
            elseif  名称 == "勾魂马面" and 事件 == "我已经想清楚了" then
                 -- 发送数据(id,142.3,1)
            elseif 名称=="飞儿" and 事件=="购买"then
                  玩家数据[数字id].商品列表=商店处理类.商品列表[9]
                  发送数据(id,9,{商品=商店处理类.商品列表[9],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="陈长寿" and  事件=="快些治疗我吧" then
                    if 玩家数据[数字id].角色.数据.等级<=15 then
                      玩家数据[数字id].角色:刷新信息("1")
                      添加最后对话(数字id,"已帮你的角色恢复至最佳状态。")
                    else
                      添加最后对话(数字id,"我这里只能治疗十五级以下的玩家哟。")
                    end
            elseif 名称=="装备收购商" and 事件=="出售"  then
                  玩家数据[数字id].给予数据={类型=1,id=0,事件="装备出售"}
                  发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
            elseif 名称=="装备鉴定商" and 事件=="给予" then
                  玩家数据[数字id].给予数据={类型=1,id=0,事件="装备鉴定"}
                  发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
            end
    elseif 地图编号==1193 then
    elseif 地图编号==1534 and 名称=="李善人" then
          if 事件=="上交地狱灵芝" then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="上交地狱灵芝"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="李善人",类型="NPC",等级="无"})
          elseif 事件=="领取酬劳" then
              local 任务id=玩家数据[数字id].角色:取任务(998)
              玩家数据[数字id].角色:取消任务(任务id)
              玩家数据[数字id].角色:添加经验(10000,"李善人")
              玩家数据[数字id].角色:添加储备(10000,"李善人",1)
              任务处理类:设置玄奘的身世(数字id)
          end
    elseif 地图编号==1526 and 名称=="周猎户" and 事件=="看来不给你点颜色看看是不会交代的"   then
            if 玩家数据[数字id].队伍 == 0 then
               常规提示(数字id,"该任务需要组队完成！")
                return
            end
            local 任务id=玩家数据[数字id].角色:取任务(307)
            战斗准备类:创建战斗(数字id,100043,任务id)
    elseif 地图编号==1525 and 名称=="小宝箱" then
          if 事件=="真是他妈太臭了" and 玩家数据[数字id].角色.数据.妖风战斗== nil then
              玩家数据[数字id].给予数据={类型=1,id=0,事件="真是他妈太臭了"}
              发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="小宝箱",类型="NPC",等级="无"})
          else
              常规提示(数字id,"#Y/你都完成过一次了 你还完成个der啊！")
          end
    elseif 地图编号==1509 and 名称=="商人的鬼魂" and 事件=="开启剧情战斗" then
            战斗准备类:创建战斗(数字id,110004,0)
    elseif 地图编号==1508 then
          if 名称=="妖风" and 事件=="开启妖风战斗"  then
              战斗准备类:创建战斗(数字id,110005,0)
          elseif 名称=="虾精" and 事件=="是的我要去" then  --建邺
               地图处理类:npc传送(数字id,1501,173,67)
          end
    elseif 地图编号==1502 then
            if 名称=="武器店老板" and 事件=="购买"  then  --建邺
                玩家数据[数字id].商品列表=商店处理类.商品列表[4]
                发送数据(id,9,{商品=商店处理类.商品列表[4],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="武器店掌柜" and 事件=="购买"  then  --建邺
                玩家数据[数字id].商品列表=商店处理类.商品列表[3]
                发送数据(id,9,{商品=商店处理类.商品列表[3],银子=玩家数据[数字id].角色.数据.银子})
            end
    elseif 地图编号==1503 and 名称=="服装店老板" and 事件=="购买" then
           玩家数据[数字id].商品列表=商店处理类.商品列表[1]
          发送数据(id,9,{商品=商店处理类.商品列表[1],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1030 and 名称=="酒店老板" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[18]
            发送数据(id,9,{商品=商店处理类.商品列表[18],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1504 and 名称=="药店老板" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[2]
            发送数据(id,9,{商品=商店处理类.商品列表[2],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1016 and 名称=="药店老板" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[19]
            发送数据(id,9,{商品=商店处理类.商品列表[19],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1017 and 名称=="饰品店老板" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[20]
            发送数据(id,9,{商品=商店处理类.商品列表[20],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1505 and 名称=="杂货店老板" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[5]
            发送数据(id,9,{商品=商店处理类.商品列表[5],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1506 then
            if 名称=="船夫" and 事件=="是的我要去" then
                  if 取等级要求(数字id,10)==false then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="船夫",模型="男人_驿站老板",对话="你或队伍中有玩家等级尚未达到10级，无法使用此传送功能。"})
                      return
                  elseif 取队长权限(数字id) then
                      地图处理类:npc传送(数字id,1092,165,132)
                  end
            elseif 名称=="老虾" and 事件=="是的我要去"  then
                    地图处理类:npc传送(数字id,1116,17,107)
            elseif 名称=="云游神医" then
                  if 事件=="请帮我治疗" then
                      if 玩家数据[数字id].角色.数据.等级<=10 then
                        玩家数据[数字id].角色:刷新信息("1")
                        添加最后对话(数字id,"已帮你的角色恢复至最佳状态。")
                      else
                        添加最后对话(数字id,"我这里只能治疗十级以下的玩家哟。")
                      end
                  elseif 事件=="请问虎子在哪?" then
                          任务数据[玩家数据[数字id].角色:取任务(400)].进程=6
                          玩家数据[数字id].角色:刷新任务跟踪()
                          发送数据(玩家数据[数字id].连接id,3532.7)
                  elseif 事件=="开启虎子战斗" then
                          战斗准备类:创建战斗(数字id,100227,0)
                  elseif 事件=="事不宜迟，我这就过去找她" then
                          任务数据[玩家数据[数字id].角色:取任务(400)].进程=8
                          玩家数据[数字id].角色:刷新任务跟踪()
                  end
            elseif 名称=="林老汉" and 事件=="是的，我想进去探个究竟"  then
                   地图处理类:npc传送(数字id,1126,12,77)

            elseif 名称=="楚恋依" then
                  if 事件=="还请恋依姐姐救救虎子" then
                      任务数据[玩家数据[数字id].角色:取任务(400)].进程=9
                      玩家数据[数字id].角色:刷新任务跟踪()
                      发送数据(玩家数据[数字id].连接id,3532.8)
                  elseif 事件=="上交枯萎的金莲" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="上交枯萎的金莲"}
                        发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="楚恋依",类型="NPC",等级="无"})
                  end
            end
    elseif 地图编号==1507 and 名称=="螃蟹精" and 事件=="是的我要去" then
            地图处理类:npc传送(数字id,1501,65,112)
    elseif 地图编号==1508 and 名称=="虾精" and 事件=="是的我要去"  then
           地图处理类:npc传送(数字id,1501,65,112)
    elseif 地图编号==1514 then
            if 名称=="老马猴" then
                  if 事件=="我要进行法术认证" then
                      玩家数据[数字id].召唤兽:法术认证(玩家数据[数字id].连接id,数字id)
                  elseif 事件=="水攻" or 事件=="烈火" or 事件=="雷击" or 事件=="落岩" or 事件=="奔雷咒" or 事件=="水漫金山" or 事件=="地狱烈火" or 事件=="泰山压顶" or 事件=="上古灵符" then
                          玩家数据[数字id].召唤兽:法术认证处理(玩家数据[数字id].连接id,数字id,事件)
                  elseif 事件=="我要取消法术认证" then
                          玩家数据[数字id].召唤兽:取消法术认证(玩家数据[数字id].连接id,数字id)
                  end
              elseif 名称=="猴医仙" then
                      if 事件=="妈的比唐僧还罗嗦" then
                          xsjc2(数字id,11)
                      elseif 事件=="上交餐风饮露" then
                          玩家数据[数字id].给予数据={类型=1,id=0,事件="上交餐风饮露"}
                          发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="猴医仙",类型="NPC",等级="无"})
                      end
              elseif 名称=="老猕猴" then
                      if 事件=="关于潜能点" then
                            if 玩家数据[数字id].角色.数据.潜能果  == nil then
                              玩家数据[数字id].角色.数据.潜能果 = 0
                            end
                            对话="潜能果是一种能把经验转化成潜力的果子。90级＞人物等级≥60级，可食用50个； 155级＞人物等级≥90级，可食用100个；170级＞人物等级≥155级，可食用150个；人物等级≥170，可食用200个。当前已经食用#G"..玩家数据[数字id].角色.数据.潜能果.."#，本次兑换需要消耗#R"..(潜能果经验[玩家数据[数字id].角色.数据.潜能果+1]*10).."#经验"
                            xx={"我要兑换潜能果","什么也不做"}
                            发送数据(玩家数据[数字id].连接id,1501,{名称="老猕猴",模型="马猴",对话=对话,选项=xx})
                      elseif 事件=="我要兑换潜能果" or 事件=="继续兑换潜能果"then
                              if 玩家数据[数字id].角色.数据.潜能果  == nil then
                                玩家数据[数字id].角色.数据.潜能果 = 0
                              end
                              local 消耗经验 = 潜能果经验[玩家数据[数字id].角色.数据.潜能果+1]*10
                              if 玩家数据[数字id].角色.数据.当前经验 < 消耗经验 then
                                添加最后对话(数字id,"你的经验暂时不够兑换潜能果")
                                return
                              elseif 玩家数据[数字id].角色.数据.等级 < 90 and 玩家数据[数字id].角色.数据.潜能果 >= 50 then
                                添加最后对话(数字id,"该等级只能使用50个潜能果")
                                return
                              elseif 玩家数据[数字id].角色.数据.等级 < 155 and 玩家数据[数字id].角色.数据.潜能果 >= 100 then
                                添加最后对话(数字id,"该等级只能使用100个潜能果")
                                return
                              elseif 玩家数据[数字id].角色.数据.等级 < 170 and 玩家数据[数字id].角色.数据.潜能果 >= 150 then
                                添加最后对话(数字id,"该等级只能使用150个潜能果")
                                return
                              elseif 玩家数据[数字id].角色.数据.等级 >= 170 and 玩家数据[数字id].角色.数据.潜能果 >= 200 then
                                添加最后对话(数字id,"该等级只能使用200个潜能果")
                                return
                              else
                                  玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 - 消耗经验
                                  玩家数据[数字id].角色.数据.潜能果 = 玩家数据[数字id].角色.数据.潜能果 + 1
                                  玩家数据[数字id].角色.数据.潜力 = 玩家数据[数字id].角色.数据.潜力 + 1
                                  常规提示(数字id,"兑换潜能果成功,已经自动使用")
                                  对话="潜能果是一种能把经验转化成潜力的果子。90级＞人物等级≥60级，可食用50个； 155级＞人物等级≥90级，可食用100个；170级＞人物等级≥155级，可食用150个；人物等级≥170，可食用200个。当前已经食用#G"..玩家数据[数字id].角色.数据.潜能果.."#，本次兑换需要消耗#R"..(潜能果经验[玩家数据[数字id].角色.数据.潜能果+1]*10).."#经验"
                                  xx={"继续兑换潜能果","什么也不做"}
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="老猕猴",模型="马猴",对话=对话,选项=xx})
                              end
                      end
            end
    elseif 地图编号==1001 then--长安城
           self:长安城对话事件处理(id,数字id,名称,事件,1001)
    elseif 地图编号==1026 and 名称=="吴举人" then
            local 任务id=玩家数据[数字id].角色:取任务(120)
            if 事件=="我要开启乌鸡国副本" then
                任务处理类:开启乌鸡副本(数字id)
            elseif 事件=="请送我进去" then
                  任务处理类:副本传送(数字id,1)
            elseif 事件=="取消乌鸡副本" and 任务id and 任务id~=0 and 任务数据[任务id] then
                    local 副本id=任务数据[任务id].副本id
                    for i,v in pairs(地图处理类.地图单位[6001]) do
                        if 任务数据[地图处理类.地图单位[6001][i].id].副本id == 副本id then
                            地图处理类:删除单位(6001,i)
                        end
                    end
                    for i,v in pairs(地图处理类.地图单位[6002]) do
                        if 任务数据[地图处理类.地图单位[6002][i].id].副本id == 副本id then
                            地图处理类:删除单位(6002,i)
                        end
                    end
                    任务数据[任务id]=nil
                    玩家数据[数字id].角色:取消任务(任务id)
            end
    elseif 地图编号==1044 then
            if 名称=="魏征" and 事件=="多谢,魏征大人" then
                xsjc2(数字id,40)
            elseif 名称=="李世民" then
                    if 事件=="千亿称号[血]" or 事件=="千亿称号[伤]" or 事件=="千亿称号[法]" or 事件=="千亿称号[防]" or 事件=="千亿称号[速]" then
                          local 已有称谓 = ""
                          for i,v in pairs(玩家数据[数字id].角色.数据.称谓) do
                              if string.find(v,"千亿称号")~=nil then
                                    已有称谓=v
                              end
                          end
                          local 对话 = ""
                          if 事件=="千亿称号[血]" then
                              对话=对话.."#G加成:气血 +2000"
                          elseif  事件=="千亿称号[伤]" then
                                  对话=对话.."#G加成:伤害 +1000"
                          elseif 事件=="千亿称号[法]" then
                                    对话=对话.."#G加成:灵力 +800"
                          elseif 事件=="千亿称号[防]" then
                                    对话=对话.."#G加成:防御 +800"
                          elseif 事件=="千亿称号[速]" then
                                    对话=对话.."#G加成:速度 +600"
                          end
                          if 已有称谓~="" then
                              对话=对话..",#W你已获得了#G"..已有称谓.."#W,点击其他查看加成"
                              local 选项 = {"千亿称号[血]","千亿称号[伤]","千亿称号[法]","千亿称号[防]","千亿称号[速]","更换千亿称号","我就是来看看"}
                              发送数据(玩家数据[数字id].连接id,1501,{名称="李世民",模型="皇帝",对话=对话,选项=选项})
                          else
                              对话=对话..",#W兑换该称谓需要1000亿经验,你确定兑换该称谓么?"
                              local 选项 = {"确定兑换"..事件,"我在考虑考虑"}
                              发送数据(玩家数据[数字id].连接id,1501,{名称="李世民",模型="皇帝",对话=对话,选项=选项})
                          end

                    elseif 事件=="确定兑换千亿称号[血]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                                    添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                                    return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -100000000000
                              玩家数据[数字id].角色:添加称谓("千亿称号[血]")
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号[血]#Y终极称谓,大家都来膜拜他吧！！！")

                    elseif 事件=="确定兑换千亿称号[伤]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                                    添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                                    return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -100000000000
                              玩家数据[数字id].角色:添加称谓("千亿称号[伤]")
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号[伤]#Y终极称谓,大家都来膜拜他吧！！！")

                    elseif 事件=="确定兑换千亿称号[法]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                                    添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                                    return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -100000000000
                              玩家数据[数字id].角色:添加称谓("千亿称号[法]")
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号[法]#Y终极称谓,大家都来膜拜他吧！！！")

                    elseif 事件=="确定兑换千亿称号[防]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                                    添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                                    return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -100000000000
                              玩家数据[数字id].角色:添加称谓("千亿称号[防]")
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号[防]#Y终极称谓,大家都来膜拜他吧！！！")
                    elseif 事件=="确定兑换千亿称号[速]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                                    添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                                    return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -100000000000
                              玩家数据[数字id].角色:添加称谓("千亿称号[速]")
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号[速]#Y终极称谓,大家都来膜拜他吧！！！")

                    elseif 事件=="更换千亿称号" then
                            local 已有称谓 = ""
                            for i,v in pairs(玩家数据[数字id].角色.数据.称谓) do
                                if string.find(v,"千亿称号")~=nil then
                                    已有称谓=v
                                end
                            end
                            if 已有称谓~="" then
                               local 选项 = {"[血]","[伤]","[法]","[防]","[速]","溜了溜了"}
                               local 编号 = 0
                               for i,v in ipairs(选项) do
                                    if 已有称谓=="千亿称号"..v then
                                      编号 = i
                                    end
                               end
                               if 编号~=0 then
                                    table.remove(选项,编号)
                                    local 对话 = "更换称号需要消耗500亿经验,点击对应属性直接更换称号,当前兑换:#G"..已有称谓
                                    发送数据(玩家数据[数字id].连接id,1501,{名称="李世民",模型="皇帝",对话=对话,选项=选项})
                               end
                            else
                                  添加最后对话(数字id,"你还未获取千亿称号")
                                  return
                            end

                    elseif 事件=="[血]" or 事件=="[伤]" or 事件=="[法]" or 事件=="[防]" or 事件=="[速]" then
                              if 玩家数据[数字id].角色.数据.当前经验 < 50000000000 then
                                  添加最后对话(数字id,"你的修炼还未达到,等经验达到500亿再来找朕吧")
                                  return
                              end
                              玩家数据[数字id].角色.数据.当前经验 = 玩家数据[数字id].角色.数据.当前经验 -50000000000
                              玩家数据[数字id].角色:批量删除称谓("千亿称号")
                              玩家数据[数字id].角色:添加称谓("千亿称号"..事件)
                              发送公告("恭喜玩家#G"..玩家数据[数字id].角色.数据.名称.."#Y兑换了#R千亿称号"..事件.."#Y终极称谓,大家都来膜拜他吧！！！")

                    end


                    -- if 事件=="领取特殊兽决" then
                    --     if 玩家数据[数字id].角色.数据.当前经验 < 100000000000 then
                    --         添加最后对话(数字id,"你的修炼还未达到,等经验达到1000亿再来找朕吧")
                    --         return
                    --     elseif 玩家数据[数字id].角色.数据.千亿兽决 ~= nil then
                    --         添加最后对话(数字id,"每人只能找朕领取一次,你不是已经领取过了么")
                    --         return
                    --     else
                    --         local 名称="高级魔兽要诀"
                    --         local 技能=取特殊要诀()
                    --         玩家数据[数字id].道具:给予道具(数字id,名称,nil,技能)
                    --         玩家数据[数字id].角色.数据.千亿兽决 = 1
                    --         常规提示(数字id,"#Y/你获得了"..名称)
                    --         广播消息({内容=string.format("#S(千亿经验)#R/%s#Y经验达到千亿,受到#R唐王#Y大加赞赏,特赐下#G/特殊魔兽要诀-%s#Y以示褒奖！".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,技能),频道="xt"})
                    --     end
                    -- elseif 事件=="查看魔兽残卷" then
                    --       local 选项 ={"上交魔兽残卷","取消"}
                    --       if 玩家数据[数字id].角色.数据.魔兽残卷 == nil then
                    --         玩家数据[数字id].角色.数据.魔兽残卷 = 0
                    --       end
                    --       local 对话 ="当前你已经上交了"..玩家数据[数字id].角色.数据.魔兽残卷.."个魔兽残卷,上交魔兽残卷达到300份以后可以获得随机特殊兽决的奖励哟"
                    --       玩家数据[数字id].最后操作="查看魔兽残卷"
                    --       添加最后对话(数字id,对话,选项)
                    -- end


            end
    elseif 地图编号==1022 then
            if 名称=="服装店老板" and 事件=="购买"  then  --建邺
                玩家数据[数字id].商品列表=商店处理类.商品列表[7]
                发送数据(id,9,{商品=商店处理类.商品列表[7],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="张裁缝" and 事件=="关于打造方式的介绍"  then

                  self.发送数据={}
                  self.发送数据.模型="男人_服装店老板"
                  self.发送数据.名称="张裁缝"
                  self.发送数据.对话="装备有两种打造方式，按普通方式打造出来的装备，鉴定后即可佩带；如果按强化方式打造装备，打造的过程会繁琐一些，但装备的基本属性会略强，如果刚好是带有附加属性的装备，所增加的属性也会有所提高。另外按强化方式打造的120级以上的武器还会有特殊的光效，你准备用哪种方式来打造呢？"
                  self.发送数据.选项={}
                  发送数据(id,1501,self.发送数据)
                -- elseif 事件=="普通打造" then
                --   发送数据(id,14,玩家数据[数字id].道具:索要道具1(数字id))
                --   玩家数据[数字id].打造方式=1
                -- elseif 事件=="强化打造" then
                --   if 玩家数据[数字id].角色:取任务(5)~=0 then
                --     常规提示(数字id,"#Y/你已经领取了一个打造任务，赶快去完成吧")
                --     return
                --   end
                --   发送数据(id,14,玩家数据[数字id].道具:索要道具1(数字id))
                --   玩家数据[数字id].打造方式=2


                -- elseif 事件=="元身打造" then
                --   if 玩家数据[数字id].角色:取任务(5)~=0 then
                --     常规提示(数字id,"#Y/你已经领取了一个打造任务，赶快去完成吧")
                --     return
                --   end
                --   发送数据(id,14.1,玩家数据[数字id].道具:索要道具1(数字id))
                --   玩家数据[数字id].打造方式=1
              end
    elseif 地图编号==1024 and 名称=="郑镖头" then
             if 押镖数据[数字id] == nil then
                  押镖数据[数字id] = 1
             end
             if  玩家数据[数字id].角色.押镖间隔~=nil and 玩家数据[数字id].角色.押镖间隔>=os.time() then
                  发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="由于押镖失败您五分钟之内不能再次领取任务。"})
                  return
              end
              if 事件 == "普通押镖任务" then
                    if 玩家数据[数字id].队伍~=0 and 取等级要求(数字id,30) == false then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="请单人并且等级大到30级再来接取押镖任务！"})
                      return
                    end
                    任务处理类:添加押镖任务(数字id)
              elseif 事件=="请帮我取消任务" then
                      if 玩家数据[数字id].角色:取任务(300)==0 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="你没有领取过这样的任务啊"})
                          return
                      elseif 玩家数据[数字id].押镖间隔~=nil and 玩家数据[数字id].押镖间隔>=os.time() then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="五分钟内不能重复取消任务。"})
                            return
                      else
                          玩家数据[数字id].押镖间隔=os.time()+300
                          玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(300))
                          发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="已经帮你取消了此任务。"})
                      end
              elseif 事件=="我要报名参赛(镖王活动)" then
                       发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="本活动需要要求最少三人组队参加，且队伍中的成员等级不能低于30级，活动期间内你将无法使用飞行道具。你可以选择下面三种镖银的任意一种进行押送，价值越高的镖银难度也会越高，但是奖励也会更高。高级镖银和珍贵镖银押送完成后将有一定几率获得",选项={"押送普通镖银","押送高级镖银","押送珍贵镖银","你让我想一想"}})
              elseif 事件=="押送普通镖银" then
                       任务处理类:添加镖王任务(数字id,"普通")
              elseif 事件=="押送高级镖银" then
                       任务处理类:添加镖王任务(数字id,"高级")
              elseif 事件=="押送珍贵镖银" then
                       任务处理类:添加镖王任务(数字id,"珍贵")
              elseif 事件=="请帮我取消任务(镖王活动)" then
                          if 玩家数据[数字id].角色:取任务(208)==0 then
                              发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="你没有领取过这样的任务啊"})
                              return
                          elseif 玩家数据[数字id].镖王间隔~=nil and 玩家数据[数字id].镖王间隔>=os.time() then
                                发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="五分钟内不能重复取消任务。"})
                                return
                          elseif not 玩家数据[数字id].队伍 or 玩家数据[数字id].队伍==0 then
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="请组队前来取消任务"})
                                  return
                          elseif not 玩家数据[数字id].队长 then
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="只有队长可以执行此操作"})
                                  return
                          else
                              local 队伍id=玩家数据[数字id].队伍
                              for n=1,#队伍数据[队伍id].成员数据 do
                                玩家数据[队伍数据[队伍id].成员数据[n]].镖王间隔=os.time()+300
                                玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(208))
                                发送数据(玩家数据[数字id].连接id,1501,{名称="郑镖头",模型="男人_镖头",对话="已经帮你取消了此任务。"})
                              end
                              return
                          end
              end
    elseif 地图编号==1020 then
            if 名称=="武器店老板" and 事件=="购买"  then  --建邺
                  玩家数据[数字id].商品列表=商店处理类.商品列表[8]
                  发送数据(id,9,{商品=商店处理类.商品列表[8],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="武器店掌柜" and 事件=="购买"  then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[4]
                    发送数据(id,9,{商品=商店处理类.商品列表[4],银子=玩家数据[数字id].角色.数据.银子})
            end
    elseif 地图编号==1116 then
          if 名称=="虾兵" and 事件=="是的 我要去" then
               地图处理类:npc传送(数字id,1506,95,37)
          elseif 名称=="传送蟹将" and 事件=="是的我要去" then
                地图处理类:npc传送(数字id,1001,470,253)
          elseif 名称=="蟹将军" and 事件=="卧槽不分青红皂白就打我啊!"  then
                  战斗准备类:创建战斗(数字id,110012,0)
          elseif 名称=="龟千岁" and 事件=="好吧,我先回去了" then
              xsjc2(数字id,34)
          end
    elseif 地图编号==1198 and 名称=="传送护卫" and 事件=="是的我要去" then
           地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1002  then
          if 名称=="接引僧" and 事件=="是的我要去" then
                地图处理类:npc传送(数字id,1001,470,253)
          elseif 名称=="慧海" and 事件=="我这就去问问" then
                  xsjc2(数字id,10)
          elseif 名称=="疥癞和尚"  then
                local 任务id=玩家数据[数字id].角色:取任务(150)
                if 事件=="我要来帮忙" then
                    任务处理类:开启水陆副本(数字id)
                    玩家数据[数字id].角色:刷新任务跟踪()
                elseif 事件=="送我过去帮忙" and 任务id and 任务id~=0 and 任务数据[任务id] then
                      local 副本id=任务数据[任务id].副本id
                      任务处理类:副本传送(数字id,3)
                      玩家数据[数字id].角色:刷新任务跟踪()
                elseif 事件=="取消水陆副本任务" and 任务id and 任务id~=0 and 任务数据[任务id] then
                      local 副本id=任务数据[任务id].副本id
                      for i,v in pairs(地图处理类.地图单位[6024]) do
                          if 任务数据[地图处理类.地图单位[6024][i].id].副本id == 副本id then
                              地图处理类:删除单位(6024,i)
                          end
                      end
                      for i,v in pairs(地图处理类.地图单位[6025]) do
                          if 任务数据[地图处理类.地图单位[6025][i].id].副本id == 副本id then
                            地图处理类:删除单位(6025,i)
                          end
                      end
                      for i,v in pairs(地图处理类.地图单位[6026]) do
                          if 任务数据[地图处理类.地图单位[6026][i].id].副本id == 副本id then
                            地图处理类:删除单位(6026,i)
                          end
                      end
                      任务数据[任务id]=nil
                      玩家数据[数字id].角色:取消任务(任务id)
                 end
            end
    elseif 地图编号==1004 and 名称 == "太乙真人"  and 事件=="师父！快醒醒！你召的豆兵全被魔气侵染了！" then
                local 任务id = 玩家数据[数字id].角色:取任务(311)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取队员任务一致(数字id,311,1) then
                    战斗准备类:创建战斗(数字id+0,100097,任务id)
                    return
                end

    elseif 地图编号==1005 and 名称 == "敖丙" and 事件=="灵珠之力，岂容你优柔寡断！" then
                local 任务id = 玩家数据[数字id].角色:取任务(311)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取队员任务一致(数字id,311,1) then
                    战斗准备类:创建战斗(数字id+0,100098,任务id)
                    return
                end
    elseif 地图编号==1006 and 名称 == "申公豹" and 事件=="你的执念，早已化作心魔！" then
                local 任务id = 玩家数据[数字id].角色:取任务(311)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取队员任务一致(数字id,311,1) then
                    战斗准备类:创建战斗(数字id+0,100099,任务id)
                    return
                end
    elseif 地图编号==1007 and 名称 == "龙王" and 事件=="老泥鳅，小爷的命硬得很，你淹不死！" then
                local 任务id = 玩家数据[数字id].角色:取任务(311)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取队员任务一致(数字id,311,1) then
                    战斗准备类:创建战斗(数字id+0,100100,任务id)
                    return
                end
    elseif 地图编号==1008 and 名称 == "哪吒" and 事件=="哪吒！敖丙还在等你"  then
                local 任务id = 玩家数据[数字id].角色:取任务(311)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取队员任务一致(数字id,311,1) then
                    战斗准备类:创建战斗(数字id+0,100101,任务id)
                    return
                end
    elseif 地图编号==1090 then
    elseif 地图编号==1009 and 名称=="雁塔地宫使者" then
           local 取层数 = function()
                  if 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                      for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                            if not 雁塔地宫[v] then
                                雁塔地宫[v] = {层数=0,火把=0,领取日期=0}
                            end
                            if 雁塔地宫[v].层数~=雁塔地宫[数字id].层数 then
                                return true
                            end
                      end
                  else
                     return false
                  end
                  return false
            end
            local 取火把 = function()
                    if 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                        for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                            if 雁塔地宫[v].火把<= 0 then
                                return true
                            end
                        end
                    else
                        return 雁塔地宫[数字id].火把 >0
                    end
                    return false
            end
            local 扣除火把 = function()
                    if 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                        for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                            雁塔地宫[v].火把 = 雁塔地宫[v].火把 - 1
                            常规提示(v,"#Y你使用了一个火把！")
                        end
                    else
                        雁塔地宫[数字id].火把= 雁塔地宫[数字id].火把 - 1
                        常规提示(数字id,"#Y你使用了一个火把！")
                    end
            end
            if not 雁塔地宫[数字id]then
                 雁塔地宫[数字id] = {层数=0,火把=0,领取日期=0}
            end
            if 事件=="开始探查" then
                    添加最后对话(数字id,"当前你已探查的层数为#R/"..雁塔地宫[数字id].层数.."#W/层!",{"确定探查","查看排行","我再考虑考虑！"})
            elseif 事件=="确定探查" then
                    if 玩家数据[数字id].队伍
                      and 玩家数据[数字id].队伍~=0
                      and not 玩家数据[数字id].队长 then
                        添加最后对话(数字id,"你不是队长无法开启！")
                    elseif 取层数() then
                            添加最后对话(数字id,"队员层数不一致！")
                    elseif  取火把() then
                            添加最后对话(数字id,"组队探查要求队员身上同样携带火把！")
                    elseif 雁塔地宫[数字id].层数 >= 100 then
                            添加最后对话(数字id,"已经达到最高层了,无法继续探查，请静等活动重置！")
                    else
                        扣除火把()
                        战斗准备类:创建战斗(数字id+0,100223,2)
                    end
            elseif 事件=="玩法介绍" then
                    添加最后对话(数字id,"#Y(1).#P挑战每层的守护兽，成功可进入下一层。\n\n#Y(2).#P每次挑战需要消耗道具火把，挑战成功可获得奖励。\n\n#Y(3).#P雁塔地宫共100层，若本服有队伍通关100层并成功封印蚩尤后重置地宫(封印蚩尤每人有7天CD,队伍中所有人CD都结束才可以触发战斗)。\n\n#Y(4).#P雁塔地宫挑战消耗火把，每挑战一次消耗一把（重复挑战某关或挑战失败也会消耗火把）。每天NPC可免费领3把，每天最多购买15把。\n\n#Y(5).#P每完成10层挑战时可以获得奖励礼包。")
                    玩家数据[数字id].最后操作="玩法介绍"
            elseif 事件=="查看排行" then
                    local 对话选项 = {}
                    for i=1,15 do
                        if 地宫排行[i] then
                            对话选项[#对话选项+1] = "第"..i.."名".."  "..地宫排行[i].名称
                        end
                    end
                    添加最后对话(数字id,"以下为本轮雁塔地宫活动排名前15的玩家信息,你可以点击选项查看具体玩家信息:",对话选项)
            elseif 事件=="火把相关" then
                    添加最后对话(数字id,"你当前拥有"..雁塔地宫[数字id].火把.."个火把，每日可以在我这里免费领取3个火把，火把最多可累计存放15把！",{"领取火把","购买火把"})
            elseif 事件=="领取火把" then
                    if 取年月日(os.time()) == 雁塔地宫[数字id].日期 then
                        添加最后对话(数字id,"你今天已经领取过了！")
                    elseif 雁塔地宫[数字id].火把 >= 15 then
                            添加最后对话(数字id,"你神兽的火把太多了请使用后在来领取！")
                    else
                        雁塔地宫[数字id].火把=雁塔地宫[数字id].火把+3
                        if 雁塔地宫[数字id].火把 >= 15 then 雁塔地宫[数字id].火把 = 15 end
                        雁塔地宫[数字id].日期=取年月日(os.time())
                        添加最后对话(数字id,"领取成功！")
                    end
            elseif 事件=="购买火把" then
                      local 购买花费 = tonumber(f函数.读配置(程序目录.."配置文件.ini","主要配置","地宫火把")) or 1000000
                      添加最后对话(数字id,"你可以花费"..购买花费.."银两在我这里购买火把！",{"确定购买","我没钱"})
            elseif 事件=="确定购买" or 事件=="继续购买" then
                      local 购买花费 = tonumber(f函数.读配置(程序目录.."配置文件.ini","主要配置","地宫火把")) or 1000000
                      if 取银子(数字id) < 购买花费 then
                          添加最后对话(数字id,"你没有这么多的银子~！")
                      elseif 雁塔地宫[数字id].火把 < 15 then
                            玩家数据[数字id].角色:扣除银子(购买花费,"雁塔地宫",1)
                            雁塔地宫[数字id].火把=雁塔地宫[数字id].火把+1
                            if 雁塔地宫[数字id].火把 >= 15 then 雁塔地宫[数字id].火把 = 15 end
                            添加最后对话(数字id,"你可以花费"..购买花费.."银两在我这里购买火把！",{"继续购买","我没钱"})
                      else
                            添加最后对话(数字id,"你身上可存放的火把数量已经满了！")
                      end
            elseif 事件=="我要封印蚩尤" then
                      if 雁塔地宫[数字id] and 雁塔地宫[数字id].层数 >= 100 then
                          if not 玩家数据[数字id].队伍 or 玩家数据[数字id].队伍==0 then
                              添加最后对话(数字id,"该任务难度较大，请组队前来！")
                              return
                          elseif not 玩家数据[数字id].队长 then
                                  添加最后对话(数字id,"只有队长开运开启！")
                                  return
                          elseif 取层数() then
                                  添加最后对话(数字id,"队员层数不一致！")
                                  return
                          end
                          for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                              if 雁塔地宫[v].挑战蚩尤 and os.time()<雁塔地宫[v].挑战蚩尤  then
                                  添加最后对话(数字id,"队内成员"..玩家数据[v].角色.数据.名称.."已经参加过封印蚩尤了！")
                                  return
                              end
                          end
                          for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
                              雁塔地宫[v].挑战蚩尤= os.time() + 3600*24*7
                          end
                          战斗准备类:创建战斗(数字id+0,100224,3)
                      else
                        添加最后对话(数字id,"你当前没有资格封印蚩尤(只有地宫达到100层以上且本轮未参与过封印蚩尤方可尝试封印)！")
                      end
            else
                  for i=1,15 do
                      if 地宫排行[i] then
                          if 事件 == "第"..i.."名".."  "..地宫排行[i].名称 then
                            local 对话内容 = "#Y/以下为本轮雁塔地宫排名第#Y"..i.."#Y的#R"..地宫排行[i].名称.."#Y具体信息：\n\n"
                            对话内容 = 对话内容 .. "#W/等级："..地宫排行[i].等级.."\n\n"
                            对话内容 = 对话内容 .. "#W/门派："..地宫排行[i].门派.."\n\n"
                            对话内容 = 对话内容 .. "#W/层数："..地宫排行[i].层数.."层\n\n"
                            添加最后对话(数字id,对话内容)
                            return
                          end
                      end
                  end
            end
    elseif 地图编号==1135 then
            if 名称=="接引道童" and 事件=="是的我要去" then
                          地图处理类:npc传送(数字id,1001,470,253)
            elseif 名称=="觉岸" then
                    if 事件=="我想学习奇门遁甲" then
                          玩家数据[数字id].角色:学习剧情技能(数字id,"奇门遁甲",4,4)
                    elseif 事件=="购买五色旗盒" then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你可以在我这里花费5000000两银子购买五色旗盒，请确认是否要购买五色旗盒？",选项={"确认购买","我没那么多的钱"}})
                    elseif 事件=="确认购买" then
                           if 玩家数据[数字id].角色.数据.银子<5000000 then
                                发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你没有那么多的银子"})
                                return
                           else
                                玩家数据[数字id].角色:扣除银子(5000000,"觉案购买五色旗盒",1)
                                玩家数据[数字id].道具:给予法宝(数字id,"五色旗盒")
                                发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你花费5000000两银子成功购买了一个五色旗盒#1"})
                                return
                           end
                   end
            end
    elseif 地图编号==1142 and 名称=="接引女使" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1138 and 名称=="引路族民" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1513 and 名称=="引路小妖" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1131 and 名称=="传送小妖" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1512 and 名称=="传送牛妖" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1122 then
            if 名称=="地遁鬼" and 事件=="是的我要去"  then
                      地图处理类:npc传送(数字id,1001,470,253)
            elseif 名称=="钟馗" then
                    if 事件=="好的 我帮你" then
                      任务处理类:添加抓鬼任务(数字id)
                    elseif 事件=="取消 抓鬼任务" then
                            if 取队伍任务(玩家数据[数字id].队伍,8) then
                              local 队伍id=玩家数据[数字id].队伍
                                    for n=1,#队伍数据[队伍id].成员数据 do
                                          if 任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(8)] then
                                          地图处理类:删除单位(任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(8)].地图编号,任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(8)].编号)
                                          end
                                          玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(8))
                                          常规提示(队伍数据[队伍id].成员数据[n],"#Y/已经取消任务,同时任务总数清0")
                                    end
                            else
                                 常规提示(数字id,"#Y/对不起!你都没接任务,何来取消?")
                            end
                    end
            elseif 名称=="孟婆"  then
                      if 事件=="当主角真累啊,都是跑腿的命" then
                          xsjc2(数字id,20)
                      elseif 事件=="你接过孟婆汤" then
                              xsjc2(数字id,24)
                      end
            elseif 名称=="地府商人" and 事件=="购买商品" then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[29]
                    self.初始金额 = 0
                    for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                        if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                          self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                        end
                    end
                    发送数据(id,70,{商品=商店处理类.商品列表[29],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
            elseif 名称=="地府货商"  and 事件=="购买商品"  then
                        玩家数据[数字id].商品列表=商店处理类.商品列表[30]
                        self.初始金额 = 0
                        for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                            if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                              self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                            end
                        end
                        发送数据(id,70,{商品=商店处理类.商品列表[30],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})


            end
    elseif 地图编号==1127 and 名称=="幽冥鬼" then
          if 事件=="做爱什么的我不知道,但是我知道怎么超度你!" then
                战斗准备类:创建战斗(数字id,110011,0)
          elseif 事件=="幽冥鬼看着文秀的信物,流下了伤心的泪水" then
                xsjc2(数字id,23)
          end
    elseif 地图编号==1139 and 名称=="接引小妖" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1116 and 名称=="传送蟹将" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1140 and 名称=="接引仙女" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1111 then
            if 名称=="接引仙女" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1001,470,253)
            elseif 名称=="守门天将" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1091,24,119)
            elseif 名称=="执法天兵" and 事件=="卧槽,这就开干了?" then
                  战斗准备类:创建战斗(数字id,110008,0)
            elseif 名称=="马真人" then
                    if 事件=="领取宠物修炼任务" then
                        if 玩家数据[数字id].角色:取任务(13)~=0 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="请先完成之前领取的任务。"})
                          return
                        else
                          发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="领取宠物修炼任务需要花费50万两银子，并且人物等级必须>=60级方可领取。",选项={"我要领取宠物修炼任务","我再考虑考虑"}})
                          return
                        end
                    elseif 事件=="我要领取宠物修炼任务" then
                            if 玩家数据[数字id].角色:取任务(13)~=0 then
                              发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="请先完成之前领取的任务。"})
                              return
                            else
                              任务处理类:添加宠修任务(数字id)
                            end
                    elseif 事件=="跳过宠物修炼任务" then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="前90次任务均可以在我这里选择跳过本环任务，直接进入下一环任务。但将无法获得修炼经验奖励，且需要扣除10万两银子和10点任务积分，只有当前任务积分≥50点时才可使用跳过功能，你需要我帮你跳过本轮任务吗？",选项={"请帮我跳过本环任务","我再考虑考虑"}})
                          return
                    elseif 事件=="请帮我跳过本环任务" then
                            if 玩家数据[数字id].角色:取任务(13)==0 then
                                发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="你有在我这里领取过任务吗？"})
                                return
                            else
                                  local 任务id=玩家数据[数字id].角色:取任务(13)
                                  if 玩家数据[数字id].角色.数据.银子<100000 then
                                        发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="你没有那么多的银子"})
                                        return
                                  elseif 任务数据[任务id].积分<50 then
                                       发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="你本次的任务积分尚未达到50点，无法使用跳过功能。"})
                                       return
                                  elseif 任务数据[任务id].次数>=90 then
                                        发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="我这里只能跳过前90环任务。"})
                                        return
                                  else
                                        -- 玩家数据[数字id].角色:取消任务(任务id)

                                        任务数据[任务id].次数=任务数据[任务id].次数+1
                                        任务数据[任务id].积分=任务数据[任务id].积分-50
                                        玩家数据[数字id].角色:扣除银子(100000,"跳过宠物修炼任务",1)
                                        任务处理类:添加宠修任务(数字id)
                                        local 任务id=玩家数据[数字id].角色:取任务(13)
                                        玩家数据[数字id].角色:取消任务(任务id)
                                        任务数据[任务id]=nil
                                        -- 玩家数据[数字id].角色:取消任务(任务id)
                                        发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="你消耗了5点任务积分和10万两银子跳过了本环任务。"})
                                        return
                                  end
                              end
                      elseif 事件=="取消宠物修炼任务" then
                              if 玩家数据[数字id].角色:取任务(13)==0 then
                                      发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="你有在我这里领取过任务吗？"})
                                      return
                              else
                                    local 任务id=玩家数据[数字id].角色:取任务(13)
                                    玩家数据[数字id].角色:取消任务(任务id)
                                    任务数据[任务id]=nil
                                    发送数据(玩家数据[数字id].连接id,1501,{名称="马真人",模型="男人_道士",对话="已帮你取消了宠物修炼任务。"})
                                    return
                              end
                      end
            elseif 名称=="千里眼" and 事件=="好的，我这就去" and 玩家数据[数字id].角色:取任务(307)~=0 then
                    local 任务id=玩家数据[数字id].角色:取任务(307)
                    if 任务数据[任务id].分类==3 then
                        任务数据[任务id].分类=4
                    elseif 任务数据[任务id].分类==5 then
                        任务数据[任务id].分类=6
                    end
                    玩家数据[数字id].角色:刷新任务跟踪()
            elseif 名称=="大力神灵" and 事件=="看我不好好的收拾你！" and 玩家数据[数字id].角色:取任务(307)~=0 then
                      战斗准备类:创建战斗(数字id,100040,玩家数据[数字id].角色:取任务(307))
            elseif 名称=="水兵统领" then
                     if 事件=="触发剧情" then
                          local 对话数据={}
                          对话数据.模型="男人_将军"
                          对话数据.名称="水兵统领"
                          对话数据.对话="自从天蓬元帅被玉帝炒鱿鱼,水兵统领这个位子就是我的了,说起来还要多谢那个天英星呢哈哈哈~~"
                          副本处理类:发送全队对话信息(数字id,对话数据)
                          对话数据.模型="男人_将军"
                          对话数据.名称="水兵统领"
                          对话数据.对话="他帮助天蓬元帅给嫦娥递情书,惹恼了玉帝,被罢了官职,贬去下界转世为凡人,现在大概不知道轮到多少世,还在#G判官#那里报到,等着投胎呢!"
                          对话数据.选项={"好的我这就动身"}
                          副本处理类:发送全队对话信息(数字id,对话数据)
                      elseif 事件=="好的我这就动身" and  玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 10  then
                                任务数据[玩家数据[数字id].角色:取任务(996)].进程=11
                                玩家数据[数字id].角色:刷新任务跟踪()
                     end
            end
    elseif 地图编号==1123 and 名称=="判官" and 事件=="好的" and 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 11 then
            任务数据[玩家数据[数字id].角色:取任务(996)].进程=12
            玩家数据[数字id].角色:刷新任务跟踪()
    elseif 地图编号==6035 and 名称=="秋风的回忆"  then
            if 事件=="接桃园任务" then
                任务处理类:设置初入桃源村(数字id)
            elseif 事件=="接建邺任务" then
                任务处理类:设置商人的鬼魂(数字id)
            elseif 事件=="接玄奘身世" then
                任务处理类:设置玄奘的身世(数字id)
            elseif 事件=="接取新手任务" then
                任务处理类:设置新手指引任务(数字id)
            elseif 事件=="取消桃园任务" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
            elseif 事件=="取消建邺任务" then
                local 任务id=玩家数据[数字id].角色:取任务(998)
                玩家数据[数字id].角色:取消任务(任务id)
            elseif 事件=="取消玄奘身世" then
                local 任务id=玩家数据[数字id].角色:取任务(997)
                玩家数据[数字id].角色:取消任务(任务id)
            elseif 事件=="取消新手指引" then
                local 任务id=玩家数据[数字id].角色:取任务(402)
                玩家数据[数字id].角色:取消任务(任务id)
            end
    elseif 地图编号==1114 and 名称=="吴刚" then
            if 事件=="什么是化境！" then
                添加最后对话(数字id,"角色等级达到135以后可以在我这里进入幻镜，成功后即可飞升成仙。飞升后的修炼等级上限将提升为25级，人物等级上限提升至155级，人物及召唤兽等级下降15级，人物将额外获得100点可分配属性点。进入幻镜前，必须先将所有师门技能等级提升至135级。")
            elseif 事件=="我想入此境，请上仙指点一二" then
                    添加最后对话(数字id,"您将进入飞升战斗，该战斗将连续进行三场，三场战斗必须全部获胜。你是否要切入战斗？",{"切入飞升战斗","让我想一会"})
            elseif 事件=="切入飞升战斗" then
                    if 玩家数据[数字id].角色.数据.飞升 then
                        添加最后对话(数字id,"你已经飞升过了，请勿胡说八道。")
                        return
                    end
                    if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长==false then
                        添加最后对话(数字id,"你不是队长，无法进行此操作。")
                        return
                    end
                    if 玩家数据[数字id].角色.数据.等级<135  then
                        添加最后对话(数字id,"你的等级尚未达到135级，还是先去努力升级吧。")
                        return
                    end
                    for n=1,#玩家数据[数字id].角色.数据.师门技能 do
                        if 玩家数据[数字id].角色.数据.师门技能[n].等级<135 then
                            添加最后对话(数字id,string.format("你的门派技能#Y/%s#W尚未达到135级，无法进行飞升挑战。",玩家数据[数字id].角色.数据.师门技能[n].名称))
                            return
                        end
                    end
                    local 临时任务id = os.time()+数字id
                    战斗准备类:创建战斗(数字id,100046,临时任务id)
            end
    elseif 地图编号==1150 then
            if 名称=="传送天将" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1001,470,253)
            elseif 名称=="二郎神" then
                    local 门派类型="凌波城"
                    if 事件=="请收我为徒" then
                        local 任务id=玩家数据[数字id].角色:取任务(999)
                        玩家数据[数字id].角色:取消任务(任务id)
                        任务处理类:设置商人的鬼魂(数字id)
                        玩家数据[数字id].角色:加入门派(数字id,门派类型)
                    elseif 事件=="交谈" then
                      -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
                    elseif 事件=="师门任务" then
                      玩家数据[数字id].角色:门派任务(数字id,门派类型)
                    elseif 事件=="学习技能" then
                      发送数据(id,31,玩家数据[数字id].角色:取总数据())
                      发送数据(id,32)
                    end
            end
    -- elseif 地图编号==1204 then
    --     if 名称=="丹青生" then
    --     if 事件=="准备好了，你就等我们的好消息吧" then
    --       发送数据(id,6116)
    --     elseif 事件=="我来一观《天地七元图》" then
    --       设置任务630(数字id)
    --     elseif 事件=="请送我进去" then
    --       任务处理类:副本传送(数字id,4)
    --     end
    -- end
    elseif 地图编号==1146 then
            if 事件=="是的我要去" and 名称=="传送道童" then
                地图处理类:npc传送(数字id,1001,470,253)
            elseif 事件=="触发剧情" then
                  local 对话数据={}
                  对话数据.模型=玩家数据[数字id].角色.数据.造型
                  对话数据.名称=玩家数据[数字id].角色.数据.名称
                  对话数据.对话="请问清风道童,怎么才能找到天心星呢?"
                  副本处理类:发送全队对话信息(数字id,对话数据)
                  对话数据.模型="男人_道童"
                  对话数据.名称="清风"
                  对话数据.对话="天心星在我这里,但是我不能白给你,你去帮我找来两样东西,我就把它给你!\n第一件就是普陀山的#G仙露#,你可以找#Y青莲仙女#问问!"
                  对话数据.选项={"好的我这就动身"}
                  副本处理类:发送全队对话信息(数字id,对话数据)
            elseif 事件=="好的我这就动身" and 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 5 then
                        任务数据[玩家数据[数字id].角色:取任务(996)].进程=6
                        玩家数据[数字id].角色:刷新任务跟踪()
            elseif 事件=="触发后续" then
                    local 对话数据={}
                    对话数据.模型=玩家数据[数字id].角色.数据.造型
                    对话数据.名称=玩家数据[数字id].角色.数据.名称
                    对话数据.对话="你要的仙露我给你带来了!"
                    副本处理类:发送全队对话信息(数字id,对话数据)
                    对话数据.模型="男人_道童"
                    对话数据.名称="清风"
                    对话数据.对话="不错不错,那么第二件东西就是#Y金击子#\n听说在#G长寿郊外的路人甲#身上!"
                    对话数据.选项={"好的我去找"}
                    副本处理类:发送全队对话信息(数字id,对话数据)
            elseif 事件=="好的我去找" and 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 8 then
                        任务数据[玩家数据[数字id].角色:取任务(996)].进程=9
                        玩家数据[数字id].角色:刷新任务跟踪()
            end
    elseif 地图编号 == 2008 then
            if 名称 == "刑天" and 玩家数据[数字id].角色.数据.月卡 and 玩家数据[数字id].角色.数据.月卡.开通 then
                local 门派类型="九黎城"
                if 事件=="请收我为徒" then
                      local 任务id=玩家数据[数字id].角色:取任务(999)
                      玩家数据[数字id].角色:取消任务(任务id)
                      任务处理类:设置商人的鬼魂(数字id)
                      玩家数据[数字id].角色:加入门派(数字id,门派类型)
                elseif 事件=="交谈" then
                  -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
                elseif 事件=="师门任务" then
                    玩家数据[数字id].角色:门派任务(数字id,门派类型)
                elseif 事件=="学习技能" then
                    发送数据(id,31,玩家数据[数字id].角色:取总数据())
                    发送数据(id,32)
                end
            elseif 名称 == "风祖飞廉" and 事件 == "有劳风祖" then
                    地图处理类:npc传送(数字id,1001,470,250)
            elseif 名称 == "食铁兽"  and 玩家数据[数字id].角色.数据.月卡 and 玩家数据[数字id].角色.数据.月卡.开通 then
                    if 事件 == "铸斧还原武器" or 事件 == "武器转换铸斧(乾)" or 事件 == "武器转换铸斧(坤)" then
                          玩家数据[数字id].给予数据={类型=1,id=0,事件=事件}
                          发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="食铁兽",类型="NPC",等级="无"})
                    elseif 事件 == "我要变成影精灵" then
                              if 玩家数据[数字id].角色:判断是否穿戴装备() and (玩家数据[数字id].角色.数据.门派=="" or 玩家数据[数字id].角色.数据.门派=="无"
                                 or  玩家数据[数字id].角色.数据.门派=="无门派" or 玩家数据[数字id].角色.数据.门派=="九黎城")  then
                                 if 玩家数据[数字id].角色.数据.原始模型  then
                                      常规提示(数字id, "#Y请换回原始模型后在操作！")
                                 else
                                      玩家数据[数字id].角色.数据.原始模型 = {模型=玩家数据[数字id].角色.数据.模型,性别=玩家数据[数字id].角色.数据.性别,种族=玩家数据[数字id].角色.数据.种族}
                                      玩家数据[数字id].角色.数据.模型="影精灵"
                                      玩家数据[数字id].角色.数据.造型="影精灵"
                                      玩家数据[数字id].角色.数据.种族="魔"
                                      玩家数据[数字id].角色.数据.性别="女"
                                      玩家数据[数字id].角色:刷新信息("2")
                                      常规提示(数字id, "#Y改变模型完成，请重新上线！")
                                  end
                              end
                    elseif 事件 == "我要换回原型" and 玩家数据[数字id].角色.数据.原始模型 then
                            if 玩家数据[数字id].角色:判断是否穿戴装备() then
                                玩家数据[数字id].角色.数据.模型=玩家数据[数字id].角色.数据.原始模型.模型
                                玩家数据[数字id].角色.数据.造型=玩家数据[数字id].角色.数据.原始模型.模型
                                玩家数据[数字id].角色.数据.种族=玩家数据[数字id].角色.数据.原始模型.种族
                                玩家数据[数字id].角色.数据.性别=玩家数据[数字id].角色.数据.原始模型.性别
                                玩家数据[数字id].角色.数据.原始模型=nil
                                玩家数据[数字id].角色:刷新信息("2")
                                常规提示(数字id, "#Y改变模型完成，请重新上线！")
                            end
                    end



            end



    elseif 地图编号==1054 and 名称=="程咬金" then
            local 门派类型="大唐官府"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
                 -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                  玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
                    --local 临时数据=玩家数据[数字id].角色:取总数据()
                    发送数据(id,31,玩家数据[数字id].角色:取总数据())
                    发送数据(id,32)
            end
    elseif 地图编号==1043 and 名称=="空度禅师" then
            local 门派类型="化生寺"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                 玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
              玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              发送数据(id,31,玩家数据[数字id].角色:取总数据())
              发送数据(id,32)
            end
    elseif 地图编号==1137 and 名称=="菩提老祖" then
            local 门派类型="方寸山"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
              玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
              发送数据(id,31,玩家数据[数字id].角色:取总数据())
              发送数据(id,32)
            end
    elseif 地图编号==1143 and 名称=="孙婆婆" then
            local 门派类型="女儿村"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
              发送数据(id,31,玩家数据[数字id].角色:取总数据())
              发送数据(id,32)
            end
    elseif 地图编号==1154 and 名称=="巫奎虎" then
            local 门派类型="神木林"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
              发送数据(id,31,玩家数据[数字id].角色:取总数据())
              发送数据(id,32)
            end
    elseif 地图编号==1144 and 名称=="白晶晶" then
            local 门派类型="盘丝洞"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
               发送数据(id,31,玩家数据[数字id].角色:取总数据())
               发送数据(id,32)
            end
    elseif 地图编号==1134 and 名称=="大大王" then
            local 门派类型="狮驼岭"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            elseif 事件=="好的，我这就去" then
                local 任务id=玩家数据[数字id].角色:取任务(307)
                任务数据[任务id].分类=11
                玩家数据[数字id].角色:刷新任务跟踪()
            end
    elseif 地图编号==1145 and 名称=="牛魔王" then
            local 门派类型="魔王寨"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            end
    elseif 地图编号==1124 and 名称=="地藏王" then
            local 门派类型="阴曹地府"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
              --local 临时数据=玩家数据[数字id].角色:取总数据()
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            end
    elseif 地图编号==1125 then
            if 名称=="白无常" and 事件=="请送我过去" then
                   地图处理类:npc传送(数字id,1001,359,34)
            elseif 名称=="黑无常" and 事件=="我们来帮你" then
                   任务处理类:设置鬼王任务(数字id)
            end
    elseif 地图编号==1156 and 名称=="地涌夫人" then
            local 门派类型="无底洞"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
                --local 临时数据=玩家数据[数字id].角色:取总数据()
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            end
    elseif 地图编号==1117 and 名称=="东海龙王" then
            local 门派类型="龙宫"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
                --local 临时数据=玩家数据[数字id].角色:取总数据()
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            end
    elseif 地图编号==1141 then
            if 名称=="观音姐姐" then
                local 门派类型="普陀山"
                if 事件=="请收我为徒" then
                    local 任务id=玩家数据[数字id].角色:取任务(999)
                    玩家数据[数字id].角色:取消任务(任务id)
                    任务处理类:设置商人的鬼魂(数字id)
                  玩家数据[数字id].角色:加入门派(数字id,门派类型)
                elseif 事件=="交谈" then
                  -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
                elseif 事件=="师门任务" then
                  玩家数据[数字id].角色:门派任务(数字id,门派类型)
                elseif 事件=="学习技能" then
                  --local 临时数据=玩家数据[数字id].角色:取总数据()
                  发送数据(id,31,玩家数据[数字id].角色:取总数据())
                  发送数据(id,32)
                end
            elseif 名称=="青莲仙女" then
                    if 事件=="多谢美女姐姐" then
                        玩家数据[数字id].道具:给予道具(数字id,"九转回魂丹",1,10,nil,"专用")
                        常规提示(数字id,"你获得了一个#G九转回魂丹！")
                        xsjc2(数字id,19)
                    elseif 事件=="触发剧情" then
                            local 对话数据={}
                            对话数据.模型=玩家数据[数字id].角色.数据.造型
                            对话数据.名称=玩家数据[数字id].角色.数据.名称
                            对话数据.对话="美女姐姐,好久不见,在下求普陀#G仙露#一用"
                            副本处理类:发送全队对话信息(数字id,对话数据)
                            对话数据.模型="普陀_接引仙女"
                            对话数据.名称="青莲仙女"
                            对话数据.对话="可以是可以,但是你得帮我找一样东西过来,我们进行交换!帮我找找#G火凤之睛#吧"
                            对话数据.选项={"好的我这就动身"}
                            副本处理类:发送全队对话信息(数字id,对话数据)
                    elseif 事件=="好的我这就动身" and 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 6  then
                              任务数据[玩家数据[数字id].角色:取任务(996)].进程=7
                              玩家数据[数字id].角色:刷新任务跟踪()
                    elseif 事件=="上交火凤之睛" then
                          玩家数据[数字id].给予数据={类型=1,id=0,事件="上交火凤之睛"}
                          发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="青莲仙女",类型="NPC",等级="无"})
                    end
            end
    elseif 地图编号==1112 then
            if 名称=="李靖" then
                local 门派类型="天宫"
                if 事件=="请收我为徒" then
                    local 任务id=玩家数据[数字id].角色:取任务(999)
                    玩家数据[数字id].角色:取消任务(任务id)
                    任务处理类:设置商人的鬼魂(数字id)
                    玩家数据[数字id].角色:加入门派(数字id,门派类型)
                elseif 事件=="交谈" then
                  -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
                elseif 事件=="师门任务" then
                         玩家数据[数字id].角色:门派任务(数字id,门派类型)
                elseif 事件=="学习技能" then
                        发送数据(id,31,玩家数据[数字id].角色:取总数据())
                        发送数据(id,32)
                end
            elseif 名称=="杨戬" and  事件=="开启剧情战斗"  then
                  战斗准备类:创建战斗(数字id,100258,0)
            elseif 名称=="玉皇大帝" then
            end
    elseif 地图编号==1147 and 名称=="镇元子" then
            local 门派类型="五庄观"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                  玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
                  --local 临时数据=玩家数据[数字id].角色:取总数据()
                  发送数据(id,31,玩家数据[数字id].角色:取总数据())
                  发送数据(id,32)
            end
    elseif 地图编号==1252 and 名称=="天女魃" then
              local 门派类型="女魃墓"
              if 事件=="请收我为徒" then
                  local 任务id=玩家数据[数字id].角色:取任务(999)
                  玩家数据[数字id].角色:取消任务(任务id)
                  任务处理类:设置商人的鬼魂(数字id)
                  玩家数据[数字id].角色:加入门派(数字id,门派类型)
              elseif 事件=="交谈" then
                -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
              elseif 事件=="师门任务" then
                    玩家数据[数字id].角色:门派任务(数字id,门派类型)
              elseif 事件=="学习技能" then
                    发送数据(id,31,玩家数据[数字id].角色:取总数据())
                    发送数据(id,32)
              end
    elseif 地图编号==1253 and 名称=="小夫子" then
            local 门派类型="天机城"
            if 事件=="请收我为徒" then
                local 任务id=玩家数据[数字id].角色:取任务(999)
                玩家数据[数字id].角色:取消任务(任务id)
                任务处理类:设置商人的鬼魂(数字id)
                玩家数据[数字id].角色:加入门派(数字id,门派类型)
            elseif 事件=="交谈" then
              -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
            elseif 事件=="师门任务" then
                  玩家数据[数字id].角色:门派任务(数字id,门派类型)
            elseif 事件=="学习技能" then
                发送数据(id,31,玩家数据[数字id].角色:取总数据())
                发送数据(id,32)
            end
    elseif 地图编号==1251 then
              if 名称=="齐天大圣" then
                    local 门派类型="花果山"
                    if 事件=="请收我为徒" then
                        local 任务id=玩家数据[数字id].角色:取任务(999)
                        玩家数据[数字id].角色:取消任务(任务id)
                        任务处理类:设置商人的鬼魂(数字id)
                      玩家数据[数字id].角色:加入门派(数字id,门派类型)
                    elseif 事件=="交谈" then
                      -- 玩家数据[数字id].角色:门派交谈(数字id,门派类型)
                    elseif 事件=="师门任务" then
                      玩家数据[数字id].角色:门派任务(数字id,门派类型)
                    elseif 事件=="学习技能" then
                      发送数据(id,31,玩家数据[数字id].角色:取总数据())
                      发送数据(id,32)
                    end
              elseif 名称=="传送侍卫" and 事件=="是的我要去" then
                      地图处理类:npc传送(数字id,1001,470,253)
              end
    elseif 地图编号==1129 then
    elseif 地图编号==1092 then
            if 名称=="船夫" and 事件=="是的我要去" then
                地图处理类:npc传送(数字id,1506,70,93)
            elseif 名称=="渔夫" then
                    if 事件=="花费100万两购买鱼竿" then
                        if 玩家数据[数字id].角色.数据.钓鱼积分 == nil then
                          玩家数据[数字id].角色.数据.钓鱼积分 = 0
                        end
                        local 价格 = 1000000
                        if 玩家数据[数字id].角色.数据.银子<价格 then
                          常规提示(数字id,"你个穷鬼,没有钱还想啊！")
                          return
                        else
                          玩家数据[数字id].角色:扣除银子(价格,"购买鱼竿",1)
                          玩家数据[数字id].道具:给予道具(数字id,"鱼竿",10)
                          常规提示(数字id,"#Y你花费100万两银子购买了10根鱼竿")
                        end
                    elseif 事件=="兑换海产" then
                         if 玩家数据[数字id].角色.数据.钓鱼积分 == nil then
                            玩家数据[数字id].角色.数据.钓鱼积分 = 0
                         end
                         玩家数据[数字id].商品列表=商店处理类.商品列表[26]
                        发送数据(id,9.1,{商品=商店处理类.商品列表[26],钓鱼积分=玩家数据[数字id].角色.数据.钓鱼积分})
                    end
            elseif 名称=="傲来珍品商人" and 事件=="购买" then  --建邺
                    玩家数据[数字id].商品列表=商店处理类.商品列表[23]
                    发送数据(id,9,{商品=商店处理类.商品列表[23],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="云游道人" and 事件=="购买"  then  --建邺
                    玩家数据[数字id].商品列表=商店处理类.商品列表[24]
                    发送数据(id,9,{商品=商店处理类.商品列表[24],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="红毛猿" then
                    if 事件=="我要开启齐天大圣" then
                         任务处理类:开启齐天大圣副本(数字id)
                    elseif 事件=="我这就前往" then
                         任务处理类:副本传送(数字id,6)
                    end
            elseif 名称=="报名官" then
                      if 事件=="我要报名参赛" then
                          if 活动次数查询(数字id,"游泳比赛")==false then
                              return
                          end
                          任务处理类:添加游泳任务(数字id)
                      elseif 事件 == "我要取消任务" then
                            if 玩家数据[数字id].角色:取任务(109)==0 then
                                发送数据(玩家数据[数字id].连接id,1501,{名称="报名官",模型="雨师",对话="你有在我这里领取任务吗？"})
                            else
                                local 队伍id=玩家数据[数字id].队伍
                                for n=1,#队伍数据[队伍id].成员数据 do
                                  玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(109))
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="报名官",模型="雨师",对话="已经成功帮你取消了任务。"})
                                end
                            end
                      end
            elseif 名称=="金毛猿" and 事件=="请送我进去" then
                    if 迷宫数据.开关==false then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="金毛猿",模型="马猴",对话="当前不是活动时间。"})
                        return
                    elseif 玩家数据[数字id].角色.数据.等级<30 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="金毛猿",模型="马猴",对话="你当前等级小于30级无法参加"})
                          return
                    else
                        local xy=地图处理类.地图坐标[1601]:取随机点()
                        地图处理类:npc传送(数字id,1601,xy.x,xy.y)
                    end
            elseif 名称=="偷偷怪" then
            elseif 名称=="仙岛引路人" and 事件=="是的我要去"  then
                    地图处理类:npc传送(数字id,1207,10,109)
            elseif 名称=="蝴蝶妹妹" and 事件=="我要学习宝石工艺"  then
                    玩家数据[数字id].角色:学习剧情技能(数字id,"宝石工艺",2,7)
            elseif 名称=="驿站老板" and 事件=="是的我要去" then
                    地图处理类:npc传送(数字id,1001,363,198)
            elseif 名称=="傲来商人" and 事件=="购买商品" then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[31]
                    self.初始金额 = 0
                    for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                        if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                            self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                        end
                    end
                    发送数据(id,70,{商品=商店处理类.商品列表[31],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
            elseif 名称=="傲来货商" and 事件=="购买商品" then
                      玩家数据[数字id].商品列表=商店处理类.商品列表[32]
                      self.初始金额 = 0
                      for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                          if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                              self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                          end
                      end
                      发送数据(id,70,{商品=商店处理类.商品列表[32],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
            elseif 名称=="八卦炼丹炉" then
                      if 事件=="买点炼丹材料" then
                          if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.炼丹材料.货币类型,自定义数据.炼丹材料.数量,"炼丹材料",1) then
                              玩家数据[数字id].道具:给予道具(数字id,"翡翠琵琶",1)
                              常规提示(数字id,"你获得了一个翡翠琵琶！")
                          else
                              常规提示(数字id,"你个穷鬼,没有钱还想买炼丹材料啊！")
                              return
                          end
                          道具刷新(数字id)
                      elseif 事件=="我要炼丹" then
                              炼丹查看[数字id] = 1
                              if 玩家数据[数字id].角色.数据.炼丹灵气 == nil then
                                玩家数据[数字id].角色.数据.炼丹灵气 = 0
                              end
                              if 炼丹炉.下注时间==nil then
                                 炼丹炉.下注时间 = 90
                              end
                              if 炼丹炉.转盘时间==nil then
                                 炼丹炉.转盘时间 = 30
                              end
                              if 炼丹炉.停止时间==nil then
                                 炼丹炉.停止时间 = 10
                              end
                              local 临时炼丹 = DeepCopy(炼丹炉)
                              临时炼丹.开奖控制=nil
                              发送数据(玩家数据[数字id].连接id,108,{时间=临时炼丹,数据=临时炼丹[数字id],灵气=玩家数据[数字id].角色.数据.炼丹灵气,物品数据=玩家数据[数字id].道具:索要道具1(数字id)})
                              发送数据(玩家数据[数字id].连接id,108.1,{数据=临时炼丹[数字id],灵气=玩家数据[数字id].角色.数据.炼丹灵气})
                      elseif 事件=="领取寄存丹药" then
                            if 商品存放[数字id] == nil then
                              常规提示(数字id,"这里没有存放你的奖励哟！")
                              return
                            else
                                local 奖励数据 = 商品存放[数字id]
                                if 奖励数据[1] > 0 then
                                  玩家数据[数字id].道具:给予道具(数字id,"金砂丹",奖励数据[1])
                                end
                                if 奖励数据[2] > 0 then
                                  玩家数据[数字id].道具:给予道具(数字id,"银砂丹",奖励数据[2])
                                end
                                if 奖励数据[3] > 0 then
                                  玩家数据[数字id].道具:给予道具(数字id,"铜砂丹",奖励数据[3])
                                end
                                常规提示(数字id,"#Y/恭喜你在炼丹中中得八卦位！")
                                广播消息({内容=string.format("#S(八卦炼丹)#Y恭喜:#G%s#Y在炼丹中获得了:#G%s颗#Y金丹 #G%s颗#Y银丹 #G%s颗#Y铜丹",玩家数据[数字id].角色.数据.名称,奖励数据[1],奖励数据[2],奖励数据[3]),频道="hd"})
                                商品存放[数字id] = nil
                            end
                      end
            end
    elseif 地图编号==1028 then
            if 名称=="酒店老板" then
                if 事件=="我要住店休息" then
                        ---local id组=取id组(数字id)
                        if 玩家数据[数字id].角色.数据.银子<500 then
                          常规提示(数字id,"500两银子都没有还想住店？滚回城外的乱葬岗去！")
                          return
                        else
                            玩家数据[数字id].角色:扣除银子(500,"酒店休息",1)
                            玩家数据[数字id].角色.数据.活力=math.floor(玩家数据[数字id].角色.数据.活力*0.9)
                            if 玩家数据[数字id].角色.数据.活力<=0 then
                              玩家数据[数字id].角色.数据.活力=0
                            end
                            玩家数据[数字id].角色.数据.气血上限=玩家数据[数字id].角色.数据.最大气血
                            玩家数据[数字id].角色.数据.气血=玩家数据[数字id].角色.数据.最大气血
                            玩家数据[数字id].角色.数据.魔法=玩家数据[数字id].角色.数据.最大魔法
                            体活刷新(数字id)
                            发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
                            发送数据(玩家数据[数字id].连接id,1501,{名称="酒店老板",模型="男人_酒店老板",对话="收您500两银子，已经帮你恢复至最佳状态。欢迎下次再来。"})
                            return
                         end
                elseif 事件=="我要制作仙露丸子" then   -----------------
                        发送数据(id,160,{体力=玩家数据[数字id].角色.数据.体力,道具=玩家数据[数字id].道具:索要道具2(数字id)})
                end
            elseif 名称=="店小二" and 事件=="听听无妨(消耗2000两银子)" then
                    if 玩家数据[数字id].角色.数据.银子<2000 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="店小二",模型="男人_店小二",对话="2000两银子都没有，还想在我这里打听消息？"})
                        return
                    elseif  玩家数据[数字id].队伍~=0  then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="店小二",模型="男人_店小二",对话="少侠请不要组队，宝图这种东西少侠还需要找人与您一起分享吗？"})
                            return
                    elseif 玩家数据[数字id].角色:取任务(4)~=0 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="店小二",模型="男人_店小二",对话="不是已经告诉你强盗的消息了吗？赶紧去找他，晚了人家可就跑了。"})
                          return
                    else
                        玩家数据[数字id].角色:扣除银子(2000,"宝图任务-领取",1)
                        任务处理类:添加宝图任务(数字id)
                        return
                    end
            end
    elseif 地图编号==1101 and 名称=="杜天" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[10]
            发送数据(id,9,{商品=商店处理类.商品列表[10],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1095 and 名称=="牛师傅" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[11]
            发送数据(id,9,{商品=商店处理类.商品列表[11],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1104 and 名称=="沈妙衣" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[12]
            发送数据(id,9,{商品=商店处理类.商品列表[12],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1083 and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[15]
            发送数据(id,9,{商品=商店处理类.商品列表[15],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1085 and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[16]
            发送数据(id,9,{商品=商店处理类.商品列表[16],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1105 and 名称=="杂货店老板" and 事件=="购买"then
            玩家数据[数字id].商品列表=商店处理类.商品列表[13]
            发送数据(id,9,{商品=商店处理类.商品列表[13],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1093 and 名称=="王福来" and 事件=="购买" then
            玩家数据[数字id].商品列表=商店处理类.商品列表[14]
            发送数据(id,9,{商品=商店处理类.商品列表[14],银子=玩家数据[数字id].角色.数据.银子})
    elseif 地图编号==1202 then --无名鬼蜮
            if 名称=="小宝箱" then
            end
    elseif 地图编号==1208 then
            if 名称=="药店伙计" and 事件=="购买" then  --建邺
                      玩家数据[数字id].商品列表=商店处理类.商品列表[17]
                      发送数据(id,9,{商品=商店处理类.商品列表[17],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="申太公" and 事件=="我要进入仙源洞天" then
                      地图处理类:npc传送(数字id,1216,213,55)
            elseif 名称=="土地公公" and 事件=="好的，我这就去" and 玩家数据[数字id].角色:取任务(307)~=0 then
                      local 任务id=玩家数据[数字id].角色:取任务(307)
                      任务数据[任务id].分类=7
                      玩家数据[数字id].角色:刷新任务跟踪()
            elseif 名称=="妖魔亲信" and 玩家数据[数字id].角色:取任务(307)~=0  then
                      local 任务id=玩家数据[数字id].角色:取任务(307)
                      if 事件=="妖怪，终于让我逮到你了，速速带我去找你老大，饶你不死！" then
                           战斗准备类:创建战斗(数字id,100041,任务id)
                      elseif 事件=="速速送我过去" then
                          任务数据[任务id].分类=8
                          地图处理类:npc传送(数字id,1215,105,83)
                      end
            end
    elseif 地图编号==1215 and 名称=="蜃妖元神" and 事件=="妖孽，速速放了天马" and 玩家数据[数字id].角色:取任务(307)~=0 then
              local 任务id=玩家数据[数字id].角色:取任务(307)
              战斗准备类:创建战斗(数字id,100042,任务id)
    elseif 地图编号==1216 then
            if 名称=="召唤师" and 事件=="我来给召唤兽换进阶造型" then
                    玩家数据[数字id].召唤兽:进阶造型处理(数字id)
            elseif 名称=="百兽王" then
                    if 事件=="确定" then
                          任务处理类:开启坐骑任务(数字id)
                    elseif 事件=="取消坐骑任务" then
                          if 玩家数据[数字id].角色:取任务(307)~=0 then
                              local 任务id=玩家数据[数字id].角色:取任务(307)
                              玩家数据[数字id].角色:取消任务(任务id)
                          else
                              常规提示(数字id,"#Y/你都没任务取消个P啊~")
                          end
                    end
            elseif 名称=="仙缘染坊主" then
                    -- if 事件=="给坐骑染色" then
                    --       if 玩家数据[数字id].角色.数据.坐骑==nil then
                    --           发送数据(玩家数据[数字id].连接id,1501,{名称="仙缘染坊主",模型="女人_赵姨娘",对话="少侠是来寻我开心的么？你要染色的坐骑呢！"})
                    --           return
                    --       elseif 玩家数据[数字id].角色.数据.坐骑.祥瑞 then
                    --              发送数据(玩家数据[数字id].连接id,1501,{名称="仙缘染坊主",模型="女人_赵姨娘",对话="少侠是来寻我开心的么？祥瑞无法染色！"})
                    --              return
                    --       end
                    --       发送数据(玩家数据[数字id].连接id,80,玩家数据[数字id].角色.数据.坐骑)
                    -- elseif 事件=="给坐骑饰品染色" then
                    --       if 玩家数据[数字id].角色.数据.坐骑==nil then
                    --           发送数据(玩家数据[数字id].连接id,1501,{名称="仙缘染坊主",模型="女人_赵姨娘",对话="少侠是来寻我开心的么？你要染色的坐骑呢！"})
                    --           return
                    --       elseif 玩家数据[数字id].角色.数据.坐骑.祥瑞 then
                    --              发送数据(玩家数据[数字id].连接id,1501,{名称="仙缘染坊主",模型="女人_赵姨娘",对话="少侠是来寻我开心的么？祥瑞无法染色！"})
                    --              return
                    --       end
                    --       if 玩家数据[数字id].角色.数据.坐骑.饰品==nil then
                    --           发送数据(玩家数据[数字id].连接id,1501,{名称="仙缘染坊主",模型="女人_赵姨娘",对话="少侠是来寻我开心的么？你要染色的坐骑饰品呢！"})
                    --           return
                    --       end
                    --       发送数据(玩家数据[数字id].连接id,81,玩家数据[数字id].角色.数据.坐骑)
                    -- end
            end
    elseif 地图编号==1174 then
          if 名称=="地遁鬼" and 事件=="是的我要去" then
                    地图处理类:npc传送(数字id,1091,73,103)
          elseif 名称=="女娲神迹传送人" and 事件=="是的我要去" then
                    地图处理类:npc传送(数字id,1201,47,105)
          elseif 名称=="北俱商人" and 事件=="购买商品" then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[35]
                    self.初始金额 = 0
                    for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                        if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                            self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                        end
                    end
                    发送数据(id,70,{商品=商店处理类.商品列表[35],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
          elseif 名称=="北俱货商" and 事件=="购买商品" then

                    玩家数据[数字id].商品列表=商店处理类.商品列表[36]
                    self.初始金额 = 0
                    for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                        if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                            self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                        end
                    end
                    发送数据(id,70,{商品=商店处理类.商品列表[36],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
          elseif 名称=="驿站老板" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1001,362,198)

          end
    elseif 地图编号==1091 then
            if 名称=="驿站老板" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1174,193,155)
            elseif 名称=="传送天将" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1111,246,158)
            elseif 名称=="西牛贺洲土地" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1173,33,96)
            elseif 名称=="鬼谷道人" then
                    if 事件=="领取任务" then
                          任务处理类:添加降妖除魔任务(数字id)
                    elseif 事件=="取消任务" then
                            if 玩家数据[数字id].队伍==0 or 玩家数据[数字id].队长==false  then
                                  常规提示(数字id,"#Y/取消任务必须组队取消且由队长取消")
                                  return
                            end
                            if 降妖伏魔[数字id]==nil then
                                 降妖伏魔[数字id]=1
                            end
                            降妖伏魔[数字id]=1
                            if 取队伍任务(玩家数据[数字id].队伍,200) then
                                local 队伍id=玩家数据[数字id].队伍
                                for n=1,#队伍数据[队伍id].成员数据 do
                                      if 任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(200)] then
                                           地图处理类:删除单位(任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(200)].地图编号,任务数据[玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(200)].编号)
                                      end
                                      玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(200))
                                     常规提示(队伍数据[队伍id].成员数据[n],"#Y/已经取消任务,同时任务总数清0")
                                end
                            else
                                 常规提示(数字id,"#Y/对不起!你都没接任务,何来取消?")
                            end
                    end
              elseif 名称=="路人甲" and 事件=="不给,打你啊!" then
                       战斗准备类:创建战斗(数字id,100257,0)
              end
    elseif 地图编号==1110 then
            if 名称=="驿站老板" and 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1001,362,198)
            elseif 名称=="白琉璃" and 事件=="得罪了,小琉璃" then
                  战斗准备类:创建战斗(数字id,110009,0)
            elseif 名称=="小二" and 事件=="我这就去准备吃的" then
                  xsjc2(数字id,36)
            elseif 名称=="婆婆" and 事件=="上交吃的" then
                  玩家数据[数字id].给予数据={类型=1,id=0,事件="上交吃的"}
                  发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="婆婆",类型="NPC",等级="无"})
            elseif 名称=="山神" then
                  if 事件=="好的,我这就去给你找美食" then
                        xsjc2(数字id,31)
                  elseif 事件=="上交特别的美味" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="上交特别的美味"}
                        发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="山神",类型="NPC",等级="无"})
                  end
            elseif 名称=="文秀" and 事件=="原来如此" then
                  xsjc2(数字id,22)
            elseif 名称=="虾兵" and 事件=="50万银子确实不贵,因为我TM要发展接下来的剧情,必须买!" then
                    if 取银子(数字id) < 500000 then
                        常规提示(数字id,"#Y/你当前的银子不够50万!")
                        return
                    end
                    玩家数据[数字id].角色:扣除银子(500000,"十二生肖",1)
                    xsjc2(数字id,30)
                    发送数据(玩家数据[数字id].连接id,1501,{名称="虾兵",模型="虾兵",对话="老板大气!虽然我不知道十八年前的事情\n不代表我们其他#S水族#的人不知道\n你去#G龙宫#问问,一定会有知情之人\n不过你现在修为太低,需要#P避水珠#才可以进入龙宫\n刚好附近的#R山神#携有此物,你去找找他吧!"})

            elseif 名称=="衙役" then
                    if 事件=="真是个老色批" and 玩家数据[数字id].角色:取任务(997) ~= 0 and  任务数据[玩家数据[数字id].角色:取任务(997)]~=nil then
                        xsjc2(数字id,26)
                    elseif 事件=="我要去江州府" then
                        if  玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)]~=nil and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 27 then
                             xsjc2(数字id,28)
                        end
                        地图处理类:npc传送(数字id,1168,20,23)
                    end
            elseif 名称=="大唐国境土地"  and 事件=="是的我要去"  then
                   地图处理类:npc传送(数字id,1150,8,92)
            elseif 名称=="普陀山接引仙女" and 事件=="是的我要去" then
                   地图处理类:npc传送(数字id,1140,85,64)
            end
    elseif 地图编号==1033 and 名称=="小桃红" and 事件=="溜了溜了" then
                xsjc2(数字id,27)
    elseif 地图编号==1168 then
            if 名称=="殷温娇" then
                if 事件=="请你继续说" then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="殷温娇",模型="陈妈妈",
                      对话="那时我担心怀着的#Y孩子有危险#,三月后孩子生下\n顺水放走,但恐难以识认,即咬破手指,写下#R血书#一纸\n将父母姓名,跟脚原由,备细开载\n又将此子左脚上一个小手指,用口咬下,以为记验\n我怀疑夫君当时是#G被水族所救#,少侠帮我去#S江下#水族问问",选项={"这剧情真是峰回路转,跌宕起伏"}})
                elseif 事件=="这剧情真是峰回路转,跌宕起伏" then
                      xsjc2(数字id,29)
                elseif 事件=="帮人帮到底,送佛送到西" then
                       xsjc2(数字id,35)
                elseif 事件=="好的,这种事情我愿意帮忙" then
                       xsjc2(数字id,38)
                end
            elseif 名称=="刘洪" and  事件=="开打开打" then
                     战斗准备类:创建战斗(数字id,110013,0)
            end
    elseif 地图编号==1113 and 名称=="金童子" then
            if 事件=="请帮我进行法宝合成" then
                  --   发送数据(玩家数据[数字id].连接id,83,玩家数据[数字id].道具:索要道具1(数字id))
                  local 找到内丹 = 0
                  if 玩家数据[数字id].角色.数据.完成法宝任务==nil then
                     玩家数据[数字id].角色.数据.完成法宝任务 = 0
                  end
                  if 玩家数据[数字id].角色.数据.完成法宝任务 >=1 then
                     常规提示(数字id,"少侠您已经完成了法宝合成,点我完成法宝任务吧#86")
                      return
                  end
                  if 取银子(数字id) < 2000000 then
                      常规提示(数字id,"你银子不够200万")
                      return
                  end
                  if 玩家数据[数字id].角色.数据.体力 < 100 then
                      常规提示(数字id,"你体力不够100点")
                      return
                  end
                  local 寻找格子={}
                  for n,v in pairs(玩家数据[数字id].角色.数据.道具) do
                        if n~=nil and 玩家数据[数字id].道具.数据[v]~=nil then
                            local 寻找名称=玩家数据[数字id].道具.数据[v].名称
                            if 寻找名称=="内丹" then
                               找到内丹 = v
                            elseif 寻找名称=="天蚕丝" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="玄龟板" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="阴沉木" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="麒麟血" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="金凤羽" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="补天石" then
                               寻找格子[#寻找格子+1]=v
                            elseif 寻找名称=="龙之筋" then
                               寻找格子[#寻找格子+1]=v
                            end
                        end
                    end
                    if 找到内丹~=0 then
                        if #寻找格子<4 then
                            常规提示(数字id,"合成材料不够")
                            return
                          else
                             玩家数据[数字id].角色:扣除银子(2000000,"法宝合成",1)
                             玩家数据[数字id].角色.数据.体力=玩家数据[数字id].角色.数据.体力-100
                             玩家数据[数字id].道具.数据[找到内丹]=nil
                             for n=1,4 do
                                玩家数据[数字id].道具.数据[寻找格子[n]]=nil
                              end
                            玩家数据[数字id].角色.数据.完成法宝任务 = 1
                            常规提示(数字id,"恭喜少侠完成了法宝合成，请在此点击我选择完成法宝任务领取法宝哦")
                            道具刷新(数字id)
                         end
                    else
                        常规提示(数字id,"缺少内丹材料，无法合成！")
                        return
                    end

            elseif 事件=="完成法宝任务" then
                  if 玩家数据[数字id].角色.数据.完成法宝任务 and 玩家数据[数字id].角色.数据.完成法宝任务>=1 then
                        local 名称=玩家数据[数字id].角色:完成法宝任务()
                        玩家数据[数字id].道具:给予法宝(数字id,名称)
                        玩家数据[数字id].角色.数据.完成法宝任务 = nil
                        广播消息({内容=string.format("#S(法宝任务)#R/%s#Y完成了法宝任务，因此获得了#G/%s#Y法宝",玩家数据[数字id].角色.数据.名称,名称),频道="xt"})
                  else
                       常规提示(数字id,"#Y/少侠你确定你完成法宝合成了吗#55 不要骗小童哦#17！")
                  end
            elseif 事件=="请告诉我如何获得内丹" then
                  任务处理类:开启法宝内丹任务(数字id)
            elseif 事件=="请帮我取消法宝任务" then
                   玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(308))
            elseif 事件=="取消法宝内丹任务" then
                   玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(309))
            elseif 事件=="开启法宝任务" then
                  if 玩家数据[数字id].角色:取任务(308)~=0 then
                     添加最后对话(数字id,"你已经有法宝任务了")
                  else
                     任务处理类:开启法宝任务(数字id)
                  end
            end
    elseif 地图编号==1173 then
          if 名称=="南瞻部洲土地" and 事件=="是的我要去" then
                地图处理类:npc传送(数字id,1091,85,157)
          elseif 名称=="强盗头子" and 事件=="学习技能" then
                玩家数据[数字id].角色:学习剧情技能(数字id,"妙手空空",3,10)
          elseif 名称=="白鹿精" and 事件=="不和我回去是吧?那只有把你打趴了!" then
                战斗准备类:创建战斗(数字id,110006,0)
          elseif 名称=="刘洪" and 事件=="这会儿后悔已经晚了" then
                战斗准备类:创建战斗(数字id,110014,0)
          elseif 名称=="天兵飞剑" then
                if 事件=="天命取经人你都敢打?尽管放马过来,这件事我管定了!" then
                      local 任务id=玩家数据[数字id].角色:取任务(997)
                      玩家数据[数字id].角色:取消任务(任务id)
                      任务处理类:设置大战心魔(数字id)
                elseif 事件=="开启剧情战斗" then
                      战斗准备类:创建战斗(数字id,110015,0)
                end

          elseif 名称=="卷帘大将" then
                if 事件=="触发剧情" then
                      local 对话数据={}
                      对话数据.模型=玩家数据[数字id].角色.数据.造型
                      对话数据.名称=玩家数据[数字id].角色.数据.名称
                      对话数据.对话="卷帘大将何必动怒,其实被贬也并非坏事,你可知你是五个天命取经人之一,若能皈依佛门,取得真经,不又是造福天下的一番事业?"
                      副本处理类:发送全队对话信息(数字id,对话数据)
                      对话数据.模型="沙僧"
                      对话数据.名称="卷帘大将"
                      对话数据.对话="呸!想我修道三千万年方为天将,南天门里灵霄殿上哪个不知我威名,你这厮连累了我不说,还想让我去当和尚,取什么鸟经,我不杀你难解我心头之恨!"
                      对话数据.选项={"开启剧情战斗"}
                      副本处理类:发送全队对话信息(数字id,对话数据)
                elseif 事件=="触发后续" then
                      local 对话数据={}
                      对话数据.模型="沙僧"
                      对话数据.名称="卷帘大将"
                      对话数据.对话="哈哈哈,你不认得我么,其实每个人的心中都有我的存在,有越多的仇恨和痛苦我就越强大,我就是人们心中的邪念,也是你们所说的心魔!"
                      副本处理类:发送全队对话信息(数字id,对话数据)
                      对话数据.模型="沙僧"
                      对话数据.名称="卷帘大将"
                      对话数据.对话="哈哈,卷帘大将心中积累了太多不甘和怨恨,他越是想要做回天神,越是压抑自己的本性,怨恨就积累得越多,我就是利用他这种力量,你想要他西去取经,造福天下,我偏要他在世为妖,祸害人间!"
                      对话数据.选项={"我不会让你得逞的,看招!"}
                      副本处理类:发送全队对话信息(数字id,对话数据)
                elseif 事件=="开启剧情战斗" then
                      战斗准备类:创建战斗(数字id,100255,0)
                elseif 事件=="我不会让你得逞的,看招!" then
                      战斗准备类:创建战斗(数字id,100256,0)
                elseif 事件=="开启剧情战斗2" then
                      战斗准备类:创建战斗(数字id,100260,0)
                end
          elseif 名称=="驿站老板" then
                if 事件=="送我过去" then
                      地图处理类:npc传送(数字id,1228,85,184)
                elseif 事件=="是的我要去" then
                      地图处理类:npc传送(数字id,1001,363,198)
                end
          end
    elseif 地图编号==1228 and 名称=="碗子山土地" and 事件=="是的我要去" then
               地图处理类:npc传送(数字id,1139,83,44)
    elseif 地图编号==1226 then
          if 名称=="驿站老板" and 事件=="我要前往长安城" then
                  地图处理类:npc传送(数字id,1001,363,198)
          elseif 名称=="土地公公" and 事件=="请送我进去" then
                  任务处理类:进入宝藏山(数字id)
          elseif 名称=="药店老板" and 事件=="购买" then
                  玩家数据[数字id].商品列表=商店处理类.商品列表[25]
                  发送数据(id,9,{商品=商店处理类.商品列表[25],银子=玩家数据[数字id].角色.数据.银子})
          end
    elseif 地图编号==5001 and 名称=="土地公公" and 事件=="送我过去" then
               地图处理类:npc传送(数字id,1226,115,15)
    elseif 地图编号==1235 and 名称=="驿站老板" and 事件=="是的我要去" then  -- 丝绸之路
               地图处理类:npc传送(数字id,1040,25,110)
    elseif 地图编号==1210 and 名称=="驿站老板" and 事件=="是的送我过去" then--麒麟山
               地图处理类:npc传送(数字id,1226,10,112)
    elseif 地图编号==1153 then
          if 名称=="玄奘" then
                if 事件=="没事,我帮你教训他" then
                    xsjc2(数字id,6)
                elseif 事件=="行,我帮你跑一趟吧" then
                    xsjc2(数字id,8)
                elseif 事件=="玄奘法师怎么有点疯疯癫癫的,问问旁边酒肉和尚情况" then
                    xsjc2(数字id,17)
                elseif 事件=="上交九转回魂丹" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="上交九转回魂丹"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="玄奘",类型="NPC",等级="无"})
                elseif 事件=="终于告一段落了" then
                    local 任务id=玩家数据[数字id].角色:取任务(997)
                    玩家数据[数字id].角色:取消任务(任务id)
                end
           elseif 名称=="酒肉和尚" then
                if 事件=="你还敢揍他,找死!" then
                     战斗准备类:创建战斗(数字id,110007,0)
                elseif 事件=="快把解药拿出来,不然别怪我不客气!" then
                    发送数据(玩家数据[数字id].连接id,1501,{名称="酒肉和尚",模型="雨师",
                    对话="就你这态度,想要#G解药#?门儿都没有\n你以为上次我#R输给你#,是你厉害吗?\n那是因为我连#Y一半的实力#,都没拿出来,只是和你#Y玩玩儿\n不过这一次就不一样了,我要#S认真#起来了!#4",选项={"那我们就试试吧!"}})
                elseif 事件=="那我们就试试吧!" then
                      战斗准备类:创建战斗(数字id,110010,0)
                end
           end
    elseif 地图编号==1070 then
            if 名称=="南极仙翁" then
                  if 事件=="学习仙灵店铺" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"仙灵店铺",3,5)
                  elseif 事件=="没问题包在我身上吧" then
                      xsjc2(数字id,2)
                  elseif 事件=="打了还赔医药费,真倒霉" then
                      xsjc2(数字id,4)
                  elseif 事件=="上交百色花" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="上交百色花"}
                      发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="南极仙翁",类型="NPC",等级="无"})
                  end
            elseif 事件=="随机领养孩子" then
              -- if  玩家数据[数字id].召唤兽:是否携带上限() then
              --     添加最后对话(数字id,"召唤兽携带已达上限")
              --     return
              -- elseif 玩家数据[数字id].角色:扣除仙玉(100000,"领养孩子",数字id) == false then
              --     添加最后对话(数字id,"领养孩子需要消耗10W仙玉！你的仙玉不够")
              --     return
              -- end
              --   local 模型库 = {"小毛头","小丫丫","小仙灵","小仙女","小精灵","小魔头"}
              --   local 模型 = 模型库[取随机数(1,#模型库)]
              --   玩家数据[数字id].召唤兽:添加召唤兽(模型,模型,"孩子")
              --   常规提示(数字id,"#Y/你获得了一个孩子")
            elseif 名称=="凤凰姑娘" and 事件=="生肖家园" then
                if 取银子(数字id)<1000000 then
                  常规提示(数字id,"#Y您当前的银子不够进入生肖家园")
                  return
                end
                玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-1000000
                地图处理类:npc传送(数字id,1213,107,59)
            elseif 名称=="蝴蝶女" then
                if 事件=="逢山开路，遇水架桥，这有何难，待我渡来。" then
                    任务处理类:开启通天河副本(数字id)
                elseif 事件=="送我进去" then
                    任务处理类:副本传送(数字id,4)
                elseif 事件=="取消通天河副本" then
                        local 任务id=玩家数据[数字id].角色:取任务(160)
                        if not  任务id or 任务id ==0 or not  任务数据[任务id] then
                          return
                        end
                        local 副本id=任务数据[任务id].副本id
                        for i,v in pairs(地图处理类.地图单位[6027]) do
                          if 任务数据[地图处理类.地图单位[6027][i].id].副本id == 副本id then
                              地图处理类:删除单位(6027,i)
                          end
                        end
                        for i,v in pairs(地图处理类.地图单位[6028]) do
                          if 任务数据[地图处理类.地图单位[6028][i].id].副本id == 副本id then
                              地图处理类:删除单位(6028,i)
                          end
                        end
                        for i,v in pairs(地图处理类.地图单位[6029]) do
                          if 任务数据[地图处理类.地图单位[6029][i].id].副本id == 副本id then
                              地图处理类:删除单位(6029,i)
                          end
                        end
                        for i,v in pairs(地图处理类.地图单位[6030]) do
                          if 任务数据[地图处理类.地图单位[6030][i].id].副本id == 副本id then
                              地图处理类:删除单位(6030,i)
                          end
                        end

                        任务数据[任务id]=nil
                        玩家数据[数字id].角色:取消任务(任务id)
                  end
            elseif 名称=="长寿珍品商人" and 事件=="购买"  then  --建邺
                  玩家数据[数字id].商品列表=商店处理类.商品列表[22]
                  发送数据(id,9,{商品=商店处理类.商品列表[22],银子=玩家数据[数字id].角色.数据.银子})
            elseif 名称=="长寿商人" and 事件=="购买商品" then
                  self.初始金额 = 0
                  for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                      if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                          self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                      end
                  end
                  发送数据(id,70,{商品=商店处理类.商品列表[33],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
            elseif 名称=="长寿货商" and 事件=="购买商品" then
                      玩家数据[数字id].商品列表=商店处理类.商品列表[34]
                      self.初始金额 = 0
                      for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                          if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                              self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                          end
                      end
                      发送数据(id,70,{商品=商店处理类.商品列表[34],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
            elseif 名称=="太白金星" then
                    if 事件 == "开启大闹天宫" then
                       任务处理类:开启大闹天宫副本(数字id)
                    elseif 事件=="我这就前往" then
                       任务处理类:副本传送(数字id,5)
                    end
                    if 玩家数据[数字id].角色:取任务(307)~=0 and 任务数据[玩家数据[数字id].角色:取任务(307)]~=nil then
                        local 任务id=玩家数据[数字id].角色:取任务(307)
                        if 事件=="好的，我这就去" and 任务数据[任务id].分类==1 then
                              任务数据[任务id].分类=2
                              玩家数据[数字id].角色:刷新任务跟踪()
                        elseif 事件=="我去看看" and 任务数据[任务id].分类==9 then
                              任务数据[任务id].分类=10
                              玩家数据[数字id].角色:刷新任务跟踪()
                        end
                    end
            elseif 名称=="慧觉和尚" then
                    local 任务id=玩家数据[数字id].角色:取任务(130)
                    if 事件=="我要开启车迟斗法副本" then
                          任务处理类:开启车迟副本(数字id)
                    elseif 事件=="请送我进去" then
                            任务处理类:副本传送(数字id,2)
                    elseif 事件=="我要取消车迟斗法任务" and 任务id ~=0 and 任务数据[任务id]~=nil then
                            local 副本id=任务数据[任务id].副本id
                            for i,v in pairs(地图处理类.地图单位[6021]) do
                                if 任务数据[地图处理类.地图单位[6021][i].id].副本id == 副本id then
                                    地图处理类:删除单位(6021,i)
                                end
                            end
                            for i,v in pairs(地图处理类.地图单位[6022]) do
                                if 任务数据[地图处理类.地图单位[6022][i].id].副本id == 副本id then
                                    地图处理类:删除单位(6022,i)
                                end
                            end
                            for i,v in pairs(地图处理类.地图单位[6023]) do
                                if 任务数据[地图处理类.地图单位[6023][i].id].副本id == 副本id then
                                    地图处理类:删除单位(6023,i)
                                end
                            end
                            local 取消id=玩家数据[数字id].角色:取任务(130)
                            任务数据[取消id]=nil
                            玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(130))
                    end
            elseif 名称=="PK申请人" then
                    if 事件 == "开启PK开关" then
                          if 玩家数据[数字id].角色.数据.等级<=60 then
                            常规提示(数字id,"#Y等级低于60级的玩家无法开启此开关!#104")
                          elseif 玩家数据[数字id].角色.数据.PK开关 ~= nil then
                            常规提示(数字id,"你当前已经开启了此开关了!#104")
                          else
                              玩家数据[数字id].角色.数据.PK开关 = os.time()
                              添加最后对话(数字id,"你已经开启了pk开关,你的名字将显示为黄色,当前可强行PK次数1次,PK结束后自动失效！")
                              发送数据(id,93,{开关=true})
                              地图处理类:更改PK(数字id,true)
                          end
                    elseif 事件 == "关闭PK开关" then
                            if 玩家数据[数字id].角色.数据.PK开关 == nil then
                              常规提示(数字id,"你当前没有开启了此开关!#104")
                            elseif os.time() - 玩家数据[数字id].角色.数据.PK开关 <= 86400 then
                              常规提示(数字id,"当前无法关闭此开关!#104,#Y请"..时间转换(玩家数据[数字id].角色.数据.PK开关+86400).."前来进行关闭操作")
                            else
                              玩家数据[数字id].角色.数据.PK开关 = nil
                              常规提示(数字id,"恭喜你关闭PK开关成功!#104")
                              发送数据(id,93)
                              地图处理类:更改PK(数字id)
                            end
                    elseif 事件 == "关闭强P开关" then
                            if 玩家数据[数字id].角色.数据.强P开关 == nil then
                              常规提示(数字id,"你当前没有开启了此开关!#104")
                            else
                              玩家数据[数字id].角色.数据.强P开关 = nil
                              常规提示(数字id,"恭喜你关闭PK开关成功!#104")
                              发送数据(id,93)
                              地图处理类:更改强PK(数字id)
                            end
                    elseif 事件 == "开启强P开关" then
                              if 玩家数据[数字id].角色.数据.强P开关 ~= nil then
                                常规提示(数字id,"你当前已经开启了此开关了!#104")
                              elseif 玩家数据[数字id].角色.数据.人气 < 100 then
                                常规提示(数字id,"开启此开关需要消耗100点人气!#104")
                              else
                                玩家数据[数字id].角色.数据.强P开关 = 1
                                玩家数据[数字id].角色.数据.人气 =  玩家数据[数字id].角色.数据.人气 - 100
                                常规提示(数字id,"您消耗100点人气开启了强P开关!#104")
                                添加最后对话(数字id,"你已经开启了强P开关,你的名字将显示为红色,当前可强行PK次数1次,PK结束后自动失效！")
                                发送数据(id,94,{开关=true})
                                地图处理类:更改强PK(数字id,true)
                              end
                    end
            elseif 名称 == "药店老板" and 事件 == "购买"  then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[50]
                    发送数据(id,9,{商品=商店处理类.商品列表[50],银子=玩家数据[数字id].角色.数据.银子})
            end
    elseif 地图编号==1528 and 名称=="法明长老" then
            if 事件=="我要学习打坐" then
              玩家数据[数字id].角色:学习剧情技能(数字id,"打坐",3,5)
            elseif 事件=="你灰溜溜的离开了" then
                xsjc2(数字id,9)
            elseif 事件=="上交定神香" then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="上交定神香"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="法明长老",类型="NPC",等级="无"})
            elseif 事件=="上交佛光舍利子" then
                玩家数据[数字id].给予数据={类型=1,id=0,事件="上交佛光舍利子"}
                发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="法明长老",类型="NPC",等级="无"})
            end
    elseif 地图编号==1103 and 名称=="美猴王" and 事件=="我要学习变化之术" then
            玩家数据[数字id].角色:学习剧情技能(数字id,"变化之术",4,5)
    elseif 地图编号==1207 and 名称=="驿站老板" and 事件=="是的送我过去" then
            地图处理类:npc传送(数字id,1092,23,55)
    elseif 地图编号==1040 then--西凉女国
            if 事件 == "是的我要去" and 名称 == "驿站老板" then
                 地图处理类:npc传送(数字id,1208,128,36)
            elseif 事件 == "我去！我去！" and 名称 == "驿站老板" then
                 地图处理类:npc传送(数字id,1235,465,85)
            elseif 事件 == "开启突破战斗" then
                  战斗准备类:创建战斗(数字id,100261,0)
            elseif 事件 == "木桩伤害测试" then
                   战斗准备类:创建战斗(数字id,110000,0)
            elseif 事件=="什么是渡劫！" then
                 添加最后对话(数字id,"角色等级达到155以后可以在我这里渡劫，成功后即可跨入神界，渡劫成神。渡劫后的修炼等级上限将提升为25级，人物等级上限提升至175级。进入渡劫前，必须先将所有师门技能等级提升至155级。")
            elseif 事件=="我想渡劫，请上仙指点一二" then
               添加最后对话(数字id,"您将进入渡劫战斗，该战斗将持续三天三夜，必须全部获胜。你是否要切入战斗？",{"切入渡劫战斗","让我想一会"})
            elseif 事件=="切入渡劫战斗" then
                   if 玩家数据[数字id].角色.数据.渡劫 then
                      添加最后对话(数字id,"你已经渡劫过了，请勿胡说八道。")
                   elseif 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长==false then
                          添加最后对话(数字id,"你不是队长，无法进行此操作。")
                   elseif 玩家数据[数字id].角色.数据.等级<155  then
                          添加最后对话(数字id,"你的等级尚未达到155级，还是先去努力升级吧。")
                   else
                        for n=1,#玩家数据[数字id].角色.数据.师门技能 do
                            if 玩家数据[数字id].角色.数据.师门技能[n].等级<155 then
                               添加最后对话(数字id,string.format("你的门派技能#Y%s#W尚未达到155级，无法进行渡劫挑战。",玩家数据[数字id].角色.数据.师门技能[n].名称))
                               return
                            end
                        end
                        local 临时任务id = os.time()+数字id
                        战斗准备类:创建战斗(数字id,100049,临时任务id)
                    end

            elseif 事件=="了解圣王！" then
                     添加最后对话(数字id,"角色等级达到175，技能达到180以后！可以在我这里入圣，击败其他神界之主，方可入圣，击败我这里的神界之主方可成为一圣，修炼上限提升至30.可以获取特效炫酷称谓")
            elseif 事件=="圣王，得罪了" then
                    添加最后对话(数字id,"您将进入入圣战斗，该战斗将持续500年，必须全部获胜。你是否要切入战斗？",{"切入入圣战斗","让我想一会"})
            elseif 事件=="切入入圣战斗" then
                     if 玩家数据[数字id].角色.数据.入圣 then
                        添加最后对话(数字id,"你已经入圣过了，请勿胡说八道。")
                     elseif 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长==false then
                            添加最后对话(数字id,"你不是队长，无法进行此操作。")
                     elseif 玩家数据[数字id].角色.数据.等级<175 then
                            添加最后对话(数字id,"你的等级尚未达到175级，还是先去努力升级吧。")
                     else
                          for n=1,#玩家数据[数字id].角色.数据.师门技能 do
                              if 玩家数据[数字id].角色.数据.师门技能[n].等级<180 then
                                 添加最后对话(数字id,string.format("你的门派技能#Y%s#W尚未达到180级，无法进行入圣挑战。",玩家数据[数字id].角色.数据.师门技能[n].名称))
                                 return
                              end
                          end
                          local 临时任务id = os.time()+数字id
                          战斗准备类:创建战斗(数字id,100050,临时任务id)
                     end
            elseif 事件=="我已经准备好了，请开始挑战吧" then
                      if 玩家数据[数字id].角色.数据.等级<119 then--就用这个测试吧
                        常规提示(数字id,"#Y等级不足119级无法开启")
                        return
                      end
                      if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                      if 取队伍人数(数字id)<2 then 常规提示(数字id,"#Y/生死劫活动最少要有三人") return  end
                      发送数据(玩家数据[数字id].连接id,87)
            end
    elseif 地图编号 == 1019 then
            if 事件 == "是的我要打工一次挣钱" and 名称 == "颜如玉" then
                if 玩家数据[数字id].角色.数据.体力 >=40 then
                    玩家数据[数字id].角色.数据.体力=玩家数据[数字id].角色.数据.体力-40
                    玩家数据[数字id].角色:添加银子(3000,"颜如玉打工一次挣钱",1)
                    常规提示(数字id,"恭喜!你得到了3000的报酬!#104")
                else
                    常规提示(数字id,"当前体力不够!在干活就挂了,年轻人不要那么拼命!")
                end
            elseif 事件 == "是的我要打工一天" and 名称 == "颜如玉" then
                  if 玩家数据[数字id].角色.数据.体力 >=40 then
                      for i=1,30 do
                          if 玩家数据[数字id].角色.数据.体力 >=40 then
                            玩家数据[数字id].角色.数据.体力=玩家数据[数字id].角色.数据.体力-40
                            玩家数据[数字id].角色:添加银子(3000,"颜如玉打工一次挣钱",1)
                            常规提示(数字id,"恭喜!你得到了3000的报酬!#105")
                          end
                      end
                  else
                      常规提示(数字id,"当前体力不够!在干活就挂了,年轻人不要那么拼命!")
                  end
            elseif 事件 == "制作神兵图鉴" then
                    self.消耗体力 = 玩家数据[数字id].角色.数据.等级/2
                    if 玩家数据[数字id].角色.数据.体力 >= self.消耗体力 then
                        玩家数据[数字id].角色.数据.体力 = 玩家数据[数字id].角色.数据.体力 - self.消耗体力
                        玩家数据[数字id].道具:给予道具(数字id,"神兵图鉴",1,110)
                        常规提示(数字id,"你获得1个神兵图鉴")
                    else
                        常规提示(数字id,"当前体力不够!无法制作神兵图鉴!")
                    end
            elseif 事件 == "制作飞行符" then
                    self.消耗体力 = 玩家数据[数字id].角色.数据.等级/2
                    if 玩家数据[数字id].角色.数据.体力 >= self.消耗体力 then
                        玩家数据[数字id].角色.数据.体力 = 玩家数据[数字id].角色.数据.体力 - self.消耗体力
                        玩家数据[数字id].道具:给予道具(数字id,"飞行符",10)
                        常规提示(数字id,"你获得10个飞行符")
                    else
                        常规提示(数字id,"当前体力不够!无法制作飞行符!")
                    end
            elseif 事件 == "制作灵宝图鉴" then
                      self.消耗体力 = 玩家数据[数字id].角色.数据.等级/2
                        if 玩家数据[数字id].角色.数据.体力 >= self.消耗体力 then
                            玩家数据[数字id].角色.数据.体力 = 玩家数据[数字id].角色.数据.体力 - self.消耗体力
                            玩家数据[数字id].道具:给予道具(数字id,"灵宝图鉴",1,110)
                            常规提示(数字id,"你获得1个灵宝图鉴")
                        else
                            常规提示(数字id,"当前体力不够!无法制作灵宝图鉴!")
                        end
            end
    elseif 地图编号 == 1025 and 名称 == "冯铁匠"  then
            if 事件 == "是的我要打工一次挣钱" then
                  if 玩家数据[数字id].角色.数据.活力 >=40 then
                      玩家数据[数字id].角色.数据.活力=玩家数据[数字id].角色.数据.活力-40
                      玩家数据[数字id].角色:添加银子(3000,"冯铁匠打工一次挣钱",1)
                      常规提示(数字id,"恭喜!你得到了3000的报酬!")
                  else
                      常规提示(数字id,"当前活力不够!在干活就挂了,年轻人不要那么拼命!")
                  end
            elseif 事件 == "是的我要打工一天" then
                  if 玩家数据[数字id].角色.数据.活力 >=40 then
                      for i=1,30 do
                          if 玩家数据[数字id].角色.数据.活力 >=40 then
                            玩家数据[数字id].角色.数据.活力=玩家数据[数字id].角色.数据.活力-40
                            玩家数据[数字id].角色:添加银子(3000,"冯铁匠打工一次挣钱",1)
                            常规提示(数字id,"恭喜!你得到了3000的报酬!")
                          end
                      end
                  else
                      常规提示(数字id,"当前活力不够!在干活就挂了,年轻人不要那么拼命!")
                  end
            end

    elseif 地图编号 == 1621 then
            if 事件 == "开打开打" then
                local 任务id = 玩家数据[数字id].角色:取任务(411)
                if not 任务id or 任务id==0 then return end
                if not 玩家数据[数字id].队长 then 添加最后对话(数字id,"只有队长可以开启。") return  end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5个会员进行") return  end
                if not 任务数据[任务id].战斗时间 or os.time()-任务数据[任务id].战斗时间 <40 then
                  常规提示(数字id,"在干活就挂了,年轻人不要那么拼命!#R"..(40-(os.time()-任务数据[任务id].战斗时间)).."#Y秒后再来战斗吧")
                  return
                end
                if 取队员任务一致(数字id,411,1) then
                    战斗准备类:创建战斗(数字id+0,100158,任务id)
                    任务数据[任务id].战斗时间=os.time()
                    return
                end

            end
    elseif 地图编号 == 1865 then
            if 事件 == "给我些任务" then
                任务处理类:设置青龙任务(数字id)
            elseif 事件 == "取消任务" then
                  if 玩家数据[数字id].角色:取任务(301)==0 then
                    发送数据(玩家数据[数字id].连接id,1501,{名称="青龙总管",模型="男人_兰虎",对话="你有在我这里领取任务吗？"})
                  elseif 玩家数据[数字id].角色.青龙间隔 ~= nil and os.time()-玩家数据[数字id].角色.青龙间隔 <= 300 then
                    常规提示(数字id,"#Y/5分钟内不允许连续取消任务！")
                    return
                  else
                    玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(301))
                    发送数据(玩家数据[数字id].连接id,1501,{名称="青龙总管",模型="男人_兰虎",对话="已经成功帮你取消了任务。"})
                    玩家数据[数字id].角色.青龙间隔 = os.time()
                  end
            end
    elseif 地图编号 == 1845 then
          if 事件 == "给我些任务" then
                任务处理类:设置玄武任务(数字id)
          elseif 事件 == "取消任务" then
                  local id组 = 取id组(数字id)
                  for i=1,#id组 do
                      if 玩家数据[id组[i]].角色:取任务(302)==0 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="玄武总管",模型="男人_兰虎",对话="你有在我这里领取任务吗？"})
                      elseif 玩家数据[id组[i]].角色.玄武间隔 ~= nil and os.time()-玩家数据[id组[i]].角色.玄武间隔 <= 300 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="玄武总管",模型="男人_兰虎",对话="5分钟内不允许连续取消任务"})
                          return
                      else
                          玩家数据[id组[i]].角色:取消任务(玩家数据[id组[i]].角色:取任务(302))
                          发送数据(玩家数据[数字id].连接id,1501,{名称="厢房总管",模型="男人_兰虎",对话="已经成功帮你取消了任务。"})
                          玩家数据[id组[i]].角色.玄武间隔 = os.time()
                      end
                  end
          end
    elseif 地图编号 == 1815 then
          if 事件 == "申请成为商人" then
               if 帮派处理类:取是否有帮派(数字id) then
                      local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
                      if 玩家数据[数字id].角色.数据.跑商 and 帮派数据[帮派编号].成员数据[数字id].职务 == "商人"  then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="你已经是商人了,无需再次申请！"})
                      elseif 帮派数据[帮派编号].商人 ~= nil and #帮派数据[帮派编号].商人 >=8 then
                          常规提示(数字id,"#Y/当前帮派商人的名额已经满了")
                          return
                      elseif 玩家数据[数字id].角色.数据.帮派数据.权限+0 > 0 then
                          常规提示(数字id,"#Y/只有帮众才可以申请为商人")
                          return
                      elseif 帮派数据[帮派编号].成员数据[数字id].职务 == "副帮主" or 帮派数据[帮派编号].成员数据[数字id].职务 == "帮主" then
                          常规提示(数字id,"#Y/帮主副帮主无需申请成为商人！")
                          return
                      else
                          if 帮派数据[帮派编号].商人 == nil then
                             帮派数据[帮派编号].商人 = {}
                          end
                          帮派数据[帮派编号].商人[#帮派数据[帮派编号].商人+1] = {名称=玩家数据[数字id].角色.数据.名称,id=数字id}
                          帮派数据[帮派编号].成员数据[数字id].职务 = "商人"
                          玩家数据[数字id].角色:删除称谓(帮派数据[帮派编号].帮派名称.."帮众")
                          玩家数据[数字id].角色:添加称谓(帮派数据[帮派编号].帮派名称.."商人")
                      end
                end
          elseif 事件 == "给我些任务" then
                  if 帮派处理类:取是否有帮派(数字id) then
                        local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
                        if 玩家数据[数字id].角色.数据.跑商 ~= nil then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="你身上已经领取了跑商任务，赶快去帮帮派赚取足够的银票吧！"})
                            return
                        elseif 帮派数据[帮派编号].成员数据[数字id].职务 ~= "商人" and 帮派数据[帮派编号].成员数据[数字id].职务 ~= "帮主" and 帮派数据[帮派编号].成员数据[数字id].职务 ~= "副帮主" then
                           发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="你不是商人跑什么跑，赶快去帮帮派赚取足够的银票吧！"})
                            return
                        elseif 玩家数据[数字id].队伍~=0 then
                           常规提示(数字id,"#Y/组队无法领取此任务")
                            return
                        else
                            玩家数据[数字id].角色.数据.跑商 = true
                            玩家数据[数字id].角色.数据.跑商时间 = os.time()
                            玩家数据[数字id].道具:给予道具(数字id,"帮派银票")
                            常规提示(数字id,"#Y/你获得了帮派银票")
                            发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="为了帮派的明天大家一起努力赚取足够的银票吧！"})
                        end
                   end
          elseif 事件 == "取消跑商任务" then
                  if 玩家数据[数字id].角色.数据.跑商 then
                       发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="是否确定取消帮派跑商任务，若取消的话会扣除5万大洋作为惩罚！",选项={"确定取消跑商任务","你让我想一想"}})
                  else
                      常规提示(数字id,"#Y/你并没有在我这里接取任何的任务，存心来捣乱的吗？关门放守护兽！")
                      return
                  end
          elseif 事件 == "确定取消跑商任务" then
                  local 跑商惩罚 = 50000
                  if 取银子(数字id) < 跑商惩罚 then
                      常规提示(数字id,"#Y/你当前的银子不够5万，无法取消跑商任务！")
                      return
                  end
                   if 帮派处理类:取是否有帮派(数字id) then
                        玩家数据[数字id].角色:扣除银子(跑商惩罚,"取消帮派跑商惩罚",1)
                        玩家数据[数字id].道具:消耗背包道具(数字id,"帮派银票")
                        玩家数据[数字id].角色.数据.跑商 = nil
                        发送数据(玩家数据[数字id].连接id,1501,{名称="金库总管",模型="男人_兰虎",对话="已帮你取消了帮派跑商的任务，并扣除了5W大洋作为惩罚！"})
                    end
          end
    elseif 地图编号==1013 or 地图编号==1524 or 地图编号==1081 or 地图编号==1099 then
            if 事件=="存款取款" then
              --发送数据(id,71,{银子=玩家数据[数字id].角色.数据.银子,存银=玩家数据[数字id].角色.数据.存银})
            end
    elseif 地图编号==1084  then
              if 事件=="我要制作家具设计图" then
                发送数据(id,159)
              elseif 事件=="听说这里可以制作如意符" then
                 if 玩家数据[数字id].角色.数据.体力<40 then
                      常规提示(数字id,"#Y少侠,当前体力不够，制造失败")
                    return
                  end
                 if 玩家数据[数字id].角色.数据.银子<2000 then
                     常规提示(数字id,"#Y少侠,当前银子不够，制造失败")
                    return
                  end
                  玩家数据[数字id].角色.数据.体力=玩家数据[数字id].角色.数据.体力-40
                  玩家数据[数字id].角色.数据.银子=玩家数据[数字id].角色.数据.银子-2000
                  玩家数据[数字id].道具:给予道具(数字id,"如意符")
                  常规提示(数字id,"#Y/你获得了#R/如意符")
                  体活刷新(数字id)
               elseif 事件=="我想合成家具" then
               end
    elseif 地图编号==1855 and 事件=="提高修炼修为"  then
              发送数据(id,74,{银子=玩家数据[数字id].角色.数据.银子,存银=玩家数据[数字id].角色.数据.存银})
    elseif 地图编号==1825 and 事件=="学习技能"  then
              local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
              if 帮派编号~=nil and 帮派编号>0 then
                 发送数据(id,75,{银子=玩家数据[数字id].角色.数据.银子,储备=玩家数据[数字id].角色.数据.储备,帮派数据=帮派数据[帮派编号]})
              end
    elseif 地图编号==6024 then
            local 任务id = 玩家数据[数字id].角色:取任务(150)
            if 名称=="道场督僧" then
                  if 事件=="装潢（需要完成"..任务数据[任务id].装潢.."/10个）" then
                      if 任务数据[任务id].装潢>=10 then
                        常规提示(数字id,"#Y/装潢任务已经完成，去看看其他的任务")
                        return
                      else
                            if 玩家数据[数字id].采摘木材~=nil or 玩家数据[数字id].驱逐泼猴~=nil then
                              添加最后对话(数字id,"少侠身上已经任务，请先完成任务再来！")
                              return
                            elseif 玩家数据[数字id].队伍~=0 then
                              添加最后对话(数字id,"组队状态下无法接任务！")
                              return
                            end
                            if 取随机数()<=50 then
                                对话="道场还缺些木材，去道场后院采集一些桃木。"
                                发送数据(玩家数据[数字id].连接id,1501,{名称="道场督僧",模型="男人_方丈",对话=对话})
                                玩家数据[数字id].采摘木材=true
                            else
                                任务处理类:驱逐泼猴(数字id)
                                local 任务id1 = 玩家数据[数字id].角色:取任务(352)
                                对话=string.format("去道场的后院#R%s,%s#W附近的#R蟠桃树#W上查找一下泼猴的踪迹。",任务数据[任务id1].x,任务数据[任务id1].y)
                                发送数据(玩家数据[数字id].连接id,1501,{名称="道场督僧",模型="男人_方丈",对话=对话})
                                玩家数据[数字id].驱逐泼猴=true
                            end
                      end
                  elseif 事件=="邀请（需要完成"..任务数据[任务id].邀请.."/10个）" then
                        if 任务数据[任务id].邀请>=10 then
                          常规提示(数字id,"#Y/邀请任务已经完成，去看看其他的任务")
                          return
                        else
                          任务处理类:添加水陆邀请任务(数字id)
                        end
                  end
            elseif 名称=="道场童子" and 事件=="快送我过去" then
                    地图处理类:npc传送(数字id,6025,500,10)
            elseif 名称=="翼虎将军" and 事件=="妖怪看打" then
                      local 副本id=任务数据[玩家数据[数字id].角色:取任务(150)].副本id
                      if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                      if 取队伍人数(数字id)<3 then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
                      if 副本数据.水陆大会.进行[副本id].翼虎 then
                          if 副本数据.水陆大会.进行[副本id].击败翼虎 then
                              添加最后对话(数字id+0,"少侠饶命！我再也不敢了")
                          else
                              if 副本数据.水陆大会.进行[副本id].袈裟 then
                                战斗准备类:创建战斗(数字id+0,100116,玩家数据[数字id].角色:取任务(150))
                              else
                                添加最后对话(数字id+0,"只有找到观音的袈裟才能降服我")
                              end
                          end
                        else
                            战斗准备类:创建战斗(数字id+0,100114,玩家数据[数字id].角色:取任务(150))
                        end
            elseif 名称=="蝰蛇将军" and 事件=="妖怪看打" then
                      local 副本id=任务数据[玩家数据[数字id].角色:取任务(150)].副本id
                      if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                      if 取队伍人数(数字id)<3 then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
                      if 副本数据.水陆大会.进行[副本id].蝰蛇 then
                          if 副本数据.水陆大会.进行[副本id].击败蝰蛇 then
                              添加最后对话(数字id+0,"少侠饶命！我再也不敢了")
                          else
                              if 副本数据.水陆大会.进行[副本id].袈裟 then
                                  战斗准备类:创建战斗(数字id+0,100117,玩家数据[数字id].角色:取任务(150))
                              else
                                  添加最后对话(数字id+0,"只有找到观音的袈裟才能降服我")
                              end
                          end
                      else
                          战斗准备类:创建战斗(数字id+0,100115,玩家数据[数字id].角色:取任务(150))
                      end
            end
    elseif 地图编号==6025 and 玩家数据[数字id].角色:取任务(150)~=0  then
            local 任务id=玩家数据[数字id].角色:取任务(150)
            local 副本id=任务数据[任务id].副本id
            if 名称=="传送侍卫" and 事件=="快送我过去"  then
                  地图处理类:npc传送(数字id,6024,10,88)
            elseif 事件=="我来领取佛祖法宝<领取袈裟>" then
                  if 副本数据.水陆大会.进行[副本id].袈裟  then
                    添加最后对话(数字id,"法宝已被领取，无需重复领取")
                  else
                      副本数据.水陆大会.进行[副本id].袈裟=true
                  --  玩家数据[数字id].角色.袈裟=true
                      添加最后对话(数字id,"少侠拿着法宝，赶快去救玄奘吧！")
                  end
            elseif 事件=="我来领取佛祖法宝<领取锡杖>" then
                  if 副本数据.水陆大会.进行[副本id].锡杖  then
                      添加最后对话(数字id,"法宝已被领取，无需重复领取")
                  else
                      副本数据.水陆大会.进行[副本id].锡杖=true
                     -- 玩家数据[数字id].角色.锡杖=true
                      添加最后对话(数字id,"少侠拿着法宝，赶快去救玄奘吧！")
                  end
            end
    elseif 地图编号==1249 and 名称=="传送侍卫" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1250 and 名称=="传送侍卫" and 事件=="是的我要去" then
              地图处理类:npc传送(数字id,1001,470,253)
    elseif 地图编号==1332 then
          --   if 事件=="孩子管理" then
          --        if #玩家数据[数字id].孩子.数据==0 then
          --           添加最后对话(数字id,"老爷真是可怜竟然连个孩子都没有#15")
          --           return
          --        end
          --          添加最后对话(数字id,"请选择项目",{"设置成长方向","培养孩子","孩子拜师","技能学习"})
          --   elseif 事件=="设置成长方向" then
          --        local 选项 = {}
          --        for i=1,#玩家数据[数字id].孩子.数据 do
          --            选项[i]=玩家数据[数字id].孩子.数据[i].模型
          --        end
          --          添加最后对话(数字id,"选择子女",选项)
          --          玩家数据[数字id].最后操作="设置成长方向"
          --   elseif 事件=="培养孩子" then
          --         local 选项 = {}
          --         for i=1,#玩家数据[数字id].孩子.数据 do
          --              选项[i]=玩家数据[数字id].孩子.数据[i].模型
          --         end
          --         添加最后对话(数字id,"选择子女",选项)
          --         玩家数据[数字id].最后操作="培养孩子"
          --   elseif 事件=="孩子拜师" then
          --        local 选项 = {}
          --        for i=1,#玩家数据[数字id].孩子.数据 do
          --            选项[i]=玩家数据[数字id].孩子.数据[i].模型
          --        end
          --        添加最后对话(数字id,"选择子女",选项)
          --        玩家数据[数字id].最后操作="孩子拜师"
          --   elseif 事件=="技能学习" then
          --          local 选项 = {}
          --          for i=1,#玩家数据[数字id].孩子.数据 do
          --              选项[i]=玩家数据[数字id].孩子.数据[i].模型
          --          end
          --          添加最后对话(数字id,"选择子女",选项)
          --          玩家数据[数字id].最后操作="技能学习"
          --   elseif 事件=="干" then
          --         添加最后对话(数字id,"7号技师为您服务#9",{"蚂蚁上树","猴子偷桃","老汉推车","弯弓射雕"})  --房屋女仆对话,
          --   end
          --   if 玩家数据[数字id].最后操作=="设置成长方向"  then
          --         local 临时 = {"体质","魔力","力量","耐力","敏捷"}
          --         for i=1,#玩家数据[数字id].孩子.数据 do
          --             if 事件==玩家数据[数字id].孩子.数据[i].模型 then
          --                玩家数据[数字id].最后操作=i
          --                玩家数据[数字id].设置成长方向=1
          --                添加最后对话(数字id,"子女升级时自动获取对应属性",临时)
          --             end
          --         end
          --         临时=nil
          --   end
          --   if 玩家数据[数字id].最后操作=="培养孩子" then
          --       for i=1,#玩家数据[数字id].孩子.数据 do
          --          local 选项 = {}
          --          for i=1,#玩家数据[数字id].孩子.数据 do
          --              选项[i]=玩家数据[数字id].孩子.数据[i].模型
          --              玩家数据[数字id].培养孩子=1
          --          end
          --          添加最后对话(数字id,"每次扣除100W银子和100W经验",选项)
          --       end
          -- end



          -- if 玩家数据[数字id].设置成长方向==1 then
          --     local 临时 = {"体质","魔力","力量","耐力","敏捷"}
          --     for i=1,#临时 do
          --         if 事件==临时[i] then
          --            玩家数据[数字id].孩子.数据[玩家数据[数字id].最后操作].成长方向=临时[i]
          --            添加最后对话(数字id,"成功")
          --            玩家数据[数字id].设置成长方向=nil
          --            玩家数据[数字id].最后操作=nil
          --         end
          --     end
          --     临时=nil
          -- end


          -- if 玩家数据[数字id].培养孩子==1 then
          --     for i=1,#玩家数据[数字id].孩子.数据 do
          --         if 事件==玩家数据[数字id].孩子.数据[i].模型 then
          --            if 玩家数据[数字id].孩子.数据[i].成长方向==nil then
          --               添加最后对话(数字id,"请先选择孩子培养方向")
          --            end
          --            local 内容 = {序列=2,参数=i}
          --            玩家数据[数字id].孩子:数据处理(数字id,内容)
          --            玩家数据[数字id].培养孩子=nil
          --            玩家数据[数字id].最后操作=nil
          --            添加最后对话(数字id,"培养完成")
          --         end
          --     end
          -- end

          -- if 玩家数据[数字id].最后操作=="孩子拜师" then
          --     玩家数据[数字id].孩子拜师=事件
          --     local  门派 = {"方寸山","女儿村","神木林","化生寺","大唐官府","盘丝洞","阴曹地府","无底洞","魔王寨","狮驼岭","天宫","普陀山","凌波城","五庄观","龙宫"}
          --     for i=1,#玩家数据[数字id].孩子.数据 do
          --         if 玩家数据[数字id].孩子.数据[i].模型==玩家数据[数字id].孩子拜师 then
          --           玩家数据[数字id].孩子拜师选择=玩家数据[数字id].孩子.数据[i].模型
          --           添加最后对话(数字id,"请选择您中意的门派",门派)

          --         end
          --     end
          --     for i=1,#门派 do
          --         if 事件==门派[i] then
          --           玩家数据[数字id].最后操作="选择孩子门派"
          --         end
          --     end
          -- end
          -- if 玩家数据[数字id].最后操作=="选择孩子门派" then
          --     for i=1,#玩家数据[数字id].孩子.数据 do
          --         if 玩家数据[数字id].孩子拜师选择==玩家数据[数字id].孩子.数据[i].模型 then
          --            if 玩家数据[数字id].孩子.数据[i].成长方向==nil then
          --               添加最后对话(数字id,"请先选择孩子培养方向")
          --            end
          --            local 内容 = {序列=3,参数=i,门派=事件}
          --            玩家数据[数字id].孩子:数据处理(数字id,内容)
          --            玩家数据[数字id].孩子拜师=nil
          --            玩家数据[数字id].最后操作=nil
          --            玩家数据[数字id].孩子拜师选择=nil
          --            添加最后对话(数字id,"操作完成")
          --         end
          --     end
          -- end

          -- if 玩家数据[数字id].最后操作=="技能学习"  then
          --     for i=1,#玩家数据[数字id].孩子.数据 do
          --         if 事件==玩家数据[数字id].孩子.数据[i].模型 then
          --            发送数据(玩家数据[数字id].连接id,124,{道具=玩家数据[数字id].道具:索要道具1(数字id),孩子=玩家数据[数字id].孩子.数据[i],位置=i})
          --            玩家数据[数字id].最后操作=nil
          --            break
          --         end
          --     end
          -- end

    elseif 地图编号==6037 and 名称=="阎王" and 事件=="那就别怪我不客气" then
              战斗准备类:创建战斗(数字id+0,100215,0)
              return
    elseif 地图编号==6038 and 名称=="玉皇大帝" and 事件=="那老孙就露两手" then
              战斗准备类:创建战斗(数字id+0,100216,0)
              return
    elseif 地图编号==6039 and 名称=="镇塔之神" and 事件=="放马过来"  then

                战斗准备类:创建战斗(数字id+0,100220,0)
                return
    end
  end
end


function 对话处理:长安城对话事件处理(id,数字id,名称,事件)
        if 名称=="罗道人" then
                      if 事件=="购买" then
                        玩家数据[数字id].商品列表=商店处理类.商品列表[6]
                        发送数据(id,9,{商品=商店处理类.商品列表[6],银子=玩家数据[数字id].角色.数据.银子})
                      end
        elseif 名称=="长安珍品商人" and 玩家数据[数字id].最后对话.编号==27 then
                      if 事件=="购买" then
                        玩家数据[数字id].商品列表=商店处理类.商品列表[21]
                        发送数据(id,9,{商品=商店处理类.商品列表[21],银子=玩家数据[数字id].角色.数据.银子})
                      end
        elseif 名称=="染色师" then
                      if 事件=="我想为人物染色" then
                        发送数据(id,29)
                      end
        elseif 名称=="比武大会主持人" then  --比武npc
                if 事件=="单人报名" then
                    英雄大会:玩家单人报名(数字id)
                elseif 事件=="组队报名" then
                        英雄大会:玩家组队报名(数字id)
                elseif 事件=="添加新队友" then
                        英雄大会:队伍添加报名(数字id)
                elseif 事件=="多人模式" then
                        英雄大会:玩家组队报名(数字id,1)
                elseif 事件=="单人模式" then
                        英雄大会:玩家单人报名(数字id,1)
                elseif 事件=="查看队友" then
                        if 英雄大会.报名[数字id] and 英雄大会.报名[数字id].组队 and 英雄大会.报名[数字id].成员 then
                            local 在线队友 = {}
                            for k,v in pairs(英雄大会.报名[数字id].成员) do
                                if 玩家数据[k]~=nil and 英雄大会.报名[数字id].成员[k] then
                                  在线队友[#在线队友+1] = 玩家数据[k].角色.数据.名称
                                end
                            end
                            发送数据(玩家数据[数字id].连接id,1501,{名称="比武大会主持人",模型="男人_将军",对话="当前在线队友:",选项=在线队友})
                        end
                elseif 事件=="进入比武" then
                        if 英雄大会.报名[数字id].组队 then
                            if 玩家数据[数字id].队伍~=0 and 玩家数据[数字id].队长  then
                                if 英雄大会:是否同组(数字id) then
                                    local xy=地图处理类.地图坐标[6004]:取随机点()
                                    地图处理类:npc传送(数字id,6004,xy.x,xy.y)
                                else
                                    常规提示(数字id,"队伍中有成员不是同队报名")
                                end
                            else
                                    常规提示(数字id,"组队模式必须组队并是队长才可以进入")
                            end
                        else
                              if 玩家数据[数字id].队伍~=0 then
                                  常规提示(数字id,"单人报名无法组队进入")
                                  return
                              end
                              local xy=地图处理类.地图坐标[6003]:取随机点()
                              地图处理类:npc传送(数字id,6003,xy.x,xy.y)
                         end
                elseif 事件=="我要领取比赛奖励" then
                          if 英雄排名.第一名[数字id] and not 英雄排名.第一名[数字id].领取 and not 英雄排名[数字id].领取 and 英雄排名.第一名[数字id].积分>0 then
                              玩家数据[数字id].角色:自定义银子添加("比武第一名",1)
                              玩家数据[数字id].角色:添加活跃积分(150,"比武第一名",1)
                              玩家数据[数字id].角色:添加比武积分(150,"比武第一名",1)
                              玩家数据[数字id].角色:批量删除称谓("英雄大会")
                              玩家数据[数字id].角色:添加称谓("英雄大会冠军")

                              英雄排名[数字id].领取 = true
                              英雄排名.第一名[数字id].领取 =true
                              local 获得物品={}
                              for i=1,#自定义数据.比武第一名 do
                                  if 取随机数()<=自定义数据.比武第一名[i].概率 then
                                      获得物品[#获得物品+1]=自定义数据.比武第一名[i]
                                  end
                              end
                              获得物品=删除重复(获得物品)
                              if 获得物品~=nil then
                                  local 取编号=取随机数(1,#获得物品)
                                  if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                      玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                      广播消息({内容=string.format("#S/(英雄大会)#R/%s#Y/获得了第一名，唐皇奖励了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                  end
                              end

                               return
                          end
                          if 英雄排名.第二名[数字id] and not 英雄排名.第二名[数字id].领取 and not 英雄排名[数字id].领取 and 英雄排名.第二名[数字id].积分>0 then
                              玩家数据[数字id].角色:自定义银子添加("比武第二名",1)
                              玩家数据[数字id].角色:添加活跃积分(100,"比武第二名",1)
                              玩家数据[数字id].角色:添加比武积分(100,"比武第二名",1)
                              玩家数据[数字id].角色:批量删除称谓("英雄大会")
                              玩家数据[数字id].角色:添加称谓("英雄大会亚军")
                              英雄排名[数字id].领取 = true
                              英雄排名.第二名[数字id].领取 =true
                              local 获得物品={}
                              for i=1,#自定义数据.比武第二名 do
                                  if 取随机数()<=自定义数据.比武第二名[i].概率 then
                                      获得物品[#获得物品+1]=自定义数据.比武第二名[i]
                                  end
                              end
                              获得物品=删除重复(获得物品)
                              if 获得物品~=nil then
                                  local 取编号=取随机数(1,#获得物品)
                                  if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                      玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                      广播消息({内容=string.format("#S/(英雄大会)#R/%s#Y/获得了第二名，唐皇奖励了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                  end
                              end

                              return
                          end
                          if 英雄排名.第三名[数字id] and not 英雄排名.第三名[数字id].领取 and not 英雄排名[数字id].领取 and 英雄排名.第三名[数字id].积分>0 then
                                玩家数据[数字id].角色:自定义银子添加("比武第三名",1)
                                玩家数据[数字id].角色:添加活跃积分(50,"比武第三名",1)
                                玩家数据[数字id].角色:添加比武积分(50,"比武第三名",1)
                                玩家数据[数字id].角色:批量删除称谓("英雄大会")
                                玩家数据[数字id].角色:添加称谓("英雄大会季军")
                                英雄排名[数字id].领取 = true
                                英雄排名.第三名[数字id].领取 =true
                                local 获得物品={}
                                for i=1,#自定义数据.比武第三名 do
                                    if 取随机数()<=自定义数据.比武第三名[i].概率 then
                                        获得物品[#获得物品+1]=自定义数据.比武第三名[i]
                                    end
                                end
                                获得物品=删除重复(获得物品)
                                if 获得物品~=nil then
                                    local 取编号=取随机数(1,#获得物品)
                                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                        玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                        广播消息({内容=string.format("#S/(英雄大会)#R/%s#Y/获得了第三名，唐皇奖励了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                    end
                                end
                                return
                          end
                          if 英雄排名[数字id] then
                              if not 英雄排名[数字id].领取 then
                                    玩家数据[数字id].角色:自定义银子添加("比武第四名",1)
                                    玩家数据[数字id].角色:添加活跃积分(5,"比武第四名",1)
                                    玩家数据[数字id].角色:添加比武积分(5,"比武第四名",1)
                                    玩家数据[数字id].角色:批量删除称谓("英雄大会")
                                    玩家数据[数字id].角色:添加称谓("英雄大会精英")
                                    英雄排名[数字id].领取 = true
                                    local 获得物品={}
                                    for i=1,#自定义数据.比武第四名 do
                                        if 取随机数()<=自定义数据.比武第四名[i].概率 then
                                             获得物品[#获得物品+1]=自定义数据.比武第四名[i]
                                        end
                                    end
                                    获得物品=删除重复(获得物品)
                                    if 获得物品~=nil then
                                        local 取编号=取随机数(1,#获得物品)
                                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                            玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                            广播消息({内容=string.format("#S/(英雄大会)#R/%s#Y/积极参与了英雄大会，唐皇奖励了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                        end
                                    end

                              else
                                  添加最后对话(数字id,"本次活动奖励已领取")
                              end
                          else
                              添加最后对话(数字id,"你没有奖励,请积极参加活动")
                          end
                elseif 事件=="我要打听天下英雄会消息" then
                          添加最后对话(数字id,"每次活动开启总时间1小时15分,10分钟报名时间,5分钟准备时间,报名结束后可以随时入场,入场开始截至报名,报名期间可以随意切换多人或单人的游戏模式,单人模式改多人模式须进队或自己创建新的战队,多人模式切换单人需退出队伍.#R/注:多人模式如有新成员必须组队后队长添加(添加队友不可以是已经参加多人模式)")
                elseif 事件=="查看排行" then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="比武大会主持人",模型="男人_将军",对话="请选择你查看排行的名次:",选项={"第一名","第二名","第三名","当前排名"}})
                elseif 事件=="第一名" then
                        local 选项 = {}
                        if 英雄排名.第一名~=nil then
                            for k,v in pairs(英雄排名.第一名) do
                                选项[#选项+1] = v.名称
                            end
                        end
                        发送数据(玩家数据[数字id].连接id,1501,{名称="比武大会主持人",模型="男人_将军",对话="本次比武排名第一的玩家有:",选项=选项})
                elseif 事件=="第二名" then
                        local 选项 = {}
                        if 英雄排名.第二名~=nil then
                            for k,v in pairs(英雄排名.第二名) do
                                选项[#选项+1] = v.名称
                            end
                        end
                        发送数据(玩家数据[数字id].连接id,1501,{名称="比武大会主持人",模型="男人_将军",对话="本次比武排名第二的玩家有:",选项=选项})
                elseif 事件=="第三名" then
                        local 选项 = {}
                        if 英雄排名.第三名~=nil then
                            for k,v in pairs(英雄排名.第三名) do
                                选项[#选项+1] = v.名称
                            end
                        end
                        发送数据(玩家数据[数字id].连接id,1501,{名称="比武大会主持人",模型="男人_将军",对话="本次比武排名第三的玩家有:",选项=选项})
                elseif 事件=="当前排名" then
                        local 排名="没有上榜"
                        local 领取="没有奖励"
                        if 英雄排名.第一名[数字id] then
                              排名="第一名"
                        elseif 英雄排名.第二名[数字id] then
                              排名="第二名"
                        elseif 英雄排名.第三名[数字id] then
                              排名="第三名"
                        end
                        if 英雄排名[数字id] then
                            if 英雄排名[数字id].领取 then
                                领取="已领取"
                            else
                                领取="未领取"
                            end
                        end
                        添加最后对话(数字id,"当前玩家角色排名:#R/"..排名.."#W/  当前奖励:#R/"..领取)
                end
        elseif 名称=="五行大师" then
                if 事件=="请帮我点化装备" or 事件=="继续点化" then
                     if 玩家数据[数字id].角色:取任务(14)~=nil and 玩家数据[数字id].角色:取任务(14)~=0 then
                          玩家数据[数字id].给予数据={类型=1,id=0,事件="点化装备"}
                          发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="五行大师",类型="NPC",等级="无"})
                      else
                           添加最后对话(数字id,"你身上没有点化任务，无法点化套装")
                      end
                elseif 事件=="请帮我更换宝宝五行" then--------------宝宝
                           if 自定义数据.更换宝宝五行==nil then
                              自定义数据.更换宝宝五行={货币类型="仙玉",数量=2000}
                           end
                           local 选项={}
                           for n=1,#玩家数据[数字id].召唤兽.数据 do
                               选项[#选项+1]=string.format("%s,等级:%s",玩家数据[数字id].召唤兽.数据[n].名称,玩家数据[数字id].召唤兽.数据[n].等级)
                           end
                           if #选项 < 1 then
                                添加最后对话(数字id,"你没有可以选择的召唤兽")
                                玩家数据[数字id].更换宝宝五行 = nil
                               return
                           end
                            发送数据(玩家数据[数字id].连接id,1501,{名称="五行大师",模型="五行大师",对话="请选择更换五行的召唤兽,更换五行需要#R/"..自定义数据.更换宝宝五行.数量.."#W/"..自定义数据.更换宝宝五行.货币类型,选项=选项})
                            玩家数据[数字id].更换宝宝五行 =  true
                elseif 事件=="请帮我更换法宝五行" then
                           if 自定义数据.更改法宝五行==nil then
                              自定义数据.更改法宝五行={货币类型="仙玉",数量=2000}
                           end
                           发送数据(玩家数据[数字id].连接id,1501,{名称="五行大师",模型="五行大师",对话="更换法宝五行需要#R/"..自定义数据.更改法宝五行.数量.."#W/"..自定义数据.更改法宝五行.货币类型,选项={"确定更换法宝五行","我在考虑考虑"}})
                elseif 事件=="确定更换法宝五行" then
                         玩家数据[数字id].给予数据={类型=1,id=0,事件="更换法宝五行"}
                         发送数据(id,3507,{道具=玩家数据[数字id].道具:索要法宝2(数字id,0),名称="五行大师",类型="法宝",等级="无"})
                elseif 事件=="请帮我更换装备五行" then
                           if 自定义数据.更改装备五行==nil then
                              自定义数据.更改装备五行={货币类型="仙玉",数量=2000}
                           end
                           发送数据(玩家数据[数字id].连接id,1501,{名称="五行大师",模型="五行大师",对话="更换装备五行需要#R/"..自定义数据.更改装备五行.数量.."#W/"..自定义数据.更改装备五行.货币类型,选项={"确定更换装备五行","我在考虑考虑"}})
                elseif 事件=="确定更换装备五行" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="更改装备五行"}
                        发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="五行大师",类型="NPC",等级="无"})
                end
        elseif 名称=="商会总管" then
              if 事件=="购买召唤兽" then
                     if 玩家数据[数字id].角色.数据.等级<69 then
                        添加最后对话(数字id,"你的等级不足69级，无法购买")
                        return
                      end
                      系统处理类:获取商店召唤兽(数字id)
              elseif 事件=="道具交易中心" then
                      -- if 玩家数据[数字id].角色.数据.等级<69 then
                      --   添加最后对话(数字id,"你的等级不足69级，无法购买")
                      --   return
                      -- end
                      -- 系统处理类:打开交易中心(数字id)
              elseif 事件=="请帮我刷新" then
                      local 银子=500000
                      if 玩家数据[数字id].角色.数据.银子<银子 then
                        添加最后对话(数字id,"你身上的银子不够")
                      else
                        玩家数据[数字id].角色:扣除银子(银子,"商会召唤兽刷新",1)
                        if not 商店bb[数字id] then
                            商店bb[数字id]={}
                            商店bb[数字id].刷新=0
                        end
                        local 刷新=商店bb[数字id].刷新+1
                        常规提示(数字id,"刷新召唤兽成功")
                        系统处理类:刷新商店召唤兽(数字id)
                        商店bb[数字id].刷新=刷新
                        发送数据(玩家数据[数字id].连接id,131,商店bb[数字id])
                      end
              elseif 事件=="购买变异召唤兽" then
                      if 玩家数据[数字id].角色.数据.等级<69 then
                        添加最后对话(数字id,"你的等级不足69级，无法购买")
                        return
                      end
                      系统处理类:获取商店变异召唤兽(数字id)
              elseif 事件=="刷新变异召唤兽" then
                      local 银子=800000
                      if 玩家数据[数字id].角色.数据.银子<银子 then
                         添加最后对话(数字id,"你身上的银子不够")
                      else
                          玩家数据[数字id].角色:扣除银子(银子,"商会召唤兽刷新",1)
                          if not 变异商店bb[数字id] then
                            变异商店bb[数字id]={}
                            变异商店bb[数字id].刷新=0
                          end
                          local 刷新=变异商店bb[数字id].刷新+1
                          常规提示(数字id,"刷新召唤兽成功")
                          系统处理类:刷新商店变异召唤兽(数字id)
                          变异商店bb[数字id].刷新=刷新
                          发送数据(玩家数据[数字id].连接id,131.1,变异商店bb[数字id])
                      end
              elseif 事件=="购买三级药品" then
                        玩家数据[数字id].商品列表=商店处理类.商会三药
                        发送数据(玩家数据[数字id].连接id,9,{商品=商店处理类.商会三药,银子=玩家数据[数字id].角色.数据.银子,名称="购买三药"})
              elseif 事件=="购买烹饪物品" then
                       玩家数据[数字id].商品列表=商店处理类.商会烹饪
                       发送数据(玩家数据[数字id].连接id,9,{商品=商店处理类.商会烹饪,银子=玩家数据[数字id].角色.数据.银子,名称="购买烹饪"})
              end
        elseif 名称=="月老" then
                if 事件=="我们结婚啦" and 共享货币[玩家数据[数字id].账号] then
                      local 队伍id = 玩家数据[数字id].队伍
                      if 玩家数据[数字id].队伍 == 0 then
                          添加最后对话(数字id,"你是要与自己结婚么(结婚请组队前来)")
                          return
                      elseif #队伍数据[队伍id].成员数据 < 2 or #队伍数据[队伍id].成员数据 >2 then
                            添加最后对话(数字id,"结婚需要2个人组队(结婚请组队前来)")
                            return
                      elseif 玩家数据[数字id].角色.数据.结婚 ~= nil then
                              添加最后对话(数字id,"你是想犯重婚罪啊！1！！！")
                              return
                      elseif 玩家数据[队伍数据[队伍id].成员数据[2]].角色.数据.结婚 ~= nil then
                              添加最后对话(数字id,"我看你头顶顶着一片草原捏！！！！")
                              return
                      elseif 共享货币[玩家数据[数字id].账号]:扣除仙玉(50000,"结婚",数字id) == false then
                              添加最后对话(数字id,"你个穷鬼没有钱还想结婚！(结婚需要5W点仙玉)")
                              return
                      else
                          local id2 = 队伍数据[队伍id].成员数据[2]
                          if 玩家数据[数字id].角色.数据.性别 == "男" and  玩家数据[id2].角色.数据.性别 == "女" then
                            玩家数据[数字id].角色.数据.结婚={老婆=玩家数据[id2].角色.数据.名称,账号=玩家数据[id2].账号,id=id2,日期=os.time()}
                            玩家数据[数字id].角色:添加称谓(玩家数据[id2].角色.数据.名称.."的相公")

                            玩家数据[id2].角色.数据.结婚={老公=玩家数据[数字id].角色.数据.名称,账号=玩家数据[数字id].账号,id=数字id,日期=os.time()}
                            玩家数据[id2].角色:添加称谓(玩家数据[数字id].角色.数据.名称.."的娘子")
                            广播消息({内容=string.format("#S(月下老人)#R/%s#Y与#R%s#Y在#R月下老人#Y处喜结连理,祝愿他们早生贵子！".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,玩家数据[id2].角色.数据.名称),频道="xt"})
                          elseif 玩家数据[数字id].角色.数据.性别 == "女" and  玩家数据[id2].角色.数据.性别 == "男" then
                            玩家数据[数字id].角色.数据.结婚={老公=玩家数据[id2].角色.数据.名称,账号=玩家数据[id2].账号,id=id2,日期=os.time()}
                            玩家数据[数字id].角色:添加称谓(玩家数据[id2].角色.数据.名称.."的娘子")

                            玩家数据[id2].角色.数据.结婚={老婆=玩家数据[数字id].角色.数据.名称,账号=玩家数据[数字id].账号,id=数字id,日期=os.time()}
                            玩家数据[id2].角色:添加称谓(玩家数据[数字id].角色.数据.名称.."的相公")
                            广播消息({内容=string.format("#S(月下老人)#R/%s#Y与%s在#R月下老人#Y处喜结连理,祝愿他们早生贵子！".."#"..取随机数(1,110),玩家数据[id2].角色.数据.名称,玩家数据[数字id].角色.数据.名称),频道="xt"})
                          else
                            添加最后对话(数字id,"你们这是有悖伦理啊！！！！")
                            return
                          end
                      end
                elseif 事件 == "我要协议离婚" then
                      local 队伍id = 玩家数据[数字id].队伍
                      if 玩家数据[数字id].角色.数据.结婚 == nil then
                            添加最后对话(数字id,"你个单身贵族,还想离婚。)")
                            return
                      elseif 玩家数据[数字id].队伍 == 0 then
                            添加最后对话(数字id,"你是要与自己协议离魂么(协议离魂请组队前来)")
                            return
                      elseif #队伍数据[队伍id].成员数据 < 2 or #队伍数据[队伍id].成员数据 >2 then
                            添加最后对话(数字id,"离婚需要2个人组队(协议离婚请组队前来)")
                            return
                      elseif 队伍数据[队伍id].成员数据[2] ~= 玩家数据[数字id].角色.数据.结婚.id then
                            添加最后对话(数字id,"你确定他是你的配偶？")
                            return
                      elseif 取银子(数字id)<20000000 then
                            添加最后对话(数字id,"这年头离婚没钱你也离不起凑合过吧(离婚需要扣除2千万银子)！")
                            return
                      else
                            玩家数据[数字id].角色:扣除银子(20000000,"离婚",1)
                            local id2 = 队伍数据[队伍id].成员数据[2]
                            if 玩家数据[数字id].角色.数据.结婚.老公 ~= nil then
                              玩家数据[数字id].角色:删除称谓(玩家数据[数字id].角色.数据.结婚.老公.."的娘子")
                              玩家数据[id2].角色:删除称谓(玩家数据[id2].角色.数据.结婚.老婆.."的相公")
                              常规提示(数字id,"#Y/你从此又过上了单身贵族的生活")
                              常规提示(id2,"#Y/你从此又过上了单身贵族的生活")
                              玩家数据[数字id].角色.数据.结婚 = nil
                              玩家数据[id2].角色.数据.结婚 = nil
                              玩家数据[id2].角色:刷新信息()
                              玩家数据[数字id].角色:刷新信息()
                            elseif 玩家数据[数字id].角色.数据.结婚.老婆 ~= nil then
                              玩家数据[数字id].角色:删除称谓(玩家数据[数字id].角色.数据.结婚.老婆.."的相公")
                              玩家数据[id2].角色:删除称谓(玩家数据[id2].角色.数据.结婚.老公.."的娘子")
                              常规提示(数字id,"#Y/你从此又过上了单身贵族的生活")
                              常规提示(id2,"#Y/你从此又过上了单身贵族的生活")
                              玩家数据[数字id].角色.数据.结婚 = nil
                              玩家数据[id2].角色.数据.结婚 = nil
                              玩家数据[id2].角色:刷新信息()
                              玩家数据[数字id].角色:刷新信息()
                            end
                      end
                elseif 事件 == "我要强制离婚" then
                        if 玩家数据[数字id].角色.数据.结婚 == nil then
                          添加最后对话(数字id,"你个单身贵族,还想离婚。)")
                          return
                        elseif 取银子(数字id)<200000000 then
                          添加最后对话(数字id,"这年头离婚没钱你也离不起凑合过吧(离婚需要扣除2E银子)！")
                          return
                        else
                            玩家数据[数字id].角色:扣除银子(200000000,"离婚",1)
                            if 玩家数据[数字id].角色.数据.结婚.老公 ~= nil then
                              玩家数据[数字id].角色:删除称谓(玩家数据[数字id].角色.数据.结婚.老公.."的娘子")
                              常规提示(数字id,"#Y/你从此又过上了单身贵族的生活")
                              玩家数据[数字id].角色.数据.结婚 = nil
                              玩家数据[数字id].角色:刷新信息()
                            elseif 玩家数据[数字id].角色.数据.结婚.老婆 ~= nil then
                              玩家数据[数字id].角色:删除称谓(玩家数据[数字id].角色.数据.结婚.老婆.."的相公")
                              常规提示(数字id,"#Y/你从此又过上了单身贵族的生活")
                              玩家数据[数字id].角色.数据.结婚 = nil
                              玩家数据[数字id].角色:刷新信息()
                            end
                        end
                    end
        elseif 名称=="长安导游" then
                if 事件=="开启新手指引" then
                    添加最后对话(数字id,"20级后首要获取银子和经验的方式就是做#Y师门任务")
                    任务数据[玩家数据[数字id].角色:取任务(402)].进程=3
                    玩家数据[数字id].角色:刷新任务跟踪()
                elseif 事件=="领取奖励" then
                    local 任务id=玩家数据[数字id].角色:取任务(402)
                    玩家数据[数字id].角色:取消任务(任务id)
                    玩家数据[数字id].角色:添加经验(10000,"新手指引")
                    玩家数据[数字id].角色:添加银子(10000,"新手指引",1)
                    添加最后对话(数字id,"当你达到30级后,可以通过\n#G抓鬼,押镖,江湖任务,官职任务,藏宝图,科举,封妖,三界悬赏令,帮派任务#进行提升\n还有更多限时活动,请打开#Y梦幻指引界面#进行查看!")
                end
        elseif 名称=="召唤兽进阶熊猫" then
              if 事件=="我来给召唤兽换进阶造型" then
                    玩家数据[数字id].召唤兽:进阶造型处理(数字id)
              elseif 事件=="还原宠物染色数据" then
                    玩家数据[数字id].召唤兽:染色还原(数字id)
              -- elseif 事件=="给坐骑染色" then
              --           if 玩家数据[数字id].角色.数据.坐骑==nil then
              --               发送数据(玩家数据[数字id].连接id,1501,{名称="召唤兽进阶熊猫",模型="召唤兽造型进阶",对话="少侠是来寻我开心的么？你要染色的坐骑呢！"})
              --               return
              --           elseif 玩家数据[数字id].角色.数据.坐骑.祥瑞 then
              --               发送数据(玩家数据[数字id].连接id,1501,{名称="召唤兽进阶熊猫",模型="召唤兽造型进阶",对话="少侠是来寻我开心的么？祥瑞无法染色！"})
              --               return
              --           end
              --           发送数据(玩家数据[数字id].连接id,80,玩家数据[数字id].角色.数据.坐骑)
              -- elseif 事件=="给坐骑饰品染色" then
              --           if 玩家数据[数字id].角色.数据.坐骑==nil then
              --               发送数据(玩家数据[数字id].连接id,1501,{名称="召唤兽进阶熊猫",模型="召唤兽造型进阶",对话="少侠是来寻我开心的么？你要染色的坐骑呢！"})
              --               return
              --           elseif 玩家数据[数字id].角色.数据.坐骑.祥瑞 then
              --               发送数据(玩家数据[数字id].连接id,1501,{名称="召唤兽进阶熊猫",模型="召唤兽造型进阶",对话="少侠是来寻我开心的么？祥瑞无法染色！"})
              --               return
              --           end
              --           if 玩家数据[数字id].角色.数据.坐骑.饰品==nil then
              --               发送数据(玩家数据[数字id].连接id,1501,{名称="召唤兽造型熊猫",模型="召唤兽造型进阶",对话="少侠是来寻我开心的么？你要染色的坐骑饰品呢！"})
              --               return
              --           end
              --           发送数据(玩家数据[数字id].连接id,81,玩家数据[数字id].角色.数据.坐骑)
              end
        elseif 名称=="国子监祭酒" then
              if 事件=="请帮我们建立师徒关系" then
                  if 玩家数据[数字id].队伍==0 or 玩家数据[数字id].队伍~=数字id or #队伍数据[玩家数据[数字id].队伍].成员数据~=2 then
                        添加最后对话(数字id,"请两人组队并由师傅担任队长后再来找我。")
                        return
                  elseif 玩家数据[数字id].角色.数据.等级<69 then
                        添加最后对话(数字id,"你的等级不足69级，无法担当育人子弟这个重任。")
                        return
                  else
                      if 师徒数据[数字id]~=nil then
                            local 选项={}
                            for n, v in pairs(师徒数据[数字id].当前) do
                               选项[#选项+1]=师徒数据[数字id].当前[n].名称
                            end
                            if #选项>=3 then
                                添加最后对话(数字id,"你同时最多只能教育三名弟子。")
                                return
                            end
                      else
                            师徒数据[数字id]={当前={},完成={},银子=0,积分=0,奖励=0,经验=0,仙玉=0,储备=0}
                      end
                      local 徒弟id=队伍数据[玩家数据[数字id].队伍].成员数据[2]
                      if 玩家数据[徒弟id].角色.数据.等级<50 or 玩家数据[徒弟id].角色.数据.等级>=60 then
                          添加最后对话(数字id,"只有等级>=50级且<=60级的角色才可以拜师。")
                          return
                      elseif 玩家数据[徒弟id].角色.数据.师傅id~=nil then
                          添加最后对话(数字id,"对方已经拜了他人为师，我劝你还是死了这条心吧。")
                          return
                      end
                      添加最后对话(数字id,"请等待对方确认是否愿意拜你为师。")
                      玩家数据[徒弟id].最后对话={编号=38}
                      玩家数据[徒弟id].最后对话.名称="国子监祭酒"
                      玩家数据[徒弟id].最后对话.模型="考官2"
                      添加最后对话(徒弟id,string.format("你将拜#G%s#W为师，一旦建立师徒关系后，身为徒弟的你将无法解除师徒关系。请确认是否同意拜其为师？",玩家数据[数字id].角色.数据.名称),{"我同意","我拒绝"})
                      玩家数据[徒弟id].拜师id=数字id
                  end
              elseif 事件=="我同意" then
                    if 玩家数据[数字id].队伍==0 or #队伍数据[玩家数据[数字id].队伍].成员数据~=2 or 玩家数据[数字id].拜师id~= 队伍数据[玩家数据[数字id].队伍].成员数据[1] then
                        添加最后对话(数字id,"对方已经离开了队伍。")
                        return
                    else
                        local 师傅id=玩家数据[数字id].拜师id
                        师徒数据[师傅id].当前[数字id]={名称=玩家数据[数字id].角色.数据.名称,id=数字id,积分=0,时间=os.time()}
                        玩家数据[数字id].角色.数据.师傅id=师傅id
                        添加最后对话(数字id,"恭喜你们成功建立了师徒关系")
                        添加最后对话(师傅id,"恭喜你们成功建立了师徒关系")
                        玩家数据[数字id].角色:添加称谓(玩家数据[师傅id].角色.数据.名称.."的徒弟")
                    end
              elseif 事件=="我们是来出师的" then
                    if 玩家数据[数字id].队伍==0 or 玩家数据[数字id].队伍~=数字id or #队伍数据[玩家数据[数字id].队伍].成员数据~=2  then
                        添加最后对话(数字id,"请两人组队并由徒弟担任队长后再来找我。")
                        return
                    elseif  师徒数据[队伍数据[玩家数据[数字id].队伍].成员数据[2]] == nil or 师徒数据[队伍数据[玩家数据[数字id].队伍].成员数据[2]].当前 == nil or 师徒数据[队伍数据[玩家数据[数字id].队伍].成员数据[2]].当前[数字id]==nil then
                        添加最后对话(数字id,"对方并不是你的师傅。请两人组队并由徒弟担任队长后再来找我")
                        return
                    elseif 玩家数据[数字id].角色.数据.等级<69 then
                        添加最后对话(数字id,"你的等级尚未达到69级，无法满足出师的要求。")
                        return
                    elseif 玩家数据[数字id].角色.数据.师傅id~=队伍数据[玩家数据[数字id].队伍].成员数据[2] then
                          添加最后对话(数字id,"对方并不是你的师傅。请两人组队并由徒弟担任队长后再来找我")
                          return
                    else
                         local 师傅id=队伍数据[玩家数据[数字id].队伍].成员数据[2]
                         玩家数据[数字id].角色:自定义银子添加("玩家出师")
                          local 获得物品={}
                          for i=1,#自定义数据.玩家出师 do
                            if 取随机数()<=自定义数据.玩家出师[i].概率 then
                               获得物品[#获得物品+1]=自定义数据.玩家出师[i]
                            end
                          end
                          获得物品=删除重复(获得物品)
                          if 获得物品~=nil then
                              local 取编号=取随机数(1,#获得物品)
                              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                  玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                  广播消息({内容=string.format("#S/(拜师学艺)#R/%s#Y在#R%s#Y的教导下不断成长，终于到了能独挡一面的地步。获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,玩家数据[师傅id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                              end
                          end
                          玩家数据[师傅id].角色:自定义银子添加("玩家师傅")
                          获得物品={}
                          for i=1,#自定义数据.玩家师傅 do
                            if 取随机数()<=自定义数据.玩家师傅[i].概率 then
                               获得物品[#获得物品+1]=自定义数据.玩家师傅[i]
                            end
                          end
                          获得物品=删除重复(获得物品)
                          if 获得物品~=nil then
                              local 取编号=取随机数(1,#获得物品)
                              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                  玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                              end
                          end
                          玩家数据[师傅id].角色.数据.出师数量 = 玩家数据[师傅id].角色.数据.出师数量 + 1
                          师徒数据[师傅id].当前[数字id]=nil
                          玩家数据[数字id].角色.数据.师傅id=nil
                          if 玩家数据[师傅id].角色.数据.出师数量==2 then
                            玩家数据[师傅id].角色:添加称谓("为人师表")
                          elseif 玩家数据[师傅id].角色.数据.出师数量==6 then
                            玩家数据[师傅id].角色:添加称谓("循循善诱")
                          elseif 玩家数据[师傅id].角色.数据.出师数量==12 then
                            玩家数据[师傅id].角色:添加称谓("诲人不倦")
                          elseif 玩家数据[师傅id].角色.数据.出师数量==20 then
                            玩家数据[师傅id].角色:添加称谓("厚德树人")
                          elseif 玩家数据[师傅id].角色.数据.出师数量==30 then
                            玩家数据[师傅id].角色:添加称谓("桃李天下")
                          elseif 玩家数据[师傅id].角色.数据.出师数量==100 then
                                  玩家数据[师傅id].角色:自定义银子添加("玩家出师一百")
                                  获得物品={}
                                  for i=1,#自定义数据.玩家出师一百 do
                                    if 取随机数()<=自定义数据.玩家出师一百[i].概率 then
                                       获得物品[#获得物品+1]=自定义数据.玩家出师一百[i]
                                    end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                      end
                                  end
                          elseif 玩家数据[师傅id].角色.数据.出师数量==200 then
                                    玩家数据[师傅id].角色:自定义银子添加("玩家出师二百")
                                    获得物品={}
                                    for i=1,#自定义数据.玩家出师二百 do
                                      if 取随机数()<=自定义数据.玩家出师二百[i].概率 then
                                         获得物品[#获得物品+1]=自定义数据.玩家出师二百[i]
                                      end
                                    end
                                    获得物品=删除重复(获得物品)
                                    if 获得物品~=nil then
                                        local 取编号=取随机数(1,#获得物品)
                                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                            玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                        end
                                    end
                          elseif 玩家数据[师傅id].角色.数据.出师数量==300 then
                                    玩家数据[师傅id].角色:自定义银子添加("玩家出师三百")
                                    获得物品={}
                                    for i=1,#自定义数据.玩家出师三百 do
                                      if 取随机数()<=自定义数据.玩家出师三百[i].概率 then
                                         获得物品[#获得物品+1]=自定义数据.玩家出师三百[i]
                                      end
                                    end
                                    获得物品=删除重复(获得物品)
                                    if 获得物品~=nil then
                                        local 取编号=取随机数(1,#获得物品)
                                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                            玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                        end
                                    end
                          elseif 玩家数据[师傅id].角色.数据.出师数量==400 then
                                    玩家数据[师傅id].角色:自定义银子添加("玩家出师四百")
                                    获得物品={}
                                    for i=1,#自定义数据.玩家出师四百 do
                                      if 取随机数()<=自定义数据.玩家出师四百[i].概率 then
                                         获得物品[#获得物品+1]=自定义数据.玩家出师四百[i]
                                      end
                                    end
                                    获得物品=删除重复(获得物品)
                                    if 获得物品~=nil then
                                        local 取编号=取随机数(1,#获得物品)
                                        if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                            玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                        end
                                    end
                          elseif 玩家数据[师傅id].角色.数据.出师数量==500 then
                                      玩家数据[师傅id].角色:自定义银子添加("玩家出师五百")
                                      获得物品={}
                                      for i=1,#自定义数据.玩家出师五百 do
                                        if 取随机数()<=自定义数据.玩家出师五百[i].概率 then
                                           获得物品[#获得物品+1]=自定义数据.玩家出师五百[i]
                                        end
                                      end
                                      获得物品=删除重复(获得物品)
                                      if 获得物品~=nil then
                                          local 取编号=取随机数(1,#获得物品)
                                          if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                              玩家数据[师傅id].道具:自定义给予道具(师傅id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          end
                                      end
                          end
                          添加最后对话(数字id,"恭喜你出师成功，对于师傅的教诲可不要忘记哟。")
                          添加最后对话(师傅id,"恭喜你成功教育了一名弟子。")
                      end
              elseif 事件=="查询我的教育记录" then
                    if 师徒数据[数字id]==nil then
                        添加最后对话(数字id,"你当前还没有教育过任何弟子。")
                        return
                    end
                    local 当前数据={}
                    for n, v in pairs(师徒数据[数字id].当前) do
                        当前数据[#当前数据+1]=师徒数据[数字id].当前[n].名称
                    end
                    local 当前=""
                    for n=1,#当前数据 do
                        当前=当前..当前数据[n]
                        if n~=#当前数据 then
                            当前=当前.."、"
                        end
                    end
                    if 当前=="" then
                          添加最后对话(数字id,string.format("你目前总共教育了#R%s#W名弟子，当前并没有教育弟子的记录。",玩家数据[数字id].角色.数据.出师数量))
                    else
                          添加最后对话(数字id,string.format("你目前总共教育了#R%s#W名弟子，当前正在教育的弟子为：#G%s",玩家数据[数字id].角色.数据.出师数量,当前))
                    end
              elseif 事件=="我是来解除师徒关系的" then
                    if 师徒数据[数字id]==nil then
                        添加最后对话(数字id,"你当前没有可解除关系的弟子。")
                        return
                    end
                    local 选项={}
                    for n, v in pairs(师徒数据[数字id].当前) do
                        选项[#选项+1]=师徒数据[数字id].当前[n].名称
                    end
                    if #选项>=1 then
                        添加最后对话(数字id,"只有距离拜师超过48小时的徒弟才可被解除师徒关系，请选择你要解除师徒关系的徒弟：",选项)
                        玩家数据[数字id].解除师徒=true
                    else
                        添加最后对话(数字id,"你当前没有可解除关系的弟子。")
                        return
                    end
              end
        elseif 名称=="殷丞相" then
              if 事件=="对!必须干死他!" then
                  xsjc2(数字id,39)
              end
        elseif 名称=="兰虎" then
                if 事件=="学习调息" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"调息",3,5)
                elseif 事件=="学习变化之术" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"变化之术",4,5)
                elseif 事件=="学习奇门遁甲" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"奇门遁甲",4,4)
                elseif 事件=="学习妙手空空" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"妙手空空",3,10)
                elseif 事件=="学习打坐" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"打坐",3,5)
                elseif 事件=="学习宝石工艺" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"宝石工艺",2,7)
                elseif 事件=="学习仙灵店铺" then
                      玩家数据[数字id].角色:学习剧情技能(数字id,"仙灵店铺",3,5)
                --elseif 事件=="学习翱翔" then
                    --     for i=1,#玩家数据[数字id].角色.数据.剧情技能 do
                    --       if 玩家数据[数字id].角色.数据.剧情技能[i].名称 =="翱翔" and 玩家数据[数字id].角色.数据.剧情技能[i].等级>=1 then
                    --         常规提示(数字id,"#Y/你的这项技能等级已达上限")
                    --          return
                    --       end
                    --     end
                    --     if 玩家数据[数字id].角色.数据.银子<500000000 then
                    --       常规提示(数字id,"#Y/你的银子不够5亿无法学习")
                    --       return
                    --     end
                    --     if 扣除点卡(60,数字id,"学习翱翔") then
                    --       玩家数据[数字id].角色:扣除银子(500000000,"学习翱翔",1)
                    --       玩家数据[数字id].角色:学习剧情技能(数字id,"翱翔",1,1)
                    --     end
                elseif 事件=="遗忘剧情技能" then
                        local 选项={}
                        for i=1,#玩家数据[数字id].角色.数据.剧情技能 do
                         -- if 玩家数据[数字id].角色.数据.剧情技能[i].名称~="翱翔" then
                            选项[#选项+1]=玩家数据[数字id].角色.数据.剧情技能[i].名称
                          --end
                        end
                        玩家数据[数字id].遗忘剧情技能 = true
                        发送数据(玩家数据[数字id].连接id,1501,{名称=玩家数据[数字id].角色.数据.名称,模型=玩家数据[数字id].角色.数据.模型,对话="你要遗忘那一个剧情技能,遗忘剧情技能需5000仙玉",选项=选项})
                        return
                elseif 事件=="兑换进阶变身卡(50W银子一次)" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="进阶变身卡"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="兰虎",类型="NPC",等级="无"})
                elseif 事件=="购买五色旗盒" then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你可以在我这里花费5000000两银子购买五色旗盒，请确认是否要购买五色旗盒？",选项={"确认购买","我没那么多的钱"}})
                elseif 事件=="确认购买" then
                      if 玩家数据[数字id].角色.数据.银子<5000000 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你没有那么多的银子"})
                        return
                      else
                        玩家数据[数字id].角色:扣除银子(5000000,"觉案购买五色旗盒",1)
                        玩家数据[数字id].道具:给予法宝(数字id,"五色旗盒")
                        发送数据(玩家数据[数字id].连接id,1501,{名称="觉岸",模型="男人_道士",对话="你花费5000000两银子成功购买了一个五色旗盒#1"})
                        return
                      end
                end
        elseif 名称=="门派闯关使者" then
              if 事件=="准备好了，请告诉我们第一关的挑战地点" then
                  if  活动次数查询(数字id,"门派闯关")==false then
                      return
                  end
                  任务处理类:添加闯关任务(数字id)
              elseif 事件 == "我要取消任务" then
                    if 玩家数据[数字id].角色:取任务(107)==0 then
                        添加最后对话(数字id,"你有在我这里领取任务吗？")
                    else
                        local 队伍id=玩家数据[数字id].队伍
                        for n=1,#队伍数据[队伍id].成员数据 do
                          玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(107))
                          添加最后对话(队伍数据[队伍id].成员数据[n],"已经成功帮你取消了任务。")
                        end
                    end
              end
        elseif 名称=="驿站老板" then
               if 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1110,82,87)
                end
        elseif 名称=="李将军" then
                if 事件=="我来帮你" then
                      任务处理类:添加官职任务(数字id)
                elseif 事件=="领取俸禄" then
                      添加最后对话(数字id,string.format("你当前共有#Y%s#W点官职贡献度，每点官职贡献度可以兑换俸禄5000两银子\n你当前一共可兑换#R%s#W两银子俸禄\n只有官职贡献度达到100点及以上才可领取俸禄\n每日只可以领取1次俸禄\n每次领取俸禄后都将消耗当前%s的官职贡献度\n请却是否要领取俸禄。",玩家数据[数字id].角色.数据.官职点,玩家数据[数字id].角色.数据.官职点*5000,"所欲"),{"确认领取俸禄","我再想想"})
                elseif 事件=="确认领取俸禄" then
                        if 玩家数据[数字id].角色.数据.官职点<100 then
                            添加最后对话(数字id,"你的官职贡献度没有达到100点，无法领取俸禄")
                            return
                        elseif 取年月日()==玩家数据[数字id].角色.俸禄时间 then
                              添加最后对话(数字id,"你本日已经领取过俸禄了，请明日再来吧")
                              return
                        else
                          -- 0914
                          local 银子=玩家数据[数字id].角色.数据.官职点*5000
                          玩家数据[数字id].角色:添加银子(银子,"官职领取俸禄",1)
                          玩家数据[数字id].角色.数据.官职点=qz(玩家数据[数字id].角色.数据.官职点*0)
                          添加最后对话(数字id,"领取俸禄成功，本次领取俸禄后你的官职贡献度为：#R/"..玩家数据[数字id].角色.数据.官职点)
                          玩家数据[数字id].角色.俸禄时间=取年月日()
                        end
                elseif 事件=="取消任务" then
                        if 玩家数据[数字id].角色:取任务(110)==0 then
                            常规提示(数字id,"#Y/你还没有领取过官职任务")
                        else
                            if 任务数据[玩家数据[数字id].角色:取任务(110)].分类==1 then
                              地图处理类:删除单位(任务数据[玩家数据[数字id].角色:取任务(110)].地图编号,任务数据[玩家数据[数字id].角色:取任务(110)].编号)
                            end
                            if 玩家数据[数字id].角色.官职间隔==nil then
                                玩家数据[数字id].角色.官职间隔=os.time()
                            elseif os.time()<玩家数据[数字id].角色.官职间隔 then
                                添加最后对话(数字id,"两次取消任务的间隔时间不能低于十分钟。")
                                return
                            end
                            玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(110))
                            任务数据[玩家数据[数字id].角色:取任务(110)]=nil
                            常规提示(数字id,"#Y/你取消了官职任务，十分钟内无法再次领取官职任务，同时你的官职任务次数也被清空了")
                            玩家数据[数字id].角色.数据.官职次数=0
                            玩家数据[数字id].角色.官职间隔=os.time()+600
                        end
                end
        elseif 名称=="圣山传送人" then
                if 事件=="是的我要去" then
                  地图处理类:npc传送(数字id,1205,120,97)
                end
        elseif 名称=="皇宫护卫" then
                if 事件=="上交心魔宝珠" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="上交心魔宝珠"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="皇宫护卫",类型="NPC",等级="无"})
                elseif 事件=="我来领取赏金任务" then
                    if 玩家数据[数字id].角色:取任务(6)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="皇宫护卫",模型="护卫",对话="你已经领取过赏金任务了#24"})
                        return
                    elseif 玩家数据[数字id].角色.数据.等级<15 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="皇宫护卫",模型="护卫",对话="只有大于15级的玩家才可领取赏金任务，你就不要来捣乱了#4"})
                          return
                    end
                    任务处理类:设置赏金任务(数字id)
                end
        elseif 名称=="礼部侍郎" then
                if 事件=="我要参加科举" then
                    游戏活动类:科举条件检测(id,数字id)
                end
        elseif 名称=="御林军" then
                if 事件=="领取一小时精修时间" then
                    if  玩家数据[数字id].角色:取任务(7756)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话=" #Y3倍经验丹效果#R/不能重复领取精修"})
                    else
                        任务处理类:添加精修时间(1,1,数字id)
                        if 玩家数据[数字id].角色.数据.多角色操作 and 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                             for i=2,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                  local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                  if 玩家数据[临时id] and 玩家数据[临时id].子角色操作 and 玩家数据[临时id].子角色操作==数字id then
                                     if 玩家数据[临时id].角色:取任务(7756)~=0 then
                                        常规提示(数字id,"#Y玩家#R"..玩家数据[临时id].角色.数据.名称.."#Y3倍经验丹效果#R不能重复领取精修")
                                     else
                                        任务处理类:添加精修时间(1,1,临时id)
                                     end
                                  end
                             end
                        end
                    end
                end
        elseif 名称=="袁天罡" then
                if 事件=="人物洗点" then
                    if 玩家数据[数字id].角色.数据.洗点次数==0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="你当前拥有1次免费重置人物属性点的机会，请先卸下人物装备、灵饰。请确认是否需要重置人物属性点？",选项={"重置人物属性点","什么也不做"}})
                    else
                          发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="你的免费重置人物属性点的机会已经用完了。现在需要消耗#R2000000#W两银子才可重置人物属性点，请先卸下人物装备、灵饰。请确认是否需要重置人物属性点？",选项={"重置人物属性点","什么也不做"}})
                    end
                elseif 事件=="领取侠义任务" then
                      任务处理类:侠义任务(数字id)
                -- elseif 事件=="勇闯镇妖塔" then
                --   对话="少侠是否已经准备好验证自己的实力？"
                --   xx={"我已准备好","让我再想想"}
                --   发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=对话,选项=xx})
                -- elseif 事件=="我已准备好" then
                --   local xy=地图处理类.地图坐标[1009]:取随机点()
                --   地图处理类:npc传送(数字id,1009,xy.x,xy.y)
                elseif 事件=="重置人物属性点" then
                      玩家数据[数字id].角色:洗点操作(数字id)
                elseif 事件=="门派转换" then
                    local  对话内容 = "穿戴装备转换会让装备绑定角色,是否转换?"
                    local  对话选项 = {"确定角色门派转换","什么也不做"}
                    local 对话模型=玩家数据[数字id].角色.数据.模型
                    local 对话名称=玩家数据[数字id].角色.数据.名称
                    发送数据(id,1501,{名称="袁天罡",模型="袁天罡",选项=对话选项,对话=对话内容})
                elseif 事件=="确定角色门派转换" then
                           发送数据(id,124,{模式=false})
                           玩家数据[数字id].转换门派模式=false
                elseif 事件=="法术认证" then
                        --玩家数据[数字id].召唤兽:法术认证(玩家数据[数字id].连接id,数字id)
                         local 取编号=玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].角色.数据.参战宝宝.认证码)
                          if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil  then
                               常规提示(数字id,"请将要法术认证的召唤兽参战")
                               return
                          elseif 取银子(数字id) < 1000000 then
                               常规提示(数字id,"你的钱不够100万无法取消！")
                               return
                          elseif 玩家数据[数字id].召唤兽:是否有装备(取编号) then
                                常规提示(数字id,"请先卸下召唤兽所穿戴的装备")
                                return
                          elseif  玩家数据[数字id].召唤兽.数据[取编号].统御 ~= nil then
                                常规提示(数字id,"该召唤兽处于统御状态,请解除统御后再进行此操作")
                                return
                          elseif 玩家数据[数字id].召唤兽.数据[取编号].种类=="神兽" then
                              常规提示(数字id,"神兽无法认证法术")
                              return
                          end
                          local 认证法术={}
                          local 临时选项={}
                          if #玩家数据[数字id].召唤兽.数据[取编号].技能~=nil then
                                for i=1,#玩家数据[数字id].召唤兽.数据[取编号].技能 do
                                    if 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="雷击" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="水攻" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="烈火"
                                      or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="落岩" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="奔雷咒" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="水漫金山"
                                      or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="地狱烈火" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="泰山压顶" or 玩家数据[数字id].召唤兽.数据[取编号].技能[i]=="上古灵符" then
                                      认证法术[#认证法术+1]=玩家数据[数字id].召唤兽.数据[取编号].技能[i]
                                    end
                                end
                          end
                          if #认证法术>=1 then
                              for i=1,#认证法术 do
                                  临时选项[i]=认证法术[i]
                                  临时选项[#认证法术+1]="不认证了"
                                  发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="少侠要对该召唤兽的哪个技能进行认证呢？",选项=临时选项})
                              end
                              玩家数据[数字id].宝宝认证技能 = 玩家数据[数字id].召唤兽.数据[取编号].认证码
                          else
                              添加最后对话(数字id,"该召唤兽没有可认证的法术哦")
                              玩家数据[数字id].宝宝认证技能 =nil
                          end
                elseif 事件=="水攻" or 事件=="烈火" or 事件=="雷击" or 事件=="落岩" or 事件=="奔雷咒" or 事件=="水漫金山" or 事件=="地狱烈火" or 事件=="泰山压顶" or 事件=="上古灵符" then
                      玩家数据[数字id].召唤兽:法术认证处理(玩家数据[数字id].连接id,数字id,事件)
                elseif 事件=="取消法术认证" then
                      发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="少侠是要对参战的召唤兽取消认证技能么？",选项={"确定取消认证技能","让我再想想"}})
                elseif 事件=="确定取消认证技能" then
                      玩家数据[数字id].召唤兽:取消法术认证(玩家数据[数字id].连接id,数字id)
                -- elseif 事件=="补充法宝灵气" then
                      -- 玩家数据[数字id].给予数据={类型=1,id=0,事件="法宝补充灵气"}
                      -- 发送数据(id,3507,{道具=玩家数据[数字id].道具:索要法宝2(数字id,0),名称="五色旗盒",类型="法宝",等级="无"})
                elseif 事件=="角色改名" then
                       发送数据(玩家数据[数字id].连接id,142,1)
                elseif 事件=="武器染色" and 共享货币[玩家数据[数字id].账号] then
                        if 玩家数据[数字id].角色.数据.装备[3]==nil or 玩家数据[数字id].角色.数据.装备[3]==0 then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="少侠是来寻我开心的么？你要染色的武器呢！"})
                            return
                        end
                        发送数据(玩家数据[数字id].连接id,76,{仙玉= 共享货币[玩家数据[数字id].账号].仙玉})
                elseif 事件=="关于兽决兑换" then
                        local 对话="你可以用两本用不到的高级兽决置换随机技能的高级兽决？特殊和超级兽决可以兑换成相应数量的碎片"
                        local xx={"确认兑换高级兽决","我要兑换兽决碎片","什么也不做"}
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=对话,选项=xx})
                elseif 事件 == "关于神兽兑换" then
                        local 兑换列表 = {
                            {key = "灵兜兜兑换", fields = {"数量", "名称"}},
                            {key = "神兽兑换一", fields = {"数量", "名称", "所需"}},
                            {key = "神兽兑换二", fields = {"数量", "名称", "所需"}},
                            {key = "神兽兑换三", fields = {"数量", "名称", "所需"}}
                        }
                        for _, 配置 in ipairs(兑换列表) do
                            if not 自定义数据[配置.key] then
                                  --error(string.format("神兽兑换配置错误：缺少%s配置", 配置.key))
                                  __gge.print(true,12,string.format("神兽兑换配置错误：缺少%s配置\n", 配置.key))
                                return
                            end
                            for _, field in ipairs(配置.fields) do
                                if not 自定义数据[配置.key][field] then
                                     --error(string.format("%s配置缺少字段：%s", 配置.key, field))
                                     __gge.print(true,12,string.format("%s配置缺少字段：%s\n", 配置.key, field))
                                    return
                                end
                            end
                        end
                        发送数据(玩家数据[数字id].连接id, 1501, {
                            名称 = "袁天罡",
                            模型 = "袁天罡",
                            对话 = string.format("你可以用#Y%d个#R灵兜兜#W兑换%s，#W灵兜兜可通过打怪爆出，",
                                自定义数据.灵兜兜兑换.数量, 自定义数据.灵兜兜兑换.名称),
                            选项 = {"灵兜兜兑换神兽","一代兑换二代","二代兑换三代","三代兑换四代", "什么也不做"}
                        })

                elseif 事件=="我要兑换兽决碎片" then
                         if 自定义数据.兽决兑换配置==nil then
                            自定义数据.兽决兑换配置={特殊=45,超级=45}
                         end
                         local 对话="每本特殊兽决可以兑换#Y"..自定义数据.兽决兑换配置.特殊.."#W个特殊兽决碎片,每本超级兽决可以兑换#Y"..自定义数据.兽决兑换配置.超级.."#W个超级兽决碎片"
                         local xx={"特殊兽决兑换碎片","超级兽决兑换碎片","什么也不做"}
                         发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=对话,选项=xx})
                elseif 事件=="关于召唤兽内丹" then
                        local 对话="你可以用3个用不到的召唤兽内丹置换随机技能的召唤兽内丹？"
                        local xx={"确认兑换低级内丹","确认兑换高级内丹","什么也不做"}
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=对话,选项=xx})
                elseif 事件=="神兽天生技能更换" then
                      local 对话="你可以用188个神兜兜和两个特殊兽决更换神兽天生技能，可以使用888个神兜兜和两个特殊兽决对4个以下天生技能的神兽添加一个天生技能(如召唤兽已有特殊兽决携带的技能，添加成功后会删除召唤兽携带的特殊技能)"
                      local xx={"188个神兜兜更换天生","888个神兜兜添加天生"}
                      发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话=对话,选项=xx})
                elseif 事件=="188个神兜兜更换天生" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="188个神兜兜"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="888个神兜兜添加天生" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="888个神兜兜"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="神兽成长资质提升" and 自定义数据.神兽提升资质~=nil and 自定义数据.神兽提升资质 then
                        local 选项={}
                        for n=1,#玩家数据[数字id].召唤兽.数据 do
                          if 玩家数据[数字id].召唤兽.数据[n].种类=="神兽" then
                           选项[#选项+1]=string.format("%s,等级:%s",玩家数据[数字id].召唤兽.数据[n].名称,玩家数据[数字id].召唤兽.数据[n].等级)
                          end
                        end
                        if #选项 < 1 then
                          添加最后对话(数字id,"你没有可以选择的召唤兽")
                          玩家数据[数字id].神兽成长资质提升 = nil
                          return
                        end
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择要提升的召唤兽",选项=选项})
                        玩家数据[数字id].神兽成长资质提升 =  true
                elseif 事件=="我确定提升该召唤兽的资质" and 自定义数据.神兽提升资质~=nil and 自定义数据.神兽提升资质 and 玩家数据[数字id].神兽成长提升~=nil and 玩家数据[数字id].神兽成长提升.认证码~=nil then
                       local 取编号 = 玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].神兽成长提升.认证码)
                       if 取编号 ~= 0 and 玩家数据[数字id].召唤兽.数据[取编号]~=nil then
                            玩家数据[数字id].给予数据={类型=1,id=0,事件="神兽成长资质提升"}
                            发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                       end
                -- elseif 事件=="999个神兜兜和1.5成长神兽兑换1.75成长神兽" then
                       -- 玩家数据[数字id].给予数据={类型=1,id=0,事件="神兽成长资质提升"}
                       -- 发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="袁天罡",等级="无"})
                elseif 事件=="确认兑换高级兽决" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="兽决兑换兽决"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="特殊兽决兑换碎片" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="特殊兑换碎片"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="超级兽决兑换碎片" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="超级兑换碎片"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="灵兜兜兑换神兽" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="灵兜兜兑换神兽"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="一代兑换二代" then
                        发送数据(玩家数据[数字id].连接id, 1501, {
                        名称 = "袁天罡",
                        模型 = "袁天罡",
                        对话 = string.format("你可以用#Y%d个#R%s#W兑换%s.",
                            自定义数据.神兽兑换一.数量, 自定义数据.神兽兑换一.所需,自定义数据.神兽兑换一.名称),
                        选项 = {"确定一代兑换二代", "什么也不做"}
                    })
                elseif 事件=="确定一代兑换二代" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="神兽兑换一"}
                        发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})

                 elseif 事件=="二代兑换三代" then
                        发送数据(玩家数据[数字id].连接id, 1501, {
                        名称 = "袁天罡",
                        模型 = "袁天罡",
                        对话 = string.format("你可以用#Y%d个#R%s#W兑换%s.",
                            自定义数据.神兽兑换二.数量, 自定义数据.神兽兑换二.所需,自定义数据.神兽兑换二.名称),
                        选项 = {"确定二代兑换三代", "什么也不做"}
                    })
                elseif 事件=="确定二代兑换三代" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="神兽兑换二"}
                        发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})

                elseif 事件=="三代兑换四代" then
                        发送数据(玩家数据[数字id].连接id, 1501, {
                        名称 = "袁天罡",
                        模型 = "袁天罡",
                        对话 = string.format("你可以用#Y%d个#R%s#W兑换%s.",
                            自定义数据.神兽兑换三.数量, 自定义数据.神兽兑换三.所需,自定义数据.神兽兑换三.名称),
                        选项 = {"确定三代兑换四代", "什么也不做"}
                    })
                elseif 事件=="确定三代兑换四代" then
                        玩家数据[数字id].给予数据={类型=1,id=0,事件="神兽兑换三"}
                        发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})





                elseif 事件=="确认兑换低级内丹" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="低内丹兑换低内丹"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="确认兑换高级内丹" then
                      玩家数据[数字id].给予数据={类型=1,id=0,事件="高内丹兑换高内丹"}
                      发送数据(玩家数据[数字id].连接id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称="袁天罡",类型="NPC",等级="无"})
                elseif 事件=="变异召唤兽添加技能格子" then
                        local 选项={}
                        for n=1,#玩家数据[数字id].召唤兽.数据 do
                            if 玩家数据[数字id].召唤兽.数据[n].种类=="变异" then
                                选项[#选项+1]=string.format("%s,等级:%s",玩家数据[数字id].召唤兽.数据[n].名称,玩家数据[数字id].召唤兽.数据[n].等级)
                            end
                        end
                        if #选项 < 1 then
                          添加最后对话(数字id,"你没有可以选择的召唤兽")
                          玩家数据[数字id].变异召唤兽添加格子 = nil
                          return
                        end
                         if 自定义数据.更改变异格子==nil then
                            自定义数据.更改变异格子={货币类型="银子",数量=100000000}
                         end
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择要添加技能格子的召唤兽,变异召唤兽添加技能需要#R/"..自定义数据.更改变异格子.数量.."#W"..自定义数据.更改变异格子.货币类型,选项=选项})
                        玩家数据[数字id].变异召唤兽添加格子 =  true
                elseif 事件=="神兽造型相关操作" then
                          玩家数据[数字id].更换模型进阶=nil
                         if 自定义数据.更换模型配置==nil then
                            自定义数据.更换模型配置={货币类型="点卡",数量=100}
                         end
                         发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择更换的模型类型,",选项={"普通模型","进阶模型","神兽模型"}})

                elseif 事件=="普通模型" then
                           local 选项={}
                           for k,v in pairs(所有召唤兽模型) do
                              选项[#选项+1]=k
                           end
                           玩家数据[数字id].更换模型进阶=nil
                           发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择参战召唤兽更换的造型,更换造型需#R/"..自定义数据.更换模型配置.数量.."#W/"..自定义数据.更换模型配置.货币类型,选项=选项})
                elseif 事件=="进阶模型" then
                          local 选项={}
                          for k,v in pairs(所有召唤兽模型) do
                                选项[#选项+1]=k
                          end
                         玩家数据[数字id].更换模型进阶=true
                         发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择参战召唤兽更换的造型,更换造型需#R/"..自定义数据.更换模型配置.数量.."#W/"..自定义数据.更换模型配置.货币类型,选项=选项})
                elseif 事件=="神兽模型" then
                        local 选项={}
                        for k,v in pairs(所有神兽模型) do
                            选项[#选项+1]=k
                        end
                        玩家数据[数字id].更换模型进阶=nil
                        发送数据(玩家数据[数字id].连接id,1501,{名称="袁天罡",模型="袁天罡",对话="请选择参战召唤兽更换的造型,更换造型需#R/"..自定义数据.更换模型配置.数量.."#W/"..自定义数据.更换模型配置.货币类型,选项=选项})
                elseif 所有神兽模型[事件] or 所有召唤兽模型[事件] then
                        local 取编号=玩家数据[数字id].召唤兽:取编号(玩家数据[数字id].角色.数据.参战宝宝.认证码)
                        if 取编号 == 0 or 玩家数据[数字id].召唤兽.数据[取编号]==nil then
                            玩家数据[数字id].更换模型进阶=nil
                           常规提示(数字id,"请先将更换造型的召唤兽参战")
                            return
                        end
                        if 玩家数据[数字id].召唤兽.数据[取编号].种类~="神兽" then
                            玩家数据[数字id].更换模型进阶=nil
                            常规提示(数字id,"只有神兽可以改变造型了")
                            return
                        end

                        if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.更换模型配置.货币类型,自定义数据.更换模型配置.数量,"更换召唤兽模型",1) then
                           if 玩家数据[数字id].更换模型进阶 and 所有召唤兽模型[事件] then
                              玩家数据[数字id].召唤兽.数据[取编号].模型 ="进阶"..事件
                           else
                               玩家数据[数字id].召唤兽.数据[取编号].模型 =事件
                           end
                           玩家数据[数字id].召唤兽:刷新信息(取编号)
                           常规提示(数字id,"恭喜你，该召唤兽更改为新的造型了")
                        else
                           常规提示(数字id,"未达到更换条件")
                        end
                        玩家数据[数字id].更换模型进阶=nil
                end
        elseif 名称=="马副将" then
                if 事件=="领取一小时" then
                    if 玩家数据[数字id].角色:取任务(7755)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你有#Y2倍经验丹效果#R不能重复领取双倍"})
                    else
                        任务处理类:添加双倍(1,1,数字id)
                        if 玩家数据[数字id].角色.数据.多角色操作 and 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                             for i=2,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                  local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                  if 玩家数据[临时id] and 玩家数据[临时id].子角色操作 and 玩家数据[临时id].子角色操作==数字id then
                                      if 玩家数据[临时id].角色:取任务(7755)~=0 then
                                          常规提示(数字id,"#Y玩家#R"..玩家数据[临时id].角色.数据.名称.."#Y有2倍经验丹效果#R不能重复领取双倍")
                                       else
                                          任务处理类:添加双倍(1,1,临时id)
                                       end
                                  end
                             end
                        end

                    end
                elseif 事件=="领取两小时" then
                    if 玩家数据[数字id].角色:取任务(7755)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你有#Y2倍经验丹效果#R不能重复领取双倍"})
                    else
                         任务处理类:添加双倍(2,1,数字id)
                          if 玩家数据[数字id].角色.数据.多角色操作 and 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                             for i=2,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                  local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                  if 玩家数据[临时id] and 玩家数据[临时id].子角色操作 and 玩家数据[临时id].子角色操作==数字id then
                                      if 玩家数据[临时id].角色:取任务(7755)~=0 then
                                          常规提示(数字id,"#Y玩家#R"..玩家数据[临时id].角色.数据.名称.."#Y有2倍经验丹效果#R不能重复领取双倍")
                                       else
                                          任务处理类:添加双倍(2,1,临时id)
                                       end
                                  end
                             end
                          end
                    end
                elseif 事件=="领取四小时" then
                      if 玩家数据[数字id].角色:取任务(7755)~=0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你有#Y2倍经验丹效果#R不能重复领取双倍"})
                      else
                          任务处理类:添加双倍(4,1,数字id)
                          if 玩家数据[数字id].角色.数据.多角色操作 and 玩家数据[数字id].队伍 and 玩家数据[数字id].队伍~=0 then
                               for i=2,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                    local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                    if 玩家数据[临时id] and 玩家数据[临时id].子角色操作 and 玩家数据[临时id].子角色操作==数字id then
                                      if 玩家数据[临时id].角色:取任务(7755)~=0 then
                                          常规提示(数字id,"#Y玩家#R"..玩家数据[临时id].角色.数据.名称.."#Y有2倍经验丹效果#R不能重复领取双倍")
                                       else
                                          任务处理类:添加双倍(4,1,临时id)
                                       end
                                  end
                               end
                          end
                      end
                elseif 事件=="冻结双倍时间" then
                      local 任务id=玩家数据[数字id].角色:取任务(2)
                      if 任务id==0 or 任务数据[任务id]==nil then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你似乎还没有在我这里领取过双倍时间。"})
                        -- elseif os.time()-双倍数据[数字id].间隔<600 then
                        -- 发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你冻结双倍时间的操作太频繁了。是我提不动刀了还是你想与我们整个大唐为敌？识相点的就等10分钟后再试。"})
                      else
                        双倍数据[数字id].冻结=取分(任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="已经帮你冻结了#R/"..双倍数据[数字id].冻结.."#W/分钟的双倍时间。记得在24点前来找我解冻，否则所冻结的双倍时间将被直接清空。"})
                        玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(2))
                        玩家数据[数字id].角色:刷新任务跟踪()
                        双倍数据[数字id].间隔=os.time()
                      end
                elseif 事件=="恢复双倍时间" then
                      if 双倍数据[数字id].冻结==0 then
                        发送数据(玩家数据[数字id].连接id,1501,{名称="马副将",模型="男人_马副将",对话="你似乎还没有在我这里冻结过双倍时间。"})
                      else
                        任务处理类:添加双倍(双倍数据[数字id].冻结,2,数字id)
                        双倍数据[数字id].冻结=0
                      end

                end

        elseif 名称=="超级红孩儿" then
                   if 事件=="开启嘉年华副本" then
                      嘉年华:设置副本任务(数字id)
                  elseif 事件=="嘉年华抽奖" then
                        if not 玩家数据[数字id].角色.数据.嘉年华 then 玩家数据[数字id].角色.数据.嘉年华 =0 end
                        玩家数据[数字id].抽中嘉年华=nil
                        发送数据(玩家数据[数字id].连接id,147,{道具=自定义数据.嘉年华配置,次数=玩家数据[数字id].角色.数据.嘉年华})
                  elseif 事件=="我想了解下" then
                       发送数据(玩家数据[数字id].连接id,1501,{名称="超级红孩儿",模型="超级红孩儿",对话="活动可以通过副本,或者打败美味可以获得嘉年华积分,嘉年华积分可以用来抽奖!每10积分抽奖1次!"})
                   end
        elseif 名称=="哪吒" then
                   if 事件=="我来和你一较高下，看谁说了算" then
                      任务处理类:设置哪吒副本(数字id)
                   end

        elseif 名称=="杜少海" then
                if 事件=="领取任务" then
                      任务处理类:添加江湖任务(数字id)
                elseif 事件=="我要取消任务" then
                        if 玩家数据[数字id].角色:取任务(11)==0 then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="杜少海",模型="男人_店小二",对话="你有在我这里领取任务吗？"})
                            return
                        elseif 玩家数据[数字id].队伍==0 then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="杜少海",模型="男人_店小二",对话="必须组队才能取消任务"})
                            return
                        end
                        local 队伍id=玩家数据[数字id].队伍
                        for n=1,#队伍数据[队伍id].成员数据 do
                            玩家数据[队伍数据[队伍id].成员数据[n]].角色.数据.江湖次数=0
                            玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(11))
                            发送数据(玩家数据[数字id].连接id,1501,{名称="杜少海",模型="男人_店小二",对话="已经成功帮你取消了任务。"})
                        end
                end
        elseif 名称=="御林军左统领" then
                if 事件=="真是无法无天！我这就去帮你把他们抓回来" then
                      任务处理类:添加皇宫飞贼任务(数字id)
                elseif 事件=="我打算去抓住这次活动的幕后黑手！" then
                      任务处理类:添加皇宫贼王任务(数字id)
                elseif 事件=="我来取消任务" then
                      if 玩家数据[数字id].角色:取任务(12)==0 then
                          发送数据(玩家数据[数字id].连接id,1501,{名称="御林军左统领",模型="护卫",对话="你有在我这里领取任务吗？"})
                      else
                          local 队伍id=玩家数据[数字id].队伍
                          for n=1,#队伍数据[队伍id].成员数据 do
                            皇宫飞贼[队伍数据[队伍id].成员数据[n]]=nil
                            皇宫飞贼.贼王[队伍数据[队伍id].成员数据[n]]=nil
                            玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(12))
                            发送数据(玩家数据[数字id].连接id,1501,{名称="御林军左统领",模型="护卫",对话="已经成功帮你取消了任务。"})
                          end
                      end
                end
        elseif 名称=="装备收购商" then
                if 事件=="出售" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="装备出售"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                end
        elseif 名称=="装备鉴定商" then
                if 事件=="给予" then
                    玩家数据[数字id].给予数据={类型=1,id=0,事件="装备鉴定"}
                    发送数据(id,3507,{道具=玩家数据[数字id].道具:索要道具1(数字id),名称=名称,类型="NPC",等级="无"})
                end
        elseif 名称 == "符石道人" then
                if 事件 == "我来给装备开运" then
                      发送数据(id,62,{道具=玩家数据[数字id].道具:索要道具1(数字id),类型=1})
                elseif 事件=="点化装备星位" then
                      发送数据(id,62,{道具=玩家数据[数字id].道具:索要道具1(数字id),类型=2})
                elseif 事件 == "我来合成符石" then
                      发送数据(id,79,玩家数据[数字id].道具:索要道具3(数字id))
                end
        elseif 名称 == "袁守城" then
                if 事件=="触发剧情" then
                    local 对话数据={}
                    对话数据.模型=玩家数据[数字id].角色.数据.造型
                    对话数据.名称=玩家数据[数字id].角色.数据.名称
                    对话数据.对话="请问老神仙,怎么才能帮助卷帘大将去除心魔?"
                    副本处理类:发送全队对话信息(数字id,对话数据)
                    对话数据.模型="男人_太上老君"
                    对话数据.名称="袁守城"
                    对话数据.对话="若你能找到被三星附体的人,魔,仙,并令他们的真神回归本位,否则就难了..."
                    对话数据.选项={"开启寻找三星的任务"}
                    副本处理类:发送全队对话信息(数字id,对话数据)
                elseif 事件=="触发后续" then
                    local 对话数据={}
                    对话数据.模型="男人_太上老君"
                    对话数据.名称="袁守城"
                    对话数据.对话="唉,此事说来话长,日前山人真魂出窍云游天庭,肉身被天衡星附体,正巧泾河龙王来与人赌胜,天衡星戏弄与他,那龙王为争一时闲气犯了天条,着人魏征斩首,龙子龙孙故而怪罪于我,价日来此吵闹"
                    副本处理类:发送全队对话信息(数字id,对话数据)
                    对话数据.模型="男人_太上老君"
                    对话数据.名称="袁守城"
                    对话数据.对话="我真魂归为后,已将天衡星擒住,天衡可是九宫阵不可缺失的一宫,我要好生看守天衡,日后把他交给能够复原九宫阵的英雄,倘若前去龙宫,那些龙子龙孙必定纠缠不休,恐怕会误了大事,所以我迟迟没有解释"
                    副本处理类:发送全队对话信息(数字id,对话数据)
                    对话数据.模型=玩家数据[数字id].角色.数据.造型
                    对话数据.名称=玩家数据[数字id].角色.数据.名称
                    对话数据.对话="我已经收集齐天心星和天英星了,就差天衡星了,赶紧给我吧!"
                    副本处理类:发送全队对话信息(数字id,对话数据)
                    对话数据.模型="男人_太上老君"
                    对话数据.名称="袁守城"
                    对话数据.对话="那真是太好了!我这就将天衡星给你了!"
                    对话数据.选项={"该去解决卷帘大将心魔了"}
                    副本处理类:发送全队对话信息(数字id,对话数据)
                elseif 事件=="该去解决卷帘大将心魔了" then
                        if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 14 then
                           任务数据[玩家数据[数字id].角色:取任务(996)].进程=15
                           玩家数据[数字id].角色:刷新任务跟踪()
                        end
                elseif 事件=="开启寻找三星的任务" then
                    if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 4 then
                       任务数据[玩家数据[数字id].角色:取任务(996)].进程=5
                       玩家数据[数字id].角色:刷新任务跟踪()
                     end
                end
        elseif 名称 == "龙孙" then
                if 事件=="开启剧情战斗" then
                    战斗准备类:创建战斗(数字id,100259,1008610)
                end
        elseif 名称=="长安商人" then
                if 事件=="购买商品" then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[27]
                    self.初始金额 = 0
                    for k,v in pairs(玩家数据[数字id].角色.数据.道具) do
                          if 玩家数据[数字id].道具.数据[v] and 玩家数据[数字id].道具.数据[v].名称 == "帮派银票" then
                              self.初始金额 = 玩家数据[数字id].道具.数据[v].初始金额
                          end
                    end
                    发送数据(id,70,{商品=商店处理类.商品列表[27],银子=self.初始金额,道具=玩家数据[数字id].道具:索要道具2(数字id)})
                end
        elseif 名称=="宝石商人" then
                if 事件=="我想购买你那些珍品" then
                    玩家数据[数字id].商品列表=商店处理类.商品列表[51]
                    发送数据(id,9,{商品=商店处理类.商品列表[51],银子=玩家数据[数字id].角色.数据.银子})
                end
        elseif 名称=="节日礼物使者" then
                if 事件=="我要领取礼物" then
                      if 节日开关 then
                          if 玩家数据[数字id].角色.数据.等级 > 9 then
                              if 玩家数据[数字id].角色.数据.节日礼物==nil or 玩家数据[数字id].角色.数据.节日礼物~=os.date("%m月%d日") then
                                    玩家数据[数字id].角色.数据.节日礼物=os.date("%m月%d日")
                                    常规提示(数字id,"#Y/节日礼物领取成功.")
                                    local 经验=qz(玩家数据[数字id].角色.数据.等级*玩家数据[数字id].角色.数据.等级*60)
                                    local 银子=qz(玩家数据[数字id].角色.数据.等级*玩家数据[数字id].角色.数据.等级*50)
                                    玩家数据[数字id].角色:添加银子(银子,"节日银子",1)
                                    玩家数据[数字id].角色:添加经验(经验,"节日经验",1)
                                    玩家数据[数字id].道具:给予道具(数字id,"藏宝图",0)
                                    常规提示(数字id,"#Y/你获得了一张藏宝图")
                              else
                                  常规提示(数字id,"#Y/您已经领取了节日礼物.")
                              end
                          else
                              常规提示(数字id,"#Y/您的人物等级小于10级,暂时不能领取礼物")
                          end
                      else
                          常规提示(数字id,"#Y/未到节日时间.")
                      end
                elseif 事件=="不，我只是来看看" then
                      return
                end
        elseif 名称=="文韵墨香使者" then---------远方文韵墨香
                if 事件=="正闲着呢，请吩咐" then
                      玩家数据[数字id].角色:文韵任务(数字id,门派)
                elseif 事件=="我想了解本次活动" then
                        发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="考官2",对话="文韵活动每天可以做100次.每次获得1点文韵积分。可在文韵商城兑换物品."})
                elseif 事件=="我来使用积分" then
                        玩家数据[数字id].商品列表=商店处理类.文韵商店
                        发送数据(id,157.1,{商品=商店处理类.文韵商店,文韵积分=玩家数据[数字id].角色.数据.文韵积分})
                elseif 事件=="取消文韵墨香任务" then----------远方文韵墨香
                          local 文韵id=玩家数据[数字id].角色:取任务(112)
                          if 文韵id==0 then
                              添加最后对话(数字id,"请先领取文韵墨香任务#50")
                              return
                          else
                              if 玩家数据[数字id].角色.文韵间隔==nil then
                                玩家数据[数字id].角色.文韵间隔=os.time()
                              elseif os.time()<玩家数据[数字id].角色.文韵间隔 then
                                添加最后对话(数字id,"两次取消任务的间隔时间不能低于一分钟。")
                                return
                              end
                              玩家数据[数字id].角色.文韵间隔=os.time()+60
                              玩家数据[数字id].角色:取消任务(文韵id)
                              玩家数据[数字id].角色.数据.文韵次数=0
                              if 任务数据[文韵id].分类==5 or 任务数据[文韵id].分类==6 or 任务数据[文韵id].分类==7 then
                                地图处理类:删除单位(任务数据[文韵id].地图编号,任务数据[文韵id].编号)
                              end
                              任务数据[文韵id]=nil
                              添加最后对话(数字id,"你的文韵墨香任务已经帮你取消了，同时任务环数也一同被清空。")
                          end
                          return
                end
        elseif 名称=="超级神柚" then
                if 事件== "我的召唤兽需要你的赐福."then
                    发送数据(id,55,{召唤兽=self.数据,道具=玩家数据[数字id].道具:索要道具2(数字id)})
                elseif 事件=="我想了解下赐福" then
                    添加最后对话(数字id,"每个召唤兽可赐福4个超级技能,每次赐福消耗一个仙露丸子,可锁定2个超级技能.锁定技能需要相应的仙露进入战斗后自动进化!每个赐福技能有25%几率进入战斗后进化.(赐福的技能召唤兽必须自带有高级技能才能赐福进化成超级技能),")
                end
        elseif 名称=="雁塔地宫" then
                if 事件=="前往参加" then
                   地图处理类:npc传送(数字id,1009,30,33)
                end
        elseif 名称=="剑会天下主持人" then
                if 事件=="我要打听剑会天下的消息" then
                      发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_马副将",对话="剑会天下每个赛季奖励有九档次,1300以上积分没档次可以领取一次,一周结算一次赛季奖励"})
                elseif 事件=="我要领取赛季奖励" then
                        if 剑会天下[数字id]==nil then
                            添加最后对话(数字id,"你还没有剑会天下匹配积分")
                            return
                        elseif 剑会天下[数字id].当前积分<=1200 then
                                添加最后对话(数字id,"你的积分小于等于1200无法领取")
                                return
                        elseif 玩家数据[数字id].角色:取道具格子()>=14 then
                                添加最后对话(数字id,"#y/你的背包空间不足,请留出大于7个格子")
                                return
                        end
                        if not 剑会天下.奖励[数字id] then 剑会天下.奖励[数字id] ={} end

                        if 剑会天下[数字id].当前积分>=5400 and 剑会天下.奖励[数字id][8] and not 剑会天下.奖励[数字id][9] then
                                  剑会天下.奖励[数字id][9]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会五千四",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会五千四 do
                                      if 取随机数()<=自定义数据.剑会五千四[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会五千四[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第九段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","武林高手","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("威震三界")

                        elseif 剑会天下[数字id].当前积分>=4800 and 剑会天下.奖励[数字id][7] and not 剑会天下.奖励[数字id][8] then
                                  剑会天下.奖励[数字id][8]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会四千八",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会四千八 do
                                      if 取随机数()<=自定义数据.剑会四千八[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会四千八[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第八段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","武林高手","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("威震三界")
                        elseif 剑会天下[数字id].当前积分>=4200 and 剑会天下.奖励[数字id][6] and not 剑会天下.奖励[数字id][7] then
                                  剑会天下.奖励[数字id][7]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会四千二",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会四千二 do
                                      if 取随机数()<=自定义数据.剑会四千二[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会四千二[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第七段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","武林高手","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("威震三界")
                        elseif 剑会天下[数字id].当前积分>=3600 and 剑会天下.奖励[数字id][5] and not 剑会天下.奖励[数字id][6] then
                                  剑会天下.奖励[数字id][6]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会三千六",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会三千六 do
                                      if 取随机数()<=自定义数据.剑会三千六[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会三千六[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第六段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","武林高手","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("威震三界")
                        elseif 剑会天下[数字id].当前积分>=3000 and 剑会天下.奖励[数字id][4] and not 剑会天下.奖励[数字id][5] then
                                  剑会天下.奖励[数字id][5]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会三千",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会三千 do
                                      if 取随机数()<=自定义数据.剑会三千[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会三千[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第五段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","武林高手","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("威震三界")
                         elseif 剑会天下[数字id].当前积分>=2400 and 剑会天下.奖励[数字id][3] and not 剑会天下.奖励[数字id][4] then
                                  剑会天下.奖励[数字id][4]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会二千四",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会二千四 do
                                      if 取随机数()<=自定义数据.剑会二千四[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会二千四[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第四段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","威震三界","绝世奇才","明日之星"})
                                  玩家数据[数字id].角色:添加称谓("武林高手")
                        elseif 剑会天下[数字id].当前积分>=1800 and 剑会天下.奖励[数字id][2] and not 剑会天下.奖励[数字id][3] then
                                  剑会天下.奖励[数字id][3]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会一千八",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会一千八 do
                                      if 取随机数()<=自定义数据.剑会一千八[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会一千八[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第三段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","威震三界","绝世奇才","武林高手"})
                                  玩家数据[数字id].角色:添加称谓("明日之星")
                        elseif 剑会天下[数字id].当前积分>=1500 and 剑会天下.奖励[数字id][1] and not 剑会天下.奖励[数字id][2] then
                                  剑会天下.奖励[数字id][2]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会一千五",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会一千五 do
                                      if 取随机数()<=自定义数据.剑会一千五[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会一千五[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第二段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"江湖小虾","明日之星","威震三界","武林高手"})
                                玩家数据[数字id].角色:添加称谓("绝世奇才")
                        elseif 剑会天下[数字id].当前积分>=1300 and not 剑会天下.奖励[数字id][1] then
                                  剑会天下.奖励[数字id][1]=true
                                  玩家数据[数字id].角色:自定义银子添加("剑会一千三",1)
                                  local 获得物品={}
                                  for i=1,#自定义数据.剑会一千三 do
                                      if 取随机数()<=自定义数据.剑会一千三[i].概率 then
                                          获得物品[#获得物品+1]=自定义数据.剑会一千三[i]
                                      end
                                  end
                                  获得物品=删除重复(获得物品)
                                  if 获得物品~=nil then
                                      local 取编号=取随机数(1,#获得物品)
                                      if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                          玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                          广播消息({内容=format("#S/(剑会天下)#R/%s#Y/领取第一段奖励！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                      end
                                  end
                                  玩家数据[数字id].角色:删除称谓({"绝世奇才","明日之星","威震三界","武林高手"})
                                  玩家数据[数字id].角色:添加称谓("江湖小虾")
                        else
                              添加最后对话(数字id,"#y/你没有奖励可以领取")

                        end
                elseif 事件=="我要查询赛季排行榜" then
                        发送数据(玩家数据[数字id].连接id,987,{内容=排行榜数据})
                end
        elseif 名称=="效果取消熊猫" then
              if 事件=="取消人物变身卡" then
                  if 玩家数据[数字id].角色:取任务(1)==0 then
                     常规提示(数字id,"#Y/你身上没有变身卡")
                      return
                  end
                  玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(1))
                  发送数据(玩家数据[数字id].连接id,39)
                  玩家数据[数字id].角色.数据.变身数据=nil
                  玩家数据[数字id].角色.数据.变异=nil
                  常规提示(数字id,"你的变身时间到期了！")
                  发送数据(玩家数据[数字id].连接id,37,{变身数据=玩家数据[数字id].角色.数据.变身数据,变异=玩家数据[数字id].角色.数据.变异})
                  地图处理类:更改模型(数字id,{[1]=玩家数据[数字id].角色.数据.变身数据,[2]=玩家数据[数字id].角色.数据.变异},1)
                  玩家数据[数字id].角色:刷新信息("1")
              elseif 事件=="取消罗羹效果" then
                    if 玩家数据[数字id].角色:取任务(10)==0 then
                        常规提示(数字id,"#Y/身上没有罗羹效果")
                        return
                    end
                    玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(10))
                    常规提示(数字id,"#Y/罗羹效果取消成功")
              end
        elseif 名称=="建房史" then
                if 事件=="我想进行房屋相关事宜" then
                        local 临时对话="我是负责审批房屋建造的，你想做房屋么？"
                        local 临时选项={"我要买房","我要拆房","申请扩建","更换风格"}
                        发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                elseif 事件=="我要买房" then
                        if 自定义数据.购买房产价格==nil then
                            自定义数据.购买房产价格={货币类型="仙玉",数量=2000}
                        end
                        local 临时对话="客官购买初级地契需#R/"..自定义数据.购买房产价格.数量.."#W/"..自定义数据.购买房产价格.货币类型
                        local 临时选项={"确定购买地契","我没钱就是看看"}
                        发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                elseif 事件=="确定购买地契" then
                        if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.购买房产价格.货币类型,自定义数据.购买房产价格.数量,"购买房产",1) then
                            玩家数据[数字id].道具:给予道具(数字id,"房屋地契")
                            添加最后对话(数字id,"#Y/你获得了#R/房屋地契#Y/一张")
                        else
                            添加最后对话(数字id,"#Y/你个穷鬼.没钱无法购买地契")
                        end
                elseif 事件=="我要拆房" then
                        if 自定义数据.拆除房产价格==nil then
                            自定义数据.拆除房产价格={货币类型="仙玉",数量=2000}
                        end
                        if 玩家数据[数字id].房屋.是否创建  then
                           local 临时对话="客官拆除房子需人工费#R/"..自定义数据.拆除房产价格.数量.."#W/"..自定义数据.拆除房产价格.货币类型..",#R/注:拆除的房子家具以及道具会被系统回收"
                           local 临时选项={"确定拆除房子","我没钱就是看看"}
                           发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="确定拆除房子" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.拆除房产价格.货币类型,自定义数据.拆除房产价格.数量,"拆除房产",1) then
                                玩家数据[数字id].道具:给予道具(数字id,"房屋地契",玩家数据[数字id].房屋.地契编号,玩家数据[数字id].房屋)
                                玩家数据[数字id].房屋.是否创建=false
                                玩家数据[数字id].房屋:房屋初始化()
                                玩家数据[数字id].房屋:存档()
                                添加最后对话(数字id,"#Y/你获得了#R/房屋地契#Y/一张")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="更换风格" then
                        if 自定义数据.房产风格价格==nil then
                            自定义数据.房产风格价格={货币类型="仙玉",数量=2000}
                        end
                        if 玩家数据[数字id].房屋.是否创建  then
                            local 临时对话="客官更换风格需人工费#R/"..自定义数据.房产风格价格.数量.."#W/"..自定义数据.房产风格价格.货币类型..",你要更换那种风格?"
                            local 临时选项={"复古","青砖","大理石","红地板","海洋系","粉红兔","中国风","卡通猫","冰雪屋","星空蓝","咖啡屋","蓝色永恒","我没钱就是看看"}
                            发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="复古" or 事件=="青砖" or 事件=="大理石" or 事件=="红地板" or 事件=="海洋系" or 事件=="粉红兔" or 事件=="中国风" or 事件=="卡通猫" or 事件=="冰雪屋" or 事件=="星空蓝" or 事件=="咖啡屋" or 事件=="蓝色永恒" then
                       if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.房屋样式~=事件 then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.房产风格价格.货币类型,自定义数据.房产风格价格.数量,"房产风格",1) then
                                    玩家数据[数字id].房屋.房屋样式 = 事件
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的房屋风格改变了,快回家看看吧")
                                end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个样式了无需更换")
                            end
                        else
                              添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="申请扩建" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            local 临时对话="我是负责审批房屋建造的，你想做房屋么？"
                            local 临时选项={"申请庭院扩建","申请阁楼扩建"}
                            发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="申请庭院扩建" then
                        if 自定义数据.庭院扩建价格==nil then
                            自定义数据.庭院扩建价格={
                                      初级={货币类型="仙玉",数量=2000},
                                      中级={货币类型="仙玉",数量=2000},
                                      高级={货币类型="仙玉",数量=2000},
                                      顶级={货币类型="仙玉",数量=2000},
                                      }
                        end
                        if 玩家数据[数字id].房屋.是否创建 then
                            local 临时对话=""
                            local 临时选项={}
                            if 玩家数据[数字id].房屋.庭院规模 == "初级" then
                                临时对话="客官升级庭院需人工费#R/"..自定义数据.庭院扩建价格.初级.数量.."#W/"..自定义数据.庭院扩建价格.初级.货币类型
                                临时选项={"升级中级庭院","我没钱就是看看"}
                            elseif 玩家数据[数字id].房屋.庭院规模 == "中级" then
                                  临时对话="客官升级庭院需人工费#R/"..自定义数据.庭院扩建价格.中级.数量.."#W/"..自定义数据.庭院扩建价格.中级.货币类型
                                  临时选项={"升级高级庭院","我没钱就是看看"}
                            elseif 玩家数据[数字id].房屋.庭院规模 == "高级" then
                                  临时对话="客官升级庭院需人工费#R/"..自定义数据.庭院扩建价格.高级.数量.."#W/"..自定义数据.庭院扩建价格.高级.货币类型
                                  临时选项={"升级顶级庭院","我没钱就是看看"}
                            elseif 玩家数据[数字id].房屋.庭院规模 == "顶级" then
                                  临时对话="客官升级庭院需人工费#R/"..自定义数据.庭院扩建价格.顶级.数量.."#W/"..自定义数据.庭院扩建价格.顶级.货币类型..",你想升级成那种?"
                                  临时选项={"园林水榭","农家小院","欢乐童年","白雪皑皑","我没钱就是看看"}
                            else
                                临时对话="客官更换庭院需人工费#R/"..自定义数据.庭院扩建价格.顶级.数量.."#W/"..自定义数据.庭院扩建价格.顶级.货币类型..",你想升级成那种?"
                                临时选项={"园林水榭","农家小院","欢乐童年","白雪皑皑","我没钱就是看看"}
                            end
                            发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="升级中级庭院" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.庭院规模=="初级" then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.庭院扩建价格.初级.货币类型,自定义数据.庭院扩建价格.初级.数量,"升级庭院",1) then
                                    玩家数据[数字id].房屋.庭院规模 = "中级"
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的庭院扩建了,赶紧回家看看吧")
                                end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="升级高级庭院" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.庭院规模=="中级" then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.庭院扩建价格.中级.货币类型,自定义数据.庭院扩建价格.中级.数量,"升级庭院",1) then
                                    玩家数据[数字id].房屋.庭院规模 = "高级"
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的庭院扩建了,赶紧回家看看吧")
                                end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="升级顶级庭院" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.庭院规模=="高级" then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.庭院扩建价格.高级.货币类型,自定义数据.庭院扩建价格.高级.数量,"升级庭院",1) then
                                    玩家数据[数字id].房屋.庭院规模 = "顶级"
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的庭院扩建了,赶紧回家看看吧")
                                end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="园林水榭" or 事件=="农家小院"  or 事件=="欢乐童年"  or 事件=="白雪皑皑"  then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.庭院规模~=事件 then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.庭院扩建价格.顶级.货币类型,自定义数据.庭院扩建价格.顶级.数量,"升级庭院",1) then
                                    玩家数据[数字id].房屋.庭院规模 = 事件
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的庭院样式改变了,赶紧回家看看吧")
                                end
                            else
                                 添加最后对话(数字id,"#Y/你本身都是这个样式了无需改变")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="申请阁楼扩建" then
                        if 自定义数据.阁楼扩建价格==nil then
                          自定义数据.阁楼扩建价格={
                                      初级={货币类型="仙玉",数量=2000},
                                      中级={货币类型="仙玉",数量=2000},
                                      高级={货币类型="仙玉",数量=2000},
                                      顶级={货币类型="仙玉",数量=2000},
                                      华宅={货币类型="仙玉",数量=2000},
                                      }
                        end
                        if 玩家数据[数字id].房屋.是否创建 then
                              local 临时对话=""
                              local 临时选项={}
                              if 玩家数据[数字id].房屋.房屋规模 == "初级" then
                                        临时对话="客官升级庭院需人工费#R/"..自定义数据.阁楼扩建价格.初级.数量.."#W/"..自定义数据.阁楼扩建价格.初级.货币类型
                                        临时选项={"升级中级房屋","我没钱就是看看"}
                              elseif 玩家数据[数字id].房屋.房屋规模 == "中级" then
                                             临时对话="客官升级房屋需人工费#R/"..自定义数据.阁楼扩建价格.中级.数量.."#W/"..自定义数据.阁楼扩建价格.中级.货币类型
                                             临时选项={"升级高级房屋","我没钱就是看看"}
                              elseif 玩家数据[数字id].房屋.房屋规模 == "高级" then
                                             临时对话="客官升级房屋需人工费#R/"..自定义数据.阁楼扩建价格.高级.数量.."#W/"..自定义数据.阁楼扩建价格.高级.货币类型
                                             临时选项={"升级顶级阁楼","我没钱就是看看"}
                              elseif 玩家数据[数字id].房屋.房屋规模 == "顶级" then
                                            临时对话="客官升级房屋需人工费#R/"..自定义数据.阁楼扩建价格.顶级.数量.."#W/"..自定义数据.阁楼扩建价格.顶级.货币类型
                                            临时选项={"升级华宅阁楼","我没钱就是看看"}
                              elseif 玩家数据[数字id].房屋.房屋规模 == "华宅" then
                                            临时对话="客官升级房屋需人工费#R/"..自定义数据.阁楼扩建价格.华宅.数量.."#W/"..自定义数据.阁楼扩建价格.华宅.货币类型
                                            临时选项={"升级豪门阁楼","我没钱就是看看"}
                              else
                                            临时对话="客官你的阁楼已经是最顶级了无需扩建"
                                            临时选项={}
                              end
                              发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_衙役",对话=临时对话,选项=临时选项})
                          else
                              添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                          end
                elseif 事件=="升级中级房屋" then
                        if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.房屋规模=="初级" then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.阁楼扩建价格.初级.货币类型,自定义数据.阁楼扩建价格.初级.数量,"升级阁楼",1) then
                                    玩家数据[数字id].房屋.房屋规模 = "中级"
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的房屋扩建了,赶紧回家看看吧")
                                end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                        else
                            添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="升级高级房屋" then
                      if 玩家数据[数字id].房屋.是否创建  then
                          if 玩家数据[数字id].房屋.房屋规模=="中级" then
                                if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.阁楼扩建价格.中级.货币类型,自定义数据.阁楼扩建价格.中级.数量,"升级阁楼",1) then
                                    玩家数据[数字id].房屋.房屋规模 = "高级"
                                    玩家数据[数字id].房屋:重新加载房屋()
                                    添加最后对话(数字id,"#Y/你的房屋扩建了,赶紧回家看看吧")
                                end
                          else
                              添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                          end
                      else
                          添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                      end
                elseif 事件=="升级顶级阁楼" then
                      if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.房屋规模=="高级" then
                                  if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.阁楼扩建价格.高级.货币类型,自定义数据.阁楼扩建价格.高级.数量,"升级阁楼",1) then
                                        玩家数据[数字id].房屋.房屋规模 = "顶级"
                                        玩家数据[数字id].房屋:重新加载房屋()
                                        添加最后对话(数字id,"#Y/你的阁楼扩建了,赶紧回家看看吧")
                                  end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                      else
                          添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                      end
                elseif 事件=="升级华宅阁楼" then
                      if 玩家数据[数字id].房屋.是否创建  then
                            if 玩家数据[数字id].房屋.房屋规模=="顶级" then
                                  if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.阁楼扩建价格.顶级.货币类型,自定义数据.阁楼扩建价格.顶级.数量,"升级阁楼",1) then
                                        玩家数据[数字id].房屋.房屋规模 = "华宅"
                                        玩家数据[数字id].房屋:重新加载房屋()
                                        添加最后对话(数字id,"#Y/你的阁楼扩建了,赶紧回家看看吧")
                                  end
                            else
                                添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                            end
                       else
                           添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                        end
                elseif 事件=="升级豪门阁楼" then
                      if 玩家数据[数字id].房屋.是否创建  then
                          if 玩家数据[数字id].房屋.房屋规模=="华宅" then
                              if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.阁楼扩建价格.华宅.货币类型,自定义数据.阁楼扩建价格.华宅.数量,"升级阁楼",1) then
                                  玩家数据[数字id].房屋.房屋规模 = "豪门"
                                  玩家数据[数字id].房屋:重新加载房屋()
                                  添加最后对话(数字id,"#Y/你的阁楼扩建了,赶紧回家看看吧")
                              end
                          else
                              添加最后对话(数字id,"#Y/你本身都是这个规模了无需扩建")
                          end
                      else
                          添加最后对话(数字id,"#Y/你哪来的房产,小心我告你打劫!!!!")
                      end
                end
                -- elseif 事件=="我要拜访别人的家" then
                --   发送数据(玩家数据[数字id].连接id,1028)
                --end
        elseif 名称=="轿夫" then
                if 事件=="马上送我回家" then
                    if 玩家数据[数字id].房屋.是否创建  then
                        if 玩家数据[数字id].队伍~=0 then
                            if 玩家数据[数字id].队长 then
                                for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                    local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
                                    发送数据(玩家数据[临时id].连接id,1026,{玩家数据[数字id].房屋})
                                end
                            else
                                return
                            end
                        else
                           发送数据(玩家数据[数字id].连接id,1026,{玩家数据[数字id].房屋})
                        end
                        if 玩家数据[数字id].房屋.庭院地图 == 1420 then
                            地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,18,25)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1421 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,20,21)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1422 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,24,26)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1424 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,28,42)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1306 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,11,85)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1885 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,18,48)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1380 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,28,27)
                        elseif 玩家数据[数字id].房屋.庭院地图 == 1382 then
                              地图处理类:npc传送(数字id,玩家数据[数字id].房屋.庭院ID,74,81)
                        end
                        return
                    else
                        常规提示(数字id,"#Y你还没有房子！")
                        return
                    end
                elseif 事件=="我要拜访别人的家" then
                             -- 发送数据(玩家数据[数字id].连接id,1031)
                end
        elseif 名称=="强化符熊猫" then
              if 事件=="购买" then
                   发送数据(玩家数据[数字id].连接id,1501,{名称="强化符熊猫",模型="召唤兽造型进阶",对话="请选择你需要的门派强化符,强化符等级与玩家自身等级相同",选项={"方寸山","女儿村","神木林","化生寺","大唐官府","盘丝洞","阴曹地府","无底洞","魔王寨","狮驼岭","天宫","普陀山","凌波城","五庄观","龙宫"}})
               elseif 事件=="方寸山" or 事件=="女儿村" or 事件=="神木林"
                    or 事件=="化生寺" or 事件=="盘丝洞" or 事件=="无底洞"
                    or 事件=="魔王寨" or 事件=="狮驼岭" or 事件=="普陀山"
                    or 事件=="凌波城" or 事件=="五庄观" or 事件=="龙宫"
                    or 事件=="天宫" or 事件=="阴曹地府" or 事件=="大唐官府" then
                      if 玩家数据[数字id].角色:扣除银子(900000,"购买强化符",1) then
                          local 取强化名称 = 取门派强化符技能名称(事件)
                          玩家数据[数字id].道具:给予道具(数字id,"强化符",1,玩家数据[数字id].角色.数据.等级,取强化名称)
                          常规提示(数字id,"#Y/你获得了一张#R/强化符")
                      else
                           常规提示(数字id,"#Y/你的银两不足")
                      end
              end
        elseif 名称=="游奕灵官" then
              if 事件=="我要查看排名" then
                  if 辰星数据.开启 then
                        添加最后对话(数字id,"活动尚未结束无法查看排名！")
                        return
                  else
                        local 对话选项 = {}
                        for i=1,15 do
                            if 辰星数据.排行[i] then
                              table.insert(对话选项, "查看第"..i.."名情报")
                            end
                        end
                        添加最后对话(数字id,"以下为本次天降辰星活动排名前15的玩家信息,你可以点击选项查看具体玩家信息:",对话选项)
                  end
              elseif 事件=="查看我的排名" then
                       if 辰星数据.开启 then
                              添加最后对话(数字id,"活动尚未结束无法查看排名！")
                              return
                        else
                              if not 辰星数据[数字id] or not 辰星数据[数字id].排名 then
                                  添加最后对话(数字id,"未找到你的排名！")
                                  return

                              else
                                  local 领取 = "未领取"
                                  if 辰星数据[数字id].奖励 then
                                       领取 = "已领取"
                                  end
                                  添加最后对话(数字id,"你当前第#R"..辰星数据[数字id].排名.."#W名,排名奖励#R"..领取)
                                  return
                              end
                        end
              elseif string.find(事件,"查看第") and string.find(事件,"名情报") then
                      local 排名 = 0
                      local 查看 = 分割文本(事件,"查看第")
                      if 查看 and 查看[2] then
                          local 查看1 = 分割文本(查看[2],"名情报")
                          if 查看1 and 查看1[1] then
                              排名 = tonumber(查看1[1])
                          end
                      end
                      if 排名 and 排名~=0 and 辰星数据.排行 and 辰星数据.排行[排名] then
                          local 队长id = 辰星数据.排行[排名]
                          if 辰星数据.队伍[队长id] then
                              local a,b=取剩余分钟(辰星数据.队伍[队长id].时间)
                              local 对话内容 ="本次天降辰星活动第#R"..排名.."#W名队伍用时：#R"..a.."#W分#R"..b.."#W秒,点击名称查看详细玩家信息"
                              local 对话选项 = {}
                              for k,v in pairs(辰星数据.队伍[队长id]) do
                                  if type(k)=="number" and 辰星数据[k] and 辰星数据[k].排名==排名 then
                                      table.insert(对话选项, 辰星数据[k].名称)
                                  end
                              end
                              玩家数据[数字id].查看辰星 = 排名
                              添加最后对话(数字id,对话内容,对话选项)
                            end
                      end
              elseif 事件=="我欲前往收服十二元辰" then
                      if 辰星数据.开启 then
                          任务处理类:天降辰星(数字id)
                      else
                          添加最后对话(数字id,"现在并不是活动时间！")
                          return
                      end
              elseif 事件=="此行甚是疲惫，我要取消任务" then
                    if 辰星数据.开启 then
                        添加最后对话(数字id,"本任务每天只可领取一次,取消后无法继续领取该任务！",{"确定取消","我再考虑考虑"})
                    else
                        添加最后对话(数字id,"现在并不是活动时间！")
                        return
                    end
              elseif 事件=="确定取消" then
                    if 玩家数据[数字id].角色:取任务(17) == 0 then
                          添加最后对话(数字id,"你没有这样的任务可以取消！")
                    else
                        玩家数据[数字id].角色:取消任务(玩家数据[数字id].角色:取任务(17))
                        玩家数据[数字id].角色:刷新任务跟踪()
                        常规提示(数字id,"你的任务取消了！")
                        return
                    end
              elseif 事件=="我要领取排行奖励" then
                      if 辰星数据.开启 then
                          添加最后对话(数字id,"活动尚未结束无法领取排名奖励！")
                          return
                      else
                            if not 辰星数据[数字id] then
                                添加最后对话(数字id,"你未参加活动无法领取！")
                                return
                            elseif not 辰星数据[数字id].排名 then
                                    添加最后对话(数字id,"你未获得排名无法领取！")
                                    return
                            elseif 辰星数据[数字id].奖励 then
                                    添加最后对话(数字id,"你已领取奖励,无法重复领取！")
                                    return
                            else
                                  local 排名 = 辰星数据[数字id].排名
                                  辰星数据[数字id].奖励 = true
                                  if 排名 and 排名>0 and 排名<=15 then
                                      if 自定义数据.辰星排行奖励 and 自定义数据.辰星排行奖励[排名] then
                                          玩家数据[数字id].角色:自定义银子添加("辰星排行奖励"..排名,1)
                                          local 获得物品={}
                                          for i,v in ipairs(自定义数据.辰星排行奖励[排名]) do
                                                if 取随机数()<=v.概率 then
                                                    获得物品[#获得物品+1]=v
                                                end
                                          end
                                          获得物品=删除重复(获得物品)
                                          if 获得物品~=nil then
                                              local 取编号=取随机数(1,#获得物品)
                                              if 获得物品[取编号] and 获得物品[取编号].名称 and 获得物品[取编号].数量 then
                                                  玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                                  local 内容 = "#S(天降辰星)玩家#R/"..玩家数据[数字id].角色.数据.名称.."#Y领取了第#R"..排名.."#Y的排名奖励，获得了#R"..获得物品[取编号].名称
                                                  广播消息({内容=内容,频道="xt"})
                                              end
                                          end
                                      end
                                  end
                            end
                      end
              elseif 事件=="我想了解玩法" then
                    添加最后对话(数字id,"挑战本活动需要依照任务指引对十二元辰进行依次挑战,挑战成功后会获得挑战九耀星君其一的机会,并且通关速度最快的15位玩家可以领取额外奖励！")
              elseif not 辰星数据.开启 and 玩家数据[数字id].查看辰星 and 辰星数据.排行 and 辰星数据.排行[玩家数据[数字id].查看辰星] then
                      local 队长id = 辰星数据.排行[玩家数据[数字id].查看辰星]
                      if 辰星数据.队伍[队长id] then
                          for k,v in pairs(辰星数据.队伍[队长id]) do
                              if type(k)=="number" and 辰星数据[k] and 辰星数据[k].名称==事件 then
                                  local 对话内容 = "#R"..事件.."#W玩家的具体信息：\n\n"
                                  对话内容 = 对话内容 .. "#W名称："..辰星数据[k].名称.."\n"
                                  对话内容 = 对话内容 .. "#W等级："..辰星数据[k].等级.."\n"
                                  对话内容 = 对话内容 .. "#W门派："..辰星数据[k].门派.."\n"
                                  添加最后对话(数字id,对话内容)
                                  break
                              end
                          end
                      end
                      玩家数据[数字id].查看辰星 = nil
              end
        elseif 名称=="一副可爱的样子" then
                local 门派神器名称 = {
                    大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
                    普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
                    盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
                    花果山 = "鸿蒙石",九黎城 = "魔息角"
                  }
                local sqmz = 门派神器名称[玩家数据[数字id].角色.数据.门派]
                if 事件=="神器任务序章" then
                    if 玩家数据[数字id].神器.数据.神器技能 then
                        return
                    end
                    if 玩家数据[数字id].角色.数据.门派=="无门派" then
                      添加最后对话(数字id,"你连门派都没有啊#24")
                      return
                    end
                    sqmz = 门派神器名称[玩家数据[数字id].角色.数据.门派]
                    添加最后对话(数字id,"或许你可以看看#G我们以前走过的路#W...或许能发现一些蛛丝马迹。这东西就交给你了...虽然破损了，但是要珍惜啊，谁知道什么时候，它又会绽放力量。",{"接受神器“"..sqmz.."”！","我需要再磨炼自己。"})
                elseif sqmz and 事件=="接受神器“"..sqmz.."”！" then
                      if 玩家数据[数字id].角色.数据.等级>=109 then
                        玩家数据[数字id].神器:添加神器(数字id)
                      else
                        添加最后对话(数字id,"需要等级达到109级方可领取。")
                      end
                elseif 事件=="更换神器技能" then
                      if 玩家数据[数字id].角色.数据.等级>=109 then
                          玩家数据[数字id].神器:重置技能(数字id)
                      else
                        添加最后对话(数字id,"需要等级达到109级方可领取。")
                      end
                end
        elseif 名称=="书生" then
                if 事件=="领取仙缘" then
                    任务处理类:添加仙缘任务(数字id)
                elseif 事件=="仙缘商城" then
                      if 玩家数据[数字id].角色.数据.仙缘积分 == nil then
                          玩家数据[数字id].角色.数据.仙缘积分 = 0
                      end
                      玩家数据[数字id].商品列表=商店处理类.仙缘商店
                      发送数据(id,157,{商品=商店处理类.仙缘商店,仙缘积分=玩家数据[数字id].角色.数据.仙缘积分})
                elseif 事件=="我要取消任务" then
                        if 玩家数据[数字id].角色:取任务(374)==0 then
                            发送数据(玩家数据[数字id].连接id,1501,{名称="书生",模型="男人_书生",对话="你有在我这里领取任务吗？"})
                            return
                        elseif 玩家数据[数字id].队伍==0 then
                              发送数据(玩家数据[数字id].连接id,1501,{名称="书生",模型="男人_书生",对话="必须组队才能取消任务"})
                              return
                        end
                        local 队伍id=玩家数据[数字id].队伍
                        for n=1,#队伍数据[队伍id].成员数据 do
                            玩家数据[队伍数据[队伍id].成员数据[n]].角色.数据.仙缘次数=0
                            玩家数据[队伍数据[队伍id].成员数据[n]].角色:取消任务(玩家数据[队伍数据[队伍id].成员数据[n]].角色:取任务(374))
                            发送数据(玩家数据[数字id].连接id,1501,{名称="书生",模型="男人_书生",对话="已经成功帮你取消了任务。"})
                        end
                end
        elseif 名称=="世界BOSS传送员" then
                if 事件=="查看世界BOSS信息" then
                    系统处理类:获取世界BOSS(数字id)
                elseif 事件=="我要领取奖励" then
                      系统处理类:领取世界挑战奖励(数字id)
                end
        elseif  名称=="一副欠揍的样子" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 事件 == "我来挑战(60-69)" then
                       if 取等级要求(数字id,60)==false then 常规提示(数字id,"#Y/大于60级的玩家才能挑战哦") return  end
                       if 活动次数查询(数字id,"挑战六九")==false then return  end
                       战斗准备类:创建战斗(数字id,110024,0)
                elseif 事件 == "我来挑战(80-89)" then
                       if 取等级要求(数字id,80)==false then 常规提示(数字id,"#Y/大于80级的玩家才能挑战哦") return  end
                       if 活动次数查询(数字id,"挑战八九")==false then return  end
                       战斗准备类:创建战斗(数字id,110025,0)
                elseif 事件 == "我来挑战(100-109)" then
                       if 取等级要求(数字id,100)==false then 常规提示(数字id,"#Y/大于100级的玩家才能挑战哦") return  end
                       if 活动次数查询(数字id,"挑战幺零九")==false then return  end
                       战斗准备类:创建战斗(数字id,110026,0)
                elseif 事件 == "我来挑战(120-145)" then
                       if 取等级要求(数字id,120)==false then 常规提示(数字id,"#Y/大于120级的玩家才能挑战哦") return  end
                       if 活动次数查询(数字id,"挑战幺二九")==false then return  end
                       战斗准备类:创建战斗(数字id,110027,0)
                elseif 事件 == "我来挑战(150以上)" then
                       if 取等级要求(数字id,145)==false then 常规提示(数字id,"#Y/大于145级的玩家才能挑战哦") return  end
                       if 活动次数查询(数字id,"挑战幺五零")==false then return  end
                       战斗准备类:创建战斗(数字id,110028,0)
                end
        elseif 名称==服务端参数.名称.."伤害测试" then
                 if 事件 == "人物伤害测试" then
                    战斗准备类:创建战斗(数字id,110000,0)
                 end
        elseif 名称 == "挑战门派师傅" then
               if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
               if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
               if 取等级要求(数字id,129)==false then 常规提示(数字id,"#Y/大于129级的玩家才能挑战哦") return  end
               if 活动次数查询(数字id,"门派师傅")==false then return  end
               if 事件=="用实力证明一下" then
                  战斗准备类:创建战斗(数字id,110023,0)
               end
        elseif 名称 == "轮回境" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取等级要求(数字id,109)==false then 常规提示(数字id,"#Y/大于109级的玩家才能挑战哦") return  end
                if 活动次数查询(数字id,"轮回境")==false then return  end
                if 事件=="我来挑战第一层" then
                    战斗准备类:创建战斗(数字id,130030,0)
                elseif 事件=="我来挑战第二层" then
                        战斗准备类:创建战斗(数字id,130031,0)
                elseif 事件=="我来挑战第三层" then
                        战斗准备类:创建战斗(数字id,130032,0)
                elseif 事件=="我来挑战第四层" then
                        战斗准备类:创建战斗(数字id,130033,0)
                elseif 事件=="我来挑战第五层" then
                        战斗准备类:创建战斗(数字id,130034,0)
                elseif 事件=="我来挑战第六层" then
                        战斗准备类:创建战斗(数字id,130035,0)
                elseif 事件=="我来挑战第七层" then
                        战斗准备类:创建战斗(数字id,130036,0)
                end
        elseif 名称=="童子之力" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取等级要求(数字id,109)==false then 常规提示(数字id,"#Y/大于109级的玩家才能挑战哦") return  end
                if 活动次数查询(数字id,"童子之力")==false then return  end
                if 事件 == "我来参加活动" then
                    战斗准备类:创建战斗(数字id,130037,0)
                end
        elseif 名称=="副本BOSS挑战" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取等级要求(数字id,109)==false then 常规提示(数字id,"#Y/大于109级的玩家才能挑战哦") return  end
                if 活动次数查询(数字id,"副本BOSS")==false then return  end
                if 事件 == "我来参加活动" then
                   战斗准备类:创建战斗(数字id,130038,0)
                end
        elseif 名称=="挑战"..服务端参数.名称.."GM" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取等级要求(数字id,109)==false then 常规提示(数字id,"#Y/大于109级的玩家才能挑战哦") return  end
                if 活动次数查询(数字id,"挑战GM")==false then return  end
                if 事件 == "我来挑战GM" then
                   战斗准备类:创建战斗(数字id,130041,0)
                end
        elseif 名称=="罗刹鬼将" then
                if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                if 取队伍人数(数字id)<5 then 常规提示(数字id,"#Y挑战最少必须由5人进行") return  end
                if 取等级要求(数字id,109)==false then 常规提示(数字id,"#Y/大于109级的玩家才能挑战哦") return  end
                if 活动次数查询(数字id,"罗刹鬼将")==false then return  end
                if 事件 == "我来挑战罗刹鬼将" then
                   战斗准备类:创建战斗(数字id,130042,0)
                end
        elseif 名称=="福禄童子" then
                if 事件 == "喜迎新春到" then
                    任务处理类:添加春节任务(数字id)
                end
        elseif 名称=="彩虹大使" then
               彩虹争霸:对话事件处理(数字id,名称,事件)
        elseif 名称=="骠骑大将军" then
                长安保卫战:NPC对话处理(数字id,名称,事件)
        elseif  名称=="跨服争霸主持人" then
                if 事件 == "跨服报名" then
                    -- if 自定义数据.跨服报名价格==nil then
                    --     自定义数据.跨服报名价格={货币类型="仙玉",数量=2000}
                    -- end
                    -- local 临时对话="报名参赛需#R/"..自定义数据.跨服报名价格.数量.."#W/"..自定义数据.跨服报名价格.货币类型..",未报名无法参加战斗,报名截至跨服开启"
                    -- local 临时选项={"确定报名","我没钱就是看看"}
                    -- 发送数据(玩家数据[数字id].连接id,1501,{名称=名称,模型="男人_马副将",对话=临时对话,选项=临时选项})
                elseif 事件 == "确定报名" then
                        -- if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y该活动不能组队报名") return end
                        --     if 跨服数据[tostring(玩家数据[数字id].账号)] and 跨服数据[tostring(玩家数据[数字id].账号)].报名  then 常规提示(数字id,"#Y你已经报名了无法重复报名") return end
                        --     if 是否跨服 then
                        --           跨服连接:发送数据(3,{账号=tostring(玩家数据[数字id].账号),玩家id=数字id},1)
                        --     else
                        --         if 玩家数据[数字id].角色:自定义扣除货币(自定义数据.跨服报名价格.货币类型,自定义数据.跨服报名价格.数量,"跨服报名",1) then
                        --             跨服数据[tostring(玩家数据[数字id].账号)] ={报名=true,数字id = 数字id}
                        --             常规提示(数字id,"#Y报名成功")
                        --         end
                        --     end
                elseif 事件 == "取消跨服报名" then
                        -- if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y该活动不能组队报名") return end
                        -- if not 跨服数据[tostring(玩家数据[数字id].账号)] then 常规提示(数字id,"#Y你未报名无法取消") return end
                        -- if not 跨服数据[tostring(玩家数据[数字id].账号)].报名  then 常规提示(数字id,"#Y你未报名无法取消") return end
                        -- if 是否跨服  then
                        --     跨服连接:发送数据(5,{账号=tostring(玩家数据[数字id].账号),玩家id=数字id},1)
                        -- else
                        --     if 跨服数据[tostring(玩家数据[数字id].账号)] then
                        --         跨服数据[tostring(玩家数据[数字id].账号)].报名 = false
                        --         常规提示(数字id,"#Y你取消了报名")
                        --     end
                        -- end
                elseif 事件 == "开启跨服" then
                       -- if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y该活动不能组队进入") return end
                       --      if 是否跨服 then
                       --          local 发送内容 = {
                       --                         报名=false,
                       --                         名称=玩家数据[数字id].角色.数据.名称,
                       --                         数字id=数字id,
                       --                         模型=玩家数据[数字id].角色.数据.模型,
                       --                         账号=tostring(玩家数据[数字id].账号),
                       --                         密码=玩家数据[数字id].密码,
                       --                         ip=玩家数据[数字id].ip,
                       --                        }
                       --          if 跨服数据[tostring(玩家数据[数字id].账号)] and  跨服数据[tostring(玩家数据[数字id].账号)].报名 then
                       --              发送内容.报名=true
                       --          end
                       --          跨服连接:发送数据(2,发送内容,1)
                       --          发送数据(玩家数据[数字id].连接id,158,{ip=服务端参数.跨服连接,端口=服务端参数.跨服端口,名称=服务端参数.名称})
                       --          系统处理类:断开游戏(数字id)
                       --      else
                       --            常规提示(数字id,"#Y服务器没开启跨服无法进入")
                       --      end
                elseif 事件 == "领取奖励" and 跨服排名[数字id] and not 跨服排名[数字id].领取 then
                           -- if 跨服排名[数字id].名次 =="第一名" then

                           --  elseif 跨服排名[数字id].名次 =="第二名" then

                           --  elseif 跨服排名[数字id].名次 =="第三名" then

                           --  end
                           --  跨服排名[数字id].领取 = true
                end
        elseif 名称=="镇塔童子" then
                if 镇妖塔数据[数字id] == nil then
                      镇妖塔数据[数字id] = {名称 = 玩家数据[数字id].角色.数据.名称,层数 = 0}
                end
                local 当前选项=""
                local 当前层数 = 0
                if 镇妖塔数据[数字id] and 镇妖塔数据[数字id].层数>20 then
                    当前层数 = math.floor(镇妖塔数据[数字id].层数/10)*10-1
                    当前选项 = "重置到"..当前层数.."层"
                end
                if 事件=="开启挑战" then
                      local 检测通过 = true
                      if 玩家数据[数字id].角色.数据.等级<69 then
                          添加最后对话(数字id,"要求等级必须达到69级")
                          return
                      elseif 玩家数据[数字id].队伍==0 then
                            添加最后对话(数字id,"此活动最少需要1人组队参加。")
                            return
                      elseif not 玩家数据[数字id].队长 then
                            添加最后对话(数字id,"只有队长可以开启。")
                            return
                      elseif 取队伍人数(数字id)<1 then
                            常规提示(数字id,"#Y/本任务至少需要1人组队完成")
                            return
                      end
                      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                            local 临时id =队伍数据[玩家数据[数字id].队伍].成员数据[n]
                            if 镇妖塔数据[临时id] == nil then
                                镇妖塔数据[临时id] = {名称 = 玩家数据[id].角色.数据.名称,层数 = 0}
                            end
                            if 镇妖塔数据[临时id].层数  ~= 镇妖塔数据[数字id].层数 then
                                检测通过 = false
                                广播队伍消息(玩家数据[数字id].队伍,"#Y/" .. 玩家数据[临时id].角色.数据.名称 .. "当前镇妖塔挑战层数为:"..镇妖塔数据[临时id].层数.."与队长进度不一致。")
                                return
                            end
                            if 镇妖塔数据[临时id].层数>=服务端参数.镇妖塔层数 then
                                检测通过 = false
                                广播队伍消息(玩家数据[数字id].队伍,"#Y/" .. 玩家数据[临时id].角色.数据.名称 .. "已达到"..服务端参数.镇妖塔层数.."层,请重置后在来挑战。")
                                return
                            end
                      end
                      if 检测通过 then
                          战斗准备类:创建战斗(数字id+0,100108,0)
                      end
                      return
                elseif 事件==当前选项.."(10E)" then
                        if 玩家数据[数字id].角色.数据.银子<1000000000 then
                           发送数据(玩家数据[数字id].连接id,1501,{名称="镇塔之神",模型="男人_将军",对话="你没有那么多的银子"})
                                return
                        else
                             玩家数据[数字id].角色:扣除银子(1000000000,"重置层数(10E)",1)
                             镇妖塔数据[数字id].层数 = 当前层数
                        end

                elseif 事件==当前选项.."(1万仙玉)" then
                        if 共享货币[玩家数据[数字id].账号]:扣除仙玉(10000,"重置层数(1万仙玉)",数字id) then
                             镇妖塔数据[数字id].层数 = 当前层数
                        else
                              添加最后对话(数字id,"重置层数需要消耗10000仙玉！你的仙玉不够")
                              return
                        end
                elseif 事件=="重置层数(2E)" then

                        if 玩家数据[数字id].角色.数据.银子<200000000 then
                           发送数据(玩家数据[数字id].连接id,1501,{名称="镇塔之神",模型="男人_将军",对话="你没有那么多的银子"})
                                return
                        else
                             玩家数据[数字id].角色:扣除银子(200000000,"重置层数(2E)",1)
                             镇妖塔数据[数字id].层数 = 0
                        end
                elseif 事件=="重置层数(2000仙玉)" and 共享货币[玩家数据[数字id].账号] then

                          if 共享货币[玩家数据[数字id].账号]:扣除仙玉(2000,"重置层数(2000仙玉)",数字id) then
                             镇妖塔数据[数字id].层数 = 0
                          else
                              添加最后对话(数字id,"重置层数需要消耗2000仙玉！你的仙玉不够")
                              return
                          end
                end
        end
end

return 对话处理