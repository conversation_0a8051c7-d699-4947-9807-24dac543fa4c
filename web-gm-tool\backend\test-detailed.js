/**
 * 详细测试 - 逐步对比加密过程
 */

const { encodeBase641, jm } = require('./src/protocol/encryption');

const testData = '112345*-*12345do local ret={["账号"]="888888",["密码"]="888888"} return ret end12345*-*12345';

console.log('=== 详细加密过程测试 ===');

// 1. 检查原始数据
console.log('1. 原始数据:');
console.log('内容:', testData);
console.log('长度:', testData.length);
console.log('UTF-8字节:', Array.from(Buffer.from(testData, 'utf8')).map(b => b.toString(16).padStart(2, '0')).join(' '));

// 2. Base64编码
const base64Result = encodeBase641(testData);
console.log('\n2. Base64编码结果:');
console.log('内容:', base64Result);
console.log('长度:', base64Result.length);

// 3. 逐字符分析Base64结果
console.log('\n3. Base64字符分析:');
const chars = base64Result.split('');
const charCounts = {};
chars.forEach(char => {
    charCounts[char] = (charCounts[char] || 0) + 1;
});
console.log('字符统计:', charCounts);

// 4. 检查特殊字符
const specialChars = ['+', '/', '='];
console.log('\n4. 特殊字符检查:');
specialChars.forEach(char => {
    const count = charCounts[char] || 0;
    const positions = [];
    for (let i = 0; i < base64Result.length; i++) {
        if (base64Result[i] === char) {
            positions.push(i);
        }
    }
    console.log(`字符 '${char}': 出现${count}次, 位置: [${positions.join(', ')}]`);
});

// 5. 完整加密过程
console.log('\n5. 完整加密过程:');
const encrypted = jm(testData);
console.log('加密结果长度:', encrypted.length);
console.log('加密结果:', encrypted);

// 6. 分析加密结果的结构
const encryptedParts = encrypted.split(',');
console.log('\n6. 加密结果分析:');
console.log('总部分数:', encryptedParts.length);
console.log('前10部分:', encryptedParts.slice(0, 10));
console.log('后10部分:', encryptedParts.slice(-10));

// 7. 查找可能的问题
console.log('\n7. 问题分析:');
const emptyParts = encryptedParts.filter(part => part === '');
console.log('空部分数量:', emptyParts.length);

const uniqueParts = [...new Set(encryptedParts)];
console.log('唯一部分数量:', uniqueParts.length);
console.log('重复最多的部分:');
const partCounts = {};
encryptedParts.forEach(part => {
    partCounts[part] = (partCounts[part] || 0) + 1;
});
const sortedParts = Object.entries(partCounts).sort((a, b) => b[1] - a[1]).slice(0, 5);
sortedParts.forEach(([part, count]) => {
    console.log(`  '${part}': ${count}次`);
});
