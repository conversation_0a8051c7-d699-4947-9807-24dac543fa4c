{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"component\", \"prefixCls\", \"className\", \"classNames\", \"disabled\", \"id\", \"name\", \"style\", \"styles\", \"multiple\", \"accept\", \"capture\", \"children\", \"directory\", \"openFileDialogOnClick\", \"onMouseEnter\", \"onMouseLeave\", \"hasControlInside\"];\n/* eslint react/no-is-mounted:0,react/sort-comp:0,react/prop-types:0 */\nimport clsx from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React, { Component } from 'react';\nimport attrAccept from \"./attr-accept\";\nimport defaultRequest from \"./request\";\nimport traverseFileTree from \"./traverseFileTree\";\nimport getUid from \"./uid\";\nvar AjaxUploader = /*#__PURE__*/function (_Component) {\n  _inherits(AjaxUploader, _Component);\n  var _super = _createSuper(AjaxUploader);\n  function AjaxUploader() {\n    var _this;\n    _classCallCheck(this, AjaxUploader);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      uid: getUid()\n    });\n    _defineProperty(_assertThisInitialized(_this), \"reqs\", {});\n    _defineProperty(_assertThisInitialized(_this), \"fileInput\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"_isMounted\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"onChange\", function (e) {\n      var _this$props = _this.props,\n        accept = _this$props.accept,\n        directory = _this$props.directory;\n      var files = e.target.files;\n      var acceptedFiles = _toConsumableArray(files).filter(function (file) {\n        return !directory || attrAccept(file, accept);\n      });\n      _this.uploadFiles(acceptedFiles);\n      _this.reset();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onClick\", function (event) {\n      var el = _this.fileInput;\n      if (!el) {\n        return;\n      }\n      var target = event.target;\n      var onClick = _this.props.onClick;\n      if (target && target.tagName === 'BUTTON') {\n        var parent = el.parentNode;\n        parent.focus();\n        target.blur();\n      }\n      el.click();\n      if (onClick) {\n        onClick(event);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onKeyDown\", function (e) {\n      if (e.key === 'Enter') {\n        _this.onClick(e);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onDataTransferFiles\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(dataTransfer, existFileCallback) {\n        var _this$props2, multiple, accept, directory, items, files, acceptFiles;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this$props2 = _this.props, multiple = _this$props2.multiple, accept = _this$props2.accept, directory = _this$props2.directory;\n              items = _toConsumableArray(dataTransfer.items || []);\n              files = _toConsumableArray(dataTransfer.files || []);\n              if (files.length > 0 || items.some(function (item) {\n                return item.kind === 'file';\n              })) {\n                existFileCallback === null || existFileCallback === void 0 || existFileCallback();\n              }\n              if (!directory) {\n                _context.next = 11;\n                break;\n              }\n              _context.next = 7;\n              return traverseFileTree(Array.prototype.slice.call(items), function (_file) {\n                return attrAccept(_file, _this.props.accept);\n              });\n            case 7:\n              files = _context.sent;\n              _this.uploadFiles(files);\n              _context.next = 14;\n              break;\n            case 11:\n              acceptFiles = _toConsumableArray(files).filter(function (file) {\n                return attrAccept(file, accept);\n              });\n              if (multiple === false) {\n                acceptFiles = files.slice(0, 1);\n              }\n              _this.uploadFiles(acceptFiles);\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"onFilePaste\", /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(e) {\n        var pastable, clipboardData;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              pastable = _this.props.pastable;\n              if (pastable) {\n                _context2.next = 3;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 3:\n              if (!(e.type === 'paste')) {\n                _context2.next = 6;\n                break;\n              }\n              clipboardData = e.clipboardData;\n              return _context2.abrupt(\"return\", _this.onDataTransferFiles(clipboardData, function () {\n                e.preventDefault();\n              }));\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function (_x3) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"onFileDragOver\", function (e) {\n      e.preventDefault();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onFileDrop\", /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee3(e) {\n        var dataTransfer;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              e.preventDefault();\n              if (!(e.type === 'drop')) {\n                _context3.next = 4;\n                break;\n              }\n              dataTransfer = e.dataTransfer;\n              return _context3.abrupt(\"return\", _this.onDataTransferFiles(dataTransfer));\n            case 4:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function (_x4) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"uploadFiles\", function (files) {\n      var originFiles = _toConsumableArray(files);\n      var postFiles = originFiles.map(function (file) {\n        // eslint-disable-next-line no-param-reassign\n        file.uid = getUid();\n        return _this.processFile(file, originFiles);\n      });\n\n      // Batch upload files\n      Promise.all(postFiles).then(function (fileList) {\n        var onBatchStart = _this.props.onBatchStart;\n        onBatchStart === null || onBatchStart === void 0 || onBatchStart(fileList.map(function (_ref4) {\n          var origin = _ref4.origin,\n            parsedFile = _ref4.parsedFile;\n          return {\n            file: origin,\n            parsedFile: parsedFile\n          };\n        }));\n        fileList.filter(function (file) {\n          return file.parsedFile !== null;\n        }).forEach(function (file) {\n          _this.post(file);\n        });\n      });\n    });\n    /**\n     * Process file before upload. When all the file is ready, we start upload.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"processFile\", /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee4(file, fileList) {\n        var beforeUpload, transformedFile, action, mergedAction, data, mergedData, parsedData, parsedFile, mergedParsedFile;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              beforeUpload = _this.props.beforeUpload;\n              transformedFile = file;\n              if (!beforeUpload) {\n                _context4.next = 14;\n                break;\n              }\n              _context4.prev = 3;\n              _context4.next = 6;\n              return beforeUpload(file, fileList);\n            case 6:\n              transformedFile = _context4.sent;\n              _context4.next = 12;\n              break;\n            case 9:\n              _context4.prev = 9;\n              _context4.t0 = _context4[\"catch\"](3);\n              // Rejection will also trade as false\n              transformedFile = false;\n            case 12:\n              if (!(transformedFile === false)) {\n                _context4.next = 14;\n                break;\n              }\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                parsedFile: null,\n                action: null,\n                data: null\n              });\n            case 14:\n              // Get latest action\n              action = _this.props.action;\n              if (!(typeof action === 'function')) {\n                _context4.next = 21;\n                break;\n              }\n              _context4.next = 18;\n              return action(file);\n            case 18:\n              mergedAction = _context4.sent;\n              _context4.next = 22;\n              break;\n            case 21:\n              mergedAction = action;\n            case 22:\n              // Get latest data\n              data = _this.props.data;\n              if (!(typeof data === 'function')) {\n                _context4.next = 29;\n                break;\n              }\n              _context4.next = 26;\n              return data(file);\n            case 26:\n              mergedData = _context4.sent;\n              _context4.next = 30;\n              break;\n            case 29:\n              mergedData = data;\n            case 30:\n              parsedData =\n              // string type is from legacy `transformFile`.\n              // Not sure if this will work since no related test case works with it\n              (_typeof(transformedFile) === 'object' || typeof transformedFile === 'string') && transformedFile ? transformedFile : file;\n              if (parsedData instanceof File) {\n                parsedFile = parsedData;\n              } else {\n                parsedFile = new File([parsedData], file.name, {\n                  type: file.type\n                });\n              }\n              mergedParsedFile = parsedFile;\n              mergedParsedFile.uid = file.uid;\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                data: mergedData,\n                parsedFile: mergedParsedFile,\n                action: mergedAction\n              });\n            case 35:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[3, 9]]);\n      }));\n      return function (_x5, _x6) {\n        return _ref5.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"saveFileInput\", function (node) {\n      _this.fileInput = node;\n    });\n    return _this;\n  }\n  _createClass(AjaxUploader, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._isMounted = true;\n      var pastable = this.props.pastable;\n      if (pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      this.abort();\n      document.removeEventListener('paste', this.onFilePaste);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var pastable = this.props.pastable;\n      if (pastable && !prevProps.pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      } else if (!pastable && prevProps.pastable) {\n        document.removeEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"post\",\n    value: function post(_ref6) {\n      var _this2 = this;\n      var data = _ref6.data,\n        origin = _ref6.origin,\n        action = _ref6.action,\n        parsedFile = _ref6.parsedFile;\n      if (!this._isMounted) {\n        return;\n      }\n      var _this$props3 = this.props,\n        onStart = _this$props3.onStart,\n        customRequest = _this$props3.customRequest,\n        name = _this$props3.name,\n        headers = _this$props3.headers,\n        withCredentials = _this$props3.withCredentials,\n        method = _this$props3.method;\n      var uid = origin.uid;\n      var request = customRequest || defaultRequest;\n      var requestOption = {\n        action: action,\n        filename: name,\n        data: data,\n        file: parsedFile,\n        headers: headers,\n        withCredentials: withCredentials,\n        method: method || 'post',\n        onProgress: function onProgress(e) {\n          var onProgress = _this2.props.onProgress;\n          onProgress === null || onProgress === void 0 || onProgress(e, parsedFile);\n        },\n        onSuccess: function onSuccess(ret, xhr) {\n          var onSuccess = _this2.props.onSuccess;\n          onSuccess === null || onSuccess === void 0 || onSuccess(ret, parsedFile, xhr);\n          delete _this2.reqs[uid];\n        },\n        onError: function onError(err, ret) {\n          var onError = _this2.props.onError;\n          onError === null || onError === void 0 || onError(err, ret, parsedFile);\n          delete _this2.reqs[uid];\n        }\n      };\n      onStart(origin);\n      this.reqs[uid] = request(requestOption);\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.setState({\n        uid: getUid()\n      });\n    }\n  }, {\n    key: \"abort\",\n    value: function abort(file) {\n      var reqs = this.reqs;\n      if (file) {\n        var uid = file.uid ? file.uid : file;\n        if (reqs[uid] && reqs[uid].abort) {\n          reqs[uid].abort();\n        }\n        delete reqs[uid];\n      } else {\n        Object.keys(reqs).forEach(function (uid) {\n          if (reqs[uid] && reqs[uid].abort) {\n            reqs[uid].abort();\n          }\n          delete reqs[uid];\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        Tag = _this$props4.component,\n        prefixCls = _this$props4.prefixCls,\n        className = _this$props4.className,\n        _this$props4$classNam = _this$props4.classNames,\n        classNames = _this$props4$classNam === void 0 ? {} : _this$props4$classNam,\n        disabled = _this$props4.disabled,\n        id = _this$props4.id,\n        name = _this$props4.name,\n        style = _this$props4.style,\n        _this$props4$styles = _this$props4.styles,\n        styles = _this$props4$styles === void 0 ? {} : _this$props4$styles,\n        multiple = _this$props4.multiple,\n        accept = _this$props4.accept,\n        capture = _this$props4.capture,\n        children = _this$props4.children,\n        directory = _this$props4.directory,\n        openFileDialogOnClick = _this$props4.openFileDialogOnClick,\n        onMouseEnter = _this$props4.onMouseEnter,\n        onMouseLeave = _this$props4.onMouseLeave,\n        hasControlInside = _this$props4.hasControlInside,\n        otherProps = _objectWithoutProperties(_this$props4, _excluded);\n      var cls = clsx(_defineProperty(_defineProperty(_defineProperty({}, prefixCls, true), \"\".concat(prefixCls, \"-disabled\"), disabled), className, className));\n      // because input don't have directory/webkitdirectory type declaration\n      var dirProps = directory ? {\n        directory: 'directory',\n        webkitdirectory: 'webkitdirectory'\n      } : {};\n      var events = disabled ? {} : {\n        onClick: openFileDialogOnClick ? this.onClick : function () {},\n        onKeyDown: openFileDialogOnClick ? this.onKeyDown : function () {},\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onDrop: this.onFileDrop,\n        onDragOver: this.onFileDragOver,\n        tabIndex: hasControlInside ? undefined : '0'\n      };\n      return /*#__PURE__*/React.createElement(Tag, _extends({}, events, {\n        className: cls,\n        role: hasControlInside ? undefined : 'button',\n        style: style\n      }), /*#__PURE__*/React.createElement(\"input\", _extends({}, pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      }), {\n        id: id\n        /**\n         * https://github.com/ant-design/ant-design/issues/50643,\n         * https://github.com/react-component/upload/pull/575#issuecomment-2320646552\n         */,\n        name: name,\n        disabled: disabled,\n        type: \"file\",\n        ref: this.saveFileInput,\n        onClick: function onClick(e) {\n          return e.stopPropagation();\n        } // https://github.com/ant-design/ant-design/issues/19948\n        ,\n\n        key: this.state.uid,\n        style: _objectSpread({\n          display: 'none'\n        }, styles.input),\n        className: classNames.input,\n        accept: accept\n      }, dirProps, {\n        multiple: multiple,\n        onChange: this.onChange\n      }, capture != null ? {\n        capture: capture\n      } : {})), children);\n    }\n  }]);\n  return AjaxUploader;\n}(Component);\nexport default AjaxUploader;", "map": {"version": 3, "names": ["_extends", "_objectSpread", "_objectWithoutProperties", "_typeof", "_regeneratorRuntime", "_asyncToGenerator", "_toConsumableArray", "_classCallCheck", "_createClass", "_assertThisInitialized", "_inherits", "_createSuper", "_defineProperty", "_excluded", "clsx", "pickAttrs", "React", "Component", "attrAccept", "defaultRequest", "traverseFileTree", "getUid", "AjaxUploader", "_Component", "_super", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "uid", "e", "_this$props", "props", "accept", "directory", "files", "target", "acceptedFiles", "filter", "file", "uploadFiles", "reset", "event", "el", "fileInput", "onClick", "tagName", "parent", "parentNode", "focus", "blur", "click", "key", "_ref", "mark", "_callee", "dataTransfer", "existFileCallback", "_this$props2", "multiple", "items", "acceptFiles", "wrap", "_callee$", "_context", "prev", "next", "some", "item", "kind", "prototype", "slice", "_file", "sent", "stop", "_x", "_x2", "_ref2", "_callee2", "pastable", "clipboardData", "_callee2$", "_context2", "abrupt", "type", "onDataTransferFiles", "preventDefault", "_x3", "_ref3", "_callee3", "_callee3$", "_context3", "_x4", "originFiles", "postFiles", "map", "processFile", "Promise", "all", "then", "fileList", "onBatchStart", "_ref4", "origin", "parsedFile", "for<PERSON>ach", "post", "_ref5", "_callee4", "beforeUpload", "transformedFile", "action", "mergedAction", "data", "mergedData", "parsedData", "mergedParsedFile", "_callee4$", "_context4", "t0", "File", "name", "_x5", "_x6", "node", "value", "componentDidMount", "_isMounted", "document", "addEventListener", "onFilePaste", "componentWillUnmount", "abort", "removeEventListener", "componentDidUpdate", "prevProps", "_ref6", "_this2", "_this$props3", "onStart", "customRequest", "headers", "withCredentials", "method", "request", "requestOption", "filename", "onProgress", "onSuccess", "ret", "xhr", "reqs", "onError", "err", "setState", "Object", "keys", "render", "_this$props4", "Tag", "component", "prefixCls", "className", "_this$props4$classNam", "classNames", "disabled", "id", "style", "_this$props4$styles", "styles", "capture", "children", "openFileDialogOnClick", "onMouseEnter", "onMouseLeave", "hasControlInside", "otherProps", "cls", "dirProps", "webkitdirectory", "events", "onKeyDown", "onDrop", "onFileDrop", "onDragOver", "onFileDragOver", "tabIndex", "undefined", "createElement", "role", "aria", "ref", "saveFileInput", "stopPropagation", "state", "display", "input", "onChange"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-upload/es/AjaxUploader.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/esm/objectWithoutProperties\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _regeneratorRuntime from \"@babel/runtime/helpers/esm/regeneratorRuntime\";\nimport _asyncToGenerator from \"@babel/runtime/helpers/esm/asyncToGenerator\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/esm/assertThisInitialized\";\nimport _inherits from \"@babel/runtime/helpers/esm/inherits\";\nimport _createSuper from \"@babel/runtime/helpers/esm/createSuper\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nvar _excluded = [\"component\", \"prefixCls\", \"className\", \"classNames\", \"disabled\", \"id\", \"name\", \"style\", \"styles\", \"multiple\", \"accept\", \"capture\", \"children\", \"directory\", \"openFileDialogOnClick\", \"onMouseEnter\", \"onMouseLeave\", \"hasControlInside\"];\n/* eslint react/no-is-mounted:0,react/sort-comp:0,react/prop-types:0 */\nimport clsx from 'classnames';\nimport pickAttrs from \"rc-util/es/pickAttrs\";\nimport React, { Component } from 'react';\nimport attrAccept from \"./attr-accept\";\nimport defaultRequest from \"./request\";\nimport traverseFileTree from \"./traverseFileTree\";\nimport getUid from \"./uid\";\nvar AjaxUploader = /*#__PURE__*/function (_Component) {\n  _inherits(AjaxUploader, _Component);\n  var _super = _createSuper(AjaxUploader);\n  function AjaxUploader() {\n    var _this;\n    _classCallCheck(this, AjaxUploader);\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _super.call.apply(_super, [this].concat(args));\n    _defineProperty(_assertThisInitialized(_this), \"state\", {\n      uid: getUid()\n    });\n    _defineProperty(_assertThisInitialized(_this), \"reqs\", {});\n    _defineProperty(_assertThisInitialized(_this), \"fileInput\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"_isMounted\", void 0);\n    _defineProperty(_assertThisInitialized(_this), \"onChange\", function (e) {\n      var _this$props = _this.props,\n        accept = _this$props.accept,\n        directory = _this$props.directory;\n      var files = e.target.files;\n      var acceptedFiles = _toConsumableArray(files).filter(function (file) {\n        return !directory || attrAccept(file, accept);\n      });\n      _this.uploadFiles(acceptedFiles);\n      _this.reset();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onClick\", function (event) {\n      var el = _this.fileInput;\n      if (!el) {\n        return;\n      }\n      var target = event.target;\n      var onClick = _this.props.onClick;\n      if (target && target.tagName === 'BUTTON') {\n        var parent = el.parentNode;\n        parent.focus();\n        target.blur();\n      }\n      el.click();\n      if (onClick) {\n        onClick(event);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onKeyDown\", function (e) {\n      if (e.key === 'Enter') {\n        _this.onClick(e);\n      }\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onDataTransferFiles\", /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(dataTransfer, existFileCallback) {\n        var _this$props2, multiple, accept, directory, items, files, acceptFiles;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _this$props2 = _this.props, multiple = _this$props2.multiple, accept = _this$props2.accept, directory = _this$props2.directory;\n              items = _toConsumableArray(dataTransfer.items || []);\n              files = _toConsumableArray(dataTransfer.files || []);\n              if (files.length > 0 || items.some(function (item) {\n                return item.kind === 'file';\n              })) {\n                existFileCallback === null || existFileCallback === void 0 || existFileCallback();\n              }\n              if (!directory) {\n                _context.next = 11;\n                break;\n              }\n              _context.next = 7;\n              return traverseFileTree(Array.prototype.slice.call(items), function (_file) {\n                return attrAccept(_file, _this.props.accept);\n              });\n            case 7:\n              files = _context.sent;\n              _this.uploadFiles(files);\n              _context.next = 14;\n              break;\n            case 11:\n              acceptFiles = _toConsumableArray(files).filter(function (file) {\n                return attrAccept(file, accept);\n              });\n              if (multiple === false) {\n                acceptFiles = files.slice(0, 1);\n              }\n              _this.uploadFiles(acceptFiles);\n            case 14:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function (_x, _x2) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"onFilePaste\", /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee2(e) {\n        var pastable, clipboardData;\n        return _regeneratorRuntime().wrap(function _callee2$(_context2) {\n          while (1) switch (_context2.prev = _context2.next) {\n            case 0:\n              pastable = _this.props.pastable;\n              if (pastable) {\n                _context2.next = 3;\n                break;\n              }\n              return _context2.abrupt(\"return\");\n            case 3:\n              if (!(e.type === 'paste')) {\n                _context2.next = 6;\n                break;\n              }\n              clipboardData = e.clipboardData;\n              return _context2.abrupt(\"return\", _this.onDataTransferFiles(clipboardData, function () {\n                e.preventDefault();\n              }));\n            case 6:\n            case \"end\":\n              return _context2.stop();\n          }\n        }, _callee2);\n      }));\n      return function (_x3) {\n        return _ref2.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"onFileDragOver\", function (e) {\n      e.preventDefault();\n    });\n    _defineProperty(_assertThisInitialized(_this), \"onFileDrop\", /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee3(e) {\n        var dataTransfer;\n        return _regeneratorRuntime().wrap(function _callee3$(_context3) {\n          while (1) switch (_context3.prev = _context3.next) {\n            case 0:\n              e.preventDefault();\n              if (!(e.type === 'drop')) {\n                _context3.next = 4;\n                break;\n              }\n              dataTransfer = e.dataTransfer;\n              return _context3.abrupt(\"return\", _this.onDataTransferFiles(dataTransfer));\n            case 4:\n            case \"end\":\n              return _context3.stop();\n          }\n        }, _callee3);\n      }));\n      return function (_x4) {\n        return _ref3.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"uploadFiles\", function (files) {\n      var originFiles = _toConsumableArray(files);\n      var postFiles = originFiles.map(function (file) {\n        // eslint-disable-next-line no-param-reassign\n        file.uid = getUid();\n        return _this.processFile(file, originFiles);\n      });\n\n      // Batch upload files\n      Promise.all(postFiles).then(function (fileList) {\n        var onBatchStart = _this.props.onBatchStart;\n        onBatchStart === null || onBatchStart === void 0 || onBatchStart(fileList.map(function (_ref4) {\n          var origin = _ref4.origin,\n            parsedFile = _ref4.parsedFile;\n          return {\n            file: origin,\n            parsedFile: parsedFile\n          };\n        }));\n        fileList.filter(function (file) {\n          return file.parsedFile !== null;\n        }).forEach(function (file) {\n          _this.post(file);\n        });\n      });\n    });\n    /**\n     * Process file before upload. When all the file is ready, we start upload.\n     */\n    _defineProperty(_assertThisInitialized(_this), \"processFile\", /*#__PURE__*/function () {\n      var _ref5 = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee4(file, fileList) {\n        var beforeUpload, transformedFile, action, mergedAction, data, mergedData, parsedData, parsedFile, mergedParsedFile;\n        return _regeneratorRuntime().wrap(function _callee4$(_context4) {\n          while (1) switch (_context4.prev = _context4.next) {\n            case 0:\n              beforeUpload = _this.props.beforeUpload;\n              transformedFile = file;\n              if (!beforeUpload) {\n                _context4.next = 14;\n                break;\n              }\n              _context4.prev = 3;\n              _context4.next = 6;\n              return beforeUpload(file, fileList);\n            case 6:\n              transformedFile = _context4.sent;\n              _context4.next = 12;\n              break;\n            case 9:\n              _context4.prev = 9;\n              _context4.t0 = _context4[\"catch\"](3);\n              // Rejection will also trade as false\n              transformedFile = false;\n            case 12:\n              if (!(transformedFile === false)) {\n                _context4.next = 14;\n                break;\n              }\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                parsedFile: null,\n                action: null,\n                data: null\n              });\n            case 14:\n              // Get latest action\n              action = _this.props.action;\n              if (!(typeof action === 'function')) {\n                _context4.next = 21;\n                break;\n              }\n              _context4.next = 18;\n              return action(file);\n            case 18:\n              mergedAction = _context4.sent;\n              _context4.next = 22;\n              break;\n            case 21:\n              mergedAction = action;\n            case 22:\n              // Get latest data\n              data = _this.props.data;\n              if (!(typeof data === 'function')) {\n                _context4.next = 29;\n                break;\n              }\n              _context4.next = 26;\n              return data(file);\n            case 26:\n              mergedData = _context4.sent;\n              _context4.next = 30;\n              break;\n            case 29:\n              mergedData = data;\n            case 30:\n              parsedData =\n              // string type is from legacy `transformFile`.\n              // Not sure if this will work since no related test case works with it\n              (_typeof(transformedFile) === 'object' || typeof transformedFile === 'string') && transformedFile ? transformedFile : file;\n              if (parsedData instanceof File) {\n                parsedFile = parsedData;\n              } else {\n                parsedFile = new File([parsedData], file.name, {\n                  type: file.type\n                });\n              }\n              mergedParsedFile = parsedFile;\n              mergedParsedFile.uid = file.uid;\n              return _context4.abrupt(\"return\", {\n                origin: file,\n                data: mergedData,\n                parsedFile: mergedParsedFile,\n                action: mergedAction\n              });\n            case 35:\n            case \"end\":\n              return _context4.stop();\n          }\n        }, _callee4, null, [[3, 9]]);\n      }));\n      return function (_x5, _x6) {\n        return _ref5.apply(this, arguments);\n      };\n    }());\n    _defineProperty(_assertThisInitialized(_this), \"saveFileInput\", function (node) {\n      _this.fileInput = node;\n    });\n    return _this;\n  }\n  _createClass(AjaxUploader, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      this._isMounted = true;\n      var pastable = this.props.pastable;\n      if (pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      this._isMounted = false;\n      this.abort();\n      document.removeEventListener('paste', this.onFilePaste);\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps) {\n      var pastable = this.props.pastable;\n      if (pastable && !prevProps.pastable) {\n        document.addEventListener('paste', this.onFilePaste);\n      } else if (!pastable && prevProps.pastable) {\n        document.removeEventListener('paste', this.onFilePaste);\n      }\n    }\n  }, {\n    key: \"post\",\n    value: function post(_ref6) {\n      var _this2 = this;\n      var data = _ref6.data,\n        origin = _ref6.origin,\n        action = _ref6.action,\n        parsedFile = _ref6.parsedFile;\n      if (!this._isMounted) {\n        return;\n      }\n      var _this$props3 = this.props,\n        onStart = _this$props3.onStart,\n        customRequest = _this$props3.customRequest,\n        name = _this$props3.name,\n        headers = _this$props3.headers,\n        withCredentials = _this$props3.withCredentials,\n        method = _this$props3.method;\n      var uid = origin.uid;\n      var request = customRequest || defaultRequest;\n      var requestOption = {\n        action: action,\n        filename: name,\n        data: data,\n        file: parsedFile,\n        headers: headers,\n        withCredentials: withCredentials,\n        method: method || 'post',\n        onProgress: function onProgress(e) {\n          var onProgress = _this2.props.onProgress;\n          onProgress === null || onProgress === void 0 || onProgress(e, parsedFile);\n        },\n        onSuccess: function onSuccess(ret, xhr) {\n          var onSuccess = _this2.props.onSuccess;\n          onSuccess === null || onSuccess === void 0 || onSuccess(ret, parsedFile, xhr);\n          delete _this2.reqs[uid];\n        },\n        onError: function onError(err, ret) {\n          var onError = _this2.props.onError;\n          onError === null || onError === void 0 || onError(err, ret, parsedFile);\n          delete _this2.reqs[uid];\n        }\n      };\n      onStart(origin);\n      this.reqs[uid] = request(requestOption);\n    }\n  }, {\n    key: \"reset\",\n    value: function reset() {\n      this.setState({\n        uid: getUid()\n      });\n    }\n  }, {\n    key: \"abort\",\n    value: function abort(file) {\n      var reqs = this.reqs;\n      if (file) {\n        var uid = file.uid ? file.uid : file;\n        if (reqs[uid] && reqs[uid].abort) {\n          reqs[uid].abort();\n        }\n        delete reqs[uid];\n      } else {\n        Object.keys(reqs).forEach(function (uid) {\n          if (reqs[uid] && reqs[uid].abort) {\n            reqs[uid].abort();\n          }\n          delete reqs[uid];\n        });\n      }\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      var _this$props4 = this.props,\n        Tag = _this$props4.component,\n        prefixCls = _this$props4.prefixCls,\n        className = _this$props4.className,\n        _this$props4$classNam = _this$props4.classNames,\n        classNames = _this$props4$classNam === void 0 ? {} : _this$props4$classNam,\n        disabled = _this$props4.disabled,\n        id = _this$props4.id,\n        name = _this$props4.name,\n        style = _this$props4.style,\n        _this$props4$styles = _this$props4.styles,\n        styles = _this$props4$styles === void 0 ? {} : _this$props4$styles,\n        multiple = _this$props4.multiple,\n        accept = _this$props4.accept,\n        capture = _this$props4.capture,\n        children = _this$props4.children,\n        directory = _this$props4.directory,\n        openFileDialogOnClick = _this$props4.openFileDialogOnClick,\n        onMouseEnter = _this$props4.onMouseEnter,\n        onMouseLeave = _this$props4.onMouseLeave,\n        hasControlInside = _this$props4.hasControlInside,\n        otherProps = _objectWithoutProperties(_this$props4, _excluded);\n      var cls = clsx(_defineProperty(_defineProperty(_defineProperty({}, prefixCls, true), \"\".concat(prefixCls, \"-disabled\"), disabled), className, className));\n      // because input don't have directory/webkitdirectory type declaration\n      var dirProps = directory ? {\n        directory: 'directory',\n        webkitdirectory: 'webkitdirectory'\n      } : {};\n      var events = disabled ? {} : {\n        onClick: openFileDialogOnClick ? this.onClick : function () {},\n        onKeyDown: openFileDialogOnClick ? this.onKeyDown : function () {},\n        onMouseEnter: onMouseEnter,\n        onMouseLeave: onMouseLeave,\n        onDrop: this.onFileDrop,\n        onDragOver: this.onFileDragOver,\n        tabIndex: hasControlInside ? undefined : '0'\n      };\n      return /*#__PURE__*/React.createElement(Tag, _extends({}, events, {\n        className: cls,\n        role: hasControlInside ? undefined : 'button',\n        style: style\n      }), /*#__PURE__*/React.createElement(\"input\", _extends({}, pickAttrs(otherProps, {\n        aria: true,\n        data: true\n      }), {\n        id: id\n        /**\n         * https://github.com/ant-design/ant-design/issues/50643,\n         * https://github.com/react-component/upload/pull/575#issuecomment-2320646552\n         */,\n        name: name,\n        disabled: disabled,\n        type: \"file\",\n        ref: this.saveFileInput,\n        onClick: function onClick(e) {\n          return e.stopPropagation();\n        } // https://github.com/ant-design/ant-design/issues/19948\n        ,\n        key: this.state.uid,\n        style: _objectSpread({\n          display: 'none'\n        }, styles.input),\n        className: classNames.input,\n        accept: accept\n      }, dirProps, {\n        multiple: multiple,\n        onChange: this.onChange\n      }, capture != null ? {\n        capture: capture\n      } : {})), children);\n    }\n  }]);\n  return AjaxUploader;\n}(Component);\nexport default AjaxUploader;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,aAAa,MAAM,0CAA0C;AACpE,OAAOC,wBAAwB,MAAM,oDAAoD;AACzF,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,iBAAiB,MAAM,6CAA6C;AAC3E,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,sBAAsB,MAAM,kDAAkD;AACrF,OAAOC,SAAS,MAAM,qCAAqC;AAC3D,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,IAAIC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,uBAAuB,EAAE,cAAc,EAAE,cAAc,EAAE,kBAAkB,CAAC;AACzP;AACA,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,cAAc,MAAM,WAAW;AACtC,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,MAAM,MAAM,OAAO;AAC1B,IAAIC,YAAY,GAAG,aAAa,UAAUC,UAAU,EAAE;EACpDb,SAAS,CAACY,YAAY,EAAEC,UAAU,CAAC;EACnC,IAAIC,MAAM,GAAGb,YAAY,CAACW,YAAY,CAAC;EACvC,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIG,KAAK;IACTlB,eAAe,CAAC,IAAI,EAAEe,YAAY,CAAC;IACnC,KAAK,IAAII,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACAN,KAAK,GAAGD,MAAM,CAACQ,IAAI,CAACC,KAAK,CAACT,MAAM,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC;IACtDjB,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,OAAO,EAAE;MACtDU,GAAG,EAAEd,MAAM,CAAC;IACd,CAAC,CAAC;IACFT,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,CAAC;IAC1Db,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IACnEb,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,YAAY,EAAE,KAAK,CAAC,CAAC;IACpEb,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,UAAU,EAAE,UAAUW,CAAC,EAAE;MACtE,IAAIC,WAAW,GAAGZ,KAAK,CAACa,KAAK;QAC3BC,MAAM,GAAGF,WAAW,CAACE,MAAM;QAC3BC,SAAS,GAAGH,WAAW,CAACG,SAAS;MACnC,IAAIC,KAAK,GAAGL,CAAC,CAACM,MAAM,CAACD,KAAK;MAC1B,IAAIE,aAAa,GAAGrC,kBAAkB,CAACmC,KAAK,CAAC,CAACG,MAAM,CAAC,UAAUC,IAAI,EAAE;QACnE,OAAO,CAACL,SAAS,IAAItB,UAAU,CAAC2B,IAAI,EAAEN,MAAM,CAAC;MAC/C,CAAC,CAAC;MACFd,KAAK,CAACqB,WAAW,CAACH,aAAa,CAAC;MAChClB,KAAK,CAACsB,KAAK,CAAC,CAAC;IACf,CAAC,CAAC;IACFnC,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,SAAS,EAAE,UAAUuB,KAAK,EAAE;MACzE,IAAIC,EAAE,GAAGxB,KAAK,CAACyB,SAAS;MACxB,IAAI,CAACD,EAAE,EAAE;QACP;MACF;MACA,IAAIP,MAAM,GAAGM,KAAK,CAACN,MAAM;MACzB,IAAIS,OAAO,GAAG1B,KAAK,CAACa,KAAK,CAACa,OAAO;MACjC,IAAIT,MAAM,IAAIA,MAAM,CAACU,OAAO,KAAK,QAAQ,EAAE;QACzC,IAAIC,MAAM,GAAGJ,EAAE,CAACK,UAAU;QAC1BD,MAAM,CAACE,KAAK,CAAC,CAAC;QACdb,MAAM,CAACc,IAAI,CAAC,CAAC;MACf;MACAP,EAAE,CAACQ,KAAK,CAAC,CAAC;MACV,IAAIN,OAAO,EAAE;QACXA,OAAO,CAACH,KAAK,CAAC;MAChB;IACF,CAAC,CAAC;IACFpC,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,WAAW,EAAE,UAAUW,CAAC,EAAE;MACvE,IAAIA,CAAC,CAACsB,GAAG,KAAK,OAAO,EAAE;QACrBjC,KAAK,CAAC0B,OAAO,CAACf,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IACFxB,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,qBAAqB,EAAE,aAAa,YAAY;MAC7F,IAAIkC,IAAI,GAAGtD,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACwD,IAAI,CAAC,SAASC,OAAOA,CAACC,YAAY,EAAEC,iBAAiB,EAAE;QACtH,IAAIC,YAAY,EAAEC,QAAQ,EAAE1B,MAAM,EAAEC,SAAS,EAAE0B,KAAK,EAAEzB,KAAK,EAAE0B,WAAW;QACxE,OAAO/D,mBAAmB,CAAC,CAAC,CAACgE,IAAI,CAAC,SAASC,QAAQA,CAACC,QAAQ,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAACC,IAAI,GAAGD,QAAQ,CAACE,IAAI;YAC7C,KAAK,CAAC;cACJR,YAAY,GAAGvC,KAAK,CAACa,KAAK,EAAE2B,QAAQ,GAAGD,YAAY,CAACC,QAAQ,EAAE1B,MAAM,GAAGyB,YAAY,CAACzB,MAAM,EAAEC,SAAS,GAAGwB,YAAY,CAACxB,SAAS;cAC9H0B,KAAK,GAAG5D,kBAAkB,CAACwD,YAAY,CAACI,KAAK,IAAI,EAAE,CAAC;cACpDzB,KAAK,GAAGnC,kBAAkB,CAACwD,YAAY,CAACrB,KAAK,IAAI,EAAE,CAAC;cACpD,IAAIA,KAAK,CAACb,MAAM,GAAG,CAAC,IAAIsC,KAAK,CAACO,IAAI,CAAC,UAAUC,IAAI,EAAE;gBACjD,OAAOA,IAAI,CAACC,IAAI,KAAK,MAAM;cAC7B,CAAC,CAAC,EAAE;gBACFZ,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,IAAIA,iBAAiB,CAAC,CAAC;cACnF;cACA,IAAI,CAACvB,SAAS,EAAE;gBACd8B,QAAQ,CAACE,IAAI,GAAG,EAAE;gBAClB;cACF;cACAF,QAAQ,CAACE,IAAI,GAAG,CAAC;cACjB,OAAOpD,gBAAgB,CAACU,KAAK,CAAC8C,SAAS,CAACC,KAAK,CAAC7C,IAAI,CAACkC,KAAK,CAAC,EAAE,UAAUY,KAAK,EAAE;gBAC1E,OAAO5D,UAAU,CAAC4D,KAAK,EAAErD,KAAK,CAACa,KAAK,CAACC,MAAM,CAAC;cAC9C,CAAC,CAAC;YACJ,KAAK,CAAC;cACJE,KAAK,GAAG6B,QAAQ,CAACS,IAAI;cACrBtD,KAAK,CAACqB,WAAW,CAACL,KAAK,CAAC;cACxB6B,QAAQ,CAACE,IAAI,GAAG,EAAE;cAClB;YACF,KAAK,EAAE;cACLL,WAAW,GAAG7D,kBAAkB,CAACmC,KAAK,CAAC,CAACG,MAAM,CAAC,UAAUC,IAAI,EAAE;gBAC7D,OAAO3B,UAAU,CAAC2B,IAAI,EAAEN,MAAM,CAAC;cACjC,CAAC,CAAC;cACF,IAAI0B,QAAQ,KAAK,KAAK,EAAE;gBACtBE,WAAW,GAAG1B,KAAK,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;cACjC;cACApD,KAAK,CAACqB,WAAW,CAACqB,WAAW,CAAC;YAChC,KAAK,EAAE;YACP,KAAK,KAAK;cACR,OAAOG,QAAQ,CAACU,IAAI,CAAC,CAAC;UAC1B;QACF,CAAC,EAAEnB,OAAO,CAAC;MACb,CAAC,CAAC,CAAC;MACH,OAAO,UAAUoB,EAAE,EAAEC,GAAG,EAAE;QACxB,OAAOvB,IAAI,CAAC1B,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;MACpC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IACJf,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,aAAa,EAAE,aAAa,YAAY;MACrF,IAAI0D,KAAK,GAAG9E,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACwD,IAAI,CAAC,SAASwB,QAAQA,CAAChD,CAAC,EAAE;QAC1F,IAAIiD,QAAQ,EAAEC,aAAa;QAC3B,OAAOlF,mBAAmB,CAAC,CAAC,CAACgE,IAAI,CAAC,SAASmB,SAASA,CAACC,SAAS,EAAE;UAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACjB,IAAI,GAAGiB,SAAS,CAAChB,IAAI;YAC/C,KAAK,CAAC;cACJa,QAAQ,GAAG5D,KAAK,CAACa,KAAK,CAAC+C,QAAQ;cAC/B,IAAIA,QAAQ,EAAE;gBACZG,SAAS,CAAChB,IAAI,GAAG,CAAC;gBAClB;cACF;cACA,OAAOgB,SAAS,CAACC,MAAM,CAAC,QAAQ,CAAC;YACnC,KAAK,CAAC;cACJ,IAAI,EAAErD,CAAC,CAACsD,IAAI,KAAK,OAAO,CAAC,EAAE;gBACzBF,SAAS,CAAChB,IAAI,GAAG,CAAC;gBAClB;cACF;cACAc,aAAa,GAAGlD,CAAC,CAACkD,aAAa;cAC/B,OAAOE,SAAS,CAACC,MAAM,CAAC,QAAQ,EAAEhE,KAAK,CAACkE,mBAAmB,CAACL,aAAa,EAAE,YAAY;gBACrFlD,CAAC,CAACwD,cAAc,CAAC,CAAC;cACpB,CAAC,CAAC,CAAC;YACL,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOJ,SAAS,CAACR,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEI,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC;MACH,OAAO,UAAUS,GAAG,EAAE;QACpB,OAAOV,KAAK,CAAClD,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;MACrC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IACJf,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,gBAAgB,EAAE,UAAUW,CAAC,EAAE;MAC5EA,CAAC,CAACwD,cAAc,CAAC,CAAC;IACpB,CAAC,CAAC;IACFhF,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,YAAY,EAAE,aAAa,YAAY;MACpF,IAAIqE,KAAK,GAAGzF,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACwD,IAAI,CAAC,SAASmC,QAAQA,CAAC3D,CAAC,EAAE;QAC1F,IAAI0B,YAAY;QAChB,OAAO1D,mBAAmB,CAAC,CAAC,CAACgE,IAAI,CAAC,SAAS4B,SAASA,CAACC,SAAS,EAAE;UAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAAC1B,IAAI,GAAG0B,SAAS,CAACzB,IAAI;YAC/C,KAAK,CAAC;cACJpC,CAAC,CAACwD,cAAc,CAAC,CAAC;cAClB,IAAI,EAAExD,CAAC,CAACsD,IAAI,KAAK,MAAM,CAAC,EAAE;gBACxBO,SAAS,CAACzB,IAAI,GAAG,CAAC;gBAClB;cACF;cACAV,YAAY,GAAG1B,CAAC,CAAC0B,YAAY;cAC7B,OAAOmC,SAAS,CAACR,MAAM,CAAC,QAAQ,EAAEhE,KAAK,CAACkE,mBAAmB,CAAC7B,YAAY,CAAC,CAAC;YAC5E,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOmC,SAAS,CAACjB,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEe,QAAQ,CAAC;MACd,CAAC,CAAC,CAAC;MACH,OAAO,UAAUG,GAAG,EAAE;QACpB,OAAOJ,KAAK,CAAC7D,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;MACrC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IACJf,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,aAAa,EAAE,UAAUgB,KAAK,EAAE;MAC7E,IAAI0D,WAAW,GAAG7F,kBAAkB,CAACmC,KAAK,CAAC;MAC3C,IAAI2D,SAAS,GAAGD,WAAW,CAACE,GAAG,CAAC,UAAUxD,IAAI,EAAE;QAC9C;QACAA,IAAI,CAACV,GAAG,GAAGd,MAAM,CAAC,CAAC;QACnB,OAAOI,KAAK,CAAC6E,WAAW,CAACzD,IAAI,EAAEsD,WAAW,CAAC;MAC7C,CAAC,CAAC;;MAEF;MACAI,OAAO,CAACC,GAAG,CAACJ,SAAS,CAAC,CAACK,IAAI,CAAC,UAAUC,QAAQ,EAAE;QAC9C,IAAIC,YAAY,GAAGlF,KAAK,CAACa,KAAK,CAACqE,YAAY;QAC3CA,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACD,QAAQ,CAACL,GAAG,CAAC,UAAUO,KAAK,EAAE;UAC7F,IAAIC,MAAM,GAAGD,KAAK,CAACC,MAAM;YACvBC,UAAU,GAAGF,KAAK,CAACE,UAAU;UAC/B,OAAO;YACLjE,IAAI,EAAEgE,MAAM;YACZC,UAAU,EAAEA;UACd,CAAC;QACH,CAAC,CAAC,CAAC;QACHJ,QAAQ,CAAC9D,MAAM,CAAC,UAAUC,IAAI,EAAE;UAC9B,OAAOA,IAAI,CAACiE,UAAU,KAAK,IAAI;QACjC,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUlE,IAAI,EAAE;UACzBpB,KAAK,CAACuF,IAAI,CAACnE,IAAI,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACF;AACJ;AACA;IACIjC,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,aAAa,EAAE,aAAa,YAAY;MACrF,IAAIwF,KAAK,GAAG5G,iBAAiB,CAAE,aAAaD,mBAAmB,CAAC,CAAC,CAACwD,IAAI,CAAC,SAASsD,QAAQA,CAACrE,IAAI,EAAE6D,QAAQ,EAAE;QACvG,IAAIS,YAAY,EAAEC,eAAe,EAAEC,MAAM,EAAEC,YAAY,EAAEC,IAAI,EAAEC,UAAU,EAAEC,UAAU,EAAEX,UAAU,EAAEY,gBAAgB;QACnH,OAAOtH,mBAAmB,CAAC,CAAC,CAACgE,IAAI,CAAC,SAASuD,SAASA,CAACC,SAAS,EAAE;UAC9D,OAAO,CAAC,EAAE,QAAQA,SAAS,CAACrD,IAAI,GAAGqD,SAAS,CAACpD,IAAI;YAC/C,KAAK,CAAC;cACJ2C,YAAY,GAAG1F,KAAK,CAACa,KAAK,CAAC6E,YAAY;cACvCC,eAAe,GAAGvE,IAAI;cACtB,IAAI,CAACsE,YAAY,EAAE;gBACjBS,SAAS,CAACpD,IAAI,GAAG,EAAE;gBACnB;cACF;cACAoD,SAAS,CAACrD,IAAI,GAAG,CAAC;cAClBqD,SAAS,CAACpD,IAAI,GAAG,CAAC;cAClB,OAAO2C,YAAY,CAACtE,IAAI,EAAE6D,QAAQ,CAAC;YACrC,KAAK,CAAC;cACJU,eAAe,GAAGQ,SAAS,CAAC7C,IAAI;cAChC6C,SAAS,CAACpD,IAAI,GAAG,EAAE;cACnB;YACF,KAAK,CAAC;cACJoD,SAAS,CAACrD,IAAI,GAAG,CAAC;cAClBqD,SAAS,CAACC,EAAE,GAAGD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;cACpC;cACAR,eAAe,GAAG,KAAK;YACzB,KAAK,EAAE;cACL,IAAI,EAAEA,eAAe,KAAK,KAAK,CAAC,EAAE;gBAChCQ,SAAS,CAACpD,IAAI,GAAG,EAAE;gBACnB;cACF;cACA,OAAOoD,SAAS,CAACnC,MAAM,CAAC,QAAQ,EAAE;gBAChCoB,MAAM,EAAEhE,IAAI;gBACZiE,UAAU,EAAE,IAAI;gBAChBO,MAAM,EAAE,IAAI;gBACZE,IAAI,EAAE;cACR,CAAC,CAAC;YACJ,KAAK,EAAE;cACL;cACAF,MAAM,GAAG5F,KAAK,CAACa,KAAK,CAAC+E,MAAM;cAC3B,IAAI,EAAE,OAAOA,MAAM,KAAK,UAAU,CAAC,EAAE;gBACnCO,SAAS,CAACpD,IAAI,GAAG,EAAE;gBACnB;cACF;cACAoD,SAAS,CAACpD,IAAI,GAAG,EAAE;cACnB,OAAO6C,MAAM,CAACxE,IAAI,CAAC;YACrB,KAAK,EAAE;cACLyE,YAAY,GAAGM,SAAS,CAAC7C,IAAI;cAC7B6C,SAAS,CAACpD,IAAI,GAAG,EAAE;cACnB;YACF,KAAK,EAAE;cACL8C,YAAY,GAAGD,MAAM;YACvB,KAAK,EAAE;cACL;cACAE,IAAI,GAAG9F,KAAK,CAACa,KAAK,CAACiF,IAAI;cACvB,IAAI,EAAE,OAAOA,IAAI,KAAK,UAAU,CAAC,EAAE;gBACjCK,SAAS,CAACpD,IAAI,GAAG,EAAE;gBACnB;cACF;cACAoD,SAAS,CAACpD,IAAI,GAAG,EAAE;cACnB,OAAO+C,IAAI,CAAC1E,IAAI,CAAC;YACnB,KAAK,EAAE;cACL2E,UAAU,GAAGI,SAAS,CAAC7C,IAAI;cAC3B6C,SAAS,CAACpD,IAAI,GAAG,EAAE;cACnB;YACF,KAAK,EAAE;cACLgD,UAAU,GAAGD,IAAI;YACnB,KAAK,EAAE;cACLE,UAAU;cACV;cACA;cACA,CAACtH,OAAO,CAACiH,eAAe,CAAC,KAAK,QAAQ,IAAI,OAAOA,eAAe,KAAK,QAAQ,KAAKA,eAAe,GAAGA,eAAe,GAAGvE,IAAI;cAC1H,IAAI4E,UAAU,YAAYK,IAAI,EAAE;gBAC9BhB,UAAU,GAAGW,UAAU;cACzB,CAAC,MAAM;gBACLX,UAAU,GAAG,IAAIgB,IAAI,CAAC,CAACL,UAAU,CAAC,EAAE5E,IAAI,CAACkF,IAAI,EAAE;kBAC7CrC,IAAI,EAAE7C,IAAI,CAAC6C;gBACb,CAAC,CAAC;cACJ;cACAgC,gBAAgB,GAAGZ,UAAU;cAC7BY,gBAAgB,CAACvF,GAAG,GAAGU,IAAI,CAACV,GAAG;cAC/B,OAAOyF,SAAS,CAACnC,MAAM,CAAC,QAAQ,EAAE;gBAChCoB,MAAM,EAAEhE,IAAI;gBACZ0E,IAAI,EAAEC,UAAU;gBAChBV,UAAU,EAAEY,gBAAgB;gBAC5BL,MAAM,EAAEC;cACV,CAAC,CAAC;YACJ,KAAK,EAAE;YACP,KAAK,KAAK;cACR,OAAOM,SAAS,CAAC5C,IAAI,CAAC,CAAC;UAC3B;QACF,CAAC,EAAEkC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;MAC9B,CAAC,CAAC,CAAC;MACH,OAAO,UAAUc,GAAG,EAAEC,GAAG,EAAE;QACzB,OAAOhB,KAAK,CAAChF,KAAK,CAAC,IAAI,EAAEN,SAAS,CAAC;MACrC,CAAC;IACH,CAAC,CAAC,CAAC,CAAC;IACJf,eAAe,CAACH,sBAAsB,CAACgB,KAAK,CAAC,EAAE,eAAe,EAAE,UAAUyG,IAAI,EAAE;MAC9EzG,KAAK,CAACyB,SAAS,GAAGgF,IAAI;IACxB,CAAC,CAAC;IACF,OAAOzG,KAAK;EACd;EACAjB,YAAY,CAACc,YAAY,EAAE,CAAC;IAC1BoC,GAAG,EAAE,mBAAmB;IACxByE,KAAK,EAAE,SAASC,iBAAiBA,CAAA,EAAG;MAClC,IAAI,CAACC,UAAU,GAAG,IAAI;MACtB,IAAIhD,QAAQ,GAAG,IAAI,CAAC/C,KAAK,CAAC+C,QAAQ;MAClC,IAAIA,QAAQ,EAAE;QACZiD,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC;MACtD;IACF;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,sBAAsB;IAC3ByE,KAAK,EAAE,SAASM,oBAAoBA,CAAA,EAAG;MACrC,IAAI,CAACJ,UAAU,GAAG,KAAK;MACvB,IAAI,CAACK,KAAK,CAAC,CAAC;MACZJ,QAAQ,CAACK,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACH,WAAW,CAAC;IACzD;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,oBAAoB;IACzByE,KAAK,EAAE,SAASS,kBAAkBA,CAACC,SAAS,EAAE;MAC5C,IAAIxD,QAAQ,GAAG,IAAI,CAAC/C,KAAK,CAAC+C,QAAQ;MAClC,IAAIA,QAAQ,IAAI,CAACwD,SAAS,CAACxD,QAAQ,EAAE;QACnCiD,QAAQ,CAACC,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACC,WAAW,CAAC;MACtD,CAAC,MAAM,IAAI,CAACnD,QAAQ,IAAIwD,SAAS,CAACxD,QAAQ,EAAE;QAC1CiD,QAAQ,CAACK,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACH,WAAW,CAAC;MACzD;IACF;EACF,CAAC,EAAE;IACD9E,GAAG,EAAE,MAAM;IACXyE,KAAK,EAAE,SAASnB,IAAIA,CAAC8B,KAAK,EAAE;MAC1B,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIxB,IAAI,GAAGuB,KAAK,CAACvB,IAAI;QACnBV,MAAM,GAAGiC,KAAK,CAACjC,MAAM;QACrBQ,MAAM,GAAGyB,KAAK,CAACzB,MAAM;QACrBP,UAAU,GAAGgC,KAAK,CAAChC,UAAU;MAC/B,IAAI,CAAC,IAAI,CAACuB,UAAU,EAAE;QACpB;MACF;MACA,IAAIW,YAAY,GAAG,IAAI,CAAC1G,KAAK;QAC3B2G,OAAO,GAAGD,YAAY,CAACC,OAAO;QAC9BC,aAAa,GAAGF,YAAY,CAACE,aAAa;QAC1CnB,IAAI,GAAGiB,YAAY,CAACjB,IAAI;QACxBoB,OAAO,GAAGH,YAAY,CAACG,OAAO;QAC9BC,eAAe,GAAGJ,YAAY,CAACI,eAAe;QAC9CC,MAAM,GAAGL,YAAY,CAACK,MAAM;MAC9B,IAAIlH,GAAG,GAAG0E,MAAM,CAAC1E,GAAG;MACpB,IAAImH,OAAO,GAAGJ,aAAa,IAAI/H,cAAc;MAC7C,IAAIoI,aAAa,GAAG;QAClBlC,MAAM,EAAEA,MAAM;QACdmC,QAAQ,EAAEzB,IAAI;QACdR,IAAI,EAAEA,IAAI;QACV1E,IAAI,EAAEiE,UAAU;QAChBqC,OAAO,EAAEA,OAAO;QAChBC,eAAe,EAAEA,eAAe;QAChCC,MAAM,EAAEA,MAAM,IAAI,MAAM;QACxBI,UAAU,EAAE,SAASA,UAAUA,CAACrH,CAAC,EAAE;UACjC,IAAIqH,UAAU,GAAGV,MAAM,CAACzG,KAAK,CAACmH,UAAU;UACxCA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,IAAIA,UAAU,CAACrH,CAAC,EAAE0E,UAAU,CAAC;QAC3E,CAAC;QACD4C,SAAS,EAAE,SAASA,SAASA,CAACC,GAAG,EAAEC,GAAG,EAAE;UACtC,IAAIF,SAAS,GAAGX,MAAM,CAACzG,KAAK,CAACoH,SAAS;UACtCA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,IAAIA,SAAS,CAACC,GAAG,EAAE7C,UAAU,EAAE8C,GAAG,CAAC;UAC7E,OAAOb,MAAM,CAACc,IAAI,CAAC1H,GAAG,CAAC;QACzB,CAAC;QACD2H,OAAO,EAAE,SAASA,OAAOA,CAACC,GAAG,EAAEJ,GAAG,EAAE;UAClC,IAAIG,OAAO,GAAGf,MAAM,CAACzG,KAAK,CAACwH,OAAO;UAClCA,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,IAAIA,OAAO,CAACC,GAAG,EAAEJ,GAAG,EAAE7C,UAAU,CAAC;UACvE,OAAOiC,MAAM,CAACc,IAAI,CAAC1H,GAAG,CAAC;QACzB;MACF,CAAC;MACD8G,OAAO,CAACpC,MAAM,CAAC;MACf,IAAI,CAACgD,IAAI,CAAC1H,GAAG,CAAC,GAAGmH,OAAO,CAACC,aAAa,CAAC;IACzC;EACF,CAAC,EAAE;IACD7F,GAAG,EAAE,OAAO;IACZyE,KAAK,EAAE,SAASpF,KAAKA,CAAA,EAAG;MACtB,IAAI,CAACiH,QAAQ,CAAC;QACZ7H,GAAG,EAAEd,MAAM,CAAC;MACd,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDqC,GAAG,EAAE,OAAO;IACZyE,KAAK,EAAE,SAASO,KAAKA,CAAC7F,IAAI,EAAE;MAC1B,IAAIgH,IAAI,GAAG,IAAI,CAACA,IAAI;MACpB,IAAIhH,IAAI,EAAE;QACR,IAAIV,GAAG,GAAGU,IAAI,CAACV,GAAG,GAAGU,IAAI,CAACV,GAAG,GAAGU,IAAI;QACpC,IAAIgH,IAAI,CAAC1H,GAAG,CAAC,IAAI0H,IAAI,CAAC1H,GAAG,CAAC,CAACuG,KAAK,EAAE;UAChCmB,IAAI,CAAC1H,GAAG,CAAC,CAACuG,KAAK,CAAC,CAAC;QACnB;QACA,OAAOmB,IAAI,CAAC1H,GAAG,CAAC;MAClB,CAAC,MAAM;QACL8H,MAAM,CAACC,IAAI,CAACL,IAAI,CAAC,CAAC9C,OAAO,CAAC,UAAU5E,GAAG,EAAE;UACvC,IAAI0H,IAAI,CAAC1H,GAAG,CAAC,IAAI0H,IAAI,CAAC1H,GAAG,CAAC,CAACuG,KAAK,EAAE;YAChCmB,IAAI,CAAC1H,GAAG,CAAC,CAACuG,KAAK,CAAC,CAAC;UACnB;UACA,OAAOmB,IAAI,CAAC1H,GAAG,CAAC;QAClB,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAE;IACDuB,GAAG,EAAE,QAAQ;IACbyE,KAAK,EAAE,SAASgC,MAAMA,CAAA,EAAG;MACvB,IAAIC,YAAY,GAAG,IAAI,CAAC9H,KAAK;QAC3B+H,GAAG,GAAGD,YAAY,CAACE,SAAS;QAC5BC,SAAS,GAAGH,YAAY,CAACG,SAAS;QAClCC,SAAS,GAAGJ,YAAY,CAACI,SAAS;QAClCC,qBAAqB,GAAGL,YAAY,CAACM,UAAU;QAC/CA,UAAU,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,qBAAqB;QAC1EE,QAAQ,GAAGP,YAAY,CAACO,QAAQ;QAChCC,EAAE,GAAGR,YAAY,CAACQ,EAAE;QACpB7C,IAAI,GAAGqC,YAAY,CAACrC,IAAI;QACxB8C,KAAK,GAAGT,YAAY,CAACS,KAAK;QAC1BC,mBAAmB,GAAGV,YAAY,CAACW,MAAM;QACzCA,MAAM,GAAGD,mBAAmB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,mBAAmB;QAClE7G,QAAQ,GAAGmG,YAAY,CAACnG,QAAQ;QAChC1B,MAAM,GAAG6H,YAAY,CAAC7H,MAAM;QAC5ByI,OAAO,GAAGZ,YAAY,CAACY,OAAO;QAC9BC,QAAQ,GAAGb,YAAY,CAACa,QAAQ;QAChCzI,SAAS,GAAG4H,YAAY,CAAC5H,SAAS;QAClC0I,qBAAqB,GAAGd,YAAY,CAACc,qBAAqB;QAC1DC,YAAY,GAAGf,YAAY,CAACe,YAAY;QACxCC,YAAY,GAAGhB,YAAY,CAACgB,YAAY;QACxCC,gBAAgB,GAAGjB,YAAY,CAACiB,gBAAgB;QAChDC,UAAU,GAAGpL,wBAAwB,CAACkK,YAAY,EAAEvJ,SAAS,CAAC;MAChE,IAAI0K,GAAG,GAAGzK,IAAI,CAACF,eAAe,CAACA,eAAe,CAACA,eAAe,CAAC,CAAC,CAAC,EAAE2J,SAAS,EAAE,IAAI,CAAC,EAAE,EAAE,CAACrI,MAAM,CAACqI,SAAS,EAAE,WAAW,CAAC,EAAEI,QAAQ,CAAC,EAAEH,SAAS,EAAEA,SAAS,CAAC,CAAC;MACzJ;MACA,IAAIgB,QAAQ,GAAGhJ,SAAS,GAAG;QACzBA,SAAS,EAAE,WAAW;QACtBiJ,eAAe,EAAE;MACnB,CAAC,GAAG,CAAC,CAAC;MACN,IAAIC,MAAM,GAAGf,QAAQ,GAAG,CAAC,CAAC,GAAG;QAC3BxH,OAAO,EAAE+H,qBAAqB,GAAG,IAAI,CAAC/H,OAAO,GAAG,YAAY,CAAC,CAAC;QAC9DwI,SAAS,EAAET,qBAAqB,GAAG,IAAI,CAACS,SAAS,GAAG,YAAY,CAAC,CAAC;QAClER,YAAY,EAAEA,YAAY;QAC1BC,YAAY,EAAEA,YAAY;QAC1BQ,MAAM,EAAE,IAAI,CAACC,UAAU;QACvBC,UAAU,EAAE,IAAI,CAACC,cAAc;QAC/BC,QAAQ,EAAEX,gBAAgB,GAAGY,SAAS,GAAG;MAC3C,CAAC;MACD,OAAO,aAAajL,KAAK,CAACkL,aAAa,CAAC7B,GAAG,EAAErK,QAAQ,CAAC,CAAC,CAAC,EAAE0L,MAAM,EAAE;QAChElB,SAAS,EAAEe,GAAG;QACdY,IAAI,EAAEd,gBAAgB,GAAGY,SAAS,GAAG,QAAQ;QAC7CpB,KAAK,EAAEA;MACT,CAAC,CAAC,EAAE,aAAa7J,KAAK,CAACkL,aAAa,CAAC,OAAO,EAAElM,QAAQ,CAAC,CAAC,CAAC,EAAEe,SAAS,CAACuK,UAAU,EAAE;QAC/Ec,IAAI,EAAE,IAAI;QACV7E,IAAI,EAAE;MACR,CAAC,CAAC,EAAE;QACFqD,EAAE,EAAEA;QACJ;AACR;AACA;AACA,WAHQ;QAIA7C,IAAI,EAAEA,IAAI;QACV4C,QAAQ,EAAEA,QAAQ;QAClBjF,IAAI,EAAE,MAAM;QACZ2G,GAAG,EAAE,IAAI,CAACC,aAAa;QACvBnJ,OAAO,EAAE,SAASA,OAAOA,CAACf,CAAC,EAAE;UAC3B,OAAOA,CAAC,CAACmK,eAAe,CAAC,CAAC;QAC5B,CAAC,CAAC;QAAA;;QAEF7I,GAAG,EAAE,IAAI,CAAC8I,KAAK,CAACrK,GAAG;QACnB0I,KAAK,EAAE5K,aAAa,CAAC;UACnBwM,OAAO,EAAE;QACX,CAAC,EAAE1B,MAAM,CAAC2B,KAAK,CAAC;QAChBlC,SAAS,EAAEE,UAAU,CAACgC,KAAK;QAC3BnK,MAAM,EAAEA;MACV,CAAC,EAAEiJ,QAAQ,EAAE;QACXvH,QAAQ,EAAEA,QAAQ;QAClB0I,QAAQ,EAAE,IAAI,CAACA;MACjB,CAAC,EAAE3B,OAAO,IAAI,IAAI,GAAG;QACnBA,OAAO,EAAEA;MACX,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAAC;IACrB;EACF,CAAC,CAAC,CAAC;EACH,OAAO3J,YAAY;AACrB,CAAC,CAACL,SAAS,CAAC;AACZ,eAAeK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}