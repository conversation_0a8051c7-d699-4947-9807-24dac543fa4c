{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { VALUE_SPLIT } from \"../utils/commonUtil\";\n/** Lazy parse options data into conduct-able info to avoid perf issue in single mode */\nexport default (function (options, fieldNames) {\n  var cacheRef = React.useRef({\n    options: [],\n    info: {\n      keyEntities: {},\n      pathKeyEntities: {}\n    }\n  });\n  var getEntities = React.useCallback(function () {\n    if (cacheRef.current.options !== options) {\n      cacheRef.current.options = options;\n      cacheRef.current.info = convertDataToEntities(options, {\n        fieldNames: fieldNames,\n        initWrapper: function initWrapper(wrapper) {\n          return _objectSpread(_objectSpread({}, wrapper), {}, {\n            pathKeyEntities: {}\n          });\n        },\n        processEntity: function processEntity(entity, wrapper) {\n          var pathKey = entity.nodes.map(function (node) {\n            return node[fieldNames.value];\n          }).join(VALUE_SPLIT);\n          wrapper.pathKeyEntities[pathKey] = entity;\n\n          // Overwrite origin key.\n          // this is very hack but we need let conduct logic work with connect path\n          entity.key = pathKey;\n        }\n      });\n    }\n    return cacheRef.current.info.pathKeyEntities;\n  }, [fieldNames, options]);\n  return getEntities;\n});", "map": {"version": 3, "names": ["_objectSpread", "React", "convertDataToEntities", "VALUE_SPLIT", "options", "fieldNames", "cacheRef", "useRef", "info", "keyEntities", "pathKeyEntities", "getEntities", "useCallback", "current", "initWrapper", "wrapper", "processEntity", "entity", "path<PERSON><PERSON>", "nodes", "map", "node", "value", "join", "key"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-cascader/es/hooks/useEntities.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport * as React from 'react';\nimport { convertDataToEntities } from \"rc-tree/es/utils/treeUtil\";\nimport { VALUE_SPLIT } from \"../utils/commonUtil\";\n/** Lazy parse options data into conduct-able info to avoid perf issue in single mode */\nexport default (function (options, fieldNames) {\n  var cacheRef = React.useRef({\n    options: [],\n    info: {\n      keyEntities: {},\n      pathKeyEntities: {}\n    }\n  });\n  var getEntities = React.useCallback(function () {\n    if (cacheRef.current.options !== options) {\n      cacheRef.current.options = options;\n      cacheRef.current.info = convertDataToEntities(options, {\n        fieldNames: fieldNames,\n        initWrapper: function initWrapper(wrapper) {\n          return _objectSpread(_objectSpread({}, wrapper), {}, {\n            pathKeyEntities: {}\n          });\n        },\n        processEntity: function processEntity(entity, wrapper) {\n          var pathKey = entity.nodes.map(function (node) {\n            return node[fieldNames.value];\n          }).join(VALUE_SPLIT);\n          wrapper.pathKeyEntities[pathKey] = entity;\n\n          // Overwrite origin key.\n          // this is very hack but we need let conduct logic work with connect path\n          entity.key = pathKey;\n        }\n      });\n    }\n    return cacheRef.current.info.pathKeyEntities;\n  }, [fieldNames, options]);\n  return getEntities;\n});"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,2BAA2B;AACjE,SAASC,WAAW,QAAQ,qBAAqB;AACjD;AACA,gBAAgB,UAAUC,OAAO,EAAEC,UAAU,EAAE;EAC7C,IAAIC,QAAQ,GAAGL,KAAK,CAACM,MAAM,CAAC;IAC1BH,OAAO,EAAE,EAAE;IACXI,IAAI,EAAE;MACJC,WAAW,EAAE,CAAC,CAAC;MACfC,eAAe,EAAE,CAAC;IACpB;EACF,CAAC,CAAC;EACF,IAAIC,WAAW,GAAGV,KAAK,CAACW,WAAW,CAAC,YAAY;IAC9C,IAAIN,QAAQ,CAACO,OAAO,CAACT,OAAO,KAAKA,OAAO,EAAE;MACxCE,QAAQ,CAACO,OAAO,CAACT,OAAO,GAAGA,OAAO;MAClCE,QAAQ,CAACO,OAAO,CAACL,IAAI,GAAGN,qBAAqB,CAACE,OAAO,EAAE;QACrDC,UAAU,EAAEA,UAAU;QACtBS,WAAW,EAAE,SAASA,WAAWA,CAACC,OAAO,EAAE;UACzC,OAAOf,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEe,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;YACnDL,eAAe,EAAE,CAAC;UACpB,CAAC,CAAC;QACJ,CAAC;QACDM,aAAa,EAAE,SAASA,aAAaA,CAACC,MAAM,EAAEF,OAAO,EAAE;UACrD,IAAIG,OAAO,GAAGD,MAAM,CAACE,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAE;YAC7C,OAAOA,IAAI,CAAChB,UAAU,CAACiB,KAAK,CAAC;UAC/B,CAAC,CAAC,CAACC,IAAI,CAACpB,WAAW,CAAC;UACpBY,OAAO,CAACL,eAAe,CAACQ,OAAO,CAAC,GAAGD,MAAM;;UAEzC;UACA;UACAA,MAAM,CAACO,GAAG,GAAGN,OAAO;QACtB;MACF,CAAC,CAAC;IACJ;IACA,OAAOZ,QAAQ,CAACO,OAAO,CAACL,IAAI,CAACE,eAAe;EAC9C,CAAC,EAAE,CAACL,UAAU,EAAED,OAAO,CAAC,CAAC;EACzB,OAAOO,WAAW;AACpB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}