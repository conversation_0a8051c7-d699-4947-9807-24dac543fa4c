/**
 * 精确复制GM工具的MessagePack格式测试
 */

const msgpack = require('msgpack-lite');
const net = require('net');

function testExactMessagePack() {
    console.log('=== 精确复制GM工具MessagePack格式测试 ===');
    
    // 使用GM工具成功发送的加密数据
    const encryptedData = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';
    
    console.log('加密数据长度:', encryptedData.length);
    
    // 按照GM工具的方式打包：mp.pack{encryptedData}
    const packedData = msgpack.encode([encryptedData]);
    
    console.log('MessagePack数据长度:', packedData.length);
    console.log('首字节: 0x' + packedData[0].toString(16).padStart(2, '0').toUpperCase());
    
    // 显示前32字节
    const hexStr = Array.from(packedData.slice(0, 32))
        .map(b => b.toString(16).padStart(2, '0').toUpperCase())
        .join(' ');
    console.log('前32字节:', hexStr);
    
    // 期望的GM工具数据
    console.log('\n=== 与GM工具对比 ===');
    console.log('GM工具长度: 374');
    console.log('我们的长度:', packedData.length);
    console.log('GM工具首字节: 0x91');
    console.log('我们的首字节: 0x' + packedData[0].toString(16).padStart(2, '0').toUpperCase());
    console.log('GM工具前16字节: 91 DA 01 72 71 4C 2C 64 65 2C 63 6A 2C 78 69 2C');
    console.log('我们的前16字节:', hexStr.substring(0, 47));
    
    // 尝试连接并发送
    console.log('\n=== 尝试发送到服务器 ===');
    
    const client = new net.Socket();
    
    client.connect(6888, '127.0.0.1', () => {
        console.log('✅ 连接成功');
        
        // 发送数据
        client.write(packedData);
        console.log('✅ 数据已发送');
    });
    
    client.on('data', (data) => {
        console.log('✅ 接收到服务器响应 - 长度:', data.length);
        console.log('响应前32字节:', Array.from(data.slice(0, 32))
            .map(b => b.toString(16).padStart(2, '0').toUpperCase())
            .join(' '));
        
        client.destroy();
    });
    
    client.on('close', () => {
        console.log('🔌 连接已关闭');
    });
    
    client.on('error', (err) => {
        console.error('❌ 连接错误:', err.message);
    });
    
    // 10秒后自动关闭
    setTimeout(() => {
        if (!client.destroyed) {
            console.log('⏰ 超时关闭连接');
            client.destroy();
        }
    }, 10000);
}

// 运行测试
testExactMessagePack();
