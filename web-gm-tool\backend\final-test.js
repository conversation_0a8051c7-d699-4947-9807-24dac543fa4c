/**
 * 最终对比测试
 */

const { jm } = require('./src/protocol/encryption');

const testData = '112345*-*12345do local ret={["账号"]="888888",["密码"]="888888"} return ret end12345*-*12345';

console.log('=== 最终加密对比 ===');

// 我们的加密结果
const ourResult = jm(testData);
console.log('我们的结果长度:', ourResult.length);

// GM工具的结果（从日志复制）
const gmResult = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';
console.log('GM工具结果长度:', gmResult.length);

console.log('长度差异:', Math.abs(ourResult.length - gmResult.length));

// 分割并对比
const ourParts = ourResult.split(',');
const gmParts = gmResult.split(',');

console.log('\n部分数量对比:');
console.log('我们:', ourParts.length);
console.log('GM工具:', gmParts.length);

// 找到第一个不同的部分
console.log('\n逐部分对比:');
const minLen = Math.min(ourParts.length, gmParts.length);
let firstDiff = -1;
for (let i = 0; i < minLen; i++) {
    if (ourParts[i] !== gmParts[i]) {
        firstDiff = i;
        console.log(`第${i}部分不同:`);
        console.log(`  我们: '${ourParts[i]}'`);
        console.log(`  GM:   '${gmParts[i]}'`);
        break;
    }
}

if (firstDiff === -1) {
    console.log('前', minLen, '部分完全相同！');
    if (ourParts.length !== gmParts.length) {
        console.log('但长度不同:');
        if (ourParts.length > gmParts.length) {
            console.log('我们多出的部分:', ourParts.slice(gmParts.length));
        } else {
            console.log('GM多出的部分:', gmParts.slice(ourParts.length));
        }
    }
}

// 显示相同的前缀长度
if (firstDiff > 0) {
    const commonPrefix = ourParts.slice(0, firstDiff).join(',');
    console.log('\n相同前缀长度:', commonPrefix.length);
    console.log('相同前缀部分数:', firstDiff);
}

// 显示结尾对比
console.log('\n结尾对比:');
console.log('我们的最后5部分:', ourParts.slice(-5));
console.log('GM的最后5部分:', gmParts.slice(-5));
