-- @Author: ASUS
-- @Date:   2021-11-20 10:52:13
-- @Last Modified by:   ASUS
-- @Last Modified time: 2025-05-12 20:38:09
local 房屋处理类 = class()


function 房屋处理类:初始化()
        self.地契编号=0
        self.地契ID=0
        self.庭院ID=0
        self.房屋ID=0
        self.牧场ID=0
        self.阁楼ID=0
        self.庭院装饰 = {}
        self.室内装饰 = {}
        self.阁楼装饰 = {}
        self.牧场装饰 = {}
        self.写出数据 = {}
        self.牧场地图=1892
        self.庭院地图=1420
        self.房屋地图=1401
        self.阁楼地图=1938
        self.庭院规模="初级"
        self.房屋规模="初级"
        self.房屋样式="复古"
		self.是否创建=false
end

function 房屋处理类:加载数据(账号,id)
	    self.玩家id = tonumber(id)
		if f函数.文件是否存在([[data/]]..账号..[[/]]..id..[[/房屋数据.txt]])==false then
			self.写出数据 = {是否创建=false}
    		写出文件([[data/]]..账号..[[/]]..id..[[/房屋数据.txt]],table.tostring(self.写出数据))
    	else
    	   self.写出数据=table.loadstring(读入文件([[data/]]..账号..[[/]]..id..[[/房屋数据.txt]]))
    	end
    	if self.写出数据.是否创建 then
    		self:加载房屋(self.写出数据)
    		self.是否创建 = true
    	end
    	self.写出数据 = {}
end

function 房屋处理类:加载房屋(数据)
	     self.地契ID=self.玩家id
	     self.地契编号=数据.地契编号
	     self.庭院规模=数据.庭院规模
         self.房屋规模=数据.房屋规模
         self.房屋样式=数据.房屋样式
         self.庭院地图=初始庭院地图[self.庭院规模]
         self.房屋地图=初始房屋地图[self.房屋规模][self.房屋样式]
         self.阁楼地图=初始房屋地图.二楼[self.房屋样式]
		 self.庭院ID=tonumber(self.地契ID..1)
         self.房屋ID=tonumber(self.地契ID..2)
         self.牧场ID=tonumber(self.地契ID..3)
	     self.阁楼ID=tonumber(self.地契ID..4)
	     地图处理类:加载地图(self.庭院ID,self.庭院地图)
	     地图处理类:加载NPC(self.庭院ID,self:庭院假人(self.庭院地图))
	     地图处理类:加载传送(self.庭院ID,self:取传送圈(self.庭院地图,self.房屋ID,nil,self.房屋地图))

	     地图处理类:加载地图(self.房屋ID,self.房屋地图)
	     地图处理类:加载NPC(self.房屋ID,self:房屋假人(self.房屋规模))
	     地图处理类:加载传送(self.房屋ID,self:取传送圈(self.房屋地图,self.阁楼ID,self.庭院ID,self.房屋地图,self.庭院地图))
    	 地图处理类:加载地图(self.牧场ID,1892)
    	-- 地图处理类:加载NPC(self.牧场ID,{名称="牧场看守",模型="男人_兰虎",x=22,y=33,方向=3,执行事件="不执行",地图颜色=0})
	     地图处理类:加载地图(self.阁楼ID,self.阁楼地图)
	     地图处理类:加载传送(self.阁楼ID,self:取传送圈(self.阁楼地图,self.房屋ID,nil,self.房屋地图))

		 if 数据.庭院装饰 then
	        self.庭院装饰 = 数据.庭院装饰
	     end
	     if 数据.室内装饰 then
	        self.室内装饰 = 数据.室内装饰
	     end
         if 数据.阁楼装饰 then
	        self.阁楼装饰 = 数据.阁楼装饰
	     end
         if 数据.牧场装饰 then
	        self.牧场装饰 = 数据.牧场装饰
	     end

end

function 房屋处理类:重新加载房屋()
	     self.庭院地图=初始庭院地图[self.庭院规模]
         self.房屋地图=初始房屋地图[self.房屋规模][self.房屋样式]
         self.阁楼地图=初始房屋地图.二楼[self.房屋样式]
		 地图处理类:加载地图(self.庭院ID,self.庭院地图)
		 地图处理类:加载NPC(self.庭院ID,self:庭院假人(self.庭院地图))
	     地图处理类:加载传送(self.庭院ID,self:取传送圈(self.庭院地图,self.房屋ID,nil,self.房屋地图))
	     地图处理类:加载地图(self.房屋ID,self.房屋地图)
	     地图处理类:加载NPC(self.房屋ID,self:房屋假人(self.房屋规模))
	     地图处理类:加载传送(self.房屋ID,self:取传送圈(self.房屋地图,self.阁楼ID,self.庭院ID,self.房屋地图,self.庭院地图))
    	 地图处理类:加载地图(self.牧场ID,1892)
    	-- 地图处理类:加载NPC(self.牧场ID,{名称="牧场看守",模型="男人_兰虎",x=22,y=33,方向=3,执行事件="不执行",地图颜色=0})
	     地图处理类:加载地图(self.阁楼ID,self.阁楼地图)
	     地图处理类:加载传送(self.阁楼ID,self:取传送圈(self.阁楼地图,self.房屋ID,nil,self.房屋地图))



end




function 房屋处理类:取传送圈(地图ID,目标,目标1,目标2,目标3)
	 local 传送 ={}
	if 地图ID == 1420 then--初级
		传送[1] = {x=12,y=29,目标=1001,坐标={485,140}} --"庭院进长安城"
		传送[2] = {x=38,y=25,目标=目标,坐标=self:取传送XY(目标2)}--庭院进室内
	elseif 地图ID == 1421 then--中级
		传送[1] = {x=12,y=24,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=45,y=25,目标=目标,坐标=self:取传送XY(目标2)}--庭院进室内
	elseif 地图ID == 1422 then--高级
		传送[1] = {x=16,y=31,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=45,y=24,目标=目标,坐标=self:取传送XY(目标2)}--庭院进室内
    elseif 地图ID == 1424 then--顶级
		传送[1] = {x=14,y=50,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=72,y=18,目标=目标,坐标=self:取传送XY(目标2)}--庭院进室内
	elseif 地图ID == 1306 then--园林水榭
		传送[1] = {x=9,y=86,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=81,y=31,目标=目标,坐标=self:取传送XY(目标2)}--庭院进室内
	elseif 地图ID == 1885 then--农家小院
		传送[1] = {x=10,y=54,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=42,y=6,目标=目标,坐标=self:取传送XY(目标2)} --庭院进室内
	elseif 地图ID == 1380 then--欢乐童年
		传送[1] = {x=21,y=21,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=84,y=32,目标=目标,坐标=self:取传送XY(目标2)} --庭院进室内
	elseif 地图ID == 1382 then--白雪皑皑
		传送[1] = {x=66,y=86,目标=1001,坐标={485,140}}--"庭院进长安城"
		传送[2] = {x=92,y=42,目标=目标,坐标=self:取传送XY(目标2)} --庭院进室内
	---------------------
	elseif 地图ID==1401 or 地图ID==1404 or 地图ID==1407 or 地图ID==1410 or 地图ID==1413 or 地图ID==1416 or 地图ID==1330 or 地图ID==1333 or 地图ID==1336 or 地图ID==1953 or 地图ID==1958 or 地图ID==1965 then--初级房屋
	    传送[1] = {x=10,y=23,目标=目标1,坐标=self:取传送XY(目标3)} --室内进庭院

    elseif 地图ID==1402 or 地图ID==1405 or 地图ID==1408 or 地图ID==1411 or 地图ID==1414 or 地图ID==1417 or 地图ID==1331 or 地图ID==1334 or 地图ID==1337 or 地图ID==1954 or 地图ID==1959 or 地图ID==1966 then--中级房屋
	    传送[1] = {x=15,y=28,目标=目标1,坐标=self:取传送XY(目标3)} --室内进庭院
    elseif 地图ID==1403 or 地图ID==1406 or 地图ID==1409 or 地图ID==1412 or 地图ID==1415 or 地图ID==1418 or 地图ID==1332 or 地图ID==1335 or 地图ID==1338 or 地图ID==1955 or 地图ID==1960 or 地图ID==1967 then--高级房屋
	    传送[1] = {x=23,y=42,目标=目标1,坐标=self:取传送XY(目标3)} --室内进庭院
    elseif 地图ID==1312 or 地图ID==1311 or 地图ID==1310 or 地图ID==1313 or 地图ID==1314 or 地图ID==1315 or 地图ID==1316 or 地图ID==1317 or 地图ID==1318 or 地图ID==1319 or 地图ID==1961 or 地图ID==1968 then--顶级房屋
	    传送[1] = {x=17,y=50,目标=目标1,坐标=self:取传送XY(目标3)} --室内进庭院
    elseif 地图ID==1322 or 地图ID==1321 or 地图ID==1320 or 地图ID==1323 or 地图ID==1324 or 地图ID==1325 or 地图ID==1326 or 地图ID==1327 or 地图ID==1328 or 地图ID==1329 or 地图ID==1962 or 地图ID==1969 then--华宅房屋
		传送[1] = {x=45,y=69,目标=目标1,坐标=self:取传送XY(目标3)}--室内进庭院
		------------------------
	elseif 地图ID ==1933 or 地图ID ==1935 or 地图ID ==1937 or 地图ID ==1939 or 地图ID ==1941 or 地图ID ==1943 or 地图ID ==1945 or 地图ID ==1947 or 地图ID ==1949 or 地图ID ==1951 or 地图ID ==1963 or 地图ID ==1970 then--豪门一层
		传送[1] = {x=37,y=51,目标=目标1,坐标=self:取传送XY(目标3)} --室内进庭院
		传送[2] = {x=26,y=18,目标=目标,坐标={64,26}} --室内进阁楼
	elseif 地图ID ==1934 or 地图ID ==1936 or 地图ID ==1938 or 地图ID ==1940 or 地图ID ==1942 or 地图ID ==1944 or 地图ID ==1946 or 地图ID ==1948 or 地图ID ==1950 or 地图ID ==1952 or 地图ID ==1964 or 地图ID ==1971 then--豪门二层
		传送[1] = {x=61,y=27,目标=目标,坐标={23,21}} --阁楼进室内
    end



   return 传送
end

function 房屋处理类:取传送XY(地图ID)

	      local xy = {0,0}
	     if 地图ID==1401 or 地图ID==1404 or 地图ID==1407 or 地图ID==1410 or 地图ID==1413 or 地图ID==1416 or 地图ID==1330 or 地图ID==1333 or 地图ID==1336 or 地图ID==1953 or 地图ID==1958 or 地图ID==1965 then
            xy[1] = 20
            xy[2] = 18
         elseif 地图ID==1402 or 地图ID==1405 or 地图ID==1408 or 地图ID==1411 or 地图ID==1414 or 地图ID==1417 or 地图ID==1331 or 地图ID==1334 or 地图ID==1337 or 地图ID==1954 or 地图ID==1959 or 地图ID==1966 then--中级房屋
            xy[1] = 20
            xy[2] = 24
         elseif 地图ID==1403 or 地图ID==1406 or 地图ID==1409 or 地图ID==1412 or 地图ID==1415 or 地图ID==1418 or 地图ID==1332 or 地图ID==1335 or 地图ID==1338 or 地图ID==1955 or 地图ID==1960 or 地图ID==1967 then--高级房屋
         	xy[1] = 30
            xy[2] = 37
         elseif 地图ID==1312 or 地图ID==1311 or 地图ID==1310 or 地图ID==1313 or 地图ID==1314 or 地图ID==1315 or 地图ID==1316 or 地图ID==1317 or 地图ID==1318 or 地图ID==1319 or 地图ID==1961 or 地图ID==1968 then---顶级房屋
            xy[1] = 25
            xy[2] = 45
         elseif 地图ID==1322 or 地图ID==1321 or 地图ID==1320 or 地图ID==1323 or 地图ID==1324 or 地图ID==1325 or 地图ID==1326 or 地图ID==1327 or 地图ID==1328 or 地图ID==1329 or 地图ID==1962 or 地图ID==1969 then----华宅
         	xy[1] = 53
            xy[2] = 65
         elseif 地图ID ==1933 or 地图ID ==1935 or 地图ID ==1937 or 地图ID ==1939 or 地图ID ==1941 or 地图ID ==1943 or 地图ID ==1945 or 地图ID ==1947 or 地图ID ==1949 or 地图ID ==1951 or 地图ID ==1963 or 地图ID ==1970 then--豪门一层
            xy[1] = 43
            xy[2] = 48
         elseif 地图ID == 1420 then
         	    xy[1] = 18
                xy[2] = 25
         elseif 地图ID == 1421 then
         	    xy[1] = 20
                xy[2] = 21
        elseif 地图ID == 1422 then
         	    xy[1] = 24
                xy[2] = 26
        elseif 地图ID == 1424 then
         	    xy[1] = 28
                xy[2] = 42
         elseif 地图ID == 1306 then
                xy[1] = 11
                xy[2] = 85
          elseif 地图ID == 1885 then
          	     xy[1] = 18
                 xy[2] = 48
          elseif 地图ID == 1380 then
          	     xy[1] = 28
                 xy[2] = 27
          elseif 地图ID == 1382 then
          	     xy[1] = 74
                 xy[2] = 81

         end
         return xy
end











function 房屋处理类:庭院假人(地图)
	local 假人坐标={}
	if 地图 == 1885 then
		假人坐标[1] = {名称="牧场管理员",模型="男人_兰虎",X=24,Y=50,方向=1,执行事件="不执行",地图颜色=0}
	elseif 地图 == 1306 then
		假人坐标[1] = {名称="牧场管理员",模型="男人_兰虎",X=41,Y=41,方向=1,执行事件="不执行",地图颜色=0}
	elseif 地图 == 1380 then
		假人坐标[1] = {名称="牧场管理员",模型="男人_兰虎",X=61,Y=30,方向=1,执行事件="不执行",地图颜色=0}
	elseif 地图 == 1382 then
		假人坐标[1] = {名称="牧场管理员",模型="男人_兰虎",X=80,Y=80,方向=1,执行事件="不执行",地图颜色=0}
	end
	return 假人坐标
end

function 房屋处理类:房屋假人(规模)
	local 假人坐标={}
	if 规模=="初级" then--顶级
         假人坐标[1] =  {名称="管家",模型="男人_店小二",X=18,Y=24,方向=2,执行事件="不执行",地图颜色=0}
     elseif 规模=="中级" then --中级
		假人坐标[1] =  {名称="管家",模型="男人_店小二",X=21,Y=30,方向=2,执行事件="不执行",地图颜色=0}
     elseif 规模=="高级" then --高级
		假人坐标[1] =  {名称="管家",模型="男人_店小二",X=34,Y=43,方向=2,执行事件="不执行",地图颜色=0}
		假人坐标[2] = {名称="佣人",模型="女人_丫鬟",X=21,Y=38,方向=0,执行事件="不执行",地图颜色=0}
	elseif 规模=="顶级" then --顶级
		假人坐标[1] =  {名称="管家",模型="男人_店小二",X=27,Y=50,方向=2,执行事件="不执行",地图颜色=0}
		假人坐标[2] = {名称="佣人",模型="女人_丫鬟",X=17,Y=45,方向=0,执行事件="不执行",地图颜色=0}
	elseif 规模=="华宅" then--华宅
		假人坐标[1] = {名称="管家",模型="男人_店小二",X=43,Y=64,方向=0,执行事件="不执行",地图颜色=0}
		假人坐标[2] = {名称="佣人",模型="女人_丫鬟",X=53,Y=69,方向=2,执行事件="不执行",地图颜色=0}
	elseif 规模=="豪门" then--豪门双层阁楼
		假人坐标[1] = {名称="管家",模型="男人_店小二",X=36,Y=44,方向=0,执行事件="不执行",地图颜色=0}
		假人坐标[2] = {名称="佣人",模型="女人_丫鬟",X=51,Y=51,方向=2,执行事件="不执行",地图颜色=0}
	end
	return 假人坐标
end



function 房屋处理类:烹饪处理()
	     local 物品表={}
	     local 临时等级 = 10
		 if self.房屋规模=="初级" then
		     物品表={"烤肉","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
		     临时等级 = 40
		 elseif self.房屋规模=="中级" then
		        物品表={"烤肉","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
		        临时等级 = 80
		 elseif self.房屋规模=="高级" then
		        物品表={"烤肉","长寿面","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","包子","烤鸭","虎骨酒","包子","女儿红"}
		        临时等级 = 100
		 elseif self.房屋规模=="顶级" then
		         物品表={"烤肉","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","佛跳墙","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","烤鸭","虎骨酒"}
		         临时等级 = 120
		 elseif self.房屋规模=="华宅" then
		        物品表={"烤肉","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","佛跳墙","珍露酒","烤鸭","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","烤鸭","虎骨酒"}
		        临时等级 = 140
		 else
		         物品表={"烤肉","醉生梦死","蛇胆酒","百味酒","梅花酒","长寿面","翡翠豆腐","桂花丸","珍露酒","烤鸭","豆斋果","烤鸭","臭豆腐","佛跳墙","烤鸭","虎骨酒"}
		         临时等级 = 180
        end
	  local 临时物品=物品表[取随机数(1,#物品表)]
	  local 临时品质=0
	  if 临时物品~="包子" then
	    临时品质=取随机数(math.floor(临时等级*0.5),math.floor(临时等级*1.5))
	  end
	  玩家数据[self.玩家id].角色:扣除银子(100000,"房屋烹饪",1)
	  常规提示(self.玩家id,"#Y/经过一阵忙碌，你烹制出了#G/"..临时品质.."#Y/品的#R/"..临时物品)
	  玩家数据[self.玩家id].道具:给予道具(self.玩家id,临时物品,1,临时品质)
	  道具刷新(self.玩家id)
end


function 房屋处理类:炼药处理()
         local 物品表={}
	     local 临时等级 = 10
		 if self.房屋规模=="初级" then
		     物品表={"金创药","金创药","金创药","金香玉","金创药","金创药","小还丹","金创药","金创药","千年保心丹","金创药","金创药","风水混元丹","金创药","金创药","定神香","金创药","金创药","蛇蝎美人","金创药","金创药","九转回魂丹","金创药","金创药","佛光舍利子","金创药","金创药","十香返生丸","金创药","金创药","金创药","金创药","五龙丹"}
		     临时等级 = 40
		 elseif self.房屋规模=="中级" then
		        物品表={"金创药","金创药","金香玉","金创药","小还丹","金创药","千年保心丹","金创药","风水混元丹","金创药","定神香","金创药","金创药","蛇蝎美人","金创药","九转回魂丹","金创药","金创药","佛光舍利子","金创药","十香返生丸","金创药","金创药","五龙丹"}
		        临时等级 = 80
		 elseif self.房屋规模=="高级" then
		        物品表={"金创药","金香玉","小还丹","千年保心丹","风水混元丹","金创药","定神香","金创药","蛇蝎美人","金创药","九转回魂丹","金创药","佛光舍利子","金创药","十香返生丸","金创药","五龙丹"}
		        临时等级 = 100
		 elseif self.房屋规模=="顶级" then
		         物品表={"金创药","金香玉","小还丹","千年保心丹","风水混元丹","金创药","定神香","金创药","蛇蝎美人","九转回魂丹","佛光舍利子","十香返生丸","五龙丹"}
		         临时等级 = 120
		 elseif self.房屋规模=="华宅" then
		        物品表={"金香玉","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","佛光舍利子","十香返生丸","五龙丹","秘制红罗羹"}
		        临时等级 = 140
		 else
		         物品表={"金香玉","小还丹","千年保心丹","风水混元丹","定神香","蛇蝎美人","九转回魂丹","佛光舍利子","十香返生丸","五龙丹","秘制绿罗羹","秘制红罗羹"}
		         临时等级 = 180
        end
	   玩家数据[self.玩家id].角色:扣除银子(100000,"炼药消耗",1)
	   local 临时物品=物品表[取随机数(1,#物品表)]
	   local 临时品质=0
	   if 临时物品~="金创药" and 临时物品~="秘制绿罗羹" and 临时物品~="秘制红罗羹" then
	      临时品质=取随机数(math.floor(临时等级*0.5),math.floor(临时等级*1.5))
	   end
	   玩家数据[self.玩家id].道具:给予道具(self.玩家id,临时物品,1,临时品质)
	   常规提示(self.玩家id,"#Y/经过一阵忙碌，炼制出了#G/"..临时品质.."#Y/品的#R/"..临时物品)
	   道具刷新(self.玩家id)
end







function 房屋处理类:房屋初始化()
	    self.地契编号=0
        self.地契ID=0
        self.庭院ID=0
        self.房屋ID=0
        self.牧场ID=0
        self.阁楼ID=0
        self.庭院装饰 = {}
        self.室内装饰 = {}
        self.阁楼装饰 = {}
        self.牧场装饰 = {}
        self.写出数据 = {}
        self.牧场地图=1892
        self.庭院地图=1420
        self.房屋地图=1401
        self.庭院规模="初级"
        self.房屋规模="初级"
        self.房屋样式="复古"

		self.是否创建=false
end

--玩家数据[id].房屋:加载房屋(self.数据[道具id])
function 房屋处理类:存档()
	     if self.是否创建 then
	     	--房屋数据[self.玩家id]={是否创建=self.是否创建,账号=玩家数据[self.玩家id].账号,玩家id=self.玩家id}
	     	self.写出数据 = {}
	        self.写出数据 = {地契ID=self.地契ID,地契编号=self.地契编号,是否创建=self.是否创建,地契ID=self.地契ID,庭院规模=self.庭院规模,房屋规模=self.房屋规模,房屋样式=self.房屋样式,庭院装饰=self.庭院装饰,室内装饰=self.室内装饰,阁楼装饰=self.阁楼装饰,牧场装饰=self.牧场装饰}
            写出文件([[data/]]..玩家数据[self.玩家id].账号..[[/]]..self.玩家id..[[/房屋数据.txt]],table.tostring(self.写出数据))
            self.写出数据 = {}
	     end
end







return 房屋处理类