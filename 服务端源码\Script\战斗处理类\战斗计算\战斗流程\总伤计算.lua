function 战斗处理类:取计算结果(编号,目标,伤害,暴击,流程,挨打,系数,名称,等级,分类,保护)

          --系数= {结果系数=1,结果伤害=0,特效={}}
          if not 系数.特效 then 系数.特效={} end
          系数.结果系数 = 系数.结果系数 + self:取阵法克制(编号,目标)
          if self.参战单位[编号].奇经八脉嗜血 then
              系数.结果系数 = 系数.结果系数 + 0.3
          end
          if self.参战单位[编号].奇经八脉跃动 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].奇经八脉噬魂 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].奇经八脉蔓延 then
              系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].出其不意加成 then
              系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].奇经八脉.獠牙 then
              系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[编号].奇经八脉.暴突 then
              系数.结果系数 = 系数.结果系数 + 0.03
          end
          if self.参战单位[编号].奇经八脉.因缘 then
              系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[目标].法术状态.绝殇 then
              系数.结果系数 = 系数.结果系数 + 0.02
          end
          if self.参战单位[目标].灵宝惊兽云尺 then
              系数.结果系数 = 系数.结果系数 + 0.5
          end
          if self.参战单位[目标].奇经八脉摧心 then
              系数.结果系数 = 系数.结果系数 + 0.12
          end
          if self.参战单位[编号].奇经八脉.雷吞 then
              if self:取装备五行(编号,3)=="金" then
                 系数.结果系数 = 系数.结果系数 + 0.05
              end
              if self:取装备五行(编号,4)=="金" then
                 系数.结果系数 = 系数.结果系数 + 0.05
              end
          end
          if self.参战单位[编号].奇经八脉.水漾 then
              if self:取装备五行(编号,3)=="水" then
                 系数.结果系数 = 系数.结果系数 + 0.04
              end
              if self:取装备五行(编号,4)=="水" then
                 系数.结果系数 = 系数.结果系数 + 0.04
              end
          end
          if self.参战单位[编号].奇经八脉狮驼怒火 then
             系数.结果系数 = 系数.结果系数 + 0.32
          end
          if self.参战单位[目标].法术状态.落花成泥 then
              系数.结果系数 = 系数.结果系数 + 0.24
          end
          if self.参战单位[编号].奇经八脉.灵能 and 取随机数()<=20 then
              系数.结果系数 = 系数.结果系数 + 0.12
          end
          if self.参战单位[编号].信仰 and self.参战单位[编号].信仰==3 then
              系数.结果系数 = 系数.结果系数 + 0.15
          end

          if self.参战单位[编号].灵宝战神宝典  then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].灵宝战神宝典
          end
          if self.参战单位[编号].灵宝寒霜盾戟 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].灵宝寒霜盾戟
          end
          if self.参战单位[编号].灵宝真阳令旗 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].灵宝真阳令旗
          end
          if self.参战单位[编号].奇经八脉.族魂 and self:取是否单独门派(编号) then
               系数.结果系数 = 系数.结果系数 +0.2
          end
          if self.参战单位[编号].奇经八脉.破军 and self.参战单位[编号].剑意>=5 then
               系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[目标].雷法削弱 then
              系数.结果系数 = 系数.结果系数 + 0.1 * self.参战单位[目标].雷法削弱.加成
          end

          if self.参战单位[编号].法术状态.诸天看护 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].法术状态.诸天看护.等级
          end
          if self.参战单位[编号].奇经八脉狩猎 and self.参战单位[目标].类型~="角色" then
              系数.结果系数 = 系数.结果系数 + 0.12
          end
          if self.参战单位[编号].奇经八脉逞凶 and self.参战单位[目标].类型 ~="角色" then
              系数.结果系数 = 系数.结果系数 + 0.12
          end
          if self.参战单位[编号].奇经八脉.破印 and self.参战单位[目标].类型~="角色" then
              系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].奇经八脉.电掣 and self.参战单位[目标].类型 ~="角色" then
              系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[编号].奇经八脉攻伐 and  self.参战单位[编号].奇经八脉攻伐<=3 then
              系数.结果系数 = 系数.结果系数 + 0.06
          end
          if self.参战单位[编号].奇经八脉.花影 and self.参战单位[目标].法术状态.暗器毒 then
              系数.结果系数 = 系数.结果系数 + 0.08
          end
          if self.参战单位[编号].奇经八脉.毒慑 and self.参战单位[目标].法术状态.尸腐毒 then
              系数.结果系数 = 系数.结果系数 + 0.04
           end
          if self.参战单位[编号].类型=="角色" and self:取队伍奇经八脉(编号,"贯通") then
              系数.结果系数 = 系数.结果系数 + 0.04
          end
          if  self.参战单位[编号].类型~="角色" and self:取队伍奇经八脉(编号,"驭兽") then
              系数.结果系数 = 系数.结果系数 + 0.03
          end

          if self.参战单位[编号].法术状态.颠倒五行 and self.参战单位[编号].法术状态.颠倒五行.万象 then
              系数.结果系数 = 系数.结果系数 + 0.08
          end
          if self.参战单位[目标].类型~="角色" and  self.参战单位[编号].奇经八脉.慧眼 and 取随机数()<=50 then
              系数.结果系数 = 系数.结果系数 + 0.6
              if 取随机数()<=60 then
                  self.参战单位[目标].奇经八脉慧眼 =  1
              end
          end
          if self.参战单位[编号].奇经八脉.雷鸣 and self.参战单位[目标].气血<= self.参战单位[目标].最大气血*0.5 then
              系数.结果系数 = 系数.结果系数 + 0.03
          end
          if self.参战单位[编号].奇经八脉.神采 and self.参战单位[编号].气血>= self.参战单位[编号].最大气血*0.7 then
              系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[目标].法术状态.摄魂 then
              local 加成效果 = self.参战单位[目标].法术状态.摄魂.境界 * 0.02+0.1
              if self.参战单位[目标].法术状态.摄魂.拘魄~=nil then
                     加成效果=加成效果*1.06
              end
              if self.参战单位[目标].法术状态.摄魂.聚魂~=nil then
                  加成效果=加成效果*1.045
              end
              系数.结果系数 = 系数.结果系数 + 加成效果
          end
          if self.参战单位[编号].符石技能.降妖伏魔 and self.参战单位[目标].鬼魂 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].符石技能.降妖伏魔/100
          end
          if self.参战单位[编号].法术状态.赤炎战笛~= nil then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].法术状态.赤炎战笛.境界
          end
          if self.参战单位[编号].战斗赐福 and self.参战单位[编号].战斗赐福.伤害结果>0 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].战斗赐福.伤害结果/100
          end
          if self.参战单位[编号].经脉流派=="虎贲上将" and #self.参战单位[编号].追加法术>0 then
              for k,v in pairs(self.参战单位[编号].追加法术) do
                    if 名称 == v.名称 then
                       if self.参战单位[编号].奇经八脉.效法 then ---
                            系数.结果系数 = 系数.结果系数 + 0.04
                       end
                       if self.参战单位[编号].奇经八脉.追戮 and #self.参战单位[编号].追加法术>=2 then
                          系数.结果系数 = 系数.结果系数 + 0.05
                       end
                       if self.参战单位[编号].奇经八脉.烈光 then
                          local 数量 = #self.参战单位[编号].追加法术
                          系数.结果系数 = 系数.结果系数 + 0.015*数量
                       end
                       if self.参战单位[编号].奇经八脉.历兵 then
                           self.参战单位[编号].伤害 = self.参战单位[编号].伤害*1.003
                       end
                       if self.参战单位[编号].奇经八脉.诛伤 and not self.参战单位[编号].首次触发[名称] then
                          系数.结果系数 = 系数.结果系数 + 0.1
                          self.参战单位[编号].首次触发[名称] = true
                       end
                       if self.参战单位[编号].奇经八脉.催迫 and self.参战单位[编号].奇经八脉催迫加成 then
                          系数.结果系数 = 系数.结果系数 + 0.12
                          self.参战单位[编号].奇经八脉催迫完成=1
                       end
                       if self.参战单位[编号].奇经八脉.攻伐 and not self.参战单位[编号].奇经八脉攻伐 then
                          self.参战单位[编号].奇经八脉攻伐=4
                       end
                    end
              end
          end
          if self.参战单位[目标].法术状态.锢魂术 ~= nil and self.参战单位[目标].法术状态.锢魂术.神器技能亡灵泣语 then --重写
              系数.结果系数 = 系数.结果系数 + (self.参战单位[目标].法术状态.锢魂术.神器技能亡灵泣语-1)
          end
           if self.参战单位[编号].奇经八脉.扑袭 and self:取是否单独门派(编号) and not self.参战单位[编号].奇经八脉扑袭 then
              系数.结果系数 = 系数.结果系数 + 0.4
              self.参战单位[编号].奇经八脉扑袭前置 = 1
          end
          if self.参战单位[编号].奇经八脉.霆震 and (self.参战单位[编号].法术状态.雷怒霆激 or self.参战单位[编号].法术状态.霹雳弦惊) then
                local 回合1 = 0
                local 回合2 = 0
                if self.参战单位[编号].法术状态.雷怒霆激 then
                   回合1 = self.参战单位[编号].法术状态.雷怒霆激.回合
                end
                if self.参战单位[编号].法术状态.霹雳弦惊 then
                   回合2 = self.参战单位[编号].法术状态.霹雳弦惊.回合
                end
                if 回合1 > 回合2 then
                    系数.结果系数 = 系数.结果系数 + (7-回合1)*0.01
                else
                   系数.结果系数 = 系数.结果系数 + (7-回合2)*0.01
                end
          end
          if self.参战单位[编号].类型~="角色" and self.参战单位[编号].主人 and self.参战单位[self.参战单位[编号].主人] and
              self.参战单位[self.参战单位[编号].主人].奇经八脉.狂化 and  self.参战单位[self.参战单位[编号].主人].经脉流派 =="万兽之王" and
              取随机数()<=12 then
              系数.结果系数 = 系数.结果系数 + 1
          end
          if self.参战单位[编号].奇经八脉.奉还 and self.参战单位[编号].奇经八脉奉还加成 and (not 战斗技能[名称].门派 or self.参战单位[目标].门派==战斗技能[名称].门派) then
              系数.结果系数 = 系数.结果系数 + 0.2
          end
          if self.参战单位[编号].魍魉追魂加成 then
              local 加成数量 = 1
              for i=1,#self.参战单位 do
                  if self.参战单位[i]~=nil and i~=编号 and self.参战单位[i].队伍 ==self.参战单位[编号].队伍 and self.参战单位[i].魍魉追魂加成 then
                     加成数量 =  加成数量 + 1
                  end
              end
              系数.结果系数 = 系数.结果系数 + (4+2*加成数量)/100
          end
          if self.参战单位[编号].奇经八脉.狱火 and self.参战单位[目标].法术状态.摄魂 and self.参战单位[目标].法术状态.摄魂.编号 and self.参战单位[目标].法术状态.摄魂.编号==编号 then
             系数.结果系数 = 系数.结果系数 + 0.12
          end
          if (self.参战单位[编号].法术状态.雷怒霆激 and self.参战单位[编号].法术状态.雷怒霆激.激越)  or (self.参战单位[编号].法术状态.霹雳弦惊 and self.参战单位[编号].法术状态.霹雳弦惊.激越) then
               系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].神器技能 and self.参战单位[编号].神器技能.名称=="流火"
            and math.floor(self.参战单位[编号].气血/self.参战单位[编号].最大气血*100)>math.floor(self.参战单位[目标].气血/self.参战单位[目标].最大气血*100) then
              系数.结果系数 = 系数.结果系数 +  self.参战单位[编号].神器技能.等级*0.08
          end
--------------------------------------------------------------------------------------削弱
          if self.参战单位[编号].玉砥柱 then
             系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[目标].移花接木 then
              系数.结果系数 = 系数.结果系数 - 0.3
          end
          if self.参战单位[编号].奇经八脉踏魄 then
              系数.结果系数 = 系数.结果系数 - 0.6
          end
          if self.参战单位[编号].奇经八脉摧心 then
              系数.结果系数 = 系数.结果系数 - 0.12
          end
          if self.参战单位[目标].奇经八脉叶护 then
              系数.结果系数 = 系数.结果系数 - 0.02
          end
          if self.参战单位[目标].法术状态.护佑 then
              系数.结果系数 = 系数.结果系数 - 0.5
          end
          if self.参战单位[编号].法术状态.冰川怒伤 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[目标].奇经八脉.安忍 then
              local  五行加成 = 0
              for n,v in pairs(self.参战单位[目标].五行珠) do
                   if self.参战单位[目标].五行珠[n] >0 then
                      五行加成 = 五行加成 + 1
                   end
              end
              系数.结果系数 = 系数.结果系数- 五行加成*0.01
           end
          if self.参战单位[目标].超级精神 then
              系数.结果系数 = 系数.结果系数 - 取随机数(1,10)/100
          end

          if self.参战单位[编号].玄武躯 or self.参战单位[编号].龙胄铠 then
             系数.结果系数 = 系数.结果系数 - 0.5
          end
          if self.参战单位[目标].信仰 and self.参战单位[目标].信仰==3 then
               系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[目标].灵宝乾坤土卷  then
               系数.结果系数 = 系数.结果系数 - self.参战单位[目标].灵宝乾坤土卷
          end

          if self.参战单位[编号].鬼魂 and self.参战单位[目标].奇经八脉.鬼念  then
              系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[编号].法术状态.修罗隐身 then
                系数.结果系数 = 系数.结果系数 - 0.3
                if self.参战单位[编号].隐匿击 and self.参战单位[编号].隐匿击>0 then
                   系数.结果系数 = 系数.结果系数 + 0.02 * self.参战单位[编号].隐匿击
                end
          end

          if self.参战单位[目标].神器技能风起云墨~=nil then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].神器技能风起云墨
          end
          if self.参战单位[目标].符石技能.真元护体 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].符石技能.真元护体/100
          end
          if self.参战单位[目标].奇经八脉.灵佑 and self.参战单位[目标].风灵>0 then
              系数.结果系数 = 系数.结果系数 - 0.02
          end

          local 降低 =true
          if self.参战单位[编号].法术状态.化怨清莲 and 降低 then--1 90  2-- 190
              系数.结果系数 = 系数.结果系数 - self.参战单位[编号].法术状态.化怨清莲.境界
              降低 = false
          end
          if self.参战单位[目标].法术状态.化怨清莲 and 降低 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].法术状态.化怨清莲.境界
          end
          if self.参战单位[目标].战斗赐福 and self.参战单位[目标].战斗赐福.伤害减免>0 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].战斗赐福.伤害减免/100
          end
          if self.参战单位[目标].奇经八脉.幻变 and self.参战单位[目标].法术状态.分身术 then
              系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[目标].奇经八脉.瘴幕 and self.参战单位[编号].法术状态.尸腐毒 then
              系数.结果系数 = 系数.结果系数 - 0.06
          end
          if self.参战单位[目标].奇经八脉.批亢 and  self.参战单位[目标].法术状态.分身术 then
              系数.结果系数 = 系数.结果系数 - 0.15
          end
          if self.参战单位[目标].类型~="角色" and self:取队伍奇经八脉(目标,"驭兽") then
              系数.结果系数 = 系数.结果系数 - 0.03
          end
          if self.参战单位[目标].神器技能 and self.参战单位[目标].神器技能.名称=="披坚执锐" then
              local 几率 = self.参战单位[目标].神器技能.等级*4
              if 取随机数()<=几率 then
                   系数.结果系数 = 系数.结果系数 - 0.9
              end
          end
          if self.参战单位[目标].法术状态.苍白纸人 then
               系数.结果系数 = 系数.结果系数 - self.参战单位[目标].法术状态.苍白纸人.境界*0.04
          elseif self.参战单位[目标].法术状态.五彩娃娃 then
                系数.结果系数 = 系数.结果系数 - self.参战单位[目标].法术状态.五彩娃娃.境界*0.025
          end
          if self.参战单位[编号].映日妙舞减伤 and self.参战单位[编号].映日妙舞减伤 == 目标 then
              系数.结果系数 = 系数.结果系数 - 0.15
          end

          if self.参战单位[目标].法术状态.护盾 and self.参战单位[目标].法术状态.护盾.灵宝护盾 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end

          if self.参战单位[目标].奇经八脉.玲珑 and 取随机数()<=20 then
              系数.结果系数 = 系数.结果系数 - 0.12
              self:增加愤怒(目标,10)
          end
          if self.参战单位[编号].法术状态.魔音摄魂~=nil and self.参战单位[目标].奇经八脉.魔音 then
               系数.结果系数 = 系数.结果系数 - 0.18
          end

           if self.参战单位[目标].类型~="角色" and self.参战单位[目标].主人 and self.参战单位[self.参战单位[目标].主人] and
              self.参战单位[self.参战单位[目标].主人].奇经八脉.守势  and not self.参战单位[目标].奇经八脉守势  then
              系数.结果系数 = 系数.结果系数 - 0.1
              self.参战单位[目标].奇经八脉守势 = 1
           end
          if self.参战单位[编号].法术状态.雾杀 and self.参战单位[编号].法术状态.雾杀.编号==目标 and self.参战单位[目标].奇经八脉.迷缚 then
              系数.结果系数 = 系数.结果系数 - 0.03
          end

          if self.参战单位[目标].类型~="角色" and self.参战单位[目标].法术状态.普渡众生 and self.参战单位[目标].法术状态.普渡众生.编号 then
              local 取编号 = self.参战单位[目标].法术状态.普渡众生.编号
              if self.参战单位[取编号] and self.参战单位[取编号].奇经八脉.慈佑 then
                 系数.结果系数 = 系数.结果系数 - 0.12
              end
          end
--------------------------------------------------
          if self.参战单位[目标].奇经八脉磐石 then
              系数.结果伤害 = 系数.结果伤害 - 55*self.参战单位[目标].奇经八脉磐石
          end
          if self.参战单位[编号].奇经八脉陷阱削弱 then
              系数.结果伤害 = 系数.结果伤害 - self.参战单位[编号].奇经八脉陷阱削弱
          end

          local 境界 = self:取指定法宝(编号,"奇门五行令")
          if 境界 and 取随机数(1,100) <= 境界 * 2 and self:取指定法宝(编号,"奇门五行令",1) then
                系数.结果系数 = 系数.结果系数 - 境界 * 0.01
                table.insert(系数.特效, "金甲仙衣")
          end
          if self.参战单位[目标].法术状态.菩提心佑 then
               系数.结果伤害 = 系数.结果伤害 - self.参战单位[编号].等级*5
          end
          if self:取指定法宝(self.参战单位[目标].主人,"失心钹",1) then
                系数.结果伤害 = 系数.结果伤害 - self:取指定法宝(self.参战单位[目标].主人,"失心钹")*15
          end
          if self.参战单位[目标].法术状态.碎甲符~=nil and self.参战单位[目标].法术状态.碎甲符.碎甲回合~=nil and self.回合数-self.参战单位[目标].法术状态.碎甲符.碎甲回合<=5 then
              local 持续加成 = self.回合数-self.参战单位[目标].法术状态.碎甲符.碎甲回合
              if 持续加成>0 then
                  系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].法术状态.碎甲符.碎甲加成*(1.2-持续加成*0.2)
              end
           end

          local 类型 = 1
          if 名称=="善恶有报" then
             if 取随机数()<=30 then
                类型 = 2
             else
                 系数.结果系数 = 系数.结果系数 + 0.8
             end
          end
          if 暴击 and 类型 ~= 2 then
              类型=3
              系数.结果系数 = 系数.结果系数 + 系数.暴伤系数
              系数.结果伤害 = 系数.结果伤害 + 系数.暴伤增加
          end
          if  系数.结果系数<=0 then 系数.结果系数 = 0.1 end
          伤害 = 伤害 * 系数.结果系数 + 系数.结果伤害
          if not self:取是否单独门派(编号) and 类型 ~= 2  then
                  local 同门 = 1
                  for i=1,#self.参战单位 do
                      if i~=编号 and self.参战单位[i]~=nil and self.参战单位[i].类型=="角色" and self.参战单位[i].门派~="无" and
                          self.参战单位[i].门派~="无门派 "and self.参战单位[i].队伍~=0 and self.参战单位[i].队伍==self.参战单位[编号].队伍 and
                          self.参战单位[i].门派==self.参战单位[编号].门派  then
                          同门 = 同门 + 1
                      end
                      if 同门==3 then
                          伤害 = 伤害 * 0.8
                      elseif 同门==4 then
                            伤害 = 伤害 * 0.7
                      elseif 同门>=5 then
                            伤害 = 伤害 * 0.6
                      end
                  end
          end
          if 分类=="物伤" and 类型 ~= 2  then
                  if self.参战单位[目标].物伤减少 then-----------重写
                      伤害 = 伤害 * self.参战单位[目标].物伤减少
                  end

          elseif 分类=="法伤" and 类型 ~= 2  then
                local 属性 =  战斗技能[名称].属性
                if 属性 and self.参战单位[目标][属性.."吸"] then
                    local 触发 = self.参战单位[目标][属性.."吸"]
                    if 取随机数()<=触发 then
                       类型=2
                    else
                        伤害 = math.floor(伤害 * 0.7)
                    end
                end
                if 属性 and  (self.参战单位[目标]["弱点"..属性] or self.参战单位[目标]["超级弱点"..属性]) and 类型~=2 then
                    伤害 = math.floor(伤害 * 1.5)
                end
                if self.参战单位[目标].法伤减少 and 类型~=2  then
                    伤害 = 伤害 * self.参战单位[目标].法伤减少
                end
          elseif 分类=="固伤" and 类型 ~= 2  then
                if self.参战单位[目标].固伤减少 then-----------重写
                      伤害 = 伤害 * self.参战单位[目标].固伤减少
                end
          elseif 分类=="合击" then
                  伤害 = math.floor(伤害*0.75)
          end
          if 系数.固定结果 and 类型~=2 then
              伤害 = math.floor(系数.固定结果)
          end
          self.战斗流程[流程].挨打方[挨打].护盾值=nil
          if 类型~=2 then
              if self.参战单位[目标].幸运 and self.参战单位[目标].超级幸运 and 取随机数()<=5  then
                  伤害=0
              elseif self.参战单位[目标].法术状态.波澜不惊 and self.参战单位[目标].法术状态.波澜不惊.等级<4 then
                  伤害 = 0
              elseif self.参战单位[目标].法术状态.天地同寿 and 分类=="法伤" then
                  伤害 = 0
                  table.insert(系数.特效, "天地同寿")
              elseif self.参战单位[目标].法术状态.同舟共济 and self.参战单位[目标].法术状态.同舟共济.等级<6 then
                  伤害 = math.floor(伤害 * 0.25)
              end
              if 伤害~=0 then
                  if self.参战单位[目标].法术状态.炎护 then --------所有
                      local 减免 = math.floor(伤害*0.5)
                      if self.参战单位[目标].魔法>减免 then
                           self:减少魔法(目标,减免)
                           伤害 = math.floor(伤害*0.5)
                      else
                          伤害 = 伤害 - self.参战单位[目标].魔法
                          self.参战单位[目标].魔法 = 0
                      end
                  end
                  if self.参战单位[目标].法术状态.护盾 and self.参战单位[目标].法术状态.护盾.护盾值 and not 系数.忽视护盾 then
                      if 伤害<self.参战单位[目标].法术状态.护盾.护盾值 then
                          self.参战单位[目标].法术状态.护盾.护盾值 = self.参战单位[目标].法术状态.护盾.护盾值 - 伤害
                          self.战斗流程[流程].挨打方[挨打].护盾值=伤害
                          伤害=0
                      else
                          self.战斗流程[流程].挨打方[挨打].护盾值 = self.参战单位[目标].法术状态.护盾.护盾值
                          伤害=伤害 - self.参战单位[目标].法术状态.护盾.护盾值
                          self:取消状态("护盾",目标)
                          self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"护盾")
                      end
                  end
                   ----------------神佑
                  if (self:取是否神佑(编号,目标,伤害) and not 保护 and not 系数.不可神佑) or 系数.直接神佑 then
                      类型=2
                      伤害= self.参战单位[目标].最大气血
                      self.参战单位[目标].神佑效果=true
                  else

                      if self.参战单位[目标].凝光炼彩 and 取随机数()<=25 then
                          self:添加状态("护盾",目标,目标,math.floor(伤害*0.5))
                          self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"护盾",目标)
                          self.参战单位[目标].法术状态.护盾.回合=3
                      end


                      if self.参战单位[编号].奇经八脉.致命 then
                          self.参战单位[目标].奇经八脉致命 = 编号
                      end

                      if self.参战单位[目标].奇经八脉.磐石 then
                          if not self.参战单位[目标].奇经八脉磐石 then
                             self.参战单位[目标].奇经八脉磐石 = 0
                          end
                          self.参战单位[目标].奇经八脉磐石 = self.参战单位[目标].奇经八脉磐石 + 1
                      end
                      if self.参战单位[目标].奇经八脉.苏醒 then
                          if self.参战单位[目标].奇经八脉苏醒==nil then
                              self.参战单位[目标].奇经八脉苏醒 = 0
                          end
                          self.参战单位[目标].奇经八脉苏醒 = self.参战单位[目标].奇经八脉苏醒 + 伤害
                      end

                      if self.参战单位[编号].奇经八脉.狂宴 and self.参战单位[目标].法术状态.尸腐毒 then
                          self:增加愤怒(编号,2)
                      end
                      if self.参战单位[目标].奇经八脉.烈焰 and 伤害>=self.参战单位[目标].最大气血*0.2 then
                            self.参战单位[目标].奇经八脉烈焰 = 4
                      end
                              ----------------------------------------解除催眠符
                      if self.参战单位[目标].法术状态.催眠符 then
                          local 攻击 = self.参战单位[目标].法术状态.催眠符.编号
                          if not self.参战单位[攻击].奇经八脉.黄粱 or (self.参战单位[攻击].奇经八脉.黄粱 and 伤害>=30) then
                              self:取消状态("催眠符",目标)
                              self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"催眠符")
                          end
                      end
                      if self.参战单位[目标].奇经八脉.飞龙  and 伤害>= self.参战单位[目标].最大气血*0.2 and 取随机数()<=75 then
                            self:添加状态("神龙摆尾",目标,目标,self.参战单位[目标].等级)
                            self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"神龙摆尾",目标)

                      end
                      if self.参战单位[目标].奇经八脉.蚀天 and 伤害>=self.参战单位[目标].最大气血*0.2 and not self.参战单位[目标].奇经八脉蚀天 then
                            self.参战单位[目标].奇经八脉蚀天 = 4
                         end
                      if self.参战单位[目标].奇经八脉.轻霜 and 取随机数()<=30 and not self.参战单位[编号].法术状态.暗器毒 and self.参战单位[编号].队伍~= self.参战单位[目标].队伍 then
                          local 加成 =  玩家数据[self.参战单位[目标].玩家id].经脉:取师门技能等级("毒经") * 3
                          self:添加状态("暗器毒",编号,目标,加成)
                          self.参战单位[编号].法术状态.暗器毒.回合 = 5
                          self:处理流程状态(self.战斗流程[流程],"暗器毒",编号)
                      end

                  end

              end
          end
          return {伤害=math.floor(伤害),类型=类型}

end




function 战斗处理类:取循环中处理(编号,目标) ---物伤法伤固伤都有
          local 添加流程={}
          if self.参战单位[目标].法术状态.波澜不惊 and self:取目标状态(目标,目标,2) then
              if self.参战单位[目标].法术状态.波澜不惊.等级<4 then
                  self.参战单位[目标].法术状态.波澜不惊.等级 =self.参战单位[目标].法术状态.波澜不惊.等级 +1
                  local 基础 = DeepCopy(self.计算属性)
                  基础.初始伤害 = self.参战单位[目标].等级*2+(self.参战单位[编号].法伤*0.1)
                  local 结果=self:取基础治疗计算(编号,目标,"波澜不惊",self.参战单位[编号].等级,基础)
                  self:增加气血(目标,结果.气血)
                  table.insert(添加流程,{伤害=结果.气血,类型=2,挨打方=目标})

              end
          end
          if self.参战单位[目标].法术状态.同舟共济 and self.参战单位[目标].法术状态.同舟共济.等级<6 and self:取目标状态(目标,目标,2) and self.伤害输出>0 then
              self.参战单位[目标].法术状态.同舟共济.等级 =self.参战单位[目标].法术状态.同舟共济.等级 +1
              local 流程 = {挨打方=self.参战单位[目标].法术状态.同舟共济.编号,类型=1,伤害=self.伤害输出}
              流程.死亡=self:减少气血(self.参战单位[目标].法术状态.同舟共济.编号,self.伤害输出,编号,"同舟共济")
              table.insert(添加流程,流程)
          end
          return 添加流程
end






function 战斗处理类:取是否神佑(编号,目标,伤害)
          if 伤害<self.参战单位[目标].气血 then
                return false
          elseif self.参战单位[编号].气血 <=0 then
                return false
          elseif self.参战单位[目标].鬼魂 then
                return false
          elseif self.参战单位[编号].法术状态.灵断 then
                 return false
          elseif self.参战单位[目标].神佑 and self.参战单位[目标].神佑>=取随机数() then
                return true
          end
          return false
end

function 战斗处理类:取抗物特性(队伍)
        local 最高 = 0
        for k,v in pairs(self.参战单位) do
              if v.队伍 == 队伍 and v.抗物特性 and 最高<v.抗物特性 then
                  最高 = v.抗物特性
              end
        end
        return 最高*6*0.01
end


function 战斗处理类:取抗法特性(队伍)
        local 最高 = 0
        for k,v in pairs(self.参战单位) do
              if v.队伍 == 队伍 and v.抗法特性 and 最高<v.抗法特性 then
                  最高 = v.抗法特性
              end
        end
        return 最高*6*0.01
end
