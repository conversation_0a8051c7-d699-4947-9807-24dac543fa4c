
local 摆摊假人类 = class()
local 五行_ = {"金","木","水","火","土"}
local 书铁范围 = {"枪矛","斧钺","剑","双短剑","飘带","爪刺","扇","魔棒","锤","鞭","环圈","刀","法杖","弓弩","宝珠","巨剑","伞","灯笼","头盔","发钗","项链","女衣","男衣","腰带","鞋子"}
local 图策范围={"项圈","护腕","铠甲"}
function 摆摊假人类:初始化()
            self.阵法名称={
                [3]="普通",
                [4]="风扬阵",
                [5]="虎翼阵",
                [6]="天覆阵",
                [7]="云垂阵",
                [8]="鸟翔阵",
                [9]="地载阵",
                [10]="龙飞阵",
                [11]="蛇蟠阵",
                [12]="鹰啸阵",
                [13]="雷绝阵",
              }
            self.一级家具={"黑麻垂曼帘","桦木圆桌","桦木立柜","草编地毯","漆花竹凳","榛木床","印花屏风",
                               "文竹","君子兰","蝴蝶兰","水仙","仙人掌","银烛台","玉瓷画瓶","踏春图","漆花地板"}
            self.二级家具={"白鹤展翅帘","蓝绸绣花帘","红木八仙桌","嵌玉虎纹桌","双鱼吉庆柜","彩绘立柜","兽皮地毯",
                              "麻布地毯","桦木靠背椅","月牙凳","八卦镇邪榻","神仙帐","狮子图屏风","花鸟图屏风","天山云雪",
                              "龟鹤延年灯","长信宫灯","雕花马桶","彩绘花瓶","夕阳山水图","猛虎坐山图","桦木地板"}
            self.额外符石={一级未激活符石=1,二级未激活符石=1,三级未激活符石=1,符石卷轴=1}
            self.新三符石={太极符石=1,阴仪符石=1,阳仪符石=1,太阴符石=1,少阴符石=1,少阳符石=1,太阳符石=1}
            self.资质范围={"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}
            self.属性范围={"体质","魔力","力量","耐力","敏捷"}

          self.开关 = false
          self.假人单位 = {}
          local 总共数据 = 0
          if 摆摊假人 then
             for i,v in ipairs(摆摊假人) do
                  if v.地图 and v.x and v.y and v.摊位名称 then
                        if not self.假人单位[v.地图] then
                            self.假人单位[v.地图]={}
                        end
                        local 假人信息 =  假人玩家类:生成假人数据(v.地图,v.x,v.y,v.离线摆摊)
                        local 编号 = #self.假人单位[v.地图] + 1
                        假人信息.假人编号 = 编号
                        假人信息.摊位名称 = v.摊位名称
                        self.假人单位[v.地图][编号] = 假人信息
                        总共数据 = 总共数据 + 1
                  end
             end
          end
          __S服务:输出("一共添加了[" .. 总共数据 .. "]个摆摊假人")

end




function 摆摊假人类:功能开关(open)
          self.开关 = open
end

function 摆摊假人类:加载摆摊()

         for k,v in pairs(self.假人单位) do

                for i,n in ipairs(v) do
                    n.摆摊数据 = {}
                    n.摆摊数据.道具 = {}
                    if 摆摊假人[n.假人编号].道具 and 摆摊假人[n.假人编号].道具[1] then
                        for j,z in ipairs(摆摊假人[n.假人编号].道具) do
                            if type(z)=="table" and z.价格 then
                                local 临时道具  =self:自定义给予道具(z.名称,z.数量,z.参数)
                                if 临时道具 then
                                    local 编号 = #n.摆摊数据.道具 + 1
                                    n.摆摊数据.道具[编号] = 临时道具
                                    n.摆摊数据.道具[编号].价格 = z.价格
                                    n.摆摊数据.道具[编号].道具编号 = 编号
                                    n.摆摊数据.道具[编号].背包编号 = 编号
                                end
                            end
                        end
                    else
                        self:加载随机道具(n.摊位名称, n.摆摊数据.道具)
                    end
                    n.摆摊数据.宝宝 = {}
                    if 摆摊假人[n.假人编号].召唤兽 and 摆摊假人[n.假人编号].召唤兽[1] then
                        for k,z in ipairs(摆摊假人[n.假人编号].召唤兽) do
                            if z.模型 and z.类型 and z.价格 then
                                local 临时宝宝 = self:加载召唤兽(z)
                                临时宝宝.价格 = z.价格
                                table.insert(n.摆摊数据.宝宝,临时宝宝)
                            end
                        end
                    else
                        self:加载随机宝宝(n.摊位名称,n.摆摊数据.宝宝)
                    end

                    n.摆摊数据.打造 = {打造技巧={},裁缝技巧={},炼金术={},淬灵之术={}}
                    n.打造熟练度={打造技巧=0,裁缝技巧=0,炼金术=0,淬灵之术=0}
                    self:加载打造(n.摊位名称,n.摆摊数据.打造,n.打造熟练度,摆摊假人[n.假人编号].打造)

                end

         end

end




local 合成棋={
            长安 = {可叠加=false,总类=11,xy={
                                            [1]={y=7,x=312},[2]={y=11,x=495},[3]={y=273,x=12},
                                            [4]={y=167,x=362},[5]={y=273,x=12},[6]={y=240,x=277},
                                            [7]={y=105,x=192},[8]={y=68,x=413},[9]={y=124,x=465},
                                            [10]={y=19,x=379},[11]={y=85,x=290},[12]={y=181,x=481},
                                            [13]={y=276,x=528},[14]={y=81,x=145},[15]={y=48,x=106}
                                            },
                    地图=1001,子类=20,y=7,x=312,分类=2,
                    总次数=200,次数=200,名称="红色合成旗"
                    },
            建邺 = {可叠加=false,总类=11,xy={
                                            [1]={y=18,x=122},[2]={y=17,x=234},[3]={y=113,x=265},
                                            [4]={y=120,x=175},[5]={y=79,x=103},[6]={y=134,x=17},
                                            [7]={y=42,x=65},[8]={y=69,x=179}
                                            },
                    地图=1501,子类=20,y=7,x=212,分类=2,
                    总次数=200,次数=200,名称="白色合成旗"
                    },
            长寿 = {可叠加=false,总类=11,xy={
                                            [1]={y=17,x=102},[2]={y=46,x=30},[3]={y=70,x=132},
                                            [4]={y=92,x=14},[5]={y=175,x=24},[6]={y=156,x=135},
                                            [7]={y=200,x=135}
                                             },
                    地图=1070,子类=20,y=7,x=312,分类=2,
                    总次数=200,次数=200,名称="黄色合成旗"
                    },
            傲来国 = {可叠加=false,总类=11,xy={
                                             [1]={y=11,x=14},[2]={y=12,x=206},[3]={y=98,x=78},
                                             [4]={y=135,x=150},[5]={y=58,x=118}
                                             },
                      地图=1092,子类=20,y=7,x=312,分类=2,
                      总次数=200,次数=200,名称="绿色合成旗"
                     },
            朱紫国 = {可叠加=false,总类=11,xy={
                                              [1]={y=24,x=141},[2]={y=24,x=17},[3]={y=109,x=12},
                                              [4]={y=61,x=91},[5]={y=91,x=181},[6]={y=107,x=148},
                                              [7]={y=25,x=85}
                                              },
                      地图=1208,子类=20,y=7,x=312,分类=2,
                      总次数=200,次数=200,名称="蓝色合成旗"
                     },
            宝象国 = {可叠加=false,总类=11,xy={
                                               [1]={y=50,x=108},[2]={y=104,x=141},[3]={y=111,x=11},
                                               [4]={y=52,x=11},[5]={y=9,x=120}
                                               },
                      地图=1226,子类=20,y=7,x=312,分类=2,
                      总次数=200,次数=200,名称="红色合成旗"
                     },
            西梁女国 = {可叠加=false,总类=11,xy={
                                                [1]={y=109,x=149},[2]={y=111,x=37},[3]={y=86,x=115},
                                                [4]={y=23,x=136},[5]={y=32,x=81},[6]={y=26,x=27},
                                                [7]={y=75,x=24},[9]={y=55,x=67}
                                                },
                        地图=1040,子类=20,y=7,x=312,分类=2,
                        总次数=200,次数=200,名称="绿色合成旗"
                       }
}

function 摆摊假人类:加载随机道具(名称,道具)
            local 个数 = 取随机数(0,30)
            if 个数>0 then
                for i=1,个数 do
                    local 物品 = nil
                    local 价格 = 0
                    if string.find(名称, "挂机") then
                        local 临时 = {"摄妖香","飞行符"}
                        local 给予 = 临时[取随机数(1,2)]
                        物品=self:给予道具(给予,取随机数(1,99))
                        if 给予=="摄妖香" then
                            价格 = 取随机数(1100,1500)
                        else
                            价格 = 取随机数(400,800)
                        end
                    elseif string.find(名称, "77")  or string.find(名称, "合成")
                        or string.find(名称, "飞行") or string.find(名称, "旗子") then
                            local 临时 = {"长安","建邺","长寿","傲来国","朱紫国","宝象国","西梁女国"}
                            local 给予 = 临时[取随机数(1,7)]
                            物品=DeepCopy(合成棋[给予])
                            价格 = 取随机数(80000,150000)
                    elseif string.find(名称, "环装") or string.find(名称, "军火") or string.find(名称, "无级")
                        or string.find(名称, "装备")  or string.find(名称, "套子") then
                            local 序列 = 取随机数(1,23)
                            local 等级 = 取随机数(6,15)
                            if string.find(名称, "环装") then
                                等级 = 取随机数(6,8)
                            end
                            local 装备 = 假人玩家类.装备物品[序列][等级]
                            local 装备名称
                            if type(装备) == "table" then
                                装备名称 = 装备[取随机数(1,#装备)]
                            else
                                装备名称 = 装备
                            end
                            if 装备名称 then
                                物品 = 假人玩家类:生成指定装备(装备名称,序列)
                                if string.find(名称, "无级") and 取随机数()<=30 then
                                    物品.第二特效 = "无级别限制"
                                end
                                local 临时 = 100
                                if 等级 ==15 then
                                    临时 = 22000
                                elseif 等级 ==14 then
                                        临时 = 17000
                                elseif 等级 ==13 then
                                        临时 = 15000
                                elseif 等级 ==12 then
                                        临时 = 13000
                                elseif 等级 ==11 then
                                        临时 = 11000
                                elseif 等级 ==10 then
                                        临时 = 9000
                                elseif 等级 ==9 then
                                        临时 = 5000
                                elseif 等级 ==8 then
                                        临时 = 500
                                elseif 等级 ==6 then
                                        临时 = 250
                                end
                                价格=math.floor(物品.级别限制 * 等级 * 临时) + 取随机数(10000,15000)
                            end
                    elseif string.find(名称, "宝图") or string.find(名称, "tt") or string.find(名称, "挖宝") then
                                if string.find(名称, "高级") then
                                    物品=self:给予道具("高级藏宝图")
                                    价格 = 取随机数(5000000,50000000)
                                else
                                    物品=self:给予道具("藏宝图")
                                    价格 = 取随机数(25000,100000)
                                end

                    elseif string.find(名称, "药") then
                             local 临时 = {}
                             local 品质 = 1
                             local 数量 = 1
                             if string.find(名称, "三") or string.find(名称, "3") then
                                 临时 = {"小还丹","金香玉","定神香","五龙丹","蛇蝎美人","佛光舍利子","千年保心丹",
                                          "风水混元丹","九转回魂丹","十香返生丸","金创药"
                                          }
                                 品质 = 取随机数(10,160)
                                 数量 = 取随机数(1,30)
                                 价格 = 品质 * 取随机数(500,1500)
                             else
                                临时 = {"孔雀红","鹿茸","仙狐涎","地狱灵芝","六道轮回","凤凰尾","火凤之睛",
                                          "龙之心屑","紫石英","白露为霜","天不老","血色茶花","熊胆","硫磺草",
                                          "丁香水","月星子","麝香","血珊瑚","餐风饮露","天龙水"
                                          }
                                数量 = 取随机数(1,99)
                                价格 = 取随机数(1000,5000)
                             end
                             物品=self:给予道具(临时[取随机数(1,#临时)],数量,品质)
                    elseif string.find(名称, "书") or string.find(名称, "铁") then
                            local 等级
                            local 参数
                            local 临时名称 = {"灵饰指南书","元灵晶石"}
                            if string.find(名称,"灵饰") or string.find(名称,"首饰")
                            or string.find(名称,"戒指") or string.find(名称,"手镯")
                            or string.find(名称,"佩饰") or string.find(名称,"耳饰") then
                                local 临时 = {6,8,10,12,14}
                                等级 = 临时[取随机数(1,#临时)]
                                临时名称 = {"灵饰指南书","元灵晶石"}
                                价格 = 取随机数((等级-5)*1000000,(等级-3)*1000000)
                            else
                                等级 = 取随机数(6,15)*10
                                临时名称 = {"制造指南书","百炼精铁"}
                                价格 = 取随机数((等级-50)*100000,(等级-30)*100000)
                                if 取随机数()<=50 then
                                   参数= 取随机数(1,18)
                                else
                                   参数= 取随机数(19,#书铁范围)
                                end
                            end
                            if 等级 then
                                物品=self:给予道具(临时名称[取随机数(1,#临时名称)],等级,参数)
                            end
                    elseif string.find(名称, "兽决") or string.find(名称, "要诀") then
                            local 临时名称 = "魔兽要诀"
                            价格 = 取随机数(800000,1000000)
                            if string.find(名称, "高级") then
                                临时名称 = "高级魔兽要诀"
                                价格 = 取随机数(8000000,50000000)
                            elseif string.find(名称, "特殊") then
                                临时名称 = "特殊魔兽要诀"
                                价格 = 取随机数(80000000,500000000)
                            end
                            物品=self:给予道具(临时名称)
                    elseif string.find(名称, "内丹") then
                            local 临时名称 = "召唤兽内丹"
                            价格 = 取随机数(200000,2000000)
                            if string.find(名称, "高级") then
                                临时名称 = "高级召唤兽内丹"
                                价格 = 取随机数(2000000,20000000)
                            end
                            物品=self:给予道具(临时名称)
                    elseif string.find(名称, "卡") or string.find(名称, "变身") then
                            local 等级 = 取随机数(1,7)
                            if string.find(名称, "级") then
                                local 取出=分割文本(名称,"级")
                                等级 = tonumber(取出[1]) or 取随机数(1,7)
                            end
                            价格 = 取随机数(等级*100000,等级*1000000)
                            if 取随机数()<=5 then
                                等级 = 取随机数(8,9)
                                价格 = 取随机数(等级*1000000,等级*500000)
                            end
                            物品=self:给予道具("怪物卡片",等级)
                    elseif string.find(名称, "烹饪") or string.find(名称, "酒") or string.find(名称, "肉") then
                             local 临时 = {"烤肉","烤鸭","珍露酒","虎骨酒","佛跳墙","豆斋果","长寿面",
                                          "臭豆腐","桂花丸","梅花酒","长寿面","百味酒","醉生梦死",
                                          "蛇胆酒","翡翠豆腐"
                                          }
                             local 品质 = 取随机数(10,160)
                             local 数量 = 取随机数(1,30)
                             物品=self:给予道具(临时[取随机数(1,#临时)],数量,品质)
                             价格 = 品质 * 取随机数(500,2500)
                    elseif string.find(名称, "石") or string.find(名称, "玛瑙") or string.find(名称, "舍利") then
                          local 宝石名称 = {"红玛瑙","太阳石","舍利子","黑宝石","月亮石","光芒石","星辉石"}
                          local 给予名称 = 宝石名称[取随机数(1,#宝石名称)]
                          local 等级 = 取随机数(1,10)
                          物品=self:给予道具(给予名称,等级)
                          价格 = 取随机数(50000,100000)*等级^2

                    elseif not string.find(名称, "宝宝") and not string.find(名称, "召唤兽")
                        and not string.find(名称, "炼妖") and not string.find(名称, "bb")
                        and not string.find(名称, "技能") and not string.find(名称, "神兽")
                        and not string.find(名称, "打造") and not string.find(名称, "炼金")
                        and not string.find(名称, "裁缝") and not string.find(名称, "锻造")
                        and not string.find(名称, "BB") and not string.find(名称, "淬灵")
                        then
                        local 临时 = 取随机数(1,#仙玉商城.银子商城.杂货商品)
                        local 给予 = 仙玉商城.银子商城.杂货商品[临时][1]
                        if 给予=="长安" or 给予=="建邺" or 给予=="长寿" or 给予=="傲来国"or 给予=="朱紫国" or 给予=="宝象国" or 给予=="西梁女国" then
                            local 临时地点 = {"长安","建邺","长寿","傲来国","朱紫国","宝象国","西梁女国"}
                            local 给与名称 = 临时地点[取随机数(1,7)]
                            物品=DeepCopy(合成棋[给与名称])
                        else
                             local 临时名称,数量,参数 = self:随机商品处理(给予)
                             物品=self:给予道具(临时名称,数量,参数)
                             if 物品.可叠加 then
                                物品.数量 = 取随机数(1,99)
                             end
                        end
                        local 临时价格 = 仙玉商城.银子商城.杂货商品[临时][4]
                        价格 = 取随机数(临时价格,math.floor(临时价格*1.5))
                    end
                    if 物品 and 价格>0 then
                        local 编号 = #道具 + 1
                        道具[编号] = 物品
                        道具[编号].价格 = 价格
                        道具[编号].道具编号 = 编号
                        道具[编号].背包编号 = 编号
                    end
                end
            end
end


function 摆摊假人类:加载随机宝宝(名称,宝宝)

            if string.find(名称, "宝宝") or  string.find(名称, "召唤兽")
            or string.find(名称, "炼妖") or string.find(名称, "bb")
            or string.find(名称, "技能") or string.find(名称, "神兽")
            or string.find(名称, "BB") then
                for i=1,取随机数(1,7) do
                    local 临时宝宝 = self:加载召唤兽()
                    临时宝宝.价格 = 取随机数(5000000,100000000)
                    table.insert(宝宝,临时宝宝)
                end

           end
end


function 摆摊假人类:加载召唤兽(数据)
            if not 数据 then 数据 = {} end
            local 物法 = 数据.物法
            local 临时 = {"宝宝","野怪","变异"}
            local 临时模型 = 取随机怪(1,155)
            local 模型 = 数据.模型 or 临时模型[2]
            local 名称 = 数据.名称 or 数据.模型 or nil
            local 类型 = 数据.类型 or 临时[取随机数(1,3)]
            local 等级 = 数据.等级 or 取随机数(1,155)
            local 技能组 = 数据.技能组 or 取随机兽决法术(取随机数(1,12))
            return self:添加召唤兽(模型,名称,类型,物法,等级,技能组)
end



function 摆摊假人类:加载打造(名称,打造,熟练度,数据)
            local 临时技能 = ""
            if 数据 then
                if 数据.打造技巧 then
                    if 数据.打造技巧.熟练度 then
                        熟练度.打造技巧 = 数据.打造技巧.熟练度
                    else
                        熟练度.打造技巧 = 取随机数(1000,50000)
                    end
                    for i=1,16 do
                        if 数据.打造技巧[i] then
                            打造.打造技巧[i] = 数据.打造技巧[i]
                            临时技能 = "打造技巧"
                        end
                    end

                end
                if 数据.裁缝技巧 then
                    if 数据.裁缝技巧.熟练度 then
                        熟练度.裁缝技巧 = 数据.裁缝技巧.熟练度
                    else
                        熟练度.裁缝技巧 = 取随机数(1000,50000)
                    end
                    for i=1,16 do
                        if 数据.裁缝技巧[i] then
                            打造.裁缝技巧[i] = 数据.裁缝技巧[i]
                            临时技能 = "裁缝技巧"
                        end
                    end

                end
                if 数据.淬灵之术 then
                    if 数据.淬灵之术.熟练度 then
                        熟练度.淬灵之术 = 数据.淬灵之术.熟练度
                    else
                        熟练度.淬灵之术 = 取随机数(1000,50000)
                    end
                    for i=1,16 do
                        if 数据.淬灵之术[i] then
                            打造.淬灵之术[i] = 数据.淬灵之术[i]
                            临时技能 = "淬灵之术"
                        end
                    end

                end
                 if 数据.炼金术 then
                    if 数据.炼金术.熟练度 then
                        熟练度.炼金术 = 数据.炼金术.熟练度
                    else
                        熟练度.炼金术 = 取随机数(1000,50000)
                    end
                    for i=1,16 do
                        if 数据.炼金术[i] then
                            打造.炼金术[i] = 数据.炼金术[i]
                            临时技能 = "炼金术"
                        end
                    end
                end
            end

            if 临时技能=="" and (string.find(名称, "打造") or string.find(名称, "炼金")
                   or string.find(名称, "裁缝") or string.find(名称, "锻造")
                   or string.find(名称, "淬灵")) then
                    local 临时 = {"打造技巧","裁缝技巧","淬灵之术","炼金术"}
                    if string.find(名称, "打造") then
                        临时技能 = "打造技巧"
                    elseif string.find(名称, "炼金") then
                        临时技能 = "炼金术"
                    elseif string.find(名称, "裁缝") then
                        临时技能 = "裁缝技巧"
                    elseif string.find(名称, "淬灵") then
                        临时技能 = "淬灵之术"
                    end
                    if 临时技能~="" then
                        for i=4,1,-1 do
                            if 临时[i]==临时技能 then
                                table.remove(临时,i)
                                break
                            end
                        end
                        local 临时等级 ={}
                        for i=1,16 do
                            if 取随机数()<=30 then
                                table.insert(临时等级,i)
                            end
                        end
                        熟练度[临时技能] = 取随机数(1000,50000)
                        for i,v in ipairs(临时等级) do
                            打造[临时技能][v] = v*200000 + 熟练度[临时技能]*取随机数(100,1000)
                        end
                    end
                    for i,v in ipairs(临时) do
                        if 取随机数()<=50 then
                            熟练度[v] = 取随机数(1000,50000)
                            local 临时等级 ={}
                            for n=1,16 do
                                if 取随机数()<=20 then
                                    table.insert(临时等级,n)
                                end
                            end
                            for k,n in ipairs(临时等级) do
                                打造[v][n] = v*200000 + 熟练度[v]*取随机数(100,1000)
                            end
                        end
                    end
            end

end


function 摆摊假人类:摊位列表(连接id, 地图)
            if not self.假人单位[地图] or not self.开关 then return end
            for k,v in pairs(self.假人单位[地图]) do
                发送数据(连接id,1006,v)
            end
end



function 摆摊假人类:索要摊位(id,假人id,序号)
         if not self.开关 then  常规提示(id,"#Y/这个摊位并不存在") return end
         local 地图 = 0
         local 编号 = 0
         for k,v in pairs(self.假人单位) do
              if 编号 == 0 and 地图 == 0 then
                  for i,n in ipairs(v) do
                        if n.id == 假人id then
                           地图 = n.地图
                           编号 = n.假人编号
                           break
                        end
                  end
              else
                    break
              end

         end
         if 地图~=0 and 编号~=0 then
            local 返回信息={}
            返回信息.id = self.假人单位[地图][编号].id
            返回信息.名称 = self.假人单位[地图][编号].摊位名称
            返回信息.摊主名称 = self.假人单位[地图][编号].名称
            返回信息.物品 = self.假人单位[地图][编号].摆摊数据.道具
            返回信息.bb = self.假人单位[地图][编号].摆摊数据.宝宝
            返回信息.打造 = self.假人单位[地图][编号].摆摊数据.打造
            返回信息.熟练度 = self.假人单位[地图][编号].打造熟练度
            self.假人单位[地图][编号].更新 = os.time()
            玩家数据[id].摊位查看=os.time()
            玩家数据[id].摊位id = 假人id
            发送数据(玩家数据[id].连接id,3520,玩家数据[id].角色.数据.银子)
            发送数据(玩家数据[id].连接id,序号,返回信息)
            return true
         end
  end



function 摆摊假人类:购买摊位商品(id,假人id,数据)
          if not self.开关 or not 假人id then  常规提示(id,"#Y/这个摊位并不存在") return end
          local 地图 = 0
          local 编号 = 0
          for k,v in pairs(self.假人单位) do
              if 编号 == 0 and 地图 == 0 then
                  for i,n in ipairs(v) do
                        if n.id == 假人id then
                           地图 = n.地图
                           编号 = n.假人编号
                           break
                        end
                  end
              else
                    break
              end
          end
          if 地图~=0 and 编号~=0 then
              if 玩家数据[id].摊位查看<self.假人单位[地图][编号].更新 then
                常规提示(id,"#Y/这个摊位的数据已经发生了变化，请重新打开该摊位")
                return
              end
              local 名称1=玩家数据[id].角色.数据.名称
              local 账号1=玩家数据[id].账号
              if 数据.道具 then --购买道具
                    if not self.假人单位[地图][编号].摆摊数据.道具
                    or not self.假人单位[地图][编号].摆摊数据.道具[数据.道具]
                    or self.假人单位[地图][编号].摆摊数据.道具[数据.道具].背包编号~=数据.道具 then
                      常规提示(id,"#Y/这个商品并不存在")
                      return
                    end
                    local 购买数量=1
                    if not 数据.数量 or not tonumber(数据.数量) or tonumber(数据.数量)<1 or math.floor(数据.数量)~=tonumber(数据.数量) then
                        购买数量 = 1
                    else
                         购买数量 = math.floor(数据.数量+0)
                    end
                    if 购买数量>999 then
                        共享货币[玩家数据[id].账号]:充值记录("刷物品被进封")
                       封禁账号(id,"1")
                       print("#Y玩家id"..id.."刷物品被封禁")
                       return
                    end
                    if 购买数量<=1 then
                       购买数量 = 1
                    end
                    if not self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量 then
                        购买数量=1
                    else
                        if 购买数量>self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量 then
                            购买数量 = math.floor(self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量)
                        end
                    end
                    local 购买价格=self.假人单位[地图][编号].摆摊数据.道具[数据.道具].价格 * 购买数量
                    if 玩家数据[id].角色.数据.银子 < 购买价格 or math.floor(购买价格)~=购买价格 or 购买价格<1 then
                      常规提示(id,"#Y/你没有那么多的银子")
                      return
                    end
                    local 临时格子=玩家数据[id].角色:取道具格子()
                    if 临时格子==0 then
                      常规提示(id,"#Y/请先整理下包裹吧！")
                      return
                    end
                    玩家数据[id].角色:扣除银子(购买价格,"摊位购买",1)
                    local 道具名称=self.假人单位[地图][编号].摆摊数据.道具[数据.道具].名称
                    local 新识别码= 取唯一识别码(id)
                    local 道具id = 玩家数据[id].道具:取新编号()
                    玩家数据[id].道具.数据[道具id]=DeepCopy(self.假人单位[地图][编号].摆摊数据.道具[数据.道具])
                    玩家数据[id].道具.数据[道具id].识别码 = 新识别码
                    玩家数据[id].角色.数据.道具[临时格子] = 道具id
                    if self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量 then
                        玩家数据[id].道具.数据[道具id].数量 = 购买数量
                        self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量 = self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量 - 购买数量
                        if self.假人单位[地图][编号].摆摊数据.道具[数据.道具].数量<=0 then
                            self.假人单位[地图][编号].摆摊数据.道具[数据.道具] = nil
                        end
                    else
                        玩家数据[id].道具.数据[道具id].数量 = nil
                        玩家数据[id].道具.数据[道具id].可叠加 = false
                        self.假人单位[地图][编号].摆摊数据.道具[数据.道具] = nil
                    end
                    玩家数据[id].角色:日志记录(format("[摊位系统-购买]购买道具[%s][%s]，花费%s两银子,出售者信息：[%s]",道具名称,新识别码,购买价格,"假人摊位"))
                    常规提示(id,"#W/购买#R/"..道具名称.."#W/成功！")
                    道具刷新(id)
              end
              if 数据.bb then--宠物
                    if not self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb] then
                      常规提示(id,"#Y/这只召唤兽不存在")
                      return
                    elseif 玩家数据[id].召唤兽:是否携带上限() then
                      常规提示(id,"#Y/你当前可携带的召唤兽数量已达上限！")
                      return
                    end
                    local 出售价格=self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb].价格
                    if 玩家数据[id].角色.数据.银子<出售价格 or math.floor(出售价格)~=出售价格 or 出售价格<1  then
                      常规提示(id,"#Y/你没有那么多的银子")
                      return
                    end
                    local 道具名称=self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb].名称
                    local 道具等级=self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb].等级
                    local 道具模型=self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb].模型
                    local 道具技能=#self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb].技能
                    玩家数据[id].角色:扣除银子(出售价格,"摊位购买",1)
                    常规提示(id,"#W/购买#R/"..道具名称.."#W/成功！")
                    玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据+1]=DeepCopy(self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb])
                    local 新认证码 = 取唯一识别码(id)
                    玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].认证码 =新认证码
                    玩家数据[id].角色:日志记录(format("[摊位系统-购买]购买召唤兽[%s][%s][%s][%s][%s]，花费%s两银子,出售者信息：[%s]",道具名称,道具模型,道具等级,道具技能,新识别码,出售价格,"假人摊位"))
                    self.假人单位[地图][编号].摆摊数据.宝宝[数据.bb]=nil
                    if 玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].参战信息~=nil then
                            玩家数据[id].召唤兽.数据[#玩家数据[id].召唤兽.数据].参战信息=nil

                    end
                    发送数据(玩家数据[id].连接id,3512,玩家数据[id].召唤兽.数据)
              end
              if 数据.打造~=nil then
                    local 打造编号 = 数据.数量 + 0

                    if not self.假人单位[地图][编号].摆摊数据.打造[数据.打造] or not  self.假人单位[地图][编号].摆摊数据.打造[数据.打造][打造编号] then
                      常规提示(id,"#Y/你购买的打造不存在")
                      return
                    end
                    local 费用 = self.假人单位[地图][编号].摆摊数据.打造[数据.打造][打造编号] + 0
                    if 玩家数据[id].角色.数据.银子 < 费用 or math.floor(费用)~=费用 or 费用<1  then
                        常规提示(id,"#Y/你没有那么多的银子")
                        return
                    end
                    local 临时格子=玩家数据[id].角色:取道具格子()
                    if 临时格子==0 then
                        常规提示(id,"#Y/请先整理下包裹吧！")
                        return
                    end
                    if 玩家数据[id].角色:取任务(5)~=0 then
                        常规提示(id,"#Y/你已经领取了一个打造任务，赶快去完成吧")
                        return
                    end
                    玩家数据[id].给予数据={类型=1,id=0,事件="购买打造处理"}
                    发送数据(玩家数据[id].连接id,3507,{道具=玩家数据[id].道具:索要道具1(id),名称= self.假人单位[地图][编号].名称,类型="玩家",等级=150})
                    玩家数据[id].摊位购买打造 = {对方id =  self.假人单位[地图][编号].id,对方名称 = self.假人单位[地图][编号].名称, 费用 = 费用,打造技能= 数据.打造,熟练度 = self.假人单位[地图][编号].打造熟练度[数据.打造],等级 = 打造编号*10 }
              end
              self:索要摊位(id,self.假人单位[地图][编号].id,3522)
              return true
         end
end




function 摆摊假人类:自定义给予道具(名称,数量,参数)

  if 名称=="钨金" or 名称=="附魔宝珠" or 名称=="超级附魔宝珠" or 名称=="炼妖石" or 名称=="天眼珠" or 名称=="上古锻造图策" then
        return self:给予道具(名称,数量,参数)
   elseif 名称=="珍珠" or 名称=="战魂" then
            local 给予等级 = 数量[取随机数(1,#数量)]
            return self:给予道具(名称,给予等级,参数)
   elseif 名称=="制造指南书" or 名称=="百炼精铁" then
              local 给予等级 = 取随机数(数量[1],数量[#数量])*10
              if 参数==nil then
                if 取随机数()<=50 then
                   参数= 取随机数(1,18)
                else
                   参数= 取随机数(19,#书铁范围)
                end
              end
              return self:给予道具(名称,给予等级,参数)

   elseif  名称=="灵饰指南书" or 名称=="元灵晶石" then
              if 参数==nil then
                 local 临时范围 = {"手镯","佩饰","戒指","耳饰"}
                 参数= 临时范围[取随机数(1,4)]
              end
              return self:给予道具(名称,数量,参数)
   elseif 名称=="魔兽要诀" or 名称=="高级魔兽要诀"or 名称=="超级魔兽要诀" or 名称=="特殊魔兽要诀" or 名称=="召唤兽内丹" or 名称=="高级召唤兽内丹" or 名称=="点化石" then
             if 参数 ==nil then
               if 名称=="召唤兽内丹" then
                  参数=取内丹()
               elseif 名称=="高级召唤兽内丹" then
                      参数=取内丹("高级")
               elseif 名称=="魔兽要诀" then
                      参数=取低级要诀()
               elseif 名称=="高级魔兽要诀" then
                      参数=取高级要诀()
               elseif 名称=="特殊魔兽要诀" then
                      参数=取特殊要诀()
               elseif 名称=="超级魔兽要诀" then
                      参数=取超级要诀()
               elseif 名称=="点化石" then
                      local 生成几率 = 取随机数()
                      if 生成几率<= 5 then
                         参数=取特殊要诀()
                      elseif 生成几率<= 35 and 生成几率>=6 then
                             参数=取高级要诀()
                      else
                           参数=取低级要诀()
                      end
               end
            end
            return self:给予道具(名称,1,参数)
    elseif 名称=="鬼谷子"  then
           return self:给予道具(名称,1,参数)
    elseif 名称=="初级清灵仙露"  or 名称=="中级清灵仙露"  or 名称=="高级清灵仙露"  or 名称=="超级清灵仙露"  or 名称=="终级清灵仙露" then
           return self:给予道具(名称,1,参数)
    elseif 名称=="宝石" then
          local 宝石名称 = {"红玛瑙","太阳石","舍利子","黑宝石","月亮石","光芒石","星辉石"}
          local 给予名称 = 宝石名称[取随机数(1,#宝石名称)]
          local 给予数量 = 取随机数(数量[1],数量[#数量])
          return self:给予道具(给予名称,给予数量,参数)
    elseif 名称=="五宝" then
          local 给予名称 = self:取五宝()
          local 给予数量 = 取随机数(数量[1],数量[#数量])
          return  self:给予道具(给予名称,给予数量,参数)
    elseif 名称=="强化石" then
          local 给予名称 = 取强化石()
          local 给予数量 = 取随机数(数量[1],数量[#数量])
          return  self:给予道具(给予名称,给予数量,参数)
    else
        local 给予数量 = 取随机数(数量[1],数量[#数量])
        return self:给予道具(名称,给予数量,参数)
    end
end



function 摆摊假人类:给予道具(名称,数量,参数)
            local 道具=物品类()
            道具:置对象(名称)
            道具.识别码=取唯一识别码(999999)
            if 名称=="鬼谷子" then
                if  参数 == nil or 参数==""  then
                     道具.子类= self.阵法名称[取随机数(4,13)]
                else
                     道具.子类 = 参数
                end
                道具.可叠加 = false
            elseif 名称=="点化石" then
                  if 参数 == nil or 参数==""  then
                      local 生成几率 = 取随机数()
                      if  生成几率<= 5 then
                        道具.附带技能=取特殊要诀()
                      elseif  生成几率<= 35 and 生成几率>=6 then
                        道具.附带技能=取高级要诀()
                      else
                        道具.附带技能=取低级要诀()
                      end
                  else
                     道具.附带技能=参数
                  end
                  道具.可叠加 = false

            elseif 名称=="精魄灵石" then
                 道具.可叠加=false
                  if 数量==nil then
                     道具.级别限制=1
                  else
                     道具.级别限制=数量
                  end
                  if 参数==nil then
                    local 给与属性={"伤害","灵力","速度","气血","防御"}
                    道具.属性 =给与属性[取随机数(1,#给与属性)]
                  else
                    道具.属性 =参数
                  end
                  if 道具.属性 =="伤害" or 道具.属性 =="灵力" then
                     道具.子类 = 1
                  elseif 道具.属性 =="速度"  then
                       道具.子类 = 2
                  else
                       道具.子类 = 3
                  end

            elseif 名称=="钟灵石" then
                local 幻化类型 = {"心源","固若金汤","锐不可当","通真达灵","气血方刚","健步如飞","心无旁骛","回春之术","风雨不动","气壮山河"}
                if 参数 ~= nil then
                  道具.附加特性 = 参数
                else
                  道具.附加特性 = 幻化类型[取随机数(1,#幻化类型)]
                end
                if 数量 ~= nil then
                  道具.级别限制 = 数量
                else
                  道具.级别限制 = 1
                end

            elseif 名称=="特赦令牌" then
                   道具.可叠加=true

            elseif 名称=="天眼通符" then
                  道具.总类=4
                  道具.分类=2
                  道具.子类=0
                  道具.可叠加 = true
            elseif 名称=="钨金" or 名称=="附魔宝珠"  or 名称=="超级附魔宝珠"  then
                  if  数量==nil then
                     道具.级别限制=10
                  else
                     道具.级别限制=数量[取随机数(1,#数量)]
                  end
                  道具.可叠加 = false
            elseif 名称=="制造指南书" then
                  道具.子类=数量
                  道具.特效=参数
                  道具.可叠加 = false
            elseif 名称=="灵饰指南书" then
                  道具.子类=数量[取随机数(1,#数量)]*10
                  if 参数 == nil or 参数==""  then
                    道具.特效=随机灵饰[取随机数(1,#随机灵饰)]
                  else
                    道具.特效=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="元灵晶石" then
                  道具.子类=数量[取随机数(1,#数量)]*10
                  道具.可叠加 = false
            elseif 名称=="灵宝图鉴" or 名称=="神兵图鉴"  or 名称=="灵饰图鉴"  then
                  道具.可叠加=false
                  道具.子类=参数
            elseif 名称=="强化符" then
                  道具.等级=参数
                  道具.类型=附加
                  道具.可叠加 = false
            elseif 道具.总类==2000 then
                  道具.耐久=100
            elseif 名称=="百炼精铁" then
                  道具.子类=数量
                  道具.可叠加=false
            elseif 名称=="初级清灵仙露" then
                  道具.灵气=取随机数(1,4)
                  道具.可叠加 = false
            elseif 名称=="中级清灵仙露" then
                  道具.灵气=取随机数(2,6)
                  道具.可叠加 = false
            elseif 名称=="高级清灵仙露" then
                  道具.灵气=取随机数(4,8)
                  道具.可叠加 = false
            elseif 名称=="超级清灵仙露" then
                  道具.灵气=50
                  道具.可叠加 = false
            elseif 名称=="终级清灵仙露" then
                  道具.灵气=110
                  道具.可叠加 = false
            elseif 名称=="召唤兽内丹" then
                  if 参数==nil or 参数==""  then
                   道具.附带技能=取内丹()
                  else
                    道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="高级召唤兽内丹" then
                  if 参数==nil or 参数==""  then
                   道具.附带技能=取内丹("高级")
                  else
                    道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="魔兽要诀" then
                  if 参数==nil or 参数==""  then
                    道具.附带技能=取低级要诀()
                  else
                    道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="高级魔兽要诀" then
                 if 参数==nil or 参数==""  then
                   道具.附带技能=取高级要诀()
                  else
                   道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="特殊魔兽要诀" then
                 if 参数==nil or 参数==""  then
                   道具.附带技能=取特殊要诀()
                  else
                   道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="超级魔兽要诀" then
                 if 参数==nil or 参数==""  then
                   道具.附带技能=取超级要诀()
                  else
                   道具.附带技能=参数
                  end
                  道具.可叠加 = false
            elseif 名称=="帮派银票" then
                  道具.初始金额=52000
                  道具.完成金额=180000
                  道具.专用="专用"
                  道具.可叠加 = false
            elseif 名称=="月华露" or 名称=="九转金丹" or 名称=="修炼果" or 名称=="超级修炼果"  then
                  if  参数 ==nil or 参数=="" then 参数 = 50 end
                  道具.阶品=参数
                  道具.可叠加 = true
            elseif 名称=="强化升级丹" then
                    if 参数 ==nil  or 参数==""  then
                      local 强化技能 = {"人物伤害","人物防御","人物气血","人物法术","人物速度","人物固伤","人物治疗","宠物伤害","宠物防御","宠物气血","宠物灵力","宠物速度"}
                      道具.阶品=强化技能[取随机数(1,#强化技能)]
                    else
                       道具.阶品=参数
                    end
                    道具.可叠加 = true
            elseif  名称 == "金柳露"      or  名称=="圣兽丹" or  名称=="吸附石" or 名称=="金刚石" or 名称=="定魂珠" or 名称=="夜光珠" or 名称=="避水珠" or 名称=="龙鳞"
                or  名称=="超级金柳露"      or  名称=="神兜兜"  or  名称=="彩果" or 名称=="分解符"  or 名称=="三界悬赏令" or 名称=="青龙石" or 名称=="白虎石" or 名称=="朱雀石"
                or 名称=="玄武石"           or 名称=="炼兽真经"  or 名称=="金银锦盒" or 名称=="2倍经验丹" or 名称=="3倍经验丹"  or 名称=="120级随机自选包" or 名称=="130级随机自选包"
                or 名称=="140级随机自选包"  or 名称=="150级随机自选包" or 名称=="160级随机自选包" or 名称=="铃铛忙盒"  or 名称 == "雪人鼻子" or 名称 == "雪人帽子" or 名称 == "雪人眼睛"
                or 名称=="随机强化丹" or 名称=="小型奖券" or 名称=="中型奖券" or 名称=="大型奖券" or 名称=="寻龙盘(小)" or 名称=="寻仙盘(小)"
                or 名称=="寻宝盘(小)" or 名称=="寻灵盘(小)" or 名称=="寻龙盘(中)" or 名称=="寻仙盘(中)" or 名称=="寻宝盘(中)" or 名称=="寻灵盘(中)"
                or 名称=="寻龙盘(大)" or 名称=="寻仙盘(大)" or 名称=="寻宝盘(大)" or 名称=="寻灵盘(大)" or 名称=="仙灵丹" or 名称=="特效点化石" or 名称=="特技点化石" or 名称=="无级别点化石"
                or 名称=="灵饰点化石" or 名称=="铃铛盲盒"  or 名称=="一级法宝碎片"  or 名称=="二级法宝碎片"  or 名称=="三级法宝碎片"  or 名称=="避火诀" or 名称=="神兽破封石" or 名称=="功德残卷"
                or 名称=="一级仙缘"   or 名称=="二级仙缘" or 名称=="三级仙缘" or 名称=="四级仙缘" or 名称=="五级仙缘" or 名称=="六级仙缘" or 名称=="七级仙缘"
                or 名称=="八级仙缘"   or 名称=="九级仙缘" or 名称=="十级仙缘" or 名称=="灵犀之屑" or 名称=="未鉴定的灵犀玉" or 名称=="月饼" or 名称=="解封丹" or 名称=="钟馗令牌" or 名称=="副本重置丹"
                or 名称=="双倍掉宝符" or 名称=="坐骑蛋"   or 名称=="锦衣碎片" or 名称=="仙露丸子" or 名称=="仙露小丸子" or 名称=="神兽涎" or 名称=="灵饰洗炼石"
                or 名称=="特殊兽决碎片" or 名称=="超级兽决碎片"  or 名称=="鸿蒙灵宝" or 名称=="鸿蒙仙宝" or 名称=="鸿蒙神宝" or 名称=="破碎的鸿蒙灵宝"
                or 名称=="破碎的鸿蒙仙宝" or 名称=="破碎的鸿蒙神宝" or 名称=="太初灵石" or 名称=="太初仙石" or 名称=="太初神石" or 名称=="破碎的太初灵石"
                or 名称=="破碎的太初仙石" or 名称=="破碎的太初神石" or string.find(名称, "礼包")~=nil then
                道具.可叠加 = true
            elseif 名称=="鸿蒙原石" or 名称=="太初原石" then
                   道具.可叠加 = true
                  if 参数==nil or 参数=="" or type(参数)~="table" then
                      local 临时列表 = {}
                      for k,v in pairs(境界属性) do
                              table.insert(临时列表, k)
                      end
                      道具.附带词条=临时列表[取随机数(1,#临时列表)]
                      道具.数额=境界属性[道具.附带词条].优秀[1]
                  else
                        道具.附带词条=参数.类型
                        道具.数额=参数.数额
                  end
              elseif 名称=="归墟之证" then
                        道具.可叠加 = false
                       if 数量==nil then
                            道具.等级=取随机数(1,5)
                       else
                           道具.等级=数量
                       end
                       local  任务类型 ={"抓鬼任务","鬼王任务","皇宫飞贼","雁塔地宫"}
                       local 类型 = ""
                       for i,v in ipairs(任务类型) do
                            if 参数==v then
                                类型 = v
                            end
                       end
                       if 类型~="" then
                            道具.类型 = 参数
                       else
                            道具.类型 =任务类型[取随机数(1,#任务类型)]
                       end
              elseif 名称=="设计图" then

                        道具.可叠加 = false
                        if  参数~=nil then
                             道具.类型 = 参数
                        else
                             道具.类型 = "黑麻垂曼帘"
                        end
                        local 道具级别=1
                        for i=1,#self.二级家具 do
                          if 道具.类型==self.二级家具[i] then
                              道具级别=2
                              break
                           end
                        end
                        道具.等级=道具级别
              elseif 名称 == "功德录"  then
                    道具.可叠加 = false
              elseif 名称=="元宵" then
                   local 食材 = {"芝麻沁香","桂花酒酿","细磨豆沙","蜜糖腰果","蜜糖腰果","蜜糖腰果","山楂拔丝","滑玉莲蓉"}
                    if 参数==nil or 参数=="" then
                      道具.食材 = 食材[取随机数(1,#食材)]
                    else
                     道具.食材=参数
                    end
                    道具.可叠加 = true
              elseif 名称=="清灵净瓶" then
                    道具.可叠加 = true
              elseif 名称=="藏宝图" or 名称=="高级藏宝图" or 名称=="玲珑宝图"  then
                      local 随机地图={1501,1506,1092,1091,1110,1142,1514,1174,1173,1146,1208}
                      local 临时地图=随机地图[取随机数(1,#随机地图)]
                      道具.地图名称=取地图名称(临时地图)
                      道具.地图编号=临时地图
                      local xy=地图处理类.地图坐标[临时地图]:取随机点()
                      道具.x=xy.x
                      道具.y=xy.y
                      道具.可叠加 = false

              elseif 名称=="炼妖石" or 名称=="上古锻造图策" then
                     道具.级别限制=数量[取随机数(1,#数量)]
                    if 参数 == nil or 参数==""  then
                      道具.种类=图策范围[取随机数(1,#图策范围)]
                    else
                      道具.种类=参数
                    end
                    道具.可叠加 = false
              elseif 名称=="天眼珠" then
                    道具.级别限制=数量[取随机数(1,#数量)]
                     if 参数 == nil or 参数==""  then
                         道具.灵气=取随机数(20,80)
                      else
                         道具.灵气=参数
                      end
                   道具.分类=11
                   道具.可叠加 = false
              elseif 名称=="珍珠" then
                    道具.级别限制 = tonumber(数量)
                     道具.可叠加 = false
              elseif 名称=="战魂" then
                    道具.级别限制=数量
                     道具.可叠加 = false
              elseif 名称~=nil and string.find(名称, "符石") ~= nil and  not self.额外符石[名称] and not self.新三符石[名称] then
                    local xlshi = 取物品数据(名称)
                    if xlshi==nil or xlshi=={} and (xlshi~=nil or xlshi~={} and #xlshi < 18)then
                      return
                    end
                    道具.级别 = 数量
                    道具.耐久度 = 150
                    道具.颜色= xlshi[20]--颜色
                    道具.符石属性 = xlshi[21]
                    道具.分类= 88
                    道具.不可交易= true
                    道具.可叠加= false
              elseif self.新三符石[名称]  then
                    local xlshi = 取物品数据(名称)
                    if xlshi==nil or xlshi=={} and (xlshi~=nil or xlshi~={} and #xlshi < 18)then
                      return
                    end
                    道具.级别 = 3
                    道具.耐久度 = 150
                    道具.颜色= xlshi[20]--颜色
                    道具.符石属性 = xlshi[21]
                    道具.分类= 88
                    道具.可叠加= false
              elseif 名称=="一级未激活符石" then
                     道具.级别=1
                     道具.可叠加=true
              elseif 名称=="二级未激活符石"  then
                    道具.级别=2
                    道具.可叠加=true
              elseif 名称=="三级未激活符石" then
                    道具.级别=3
                    道具.可叠加=true

              elseif 名称=="未激活的星石" then
                    if 参数==nil or 参数=="" then
                      道具.子类 = 取随机数(1,6)
                    else
                      if 参数 == "头盔" then
                        道具.子类 = 1
                      elseif 参数 == "饰物" then
                        道具.子类 = 2
                      elseif 参数 == "武器" then
                        道具.子类 = 3
                      elseif 参数 == "衣甲" then
                        道具.子类 = 4
                      elseif 参数 == "腰带" then
                        道具.子类 = 5
                      elseif 参数 == "靴子" then
                        道具.子类 = 6
                      else
                        道具.子类 = 取随机数(1,6)
                      end
                    end
                     道具.可叠加 = false
              elseif 名称=="符石卷轴" then
                    道具.可叠加=true
              elseif 名称=="怪物卡片" then
                    local 变身等级 = tonumber(数量)
                    if 变身等级==nil or 变身等级<=0 then
                       变身等级 =取随机数(1,3)
                    end
                    if 变身等级>=9 then 变身等级=9 end
                    if 参数==nil or 参数==""  then
                       道具.等级=变身等级
                       道具.造型=变身卡范围[变身等级][取随机数(1,#变身卡范围[变身等级])]
                    else
                      if 变身卡数据[参数]==nil then
                         道具.等级=变身等级
                         道具.造型=变身卡范围[变身等级][取随机数(1,#变身卡范围[变身等级])]
                      else
                          道具.等级=变身卡数据[参数].等级
                          道具.造型=参数
                      end
                    end
                    道具.类型=变身卡数据[道具.造型].类型
                    道具.单独=变身卡数据[道具.造型].单独
                    道具.正负=变身卡数据[道具.造型].正负
                    道具.技能=变身卡数据[道具.造型].技能
                    道具.属性=变身卡数据[道具.造型].属性
                    道具.次数=道具.等级
                    道具.可叠加 = false
              elseif 名称=="金创药" or 名称=="小还丹" or 名称=="千年保心丹" or 名称=="金香玉" or 名称=="风水混元丹"
                or 名称=="蛇蝎美人" or 名称=="定神香" or 名称=="佛光舍利子" or 名称=="九转回魂丹" or 名称=="五龙丹"
                or 名称=="十香返生丸"
                then
                    if 参数==nil or 参数=="" then 参数 = 10 end
                    道具.阶品=tonumber(参数)
                    道具.可叠加 = true
              elseif 道具.总类==5 and  道具.分类==4 then
                  道具.级别限制=tonumber(数量)
                  道具.可叠加 = false
              elseif 道具.总类==5 and  道具.分类==6 then
                  道具.级别限制=tonumber(数量)
                  道具.可叠加 = false
              elseif 道具.总类==1 and 道具.子类==1 and 道具.分类==4 then
                  if 参数==nil or 参数=="" then 参数 = 10 end
                  道具.阶品=tonumber(参数)
                  道具.可叠加 = true
              elseif 道具.总类==144 then
                  道具.可叠加 = true
              end
              if 道具.可叠加 then
                  道具.数量= tonumber(数量) or 1
              end
              return 道具
end




function 摆摊假人类:随机商品处理(名称)
           if  名称=="人物伤害" or 名称=="人物气血" or 名称=="人物防御" or 名称=="人物法术" or 名称=="人物速度"
                 or 名称=="人物固伤" or 名称=="人物治疗" or 名称=="宠物伤害" or 名称=="宠物防御" or 名称=="宠物气血"
                 or 名称=="宠物灵力" or 名称=="宠物速度"
                 then
                 return "强化升级丹",1,名称
            elseif 名称=="芝麻沁香" or 名称=="山楂拔丝" or 名称=="桂花酒酿" or 名称=="细磨豆沙" or 名称=="蜜糖腰果"  or 名称=="滑玉莲蓉" then
                    return "元宵",1,名称
            elseif 名称=="九转金丹" then
                    return "九转金丹",1,100
            elseif 名称=="九转金丹(200品质)" then
                   return "九转金丹",1,200
            elseif 名称=="九转金丹(400品质)" then
                    return "九转金丹",1,400
            elseif 名称=="九转金丹(800品质)" then
                    return "九转金丹",1,800
            elseif 名称=="九转金丹(1600品质)" then
                    return "九转金丹",1,1600
            elseif 名称=="九转金丹(3000品质)" then
                    return "九转金丹",1,3000
            elseif 名称=="月华露" then
                    return "月华露",1,100
            elseif 名称=="月华露(1000品质)" then
                    return "月华露",1,1000
            elseif 名称=="月华露(2000品质)" then
                    return "月华露",1,2000
            elseif string.find(名称, "品九转金丹") or string.find(名称, "品月华露")
                  or string.find(名称, "品小还丹") or string.find(名称, "品金香玉")
                  or string.find(名称, "品定神香") or string.find(名称, "品五龙丹")
                  or string.find(名称, "品蛇蝎美人") or string.find(名称, "品佛光舍利子")
                  or string.find(名称, "品千年保心丹") or string.find(名称, "品风水混元丹")
                  or string.find(名称, "品九转回魂丹") or string.find(名称, "品十香返生丸")
                  or string.find(名称, "品烤肉") or string.find(名称, "品烤鸭")
                  or string.find(名称, "品珍露酒") or string.find(名称, "品虎骨酒")
                  or string.find(名称, "品女儿红") or string.find(名称, "品豆斋果")
                  or string.find(名称, "品佛跳墙") or string.find(名称, "品桂花丸")
                  or string.find(名称, "品臭豆腐") or string.find(名称, "品长寿面")
                  or string.find(名称, "品梅花酒") or string.find(名称, "品百味酒")
                  or string.find(名称, "品长寿面") or string.find(名称, "品蛇胆酒")
                  or string.find(名称, "品醉生梦死") or string.find(名称, "品翡翠豆腐") then
                  local 取出=分割文本(名称,"品")
                  return 取出[2],1,tonumber(取出[1])
            elseif 名称=="灵宝图鉴" or 名称=="神兵图鉴" or 名称=="灵饰图鉴" or 名称=="珍珠"  then
                    return 名称,1,160
            elseif 名称=="炼妖石" or 名称=="上古锻造图策" or 名称=="天眼珠"  then
                    return 名称,{155},nil
            elseif 名称=="钨金" or 名称=="附魔宝珠" or 名称=="超级附魔宝珠"  then
                    return 名称,{160},nil
            elseif 名称 == "鬼谷子" then
                    return 名称,1,nil
            elseif 名称=="天覆阵"  then
                    return "鬼谷子",1,名称
            elseif 名称=="善恶点化石"  then
                    return "点化石",1,"善恶有报"
            elseif 名称=="力劈点化石"  then
                    return "点化石",1,"力劈华山"
            elseif 名称=="高神点化石"  then
                    return "点化石",1,"高级神佑复生"
            elseif 名称=="剑荡点化石"  then
                    return "点化石",1,"剑荡四方"
            elseif 名称=="高鬼点化石"  then
                    return "点化石",1,"高级鬼魂术"
            elseif  string.find(名称, "点化石") and  string.find(名称, "&") then
                    local 取出技能=分割文本(名称,"&")
                    return "点化石",1,取出技能[2]
            elseif 名称=="怪物卡片1"  then
                    return "怪物卡片",1,nil
            elseif 名称=="怪物卡片2"  then
                    return "怪物卡片",2,nil
            elseif 名称=="怪物卡片3"  then
                    return "怪物卡片",3,nil
            elseif 名称=="怪物卡片4"  then
                    return "怪物卡片",4,nil
            elseif 名称=="怪物卡片5"  then
                    return "怪物卡片",5,nil
            elseif 名称=="怪物卡片6"  then
                    return "怪物卡片",6,nil
            elseif 名称=="怪物卡片7"  then
                    return "怪物卡片",7,nil
            elseif 名称=="怪物卡片8"  then
                    return "怪物卡片",8,nil
            elseif 名称=="怪物卡片9"  then
                    return "怪物卡片",9,nil
            elseif 名称=="1级伤害精魄灵石"  then
                    return "精魄灵石",1,"伤害"
            elseif  string.find(名称, "变身卡")~=nil  then
                    local 取出造型=分割文本(名称,"&")
                    return "怪物卡片",1,取出造型[2]
            elseif string.find(名称, "级红玛瑙") or string.find(名称, "级太阳石") or
                    string.find(名称, "级月亮石") or string.find(名称, "级光芒石") or
                    string.find(名称, "级黑宝石") or string.find(名称, "级舍利子") or
                    string.find(名称, "级星辉石") or string.find(名称, "级钟灵石") or
                    string.find(名称, "级精魄灵石") then
                    local 取出=分割文本(名称,"级")
                    return 取出[2],tonumber(取出[1])
           elseif string.find(名称, "星归墟之证") then
                    local 取出=分割文本(名称,"星")
                    return "归墟之证",tonumber(取出[1])
           elseif  string.find(名称, "归墟之证")  and  string.find(名称, "&")  then
                    local 取出造型=分割文本(名称,"&")
                    if string.find(取出造型[2], "星")~=nil  then
                        local 取出 =分割文本(取出造型[2],"星")
                        return "归墟之证",tonumber(取出[1]),取出[2]
                    else
                         return "归墟之证",nil,取出造型[2]
                    end
            elseif 名称=="上古灵符" or 名称=="千钧一怒" or 名称=="夜舞倾城" or 名称=="须弥真言" or 名称=="高级龙魂" or 名称=="进击法暴"
                    or 名称=="扶摇万里"  or 名称=="进击必杀" or 名称=="惊心一剑" or 名称=="八凶法阵" or 名称=="天降灵葫" or 名称=="嗜血追击"
                    or 名称=="净台妙谛"  or 名称=="壁垒击破" or 名称=="力劈华山" or 名称=="善恶有报" or 名称=="剑荡四方" or 名称=="从天而降"
                    or 名称=="死亡召唤" or 名称=="高级盾气" or 名称=="张弛有道" or 名称=="灵山禅语" or 名称=="凝光炼彩" or 名称=="流沙轻音"
                    or 名称=="食指大动" or 名称=="叱咤风云" or 名称=="溜之大吉" or 名称=="赴汤蹈火" or 名称=="开门见山" or 名称=="昼伏夜出"
                    or 名称=="神出鬼没" or 名称=="移花接木" or 名称=="高级进击必杀" or 名称=="高级进击法暴" or 名称=="大快朵颐" or 名称=="观照万象"
                    or 名称=="浮云神马" or 名称=="理直气壮" or 名称=="灵能激发" or 名称=="苍鸾怒击" or 名称=="月光" or 名称=="凭风借力"
                    or 名称=="狂莽一击" or 名称=="虎虎生威" or 名称=="出其不意" or 名称=="风起龙游" or 名称=="北冥之渊" or 名称=="气贯长虹"
                    or 名称=="无畏布施" or 名称=="神来气旺" or 名称=="水击三千" or 名称=="哼哼哈兮" or 名称=="义薄云天" or 名称=="高级驱怪" then
                        return "特殊魔兽要诀",nil,名称
            elseif 名称=="超级毒" or 名称=="超级夜战" or 名称=="超级反震" or 名称=="超级吸血"or 名称=="超级连击" or 名称=="超级飞行" or 名称=="超级隐身" or 名称=="超级驱怪"
                    or 名称=="超级感知" or 名称=="超级再生" or 名称=="超级冥思" or 名称=="超级驱鬼" or 名称=="超级慧根" or 名称== "超级必杀" or 名称=="超级幸运"
                    or 名称=="超级神迹" or 名称=="超级招架" or 名称=="超级永恒" or 名称=="超级敏捷" or 名称=="超级偷袭" or 名称=="超级强力" or 名称=="超级防御"
                    or 名称=="超级盾气" or 名称=="超级合纵" or 名称=="超级魔之心" or 名称=="超级奔雷咒" or 名称=="超级泰山压顶" or 名称=="超级水漫金山"
                    or 名称=="超级地狱烈火" or 名称=="超级进击必杀" or 名称=="超级进击法暴" or 名称=="超级法术连击" or 名称=="超级法术暴击"
                    or 名称=="超级法术波动" or 名称=="超级壁垒击破" or 名称=="超级法术抵抗" or 名称=="超级精神集中" or 名称=="超级否定信仰"
                    or 名称=="超级雷属性吸收" or 名称=="超级土属性吸收" or 名称=="超级水属性吸收" or 名称=="超级火属性吸收" then
                    return "超级魔兽要诀",nil,名称
            elseif string.find(名称, "打造书")  then
                    local 取出=分割文本(名称,"打造书")
                    return "制造指南书",math.floor(取出[1]),取随机数(1,#书铁范围)
            elseif string.find(名称, "级精铁")  then
                    local 取出=分割文本(名称,"级精铁")
                    return "百炼精铁",math.floor(取出[1]),取随机数(1,#书铁范围)
            elseif string.find(名称, "灵饰书")  then
                    local 取出=分割文本(名称,"灵饰书")
                    return "灵饰指南书",{math.floor(取出[1]/10)},nil
            elseif string.find(名称, "灵饰书")  then
                    local 取出=分割文本(名称,"灵饰书")
                    return "灵饰指南书",{math.floor(取出[1]/10)},nil
            elseif string.find(名称, "灵饰铁")  then
                    local 取出=分割文本(名称,"灵饰铁")
                    return "元灵晶石",{math.floor(取出[1]/10)},nil
            end
            return 名称,1,nil
end




 function 摆摊假人类:添加召唤兽(模型,名称,类型,物法,等级,技能组)
      local 生成数据 = {}
      if 物法 == nil  then
        if 类型~="神兽" then
            生成数据=self:置新对象(模型,名称,类型)
        else
            生成数据=self:置神兽对象(模型,名称)
        end
      else
            生成数据=self:置神兽对象(模型,名称,物法)
      end
      if 等级 and 等级~=0 then
            生成数据.等级 = 等级
      end
      if 技能组~=nil then
           生成数据.技能 =DeepCopy(技能组)
           生成数据.技能 =删除重复(生成数据.技能)
      end
      for n=1,5 do
          生成数据.加点记录[self.属性范围[n]]=0
      end
      生成数据.潜力=生成数据.等级*5+生成数据.灵性*2
      生成数据.加点记录[self.属性范围[取随机数(1,3)]] = 生成数据.潜力
      生成数据.潜力 = 0
      self:刷新信息(生成数据)
      return 生成数据


end



function 摆摊假人类:置新对象(模型,名称,类型)
  local 生成数据 = {}
  if 类型 == "神兽"  then
   return self:置神兽对象(模型,名称)
  end
  local n = 取宝宝(模型)
  if n[1] == nil  then
    生成数据.模型 = 模型
    生成数据.等级 =  1
    生成数据.种类 = 类型
    生成数据.名称 = 名称 or 模型
    生成数据.攻击资质 = 900
    生成数据.防御资质 = 900
    生成数据.体力资质 = 1500
    生成数据.法力资质 = 1000
    生成数据.速度资质 = 900
    生成数据.躲闪资质 = 900
    生成数据.技能 =  {}
    生成数据.成长 = 0.9
    生成数据.参战等级 =  0
    生成数据.五行 = 五行_[取随机数(1,5)]
    生成数据.内丹 ={内丹上限=math.floor(生成数据.参战等级 / 35)+1,可用内丹=math.floor(生成数据.参战等级 / 35)+1}
    生成数据.内丹数据={}
    生成数据.天生技能 ={}
    生成数据.忠诚 = 100
    生成数据.体质 = 0
    生成数据.魔力 = 0
    生成数据.力量 = 0
    生成数据.耐力 = 0
    生成数据.敏捷 = 0
    生成数据.最大气血 = 0
    生成数据.最大魔法 = 0
    生成数据.伤害 = 0
    生成数据.防御 = 0
    生成数据.灵力 = 0
    生成数据.速度 = 0
    生成数据.气血 = 0
    生成数据.魔法 = 0
    生成数据.法伤 = 0
    生成数据.法防 = 0
    生成数据.潜力 = 0
    生成数据.灵性 = 0
    生成数据.进阶 = false
    生成数据.仙露上限 = 7
    生成数据.特性 = "无"
    生成数据.特性几率 = 0
    生成数据.初始属性={
          体质=10,
          魔力=10,
          力量=10,
          耐力=10,
          敏捷=10
            }
    生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
    生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
    生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
    生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
    生成数据.饰品 = nil
    生成数据.装备 = {}
    生成数据.当前经验=0
    生成数据.最大经验=50
    生成数据.认证码=取唯一识别码(9999999999)
    return 生成数据
  end
  生成数据.等级 =  1
  生成数据.模型 = 模型
  生成数据.种类 = 类型 or "野怪"
  生成数据.寿命=5000
  生成数据.天生技能 ={}
  生成数据.体质 = 0
  生成数据.魔力 = 0
  生成数据.力量 = 0
  生成数据.耐力 = 0
  生成数据.敏捷 = 0
  生成数据.最大气血 = 0
  生成数据.最大魔法 = 0
  生成数据.伤害 = 0
  生成数据.防御 = 0
  生成数据.灵力 = 0
  生成数据.速度 = 0
  生成数据.气血 = 0
  生成数据.魔法 = 0
  生成数据.法伤 = 0
  生成数据.法防 = 0
  生成数据.潜力 = 0
  生成数据.忠诚 = 100
  local 波动上限 = 1
  local 能力 = 0
  if 生成数据.种类 == "野怪"  then
     能力 = 0.725
     波动上限 = 0.95
    生成数据.忠诚 = 80
    生成数据.初始属性={
          体质=取随机数(5,15),
          魔力=取随机数(5,15),
          力量=取随机数(5,15),
          耐力=取随机数(5,15),
          敏捷=取随机数(5,15)
            }
  elseif 生成数据.种类 == "宝宝" then
    能力 = 0.925
    波动上限 = 1.1
    生成数据.初始属性={
          体质=10,
          魔力=10,
          力量=10,
          耐力=10,
          敏捷=10
            }
  elseif 生成数据.种类 == "变异" or 生成数据.种类 == "孩子" then
    能力 = 1.05
    波动上限 = 1.2
    生成数据.初始属性={
          体质=15,
          魔力=15,
          力量=15,
          耐力=15,
          敏捷=15
            }
  end
  生成数据.名称 = 名称 or 模型
  生成数据.参战等级 = n[1]
  if 生成数据.名称 == "超级大熊猫" then
    生成数据.攻击资质= n[2]
    生成数据.防御资质= n[3]
    生成数据.体力资质= n[4]
    生成数据.法力资质= n[5]
    生成数据.速度资质= n[6]
    生成数据.躲闪资质= n[7]
  else
    生成数据.攻击资质= math.ceil(n[2]*取随机小数(能力,波动上限))
    生成数据.防御资质= math.ceil(n[3]*取随机小数(能力,波动上限))
    生成数据.体力资质= math.ceil(n[4]*取随机小数(能力,波动上限))
    生成数据.法力资质= math.ceil(n[5]*取随机小数(能力,波动上限))
    生成数据.速度资质= math.ceil(n[6]*取随机小数(能力,波动上限))
    生成数据.躲闪资质= math.ceil(n[7]*取随机小数(能力,波动上限))
  end
  local jn = n[9]
  local jn0 = {}
  local cz1 = 取随机数(1,100)
  if cz1 < 30 then
    生成数据.成长 = n[8][1]
  elseif cz1 > 30  and cz1 < 60 then
    生成数据.成长 = n[8][2]
  elseif cz1 > 60  and cz1 < 80 then
    生成数据.成长 = n[8][3]
  elseif cz1 > 80  and cz1 < 95 then
    生成数据.成长 = n[8][4]
  elseif cz1 > 95  and cz1 < 100 then
    生成数据.成长 = n[8][5]
  end
  if 生成数据.成长 == 0 or 生成数据.成长 == nil  then
    生成数据.成长 = n[8][1]
  end

  if  生成数据.名称 == "超级大熊猫" then
    生成数据.技能=DeepCopy(n[9])
  else
    for q=1,#jn do
      table.insert(jn0, jn[取随机数(1,#jn)])
    end
    生成数据.技能={}
    jn0 = 删除重复(jn0)
    for j=1,#jn0 do
      生成数据.技能[j] = jn0[j]
    end
  end
  生成数据.五行 = 五行_[取随机数(1,5)]
  if n.染色方案 ~= nil then
    生成数据.染色方案 = n.染色方案
    生成数据.染色组 = {1,0}
  end
  if 类型=="变异" then
    生成数据.变异=true
    if 染色信息[模型]~=nil then
      生成数据.染色方案 = 染色信息[模型].id
      生成数据.染色组 = {染色信息[模型].方案[1],染色信息[模型].方案[2]}
    end
  end
  生成数据.灵性 = 0
  生成数据.进阶 = false
  生成数据.仙露上限 = 7
  生成数据.特性 = "无"
  生成数据.特性几率 = 0
  生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
  生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
  生成数据.饰品 = nil
  生成数据.装备 = {}
  生成数据.当前经验=0
  生成数据.最大经验=50
  生成数据.内丹数据 = {}
  生成数据.内丹 ={内丹上限=math.floor(生成数据.参战等级 / 35)+1,可用内丹=math.floor(生成数据.参战等级 / 35)+1}
  生成数据.认证码=取唯一识别码(9999999)
return  生成数据

end



function 摆摊假人类:置神兽对象(模型,名称,物法)
  local 生成数据 = {}
  local 宝宝名称=模型
  if 物法 ~= nil then
         宝宝名称 = 模型..物法
     else
      if 取随机数()<=50 then
         宝宝名称 = 模型.."(法术型)"
        else
         宝宝名称 = 模型.."(物理型)"
        end
     end
  local n = 取商城宝宝(宝宝名称)
  生成数据.模型 = 模型
  生成数据.等级 =  1
  生成数据.参战等级 = n[1]
  生成数据.种类 = "神兽"
  生成数据.名称 = 名称 or 模型
  生成数据.忠诚 = 100
  生成数据.体质 = 0
  生成数据.魔力 = 0
  生成数据.力量 = 0
  生成数据.耐力 = 0
  生成数据.敏捷 = 0
  生成数据.最大气血 = 0
  生成数据.最大魔法 = 0
  生成数据.伤害 = 0
  生成数据.防御 = 0
  生成数据.灵力 = 0
  生成数据.速度 = 0
  生成数据.气血 = 0
  生成数据.魔法 = 0
  生成数据.法伤 = 0
  生成数据.法防 = 0
  生成数据.寿命=10000
  生成数据.攻击资质= n[2]
  生成数据.防御资质= n[3]
  生成数据.体力资质= n[4]
  生成数据.法力资质= n[5]
  生成数据.速度资质= n[6]
  生成数据.躲闪资质= n[7]
  生成数据.技能=DeepCopy(n[9])
  生成数据.天生技能 = DeepCopy(n[11])
  生成数据.成长 = n[8]
  生成数据.潜力 = 0
  生成数据.五行 = 五行_[取随机数(1,5)]
  if 生成数据.天生技能 ~=nil and 生成数据.天生技能[1]~=nil  then
   for i = 1, #生成数据.天生技能 do
    生成数据.技能[#生成数据.技能+1] = 生成数据.天生技能[i]
   end
  end
  生成数据.灵性 = 0
  生成数据.进阶 = false
  生成数据.仙露上限 = 7
  生成数据.特性 = "无"
  生成数据.特性几率 = 0
  生成数据.初始属性={
        体质=20,
        魔力=20,
        力量=20,
        耐力=20,
        敏捷=20
      }
  生成数据.加点记录={
        体质=0,
        魔力=0,
        力量=0,
        耐力=0,
        敏捷=0
      }
  生成数据.进阶属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  生成数据.统御属性 = {
        力量 = 0,
        敏捷 = 0,
        耐力 = 0,
        魔力 = 0,
        体质 = 0,
      }
  生成数据.装备属性 = {
        气血 = 0,
        魔法 = 0,
        命中 = 0,
        伤害 = 0,
        防御 = 0,
        速度 = 0,
        躲避 = 0,
        灵力 = 0,
        体质 = 0,
        魔力 = 0,
        力量 = 0,
        耐力 = 0,
        敏捷 = 0,
      }
  生成数据.饰品 = nil
  生成数据.装备 = {}
  生成数据.当前经验=0
  生成数据.最大经验=50
  生成数据.内丹数据 = {}
  生成数据.内丹 ={内丹上限=6,可用内丹=6}
  生成数据.认证码=取唯一识别码(99999999)

 return 生成数据
end







function 摆摊假人类:刷新信息(单位)
        for n=1,5 do
             单位[self.属性范围[n]]=单位.初始属性[self.属性范围[n]]+单位.加点记录[self.属性范围[n]]+单位.等级
        end
        if 服务端参数.宠物仿官 then
            单位.最大气血 =  math.ceil(单位.等级 * 单位.体力资质 /1000 + 单位.体质 * 单位.成长 * 6)
            单位.最大魔法 = math.ceil(单位.等级 * 单位.法力资质 /500 + 单位.魔力 * 单位.成长 * 3)
            单位.伤害 =math.ceil(单位.等级 * 单位.攻击资质 * (13.8+10*单位.成长)/7500 + 单位.力量 * 单位.成长)
            单位.防御 =math.ceil(单位.等级 * 单位.防御资质 *(4.8+9.25*单位.成长)/7500 + 单位.耐力 * 单位.成长 * 1.33)
            单位.灵力 = math.ceil(单位.等级 * 单位.法力资质 /3400  +单位.魔力 * 单位.成长 * 0.7 + 单位.力量*0.4+ 单位.体质 *0.3+单位.耐力*0.2)
            单位.速度 = math.ceil(单位.敏捷 * 单位.速度资质/1000)
            单位.躲闪 = math.ceil(单位.等级 * 单位.躲闪资质 * 0.000845 + 单位.敏捷 * 单位.成长 * 1.3)
        else

            单位.最大气血 = math.ceil(单位.等级 * 单位.体力资质 * 0.003 + 单位.体质 * 单位.成长 * 8)
            单位.最大魔法 = math.ceil(单位.等级 * 单位.法力资质 * 0.002 + 单位.魔力 * 单位.成长 * 6)
            单位.伤害 = math.ceil(单位.等级 * 单位.攻击资质 * 0.003 + 单位.力量 * 单位.成长 * 2.4 + 单位.体质 *0.3)
            单位.防御 = math.ceil(单位.等级 * 单位.防御资质 * 0.0025 + 单位.耐力 * 单位.成长 * 1.6 )
            单位.速度 = math.ceil(单位.等级 * 单位.速度资质 * 0.0025 + 单位.敏捷 * 单位.成长 * 1.3)
            单位.灵力 = math.ceil(单位.等级 * 单位.法力资质 * 0.0009 + 单位.魔力 * 单位.成长 * 1.3 + 单位.力量*0.3+ 单位.体质 *0.2+单位.耐力*0.1)
            单位.躲闪 = math.ceil(单位.等级 * 单位.躲闪资质 * 0.001 + 单位.敏捷 * 单位.成长 * 1.3)
        end
        if  单位.等级 <= 185 then
              单位.最大经验 = 升级消耗.宠物[单位.等级+1]
        end
        单位.法伤 = 单位.灵力
        单位.法防 = 单位.灵力*0.7
        单位.气血=单位.最大气血
        单位.魔法=单位.最大魔法
end






return 摆摊假人类










