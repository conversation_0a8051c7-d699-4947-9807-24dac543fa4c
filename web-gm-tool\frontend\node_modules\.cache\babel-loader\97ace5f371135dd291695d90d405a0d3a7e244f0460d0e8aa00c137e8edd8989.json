{"ast": null, "code": "// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-tbody > tr${componentCls}-placeholder`]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [`\n          &:hover > th,\n          &:hover > td,\n        `]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;", "map": {"version": 3, "names": ["genEmptyStyle", "token", "componentCls", "textAlign", "color", "colorTextDisabled", "background", "colorBgContainer"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/es/table/style/empty.js"], "sourcesContent": ["// ========================= Placeholder ==========================\nconst genEmptyStyle = token => {\n  const {\n    componentCls\n  } = token;\n  return {\n    [`${componentCls}-wrapper`]: {\n      [`${componentCls}-tbody > tr${componentCls}-placeholder`]: {\n        textAlign: 'center',\n        color: token.colorTextDisabled,\n        [`\n          &:hover > th,\n          &:hover > td,\n        `]: {\n          background: token.colorBgContainer\n        }\n      }\n    }\n  };\n};\nexport default genEmptyStyle;"], "mappings": "AAAA;AACA,MAAMA,aAAa,GAAGC,KAAK,IAAI;EAC7B,MAAM;IACJC;EACF,CAAC,GAAGD,KAAK;EACT,OAAO;IACL,CAAC,GAAGC,YAAY,UAAU,GAAG;MAC3B,CAAC,GAAGA,YAAY,cAAcA,YAAY,cAAc,GAAG;QACzDC,SAAS,EAAE,QAAQ;QACnBC,KAAK,EAAEH,KAAK,CAACI,iBAAiB;QAC9B,CAAC;AACT;AACA;AACA,SAAS,GAAG;UACFC,UAAU,EAAEL,KAAK,CAACM;QACpB;MACF;IACF;EACF,CAAC;AACH,CAAC;AACD,eAAeP,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}