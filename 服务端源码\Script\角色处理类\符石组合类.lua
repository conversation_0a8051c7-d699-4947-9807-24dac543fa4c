
function 符石置对象(id,临时装备id)

  玩家数据[id].道具.数据[临时装备id].符石组合=nil
  if 玩家数据[id].道具.数据[临时装备id].符石 ~= nil then
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万丈霞光",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万丈霞光",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万丈霞光",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万丈霞光",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百步穿杨",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百步穿杨",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百步穿杨",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百步穿杨",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="隔山打牛",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="隔山打牛",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="隔山打牛",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="隔山打牛",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="心随我动",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="心随我动",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="心随我动",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="心随我动",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云随风舞",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云随风舞",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云随风舞",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云随风舞",等级=1}
         end
      end
    end

        if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then

               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="凤舞九天",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="凤舞九天",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="凤舞九天",等级=1}
         end
      end
    -- end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="望穿秋水",等级=1}
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万里横行",等级=1}
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="日落西山",等级=1}
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="网罗乾坤",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="网罗乾坤",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="网罗乾坤",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="石破天惊",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="石破天惊",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="石破天惊",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天雷地火",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天雷地火",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天雷地火",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="烟雨飘摇",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="烟雨飘摇",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="烟雨飘摇",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="索命无常",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="索命无常",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="索命无常",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="行云流水",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="行云流水",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="行云流水",等级=1}
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="福泽天下",等级=3}
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="福泽天下",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="福泽天下",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无心插柳",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无心插柳",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无心插柳",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无心插柳",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="风卷残云",等级=4}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="风卷残云",等级=3}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="风卷残云",等级=2}
           end
         else
            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="风卷残云",等级=1}
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天降大任",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天降大任",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天降大任",等级=1}
           end
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="高山流水",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="高山流水",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="高山流水",等级=1}
           end
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百无禁忌",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百无禁忌",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="百无禁忌",等级=1}
           end
         end
      end
    end

    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
          玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无懈可击",等级=1}
      end
    end


    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="点石成金",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="为官之道符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="为官之道符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="为官之道符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="黄庭经符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="黄庭经符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="黄庭经符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="小乘佛法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="小乘佛法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="小乘佛法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="毒经符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="毒经符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="毒经符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罡气符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罡气符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罡气符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九龙诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九龙诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九龙诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="周易学符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="周易学符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="周易学符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵性符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵性符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵性符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵通术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵通术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵通术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛逼神功符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛逼神功符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛逼神功符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽神功符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽神功符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽神功符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="蛛丝阵法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="蛛丝阵法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="蛛丝阵法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="文韬武略符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="文韬武略符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="文韬武略符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="归元心法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="归元心法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="归元心法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="佛光普照符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="佛光普照符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="佛光普照符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="倾国倾城符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="倾国倾城符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="倾国倾城符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="傲世诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="傲世诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="傲世诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="逆鳞符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="逆鳞符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="逆鳞符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="明性修身符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="明性修身符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="明性修身符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行扭转符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行扭转符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行扭转符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="拘魂诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="拘魂诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="拘魂诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="回身击符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="回身击符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="回身击符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽反嗜符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽反嗜符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="魔兽反嗜符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="盘丝大法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="盘丝大法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="盘丝大法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="十方无敌符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="十方无敌符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="十方无敌符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="符之术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="符之术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="符之术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="歧黄之术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="歧黄之术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="歧黄之术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="闭月羞花符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="闭月羞花符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="闭月羞花符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤塔符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="呼风唤雨符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="呼风唤雨符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="呼风唤雨符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤袖符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤袖符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="乾坤袖符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚经符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚经符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚经符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="幽冥术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="幽冥术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="幽冥术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火牛阵符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火牛阵符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火牛阵符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="生死搏符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="生死搏符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="生死搏符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="催情大法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="催情大法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="催情大法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神兵鉴赏符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神兵鉴赏符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神兵鉴赏符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="霹雳咒符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="霹雳咒符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="霹雳咒符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诵经符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诵经符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诵经符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="沉鱼落雁符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="沉鱼落雁符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="沉鱼落雁符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="宁气诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="宁气诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="宁气诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="破浪诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="破浪诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="破浪诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="潇湘仙雨符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="潇湘仙雨符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="潇湘仙雨符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行学说符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行学说符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="五行学说符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="尸腐恶符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="尸腐恶符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="尸腐恶符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛虱阵符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛虱阵符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="牛虱阵符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴阳二气诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴阳二气诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴阳二气诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="姊妹相随符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="姊妹相随符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="姊妹相随符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="暗度陈仓",等级=1}
             end
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无双一击符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无双一击符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无双一击符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化敌为友",等级=1}
             end
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="磬龙灭法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="磬龙灭法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="磬龙灭法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚伏魔符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚伏魔符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚伏魔符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="玉质冰肌符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="玉质冰肌符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="玉质冰肌符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混天术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混天术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混天术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙附符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙附符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙附符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="修仙术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="修仙术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="修仙术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="护法金刚符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="护法金刚符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="护法金刚符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="六道轮回符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="六道轮回符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="六道轮回符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="震天诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="震天诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="震天诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="狂兽诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="狂兽诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="狂兽诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秋波暗送符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秋波暗送符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秋波暗送符石",等级=1}
           end
         end
      end
    end
    -- if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
    --   if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
    --      if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
    --        if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
    --          if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
    --            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=3}
    --          else
    --            玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=2}
    --          end
    --        else
    --           玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=1}
    --        end
    --      end
    --   end
    -- end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="紫薇之术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="紫薇之术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="紫薇之术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神道无念符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神道无念符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神道无念符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大慈大悲符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大慈大悲符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大慈大悲符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="香飘兰麝符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="香飘兰麝符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="香飘兰麝符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清明自在符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清明自在符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清明自在符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙腾符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙腾符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="龙腾符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元道果符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元道果符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元道果符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="观音咒符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="观音咒符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="观音咒符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九幽阴魂符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九幽阴魂符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九幽阴魂符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火云术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火云术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="火云术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="训兽诀符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="训兽诀符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="训兽诀符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天外魔音符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天外魔音符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天外魔音符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="疾风步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="疾风步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="疾风步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="斜月步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="斜月步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="斜月步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="渡世步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="渡世步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="渡世步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清歌妙舞符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清歌妙舞符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="清歌妙舞符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云霄步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云霄步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="云霄步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="游龙术符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="游龙术符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="游龙术符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七星遁符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七星遁符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七星遁符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="莲花宝座符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="莲花宝座符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="莲花宝座符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无常步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无常步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="无常步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="裂石步符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="裂石步符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="裂石步符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大鹏展翅符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大鹏展翅符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="大鹏展翅符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="移形换影符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="移形换影符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="移形换影符石",等级=1}
           end
         end
      end
    end
    --凌波城符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="蓝色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天地无极符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天地无极符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天地无极符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="啸傲符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="啸傲符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="啸傲符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="法天象地符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="法天象地符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="法天象地符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="气吞山河符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="气吞山河符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="气吞山河符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="武神显圣符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="武神显圣符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="武神显圣符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诛魔符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诛魔符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="诛魔符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九转玄功符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九转玄功符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="九转玄功符石",等级=1}
           end
         end
      end
    end
    --神木林符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天人庇护符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天人庇护符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天人庇护符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神木恩泽符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神木恩泽符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神木恩泽符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="驭灵咒符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="驭灵咒符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="驭灵咒符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="瞬息万变符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="瞬息万变符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="瞬息万变符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万物轮转符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万物轮转符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万物轮转符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="巫咒符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="巫咒符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="巫咒符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万灵诸念符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万灵诸念符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="万灵诸念符石",等级=1}
           end
         end
      end
    end
    --无底洞符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秘影迷踪符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秘影迷踪符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="秘影迷踪符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元神功符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元神功符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="混元神功符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="鬼蛊灵蕴符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="鬼蛊灵蕴符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="鬼蛊灵蕴符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="白色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴风绝章符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴风绝章符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="阴风绝章符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="蓝色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="地冥妙法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="地冥妙法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="地冥妙法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黑色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="燃灯灵宝符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="燃灯灵宝符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="燃灯灵宝符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="枯骨心法符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="枯骨心法符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="枯骨心法符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="蓝色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="飞檐走壁",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="飞檐走壁",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="飞檐走壁",等级=1}
           end
         end
      end
    end
    --女魃墓符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天火献誓符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天火献誓符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天火献誓符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="藻光灵狱符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="藻光灵狱符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="藻光灵狱符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="煌火无明符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="煌火无明符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="煌火无明符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="化神以灵符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="弹指成烬符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="弹指成烬符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="弹指成烬符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罚之焰符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罚之焰符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="天罚之焰符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="白色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="离魂符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="离魂符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="离魂符石",等级=1}
           end
         end
      end
    end
    --天机城符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神工无形符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神工无形符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神工无形符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="千机奇巧符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="千机奇巧符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="千机奇巧符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="匠心不移符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="匠心不移符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="匠心不移符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="蓝色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="攻玉以石符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="攻玉以石符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="攻玉以石符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="紫色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="绿色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="紫色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="红色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="擎天之械符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="擎天之械符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="擎天之械符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="红色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="探奥索隐符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="探奥索隐符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="探奥索隐符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黄色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="运思如电符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="运思如电符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="运思如电符石",等级=1}
           end
         end
      end
    end
    --花果山符石组合
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="紫色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵猴九窍符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵猴九窍符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="灵猴九窍符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黄色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="紫色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="绿色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黑色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚之躯符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚之躯符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="金刚之躯符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="绿色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="白色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七十二变符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七十二变符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="七十二变符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="红色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="绿色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="白色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神通广大符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神通广大符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="神通广大符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="蓝色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黑色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="黄色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="绿色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="如意金箍符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="如意金箍符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="如意金箍符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="红色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="紫色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="白色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="齐天逞胜符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="齐天逞胜符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="齐天逞胜符石",等级=1}
           end
         end
      end
    end
    if 玩家数据[id].道具.数据[临时装备id].符石[1]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[1].颜色=="黑色" then
      if 玩家数据[id].道具.数据[临时装备id].符石[2]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[2].颜色 =="黄色" then
         if 玩家数据[id].道具.数据[临时装备id].符石[3]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[3].颜色 =="黑色" then
           if 玩家数据[id].道具.数据[临时装备id].符石[4]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[4].颜色 =="红色" then
             if 玩家数据[id].道具.数据[临时装备id].符石[5]~=nil and 玩家数据[id].道具.数据[临时装备id].符石[5].颜色 =="黄色" then
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="腾云驾霧符石",等级=3}
             else
               玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="腾云驾霧符石",等级=2}
             end
           else
              玩家数据[id].道具.数据[临时装备id].符石组合={符石组合="腾云驾霧符石",等级=1}
           end
         end
      end
    end
    道具刷新(id)
  else
    常规提示(id,"你想要镶嵌的符石呢")
  end
end
