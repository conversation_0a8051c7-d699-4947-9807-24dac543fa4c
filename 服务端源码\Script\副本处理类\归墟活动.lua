local 归墟活动 = class()

function 归墟活动:初始化()
		self.地图={
			抓鬼任务={1501,1092,1070,1193,1173,1146,1140,1208,1040,1226,1142},
			鬼王任务={1233,1228,1229,1040,1041,1501,1140,1091,1070,1512,1131,1140},
			皇宫飞贼={1001},
			雁塔地宫={1622},
		}
		self.模型={
			抓鬼任务={"僵尸","野鬼","牛头","马面","骷髅怪"},
			鬼王任务={"幽灵","鬼将","夜罗刹","吸血鬼","炎魔神"},
			皇宫飞贼={"护卫","强盗","吸血鬼","夜罗刹","雨师"},
			雁塔地宫={"炎魔神","大力金刚","蔓藤妖花","鬼将","噬天虎","曼珠沙华","巴蛇","葫芦宝贝","混沌兽","画魂","小龙女","巨力神猿","雷鸟人","蚌精","雨师","蝴蝶仙子","天兵","白熊"},
	     }
end





function 归墟活动:任务说明(任务id)
		 local 名称="轮回之墟"
		 local 备注="轮回之墟·"..任务数据[任务id].分类
		 local 说明="#S(轮回之墟·"..任务数据[任务id].分类..")#W前往#Y".. 取地图名称(任务数据[任务id].地图编号).."("..任务数据[任务id].x..","..任务数据[任务id].y..")#W打败#L"..任务数据[任务id].名称.."#W。本次任务难度:"..任务数据[任务id].难度.."星#R(剩余"..取分((任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))).."分钟)"
	 	 return {名称,说明,备注}
end


function 归墟活动:开启任务(id,分类,难度,进程)
	  if 玩家数据[id].队伍==0 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	  elseif 取队伍人数(id)<5 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	  elseif 取等级要求(id,69)==false then
		  	添加最后对话(id,"此活动最少至少要达到69级")
		  	return  false

	  elseif 活动次数查询(id,"归墟活动")==false then
	   		 添加最后对话(id,"队伍中有成员等级已完成该活动。")
	          return false
	  elseif 取队伍任务(玩家数据[id].队伍,398) then
		    添加最后对话(id,"队伍中已有队员领取过此任务了")
		    return false
	  elseif not self.地图[分类] or not self.模型[分类] then
	  		添加最后对话(id,"该任务数据问题,请联系管理员")
		    return false
	 end
	 if not 进程 then 进程 = 1 end
	  local 任务id=取唯一识别码(398)
	  local 地图 = self.地图[分类][取随机数(1,#self.地图[分类])]
	  local 模型 = self.模型[分类][取随机数(1,#self.模型[分类])]
	  local xy=地图处理类.地图坐标[地图]:取随机点()
	  任务数据[任务id]={
	    id=任务id,
	    起始=os.time(),
	    结束=7200,
	    玩家id=id,
	    名称="轮回之墟·"..模型,
        模型=模型,
	    x=xy.x,
	    y=xy.y,
	    地图编号=地图,
	    地图名称=取地图名称(地图),
	    分类=分类,
	    难度=难度,
	    进程=进程,
	    类型=398
	  }
	  if 分类=="皇宫飞贼" then
	        if 任务数据[任务id].模型=="护卫" then
		   			任务数据[任务id].名称 = "轮回之墟·江湖大盗"
			elseif 任务数据[任务id].模型=="强盗" then
					任务数据[任务id].名称 = "轮回之墟·毛贼"
			elseif 任务数据[任务id].模型=="吸血鬼" then
					任务数据[任务id].名称 = "轮回之墟·销赃贼"
			elseif 任务数据[任务id].模型=="夜罗刹" then
					任务数据[任务id].名称 = "轮回之墟·宝贼"
			elseif 任务数据[任务id].模型=="雨师" then
					任务数据[任务id].名称 = "轮回之墟·盗贼首领"
			end
	 elseif 分类=="雁塔地宫" then
	 		任务数据[任务id].名称 = "轮回之墟·地宫首领"
	 elseif 分类=="抓鬼任务" then
	 		任务数据[任务id].名称 = "轮回之墟·小鬼队长"
	 elseif 分类=="鬼王任务" then
	 		任务数据[任务id].名称 = "轮回之墟·鬼王统领"
	 end
	  地图处理类:添加单位(任务id)
	  玩家数据[id].角色:添加任务(任务id,1)
	  if 分类=="雁塔地宫" then
	  	  地图处理类:跳转地图(id,1622,63,34)
	  end
	 return true

end


function 归墟活动:对话内容(id,类型,标识,地图)
		if 玩家数据[id].队伍==0 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	    elseif 取队伍人数(id)<5 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	    elseif 取等级要求(id,69)==false then
		  	添加最后对话(id,"此活动最少至少要达到69级")
		  	return  false
		elseif not 取队员任务一致(id,398,1) then
			return
		end

		local 对话数据={}
		对话数据.模型=任务数据[标识].模型
		对话数据.名称=任务数据[标识].名称
		if 任务数据[标识].战斗 then
			对话数据.对话="正在战斗中，请勿打扰。"
			return 对话数据
		elseif  玩家数据[id].角色:取任务(398)~=标识 then
			对话数据.对话="我认识你么。"
			return 对话数据
		end
		对话数据.对话="是否对"..任务数据[标识].名称.."发起的战斗"
		对话数据.选项={"开启战斗","我点错了"}
		return 对话数据
end



function 归墟活动:对话处理(id,名称,事件,类型,标识)
		if 玩家数据[id].队伍==0 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	    elseif 取队伍人数(id)<5 then
		    添加最后对话(id,"此活动最少需要5人组队参加。")
		    return false
	    elseif 取等级要求(id,69)==false then
		  	添加最后对话(id,"此活动最少至少要达到69级")
		  	return  false
		end
		if 取队员任务一致(id,398,1) and 玩家数据[id].角色:取任务(398)==玩家数据[id].地图单位.标识 and 事件=="开启战斗" then
			战斗准备类:创建战斗(id,100159,玩家数据[id].地图单位.标识)
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			玩家数据[id].地图单位=nil
		end

end





function 归墟活动:战斗胜利(任务id,id组)
	 	if 任务数据[任务id]==nil then
	    	return
	  	end
		local 分类 = 任务数据[任务id].分类
		local 难度 = 任务数据[任务id].难度
		local 进程 = 任务数据[任务id].进程
		for i=1,#id组 do
			local id = id组[i]
			玩家数据[id].角色:自定义银子添加("归墟活动",难度)
     		玩家数据[id].角色:添加活跃积分(难度*2,"归墟活动",1)
     		添加活动次数(id,"归墟活动")
     		if 自定义数据.归墟活动 and 自定义数据.归墟活动[分类] then
				local 获得物品={}
	            for i=1,#自定义数据.归墟活动[分类] do
	                if 取随机数()<=自定义数据.归墟活动[分类][i].概率 then
	                   获得物品[#获得物品+1]=自定义数据.归墟活动[分类][i]
	                end
	            end
	            获得物品=删除重复(获得物品)
	            if 获得物品~=nil then
	               local 取编号=取随机数(1,#获得物品)
	               if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
	                  玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
	                   广播消息({内容=string.format("#S(轮回之墟·%s)#G%s#Y打败了#G%s星#Y难度的#G归墟任务#Y因此获得了奖励#G/%s#Y/",分类,玩家数据[id].角色.数据.名称,难度,获得物品[取编号].名称),频道="xt"})
	                end
	            end
            end
     		玩家数据[id].战斗=0
		 	玩家数据[id].角色:取消任务(任务id)
		end
		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
  		任务数据[任务id]=nil
  		进程 = 进程 +1
  		if 进程<=3 then
  			self:开启任务(id组[1],分类,难度,进程)
  		end
end


return 归墟活动