-- @Author: 作者QQ381990860
-- @Date:   2024-03-23 15:11:00
-- @Last Modified by:   作者QQ381990860
-- @Last Modified time: 2025-05-21 08:06:54
--======================================================================--
local 游戏活动类 = class()

function 游戏活动类:初始化()end
function 游戏活动类:科举条件检测(连接id,id)
  if 科举数据[id]~=nil and 科举数据[id].次数>=2 then
    常规提示(id,"你本日可参加科举活动的次数已经耗尽，请明日再来吧")
    return
  elseif 取等级(id)<30 then
    常规提示(id,"只有等级达到30级的玩家才可参加科举活动")
    return
  end
  if 科举数据[id]==nil then
    科举数据[id]={次数=0}
  end
  任务处理类:设置科举任务(id)
  科举数据[id].次数=科举数据[id].次数+1
  科举数据[id].天王令=1
  科举数据[id].地王令=2
  科举数据[id].鬼王令=3
  科举数据[id].答对=0
  科举数据[id].答错=0
  科举数据[id].总数=0
  科举数据[id].连对=0
  科举数据[id].起始=os.time()
  self:设置科举题目(连接id,id)
end

function 游戏活动类:设置科举题目(连接id,id)
  local 序列=取随机数(1,#科举题库)
  local 正确答案=科举题库[序列][4]
  local 随机答案={}
  for n=2,4 do
    随机答案[n-1]={答案=科举题库[序列][n],序列=取随机数(1,9999)}
  end
  table.sort(随机答案,function(a,b) return a.序列>b.序列 end )
  local 显示答案={}
  for n=1,3 do
    显示答案[n]=随机答案[n].答案
  end
  显示答案[#显示答案+1]="使用法宝"
  玩家数据[id].科举数据={题目=科举题库[序列][1],答案=显示答案,正确答案=正确答案}
  玩家数据[id].科举对话=true
  科举数据[id].计时=os.time()
  科举数据[id].总数=科举数据[id].总数+1
  发送数据(连接id,1501,{名称="礼部侍郎",模型="考官2",对话=format("#Y/第%s题：#W/%s",科举数据[id].总数, 玩家数据[id].科举数据.题目),选项= 玩家数据[id].科举数据.答案})
end

function 游戏活动类:科举回答题目(连接id,id,答案,类型) --1天王令 2 帝王令 3使用鬼王令 4鬼王令成功 5 鬼王令失败 6超时
	-- print(连接id,id,答案,类型,玩家数据[id].科举数据.法宝使用)
  if 答案=="使用法宝" then --发送法宝界面
      玩家数据[id].科举数据.题目="请选择你要使用的法宝："
      玩家数据[id].科举数据.答案={}
      玩家数据[id].科举数据.答案[1]="天王令(得到正确作答，还可用"..科举数据[id].天王令.."次)"
      玩家数据[id].科举数据.答案[2]="地王令(系统作答，正确率50%,还可用"..科举数据[id].地王令.."次)"
      玩家数据[id].科举数据.答案[3]="鬼王令(战斗闯关，还可用"..科举数据[id].鬼王令.."次)"
      发送数据(连接id,1501,{名称="礼部侍郎",模型="考官2",对话=玩家数据[id].科举数据.题目,选项= 玩家数据[id].科举数据.答案})
      玩家数据[id].科举数据.法宝使用=true
      return
  end
  if 玩家数据[id].科举数据.法宝使用 then
    local 法宝序列=0
    for n=1,3 do
      if 答案==玩家数据[id].科举数据.答案[n] then
        法宝序列=n
      end
    end
    if 法宝序列==0 then
      常规提示(id,"#Y/你没有这样的法宝")
      类型=9
    elseif 法宝序列==1 and 科举数据[id].天王令<=0 then
      常规提示(id,"#Y/你的这种法宝可用次数已经耗尽")
      类型=9
    elseif 法宝序列==2 and 科举数据[id].地王令<=0 then
      常规提示(id,"#Y/你的这种法宝可用次数已经耗尽")
      类型=9
    elseif 法宝序列==3 and 科举数据[id].鬼王令<=0 then
      常规提示(id,"#Y/你的这种法宝可用次数已经耗尽")
      类型=9
    else
      类型=法宝序列
      if 类型==3 then
        科举数据[id].鬼王令=科举数据[id].鬼王令-1
        常规提示(id,"#Y/你使用了鬼王令")
        玩家数据[id].科举数据.法宝使用=nil
        战斗准备类:创建战斗(id,100006,0)
        return
      end
    end
  end
  玩家数据[id].科举数据.法宝使用=nil
  local 正确=false
  玩家数据[id].科举对话=nil
  if 玩家数据[id].角色:取任务(7)==0 and 类型==nil then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
  if 类型==nil then --正常作答
    if os.time()-科举数据[id].计时<=15 then
      if 答案==玩家数据[id].科举数据.正确答案 then
        正确=true
      end
    else
      常规提示(id,"#Y/答题超时！")
    end
  elseif 类型==1 then
    正确=true
    科举数据[id].天王令=科举数据[id].天王令-1
    常规提示(id,"#Y/你使用了天王令")
  elseif 类型==2 then
    if 取随机数()<=50 then 正确=true  end
    科举数据[id].地王令=科举数据[id].地王令-1
    常规提示(id,"#Y/你使用了地王令")
  elseif 类型==4 then
    正确=true
  end
  if 正确 then
    科举数据[id].答对=科举数据[id].答对+1
    科举数据[id].连对= 科举数据[id].连对+1
    if 科举数据[id].连对>=3 then
      科举数据[id].连对=0
      科举数据[id].鬼王令=科举数据[id].鬼王令+1
      常规提示(id,"#Y/连续答对三道题#R/鬼王令+1")
    end
    local 等级=取等级(id)
    local 经验=等级*20*2
    local 银子=等级*15*2
    玩家数据[id].角色:添加经验(经验,"科举活动",1)
    玩家数据[id].角色:添加银子(银子,"科举活动",1)
    local 奖励参数=取随机数(1,500)
    if 类型~=nil then
      奖励参数=0
    end
    if 奖励参数<=20 then
      玩家数据[id].道具:给予道具(id,"藏宝图",0)
      常规提示(id,"#Y/你获得了一张藏宝图")
    elseif 奖励参数<=30 then
      local 名称=取强化石()
      玩家数据[id].道具:给予道具(id,名称,取随机数(5,10))
      常规提示(id,"#Y/你获得了"..名称)
      广播消息({内容=format("#S(科举活动)#R/%s#Y在科举活动中如有神助，妙笔生花，获得了考官奖励的#G/%s",玩家数据[id].角色.数据.名称,名称),频道="xt"})
    elseif 奖励参数<=35 then
      local 名称="魔兽要诀"
      玩家数据[id].道具:给予道具(id,名称,1)
      常规提示(id,"#Y/你获得了"..名称)
      广播消息({内容=format("#S(科举活动)#R/%s#Y在科举活动中如有神助，妙笔生花，获得了考官奖励的#G/%s",玩家数据[id].角色.数据.名称,名称),频道="xt"})
    elseif 奖励参数<=50 then
      local 名称="高级藏宝图"
      玩家数据[id].道具:给予道具(id,名称,1)
      常规提示(id,"#Y/你获得了"..名称)
      广播消息({内容=format("#S(科举活动)#R/%s#Y在科举活动中如有神助，妙笔生花，获得了考官奖励的#G/%s",玩家数据[id].角色.数据.名称,名称),频道="xt"})
    elseif 奖励参数<=60 then
      local 名称="金银锦盒"
      玩家数据[id].道具:给予道具(id,名称,取随机数(1,5))
      常规提示(id,"#Y/你获得了"..名称)
    end
  else
    科举数据[id].答错=科举数据[id].答错+1
    科举数据[id].连对=0
    常规提示(id,"#Y/你的回答不正确")
  end
  if 科举数据[id].总数>=20 then
    科举数据[id].耗时=os.time()- 科举数据[id].起始
    if 科举数据[id].答对>=15 then
      local 等级=取等级(id)
      local 经验=等级*24*科举数据[id].答对
      local 储备=等级*10*科举数据[id].答对
      玩家数据[id].角色:添加经验(经验,"科举活动最终奖励",1)
      玩家数据[id].角色:添加银子(储备,"科举活动最终奖励",1)
      local 名称="高级魔兽要诀"
      玩家数据[id].道具:给予道具(id,名称,1)
      常规提示(id,"#Y/你获得了"..名称)
      广播消息({内容=format("#S(科举活动)#R/%s#Y在科举活动的表现突出，仅用#W/%s#Y秒的时间就正确作答#W/%s#Y/道题目，因此获得了考官特别奖励的#G/%s",玩家数据[id].角色.数据.名称,科举数据[id].耗时,科举数据[id].答对,"高级藏宝图"),频道="xt"})
  end
    玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(7))
    常规提示(id,"#Y/恭喜你成功完成了本轮科举活动")
    玩家数据[id].科举数据=nil


  elseif 科举数据[id].答错>=10 then
    科举数据[id].今日答题 = true
    玩家数据[id].角色:取消任务(玩家数据[id].角色:取任务(7))
    常规提示(id,"#Y/你当前累积错误题目数量已达10道，已经无法参加后续的活动了")
    玩家数据[id].科举数据=nil

  else
    self:设置科举题目(连接id,id)
  end
end

function 游戏活动类:首席争霸战斗处理(胜利id,失败id)
  -- --失败
  -- local 门派=""
  -- self.战胜人数=1
  -- 门派=玩家数据[失败id].角色.数据.门派
  -- 首席争霸数据[门派][失败id].奖励=true
  -- 玩家数据[失败id].战斗=0
  -- 常规提示(失败id,"等待首席争霸结束后可在首席争霸使者处领取奖励")
  -- 地图处理类:跳转地图(失败id,1001,313,85)
  -- --胜利
  -- self.添加积分=self.战胜人数*5
  -- 门派=玩家数据[胜利id].角色.数据.门派
  -- 首席争霸数据[门派][胜利id].奖励=true
  -- 首席争霸数据[门派][胜利id].连胜次数=首席争霸数据[门派][胜利id].连胜次数+1
  -- if 首席争霸数据[门派][胜利id].连胜次数==5 then
  --   self.添加积分=self.添加积分+5
  --   发送公告("#G无人可挡，玩家#R"..玩家数据[胜利id].角色.数据.名称.."#G在首席争霸赛中初获五连斩！！！")
  -- elseif 首席争霸数据[门派][胜利id].连胜次数==10 then
  --   self.添加积分=self.添加积分+10
  --   发送公告("#G无人可挡，玩家#R"..玩家数据[胜利id].角色.数据.名称.."#G在首席争霸赛中十步杀一人，已获十连斩！！！")
  -- elseif 首席争霸数据[门派][胜利id].连胜次数==20 then
  --   self.添加积分=self.添加积分+20
  --   发送公告("#G无人可挡，玩家#R"..玩家数据[胜利id].角色.数据.名称.."#G在首席争霸赛中杀人如麻，已获二十连斩！！！")
  -- elseif 首席争霸数据[门派][胜利id].连胜次数==30 then
  --   self.添加积分=self.添加积分+30
  --   发送公告("#G无人可挡，玩家#R"..玩家数据[胜利id].角色.数据.名称.."#G在首席争霸赛中遇神杀神、遇魔杀魔，已获三十连战斩！！！")
  -- end
  -- 首席争霸数据[门派][胜利id].积分=首席争霸数据[门派][胜利id].积分+self.添加积分
  -- 常规提示(胜利id,"你获得了"..self.添加积分.."点首席争霸积分，当前总积分"..首席争霸数据[门派][胜利id].积分.."点")
end

function 游戏活动类:车迟回答题目(id,答案,任务id)
  local 正确=false
  玩家数据[id].车迟对话=nil
  if 玩家数据[id].角色:取任务(130)==0 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
  if 答案==玩家数据[id].车迟数据.正确答案 then
    正确=true
  end
  if 正确 then
    玩家数据[id].道具:给予道具(id,"普通木材",1)
    地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
    常规提示(id,"#Y/你获得了一个木材")
    任务数据[任务id]=nil
  else
     常规提示(id,"#Y/回答错误")
  end
end

function 游戏活动类:通天回答题目(id,答案,任务id)
  local 正确=false
  玩家数据[id].通天对话=nil
  if 玩家数据[id].角色:取任务(160)==0 then
    常规提示(id,"#Y/你没有这样的任务")
    return
  end
  if 答案==玩家数据[id].通天数据.正确答案 then
    正确=true
  end
  if 正确 == false then
    常规提示(id,"#Y/回答错误，叔叔小孩提出的问题你还能回答错误吗？可笑可笑！")
  end

  if 正确 then
    地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
    local 副本id = 任务数据[玩家数据[id].角色:取任务(160)].副本id
    副本数据.通天河.进行[副本id].通天答题=副本数据.通天河.进行[副本id].通天答题+1
    常规提示(id,"#Y/不错不错，答对了")
    任务数据[任务id]=nil
    if 副本数据.通天河.进行[副本id].通天答题>=5 then
      副本数据.通天河.进行[副本id].进程=2
      任务处理类:设置通天河副本(副本id)
    end

    for n=1,#队伍数据[玩家数据[id].队伍].成员数据 do
      玩家数据[队伍数据[玩家数据[id].队伍].成员数据[n]].角色:刷新任务跟踪()
    end
  end
end

-- function 游戏活动类:炼丹更新()

--   if 炼丹炉.时间 <= 0 then
--     self.开奖数据 = self:炼丹开奖中()
--   end
--   炼丹炉.时间 = 炼丹炉.时间 - 1
--   if 炼丹炉.时间 == 121 then
--     self:炼丹开奖(self.开奖数据)
--   end
--   for i,v in pairs(炼丹查看) do
--     if 玩家数据[i] ~= nil and 炼丹炉.时间 ~= nil then
--       发送数据(玩家数据[i].连接id,109,炼丹炉.时间)
--     else
--       炼丹查看[i] = nil
--     end
--   end
-- end

-- function 游戏活动类:炼丹开奖中()
--   local 八卦 = {"乾","巽","坎","艮","坤","震","离","兑"}
--   local 开奖 = {吉位=八卦[取随机数(1,#八卦)]}
--   开奖.八卦位 = 八卦[取随机数(1,#八卦)]
--   if 开奖.八卦位 == 开奖.吉位 then
--     开奖.八卦位 = 八卦[取随机数(1,#八卦)]
--   end
--   for i,v in pairs(炼丹查看) do
--     if 玩家数据[i] ~= nil then
--       发送数据(玩家数据[i].连接id,108.2,{吉位=开奖.吉位,八卦位=开奖.八卦位})
--     else
--       炼丹查看[i] = nil
--     end
--   end
--   炼丹炉.时间 = 130
--   return 开奖
-- end

-- function 游戏活动类:炼丹开奖(开奖)
--   if 开奖==nil then return  end
--   广播消息({内容="#S(八卦炼丹)#Y本次八卦炉炼丹结束,本次炼丹吉位为:#G"..开奖.吉位.." #Y本次炼丹八卦位为:#G"..开奖.八卦位,频道="hd"})
--   for i,v in pairs(炼丹炉) do
--     if i ~= "时间" then
--       if 炼丹炉[i][开奖.吉位] ~= nil and 炼丹炉[i][开奖.八卦位] ~= nil and 开奖.吉位==开奖.八卦位 then
--         local 奖励数据 = {}
--         奖励数据[1],奖励数据[2],奖励数据[3] = self:取奖励(炼丹炉[i][开奖.吉位])
--         if 玩家数据[i] ~= nil then
--           if 奖励数据[1] > 0 then
--             玩家数据[i].道具:给予道具(i,"金砂丹",奖励数据[1])
--           end
--           if 奖励数据[2] > 0 then
--             玩家数据[i].道具:给予道具(i,"银砂丹",奖励数据[2])
--           end
--           if 奖励数据[3] > 0 then
--             玩家数据[i].道具:给予道具(i,"铜砂丹",奖励数据[3])
--           end
--           常规提示(i,"#Y/恭喜你在炼丹中中得吉位和八卦位！")
--           广播消息({内容=string.format("#S(八卦炼丹)#Y恭喜:#G%s#Y在本次炼丹中幸运的猜中吉位以及八卦位获得了:#G%s颗#Y金丹 #G%s颗#Y银丹 #G%s颗#Y铜丹",玩家数据[i].角色.数据.名称,奖励数据[1],奖励数据[2],奖励数据[3]),频道="hd"})
--         else
--           if 商品存放[i] == nil then
--             商品存放[i] = {[1]=奖励数据[1],[2]=奖励数据[2],[3]=奖励数据[3]}
--           else
--             商品存放[i] = {}
--             商品存放[i][1] = 商品存放[i][1] + 奖励数据[1]
--             商品存放[i][2] = 商品存放[i][2] + 奖励数据[2]
--             商品存放[i][3] = 商品存放[i][3] + 奖励数据[3]
--           end
--         end
--       elseif 炼丹炉[i][开奖.吉位] ~= nil then
--         local 奖励数据 = {}
--         奖励数据[1],奖励数据[2],奖励数据[3] = self:取奖励(炼丹炉[i][开奖.吉位]/2)
--         奖励数据[1] = math.floor(奖励数据[1])
--         奖励数据[2] = math.floor(奖励数据[2])
--         奖励数据[3] = math.floor(奖励数据[3])
--         if 玩家数据[i] ~= nil then
--           if 奖励数据[1] > 0 then
--             玩家数据[i].道具:给予道具(i,"金砂丹",奖励数据[1])
--           end
--           if 奖励数据[2] > 0 then
--             玩家数据[i].道具:给予道具(i,"银砂丹",奖励数据[2])
--           end
--           if 奖励数据[3] > 0 then
--             玩家数据[i].道具:给予道具(i,"铜砂丹",奖励数据[3])
--           end
--           常规提示(i,"#Y/恭喜你在炼丹中中得吉位！")
--           广播消息({内容=string.format("#S(八卦炼丹)#Y恭喜:#G%s#Y在本次炼丹中幸运的猜中吉位获得了:#G%s颗#Y金丹 #G%s颗#Y银丹 #G%s颗#Y铜丹",玩家数据[i].角色.数据.名称,奖励数据[1],奖励数据[2],奖励数据[3]),频道="hd"})
--         else
--           if 商品存放[i] == nil then
--             商品存放[i] = {[1]=奖励数据[1],[2]=奖励数据[2],[3]=奖励数据[3]}
--           else
--             商品存放[i] = {}
--             商品存放[i][1] = 商品存放[i][1] + 奖励数据[1]
--             商品存放[i][2] = 商品存放[i][2] + 奖励数据[2]
--             商品存放[i][3] = 商品存放[i][3] + 奖励数据[3]
--           end
--         end
--       end
--     end
--   end
--   炼丹炉={时间 = 120}
--   for i,v in pairs(炼丹查看) do
--     if 玩家数据[i] ~= nil then
--       发送数据(玩家数据[i].连接id,108.1,{数据=炼丹炉[i],灵气=玩家数据[i].角色.数据.炼丹灵气})
--     else
--       炼丹查看[i] = nil
--     end
--   end
-- end

function 游戏活动类:炼丹更新()
  if 炼丹炉.下注时间>0 then
     炼丹炉.下注时间 = 炼丹炉.下注时间 - 1
     if 炼丹炉.下注时间<=0 then
        炼丹炉.下注时间 = 0
     end
  end
  if  炼丹炉.下注时间<=0 and 炼丹炉.转盘时间>0 then
      炼丹炉.转盘时间=炼丹炉.转盘时间-1
     if 炼丹炉.转盘时间==2 then
        self.开奖数据 = self:炼丹开奖中()
     elseif  炼丹炉.转盘时间<=0 then
         炼丹炉.转盘时间=0
     end
  end
  if 炼丹炉.转盘时间<=0 and  炼丹炉.停止时间>0 then
      炼丹炉.停止时间 = 炼丹炉.停止时间 - 1
      if 炼丹炉.停止时间==5 then
          self:炼丹开奖(self.开奖数据)
      elseif 炼丹炉.停止时间<=0 then
         炼丹炉={下注时间=120,转盘时间 = 15,停止时间=6}
           for i,v in pairs(炼丹查看) do
              if 玩家数据[i] ~= nil then
                发送数据(玩家数据[i].连接id,108.1,{数据=炼丹炉[i],灵气=玩家数据[i].角色.数据.炼丹灵气,物品价格=自定义数据.炼丹炉})
              else
                炼丹查看[i] = nil
              end
          end
      end
  end

   local 临时炼丹 = DeepCopy(炼丹炉)
    临时炼丹.开奖控制=nil
    for i,v in pairs(炼丹查看) do
      if 玩家数据[i] ~= nil and 临时炼丹 ~= nil then
        发送数据(玩家数据[i].连接id,109,临时炼丹)
      else
        炼丹查看[i] = nil
      end
    end

end


function 游戏活动类:炼丹下注(内容)
  local 八卦 = {"乾","巽","坎","艮","坤","震","离","兑"}
  local id =内容.数字id
  local 序号 = 内容.编号

  local  物品格子 =tonumber(内容.物品)
   if not  物品格子 or 物品格子==0 then
      return
  end
  local  道具id = 玩家数据[id].角色.数据.道具[物品格子]
  if not  道具id or 道具id==0 then
    return
  end

  if not 玩家数据[id].道具.数据[道具id] then return end

  if not 自定义数据.炼丹炉[玩家数据[id].道具.数据[道具id].名称] then return end
  local 数额 =tonumber(自定义数据.炼丹炉[玩家数据[id].道具.数据[道具id].名称])
  -- local 数额 = tonumber(内容.数额)
  -- if not 数额 then
  --     数额=0
  --  end

  if 炼丹炉.下注时间 <= 5 then
    常规提示(id,"#Y/即将开始炼丹,停止灌注灵气！")
    return
  elseif 炼丹炉.转盘时间<=0 then
    常规提示(id,"#Y/炼丹才刚刚结束,请稍等再下注！")
    return
  -- elseif 玩家数据[id].角色.数据.炼丹灵气 < 数额 then
  --   常规提示(id,"#Y/你没有这么多的灵气用于灌注！")
  --   return
  -- elseif 数额 < 10 then
  --   常规提示(id,"#Y/炼丹炉单次灌注灵气不得少于10点！")
  --   return
  else
    if 炼丹炉[id] == nil then
      炼丹炉[id]={}
    end
    if 炼丹炉[id][八卦[序号]] ~= nil then
        炼丹炉[id][八卦[序号]] = 炼丹炉[id][八卦[序号]] + 数额
    else
        炼丹炉[id][八卦[序号]] = 数额
    end
    玩家数据[id].角色.数据.炼丹灵气 = 玩家数据[id].角色.数据.炼丹灵气 - 数额
    if 玩家数据[id].道具.数据[道具id].数量 then
       玩家数据[id].道具.数据[道具id].数量=玩家数据[id].道具.数据[道具id].数量-1
       if 玩家数据[id].道具.数据[道具id].数量<=0 then
          玩家数据[id].道具.数据[道具id]=nil
          玩家数据[id].角色.数据.道具[物品格子]=nil
       end
    else
        玩家数据[id].道具.数据[道具id]=nil
        玩家数据[id].角色.数据.道具[物品格子]=nil
    end
    道具刷新(id)
    常规提示(id,"#Y/灌注灵气成功,请等候丹炉炼丹吧！")
    local 临时炼丹 = DeepCopy(炼丹炉)
    临时炼丹.开奖控制=nil
    发送数据(玩家数据[id].连接id,108.1,{数据=临时炼丹[id],灵气=玩家数据[id].角色.数据.炼丹灵气,物品价格=自定义数据.炼丹炉,物品数据=玩家数据[id].道具:索要道具1(id)})
  end
end
-- function 游戏活动类:炼丹更新()

--   if 炼丹炉.时间 <= 0 then
--     self.开奖数据 = self:炼丹开奖中()
--   end
--   炼丹炉.时间 = 炼丹炉.时间 - 1
--   if 炼丹炉.时间 == 90 then
--     self:炼丹开奖(self.开奖数据)
--   end
--   for i,v in pairs(炼丹查看) do
--     if 玩家数据[i] ~= nil and 炼丹炉.时间 ~= nil then
--       发送数据(玩家数据[i].连接id,109,炼丹炉.时间)
--     else
--       炼丹查看[i] = nil
--     end
--   end
--end

function 游戏活动类:炼丹开奖中()
  local 八卦 = {"乾","巽","坎","艮","坤","震","离","兑"}
  local 开奖 = 八卦[取随机数(1,#八卦)]
  if 炼丹炉 and 炼丹炉.开奖控制 then
      开奖= 炼丹炉.开奖控制
  end
  for i,v in pairs(炼丹查看) do
    if 玩家数据[i] ~= nil then
        发送数据(玩家数据[i].连接id,108.2,{吉位=开奖})
    else
       炼丹查看[i] = nil
    end
  end
  return 开奖
end

function 游戏活动类:炼丹开奖(开奖)
  if not 开奖 then
     开奖 = self:炼丹开奖中()
  end
  if 开奖 then
    广播消息({内容="#S(八卦炼丹)#Y本次八卦炉炼丹结束,#Y本次炼丹八卦位为:#G"..开奖,频道="hd"})
    for i,v in pairs(炼丹炉) do
      if i ~= "下注时间" and  i ~= "转盘时间" and  i ~= "停止时间" and  i ~= "开奖控制"  then
        if 炼丹炉[i][开奖] ~= nil then
          local 奖励数据 = {}
          奖励数据[1],奖励数据[2],奖励数据[3] = self:取奖励(炼丹炉[i][开奖])
          if 玩家数据[i] ~= nil then
            if 奖励数据[1] > 0 then
              玩家数据[i].道具:给予道具(i,"金砂丹",奖励数据[1])
            end
            if 奖励数据[2] > 0 then
              玩家数据[i].道具:给予道具(i,"银砂丹",奖励数据[2])
            end
            if 奖励数据[3] > 0 then
              玩家数据[i].道具:给予道具(i,"铜砂丹",奖励数据[3])
            end
            常规提示(i,"#Y/恭喜你在炼丹中中得八卦位！")
            广播消息({内容=string.format("#S(八卦炼丹)#Y恭喜:#G%s#Y在本次炼丹中幸运的猜中八卦位获得了:#G%s颗#Y金丹 #G%s颗#Y银丹 #G%s颗#Y铜丹",玩家数据[i].角色.数据.名称,奖励数据[1],奖励数据[2],奖励数据[3]),频道="hd"})
          else
            if 商品存放[i] == nil then
              商品存放[i] = {[1]=奖励数据[1],[2]=奖励数据[2],[3]=奖励数据[3]}
            else
              商品存放[i] = {}
              商品存放[i][1] = 商品存放[i][1] + 奖励数据[1]
              商品存放[i][2] = 商品存放[i][2] + 奖励数据[2]
              商品存放[i][3] = 商品存放[i][3] + 奖励数据[3]
            end
          end
        end
      end
    end
  end
 -- 炼丹炉={时间 = 90}

end

function 游戏活动类:取奖励(信息)
  local 数组 = {[1]=0,[2]=0,[3]=0}
  if math.floor(信息/1000) > 0 then
    数组[1] = math.floor(信息/1000)
    信息 = 信息 - 数组[1]*1000
    数组[2] = math.floor(信息/100)
    信息 = 信息 - 数组[2]*100
    数组[3] = math.floor(信息/10)
  elseif math.floor(信息/100) > 0 then
    数组[2] = math.floor(信息/100)
    信息 = 信息 - 数组[2]*100
    数组[3] = math.floor(信息/10)
  elseif math.floor(信息/10) > 0 then
    数组[3] = math.floor(信息/10)
  end
  return 数组[1],数组[2],数组[3]
end

-- function 游戏活动类:炼丹下注(内容)
--   local 八卦 = {"乾","巽","坎","艮","坤","震","离","兑"}
--   local id =内容.数字id
--   local 序号 = 内容.编号
--   local 数额 = 内容.数额

--   if 炼丹炉.时间 <= 5 then
--     常规提示(id,"#Y/即将开始炼丹,停止灌注灵气！")
--     return
--   elseif 炼丹炉.时间 >= 115 then
--     常规提示(id,"#Y/炼丹才刚刚结束,请稍等再下注！")
--     return
--   elseif 玩家数据[id].角色.数据.炼丹灵气 < 数额 then
--     常规提示(id,"#Y/你没有这么多的灵气用于灌注！")
--     return
--   elseif 数额 < 10 then
--     常规提示(id,"#Y/炼丹炉单次灌注灵气不得少于10点！")
--     return
--   else
--     if 炼丹炉[id] == nil then
--       炼丹炉[id]={}
--       炼丹炉[id][八卦[序号]] = 数额
--     else
--       if 炼丹炉[id][八卦[序号]] ~= nil then
--         炼丹炉[id][八卦[序号]] = 炼丹炉[id][八卦[序号]] + 数额
--       else
--         炼丹炉[id][八卦[序号]] = 数额
--       end
--     end
--     玩家数据[id].角色.数据.炼丹灵气 = 玩家数据[id].角色.数据.炼丹灵气 - 数额
--     常规提示(id,"#Y/灌注灵气成功,请等候丹炉炼丹吧！")
--     发送数据(玩家数据[id].连接id,108.1,{数据=炼丹炉[id],灵气=玩家数据[id].角色.数据.炼丹灵气})
--   end
-- end

function 游戏活动类:开启剑会天下()
    剑会开关 = true
    广播消息({内容="#Y剑会天下活动已经开启#24,#Y/参加的玩家可以通过系统左上角PK按钮选择模式进行匹配战斗！",频道="xt"})
end

function 游戏活动类:关闭剑会天下()
    剑会开关 = false
    for n,v in pairs(剑会天下.单人) do
        if 玩家数据[v.id]~=nil then
            发送数据(玩家数据[v.id].连接id,128)
            常规提示(v.id,"#Y/剑会天下活动已经结束了！")
        end
    end
    剑会天下.单人={}
    for n,v in pairs(剑会天下.三人) do
        if 玩家数据[v.id].队伍~=0 then
            for i=1,#队伍数据[玩家数据[v.id].队伍].成员数据 do
                if 玩家数据[队伍数据[玩家数据[v.id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[v.id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[v.id].队伍].成员数据[i],"#Y/剑会天下活动已经结束了！")
                end
            end
        end
    end
    剑会天下.三人={}
      for n,v in pairs(剑会天下.五人) do
        if 玩家数据[v.id].队伍~=0 then
            for i=1,#队伍数据[玩家数据[v.id].队伍].成员数据 do
                if 玩家数据[队伍数据[玩家数据[v.id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[v.id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[v.id].队伍].成员数据[i],"#Y/剑会天下活动已经结束了！")
                end
            end
        end
    end
    剑会天下.五人={}
    广播消息({内容="#Y剑会天下活动结束啦#24,#Y/无法在进行匹配战斗了哦!",频道="xt"})
end

function 游戏活动类:剑会天下积分差异(胜利id,失败id)
    --计算分差值按照每相差100分 做递增递减
    local 胜利组平均分 = 0
    local 失败组平均分 = 0
    local 胜利基础分 = 40
    local 失败基础分 = 12
    local 分差值 = 0
    if 胜利id~=0 then
        if 玩家数据[胜利id].队伍~=0 then
            local 队伍id=玩家数据[胜利id].队伍
            for n=1,#队伍数据[队伍id].成员数据 do
                local 队员id=队伍数据[队伍id].成员数据[n]
                胜利组平均分 = 胜利组平均分 + 剑会天下[队员id].当前积分
            end
            胜利组平均分 = math.floor(胜利组平均分 / #队伍数据[队伍id].成员数据)
        else
            胜利组平均分=剑会天下[胜利id].当前积分
        end
    end
    if 失败id~=0 then
        if 玩家数据[失败id].队伍~=0 then
            local 队伍id=玩家数据[失败id].队伍
            for n=1,#队伍数据[队伍id].成员数据 do
                local 队员id=队伍数据[队伍id].成员数据[n]
                失败组平均分 = 失败组平均分 + 剑会天下[队员id].当前积分
            end
            失败组平均分 = math.floor(失败组平均分 / #队伍数据[队伍id].成员数据)
        else
            失败组平均分=剑会天下[失败id].当前积分
        end
    end
    if 胜利id == 0 then
        胜利组平均分 = 失败组平均分
    end
    if 失败id == 0 then
        失败组平均分 = 胜利组平均分
    end
    分差值 = 胜利组平均分 - 失败组平均分
    if 分差值<0 then  --低分打赢高分的情况下
        if math.abs(分差值) >100 then
            local 增加值 = math.floor(math.abs(分差值)/100) * 2
            if 增加值 >= 10 then
                增加值 = 10
            end
            胜利基础分 = 胜利基础分 + 增加值
            失败基础分 = 失败基础分 + 增加值
        end
    else  --高分打赢低分的情况下
        if math.abs(分差值) >100 then
            local 增加值 = math.floor(math.abs(分差值)/100) * 2
            if 增加值 >= 10 then
                增加值 = 10
            end
            胜利基础分 = 胜利基础分 - 增加值
            失败基础分 = 失败基础分 - 增加值
        end
    end
    return {胜利基础分,失败基础分}
end

function 游戏活动类:剑会天下奖励结算(id)
    if 剑会天下.次数[id] == nil then
        剑会天下.次数[id] = {次数=0,首胜=false}
    end
    if 剑会天下.次数[id].次数 <= 2 then
          玩家数据[id].角色:自定义银子添加("剑会前三场",1)
          玩家数据[id].角色:添加活跃积分(5,"剑会前三场",1)
          local 获得物品={}
          for i=1,#自定义数据.剑会前三场 do
                    if 取随机数()<=自定义数据.剑会前三场[i].概率 then
                          获得物品[#获得物品+1]=自定义数据.剑会前三场[i]
                    end
          end
          获得物品=删除重复(获得物品)
          if 获得物品~=nil then
              local 取编号=取随机数(1,#获得物品)
              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                  玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                  广播消息({内容=format("#S/(剑会天下)#R/%s#Y/积极参加活动！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
              end
          end
         剑会天下.次数[id].次数 = 剑会天下.次数[id].次数 + 1
    end
    if 剑会天下.次数[id].首胜 == false then
        玩家数据[id].角色:自定义银子添加("剑会首胜",1)
        玩家数据[id].角色:添加活跃积分(2,"剑会首胜",1)
        local 获得物品={}
        for i=1,#自定义数据.剑会首胜 do
            if 取随机数()<=自定义数据.剑会首胜[i].概率 then
                获得物品[#获得物品+1]=自定义数据.剑会首胜[i]
            end
        end
        获得物品=删除重复(获得物品)
        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S/(剑会天下)#R/%s#Y/获得了首胜！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
            end
        end
        剑会天下.次数[id].首胜 = true
        --每天首胜发放奖励
    end
    --连胜增加积分
    if 剑会天下[id].连胜 == 3 then
        玩家数据[id].角色:自定义银子添加("剑会三连胜",1)
        玩家数据[id].角色:添加活跃积分(2,"剑会三连胜",1)
        local 获得物品={}
        for i=1,#自定义数据.剑会三连胜 do
            if 取随机数()<=自定义数据.剑会三连胜[i].概率 then
                获得物品[#获得物品+1]=自定义数据.剑会三连胜[i]
            end
        end
        获得物品=删除重复(获得物品)
        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                广播消息({内容=format("#S/(剑会天下)#R/%s#Y/在活动中连胜三场！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
            end
        end
    elseif 剑会天下[id].连胜 == 6 then
            玩家数据[id].角色:自定义银子添加("剑会六连胜",1)
            玩家数据[id].角色:添加活跃积分(5,"剑会六连胜",1)
            local 获得物品={}
            for i=1,#自定义数据.剑会六连胜 do
                if 取随机数()<=自定义数据.剑会六连胜[i].概率 then
                    获得物品[#获得物品+1]=自定义数据.剑会六连胜[i]
                end
            end
            获得物品=删除重复(获得物品)
            if 获得物品~=nil then
                local 取编号=取随机数(1,#获得物品)
                if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                    玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                    广播消息({内容=format("#S/(剑会天下)#R/%s#Y/在活动中连胜六场！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                end
            end
    elseif 剑会天下[id].连胜 == 9 then
            剑会天下[id].连胜=0
            玩家数据[id].角色:自定义银子添加("剑会九连胜",1)
            玩家数据[id].角色:添加活跃积分(10,"剑会九连胜",1)
            local 获得物品={}
            for i=1,#自定义数据.剑会九连胜 do
                if 取随机数()<=自定义数据.剑会九连胜[i].概率 then
                    获得物品[#获得物品+1]=自定义数据.剑会九连胜[i]
                end
            end
            获得物品=删除重复(获得物品)
            if 获得物品~=nil then
                local 取编号=取随机数(1,#获得物品)
                if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                    玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                    广播消息({内容=format("#S/(剑会天下)#R/%s#Y/在活动中连胜九场！获得了#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                end
            end
    end
end


function 游戏活动类:剑会天下结算处理(胜利id,失败id)
  -- print(胜利id,失败id)
    --胜利方 结算奖励
    local 分差值 = self:剑会天下积分差异(胜利id,失败id)
    if 胜利id~=0 and 剑会天下[胜利id]~=nil then
        if 玩家数据[胜利id].队伍~=0 then
            local 队伍id=玩家数据[胜利id].队伍
            for n=1,#队伍数据[队伍id].成员数据 do
                local 队员id=队伍数据[队伍id].成员数据[n]
                剑会天下[队员id].连胜=剑会天下[队员id].连胜+1
                剑会天下[队员id].当前积分=剑会天下[队员id].当前积分+分差值[1]
                local 临时数据 = 剑会天下[队员id]
                发送数据(玩家数据[队员id].连接id,129,临时数据)
                self:剑会天下奖励结算(队员id)
                玩家数据[队员id].角色:添加活跃积分(2,"剑会胜利",1)
            end
        else
              剑会天下[胜利id].连胜=剑会天下[胜利id].连胜+1
              剑会天下[胜利id].当前积分=剑会天下[胜利id].当前积分+分差值[1]
              local 临时数据 = 剑会天下[胜利id]
              发送数据(玩家数据[胜利id].连接id,129,临时数据)
              self:剑会天下奖励结算(胜利id)
              玩家数据[胜利id].角色:添加活跃积分(2,"剑会胜利",1)
        end
    end
    --失败方 结算奖励
    if 失败id~=0 and 剑会天下[失败id]~=nil then
        if 玩家数据[失败id].队伍~=0 then
            local 队伍id=玩家数据[失败id].队伍
            for n=1,#队伍数据[队伍id].成员数据 do
                local 队员id=队伍数据[队伍id].成员数据[n]
                剑会天下[队员id].连胜=0
                local 临时数据 = 剑会天下[队员id]
                发送数据(玩家数据[队员id].连接id,129,临时数据)
                self:剑会天下奖励结算(队员id)
                玩家数据[队员id].角色:添加活跃积分(1,"剑会失败",1)
                常规提示(队员id,"#Y/剑会天下PK,本次输了")
            end
        else
            剑会天下[失败id].连胜=0
             local 临时数据 = 剑会天下[失败id]
            发送数据(玩家数据[失败id].连接id,129,临时数据)
            self:剑会天下奖励结算(失败id)
             玩家数据[失败id].角色:添加活跃积分(1,"剑会失败",1)
            常规提示(失败id,"#Y/剑会天下PK,本次输了")
        end
    end
end











return 游戏活动类