local mp = require("script/ForYourOwnUse/MessagePack")
local 服务客户端类 = require("script/ForYourOwnUse/PackClient")()
辅助服务端=false
服务客户端类.发送_ = 服务客户端类.发送
function 服务客户端类:发送(...)
    local packed_data = mp.pack{...}

    -- 关键数据包信息
    local firstByte = #packed_data > 0 and string.byte(packed_data, 1) or 0
    local hexStr = ""
    for i = 1, math.min(#packed_data, 16) do
        hexStr = hexStr .. string.format("%02X ", string.byte(packed_data, i))
    end

    print('[服务连接] 发送MessagePack - 长度:', #packed_data, '首字节: 0x' .. string.format('%02X', firstByte), '前16字节:', hexStr)

    self:发送_(packed_data)
end

function 服务客户端类:连接处理(ip,端口)
  print('[服务连接] 连接服务器 -', ip .. ':' .. 端口)
  if self:连接(ip,端口) then
    print('[服务连接] 连接请求已发送')
  else
    print('[服务连接] 连接请求发送失败')
  end
end


-- function jjm(数据)
--   数据=encodeBase641(数据)
--   local jg=""
--   for n=1,#数据 do
--     local z=string.sub(数据,n,n)
--     --print(z,key[z],n,#数据)
--     if z~="" then
--       if key[z]==nil then
--         jg=jg..z
--       else
--         jg=jg..key[z]
--       end
--     end
--   end
--   return jg
-- end

-- function jjm1(数据)
--   local jg=数据
--   for n=1,#mab do
--     local z=string.sub(mab,n,n)
--     -- print(key[z],z)
--     if z=="," then print(66) end
--     if key[z]~=nil then
--       jg=string.gsub(jg,key[z],z)
--     end
--   end
--   return decodeBase641(jg)
-- end



function 服务客户端类:发送数据(序号,内容,数组转换)
  if 内容==nil then
    内容="1"
  end
  if 数组转换~=nil then
    内容=table.tostring(内容)
  end

  local 组合数据=序号..fgf..内容..fgf..获取账号
  print("[服务连接] 组合数据长度:", #组合数据, "内容:", string.sub(组合数据, 1, 50) .. "...")

  local 加密数据 = jm(组合数据)
  print("[服务连接] 发送数据包 - 序号:", 序号, "加密长度:", #加密数据)
  self:发送(加密数据)
end

function 服务客户端类:连接成功()
  print('[服务连接] 连接成功')
  系统退出=false
  tp.进程 = 0
  --引擎.置标题(全局游戏标题.."[已连接]")
end

function 服务客户端类:连接断开(so,ec)
  print('[服务连接] 连接断开 - 错误码:', ec)
  if 系统退出 then
    系统退出=false
    tp.进程 = 1
    引擎.置标题(全局游戏标题)
  else
    f函数.信息框("服务器连接断开!.....")
    引擎.关闭()
  end
end

function 服务客户端类:数据到达(内容)
  print("[服务连接] 接收数据包 - 长度:", #内容)

  data1 = mp.unpack(内容)
  if data1==nil or (data1~=nil and data1[1]==nil) then
   print("[服务连接] MessagePack解包失败")
   return
  end

  内容=data1[1]
  内容=jm1(内容)
  if 内容==nil or 内容=="" then
    print("[服务连接] 解密失败，断开连接")
    self:断开()
    return
  end

  local 数据=table.loadstring(内容)
  数据.序号=数据.序号+0
  print("[服务连接] 处理消息 - 序号:", 数据.序号)

  if 数据.序号==999 then
     f函数.信息框(数据.内容,"下线通知")
    self:断开()
    return
  end
  数据交总控处理(数据.序号,数据.内容)
end

function 服务客户端类:更新(dt) end
function 服务客户端类:初始化() end
function 服务客户端类:显示(x,y) end
return 服务客户端类