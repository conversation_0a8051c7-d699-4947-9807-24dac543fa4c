local mp = require("script/ForYourOwnUse/MessagePack")
local 服务客户端类 = require("script/ForYourOwnUse/PackClient")()
辅助服务端=false
服务客户端类.发送_ = 服务客户端类.发送
function 服务客户端类:发送(...)
    -- 添加详细的MessagePack打包调试日志
    local args = {...}
    print('[GM工具] 服务连接:发送 - 参数数量:', #args)
    for i, arg in ipairs(args) do
        print('[GM工具] 服务连接:发送 - 参数' .. i .. ':', arg, '类型:', type(arg))
        if type(arg) == 'string' and #arg > 0 then
            print('[GM工具] 服务连接:发送 - 参数' .. i .. '长度:', #arg)
            print('[GM工具] 服务连接:发送 - 参数' .. i .. '前32字符:', string.sub(arg, 1, 32))
        end
    end
    
    local packed_data = mp.pack{...}
    print('[GM工具] 服务连接:发送 - MessagePack打包后长度:', #packed_data)
    
    -- 显示MessagePack数据的十六进制表示（前32字节）
    local hexStr = ""
    for i = 1, math.min(#packed_data, 32) do
        hexStr = hexStr .. string.format("%02X ", string.byte(packed_data, i))
    end
    print('[GM工具] 服务连接:发送 - MessagePack前32字节Hex:', hexStr)
    
    -- 检查MessagePack数据开头
    if #packed_data > 0 then
        local firstByte = string.byte(packed_data, 1)
        print('[GM工具] 服务连接:发送 - MessagePack首字节:', firstByte, '(0x' .. string.format('%02X', firstByte) .. ')')
        if firstByte == 0x91 then
            print('[GM工具] 服务连接:发送 - ✓ MessagePack数组标识(0x91)确认')
        end
    end
    
    print('[GM工具] 服务连接:发送 - 调用PackClient发送函数')
    self:发送_(packed_data)
    print('[GM工具] 服务连接:发送 - PackClient发送完成')
end

function 服务客户端类:连接处理(ip,端口)
  print('[GM工具] 尝试连接服务器 IP:', ip, '端口:', 端口)
  if self:连接(ip,端口) then
    print('[GM工具] 连接请求已发送')
  else
    print('[GM工具] 连接请求发送失败')
  end
end


-- function jjm(数据)
--   数据=encodeBase641(数据)
--   local jg=""
--   for n=1,#数据 do
--     local z=string.sub(数据,n,n)
--     --print(z,key[z],n,#数据)
--     if z~="" then
--       if key[z]==nil then
--         jg=jg..z
--       else
--         jg=jg..key[z]
--       end
--     end
--   end
--   return jg
-- end

-- function jjm1(数据)
--   local jg=数据
--   for n=1,#mab do
--     local z=string.sub(mab,n,n)
--     -- print(key[z],z)
--     if z=="," then print(66) end
--     if key[z]~=nil then
--       jg=string.gsub(jg,key[z],z)
--     end
--   end
--   return decodeBase641(jg)
-- end



function 服务客户端类:发送数据(序号,内容,数组转换)
  if 内容==nil then
    内容="1"
  end
  if 数组转换~=nil then
    内容=table.tostring(内容)
  end
  
  -- 详细调试每个组件
  print("[GM工具] === 组合数据详细调试 ===")
  print("[GM工具] 序号:", 序号, "类型:", type(序号), "长度:", #tostring(序号))
  print("[GM工具] fgf:", fgf, "类型:", type(fgf), "长度:", #fgf)
  print("[GM工具] 内容:", 内容, "类型:", type(内容), "长度:", #内容)
  print("[GM工具] 获取账号:", 获取账号, "类型:", type(获取账号), "长度:", #获取账号)
  
  -- 计算理论长度
  local 理论长度 = #tostring(序号) + #fgf + #内容 + #fgf + #获取账号
  print("[GM工具] 理论总长度:", 理论长度)
  
  local 组合数据=序号..fgf..内容..fgf..获取账号
  print("[GM工具] 实际组合数据长度:", #组合数据)
  print("[GM工具] 组合数据字节长度:", string.len(组合数据))
  print("[GM工具] 组合数据前50字符:", string.sub(组合数据, 1, 50))
  print("[GM工具] 组合数据后50字符:", string.sub(组合数据, -50))
  
  -- 检查是否有隐藏字符
  local 字节统计 = {}
  for i = 1, #组合数据 do
    local byte = string.byte(组合数据, i)
    字节统计[byte] = (字节统计[byte] or 0) + 1
  end
  print("[GM工具] 特殊字节统计:")
  for byte, count in pairs(字节统计) do
    if byte < 32 or byte > 126 then
      print("[GM工具] 特殊字节:", byte, "(0x" .. string.format("%02X", byte) .. ") 出现次数:", count)
    end
  end
  
  print("[GM工具] 准备发送数据 - 序号:", 序号, "内容:", 内容, "获取账号:", 获取账号)
  print("[GM工具] 组合数据:", 组合数据)
  local 加密数据 = jm(组合数据)
  print("[GM工具] 加密后数据:", 加密数据)
  self:发送(加密数据)
  print("[GM工具] 数据已发送到服务器")
end

function 服务客户端类:连接成功()
  print('[GM工具] 连接服务器成功')
end

function 服务客户端类:连接断开(so,ec)
  print('[GM工具] 服务器连接断开, so:', so, 'ec:', ec)
  if 系统退出 then
    系统退出=false
    tp.进程 = 1
    引擎.置标题(全局游戏标题)
  else
    f函数.信息框("服务器连接断开!.....")
    引擎.关闭()
  end
end

function 服务客户端类:数据到达(内容)
  print("[GM工具] 收到服务器数据，长度:", #内容)
  print("[GM工具] 原始数据:", 内容)
  
  data1 = mp.unpack(内容)
  print("[GM工具] MessagePack解包结果:", data1)

  if data1==nil or (data1~=nil and data1[1]==nil) then
   print("[GM工具] 数据解包失败或为空，返回")
   return
  end
  内容=data1[1]
  print("[GM工具] 提取的内容:", 内容)
  内容=jm1(内容)
  print("[GM工具] 解密后内容:", 内容)
  if 内容==nil or 内容=="" then 
    print("[GM工具] 解密后内容为空，断开连接")
    self:断开() 
    return  
  end
  local 数据=table.loadstring(内容)
  print("[GM工具] 解析的数据结构:", 数据)

  数据.序号=数据.序号+0
  print("[GM工具] 处理序号:", 数据.序号, "内容:", 数据.内容)

  if 数据.序号==999 then
     f函数.信息框(数据.内容,"下线通知")
    self:断开()
    return
  end
  数据交总控处理(数据.序号,数据.内容)
end

function 服务客户端类:更新(dt) end
function 服务客户端类:初始化() end
function 服务客户端类:显示(x,y) end
return 服务客户端类