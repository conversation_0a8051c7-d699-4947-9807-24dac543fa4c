--=========================================================================
local GUI自适应类 = class(require("ggeui/基类"))
--=========================================================================
function GUI自适应类:初始化(标记,x,y,宽度,高度)
	self.纹理表 	= {}
	self._px = x or 0
	self._py = y or 0
	self._包围盒:置中心(-self._px,-self._py)
	for i=1,9 do
		self.纹理表[i] = require("gge精灵类")()
	end
end
function GUI自适应类:置宽高(w,h)
	self._宽度 		= w
	self._高度 		= h
	self._包围盒:置宽高(w,h)
	local 偏移 = self.纹理表[1]:取宽度()
	local 偏移2 = 偏移*2
	self.纹理表[3]:置中心(-self._宽度+偏移,0)--右上
	self.纹理表[6]:置中心(-self._宽度+偏移,-偏移)--右边
	self.纹理表[7]:置中心(0,-self._高度+偏移)--左下
	self.纹理表[8]:置中心(-偏移,-self._高度+偏移)--下边
	self.纹理表[9]:置中心(-self._宽度+偏移,-self._高度+偏移)--右下

	self.纹理表[2]:置区域(0,0,self._宽度-偏移2,偏移)
	self.纹理表[4]:置区域(0,0,偏移,self._高度-偏移2)
	self.纹理表[5]:置区域(0,0,self._宽度-偏移2,self._高度-偏移2)
	self.纹理表[6]:置区域(0,0,偏移,self._高度-偏移2)
	self.纹理表[8]:置区域(0,0,self._宽度-偏移2,偏移)
end
function GUI自适应类:置左上纹理(纹理)
	self.纹理表[1]:置纹理(纹理)
end
function GUI自适应类:置右上纹理(纹理)
	self.纹理表[3]:置纹理(纹理)
	self.纹理表[3]:置中心(-self._宽度+纹理:取宽度(),0)--右上
end
function GUI自适应类:置左下纹理(纹理)
	self.纹理表[7]:置纹理(纹理)
	self.纹理表[7]:置中心(0,-self._高度+纹理:取高度())--左下
end
function GUI自适应类:置右下纹理(纹理)
	self.纹理表[9]:置纹理(纹理)
	self.纹理表[9]:置中心(-self._宽度+纹理:取宽度(),-self._高度+纹理:取高度())--右下
end
function GUI自适应类:置上边纹理(纹理)
	self.纹理表[2]:置纹理(纹理)
	self.纹理表[2]:置中心(-纹理:取宽度(),0)
	self.纹理表[2]:置区域(0,0,self._宽度-纹理:取宽度()*2,纹理:取高度())
end
function GUI自适应类:置下边纹理(纹理)
	self.纹理表[8]:置纹理(纹理)
	self.纹理表[8]:置中心(-纹理:取宽度(),-self._高度+纹理:取高度())--下边
	self.纹理表[8]:置区域(0,0,self._宽度-纹理:取宽度()*2,纹理:取高度())
end
function GUI自适应类:置左边纹理(纹理)
	self.纹理表[4]:置纹理(纹理)
	self.纹理表[4]:置中心(0,-纹理:取高度())
	self.纹理表[4]:置区域(0,0,纹理:取宽度(),self._高度-纹理:取高度()*2)
end
function GUI自适应类:置右边纹理(纹理)
	self.纹理表[6]:置纹理(纹理)
	self.纹理表[6]:置中心(-self._宽度+纹理:取宽度(),-纹理:取高度())--右边
	self.纹理表[6]:置区域(0,0,纹理:取宽度(),self._高度-纹理:取高度()*2)
end
function GUI自适应类:置中间纹理(纹理)
	self.纹理表[5]:置纹理(纹理)
	self.纹理表[5]:置中心(-纹理:取宽度(),-纹理:取高度())
	self.纹理表[5]:置区域(0,0,self._宽度-纹理:取宽度()*2,self._高度-纹理:取高度()*2)
end
function GUI自适应类:_显示()
	for i=1,9 do
		self.纹理表[i]:显示(self._x,self._y)
	end
	--self._包围盒:显示()
end
function GUI自适应类:_消息事件(类型,x,y)
	if 类型 == '窗口移动' then
		self._x,self._y = x+self._px,y+self._py
		self._包围盒:置坐标(x,y)
	end
end
return GUI自适应类