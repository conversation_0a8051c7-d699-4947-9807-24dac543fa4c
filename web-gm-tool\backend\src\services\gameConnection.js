/**
 * 游戏服务器连接管理模块
 * 负责与网关服务器的TCP连接和数据通信
 */

const net = require('net');
const EventEmitter = require('events');
const { protocolHandler } = require('../protocol');

class GameConnection extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.host = options.host || 'localhost';
        this.port = options.port || 8888;
        this.autoReconnect = options.autoReconnect !== false;
        this.reconnectInterval = options.reconnectInterval || 5000;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 10;
        
        this.socket = null;
        this.connected = false;
        this.reconnectAttempts = 0;
        this.reconnectTimer = null;
        this.receiveBuffer = Buffer.alloc(0);
        
        // 绑定事件处理
        this.on('error', this.handleError.bind(this));
        this.on('disconnect', this.handleDisconnect.bind(this));
    }

    /**
     * 连接到游戏服务器
     * @returns {Promise<boolean>} 连接是否成功
     */
    connect() {
        return new Promise((resolve, reject) => {
            if (this.connected) {
                resolve(true);
                return;
            }

            console.log(`[连接] 正在连接到 ${this.host}:${this.port}`);
            
            this.socket = new net.Socket();
            
            // 连接成功事件
            this.socket.on('connect', () => {
                console.log(`[连接] 成功连接到 ${this.host}:${this.port}`);
                this.connected = true;
                this.reconnectAttempts = 0;
                this.clearReconnectTimer();
                // 添加小延迟，让服务端准备好
                setTimeout(() => {
                    this.emit('connected');
                }, 100);
                resolve(true);
            });

            // 数据接收事件
            this.socket.on('data', (data) => {
                this.handleReceiveData(data);
            });

            // 连接关闭事件
            this.socket.on('close', (hadError) => {
                console.log('[连接] 连接已关闭, hadError:', hadError);
                console.log('[连接] 关闭时间:', new Date().toISOString());
                this.connected = false;
                this.emit('disconnect');
            });

            // 连接错误事件
            this.socket.on('error', (error) => {
                console.error('[连接] 连接错误:', error.message);
                console.error('[连接] 错误代码:', error.code);
                console.error('[连接] 错误时间:', new Date().toISOString());
                this.connected = false;
                this.emit('error', error);
                reject(error);
            });

            // 开始连接
            this.socket.connect(this.port, this.host);
        });
    }

    /**
     * 断开连接
     */
    disconnect() {
        console.log('[连接] 主动断开连接');
        this.autoReconnect = false;
        this.clearReconnectTimer();
        
        if (this.socket) {
            this.socket.destroy();
            this.socket = null;
        }
        
        this.connected = false;
        this.emit('disconnect');
    }

    /**
     * 发送JSON数据（使用纯TCP协议）
     * @param {string} jsonData - 要发送的JSON字符串
     * @returns {Promise<boolean>} 发送是否成功
     */
    sendPacket(jsonData) {
        return new Promise((resolve, reject) => {
            if (!this.connected || !this.socket) {
                reject(new Error('未连接到服务器'));
                return;
            }

            try {
                console.log('[发送] === 准备发送纯JSON数据 ===');
                console.log('[发送] JSON数据:', jsonData);

                // 发送纯JSON数据，以换行符结尾
                const dataToSend = jsonData + '\n';

                console.log('[发送] 发送内容:', dataToSend);
                console.log('[发送] 数据长度:', dataToSend.length);

                this.socket.write(dataToSend, 'utf8', (error) => {
                    if (error) {
                        console.error('[发送] 数据发送失败:', error.message);
                        reject(error);
                    } else {
                        console.log(`[发送] 纯JSON数据发送成功`);
                        console.log('[发送] === 数据发送完成 ===');
                        resolve(true);
                    }
                });
            } catch (error) {
                console.error('[发送] 发送异常:', error.message);
                reject(error);
            }
        });
    }

    /**
     * 发送登录请求
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendLogin(username, password) {
        try {
            const loginPacket = protocolHandler.createLoginPacket(username, password);
            return await this.sendPacket(loginPacket);
        } catch (error) {
            console.error('[登录] 创建登录数据包失败:', error.message);
            throw error;
        }
    }

    /**
     * 发送充值请求
     * @param {number} type - 充值类型
     * @param {string} account - 账号
     * @param {number} amount - 充值数量
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendRecharge(type, account, amount) {
        try {
            const rechargePacket = protocolHandler.createRechargePacket(type, account, amount);
            return await this.sendPacket(rechargePacket);
        } catch (error) {
            console.error('[充值] 创建充值数据包失败:', error.message);
            throw error;
        }
    }

    /**
     * 发送账号管理请求
     * @param {number} operation - 操作类型
     * @param {string} targetAccount - 目标账号
     * @param {object} params - 额外参数
     * @returns {Promise<boolean>} 发送是否成功
     */
    async sendAccountManage(operation, targetAccount, params = {}) {
        try {
            const packet = protocolHandler.createAccountManagePacket(operation, targetAccount, params);
            return await this.sendPacket(packet);
        } catch (error) {
            console.error('[账号管理] 创建数据包失败:', error.message);
            throw error;
        }
    }

    /**
     * 处理接收到的数据
     * @param {Buffer} data - 接收到的数据
     */
    handleReceiveData(data) {
        console.log('[接收] 收到原始数据，长度:', data.length);

        // 将新数据添加到接收缓冲区
        this.receiveBuffer = Buffer.concat([this.receiveBuffer, data]);
        console.log('[接收] 当前缓冲区总长度:', this.receiveBuffer.length);

        // 处理JSON数据（以换行符分割）
        const dataStr = this.receiveBuffer.toString('utf8');
        const lines = dataStr.split('\n');

        // 保留最后一行（可能不完整）
        this.receiveBuffer = Buffer.from(lines.pop(), 'utf8');

        // 处理完整的行
        for (const line of lines) {
            if (line.trim()) {
                try {
                    const jsonData = JSON.parse(line.trim());
                    console.log('[接收] 收到JSON响应:', jsonData);
                    this.emit('packet', jsonData);
                } catch (error) {
                    console.error('[接收] JSON解析失败:', error.message, '数据:', line);
                }
            }
        }
    }

    /**
     * 处理连接错误
     * @param {Error} error - 错误对象
     */
    handleError(error) {
        console.error('[连接] 连接错误:', error.message);
        this.connected = false;
    }

    /**
     * 处理连接断开
     */
    handleDisconnect() {
        console.log('[连接] 连接已断开');
        this.connected = false;
        
        if (this.autoReconnect && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('[重连] 达到最大重连次数，停止重连');
            this.emit('maxReconnectReached');
        }
    }

    /**
     * 安排重连
     */
    scheduleReconnect() {
        if (this.reconnectTimer) {
            return;
        }

        this.reconnectAttempts++;
        console.log(`[重连] 第${this.reconnectAttempts}次重连尝试，${this.reconnectInterval}ms后开始`);
        
        this.reconnectTimer = setTimeout(() => {
            this.reconnectTimer = null;
            this.connect().catch((error) => {
                console.error('[重连] 重连失败:', error.message);
            });
        }, this.reconnectInterval);
    }

    /**
     * 清除重连定时器
     */
    clearReconnectTimer() {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }
    }

    /**
     * 获取连接状态
     * @returns {boolean} 是否已连接
     */
    isConnected() {
        return this.connected;
    }

    /**
     * 获取连接信息
     * @returns {object} 连接信息
     */
    getConnectionInfo() {
        return {
            host: this.host,
            port: this.port,
            connected: this.connected,
            reconnectAttempts: this.reconnectAttempts,
            autoReconnect: this.autoReconnect
        };
    }
}

module.exports = GameConnection;
