/**
 * WebSocket处理模块
 * 负责前端WebSocket连接管理和消息路由
 */

const { Server } = require('socket.io');
const GameConnection = require('../services/gameConnection');
const { constants } = require('../protocol');

class SocketHandler {
    constructor(httpServer, options = {}) {
        this.io = new Server(httpServer, {
            cors: {
                origin: options.corsOrigin || "http://localhost:3000",
                methods: ["GET", "POST"]
            }
        });

        this.gameConnections = new Map(); // 存储每个客户端的游戏连接
        this.clientSessions = new Map();  // 存储客户端会话信息
        
        this.setupSocketEvents();
        console.log('[WebSocket] WebSocket服务器已启动');
    }

    /**
     * 设置Socket事件监听
     */
    setupSocketEvents() {
        this.io.on('connection', (socket) => {
            console.log(`[WebSocket] 客户端连接: ${socket.id}`);
            
            // 初始化客户端会话
            this.clientSessions.set(socket.id, {
                authenticated: false,
                username: null,
                connectedAt: new Date()
            });

            // 设置客户端事件监听
            this.setupClientEvents(socket);

            // 客户端断开连接
            socket.on('disconnect', () => {
                this.handleClientDisconnect(socket);
            });
        });
    }

    /**
     * 设置客户端事件监听
     * @param {Socket} socket - Socket.io客户端连接
     */
    setupClientEvents(socket) {
        // 登录请求
        socket.on('login', async (data) => {
            await this.handleLogin(socket, data);
        });

        // 充值请求
        socket.on('recharge', async (data) => {
            await this.handleRecharge(socket, data);
        });

        // 账号管理请求
        socket.on('accountManage', async (data) => {
            await this.handleAccountManage(socket, data);
        });

        // 装备发送请求
        socket.on('sendEquipment', async (data) => {
            await this.handleSendEquipment(socket, data);
        });

        // 游戏活动请求
        socket.on('gameActivity', async (data) => {
            await this.handleGameActivity(socket, data);
        });

        // 断开游戏连接
        socket.on('disconnectGame', () => {
            this.disconnectGameConnection(socket.id);
        });

        // 获取连接状态
        socket.on('getStatus', () => {
            this.sendConnectionStatus(socket);
        });
    }

    /**
     * 处理登录请求
     * @param {Socket} socket - 客户端连接
     * @param {object} data - 登录数据
     */
    async handleLogin(socket, data) {
        try {
            const { username, password, gameHost, gamePort } = data;
            
            if (!username || !password) {
                socket.emit('loginResult', {
                    success: false,
                    message: '用户名和密码不能为空'
                });
                return;
            }

            console.log(`[登录] 用户 ${username} 尝试登录`);

            // 创建游戏连接
            const gameConnection = new GameConnection({
                host: gameHost || 'localhost',
                port: gamePort || 8888
            });

            // 设置游戏连接事件监听
            this.setupGameConnectionEvents(socket, gameConnection);

            // 连接到游戏服务器
            await gameConnection.connect();
            
            // 发送登录请求
            await gameConnection.sendLogin(username, password);
            
            // 保存连接
            this.gameConnections.set(socket.id, gameConnection);
            
            // 更新会话信息
            const session = this.clientSessions.get(socket.id);
            session.username = username;
            session.gameHost = gameHost;
            session.gamePort = gamePort;

            socket.emit('loginResult', {
                success: true,
                message: '登录请求已发送，等待服务器响应'
            });

        } catch (error) {
            console.error('[登录] 登录失败:', error.message);
            socket.emit('loginResult', {
                success: false,
                message: `登录失败: ${error.message}`
            });
        }
    }

    /**
     * 处理充值请求
     * @param {Socket} socket - 客户端连接
     * @param {object} data - 充值数据
     */
    async handleRecharge(socket, data) {
        try {
            if (!this.isAuthenticated(socket)) {
                socket.emit('rechargeResult', {
                    success: false,
                    message: '请先登录'
                });
                return;
            }

            const { type, account, amount } = data;
            const gameConnection = this.gameConnections.get(socket.id);

            if (!gameConnection || !gameConnection.isConnected()) {
                socket.emit('rechargeResult', {
                    success: false,
                    message: '游戏连接已断开'
                });
                return;
            }

            console.log(`[充值] 类型: ${type}, 账号: ${account}, 数量: ${amount}`);
            
            await gameConnection.sendRecharge(type, account, amount);
            
            socket.emit('rechargeResult', {
                success: true,
                message: '充值请求已发送'
            });

        } catch (error) {
            console.error('[充值] 充值失败:', error.message);
            socket.emit('rechargeResult', {
                success: false,
                message: `充值失败: ${error.message}`
            });
        }
    }

    /**
     * 处理账号管理请求
     * @param {Socket} socket - 客户端连接
     * @param {object} data - 账号管理数据
     */
    async handleAccountManage(socket, data) {
        try {
            if (!this.isAuthenticated(socket)) {
                socket.emit('accountManageResult', {
                    success: false,
                    message: '请先登录'
                });
                return;
            }

            const { operation, targetAccount, params } = data;
            const gameConnection = this.gameConnections.get(socket.id);

            if (!gameConnection || !gameConnection.isConnected()) {
                socket.emit('accountManageResult', {
                    success: false,
                    message: '游戏连接已断开'
                });
                return;
            }

            console.log(`[账号管理] 操作: ${operation}, 目标账号: ${targetAccount}`);
            
            await gameConnection.sendAccountManage(operation, targetAccount, params);
            
            socket.emit('accountManageResult', {
                success: true,
                message: '账号管理请求已发送'
            });

        } catch (error) {
            console.error('[账号管理] 操作失败:', error.message);
            socket.emit('accountManageResult', {
                success: false,
                message: `操作失败: ${error.message}`
            });
        }
    }

    /**
     * 处理装备发送请求
     * @param {Socket} socket - 客户端连接
     * @param {object} data - 装备数据
     */
    async handleSendEquipment(socket, data) {
        try {
            if (!this.isAuthenticated(socket)) {
                socket.emit('equipmentResult', {
                    success: false,
                    message: '请先登录'
                });
                return;
            }

            const { account, equipment } = data;
            const gameConnection = this.gameConnections.get(socket.id);

            if (!gameConnection || !gameConnection.isConnected()) {
                socket.emit('equipmentResult', {
                    success: false,
                    message: '游戏连接已断开'
                });
                return;
            }

            console.log(`[装备发送] 目标账号: ${account}`);
            
            const equipmentPacket = require('../protocol').protocolHandler.createEquipmentPacket(account, equipment);
            await gameConnection.sendPacket(equipmentPacket);
            
            socket.emit('equipmentResult', {
                success: true,
                message: '装备发送请求已发送'
            });

        } catch (error) {
            console.error('[装备发送] 发送失败:', error.message);
            socket.emit('equipmentResult', {
                success: false,
                message: `发送失败: ${error.message}`
            });
        }
    }

    /**
     * 处理游戏活动请求
     * @param {Socket} socket - 客户端连接
     * @param {object} data - 活动数据
     */
    async handleGameActivity(socket, data) {
        try {
            if (!this.isAuthenticated(socket)) {
                socket.emit('activityResult', {
                    success: false,
                    message: '请先登录'
                });
                return;
            }

            const { activityType, params } = data;
            const gameConnection = this.gameConnections.get(socket.id);

            if (!gameConnection || !gameConnection.isConnected()) {
                socket.emit('activityResult', {
                    success: false,
                    message: '游戏连接已断开'
                });
                return;
            }

            console.log(`[游戏活动] 活动类型: ${activityType}`);
            
            const activityPacket = require('../protocol').protocolHandler.createActivityPacket(activityType, params);
            await gameConnection.sendPacket(activityPacket);
            
            socket.emit('activityResult', {
                success: true,
                message: '活动请求已发送'
            });

        } catch (error) {
            console.error('[游戏活动] 请求失败:', error.message);
            socket.emit('activityResult', {
                success: false,
                message: `请求失败: ${error.message}`
            });
        }
    }

    /**
     * 设置游戏连接事件监听
     * @param {Socket} socket - 客户端连接
     * @param {GameConnection} gameConnection - 游戏连接
     */
    setupGameConnectionEvents(socket, gameConnection) {
        // 游戏服务器数据包
        gameConnection.on('packet', (data) => {
            socket.emit('gamePacket', data);
        });

        // 游戏连接断开
        gameConnection.on('disconnect', () => {
            socket.emit('gameDisconnected', {
                message: '游戏连接已断开'
            });
        });

        // 游戏连接错误
        gameConnection.on('error', (error) => {
            socket.emit('gameError', {
                message: `游戏连接错误: ${error.message}`
            });
        });

        // 达到最大重连次数
        gameConnection.on('maxReconnectReached', () => {
            socket.emit('gameError', {
                message: '游戏连接重连失败，请检查网络或服务器状态'
            });
        });
    }

    /**
     * 断开游戏连接
     * @param {string} socketId - 客户端ID
     */
    disconnectGameConnection(socketId) {
        const gameConnection = this.gameConnections.get(socketId);
        if (gameConnection) {
            gameConnection.disconnect();
            this.gameConnections.delete(socketId);
        }
    }

    /**
     * 处理客户端断开连接
     * @param {Socket} socket - 客户端连接
     */
    handleClientDisconnect(socket) {
        console.log(`[WebSocket] 客户端断开连接: ${socket.id}`);
        
        // 断开游戏连接
        this.disconnectGameConnection(socket.id);
        
        // 清理会话信息
        this.clientSessions.delete(socket.id);
    }

    /**
     * 发送连接状态
     * @param {Socket} socket - 客户端连接
     */
    sendConnectionStatus(socket) {
        const session = this.clientSessions.get(socket.id);
        const gameConnection = this.gameConnections.get(socket.id);
        
        socket.emit('connectionStatus', {
            authenticated: session?.authenticated || false,
            username: session?.username || null,
            gameConnected: gameConnection?.isConnected() || false,
            gameConnectionInfo: gameConnection?.getConnectionInfo() || null
        });
    }

    /**
     * 检查是否已认证
     * @param {Socket} socket - 客户端连接
     * @returns {boolean} 是否已认证
     */
    isAuthenticated(socket) {
        const session = this.clientSessions.get(socket.id);
        return session?.authenticated || false;
    }

    /**
     * 获取在线统计
     * @returns {object} 在线统计信息
     */
    getOnlineStats() {
        const totalClients = this.clientSessions.size;
        const authenticatedClients = Array.from(this.clientSessions.values())
            .filter(session => session.authenticated).length;
        const gameConnections = this.gameConnections.size;

        return {
            totalClients,
            authenticatedClients,
            gameConnections,
            timestamp: new Date()
        };
    }
}

module.exports = SocketHandler;
