/**
 * 测试字符映射是否正确
 * 使用已知的GM工具加密结果反推Base64
 */

const { CHAR_KEY_MAP } = require('./src/protocol/encryption');

// GM工具的加密结果
const gmEncrypted = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,=';

console.log('=== 反向解析GM工具的Base64 ===');

// 创建反向映射表
const reverseMap = {};
for (const [char, mapping] of Object.entries(CHAR_KEY_MAP)) {
    reverseMap[mapping] = char;
}

console.log('反向映射表大小:', Object.keys(reverseMap).length);

// 反向解析
let reconstructedBase64 = gmEncrypted;

// 按长度排序，先替换长的映射
const sortedMappings = Object.entries(CHAR_KEY_MAP)
    .sort((a, b) => b[1].length - a[1].length);

for (const [char, mapping] of sortedMappings) {
    const regex = new RegExp(escapeRegExp(mapping), 'g');
    reconstructedBase64 = reconstructedBase64.replace(regex, char);
}

console.log('重构的Base64长度:', reconstructedBase64.length);
console.log('重构的Base64:', reconstructedBase64);

// 与我们的Base64对比
const ourBase64 = 'MTEyMzQ1Ki0qMTIzNDVkbyBsb2NhbCByZXQ9e1siJvciXT0iODg4ODg4IixbIuWvhueggiJdPSI4ODg4ODgiXSByZXR1cm4gcmV0IGVuZDEyMzQ1Ki0qMTIzNDU=';

console.log('\n对比结果:');
console.log('GM Base64长度:', reconstructedBase64.length);
console.log('我们Base64长度:', ourBase64.length);
console.log('长度相同:', reconstructedBase64.length === ourBase64.length);
console.log('内容相同:', reconstructedBase64 === ourBase64);

if (reconstructedBase64 !== ourBase64) {
    console.log('\n差异分析:');
    const minLen = Math.min(reconstructedBase64.length, ourBase64.length);
    for (let i = 0; i < minLen; i++) {
        if (reconstructedBase64[i] !== ourBase64[i]) {
            console.log(`第${i}位不同:`);
            console.log(`  GM:   '${reconstructedBase64[i]}' (${reconstructedBase64.charCodeAt(i)})`);
            console.log(`  我们: '${ourBase64[i]}' (${ourBase64.charCodeAt(i)})`);
            console.log(`  前后文: GM='${reconstructedBase64.substring(i-5, i+5)}' 我们='${ourBase64.substring(i-5, i+5)}'`);
            break;
        }
    }
}

function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}
