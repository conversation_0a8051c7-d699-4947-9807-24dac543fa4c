--======================================================================--
-- @作者: <EMAIL>(313738139) 老毕
-- 单机GGE研究群: 34211 9466 (如果要研究商业 那请勿进!)
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-05-24 07:04:03
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解梦幻西游所有资源
--======================================================================--
local 假人玩家类 = class()
local 模型选项={"剑侠客","逍遥生","飞燕女","英女侠","神天兵","龙太子","羽灵神","舞天姬",
                "巨魔王","虎头怪","狐美人","骨精灵","巫蛮儿","偃无师","桃夭夭","鬼潇潇"}
local 名称选项 = {"服战联盟","中国威武","东海白衣人","剑蓝魔","三刀结束","老牛五号",
                  "老牛四号","老牛三号","老牛二号","老牛一号","避孕六号","避孕五号",
                    "避孕四号","避孕三号","避孕二号","避孕一号","小哥","专业出菜",
                    "111","专业出菜","无敌城府","无敌佩奇","亲爱的GM","无字情书",
                    "一艘小纸船","素笺淡墨","單人旅途","天青色等烟雨","落花人独立",
                    "星星的軌跡","思念浊成酒酿","浮期妄年","半醉亱未央","梦想者",
                    "时光、岁印斑驳","爱情孤独空人心","北巷南猫i","黛烟微醉不负泪",
                    "颩と寂寞","清风笑我","秦桑低绿枝","妄醉无凭","桃风杏雨","繁花落幕",
                    "卖5技能宝宝","醉离殇","酒柠","纯白","浮生醉清风","谁予琴乱","柔情空似水",
                    "過期關系","凉薄少女梦亡海","记忆交暖","摆摊小号1","哀叹小号2","摆摊小号3",
                    "摆摊小号4","摆摊小号5","苍井空","橘右京","程咬金","最高的幸运i","倦与恋",
                    "转角预定愛","酒徒醉夢人","残缺の美丽","浪漫只怕太慢","梅花疏淡","微光倾城",
                    "懵智","拟墨画扇","伴我老","森树白云","留了背影","浅梦丷墨汐°","明月夜",
                    "青空未雨","千梦","都怪时光太动听","小黑","小白","无名","专业打油酱","酒到微醺",
                    "北疆雨寒","放慢心跳","Θ内心d1.伤","轻音柔体萌萝莉","觅览少女","愛遇良人",
                    "余音未散","别提","爆菊无极限","夜凉若水","览觅少年","一生傲娇","欲望泯灭人性",
                    "献给清明节","青燈古酒","花开似梦","入戏太深","不会撒谎","年少不知米青仔贵",
                    "执我之手","白衣煮茶","黑白棋局","忘不了，她","半夏白色枕头","末世岛屿",
                    "携我之心","哼哈二将","对面是二傻","繁华宛似浮云","風卷著沙","纪年海泊",
                    "沉鱼落雁","Louise゛","捣你菊花","情深不寿","淚順著雨","封心灬锁爱","暈灬噠120",
                    "苍天有井自然空","命定浮沉","慧极必伤","刹那芳华","路易絲","灬情丶落轩","清酒暖心",
                    "与情无染","情难自控","X凄凉菂羙","有飘影更自信","南巷孤貓","烈酒醉人","沦陷的痛",
                    "夜的第七章","星逝破晓","一尾流莺","北城孤人","若如初见","简单","风恋绝尘","特别的人η",
                    "一指流砂","温柔散尽","彻底完败","揍跪了丶好么","月寒影对","特别的梦η","烟花寂凉",
                    "ˉ罗密欧","沟沟有深度","绽放情花","梦暖情双","新不了情","Θ“肮脏_矜持”","你的菊花真美",
                    "墨染诗水","刺痛命脉","野性难改","內傷無法治愈","最短不过善变","青絲繞手","樱花年华",
                    "十夏之风","负心人","举杯邀月","已然情深","情字繞口","徒增伤悲","ㄨ雨℡逝ㄚ","床单一片红",
                    "清歡相伴","何惧缘浅","一拉拉","扭曲旳黑暗”","转角遇到墙","竖萧而奏","溫酒相隨","似水柔情",
                    "贱人·滚之","细雨微醺","北軌陽葵","抚琴而唱","旧事重提","ω带刺╮男淫","紫丶星河","烟过是云",
                    "南岸青梔","撕心裂肺","伸手难辨","中指的邂逅","萧暗寒剑","雨过你在","忠贞罘渝","极速ω堕落",
                    "眼兮的温柔","北港初晴","青梅落影","落荒而逃","不服来嘛","双手成就你的梦","寡人寡巷","南岸末阴",
                    "从不服输","１切都昰命▂","吟唱寒荒","凉城以北","孤人孤街","半夏时光","説不岀旳疼╮","撸自身哥哥",
                    "南館瀟湘","空城旧梦","黑白年代","∑幼稚鬼","信基哥原地复活","鱼忘七秒#","壹席青城","十言九妄",
                    "不在乎的在乎ゝ","溪舔取精","南人旧心","人忘七年#","独守空城","撕裂的天堂","今天干我儿","灯为谁点",
                    "北人久情","事与愿违","初见钟情","潇潇暮雨","彼此相爱゛","脂为谁添","南熙寒笙","師傅.別念了",
                    "蝶梦恋花","发酵的恋","っ遮遮掩掩゛","夏末染殇","地心侵略者","风泊天","琴瑟在御","茹梦初醒",
                    "沐风朝朝","炫枫少","寒风笑雪夜","葬我以风","凤凰于飞","鱼巷猫归","抱你到天亮.","浅唱呐忧伤",
                    "闹市海港","赐你以梦","深秋无痕","╮麻辣","百合大队长","人走茶凉","旧城窄巷","回眸的笑",
                    "ε择日","一寸光阴","北葵向暖","曲终人散","少钩鈏我","为了你","一寸精","暖阳今夏","南栀倾寒",
                    "满城灯火","烈酒i","夜凉如水","这季悲凉","冷月昨秋","醉生梦死","极度深寒","淡丿子枫","深如大海i",
                    "|回身诉说","葑鈊ご独爱","各自生欢","梦冥光","追忆年华","深海溺心 i","复制回忆","在为止的路上狂奔",
                    "苏秋辰","听一首歌丶","笑叹如梦","时光不复","ω永别","碎泪","任憑風吹","念一个人丶",
                    "人亦已歌","咎由自取","幹杯清茶","不準妳走","初衷是你","阴月下的墓碑","渔舟唱晚","續杯烈酒",
                    "邀月对影","风吹屁丫爽","夜寒影对","彼岸灯火","今夕何夕","沦陷′2009"}


local 玩家称谓 = {"武神坛甲组冠军","2008怀旧","超凡入圣","兄弟情深帮众","神话帮众","三界贤者","太乙金仙","天下帮众"}
local 玩家光环 = {"烈焰澜翻","双鲤寄情","凌波微步","星光熠熠","浩瀚星河","荷塘涟漪","荷塘涟漪","水墨游龙","爱的光影","红叶随风","花的海洋","祥云瑞气","雪花飘落","珠落玉盘","月影婆娑"}
local 玩家锦衣 = {"冰寒绡月白","冰雪玉兔","炽云缎墨黑","从军行","从军行月曜","飞天舞","飞天舞朝露","绯雪织凝霜","官服","花间梦","蒹葭苍苍寒月","浪淘纱"
              ,"浪淘纱墨黑","鹿角弯弯","萌萌小厨","明光宝甲","青花瓷","水云归","霞姿月韵","闲云野鹤","雪眸影夜烬","夜影","羽仙歌"
           }
local 坐骑祥瑞 = {}--女坐骑专用

local 角色信息 = {
   飞燕女 = {模型="飞燕女",ID=1,染色方案=3,性别="女",种族="人",门派={"大唐官府","女儿村","方寸山","神木林"},武器={"双剑","环圈"}},
    英女侠 = {模型="英女侠",ID=2,染色方案=4,性别="女",种族="人",门派={"大唐官府","女儿村","方寸山","神木林"},武器={"双剑","鞭"}},
    巫蛮儿 = {模型="巫蛮儿",ID=3,染色方案=13,性别="女",种族="人",门派={"大唐官府","女儿村","方寸山","神木林"},武器={"宝珠","法杖"}},
    逍遥生 = {模型="逍遥生",ID=4,染色方案=1,性别="男",种族="人",门派={"大唐官府","化生寺","方寸山","神木林"},武器={"扇","剑"}},
    剑侠客 = {模型="剑侠客",ID=5,染色方案=2,性别="男",种族="人",门派={"大唐官府","化生寺","方寸山","神木林"},武器={"刀","剑"}},
    狐美人 = {模型="狐美人",ID=6,染色方案=7,性别="女",种族="魔",门派={"盘丝洞","阴曹地府","魔王寨","无底洞"},武器={"爪刺","鞭"}},
    骨精灵 = {模型="骨精灵",ID=7,染色方案=8,性别="女",种族="魔",门派={"盘丝洞","阴曹地府","魔王寨","无底洞"},武器={"魔棒","爪刺"}},
    杀破狼 = {模型="杀破狼",ID=8,染色方案=15,性别="男",种族="魔",门派={"狮驼岭","阴曹地府","魔王寨","无底洞"},武器={"宝珠","弓弩"}},
    巨魔王 = {模型="巨魔王",ID=9,染色方案=5,性别="男",种族="魔",门派={"狮驼岭","阴曹地府","魔王寨","无底洞"},武器={"刀","斧钺"}},
    虎头怪 = {模型="虎头怪",ID=10,染色方案=6,性别="男",种族="魔",门派={"狮驼岭","阴曹地府","魔王寨","无底洞"},武器={"斧钺","锤子"}},
    舞天姬 = {模型="舞天姬",ID=11,染色方案=11,性别="女",种族="仙",门派={"天宫","普陀山","龙宫","凌波城"},武器={"飘带","环圈"}},
    玄彩娥 = {模型="玄彩娥",ID=12,染色方案=12,性别="女",种族="仙",门派={"天宫","普陀山","龙宫","凌波城"},武器={"飘带","魔棒"}},
    羽灵神 = {模型="羽灵神",ID=13,染色方案=17,性别="男",种族="仙",门派={"天宫","普陀山","龙宫","凌波城"},武器={"法杖","弓弩"}},
    神天兵 = {模型="神天兵",ID=14,染色方案=9,性别="男",种族="仙",门派={"天宫","五庄观","龙宫","凌波城"},武器={"锤","枪矛"}},
    龙太子 = {模型="龙太子",ID=15,染色方案=10,性别="男",种族="仙",门派={"天宫","五庄观","龙宫","凌波城"},武器={"扇","枪矛"}},
    桃夭夭 = {模型="桃夭夭",ID=16,染色方案=18,性别="女",种族="仙",门派={"天宫","普陀山","龙宫","凌波城"},武器={"灯笼"}},
    偃无师 = {模型="偃无师",ID=17,染色方案=14,性别="男",种族="人",门派={"大唐官府","化生寺","方寸山","神木林"},武器={"剑","巨剑"}},
    鬼潇潇 = {模型="鬼潇潇",ID=18,染色方案=16,性别="女",种族="魔",门派={"盘丝洞","阴曹地府","魔王寨","无底洞"},武器={"爪刺","伞"}},
}
function 假人玩家类:初始化()
  self.数字id = 服务端参数.角色id + 10000
  self.开关 = false
  self.移动计时 = os.time()
  self.map={
    1001,
    1501,
    1506,
    1092,
    1070,
    1040,
    1226,
    1208,
    1173,
    1193,
    1002,
    1198,
    1116,
    1122,
    1140,
    1142,
    1111,
    1135,
    1512,
    1146,
    1131,
    1513,
    1139,
    1138
     }
  --self.maplist = {}
  if 假人数据 and 假人数据.地图人数 then
      self.map = {}
      for k,v in pairs(假人数据.地图人数) do
          table.insert(self.map,k)
      end
  end
  self.假人单位 = {}
  self.npcUserList = {}
  self.装备物品=绑定等级物品()
  local 总共数据 = 0
  for i,v in ipairs(self.map) do
      local 数量 = 取随机数(10,40)
      if 假人数据 and 假人数据.地图人数 and 假人数据.地图人数[v] then
          数量 = 取随机数(假人数据.地图人数[v][1],假人数据.地图人数[v][2])
      end
      for n=1,数量 do
          local 编号 = #self.假人单位+1
          self.假人单位[编号] = self:生成假人数据(v)
          总共数据 = 总共数据 + 1
      end
  end
  __S服务:输出("一共添加了[" .. 总共数据 .. "]个玩家假人")
end

function 假人玩家类:生成假人数据(地图,x,y,离线摆摊)
          local 名称 = 名称选项[取随机数(1,#名称选项)]
          if 假人数据 and 假人数据.地图名称 and 假人数据.地图名称[1] then
              名称 = 假人数据.地图名称[取随机数(1,#假人数据.地图名称)]
          end
          local 模型 = 模型选项[取随机数(1,#模型选项)]
          local 称谓 = nil
          if 取随机数(1,100)<=10 then
              称谓 = 玩家称谓[取随机数(1,#玩家称谓)]
              if 假人数据 and 假人数据.地图称谓 and 假人数据.地图称谓[1] then
                  称谓 = 假人数据.地图称谓[取随机数(1,#假人数据.地图称谓)]
              end
          end
          local 序列= 角色武器类型[模型][取随机数(1,2)]
          local 物品 = self.装备物品[序列]
          local 武器物品  = 物品[取随机数(1,#物品)]
          local 武器 = self:生成指定装备(武器物品,序列)
          if 武器~=nil and  取随机数(1,3)<=1 then
              武器.染色方案=23
              武器.染色组={取随机数(1,583),0,0}
          end
          if 取随机数(1,3)<=1 then 武器={} end
          local 队长 = false
          if 取随机数(1,4)<=1 then
              队长 = true
          end
          if x and y then
              队长 = false
          end
          local 坐骑 = {}
          if (not 武器 or not 武器.名称) and 取随机数(1,2)<=1 then
             坐骑.模型 = 坐骑祥瑞[取随机数(1,#坐骑祥瑞)]
             if 假人数据 and 假人数据.坐骑祥瑞 and 假人数据.坐骑祥瑞[1] then
                坐骑.模型 = 假人数据.坐骑祥瑞[取随机数(1,#假人数据.坐骑祥瑞)]
            end
          end
          local 锦衣 = {}
          if (not 坐骑 or not 坐骑.模型) and 取随机数(1,2)<=1 then
             锦衣.名称 = 玩家锦衣[取随机数(1,#玩家锦衣)]
             if 假人数据 and 假人数据.地图锦衣 and 假人数据.地图锦衣[1] then
                锦衣.名称 = 假人数据.地图锦衣[取随机数(1,#假人数据.地图锦衣)]
             end
          end
          local 光环 = {}
          if 取随机数(1,4)<=1 then
            光环.名称 = 玩家光环[取随机数(1,#玩家光环)]
            if 假人数据 and 假人数据.地图光环 and 假人数据.地图光环[1] then
                光环.名称 = 假人数据.地图光环[取随机数(1,#假人数据.地图光环)]
            end
          end
          local 开通 =false
          if 取随机数(1,4)<=1 then
             开通 = true
          end
          local 临时id = self.数字id
          local 靓号 = "("..临时id..")"
          if 开通 and 取随机数(1,2)<=1 then
             靓号 =金色id(临时id,取随机数(1,7))
          end
          self.数字id = self.数字id + 1
          local 临时yx=地图处理类.地图坐标[地图]:取随机点()
          local 临时x = 临时yx.x*20
          local 临时y = 临时yx.y*20
          if x and y then
              临时x = x*20
              临时y = y*20
          end
          return {
                  x=临时x,
                  y=临时y,
                  名称 = 名称,
                  模型 = 模型,
                  地图 = 地图,
                  靓号 = 靓号,
                  武器 = 武器,
                  坐骑 = 坐骑,
                  队长 = 队长,
                  id = 临时id,
                  当前称谓 = 称谓,
                  装备={[3]=武器},
                  原始 = {x=临时x,y = 临时y},
                  方向 = 取随机数(0,7),
                  锦衣={[1]=锦衣,[2]=光环},
                  染色方案 = 角色信息[模型].染色方案,
                  染色组 = {取随机数(0,5),取随机数(0,5),取随机数(0,5),0},
                  月卡={购买时间=0,到期时间=0,当前领取=0,开通=开通},
                  离线摆摊=离线摆摊,
                  }
end




function 假人玩家类:生成指定装备(名称,序列)
  if not 名称 or not 序列 then return end
  local 道具 = 物品类()
  道具:置对象(名称)

  if 序列 < 19 then -- 武器
          道具.命中 = 取随机数(打造属性.命中[道具.级别限制][1],打造属性.命中[道具.级别限制][2])
          道具.伤害 = 取随机数(打造属性.伤害[道具.级别限制][1],打造属性.伤害[道具.级别限制][2])
          if 取随机数(1,100) <= 40  then
                local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
                local sx1 = 取随机数(1,#额外属性)
                local 属性1  = 额外属性[sx1]
                道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                if 取随机数(1,100) <= 20 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
                end
          end
  elseif 序列 == 19 then -- 帽子
          道具.防御 = 取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
          道具.魔法 = 取随机数(打造属性.魔法[道具.级别限制][1],打造属性.魔法[道具.级别限制][2])
  elseif 序列 == 20 then -- 项链
          道具.灵力 = 取随机数(打造属性.灵力[道具.级别限制][1],打造属性.灵力[道具.级别限制][2])
  elseif 序列 == 21 then -- 衣服
          道具.防御 = 取随机数(打造属性.防御.衣服[道具.级别限制][1],打造属性.防御.衣服[道具.级别限制][2])
          if 取随机数(1,100) <= 40 then
              local 额外属性 = {"体质","力量","耐力","魔力","敏捷"}
              local sx1 = 取随机数(1,#额外属性)
              local 属性1  = 额外属性[sx1]
              道具[属性1] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
              if 取随机数(1,100) <= 20 then
                    table.remove(额外属性,sx1)
                    local 属性2 = 额外属性[取随机数(1,#额外属性)]
                    道具[属性2] = math.floor(取随机数(道具.级别限制*0.1,道具.级别限制*0.3))
              end
          end
  elseif 序列 == 22 then -- 腰带
          道具.防御 = 取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
          道具.气血 = 取随机数(打造属性.气血[道具.级别限制][1],打造属性.气血[道具.级别限制][2])
  elseif 序列 == 23 then -- 鞋子
          道具.防御 = 取随机数(打造属性.防御[道具.级别限制][1],打造属性.防御[道具.级别限制][2])
          道具.敏捷 = 取随机数(打造属性.敏捷[道具.级别限制][1],打造属性.敏捷[道具.级别限制][2])
  end
  local 特效几率 = 40
  local 通用特效 = {"无级别限制","神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生","易修理","超级简易"}
  if 道具.分类 == 5 then
        table.insert(通用特效,"愤怒")
        table.insert(通用特效,"暴怒")
  end
  if 取随机数()<=特效几率 then
      道具.特效 = 通用特效[取随机数(1,#通用特效)]
  end
  local 通用特技 = {"气疗术","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒",
    "起死回生","水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼",
    "碎甲术","破甲术","破血狂攻","慈航普渡","笑里藏刀","罗汉金钟","破碎无双","圣灵之甲","野兽之力"
    ,"琴音三叠","菩提心佑","先发制人","身似菩提"}

  if 取随机数()<= 特效几率 then
        道具.特技 = 通用特技[取随机数(1,#通用特技)]
  end
  return 道具
end








function 假人玩家类:功能开关(open)
  self.开关 = open
end

local 随机发话 = {"...","初出江湖经验真高，嘎嘎刷起来","傲来炉子昨天搞了1000万真轻松","那玩意看人品","我去天台底下挑几只合适宝宝去","驿站卖特殊兽决的人呢？","努力变强特殊兽决我要自己刷出来","谁！还有谁？擂台无敌！求虐！","收五宝咯","你好","美女","易经丹哪里刷的","星宿出易经丹 不一定抢得到会员卡福利能领到","法宝哪里领的？","法宝是杀天罡地煞得到的","组队抓鬼","特么的你还在啊","跑商去","跑商真累","去卖体","不去","要吃饭了","打酱油","走开","去PK下","卖点卡挣钱啊","我去","牛逼","开玩笑","我带你","有没有活动的","好看","美女","瓦塔","活动","活动去不去","搞基啊","等你","来来来","看热闹","你真","你h","你真有耐心","无聊","开始","一起",
  "*^_^*","小妞哪个","哪你帅哥恋爱了呀","老G说了 找到bug有奖励 我要去找bug咯？","有没有通宵抓鬼的？","全民PK赛没什么意思啊","有没有卖树苗的？？","收菜的有没有","感冒还是发骚了呀","你还真有病耶","四眼仔","^@@^","@_@","毛毛虫","水母","乌鸦","玫瑰花","狗狗吃骨头啦","哎呀呀","咋了","起来起来","……","别睡了有美媚哦","小样别不勾引哥，哥可不吃你那一套滴，你还没用美人计呢","咋了，小样  Y(^_^)Y 被哥说得你没话说了吧","（*^︹^*︺","Y(^_^)Y","耶小爱胜利了","哦……耶……","哦……","耶……","(～ o ～)~zZ","猪，一天就知道睡","^o^y勝利^o^y","好吧就让你一次","下次嗯……嗯……",
  "2个相同等级宝石右键自动合成真爽，但是要去傲来学会宝石工艺，不然有几率失败哦","收高级魔之心有的MMMMMMMMMMMMMMMMMMMMM","Ｓòrγy","简直是服了","什么玩意","我爱这个服","最喜欢这个版本，你骂谁？","傻逼","简直无敌了","做的太像了，游戏","咋了美女","π_π","100元卖个定制宝宝","完美的游戏","我爱老G","收菜收菜","游戏还需要另外花钱买吗？","游戏有宝宝套装吗？","有没有土豪带一下","这宝宝胚子真难洗 有卖的吗 摆个摊我瞅瞅","有钓鱼的吗 一起去啊！","赶紧刷够200活跃找老G领1000仙玉去","傲来卖内丹的人呢","有人收货吗？","出菜出菜带上比例M我","小样发什么呆呀","来跟哥聊聊天","吹吹牛","打打屁","(^ω^)","猪!你的鼻子有两个孔,感冒时的你还挂着鼻涕牛牛. 。","猪!你有着黑漆漆的眼,望呀望呀望也看不到边. 。","猪!你的耳朵是那么大,呼扇呼扇也听不到我在骂你傻.。 ","猪!你的尾巴是卷又卷","原来跑跑跳跳还离不开它 。","哦~~~ ","(=^ω^=)","喵喵过来，喵……去哪了呀……","小蝌蚪，没有腿。一条尾巴一张嘴","小金鱼，我也养了两条哦(^_^)∠※   (^_^)∠※",
  "宝贝哭什么呀,你看长江都洪水了","本服最强只有GM了吧","袁天罡可以洗点可以去看看","老G那里有会员卡出售听说经验加成百分百","我听说可以自动抓鬼，全场景传送功能","想要快速获取帮贡丢盒子最快","想赚钱的玩家赶紧去商会总管那里买点变异的持国谛听胚子摆摊我要买来炼妖","月华露跟金柳露还有低级兽决在五行大师那里摆摊卖的最快了","我想打造武器装备，会打造的请把打造摆摊上架万分感谢。","出售一本特殊兽决MMMMMMMMMMMMMMM","求带烧经验","现在抗夜费用都这么贵了","出本服极品号","有人帮忙杀蚩尤的吗？","哭你吗","这服算是我淘宝见过最好的端了","亚美尼亚雇佣军有想去的没","⊙⊙","你再瞪哥，我扁死你","怎么给哥抛媚眼，说吧有什么阴谋呀","你还为哥陶醉","狗狗呀又吐舌头 了","喝了多少呀就醉了","╮(╯▽╰)╭","哼","(╯﹏╰)","饿……就吃饭去"}

function 假人玩家类:走动()--假玩家随机事件
    if self.开关 and  os.time()- self.移动计时>=1 then
        for k,v in pairs(self.假人单位) do
                local 路径 = nil
                if not v.走路玩家 then
                    v.走路玩家 = {}
                end
                if 走动假人 and 取随机数()<=6 then
                    local 临时xy =地图处理类.地图坐标[v.地图]:取随机点()
                    v.x = 临时xy.x * 20
                    v.y = 临时xy.y * 20
                    路径 ={x =临时xy.x,y=临时xy.y,数字id = v.id,距离=0}
                end
                local 说话表情 = ""
                if v.发言计时 and os.time()-v.发言计时 >=5 then
                   v.说话内容 = nil
                   if os.time()-v.发言计时 >=60  then
                      v.发言计时 = nil
                      v.已发玩家 = {}
                  end
                end
                if 假人说话 and not v.发言计时 and 取随机数(1,100) <=3  then
                    if 取随机数(1,100)<=40 then 说话表情="#"..取随机数(1,99) end
                    v.说话内容=随机发话[取随机数(1,#随机发话)]..说话表情
                    if 假人数据 and 假人数据.地图发言 and 假人数据.地图发言[1] then
                        v.说话内容 = 假人数据.地图发言[取随机数(1,#假人数据.地图发言)]..说话表情
                    end
                    v.已发玩家 = {}
                    v.发言计时 = os.time()
                end

                for j, z in pairs(地图处理类.地图玩家[v.地图]) do
                    if 玩家数据[j] then
                        if 路径 and not v.走路玩家[j] and 取两点距离(v,玩家数据[j].角色.数据.地图数据)<=600 then
                              发送数据(玩家数据[j].连接id,1008,{数字id=v.id,路径=路径})
                              v.走路玩家[j] = {x=v.x,y=v.y}
                        else
                            if v.走路玩家[j] and 取两点距离(v.走路玩家[j],玩家数据[j].角色.数据.地图数据)<=600
                                and 取两点距离(v,玩家数据[j].角色.数据.地图数据)>=600 then
                                local 路径1 ={x =v.x/20,y=v.y/20,数字id = v.id,距离=0}
                                发送数据(玩家数据[j].连接id,1008,{数字id=v.id,路径=路径1})
                                v.走路玩家[j] = nil
                            end
                        end
                        if v.说话内容 and not v.已发玩家[j] and 取两点距离(v,玩家数据[j].角色.数据.地图数据)<=600 then
                                  发送数据(玩家数据[j].连接id,1018,{id=v.id,文本=v.说话内容})
                                  发送数据(玩家数据[j].连接id,38,{内容="["..v.名称..v.靓号.."]"..v.说话内容,频道="dq"})
                                  v.已发玩家[j] = true
                        end
                    end
                end

        end
        self.移动计时 =os.time()
    end
end







function 假人玩家类:发送假人(连接id, 地图)

          for k,v in pairs(self.假人单位) do
            if v.地图 == 地图 then
              发送数据(连接id,1006,v)
            end
          end
end





return 假人玩家类