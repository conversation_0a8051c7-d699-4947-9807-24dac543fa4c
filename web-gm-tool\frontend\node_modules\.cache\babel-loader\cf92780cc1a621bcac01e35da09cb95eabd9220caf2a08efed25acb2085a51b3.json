{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/FolderAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderAddOutlined = function FolderAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderAddOutlinedSvg\n  }));\n};\n\n/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4NCA0NDMuMVY1MjhoLTg0LjVjLTQuMSAwLTcuNSAzLjEtNy41IDd2NDJjMCAzLjggMy40IDcgNy41IDdINDg0djg0LjljMCAzLjkgMy4yIDcuMSA3IDcuMWg0MmMzLjkgMCA3LTMuMiA3LTcuMVY1ODRoODQuNWM0LjEgMCA3LjUtMy4yIDcuNS03di00MmMwLTMuOS0zLjQtNy03LjUtN0g1NDB2LTg0LjljMC0zLjktMy4xLTcuMS03LTcuMWgtNDJjLTMuOCAwLTcgMy4yLTcgNy4xem0zOTYtMTQ0LjdINTIxTDQwMy43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1OTJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMzMC40YzAtMTcuNy0xNC4zLTMyLTMyLTMyek04NDAgNzY4SDE4NFYyNTZoMTg4LjVsMTE5LjYgMTE0LjRIODQwVjc2OHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderAddOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FolderAddOutlinedSvg", "AntdIcon", "FolderAddOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/FolderAddOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FolderAddOutlinedSvg from \"@ant-design/icons-svg/es/asn/FolderAddOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FolderAddOutlined = function FolderAddOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FolderAddOutlinedSvg\n  }));\n};\n\n/**![folder-add](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4NCA0NDMuMVY1MjhoLTg0LjVjLTQuMSAwLTcuNSAzLjEtNy41IDd2NDJjMCAzLjggMy40IDcgNy41IDdINDg0djg0LjljMCAzLjkgMy4yIDcuMSA3IDcuMWg0MmMzLjkgMCA3LTMuMiA3LTcuMVY1ODRoODQuNWM0LjEgMCA3LjUtMy4yIDcuNS03di00MmMwLTMuOS0zLjQtNy03LjUtN0g1NDB2LTg0LjljMC0zLjktMy4xLTcuMS03LTcuMWgtNDJjLTMuOCAwLTcgMy4yLTcgNy4xem0zOTYtMTQ0LjdINTIxTDQwMy43IDE4Ni4yYTguMTUgOC4xNSAwIDAwLTUuNS0yLjJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY1OTJjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjMzMC40YzAtMTcuNy0xNC4zLTMyLTMyLTMyek04NDAgNzY4SDE4NFYyNTZoMTg4LjVsMTE5LjYgMTE0LjRIODQwVjc2OHoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FolderAddOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FolderAddOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}