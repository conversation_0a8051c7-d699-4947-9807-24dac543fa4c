{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from \"./MeasureCell\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    columnsKey = _ref.columnsKey,\n    onColumnResize = _ref.onColumnResize;\n  var ref = React.useRef(null);\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    },\n    ref: ref\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      if (isVisible(ref.current)) {\n        infoList.forEach(function (_ref2) {\n          var columnKey = _ref2.data,\n            size = _ref2.size;\n          onColumnResize(columnKey, size.offsetWidth);\n        });\n      }\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}", "map": {"version": 3, "names": ["React", "ResizeObserver", "MeasureCell", "isVisible", "MeasureRow", "_ref", "prefixCls", "columnsKey", "onColumnResize", "ref", "useRef", "createElement", "className", "concat", "style", "height", "fontSize", "Collection", "onBatchResize", "infoList", "current", "for<PERSON>ach", "_ref2", "column<PERSON>ey", "data", "size", "offsetWidth", "map", "key"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-table/es/Body/MeasureRow.js"], "sourcesContent": ["import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport MeasureCell from \"./MeasureCell\";\nimport isVisible from \"rc-util/es/Dom/isVisible\";\nexport default function MeasureRow(_ref) {\n  var prefixCls = _ref.prefixCls,\n    columnsKey = _ref.columnsKey,\n    onColumnResize = _ref.onColumnResize;\n  var ref = React.useRef(null);\n  return /*#__PURE__*/React.createElement(\"tr\", {\n    \"aria-hidden\": \"true\",\n    className: \"\".concat(prefixCls, \"-measure-row\"),\n    style: {\n      height: 0,\n      fontSize: 0\n    },\n    ref: ref\n  }, /*#__PURE__*/React.createElement(ResizeObserver.Collection, {\n    onBatchResize: function onBatchResize(infoList) {\n      if (isVisible(ref.current)) {\n        infoList.forEach(function (_ref2) {\n          var columnKey = _ref2.data,\n            size = _ref2.size;\n          onColumnResize(columnKey, size.offsetWidth);\n        });\n      }\n    }\n  }, columnsKey.map(function (columnKey) {\n    return /*#__PURE__*/React.createElement(MeasureCell, {\n      key: columnKey,\n      columnKey: columnKey,\n      onColumnResize: onColumnResize\n    });\n  })));\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,oBAAoB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,SAAS,MAAM,0BAA0B;AAChD,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAE;EACvC,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,UAAU,GAAGF,IAAI,CAACE,UAAU;IAC5BC,cAAc,GAAGH,IAAI,CAACG,cAAc;EACtC,IAAIC,GAAG,GAAGT,KAAK,CAACU,MAAM,CAAC,IAAI,CAAC;EAC5B,OAAO,aAAaV,KAAK,CAACW,aAAa,CAAC,IAAI,EAAE;IAC5C,aAAa,EAAE,MAAM;IACrBC,SAAS,EAAE,EAAE,CAACC,MAAM,CAACP,SAAS,EAAE,cAAc,CAAC;IAC/CQ,KAAK,EAAE;MACLC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE;IACZ,CAAC;IACDP,GAAG,EAAEA;EACP,CAAC,EAAE,aAAaT,KAAK,CAACW,aAAa,CAACV,cAAc,CAACgB,UAAU,EAAE;IAC7DC,aAAa,EAAE,SAASA,aAAaA,CAACC,QAAQ,EAAE;MAC9C,IAAIhB,SAAS,CAACM,GAAG,CAACW,OAAO,CAAC,EAAE;QAC1BD,QAAQ,CAACE,OAAO,CAAC,UAAUC,KAAK,EAAE;UAChC,IAAIC,SAAS,GAAGD,KAAK,CAACE,IAAI;YACxBC,IAAI,GAAGH,KAAK,CAACG,IAAI;UACnBjB,cAAc,CAACe,SAAS,EAAEE,IAAI,CAACC,WAAW,CAAC;QAC7C,CAAC,CAAC;MACJ;IACF;EACF,CAAC,EAAEnB,UAAU,CAACoB,GAAG,CAAC,UAAUJ,SAAS,EAAE;IACrC,OAAO,aAAavB,KAAK,CAACW,aAAa,CAACT,WAAW,EAAE;MACnD0B,GAAG,EAAEL,SAAS;MACdA,SAAS,EAAEA,SAAS;MACpBf,cAAc,EAAEA;IAClB,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}