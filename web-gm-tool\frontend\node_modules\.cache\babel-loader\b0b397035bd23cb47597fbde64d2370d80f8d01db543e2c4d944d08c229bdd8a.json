{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MedicineBoxFilledSvg from \"@ant-design/icons-svg/es/asn/MedicineBoxFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MedicineBoxFilled = function MedicineBoxFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MedicineBoxFilledSvg\n  }));\n};\n\n/**![medicine-box](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzOS4yIDI3OC4xYTMyIDMyIDAgMDAtMzAuNC0yMi4xSDczNlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMTJoLTcyLjhhMzEuOSAzMS45IDAgMDAtMzAuNCAyMi4xTDExMiA1MDJ2Mzc4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MDJsLTcyLjgtMjIzLjl6TTY2MCA2MjhjMCA0LjQtMy42IDgtOCA4SDU0NHYxMDhjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjYzNkgzNzJjLTQuNCAwLTgtMy42LTgtOHYtNDhjMC00LjQgMy42LTggOC04aDEwOFY0NjRjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYxMDhoMTA4YzQuNCAwIDggMy42IDggOHY0OHptNC0zNzJIMzYwdi03MmgzMDR2NzJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MedicineBoxFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MedicineBoxFilled';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "MedicineBoxFilledSvg", "AntdIcon", "MedicineBoxFilled", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/MedicineBoxFilled.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport MedicineBoxFilledSvg from \"@ant-design/icons-svg/es/asn/MedicineBoxFilled\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar MedicineBoxFilled = function MedicineBoxFilled(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: MedicineBoxFilledSvg\n  }));\n};\n\n/**![medicine-box](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgzOS4yIDI3OC4xYTMyIDMyIDAgMDAtMzAuNC0yMi4xSDczNlYxNDRjMC0xNy43LTE0LjMtMzItMzItMzJIMzIwYy0xNy43IDAtMzIgMTQuMy0zMiAzMnYxMTJoLTcyLjhhMzEuOSAzMS45IDAgMDAtMzAuNCAyMi4xTDExMiA1MDJ2Mzc4YzAgMTcuNyAxNC4zIDMyIDMyIDMyaDczNmMxNy43IDAgMzItMTQuMyAzMi0zMlY1MDJsLTcyLjgtMjIzLjl6TTY2MCA2MjhjMCA0LjQtMy42IDgtOCA4SDU0NHYxMDhjMCA0LjQtMy42IDgtOCA4aC00OGMtNC40IDAtOC0zLjYtOC04VjYzNkgzNzJjLTQuNCAwLTgtMy42LTgtOHYtNDhjMC00LjQgMy42LTggOC04aDEwOFY0NjRjMC00LjQgMy42LTggOC04aDQ4YzQuNCAwIDggMy42IDggOHYxMDhoMTA4YzQuNCAwIDggMy42IDggOHY0OHptNC0zNzJIMzYwdi03MmgzMDR2NzJ6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(MedicineBoxFilled);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'MedicineBoxFilled';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}