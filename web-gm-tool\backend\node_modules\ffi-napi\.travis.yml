sudo: false

env:
  - CXX=g++-4.9
  - CXX=clang++

addons:
  apt:
    sources:
      - ubuntu-toolchain-r-test
    packages:
      - g++-4.9

language: node_js

os:
  - linux
  - osx

osx_image: xcode10

node_js:
  - "10"
  - "12"
  - "14"

jobs:
  include:
      - os: linux
        arch: arm64
        node_js: 12
        env: CXX=g++-4.9
  exclude:
      - os: osx
        env: CXX=g++-4.9
      - os: linux
        env: CXX=clang++

install:
  - npm install --build-from-source

after_success:
  - npm install coveralls
  - nyc report --reporter=text-lcov | ./node_modules/.bin/coveralls

before_deploy:
  - ARCHIVE_NAME="${TRAVIS_TAG:-latest}-$TRAVIS_OS_NAME-`uname -m`.tar"
  - npm run prebuild --v8_enable_pointer_compression=false --v8_enable_31bit_smis_on_64bit_arch=false
  - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then ARCH=ia32 npm run prebuild --v8_enable_pointer_compression=false --v8_enable_31bit_smis_on_64bit_arch=false; fi
  - tar --create --verbose --file="$ARCHIVE_NAME" --directory "$TRAVIS_BUILD_DIR/prebuilds" .

deploy:
  provider: releases
  draft: false
  prerelease: true
  file: "$ARCHIVE_NAME"
  skip_cleanup: true
  on:
    tags: true
    node: 12
  api_key: $PREBUILD_GITHUB_TOKEN
