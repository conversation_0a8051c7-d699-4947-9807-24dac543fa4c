--======================================================================--
--======================================================================--
local 活动处理 = class()


function 活动处理:初始化() end

function 活动处理:活动选项解析(连接id,数字id,序号,内容)
  if not 玩家数据[数字id].地图单位 then return end
  if 任务数据[玩家数据[数字id].地图单位.标识] == nil then
    return
  end
  local 类型=任务数据[玩家数据[数字id].地图单位.标识].类型
  local 事件=内容[1]
  local 名称=内容[3]
  local 判断玩家 = function()
        if 任务数据[玩家数据[数字id].地图单位.标识].玩家id~=数字id then
          return  false
        end
        return true
  end
  local 判断战斗 = function()
        if 任务数据[玩家数据[数字id].地图单位.标识].战斗 then
          常规提示(数字id,"#Y/对方正在战斗中")
          return  false
        end
        return true
  end
  local 判断队伍 = function()
          if not 玩家数据[数字id].队伍 or 玩家数据[数字id].队伍==0  then
            常规提示(数字id,"#Y必须组队才能触发该活动")
            return  false
          end
          return true
  end
  local 判断人数 = function(人数)
          if 取队伍人数(数字id)<人数  then
            常规提示(数字id,"#Y该活动必须"..人数.."人才可以触发")
            return  false
          end
          return true
  end
  local 判断等级 = function(等级)
            if type(等级)=="table" then
                if 取队伍最低等级(数字id,等级[1]) then
                    常规提示(数字id,"#Y/此任务至少要达到"..等级[1].."级")
                    return  false
                end
                if 取队伍最高等级(数字id,等级[2]) then
                    常规提示(数字id,"#Y/此任务高等级不能超过"..等级[2].."级")
                    return  false
                end
            else
                if not 取等级要求(数字id,等级)  then
                  常规提示(数字id,"#Y该活动必须"..等级.."级才可以触发")
                  return  false
                end
            end
            return true
  end
  local 判断次数 = function(类型)
          if not 活动次数查询(数字id,类型)  then
            return  false
          end
          return true
  end
  local 判断任务 = function(任务)
        if not 取队员任务一致(数字id,任务) then
           发送数据(玩家数据[数字id].连接id,1501,{内容="你是谁？我们认识吗#55",模型=任务数据[玩家数据[数字id].地图单位.标识].模型,名称=任务数据[玩家数据[数字id].地图单位.标识].名称})
           return false
        end
        return true
  end
  local 触发条件= function(战斗,队伍,等级,人数,次数)
        if 战斗 and not 判断战斗() then
            return false
        end
        if 队伍 and not 判断队伍() then
            return false
        end
        if 等级 and not 判断等级(等级) then
            return false
        end
        if 人数 and not 判断人数(人数) then
            return false
        end
        if 次数 and not 判断次数(次数) then
            return false
        end
        return true
  end


  if 取队长权限(数字id)==false then 常规提示(数字id,"#Y你不是队长")  return  end
  if 类型==4 then
        if 事件=="交出宝藏" and 判断战斗() then
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100002,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==22 then
    if 判断玩家() then
        local 任务id=玩家数据[数字id].地图单位.标识
        if 任务数据[任务id].阶段<4 and 任务数据[任务id].操作 then
              if 事件=="给摇钱树浇水" then
                任务数据[任务id].浇水=任务数据[任务id].浇水+1
                任务数据[任务id].操作=false
                任务数据[任务id].成功操作=任务数据[任务id].成功操作+1
                if 取随机数() >= 10 and 任务数据[任务id].刷出强盗 == nil then
                  任务处理类:刷新摇钱树强盗(数字id,任务id)
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截,并且吸引了附近的强盗前来抢劫。")
                else
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截")
                end
              elseif 事件=="给摇钱树施肥" then
                任务数据[任务id].施肥=任务数据[任务id].施肥+1
                任务数据[任务id].操作=false
                任务数据[任务id].成功操作=任务数据[任务id].成功操作+1
                if 取随机数() >= 10 and 任务数据[任务id].刷出强盗 == nil then
                  任务处理类:刷新摇钱树强盗(数字id,任务id)
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截,并且吸引了附近的强盗前来抢劫。")
                else
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截")
                end
              elseif 事件=="给摇钱树除虫" then
                任务数据[任务id].除虫=任务数据[任务id].除虫+1
                任务数据[任务id].操作=false
                任务数据[任务id].成功操作=任务数据[任务id].成功操作+1
                if 取随机数() >= 10 and 任务数据[任务id].刷出强盗 == nil then
                  任务处理类:刷新摇钱树强盗(数字id,任务id)
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截,并且吸引了附近的强盗前来抢劫。")
                else
                  添加最后对话(数字id,"你的摇钱树经过你的悉心照料又长高了一大截")
                end
              end
        elseif 任务数据[任务id].阶段==5 then
                if 任务数据[任务id].次数>0 then
                    if 事件=="轻轻摇动" then
                        任务处理类:添加摇钱树元宝任务(数字id,{x=任务数据[任务id].x,y=任务数据[任务id].y})
                        任务数据[任务id].次数=任务数据[任务id].次数-1
                        if 任务数据[任务id].次数<=0 then
                          地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
                          玩家数据[数字id].角色:取消任务(任务id)
                          if 任务数据[任务id].刷出强盗 ~= nil then
                            地图处理类:删除单位(任务数据[任务数据[任务id].刷出强盗].地图编号,任务数据[任务数据[任务id].刷出强盗].编号)
                            任务数据[任务数据[任务id].刷出强盗]  = nil
                            玩家数据[数字id].角色:取消任务(任务数据[任务id].刷出强盗)
                          end
                          任务数据[任务id]=nil
                        else
                          添加最后对话(数字id,string.format("你轻轻摇动了一下树枝，似乎掉下来一些东西。（你还可以摇动#R%s#W次）",任务数据[任务id].次数))
                        end
                    end
                end
        end
    else
      发送数据(玩家数据[数字id].连接id,1501,{内容="你是谁？我们认识吗#55",模型=任务数据[玩家数据[数字id].地图单位.标识].模型,名称=任务数据[玩家数据[数字id].地图单位.标识].名称})
    end
  elseif 类型==24 then

        if 事件=="纳命来" and 判断玩家() and 判断战斗()  then
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,100120,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
        end

  elseif 类型==101 then
        if 事件=="少说废话,开打" and 触发条件(1,1,69,3,"封妖战斗") then --(战斗,队伍,等级,人数,次数)
                任务数据[玩家数据[数字id].地图单位.标识].战斗=true
                战斗准备类:创建战斗(数字id+0,100004,玩家数据[数字id].地图单位.标识)
                玩家数据[数字id].地图单位=nil
                return
        end
  elseif 类型==102 then
          if 事件=="我来领养你" and 触发条件(1,1,69) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100005,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end

  elseif 类型==104 then
        if 事件=="请星君赐教" and 触发条件(1,1,69,3,"星宿") then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100009,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==105 then
    if 事件=="让我来收拾你" and 触发条件(1,1,69,3,"妖魔鬼怪") then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100010,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
    end
  -- elseif 类型==106 then

  elseif 类型==108 then
    if 事件=="我要报道" and  触发条件(nil,1,30,nil,"游泳比赛") and 判断任务(109) then --(战斗,队伍,等级,人数,次数)
          local 任务id = 玩家数据[数字id].角色:取任务(109)
          if 任务数据[任务id].已战斗 or 取随机数()<=60 then
              任务处理类:完成游泳任务(数字id)
          else
              战斗准备类:创建战斗(数字id+0,100012,任务id)
              玩家数据[数字id].地图单位=nil
          end
          return
    end
  elseif 类型==110 then
        if 事件=="放肆，看我怎么收拾你" and 判断战斗() then
          if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y/该任务不允许组队完成") return  end
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100013,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        elseif 事件=="请接收物资" then
            任务处理类:完成官职任务(数字id,2)
        end
  elseif 类型==111 then
          if 事件=="放肆，找打" and 判断战斗() then
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100016,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          elseif 事件=="不要怕，我来帮你" and 判断战斗() then
              if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y/该任务不允许组队完成") return  end
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100017,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          elseif 事件=="手底下分高低" and 判断战斗() then
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100018,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==112 then---------远方文韵墨香
          if 事件=="放肆，找打" and 判断战斗() then
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100430,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          elseif 事件=="不要怕，我来帮你" and 判断战斗() then
                  if 玩家数据[数字id].队伍~=0 then 常规提示(数字id,"#Y/该任务不允许组队完成") return  end
                  任务数据[玩家数据[数字id].地图单位.标识].战斗=true
                  战斗准备类:创建战斗(数字id+0,100431,玩家数据[数字id].地图单位.标识)
                  玩家数据[数字id].地图单位=nil
                  return
          elseif 事件=="手底下分高低" and 判断战斗() then
                任务数据[玩家数据[数字id].地图单位.标识].战斗=true
                战斗准备类:创建战斗(数字id+0,100432,玩家数据[数字id].地图单位.标识)
                玩家数据[数字id].地图单位=nil
                return
          end
  elseif 类型==8 then
        if 事件=="回你的地狱去" and 触发条件(1,1) and 判断任务(8) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100008,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==211 then
        if 事件=="回你的地狱去" and 触发条件(1,1) and 判断任务(211) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100307,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
        end
  elseif 类型==11 then
        if (事件=="嗯，你是磊落的侠客，失敬失敬！" or 事件=="哼，你是真的盗贼，我要将你抓捕归案！") and 触发条件(1,1) and 判断任务(11) then --(战斗,队伍,等级,人数,次数)
             任务数据[玩家数据[数字id].地图单位.标识].战斗=true
             战斗准备类:创建战斗(数字id+0,100021,玩家数据[数字id].地图单位.标识)
             玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==12 then
        if 事件=="将偷盗的宝物交出来" and 触发条件(1,1) and 判断任务(12) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100022,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==200 then
        if 事件=="我要降服你" and 触发条件(1,1,30,nil,"降妖伏魔") and 判断任务(200) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100242,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
   elseif 类型==201 then
          if 任务数据[玩家数据[数字id].地图单位.标识].名称=="迷宫土地" then
                if 事件=="我要离开本层迷宫" then
                  local 传送地图= 玩家数据[数字id].角色.数据.地图数据.编号
                  if 任务数据[玩家数据[数字id].地图单位.标识].真假 then
                    传送地图=传送地图+1
                  else
                    传送地图=传送地图-1
                  end
                  local xy=地图处理类.地图坐标[传送地图]:取随机点()
                  地图处理类:跳转地图(数字id,传送地图,xy.x,xy.y)
                  常规提示(数字id,"#Y你来到了#R"..取地图名称(传送地图))
                end
          elseif 任务数据[玩家数据[数字id].地图单位.标识].名称=="迷宫守卫" then
                  if 事件=="领取奖励" then
                      玩家数据[数字id].道具:迷宫奖励(数字id)
                  end
          end

  elseif 类型==205 then
          if 事件=="休得在此放肆" and 触发条件(1,1,30,3,"妖王") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100020,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==209 then
          if 事件=="我岂是贪污受贿之人" and 触发条件(1,nil,30,nil,"三界悬赏令") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100026,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          elseif 事件=="既然如此，就留你一条活路" and 触发条件(1,nil,30,nil,"三界悬赏令") then --(战斗,队伍,等级,人数,次数)
                  玩家数据[数字id].角色:添加银子(200000,"三界悬赏令",1)
                  玩家数据[数字id].角色:取消任务(玩家数据[数字id].地图单位.标识)
                  地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].编号)
                  任务数据[玩家数据[数字id].地图单位.标识]=nil
                  return
          end
  elseif 类型==206 then
          if 事件=="我们准备好了" and 触发条件(1,1,69,3,"世界BOSS") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100024,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==210 then
          if 事件=="知了还这么嚣张？讨打！" and 触发条件(1,1,69,3,"知了王") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100027,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==123 then
          地图处理类:跳转地图(数字id,6002,26,58)
          玩家数据[数字id].地图单位=nil
  elseif 类型==124 then
          if 事件=="哼，少废话，看打！" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100029,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==125 then
          if 事件=="休得在此作恶" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100030,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==126 then
          if 事件=="我看你就是假的国王" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100031,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==127 then
    if 事件=="捡起来,放口袋里" then
        local x银子 = 取随机数(1,50)
        玩家数据[数字id].角色:添加银子(x银子,"地图捡银子",1)
        地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].编号)
        任务数据[玩家数据[数字id].地图单位.标识]=nil
        return
    elseif 事件=="捡物品,放口袋里" then
            local x道具格子=玩家数据[数字id].角色:取道具格子()
            if x道具格子==0 then
              常规提示(数字id,"您的道具栏物品已经满啦")
              return
            end
            local x数量 = 取随机数(1,3)
            local 临时品质=0
            local 临时等级=10
            local 物品表={"包子","烤鸭","佛跳墙"}
            local 临时物品=物品表[取随机数(1,#物品表)]
            local x模型 = 任务数据[玩家数据[数字id].地图单位.标识].模型
            if x模型=="食物" then
              物品表={"包子","烤鸭","佛跳墙"}
              临时物品=物品表[取随机数(1,#物品表)]
              if 临时物品~="包子" then
                x数量 =1
                临时品质=取随机数(math.floor(临时等级*0.5),临时等级)
              end
            elseif x模型=="口粮" or x模型=="摄妖香" or x模型=="药品" then
              if x模型=="药品" then
                物品表={"灵脂","曼陀罗花","鬼切草","佛手","四叶花","鬼切草"}
                临时物品=物品表[取随机数(1,#物品表)]
                x数量 =1
                临时品质=取随机数(math.floor(临时等级*0.5),临时等级)
              else
                临时物品=x模型
              end
            end
            地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].编号)
            任务数据[玩家数据[数字id].地图单位.标识]=nil
            常规提示(数字id,"#Y/恭喜你得到了"..x数量.."个"..临时物品.."...")
            玩家数据[数字id].道具:给予道具(数字id,临时物品,x数量,临时品质)
            return
    elseif 事件=="抓起来,做宠物" then
      if 玩家数据[数字id].召唤兽:是否携带上限()==false then
          local 种类
          if 取随机数(1,150)<=20 then
            种类=true
          end
          local x模型 = 任务数据[玩家数据[数字id].地图单位.标识].模型
          玩家数据[数字id].召唤兽:添加召唤兽(x模型,x模型,"宝宝")
          常规提示(数字id,"#Y/你获得了一只"..x模型)
          地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].编号)
          任务数据[玩家数据[数字id].地图单位.标识]=nil
      else
        常规提示(数字id,"#Y/对不起!你携带的宝宝已经到达上限了.")
      end
      return
    end
  elseif 类型==128 then
          if 事件=="神仙不做,看来我要抓你回天庭" and 触发条件(1,1,69,3,"天庭叛逆") then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100032,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==129 then
          if 事件=="派对时刻,要和我一起派对战斗吗?" and 触发条件(1,1,50,3,"糖果派对") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100033,玩家数据[数字id].地图单位.标识)
              return
          end
  elseif 类型==131 then
          if 玩家数据[数字id].车迟对话 then
            游戏活动类:车迟回答题目(数字id,事件,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==132 then
          if 事件=="送我过去" then
              local 副本id=任务数据[玩家数据[数字id].地图单位.标识].副本id
              副本数据.车迟斗法.进行[副本id].进程=4
              任务处理类:设置车迟斗法副本(副本id)
              任务处理类:副本传送(数字id,2)
              for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
               玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
              end
          end
  elseif 类型==133 then
          if 事件=="我来吃掉你" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100066,玩家数据[数字id].地图单位.标识)
          end
  elseif 类型==134 then
          if 事件=="妖怪找打" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100067,玩家数据[数字id].地图单位.标识)
              return
          end
  elseif 类型==135 then
          if 事件=="送我过去" then
            local 副本id=任务数据[玩家数据[数字id].地图单位.标识].副本id
            副本数据.车迟斗法.进行[副本id].进程=6
            任务处理类:设置车迟斗法副本(副本id)
            任务处理类:副本传送(数字id,2)
            for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
              玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
            end
          end
  elseif 类型==136 then
          if 事件=="送我过去" then
            地图处理类:跳转地图(数字id,1070,125,144)
          end
  elseif 类型==137 then
          if 事件=="上仙莫要助纣为虐" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100068,玩家数据[数字id].地图单位.标识)
              return
          end
  elseif 类型==138 then
          if 事件=="妖怪看打" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100069,玩家数据[数字id].地图单位.标识)
            return
          end
  elseif 类型==140 then
          if 事件=="在下心平气和，准备好变化(每次需要10点体力)" then
              if 玩家数据[数字id].角色.数据.体力<10 then
                添加最后对话(数字id,"你当前的体力不够，无法变化")
                return
              end
              if 玩家数据[数字id].角色.数据.变身数据~=nil then
                玩家数据[数字id].角色.数据.变身数据=nil
                玩家数据[数字id].角色.数据.变异=nil
                local 任务id = 玩家数据[数字id].角色:取任务(1)
                任务数据[任务id]=nil
                玩家数据[数字id].角色:取消任务(任务id)
              end
              local 造型 = 车迟变身卡范围[取随机数(1,#车迟变身卡范围)]
              玩家数据[数字id].角色.数据.体力=玩家数据[数字id].角色.数据.体力-10
              玩家数据[数字id].角色.数据.变身数据=造型
              玩家数据[数字id].角色.数据.变异=true
              玩家数据[数字id].角色:刷新信息()
              发送数据(玩家数据[数字id].连接id,37,{变身数据=玩家数据[数字id].角色.数据.变身数据,变异=玩家数据[数字id].角色.数据.变异})
              常规提示(数字id,"你心平气和，变化成功")
              发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
              发送数据(玩家数据[数字id].连接id,12)
              任务处理类:添加变身(数字id,9)
              地图处理类:更改模型(数字id,{[1]=玩家数据[数字id].角色.数据.变身数据,[2]=玩家数据[数字id].角色.数据.变异},1)
          end
  elseif 类型==141 then
          if 事件=="不知悔改，看我如何收服你" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100070,玩家数据[数字id].地图单位.标识)
              return
          end
  elseif 类型==151 then
    local 任务id = 玩家数据[数字id].角色:取任务(150)
    local 副本id=任务数据[任务id].副本id
    if 事件=="采摘木材" then
      if 取随机数()<=50 then
          战斗准备类:创建战斗(数字id,100112,任务id)
      else
            任务数据[任务id].装潢=任务数据[任务id].装潢+2
            玩家数据[数字id].采摘木材=nil
            副本数据.水陆大会.进行[副本id].装潢=副本数据.水陆大会.进行[副本id].装潢+2
            常规提示(数字id,"#Y完成了采摘木材，装潢任务进度+2")
            if 副本数据.水陆大会.进行[副本id].装潢>=10 and 副本数据.水陆大会.进行[副本id].邀请>=10 then
                for i,v in pairs(地图处理类.地图单位[6024]) do
                  if 地图处理类.地图单位[6024][i].名称 == "蟠桃树" and 任务数据[地图处理类.地图单位[6024][i].id].副本id == 副本id then
                    地图处理类:删除单位(6024,i)
                  end
                end
                副本数据.水陆大会.进行[副本id].进程=2
                任务处理类:设置水陆大会副本(副本id)
                发送数据(玩家数据[数字id].连接id,1501,{名称="道场督僧",模型="男人_方丈",对话="感谢少侠为水陆大会建设做出的贡献，道场已经建设完毕"})
            end
            玩家数据[数字id].角色:刷新任务跟踪()
      end
    end
  elseif 类型==156 then
          local 任务id = 玩家数据[数字id].角色:取任务(150)
          local 副本id=任务数据[任务id].副本id
          if 事件=="快送我过去" then
              if 副本数据.水陆大会.进行[副本id].进程==6 then
                  副本数据.水陆大会.进行[副本id].进程=7
                  任务处理类:设置水陆大会副本(副本id)
                  地图处理类:跳转地图(数字id,6026,54,91)
                  for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                   玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                  end
              end
          end
  elseif 类型==157 then
          if 事件=="妖孽找死" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100118,玩家数据[数字id].地图单位.标识)
              return
          end
  elseif 类型==158 then
    if 事件=="除魔卫道是我们应尽的职责" then
      local 副本id=任务数据[玩家数据[数字id].地图单位.标识].副本id
      local 任务id=玩家数据[数字id].角色:取任务(150)
        for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
          local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[i]
          任务处理类:完成水路大会奖励(临时id)
          玩家数据[临时id].角色:取消任务(玩家数据[临时id].角色:取任务(150))
          任务数据[任务id]=nil
        end
      地图处理类:跳转地图(数字id,1001,413,70)
    end
  elseif 类型==161 then
    if 事件=="好啊，快出题吧小考官。" then
      local 序列=取随机数(1,#科举题库)
      local 正确答案=科举题库[序列][4]
      local 随机答案={}
      for n=2,4 do
        随机答案[n-1]={答案=科举题库[序列][n],序列=取随机数(1,9999)}
      end
      table.sort(随机答案,function(a,b) return a.序列>b.序列 end )
      local 显示答案={}
      for n=1,3 do
        显示答案[n]=随机答案[n].答案
      end
      if 玩家数据[数字id].通天数据==nil then
        玩家数据[数字id].通天数据={题目=0,答案=0,正确答案=0}
      end
      玩家数据[数字id].通天数据={题目=科举题库[序列][1],答案=显示答案,正确答案=正确答案,任务id=玩家数据[数字id].地图单位.标识}
      玩家数据[数字id].通天对话=true
      添加最后对话(数字id,string.format("#W/%s", 玩家数据[数字id].通天数据.题目),玩家数据[数字id].通天数据.答案)
    end
  elseif 类型==162 then
    if 事件=="好可爱的小朋友，看的我，变！（会取消原有的变身效果）" then
      local 副本id=任务数据[玩家数据[数字id].地图单位.标识].副本id
      if 玩家数据[数字id].角色.数据.变身数据=="小毛头" or 玩家数据[数字id].角色.数据.变身数据=="小魔头" or 玩家数据[数字id].角色.数据.变身数据=="小仙灵" or 玩家数据[数字id].角色.数据.变身数据=="小丫丫" or 玩家数据[数字id].角色.数据.变身数据=="小精灵" or 玩家数据[数字id].角色.数据.变身数据=="小仙女" then
        常规提示(数字id,"当前已经变化造型，无需再变化")
      else
        if 玩家数据[数字id].角色.数据.变身数据~=nil then
          玩家数据[数字id].角色.数据.变身数据=nil
          玩家数据[数字id].角色.数据.变异=nil
          local 任务id = 玩家数据[数字id].角色:取任务(1)
          任务数据[任务id]=nil
          玩家数据[数字id].角色:取消任务(任务id)
        end
        local 造型 = "小毛头"
        if 玩家数据[数字id].角色.数据.种族=="仙" then
          if 玩家数据[数字id].角色.数据.性别=="女" then
            造型="小仙女"
          else
            造型="小仙灵"
          end
        elseif 玩家数据[数字id].角色.数据.种族=="魔" then
          if 玩家数据[数字id].角色.数据.性别=="女" then
            造型="小精灵"
          else
            造型="小魔头"
          end
        elseif 玩家数据[数字id].角色.数据.种族=="人" then
          if 玩家数据[数字id].角色.数据.性别=="女" then
            造型="小丫丫"
          end
        end
        玩家数据[数字id].角色.数据.变身数据=造型
        玩家数据[数字id].角色.数据.变异=nil
        玩家数据[数字id].角色:刷新信息()
        发送数据(玩家数据[数字id].连接id,37,{变身数据=玩家数据[数字id].角色.数据.变身数据,变异=玩家数据[数字id].角色.数据.变异})
        发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
        发送数据(玩家数据[数字id].连接id,12)
        任务处理类:添加变身(数字id,9)
        地图处理类:更改模型(数字id,{[1]=玩家数据[数字id].角色.数据.变身数据,[2]=玩家数据[数字id].角色.数据.变异},1)
        副本数据.通天河.进行[副本id].变身次数=副本数据.通天河.进行[副本id].变身次数+5
        if 副本数据.通天河.进行[副本id].变身次数>=5 then
          local 地图 = 任务数据[玩家数据[数字id].地图单位.标识].地图编号
          local 副本id = 任务数据[玩家数据[数字id].地图单位.标识].副本id
          for k,v in pairs(地图处理类.地图单位[地图]) do
              if  v.类型==162 and v.副本id ==副本id then
                地图处理类:删除单位(地图,k)
              end
          end
          副本数据.通天河.进行[副本id].进程=3
          任务处理类:设置通天河副本(副本id)
        end
        for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
         玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
        end
      end
    end
  elseif 类型==163 then
    if 事件=="触摸灵灯" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100125,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==164 then
    if 事件=="我这就前往" then
      local 副本id=任务数据[玩家数据[数字id].地图单位.标识].副本id
      副本数据.通天河.进行[副本id].进程=5
      任务处理类:设置通天河副本(副本id)
      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
        玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
      end
      玩家数据[数字id].地图单位=nil
    end
  elseif 类型==165 then
    if 事件=="小小河妖还这么嚣张？讨打！" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100126,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==166 then
    if 事件=="那就得罪了" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
      if 任务数据[玩家数据[数字id].地图单位.标识].名称=="散财童子" then
         任务数据[玩家数据[数字id].地图单位.标识].战斗=true
         战斗准备类:创建战斗(数字id+0,100127,玩家数据[数字id].地图单位.标识)
         玩家数据[数字id].地图单位=nil
         return
      else
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100128,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
      end
    end
  elseif 类型==169 then
    if 事件=="妖孽受死" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100130,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==170 then
    if 事件=="妖孽受死" and 触发条件(1,1,50,3) then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100135,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
    end
  elseif 类型==304 then
      local 等级 = 任务数据[玩家数据[数字id].地图单位.标识].等级 or 70
      if 事件=="我们准备好了" and 触发条件(1,1,{(等级-20),(等级+20)},3,"地煞星") then --(战斗,队伍,等级,人数,次数)
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,100037,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
          return
      end
  elseif 类型==305 then
    if 事件=="小小先锋还这么嚣张？讨打！" and 触发条件(1,1,69,3,"知了先锋") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100038,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end


  elseif 类型==306 then
    if 事件=="我来阻止你" and 触发条件(1,1,170,3,"创世佛屠") then --(战斗,队伍,等级,人数,次数)
       任务数据[玩家数据[数字id].地图单位.标识].战斗=true
       战斗准备类:创建战斗(数字id+0,100039,玩家数据[数字id].地图单位.标识)
       玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==309 then
        if 事件=="邪魔休要猖狂" and 触发条件(1,nil,69) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100045,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
          return
        end

  elseif 类型==310 then
        if 事件=="我放你妹" and 触发条件(1,1,155,3,"善恶如来") then --(战斗,队伍,等级,人数,次数)
           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
           战斗准备类:创建战斗(数字id+0,100057,玩家数据[数字id].地图单位.标识)
           玩家数据[数字id].地图单位=nil
           return
        end
  elseif 类型==315 then
    if 事件=="你觉得你能保护你所在的门派?找死!" and 触发条件(1,1,69,5,"门派入侵") then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100058,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
      end
  elseif 类型==314 then
          local 等级 = 任务数据[玩家数据[数字id].地图单位.标识].等级 or 70
          if 事件=="请星君赐教" and 触发条件(1,1,{(等级-20),(等级+20)},3,"天罡星") then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100056,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==318 then
    if 事件=="让我送你回鼠洞" and 触发条件(1,1,50,nil,"四墓灵鼠") then --(战斗,队伍,等级,人数,次数)
       任务数据[玩家数据[数字id].地图单位.标识].战斗=true
       战斗准备类:创建战斗(数字id+0,100060,玩家数据[数字id].地图单位.标识)
       玩家数据[数字id].地图单位=nil
       return
    end
  elseif 类型==336 then
    if 事件=="瞎子看招"  and 触发条件(1,1,69,3,"小小盲僧") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110047,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==348 then
    if 事件=="喝就喝、谁怕谁" and 触发条件(1,nil,50) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100106,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==349 then
    if 事件=="吃你个猪头啊，看打！" and 判断战斗() then
      战斗准备类:创建战斗(数字id+0,100107,玩家数据[数字id].地图单位.标识)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      任务数据[玩家数据[数字id].地图单位.标识].触发战斗玩家=数字id
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==350 then
          if 事件=="会不会说人话" and 判断战斗() then
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100109,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==351 then
          if 事件=="妖兽看招"and 判断战斗() then
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100110,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==352 then
          if 事件=="泼猴看打" and 判断战斗() then
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100113,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==355 then
    if 事件=="让我来消灭你" and 触发条件(1,1,60,3,"新冠病毒") then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,110021,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
    end
  elseif 类型==356 then
    if 事件=="小小年兽" and 触发条件(1,1,50,2) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100122,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==357 then
    if 事件=="打倒年兽" and 触发条件(1,1,69,2) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100123,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==358 then
    if 事件=="邪恶年兽休要猖狂" and 触发条件(1,1,任务数据[玩家数据[数字id].地图单位.标识].等级,2) then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,100124,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
    -------------------------------------------------------------------------------新加活动
  elseif 类型==359 then
      if 事件=="我来瞧瞧你的啥" and 触发条件(1,1,50,2,"天降灵猴") then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100051,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
      end


  elseif 类型==360 then
    if 事件=="我看你是找打" and 触发条件(1,1,50,2,"经验宝宝") then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100053,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
      end

  elseif 类型>=361 and 类型<=366 then
        if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
          local 战斗序号 = 100085 + (类型 - 361)
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,战斗序号,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
          return
        end
  -- elseif 类型==362 then
  --       if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
  --         任务数据[玩家数据[数字id].地图单位.标识].战斗=true
  --         战斗准备类:创建战斗(数字id+0,100086,玩家数据[数字id].地图单位.标识)
  --         玩家数据[数字id].地图单位=nil
  --         return
  --       end
  -- elseif 类型==363 then
  --       if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
  --           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
  --           战斗准备类:创建战斗(数字id+0,100087,玩家数据[数字id].地图单位.标识)
  --           玩家数据[数字id].地图单位=nil
  --           return
  --       end
  -- elseif 类型==364 then
  --       if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
  --           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
  --           战斗准备类:创建战斗(数字id+0,100088,玩家数据[数字id].地图单位.标识)
  --           玩家数据[数字id].地图单位=nil
  --           return
  --       end
  -- elseif 类型==365 then
  --       if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
  --           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
  --           战斗准备类:创建战斗(数字id+0,100089,玩家数据[数字id].地图单位.标识)
  --           玩家数据[数字id].地图单位=nil
  --         return
  --       end
  -- elseif 类型==366 then
  --       if 事件=="对对对。" and 触发条件(1,1,69,3,"王者荣耀") then --(战斗,队伍,等级,人数,次数)
  --           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
  --           战斗准备类:创建战斗(数字id+0,100090,玩家数据[数字id].地图单位.标识)
  --           玩家数据[数字id].地图单位=nil
  --           return
  --       end
  elseif 类型==367 then
        if 事件=="请星官赐教" and 触发条件(1,1,69,3,"星官") then --(战斗,队伍,等级,人数,次数)
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,100054,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
          return
        end
 elseif 类型==368 then
    if 事件=="影青龙，我这就来干死你！" and 触发条件(1,1,{69,89},5,"圣兽残魂") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110016,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==369 then
    if 事件=="影朱雀，我这就来干死你！" and 触发条件(1,1,{89,109},5,"圣兽残魂") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110017,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==370 then
    if 事件=="影白虎，我这就来干死你！" and 触发条件(1,1,{109,129},5,"圣兽残魂") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110018,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==371 then
    if 事件=="影玄武，我这就来干死你！" and 触发条件(1,1,{129,159},5,"圣兽残魂") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110019,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型==372 then
    if 事件=="影麒麟，我这就来干死你！" and 触发条件(1,1,{159,175},5,"圣兽残魂") then --(战斗,队伍,等级,人数,次数)
      任务数据[玩家数据[数字id].地图单位.标识].战斗=true
      战斗准备类:创建战斗(数字id+0,110020,玩家数据[数字id].地图单位.标识)
      玩家数据[数字id].地图单位=nil
      return
    end

  elseif 类型==373 then
    if 事件=="宝物给我拿过来" and 触发条件(1,1,60,5,"财神爷") then --(战斗,队伍,等级,人数,次数)
       任务数据[玩家数据[数字id].地图单位.标识].战斗=true
       战斗准备类:创建战斗(数字id+0,110022,玩家数据[数字id].地图单位.标识)
       玩家数据[数字id].地图单位=nil
      return
    end


  elseif 类型==374 then
    if 事件=="乐意之极" and 触发条件(1,1) and 判断任务(374) then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,110029,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
      return
    end
  elseif 类型>=375 and 类型<=384 then
      if 事件=="交出仙缘(战斗)" and 判断玩家() and 判断战斗() then --(战斗,队伍,等级,人数,次数)
            local 战斗序号 = 110030 + (类型 - 375)
            if 类型==383 then
                战斗序号 = 110039
            elseif 类型==384 then
                战斗序号 = 110040
            end
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,战斗序号,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
      elseif 事件=="交出银两放你一马！" and 判断玩家() and 判断战斗() then --(战斗,队伍,等级,人数,次数)
              local 添加银子 = 200000 + (类型 - 375)* 50000
              玩家数据[数字id].角色:添加银子(添加银子,"仙缘任务",1)
              玩家数据[数字id].角色:取消任务(玩家数据[数字id].地图单位.标识)
              地图处理类:删除单位(任务数据[玩家数据[数字id].地图单位.标识].地图编号,任务数据[玩家数据[数字id].地图单位.标识].编号)
              任务数据[玩家数据[数字id].地图单位.标识]=nil
              return
      end
  elseif 类型==385 then
          if 事件=="让我来收服你" and 触发条件(1,1,60,3,"十二生肖") then --(战斗,队伍,等级,人数,次数)
             任务数据[玩家数据[数字id].地图单位.标识].战斗=true
             战斗准备类:创建战斗(数字id+0,110041,玩家数据[数字id].地图单位.标识)
             玩家数据[数字id].地图单位=nil
             return
          end
  elseif 类型==386 then
          if 事件=="桐人，挡我一下试试！" and 触发条件(1,1,100,3,"桐人") then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,110042,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==387 then
          if 事件=="魔化铜人，挡我一下试试！" and 触发条件(1,1,100,3,"魔化桐人") then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,110043,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==388 then
          if 事件=="混世魔王，挡我一下试试！" and 触发条件(1,1,100,3,"混世魔王") then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,110044,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==389 then
          if 事件=="我看你是找打" and 触发条件(1,1,109,1,"万象福") then --(战斗,队伍,等级,人数,次数)
             任务数据[玩家数据[数字id].地图单位.标识].战斗=true
             战斗准备类:创建战斗(数字id+0,110045,玩家数据[数字id].地图单位.标识)
             玩家数据[数字id].地图单位=nil
             return
          end
  elseif 类型==399 then
        if 事件=="新年快乐！蛇年大吉！" and 触发条件(1,1,69,1,"新春快乐") then --(战斗,队伍,等级,人数,次数)
           任务数据[玩家数据[数字id].地图单位.标识].战斗=true
           战斗准备类:创建战斗(数字id+0,110046,玩家数据[数字id].地图单位.标识)
           玩家数据[数字id].地图单位=nil
           return
        end

  elseif 类型==212 then
    if 事件=="小乖乖赶紧回家" and 触发条件(1,1,50,1) and 判断任务(212) then --(战斗,队伍,等级,人数,次数)
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,100052,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
          return
    end

    ------------摩托新增齐天大圣副本
  elseif 类型==193 then
          if 事件=="我看谁敢" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
              任务数据[玩家数据[数字id].地图单位.标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100214,玩家数据[数字id].地图单位.标识)
              玩家数据[数字id].地图单位=nil
              return
          end
  elseif 类型==195 then
          if 事件=="找死" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100217,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==197 then
        if 事件=="口出狂言" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100218,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        elseif 事件=="比划比划" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            战斗准备类:创建战斗(数字id+0,100219,玩家数据[数字id].地图单位.标识)
            玩家数据[数字id].地图单位=nil
            return
        end
  elseif 类型==185 then
        if 事件=="定" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
          任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id+0,100149,玩家数据[数字id].地图单位.标识)
          玩家数据[数字id].地图单位=nil
          return
        end
  elseif 类型==187 then
          if 事件=="吃老孙一棒" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
            任务数据[玩家数据[数字id].地图单位.标识].战斗=true
            if 任务数据[玩家数据[数字id].地图单位.标识].名称 == "天兵统领" then
              战斗准备类:创建战斗(数字id+0,100154,玩家数据[数字id].地图单位.标识)
            else
              战斗准备类:创建战斗(数字id+0,100155,玩家数据[数字id].地图单位.标识)
            end
            玩家数据[数字id].地图单位=nil
            return
          end
  elseif 类型==188 then --(战斗,队伍,等级,人数,次数)
      if 事件=="放马过来" and 触发条件(1,1,69,1) then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id+0,100156,玩家数据[数字id].地图单位.标识)
        玩家数据[数字id].地图单位=nil
        return
      end
  elseif 类型==5594 then
      if 事件=="进入战备区域" then
         帮战活动类:进入比赛场景(数字id)
      end
  elseif 类型==5595 then
      if 事件=="进入帮战战场" then
        if 帮战活动类.活动开关 then
          帮战活动类:进入杀戮场景(数字id)
        else
          local 帮派id = 玩家数据[数字id].角色.数据.帮派数据.编号
          if 帮战活动类.获胜帮派 and 帮战活动类.获胜帮派==帮派id and 帮战活动类.宝箱开关 then
             local 临时yx=地图处理类.地图坐标[6011]:取随机点()
             地图处理类:跳转地图(数字id,6011,临时yx.x,临时yx.y)
          else
              常规提示(数字id,"当前无法进入战场")
          end
        end
      end
   elseif 类型==5597 then
      if 事件=="送我出去" then
         地图处理类:跳转地图(数字id,1001,200,110)
      end
 elseif 类型 >= 1312 and 类型<= 1315 then
         长安保卫战:怪物对话处理(数字id+0,名称,事件,类型,玩家数据[数字id].地图单位.标识)

elseif 类型 >= 390 and 类型<= 397 then
        嘉年华:怪物对话处理(数字id+0,名称,事件,类型,玩家数据[数字id].地图单位.标识)
 elseif 类型==398 then
       归墟活动:对话处理(数字id+0,名称,事件,类型,玩家数据[数字id].地图单位.标识)
 elseif 类型==16 then
        if 任务数据[玩家数据[数字id].地图单位.标识] == nil then return  end
        if 事件=="我来试试" and 触发条件(nil,1,69,1) and 判断任务(17) then --(战斗,队伍,等级,人数,次数)
        --  任务数据[玩家数据[数字id].地图单位.标识].战斗=true
          战斗准备类:创建战斗(数字id,100059,玩家数据[数字id].角色:取任务(17))
          玩家数据[数字id].地图单位=nil
          return
        end
  elseif 类型==18 then
      if 任务数据[玩家数据[数字id].地图单位.标识] == nil then return end
      if 事件=="接受考验" and 触发条件(1,1,69,1) and 判断任务(18) then --(战斗,队伍,等级,人数,次数)
        任务数据[玩家数据[数字id].地图单位.标识].战斗=true
        战斗准备类:创建战斗(数字id,100061,玩家数据[数字id].角色:取任务(18))
        玩家数据[数字id].地图单位=nil
        return
      end
  end
end




return 活动处理