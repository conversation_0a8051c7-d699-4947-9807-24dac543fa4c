/**
 * 测试814的字节表示
 */

console.log('=== 814字节表示测试 ===');

// 814的十六进制表示
console.log('814的十六进制:', 814.toString(16)); // 32e

// 小端序表示（低字节在前）
const buffer = Buffer.allocUnsafe(2);
buffer.writeUInt16LE(814, 0);
console.log('814小端序字节:', Array.from(buffer).map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()).join(' '));

// 大端序表示（高字节在前）
buffer.writeUInt16BE(814, 0);
console.log('814大端序字节:', Array.from(buffer).map(b => '0x' + b.toString(16).padStart(2, '0').toUpperCase()).join(' '));

// 验证0x2E 0x03
const testBuffer = Buffer.from([0x2E, 0x03]);
console.log('0x2E 0x03转为数值:', testBuffer.readUInt16LE(0));
