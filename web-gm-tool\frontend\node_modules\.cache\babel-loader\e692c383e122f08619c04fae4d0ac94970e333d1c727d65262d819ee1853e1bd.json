{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport InsuranceOutlinedSvg from \"@ant-design/icons-svg/es/asn/InsuranceOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar InsuranceOutlined = function InsuranceOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: InsuranceOutlinedSvg\n  }));\n};\n\n/**![insurance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0MS42IDMwNi44TDQwMyAyODguNmE2LjEgNi4xIDAgMDAtOC40IDMuN2MtMTcuNSA1OC41LTQ1LjIgMTEwLjEtODIuMiAxNTMuNmE2LjA1IDYuMDUgMCAwMC0xLjIgNS42bDEzLjIgNDMuNWMxLjMgNC40IDcgNS43IDEwLjIgMi40IDcuNy04LjEgMTUuNC0xNi45IDIzLjEtMjZWNjU2YzAgNC40IDMuNiA4IDggOEg0MDNjNC40IDAgOC0zLjYgOC04VjM5My4xYTQyOS4yIDQyOS4yIDAgMDAzMy42LTc5YzEtMi45LS4zLTYtMy03LjN6bTI2LjggOS4ydjEyNy4yYzAgNC40IDMuNiA4IDggOGg2NS45djE4LjZoLTk0LjljLTQuNCAwLTggMy42LTggOHYzNS42YzAgNC40IDMuNiA4IDggOGg1NS4xYy0xOS4xIDMwLjgtNDIuNCA1NS43LTcxIDc2YTYgNiAwIDAwLTEuNiA4LjFsMjIuOCAzNi41YzEuOSAzLjEgNi4yIDMuOCA4LjkgMS40IDMxLjYtMjYuOCA1OC43LTYyLjkgODAuNi0xMDcuNnYxMjBjMCA0LjQgMy42IDggOCA4aDM2LjJjNC40IDAgOC0zLjYgOC04VjUzNmMyMS4zIDQxLjcgNDcuNSA3Ny41IDc4LjEgMTA2LjkgMi42IDIuNSA2LjggMi4xIDguOS0uN2wyNi4zLTM1LjNjMi0yLjcgMS40LTYuNS0xLjItOC40LTMwLjUtMjIuNi01NC4yLTQ3LjgtNzIuMy03Ni45aDU5YzQuNCAwIDgtMy42IDgtOFY0NzhjMC00LjQtMy42LTgtOC04aC05OC44di0xOC42aDY2LjdjNC40IDAgOC0zLjYgOC04VjMxNmMwLTQuNC0zLjYtOC04LThINDc2LjRjLTQuNCAwLTggMy42LTggOHptNTEuNSA0Mi44aDk3Ljl2NDEuNmgtOTcuOXYtNDEuNnptMzQ3LTE4OC45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(InsuranceOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'InsuranceOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "InsuranceOutlinedSvg", "AntdIcon", "InsuranceOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/InsuranceOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport InsuranceOutlinedSvg from \"@ant-design/icons-svg/es/asn/InsuranceOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar InsuranceOutlined = function InsuranceOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: InsuranceOutlinedSvg\n  }));\n};\n\n/**![insurance](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ0MS42IDMwNi44TDQwMyAyODguNmE2LjEgNi4xIDAgMDAtOC40IDMuN2MtMTcuNSA1OC41LTQ1LjIgMTEwLjEtODIuMiAxNTMuNmE2LjA1IDYuMDUgMCAwMC0xLjIgNS42bDEzLjIgNDMuNWMxLjMgNC40IDcgNS43IDEwLjIgMi40IDcuNy04LjEgMTUuNC0xNi45IDIzLjEtMjZWNjU2YzAgNC40IDMuNiA4IDggOEg0MDNjNC40IDAgOC0zLjYgOC04VjM5My4xYTQyOS4yIDQyOS4yIDAgMDAzMy42LTc5YzEtMi45LS4zLTYtMy03LjN6bTI2LjggOS4ydjEyNy4yYzAgNC40IDMuNiA4IDggOGg2NS45djE4LjZoLTk0LjljLTQuNCAwLTggMy42LTggOHYzNS42YzAgNC40IDMuNiA4IDggOGg1NS4xYy0xOS4xIDMwLjgtNDIuNCA1NS43LTcxIDc2YTYgNiAwIDAwLTEuNiA4LjFsMjIuOCAzNi41YzEuOSAzLjEgNi4yIDMuOCA4LjkgMS40IDMxLjYtMjYuOCA1OC43LTYyLjkgODAuNi0xMDcuNnYxMjBjMCA0LjQgMy42IDggOCA4aDM2LjJjNC40IDAgOC0zLjYgOC04VjUzNmMyMS4zIDQxLjcgNDcuNSA3Ny41IDc4LjEgMTA2LjkgMi42IDIuNSA2LjggMi4xIDguOS0uN2wyNi4zLTM1LjNjMi0yLjcgMS40LTYuNS0xLjItOC40LTMwLjUtMjIuNi01NC4yLTQ3LjgtNzIuMy03Ni45aDU5YzQuNCAwIDgtMy42IDgtOFY0NzhjMC00LjQtMy42LTgtOC04aC05OC44di0xOC42aDY2LjdjNC40IDAgOC0zLjYgOC04VjMxNmMwLTQuNC0zLjYtOC04LThINDc2LjRjLTQuNCAwLTggMy42LTggOHptNTEuNSA0Mi44aDk3Ljl2NDEuNmgtOTcuOXYtNDEuNnptMzQ3LTE4OC45TDUyNy4xIDU0LjFDNTIzIDUyLjcgNTE3LjUgNTIgNTEyIDUycy0xMSAuNy0xNS4xIDIuMUwxNTcuMSAxNjkuOWMtOC4zIDIuOC0xNS4xIDEyLjQtMTUuMSAyMS4ydjQ4Mi40YzAgOC44IDUuNyAyMC40IDEyLjYgMjUuOUw0OTkuMyA5NjhjMy41IDIuNyA4IDQuMSAxMi42IDQuMXM5LjItMS40IDEyLjYtNC4xbDM0NC43LTI2OC42YzYuOS01LjQgMTIuNi0xNyAxMi42LTI1LjlWMTkxLjFjLjItOC44LTYuNi0xOC4zLTE0LjktMjEuMnpNODEwIDY1NC4zTDUxMiA4ODYuNSAyMTQgNjU0LjNWMjI2LjdsMjk4LTEwMS42IDI5OCAxMDEuNnY0MjcuNnoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(InsuranceOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'InsuranceOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,oBAAoB,MAAM,gDAAgD;AACjF,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC7D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,iBAAiB,CAAC;AAC9D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,mBAAmB;AAC3C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}