local  经脉处理类= class()

function 经脉处理类:初始化()
self.门派= "无"
self.当前经脉 = "无"
self.玩家id = 0
self.已学经脉 ={}
self.乾元丹 ={乾元丹=0,附加乾元丹=0,剩余乾元丹=0,可换乾元丹=0}
end





function 经脉处理类:加载数据(id)
   玩家数据[id].奇经八脉 = {}
   玩家数据[id].奇经特效 = {}
   self.玩家id = id
   self.门派= 玩家数据[id].角色.数据.门派
   self.当前经脉 = 玩家数据[id].角色.数据.经脉流派
   self.乾元丹 = 玩家数据[id].角色.数据.乾元丹
  if self.门派~=nil and self.门派~="" and self.门派~="无门派"  and self.门派~="无" then
     if 玩家数据[id].角色.数据.经脉流派 ==nil then
         local 现有流派 = self:取经脉流派()
         local 当前流派= {}
         for k,v in pairs(现有流派) do
           当前流派[#当前流派+1] = k
         end
        玩家数据[id].角色.数据.经脉流派 = 当前流派[1]
        self.当前经脉 = 玩家数据[id].角色.数据.经脉流派
     end

    玩家数据[id].奇经特效 = self:取奇经八脉特效()
    if  玩家数据[id].角色.数据.奇经八脉[self.当前经脉]~= nil then
       self.已学经脉 = 玩家数据[id].角色.数据.奇经八脉[self.当前经脉]
       local  取经脉配置 = self:取奇经八脉()
      for i=1,#self.已学经脉 do
         玩家数据[id].奇经八脉[取经脉配置[self.已学经脉[i]]] = 1
      end
    end
  end
end




function 经脉处理类:是否可学(当前,位置)
  if 当前==nil or 位置==nil then return false end
  local 经脉={{1,2,3},{4,5,6},{7,8,9},{10,11,12},{13,14,15},{16,17,18},{19,20,21}}
  for i=1,3 do
    if 经脉[当前][i] == 位置 then
      return true
    end
  end
  return false
end




function 经脉处理类:兑换乾元丹(id)
  local 附加乾元丹=玩家数据[id].角色.数据.乾元丹.附加乾元丹
  local 乾元丹消耗=取乾元丹消耗(附加乾元丹+1)
  if 取银子(id) < 乾元丹消耗.金钱 then
      常规提示(id,"#Y/你当前的银两不够兑换乾元丹哦！")
      return
  end
  if 玩家数据[id].角色.数据.当前经验 < 乾元丹消耗.经验 then
      常规提示(id,"#Y/你当前的经验不够兑换乾元丹哦！")
      return
  end
  if 玩家数据[id].角色.数据.乾元丹.附加乾元丹 >= 玩家数据[id].角色.数据.乾元丹.可换乾元丹 then
      常规提示(id,"#Y/你当前不满足兑换乾元丹的条件哦！")
      return
  end
  玩家数据[id].角色:扣除银子(乾元丹消耗.金钱,"乾元丹消耗",1)
  玩家数据[id].角色.数据.当前经验=玩家数据[id].角色.数据.当前经验-乾元丹消耗.经验
   常规提示(id,"#Y/你失去了#R/"..乾元丹消耗.经验.."#Y/经验")
  玩家数据[id].角色.数据.乾元丹.附加乾元丹=玩家数据[id].角色.数据.乾元丹.附加乾元丹+1
  玩家数据[id].角色.数据.乾元丹.剩余乾元丹=玩家数据[id].角色.数据.乾元丹.剩余乾元丹+1
  self.乾元丹 =  玩家数据[id].角色.数据.乾元丹
  添加最后对话(id,"兑换乾元丹成功！")

end


function 经脉处理类:切换奇经八脉处理(id,经脉,多角色)
  if 玩家数据[id].角色.数据.体力<100 then
     常规提示(id,"#Y/切换流派需要100体力,你的体力不够",多角色)
  else
      local 判断 = false
      local 现有流派 = self:取经脉流派()
      for k,v in pairs(现有流派) do
          if 经脉 == k then
             判断 = true
          end
      end
      if 判断 then
          玩家数据[id].角色.数据.经脉流派 = 经脉
          玩家数据[id].角色.数据.体力 = 玩家数据[id].角色.数据.体力 - 100
          if 玩家数据[id].角色.数据.自动指令~=nil and 玩家数据[id].角色.数据.自动指令.类型~="" and  玩家数据[id].奇经特效~=nil and  玩家数据[id].奇经特效[玩家数据[id].角色.数据.自动指令.类型] then
             玩家数据[id].角色.数据.自动指令={下达=false,类型="攻击",目标=0,敌我=0,参数="",附加=""}
          end
          self:加载数据(id)
          玩家数据[id].角色:刷新信息()
          if 多角色 ==nil then
            发送数据(玩家数据[id].连接id,77,玩家数据[id].角色.数据)
          end
          常规提示(id,"#Y/切换经脉成功",多角色)
      else
          常规提示(id,"#Y/数据错误请重新登陆游戏",多角色)
      end
  end



end



function 经脉处理类:增加奇经八脉(内容,多角色)
  if  #self.已学经脉>=7 then
     常规提示(self.玩家id,"已学完该派系经脉",多角色)
   elseif #self.已学经脉>=3 and 玩家数据[self.玩家id].角色.数据.等级<= 69 then
       常规提示(self.玩家id,"当前等级已达技能学习上限,切换派系后继续学习",多角色)
   elseif #self.已学经脉>=4 and 玩家数据[self.玩家id].角色.数据.等级<= 89 then
       常规提示(self.玩家id,"当前等级已达技能学习上限,切换派系后继续学习",多角色)
   elseif #self.已学经脉>=5 and 玩家数据[self.玩家id].角色.数据.等级<= 99 then
       常规提示(self.玩家id,"当前等级已达技能学习上限,切换派系后继续学习",多角色)
   elseif #self.已学经脉>=6 and 玩家数据[self.玩家id].角色.数据.等级< 109 then
       常规提示(self.玩家id,"当前等级已达技能学习上限,切换派系后继续学习",多角色)
  elseif 内容.序列==20 and self.乾元丹.剩余乾元丹<3 then
       常规提示(self.玩家id,"学习该经脉需要3颗乾元丹",多角色)
  else
       local 位置 = 内容.序列
       local 排列 = #self.已学经脉+1
      if self.乾元丹.剩余乾元丹~=nil and self.乾元丹.剩余乾元丹>0 and self:是否可学(排列,位置) then
          self.已学经脉[#self.已学经脉+1] = 位置
          玩家数据[self.玩家id].角色.数据.奇经八脉[self.当前经脉] = self.已学经脉
          if 位置 == 20 then
              玩家数据[self.玩家id].角色.数据.乾元丹.剩余乾元丹 = 玩家数据[self.玩家id].角色.数据.乾元丹.剩余乾元丹 - 3
              玩家数据[self.玩家id].角色.数据.乾元丹.乾元丹= 玩家数据[self.玩家id].角色.数据.乾元丹.乾元丹 + 3
          else
              玩家数据[self.玩家id].角色.数据.乾元丹.剩余乾元丹 = 玩家数据[self.玩家id].角色.数据.乾元丹.剩余乾元丹 - 1
              玩家数据[self.玩家id].角色.数据.乾元丹.乾元丹= 玩家数据[self.玩家id].角色.数据.乾元丹.乾元丹 + 1
          end
          self.乾元丹 = 玩家数据[self.玩家id].角色.数据.乾元丹
          self:加载数据(self.玩家id)
          常规提示(self.玩家id,"学习该经脉技能成功",多角色)
           玩家数据[self.玩家id].角色:刷新信息("1")
      else
        常规提示(self.玩家id,"无法学习该经脉技能",多角色)
      end
   end
   if 多角色==nil then
    发送数据(玩家数据[self.玩家id].连接id,77,玩家数据[self.玩家id].角色.数据)
    end
end


function 经脉处理类:清空奇经八脉(多角色)
  if  玩家数据[self.玩家id].角色.数据.银子<500000 then
      常规提示(self.玩家id,"重置经脉需要50万银子",多角色)
  else
    if self.已学经脉 ~=nil then
       self.已学经脉 ={}
       玩家数据[self.玩家id].角色.数据.银子 = 玩家数据[self.玩家id].角色.数据.银子 - 500000
       玩家数据[self.玩家id].角色.数据.奇经八脉 = self:取经脉流派()
       玩家数据[self.玩家id].奇经八脉 ={}
      self.乾元丹.剩余乾元丹=self.乾元丹.剩余乾元丹 + self.乾元丹.乾元丹
      self.乾元丹.乾元丹=0
      if self.乾元丹.剩余乾元丹 > self:乾元丹数量(玩家数据[self.玩家id].角色.数据.等级) then
        self.乾元丹.剩余乾元丹 = self:乾元丹数量(玩家数据[self.玩家id].角色.数据.等级)
      end
      玩家数据[self.玩家id].角色.数据.乾元丹 = self.乾元丹
      玩家数据[self.玩家id].角色:刷新信息("1")
      常规提示(self.玩家id,"#Y/你失去了#R/500000#Y/两银子",多角色)
      常规提示(self.玩家id,"#Y/恭喜你，经脉重置成功！",多角色)
    end
  end
if 多角色==nil then
   发送数据(玩家数据[self.玩家id].连接id,77,玩家数据[self.玩家id].角色.数据)
end

end

function 经脉处理类:清空当前奇经八脉(多角色)
  if  玩家数据[self.玩家id].角色.数据.银子<100000 then
      常规提示(self.玩家id,"重置经脉需要10万银子",多角色)
  else
      if self.已学经脉 ~=nil then
         玩家数据[self.玩家id].角色.数据.银子 = 玩家数据[self.玩家id].角色.数据.银子 - 100000
         玩家数据[self.玩家id].角色.数据.奇经八脉[self.当前经脉] = {}
         玩家数据[self.玩家id].奇经八脉 ={}
         if #self.已学经脉==7 then
            self.乾元丹.剩余乾元丹=self.乾元丹.剩余乾元丹 + 9
            self.乾元丹.乾元丹=self.乾元丹.乾元丹- 9
         else
            self.乾元丹.剩余乾元丹=self.乾元丹.剩余乾元丹 + #self.已学经脉
            self.乾元丹.乾元丹=self.乾元丹.乾元丹- #self.已学经脉
         end
          self.已学经脉 ={}
        if self.乾元丹.剩余乾元丹 > self:乾元丹数量(玩家数据[self.玩家id].角色.数据.等级) then
          self.乾元丹.剩余乾元丹 = self:乾元丹数量(玩家数据[self.玩家id].角色.数据.等级)
        end
        玩家数据[self.玩家id].角色.数据.乾元丹 = self.乾元丹
        玩家数据[self.玩家id].角色:刷新信息("1")
        常规提示(self.玩家id,"#Y/你失去了#R/100000#Y/两银子",多角色)
        常规提示(self.玩家id,"#Y/恭喜你，当前经脉重置成功！",多角色)
      end
  end
  if 多角色==nil then
     发送数据(玩家数据[self.玩家id].连接id,77,玩家数据[self.玩家id].角色.数据)
   end
end



function 经脉处理类:乾元丹数量(等级)
   if 等级<89 then
       return 3
    elseif 等级>=89 and  等级<109 then
       return 6
    elseif 等级>=109 and 等级<129 then
       return 9
    elseif 等级>=129 and 等级<155 then
       return 12
    elseif 等级>=155 and 等级<159 then
       return 15
    elseif 等级>=159 and 等级<164  then
       return 18
     elseif 等级>=164 and 等级<168  then
       return 21
    elseif 等级>=168 and 等级<171  then
       return 24
    elseif 等级>=171 then
       return 36
    end

end




function 经脉处理类:取经脉是否有(名称)
  for n, v in pairs(玩家数据[self.玩家id].奇经八脉) do
    if 名称 == n then
       return true
     end
  end
  return  false
end




function 经脉处理类:取经脉流派()
  if  self.门派== "大唐官府" then
     return  {浴血豪侠={},无双战神={},虎贲上将={}}
  elseif  self.门派== "方寸山" then
     return  {拘灵散修={},伏魔天师={},五雷正宗={}}
  elseif  self.门派== "女儿村" then
     return  {绝代妖娆={},花雨伊人={},妙舞佳人={}}
  elseif  self.门派== "化生寺" then
     return  {杏林妙手={},护法金刚={},无量尊者={}}
  elseif  self.门派== "盘丝洞" then
     return  {风华舞圣={},迷情妖姬={},百媚魔姝={}}
  elseif  self.门派== "阴曹地府" then
     return  {勾魂阎罗={},六道魍魉={},诛刑毒师={}}
  elseif  self.门派== "魔王寨" then
     return  {平天大圣={},盖世魔君={},风火妖王={}}
  elseif  self.门派== "狮驼岭" then
     return  {嗜血狂魔={},万兽之王={},狂怒斗兽={}}
  elseif  self.门派== "天宫" then
     return  {镇妖神使={},踏雷天尊={},霹雳真君={}}
  elseif  self.门派== "普陀山" then
     return  {莲台仙子={},五行咒师={},落伽神女={}}
  elseif  self.门派== "五庄观" then
     return  {清心羽客={},乾坤力士={},万寿真仙={}}
  elseif  self.门派== "龙宫" then
     return  {海中蛟虬={},云龙真身={},沧海潜龙={}}
  elseif  self.门派== "神木林" then
     return  {通天法王={},巫影祭司={},灵木药宗={}}
  elseif  self.门派== "凌波城" then
    return   {九天武圣={},灵霄斗士={},风云战将={}}
  elseif  self.门派== "无底洞" then
     return  {妙谛金莲={},摄魂迷影={},幽冥巫煞={}}
  elseif  self.门派== "九黎城" then
      return  {铁火战魔={}}


  end
end


function 经脉处理类:取奇经八脉()
    if  self.门派== "大唐官府" then
      if  self.当前经脉 == "浴血豪侠" then
        return  {"目空","风刃","扶阵","翩鸿","勇武","长驱直入","杀意","贪心","静岳","干将","勇念","神凝","执剑","不惊","傲视","破空","历战","安神","额外能力","无敌","浴血豪侠"}
        elseif self.当前经脉 == "无双战神" then
        return {"目空","勇进","突刺","翩鸿","勇武","长驱直入","亢强","贪心","静岳","干将","勇念","神凝","执剑","不惊","突进","乘胜","孤勇","熟练","额外能力","破军","无双战神"}
        elseif self.当前经脉 == "虎贲上将" then
        return {"潜心","笃志","昂扬","效法","追戮","烈光","摧枯拉朽","肃杀","历兵","怒伤","奉还","催迫","攻伐","暴突","诛伤","灵能","奋战","破刃","额外能力","披挂上阵","虎贲上将"}
      end
    elseif  self.门派== "方寸山" then
      if  self.当前经脉 == "拘灵散修" then
        return {"雷动","苦缠","灵咒","黄粱","制约","必果","补缺","不倦","精炼","化身","调息","幻变","斗法","吐纳","专神","鬼念","灵威","碎甲","额外能力","顺势而为","拘灵散修"}
        elseif self.当前经脉 == "伏魔天师" then
        return{"驱雷","策电","雷动","鬼恸","穿透","余悸","宝诀","妙用","不灭","化身","怒霆","批亢","顺势","炼魂","吐纳","灵能","囚笼","摧心","额外能力","钟馗论道","伏魔天师"}
        elseif self.当前经脉 == "五雷正宗" then
        return{"五雷挪移","雷动","天箓","咒诀","穿透","符威","宝诀","妙用","不灭","雷法翻天","雷鸣","雷法倒海","顺势","神机","吐纳","造化","囚笼","摧心","额外能力","奇门","五雷正宗"}
      end


    elseif  self.门派== "女儿村" then
      if  self.当前经脉 == "绝代妖娆" then
        return {"独尊","暗伤","重明","倩影","花舞","风行","傲娇","花护","空灵","叶护","国色","轻霜","抑怒","机巧","毒雾","嫣然","磐石","倾国","额外能力","碎玉弄影","绝代妖娆"}
        elseif self.当前经脉 == "花雨伊人" then
        return{"涂毒","杏花","暗伤","淬芒","花舞","暗刃","傲娇","花护","天香","轻霜","花影","百花","毒雾","毒引","余韵","磐石","飞花","花殇","额外能力","鸿渐于陆","花雨伊人"}
       elseif self.当前经脉 == "妙舞佳人" then
        return{"天籁","即兴","夺目","独舞","静心","嫣然曼舞","翻飞","水漾","惊鸿起舞","矫健","花雨","跃动","幽美","风姿","风回旋舞","纷舞","轻霜","映日妙舞","额外能力","余韵索心","妙舞佳人"}

      end
    elseif  self.门派== "化生寺" then
      if  self.当前经脉 == "杏林妙手" then
        return{"销武","止戈","圣手","妙手","仁心","化瘀","佛显","心韧","归气","天照","舍利","佛佑","佛法","佛性","妙悟","慈心","虔诚","佛缘","额外能力","渡劫金身","杏林妙手"}
        elseif self.当前经脉 == "护法金刚" then
        return{"施他","佛屠","销武","聚念","仁心","磅礴","佛显","心韧","归气","感念","舍利","无碍","佛法","佛性","妙悟","慈心","映法","流刚","额外能力","诸天看护","护法金刚"}
        elseif self.当前经脉 == "无量尊者" then
        return {"诵律","授业","修习","诵经","悲悯","解惑","持戒","生花","悟彻","抚琴","舍利","静气","自在","无量","慧定","金刚","达摩","韦陀","额外能力","坐禅","无量尊者"}
      end
    elseif  self.门派== "盘丝洞" then
      if  self.当前经脉 == "风华舞圣" then
        return{"粘附","妖气","怜心","迷瘴","鼓乐","魔音","玲珑","安抚","丹香","迷梦","忘川","连绵","情劫","绝殇","绵密","结阵","媚态","绝媚","额外能力","落花成泥","风华舞圣"}
        elseif self.当前经脉 == "迷情妖姬" then
        return{"粘附","妖气","怜心","迷瘴","鼓乐","忘忧","玲珑","安抚","倾情","连绵","忘川","意乱","情劫","魔瘴","迷意","结阵","绝媚","利刃","额外能力","偷龙转凤","迷情妖姬"}
        elseif self.当前经脉 == "百媚魔姝" then
        return{"粘附","杀戮","罗网","天网","凌弱","制怒","狂击","千蛛","引诛","附骨","亡缚","罗刹","障眼","连绵","意乱","结阵","牵魂蛛丝","扑袭","额外能力","绝命毒牙","百媚魔姝"}
      end
    elseif  self.门派== "阴曹地府" then
      if  self.当前经脉 == "勾魂阎罗" then
        return{"阎罗","回旋","夜行","入骨","聚魂","拘魄","索魂","伤魂","克敌","黄泉","幽冥","冥视","幽光","泉暴","鬼火","魂飞","汲魂","扼命","额外能力","魍魉追魂","勾魂阎罗"}
        elseif self.当前经脉 == "六道魍魉" then
        return{"阎罗","回旋","夜行","聚魂","狱火","六道","索魂","伤魂","百炼","追命","幽冥","百爪狂杀","咒令","泉暴","鬼火","恶焰","汲魂","噬毒","额外能力","夜之王者","六道魍魉"}
        elseif self.当前经脉 == "诛刑毒师" then
        return{"毒炽","回旋","阴翳","聚魂","破毒","入魂","毒慑","破印","瘴幕","无赦咒令","幽冥","通暝","狂宴","鬼火","轮回","追命","汲魂","恶焰","额外能力","生杀予夺","诛刑毒师"}
      end

    elseif  self.门派== "魔王寨" then
      if  self.当前经脉 == "平天大圣" then
        return{"充沛","震怒","激怒","蚀天","邪火","赤暖","火神","震天","真炎","神焰","崩摧","焚尽","咆哮","狂月","燃魂","威吓","连营","魔心","额外能力","魔焰滔天","平天大圣"}
        elseif self.当前经脉 == "盖世魔君" then
        return{"充沛","震怒","炙烤","烈焰","赤暖","邪火","火神","震天","折服","焰星","崩摧","焰威","咆哮","狂月","魔焱","威吓","连营","狂劲","额外能力","升温","盖世魔君"}
        elseif self.当前经脉 == "风火妖王" then
        return{"秘传三昧真火","烈火真言","秘传飞砂走石","极炙","咒言","摧山","不忿","震天","融骨","神焰","焦土","不灭","烬藏","固基","惊悟","威吓","旋阳","魔心","额外能力","燎原","风火妖王"}
      end
    elseif  self.门派== "狮驼岭" then
      if  self.当前经脉 == "嗜血狂魔" then
        return{"爪印","迅捷","驭兽","化血","宁息","兽王","威压","鹰啸","怒象","九天","魔息","协战","怒火","狂袭","癫狂","死地","乱击","肝胆","额外能力","背水","嗜血狂魔"}
        elseif self.当前经脉 == "万兽之王" then
        return{"狮王","健壮","图腾","急救","饮血","守势","护盾","狂化","矫健","协同","九天","嗜血","羁绊","獠牙","钢牙","复仇","逞凶","救主","额外能力","最佳搭档","万兽之王"}
        elseif self.当前经脉 == "狂怒斗兽" then
        return{"狂躁","狂化","狂啸","攫取","屏息","不羁","狮噬","象踏","长啸","九天","魔息","协战","羁绊","狂袭","狂血","狂乱","雄风","狩猎","额外能力","困兽之斗","狂怒斗兽"}
      end
    elseif  self.门派== "天宫" then
      if  self.当前经脉 == "镇妖神使" then
        return{"威吓","疾雷","轰鸣","趁虚","余韵","天威","震慑","神念","藏招","苏醒","护佑","坚壁","月桂","怒火","套索","神律","神尊","洞察","额外能力","画地为牢","镇妖神使"}
        elseif self.当前经脉 == "踏雷天尊" then
        return{"频变","威吓","惊曜","霜雷","轰鸣","驭意","电掣","神念","伏魔","雷霆汹涌","苏醒","天劫","怒电","共鸣","灵光","洞察","仙音","雷波","额外能力","风雷韵动","踏雷天尊"}
        elseif self.当前经脉 == "霹雳真君" then
        return{"霆震","疾雷","激越","存雄","余韵","慨叹","电掣","伏魔","惊霆","雷吞","苏醒","电光火石","神采","劲健","啸傲","神律","气势","洞察","额外能力","威仪九霄","霹雳真君"}
      end
    elseif  self.门派== "普陀山" then
      if  self.当前经脉 == "莲台仙子" then
        return{"推衍","化戈","普照","莲花心音","静心","慈佑","劳心","普渡","度厄","甘露","清净","莲动","法华","灵动","感念","玉帛","雨润","道衍","额外能力","波澜不惊","莲台仙子"}
        elseif self.当前经脉 == "五行咒师" then
        return{"庄严","借灵","推衍","默诵","静心","莲花心音","赐咒","普渡","慧眼","无怖","清净","秘术","感念","莲心剑意","灵动","道衍","缘起","法咒","额外能力","五行制化","五行咒师"}
        elseif self.当前经脉 == "落伽神女" then
        return{"湛然","因缘","莲音","安忍","静心","低眉","顿悟","怒目","馀威","清净","业障","困兽","无尽","抖擞","莲华","化用","智念","执念","额外能力","万象","落伽神女"}
      end
    elseif  self.门派== "五庄观" then
      if  self.当前经脉 == "清心羽客" then
        return{"体恤","运转","行气","心浪","养生","蓄志","归本","修心","存思","修身","同辉","守中","乾坤","意境","存神","陌宝","心随意动","玄机","额外能力","清风望月","清心羽客"}
        elseif self.当前经脉 == "乾坤力士" then
        return{"体恤","锤炼","神附","心浪","养生","强击","无极","修心","混元","修身","剑气","雨杀","意境","起雨","滂沱","剑势","心随意动","致命","额外能力","天命剑法","乾坤力士"}
        elseif self.当前经脉 == "万寿真仙" then
        return{"木摧","道果","饮露","炼果","心浪","聚力","无极","修心","混元","刺果","修身","三元","凝神","纳气","气盛","剑势","还元","致命","额外能力","归真","万寿真仙"}
      end
    elseif  self.门派== "龙宫" then
      if  self.当前经脉 == "海中蛟虬" then
        return{"波涛","破浪","狂浪","叱咤","踏涛","龙啸","逐浪","龙珠","龙息","龙慑","傲翔","飞龙","骇浪","月光","戏珠","汹涌","龙魄","斩浪","额外能力","亢龙归海","海中蛟虬"}
        elseif self.当前经脉 == "云龙真身" then
        return{"波涛","破浪","云霄","呼风","踏涛","清吟","龙息","龙珠","唤雨","龙慑","傲翔","飞龙","戏珠","月光","云变","沐雨","龙魄","摧意","额外能力","雷浪穿云","云龙真身"}
        elseif self.当前经脉 == "沧海潜龙" then
        return{"傲岸","云魂","雨魄","盘龙","踏涛","叱咤","凛然","龙珠","回灵","龙慑","傲翔","飞龙","戏珠","月光","波涛","龙钩","琴魂","惊鸿","额外能力","潜龙在渊","沧海潜龙"}
      end
    elseif  self.门派== "神木林" then
      if  self.当前经脉 == "通天法王" then
        return{"法身","风魂","灵佑","追击","咒法","狂叶","劲草","冰锥","苍埃","神木","月影","薪火","纯净","蔓延","破杀","星光","滋养","灵归","额外能力","风卷残云","通天法王"}
        elseif self.当前经脉 == "巫影祭司" then
        return{"风魂","迷缚","法身","伏毒","咒法","灵木","绞藤","冰锥","寄生","神木","月影","薪火","纯净","蔓延","破杀","激活","滋养","毒萃","额外能力","凋零之歌","巫影祭司"}
        elseif self.当前经脉 == "灵木药宗" then --官服已改
        return{"木魂","绿茵","滋润","明心","反哺","祛除","药颂","补血","灵精","风神","月影","转化","纯净","救主","净化","润泽","木精","残余","额外能力","百草古树长青","灵木药宗"}
      end
    elseif self.门派== "凌波城" then
      if  self.当前经脉 == "九天武圣" then
        return{"山破","战诀","无双","聚气","贯通","魂聚","神躯","冰暴","不动","力战","破击","巧变","海沸","怒火","煞气","强袭","混元","再战","额外能力","天神怒斩","九天武圣"}
        elseif self.当前经脉 == "灵霄斗士" then
        return{"石摧","战诀","天泽","聚气","贯通","魂聚","神躯","涡流","不动","妙得","闪雷","惊涛","海沸","怒火","煞气","乘势","追袭","再战","额外能力","真君显灵","灵霄斗士"}
        elseif self.当前经脉 == "风云战将" then
        return{"山破","战诀","拍岸","怒眼","贯通","魂聚","神躯","砥石","不动","威震","盛势","天眼","海沸","怒火","煞气","蓄势","杀罚","再战","额外能力","耳目一新","风云战将"}
      end
    elseif self.门派== "无底洞" then
      if  self.当前经脉 == "妙谛金莲" then
        return{"灵照","秉幽","护法","涌泉","绝处逢生","烛照","华光","风墙","血潮","精进","救人","疗愈","持戒","罗汉","灵通","忍辱","暗潮","噬魂","额外能力","同舟共济","妙谛金莲"}
        elseif self.当前经脉 == "摄魂迷影" then
        return{"阴魅","诡印","萦魄","御兽","绝处逢生","陷阱","椎骨","风墙","血潮","妖法","精进","救人","烈煞","持戒","罗汉","忍辱","暗潮","噬魂","额外能力","妖风四起","摄魂迷影"}
        elseif self.当前经脉 == "幽冥巫煞" then
        return{"弥愤","魂守","刺骨","余咒","鬼袭","羽裂","分魄","盛怒","血潮","夺血","灵变","深刻","牵动","独一","聚魂","纠缠","灵身","踏魄","额外能力","冥煞","幽冥巫煞"}
      end
  elseif self.门派== "九黎城" then----------------
        return{"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"}


    end
end













function 经脉处理类:取奇经八脉特效()

    if  self.门派== "大唐官府" then
      if  self.当前经脉 == "浴血豪侠" then
        return  {翩鸿一击=1}
        elseif self.当前经脉 == "无双战神" then
        return {翩鸿一击=1,连破=1}
        elseif self.当前经脉 == "虎贲上将" then
        return {披坚执锐=1}
      end
    elseif  self.门派== "方寸山" then
      if  self.当前经脉 == "拘灵散修" then
        return {}
        elseif self.当前经脉 == "伏魔天师" then
        return{悲恸=1,奔雷=1,}
        elseif self.当前经脉 == "五雷正宗" then
        return{五雷正法=1,雷法崩裂=1,雷法震煞=1,雷法坤伏=1,咒符=1}
      end
    elseif  self.门派== "女儿村" then
      if  self.当前经脉 == "绝代妖娆" then
        return {自矜=1}
        elseif self.当前经脉 == "花雨伊人" then
        return{}
        elseif self.当前经脉 == "妙舞佳人" then--------------------官服已改
        return{踏歌=1,乐韵=1,轻歌飘舞=1,翩跃飞舞=1}
      end



    elseif  self.门派== "化生寺" then
      if  self.当前经脉 == "杏林妙手" then
        return{明光=1,佛眷=1,}
        elseif self.当前经脉 == "护法金刚" then
        return{明光=1,聚气=1}
        elseif self.当前经脉 == "无量尊者" then
        return {度厄=1}
      end
    elseif  self.门派== "盘丝洞" then
      if  self.当前经脉 == "风华舞圣" then
        return{神迷=1}
        elseif self.当前经脉 == "迷情妖姬" then
        return{神迷=1}
        elseif self.当前经脉 == "百媚魔姝" then
        return{神迷=1,千蛛噬魂=1,蛛丝缠绕=1}
      end
    elseif  self.门派== "阴曹地府" then
      if  self.当前经脉 == "勾魂阎罗" then
        return{六道无量=1}
        elseif self.当前经脉 == "六道魍魉" then
        return{六道无量=1}
        elseif self.当前经脉 == "诛刑毒师" then
        return{血影蚀心=1,百鬼噬魂=1}
      end
    elseif  self.门派== "魔王寨" then
      if  self.当前经脉 == "平天大圣" then
        return{魔冥=1}
        elseif self.当前经脉 == "盖世魔君" then
        return{魔冥=1}
        elseif self.当前经脉 == "风火妖王" then
        return{}
      end
    elseif  self.门派== "狮驼岭" then
      if  self.当前经脉 == "嗜血狂魔" then
        return{}
        elseif self.当前经脉 == "万兽之王" then
        return{驯兽幼狮=1,幼狮之搏=1,鹰击长空=1,狮魂=1}
        elseif self.当前经脉 == "狂怒斗兽" then
        return{狂怒=1}
      end
    elseif  self.门派== "天宫" then
      if  self.当前经脉 == "镇妖神使" then
        return{}
        elseif self.当前经脉 == "踏雷天尊" then
        return{电芒=1}
        elseif self.当前经脉 == "霹雳真君" then
        return{风雷斩=1,霹雳弦惊=1,雷怒霆激=1,返璞=1}
      end
    elseif  self.门派== "普陀山" then
      if  self.当前经脉 == "莲台仙子" then
        return{}
        elseif self.当前经脉 == "五行咒师" then
        return{}
        elseif self.当前经脉 == "落伽神女" then
        return{五行珠=1,剑意莲心=1}
      end
    elseif  self.门派== "五庄观" then
      if  self.当前经脉 == "清心羽客" then
        return{}
        elseif self.当前经脉 == "乾坤力士" then
        return{骤雨=1}
        elseif self.当前经脉 == "万寿真仙" then
        return{敲金击玉=1,还丹=1,金击式=1}
      end
    elseif  self.门派== "龙宫" then
      if  self.当前经脉 == "海中蛟虬" then
        return{龙魂=1,龙骇=1}
        elseif self.当前经脉 == "云龙真身" then
        return{龙魂=1,龙骇=1}
        elseif self.当前经脉 == "沧海潜龙" then
        return{龙魂=1,龙骇=1}
      end
    elseif  self.门派== "神木林" then
      if  self.当前经脉 == "通天法王" then
        return{风灵=1,鞭挞=1}
        elseif self.当前经脉 == "巫影祭司" then
        return{风灵=1,鞭挞=1,蛊树迷瘴=1,催化=1}
        elseif self.当前经脉 == "灵木药宗" then
        return{百草诀=1,药灵=1,百草神木复苏=1,滋养=1,百草复苏=1}
      end
    elseif self.门派== "凌波城" then
      if  self.当前经脉 == "九天武圣" then
        return{吞山=1,饮海=1}
        elseif self.当前经脉 == "灵霄斗士" then
        return{超级战意=1}
        elseif self.当前经脉 == "风云战将" then
        return{天眼神通=1}
      end
    elseif self.门派== "无底洞" then
      if  self.当前经脉 == "妙谛金莲" then
        return{金莲=1,由己渡人=1,焕生咒=1,燃血术=1}
        elseif self.当前经脉 == "摄魂迷影" then
        return{燃血术=1}
        elseif self.当前经脉 == "幽冥巫煞" then
        return{裂魂=1,燃血术=1,追魂刺=1}
      end
    elseif self.门派== "九黎城" then
        return {}




    end
end




----------------增加属性效果 -------------------------
function 经脉处理类:武器伤害处理(百分比)
  if 玩家数据[self.玩家id].角色.数据.装备[3]~=nil then
    local 道具id = 玩家数据[self.玩家id].角色.数据.装备[3]
    local 武器道具 =玩家数据[self.玩家id].道具.数据[道具id]
    if not 玩家数据[self.玩家id].道具.数据[道具id] then
        玩家数据[self.玩家id].角色.数据.装备[3]=nil
        return 0
    end
    return  tonumber(武器道具.伤害)*(百分比/100)
  end
 return 0
end


function 经脉处理类:取师门技能等级(名称)
  for i=1,#玩家数据[self.玩家id].角色.数据.师门技能 do
    if 玩家数据[self.玩家id].角色.数据.师门技能[i]~=nil then
      if 玩家数据[self.玩家id].角色.数据.师门技能[i].名称 == 名称 then
          return 玩家数据[self.玩家id].角色.数据.师门技能[i].等级
      end
    end
  end
 return 0
end

function 经脉处理类:取装备宝石数量(名称)
  local 数量 = 0
  for i=1,6 do
    if 玩家数据[self.玩家id].角色.数据.装备[i]~=nil then
       local  道具id = 玩家数据[self.玩家id].角色.数据.装备[i]
       if not 玩家数据[self.玩家id].道具.数据[道具id] then
          玩家数据[self.玩家id].角色.数据.装备[3]=nil
          return 0
      end
       if 玩家数据[self.玩家id].道具.数据[道具id].镶嵌类型~=nil then
          for n=1,#玩家数据[self.玩家id].道具.数据[道具id].镶嵌类型 do
              if  玩家数据[self.玩家id].道具.数据[道具id].镶嵌类型[n] == 名称 then
                数量 = 数量 + 1
              end
          end
       end
    end
  end
return 数量
end




function 经脉处理类:刷新经脉属性()
  local 经脉属性 = {体质=0,魔力=0,力量=0,耐力=0,敏捷=0,气血=0,伤害=0,防御=0,速度=0,
                治疗能力=0,法术伤害=0,法术防御=0,封印命中等级=0,抵抗封印等级=0,法术暴击等级=0,物理暴击等级=0}
    if  self.门派== "大唐官府" then
        if self:取经脉是否有("无敌") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("破军") then
            经脉属性.气血 = 经脉属性.气血 + 560
        end
        if self:取经脉是否有("披挂上阵") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end

    elseif  self.门派== "方寸山" then
        if self:取经脉是否有("斗法") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("顺势而为") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("钟馗论道") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("奇门") then
            经脉属性.法术暴击等级 = 经脉属性.法术暴击等级 + 120
        end
    elseif  self.门派== "女儿村" then
        if self:取经脉是否有("碎玉弄影") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + 120
        end
        if self:取经脉是否有("鸿渐于陆") then
            经脉属性.抵抗封印等级 = 经脉属性.抵抗封印等级 + 160
        end
        if self:取经脉是否有("余韵索心") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end

    elseif  self.门派== "化生寺" then
        if self:取经脉是否有("渡劫金身") then
            经脉属性.治疗能力 = 经脉属性.治疗能力 + 60
        end
        if self:取经脉是否有("诸天看护") then
            经脉属性.法术防御 = 经脉属性.法术防御 + 160
        end
        if self:取经脉是否有("金刚") then
            经脉属性.防御 = 经脉属性.防御 + 80
        end
        if self:取经脉是否有("达摩") then
            经脉属性.气血 = 经脉属性.气血 + 320
        end
        if self:取经脉是否有("韦陀") then
            经脉属性.速度 = 经脉属性.速度 + 30
        end
        if self:取经脉是否有("坐禅") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("止戈") then
            经脉属性.治疗能力 = 经脉属性.治疗能力 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("生花") then
          local  取师门等级 = self:取师门技能等级("渡世步")
          if  取师门等级>=玩家数据[self.玩家id].角色.数据.等级 then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 60
          end
        end
    elseif  self.门派== "盘丝洞" then
        if self:取经脉是否有("妖气") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("落花成泥") then
            经脉属性.法术防御 = 经脉属性.法术防御 + 160
        end
        if self:取经脉是否有("偷龙转凤") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("亡缚") then
            经脉属性.物理暴击等级 = 经脉属性.物理暴击等级 + 100
        end
        if self:取经脉是否有("绝命毒牙") then
            经脉属性.抵抗封印等级 = 经脉属性.抵抗封印等级 + 160
        end
    elseif  self.门派== "阴曹地府" then
        if self:取经脉是否有("魍魉追魂") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("夜之王者") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("生杀予夺") then
            经脉属性.物理暴击等级 = 经脉属性.物理暴击等级 + 120
        end
    elseif  self.门派== "魔王寨" then
        if self:取经脉是否有("魔心") then
            经脉属性.法术暴击等级 = 经脉属性.法术暴击等级 + 100
        end
        if self:取经脉是否有("魔焰滔天") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("升温") then
            经脉属性.防御 = 经脉属性.防御 + 160
        end
        if self:取经脉是否有("燎原") then
            经脉属性.法术暴击等级 = 经脉属性.法术暴击等级 + 120
        end
        if self:取经脉是否有("狂月") then
            local 宝石数量 = self:取装备宝石数量("月亮石")
            经脉属性.防御 = 经脉属性.防御 + 12*宝石数量*0.4
        end
        if self:取经脉是否有("震天") then
          local  取师门等级 = self:取师门技能等级("震天诀")
          if  取师门等级>=玩家数据[self.玩家id].角色.数据.等级 then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 60
          end
        end
    elseif  self.门派== "狮驼岭" then
        if self:取经脉是否有("背水") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("最佳搭档") then
            经脉属性.物理暴击等级 = 经脉属性.物理暴击等级 + 120
        end
        if self:取经脉是否有("困兽之斗") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
   elseif  self.门派== "天宫" then
        if self:取经脉是否有("月桂") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("画地为牢") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + 120
        end
        if self:取经脉是否有("风雷韵动") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("威仪九霄") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("劲健") then
            经脉属性.伤害 = 经脉属性.伤害 + 50
        end
        if self:取经脉是否有("护佑") then
            经脉属性.防御 = 经脉属性.防御 + 50
            经脉属性.法术防御 = 经脉属性.法术防御 + 50
        end
        if self:取经脉是否有("轰鸣") and self.当前经脉 == "踏雷天尊" then
          local  取师门等级 = self:取师门技能等级("混天术")
          if  取师门等级>=玩家数据[self.玩家id].角色.数据.等级 then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 60
          end
        end
       if self:取经脉是否有("灵光") then
            local 宝石数量 = self:取装备宝石数量("舍利子")
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 6*宝石数量*0.25
        end
    elseif  self.门派== "普陀山" then
        if self:取经脉是否有("波澜不惊") then
            经脉属性.法术防御 = 经脉属性.法术防御 + 160
        end
        if self:取经脉是否有("五行制化") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("万象") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("推衍") then
            经脉属性.治疗能力 = 经脉属性.治疗能力+40
        end
    elseif  self.门派== "五庄观" then
        if self:取经脉是否有("修身") then
            经脉属性.力量 = 经脉属性.力量 + 14
            经脉属性.体质 = 经脉属性.体质 + 14
            经脉属性.魔力 = 经脉属性.魔力 + 14
            经脉属性.耐力 = 经脉属性.耐力 + 14
            经脉属性.敏捷 = 经脉属性.敏捷 + 14
        end
        if self:取经脉是否有("陌宝") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + 80
        end
        if self:取经脉是否有("清风望月") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("锤炼") then
            经脉属性.伤害 = 经脉属性.伤害 + self:武器伤害处理(4)
        end
        if self:取经脉是否有("天命剑法") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("归真") then
            经脉属性.物理暴击等级 = 经脉属性.物理暴击等级 + 120
        end
    elseif  self.门派== "龙宫" then
        if self:取经脉是否有("亢龙归海") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("雷浪穿云") then
            经脉属性.防御 = 经脉属性.防御 + 160
        end
        if self:取经脉是否有("潜龙在渊") then
            经脉属性.法术暴击等级 = 经脉属性.法术暴击等级 + 120
        end

         if self:取经脉是否有("踏涛")  then
          local  取师门等级 = self:取师门技能等级("破浪诀")
          if  取师门等级>=玩家数据[self.玩家id].角色.数据.等级 then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 60
          end
        end

       if self:取经脉是否有("月光") then
            local 宝石数量 = self:取装备宝石数量("月亮石")
            经脉属性.防御 = 经脉属性.防御 + 12*宝石数量*0.4
        end
    elseif  self.门派== "神木林" then
        if self:取经脉是否有("风卷残云") then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 80
        end
        if self:取经脉是否有("凋零之歌") then
            经脉属性.气血 = 经脉属性.气血 + 560
        end
        if self:取经脉是否有("百草古树长青") then
            经脉属性.抵抗封印等级 = 经脉属性.抵抗封印等级 + 160
        end
        if self:取经脉是否有("咒法")  then
          local  取师门等级 = self:取师门技能等级("巫咒")
          if  取师门等级>=玩家数据[self.玩家id].角色.数据.等级 then
            经脉属性.法术伤害 = 经脉属性.法术伤害 + 60
          end
        end
        if self:取经脉是否有("纯净") then
            local 宝石数量 = self:取装备宝石数量("光芒石")
            经脉属性.气血 = 经脉属性.气血 + 40*宝石数量*0.7
        end
        if self:取经脉是否有("绿茵") then
            经脉属性.治疗能力 = 经脉属性.治疗能力+self:武器伤害处理(18)
        end
        if self:取经脉是否有("转化") then
            local 宝石数量 = self:取装备宝石数量("光芒石")
            经脉属性.治疗能力 = 经脉属性.治疗能力 + 5*宝石数量
        end

    elseif  self.门派== "凌波城" then
        if self:取经脉是否有("天神怒斩") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end
        if self:取经脉是否有("真君显灵") then
            经脉属性.速度 = 经脉属性.速度 + 60
        end
        if self:取经脉是否有("耳目一新") then
            经脉属性.伤害 = 经脉属性.伤害 + 80
        end

    elseif  self.门派== "无底洞" then
        if self:取经脉是否有("秉幽") then
            经脉属性.治疗能力 = 经脉属性.治疗能力 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("同舟共济") then
            经脉属性.法术防御 = 经脉属性.法术防御 + 160
        end

        if self:取经脉是否有("诡印") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + self:武器伤害处理(18)
        end
        if self:取经脉是否有("烈煞") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + 100
        end
        if self:取经脉是否有("妖风四起") then
            经脉属性.封印命中等级 = 经脉属性.封印命中等级 + 120
        end
        if self:取经脉是否有("冥煞") then
            经脉属性.防御 = 经脉属性.防御 + 160
        end
    elseif  self.门派== "九黎城" then
          if self:取经脉是否有("魔神之刃") then
              经脉属性.速度 = 经脉属性.速度 + 60
          end


    end




    for k,v in pairs(经脉属性) do
        玩家数据[self.玩家id].角色.数据.装备属性[k]=玩家数据[self.玩家id].角色.数据.装备属性[k]+v
    end



end






return  经脉处理类