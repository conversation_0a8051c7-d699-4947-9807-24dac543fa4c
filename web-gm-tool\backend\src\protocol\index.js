/**
 * GM工具协议层入口文件
 * 统一导出所有协议相关功能
 */

const encryption = require('./encryption');
const packet = require('./packet');
const constants = require('./constants');

/**
 * 协议处理器类
 * 封装所有协议相关操作
 */
class ProtocolHandler {
    constructor() {
        this.constants = constants;
    }

    /**
     * 创建登录数据包
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @returns {Buffer} 登录数据包
     */
    createLoginPacket(username, password) {
        const loginData = {
            账号: username,
            密码: password
        };
        // 登录时不传递账号参数，因为获取账号变量在登录时应该为空
        return packet.createSendPacket(constants.PROTOCOL_IDS.LOGIN, loginData);
    }

    /**
     * 创建充值数据包
     * @param {number} type - 充值类型
     * @param {string} account - 账号
     * @param {number} amount - 充值数量
     * @returns {Buffer} 充值数据包
     */
    createRechargePacket(type, account, amount) {
        const rechargeData = {
            账号: account,
            数量: amount,
            类型: type
        };
        return packet.createSendPacket(type, rechargeData, account);
    }

    /**
     * 创建账号管理数据包
     * @param {number} operation - 操作类型
     * @param {string} targetAccount - 目标账号
     * @param {object} params - 额外参数
     * @returns {Buffer} 账号管理数据包
     */
    createAccountManagePacket(operation, targetAccount, params = {}) {
        const data = {
            目标账号: targetAccount,
            ...params
        };
        return packet.createSendPacket(operation, data, targetAccount);
    }

    /**
     * 创建装备发送数据包
     * @param {string} account - 目标账号
     * @param {object} equipment - 装备信息
     * @returns {Buffer} 装备数据包
     */
    createEquipmentPacket(account, equipment) {
        const equipmentData = {
            账号: account,
            装备: equipment
        };
        return packet.createSendPacket(constants.PROTOCOL_IDS.SEND_EQUIPMENT, equipmentData, account);
    }

    /**
     * 创建游戏活动数据包
     * @param {number} activityType - 活动类型
     * @param {object} params - 活动参数
     * @returns {Buffer} 活动数据包
     */
    createActivityPacket(activityType, params = {}) {
        return packet.createSendPacket(activityType, params, '');
    }

    /**
     * 解析接收到的数据包
     * @param {Buffer} buffer - 接收到的数据包
     * @returns {object|null} 解析后的数据
     */
    parsePacket(buffer) {
        return packet.parseReceivePacket(buffer);
    }

    /**
     * 验证数据包完整性
     * @param {Buffer} buffer - 数据包缓冲区
     * @returns {boolean} 是否完整
     */
    isPacketComplete(buffer) {
        return packet.isPacketComplete(buffer);
    }

    /**
     * 获取所需的数据包长度
     * @param {Buffer} buffer - 数据包缓冲区
     * @returns {number} 所需长度
     */
    getRequiredPacketLength(buffer) {
        return packet.getRequiredPacketLength(buffer);
    }

    /**
     * 加密数据
     * @param {string} data - 要加密的数据
     * @returns {string} 加密后的数据
     */
    encrypt(data) {
        return encryption.jm(data);
    }

    /**
     * 解密数据
     * @param {string} data - 要解密的数据
     * @returns {string} 解密后的数据
     */
    decrypt(data) {
        return encryption.jm1(data);
    }

    /**
     * 格式化数据对象
     * @param {object} data - 数据对象
     * @returns {string} 格式化后的字符串
     */
    formatData(data) {
        return encryption.tableToString(data);
    }

    /**
     * 获取协议ID
     * @param {string} name - 协议名称
     * @returns {number} 协议ID
     */
    getProtocolId(name) {
        return constants.PROTOCOL_IDS[name];
    }

    /**
     * 获取错误消息
     * @param {string} key - 错误键
     * @returns {string} 错误消息
     */
    getErrorMessage(key) {
        return constants.ERROR_MESSAGES[key];
    }

    /**
     * 获取成功消息
     * @param {string} key - 成功键
     * @returns {string} 成功消息
     */
    getSuccessMessage(key) {
        return constants.SUCCESS_MESSAGES[key];
    }
}

// 创建单例实例
const protocolHandler = new ProtocolHandler();

module.exports = {
    ProtocolHandler,
    protocolHandler,
    encryption,
    packet,
    constants
};
