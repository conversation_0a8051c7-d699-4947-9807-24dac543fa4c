{
	"auto_complete":
	{
		"selected_items":
		[
			[
				"table",
				"table.insert	(table, [pos,] value)"
			],
			[
				"for",
				"fori	for i,v in ipairs()"
			],
			[
				"c",
				"ceil	(设置.lua)"
			],
			[
				"st",
				"string.len	(s)"
			],
			[
				"e",
				"else	else end"
			],
			[
				"self",
				"self:打开"
			],
			[
				"fo",
				"for	for i=1,10"
			],
			[
				"fl",
				"floor"
			],
			[
				"lo",
				"local	local x = 1"
			],
			[
				"ba",
				"break	(帮派查看类.lua)"
			],
			[
				"多角色",
				"多角色操作数据"
			],
			[
				"t",
				"tonumber	(e [, base])"
			],
			[
				"s",
				"string"
			],
			[
				"els",
				"else	else end"
			],
			[
				"无介绍",
				"无介绍报错管理员	(技能库.lua)"
			],
			[
				"物品",
				"物品信息	(道具行囊.lua)"
			],
			[
				"刷新",
				"刷新道具行囊"
			],
			[
				"os",
				"os.time	([table])"
			],
			[
				"取",
				"取随机数"
			],
			[
				"p",
				"print"
			],
			[
				"else",
				"else	else end"
			],
			[
				"r",
				"return	return 返回"
			]
		]
	},
	"buffers":
	[
		{
			"file": "script/资源类/文件类.lua",
			"settings":
			{
				"buffer_size": 6280,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/初系统/功能界面.lua",
			"settings":
			{
				"buffer_size": 69779,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/初系统/登陆.lua",
			"settings":
			{
				"buffer_size": 2964,
				"line_ending": "Windows"
			}
		},
		{
			"file": "Main.lua",
			"settings":
			{
				"buffer_size": 3327,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/PackClient.lua",
			"settings":
			{
				"buffer_size": 3020,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/MessagePack.lua",
			"settings":
			{
				"buffer_size": 29611,
				"line_ending": "Windows"
			}
		},
		{
			"contents": "Searching 46 files for \"Socket\"\n\nC:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script\\ForYourOwnUse\\Client.lua:\n   15      print('[GM工具] Client:连接 - IP:', ip, '端口:', port, '异步模式:', async and 1 or 0)\n   16      \n   17:     -- 显示当前HP-Socket状态\n   18      local state = self._hp:GetState()\n   19      print('[GM工具] Client:连接 - 当前状态:', state)\n   20      \n   21:     -- 显示HP-Socket配置信息\n   22:     if self._hp.GetSocketBufferSize then\n   23:         print('[GM工具] Client:连接 - 缓冲区大小:', self._hp:GetSocketBufferSize())\n   24      end\n   25      if self._hp.GetKeepAliveTime then\n   ..\n   58  \n   59  --准备连接通知\n   60: function Client:OnPrepareConnect(socket)\n   61:     print('[GM工具] Client:OnPrepareConnect - 准备连接，Socket:', socket)\n   62      return 1\n   63  end\n   ..\n  111  end\n  112  --通信错误通知\n  113: local EnSocketOperation={\n  114      [0]='UNKNOWN'  , --   // Unknown\n  115      [1]='ACCEPT'   , --   // Acccept\n  ...\n  120  }\n  121  \n  122: -- HP-Socket错误代码说明\n  123  local ErrorCodes = {\n  124      [0] = \"SE_OK - 成功\",\n  125      [1] = \"SE_ILLEGAL_STATE - 当前状态不允许操作\",\n  126      [2] = \"SE_INVALID_PARAM - 非法参数\",\n  127:     [3] = \"SE_SOCKET_CREATE - 创建 SOCKET 失败\",\n  128:     [4] = \"SE_SOCKET_BIND - 绑定 SOCKET 失败\",\n  129:     [5] = \"SE_SOCKET_PREPARE - 设置 SOCKET 失败\",\n  130:     [6] = \"SE_SOCKET_LISTEN - 监听 SOCKET 失败\",\n  131      [7] = \"SE_CP_CREATE - 创建完成端口失败\",\n  132      [8] = \"SE_WORKER_THREAD_CREATE - 创建工作线程失败\",\n  ...\n  143  function Client:OnClose(enOperation,iErrorCode)\n  144      print('[GM工具] Client:OnClose - 连接断开回调触发')\n  145:     print('[GM工具] Client:OnClose - 操作类型:', enOperation, '(' .. (EnSocketOperation[enOperation] or 'UNKNOWN') .. ')')\n  146      print('[GM工具] Client:OnClose - 错误代码:', iErrorCode, '(' .. (ErrorCodes[iErrorCode] or '未知错误') .. ')')\n  147      \n  ...\n  201      return self._hp:GetConnectionID()\n  202  end\n  203: --  /* 获取 Client Socket 的地址信息 */\n  204  function Client:取本地地址信息()\n  205      return self._hp:GetLocalAddress()\n\nC:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script\\ForYourOwnUse\\PackClient.lua:\n   11      \n   12      self._hp = self._new(__gge.cs,__gge.state)\n   13:     print('[GM工具] PackClient:初始化 - HP-Socket对象创建完成')\n   14      \n   15      self._hp:Create_TcpPackClient(self)\n   ..\n   73      end\n   74      \n   75:     print('[GM工具] PackClient:发送 - 调用HP-Socket SendPack函数')\n   76      self._hp:SendPack(Data)\n   77      print('[GM工具] PackClient:发送 - SendPack调用完成')\n\nC:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script\\ForYourOwnUse\\PullClient.lua:\n   31  -- } En_HP_HandleResult;\n   32  --准备连接通知\n   33: function PullClient:OnPrepareConnect(socket)\n   34      self._info = {\n   35          ishead = true,\n\nC:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script\\ForYourOwnUse\\TcpClient.lua:\n   24  end\n   25  --/* 设置通信数据缓冲区大小（根据平均通信数据包大小调整设置，通常设置为：(N * 1024) - sizeof(TBufferObj)） */\n   26: function TcpClient:置缓冲区大小(dwSocketBufferSize)\n   27:     self._hp:SetSocketBufferSize(dwSocketBufferSize)\n   28      return self\n   29  end\n   ..\n   40  --/* 获取通信数据缓冲区大小 */\n   41  function TcpClient:取缓冲区大小()\n   42:     return self._hp:GetSocketBufferSize()\n   43  end\n   44  --/* 获取正常心跳包间隔 */\n\n26 matches across 4 files\n",
			"settings":
			{
				"buffer_size": 3138,
				"line_ending": "Windows",
				"name": "Find Results",
				"scratch": true
			}
		},
		{
			"file": "gge引擎.lua",
			"settings":
			{
				"buffer_size": 12683,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/TcpClient.lua",
			"settings":
			{
				"buffer_size": 1592,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/PullClient.lua",
			"settings":
			{
				"buffer_size": 1982,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/Client.lua",
			"settings":
			{
				"buffer_size": 6856,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/全局/自己_专用.LUA",
			"settings":
			{
				"buffer_size": 14290,
				"line_ending": "Windows"
			}
		},
		{
			"file": "script/ForYourOwnUse/服务连接.lua",
			"settings":
			{
				"buffer_size": 4491,
				"line_ending": "Windows"
			}
		}
	],
	"build_system": "Packages/Lua/ggegame.sublime-build",
	"build_system_choices":
	[
		[
			[
				[
					"Packages/Lua/ggegame.sublime-build",
					""
				],
				[
					"Packages/Lua/ggegame.sublime-build",
					"Run"
				],
				[
					"Packages/Lua/ggegame.sublime-build",
					"RunInCommand"
				],
				[
					"Packages/Lua/ggegame.sublime-build",
					"SetGGE"
				],
				[
					"Packages/Lua/ggegame.sublime-build",
					"Stop"
				],
				[
					"Packages/Lua/ggegame.sublime-build",
					"AboutGGE"
				]
			],
			[
				"Packages/Lua/ggegame.sublime-build",
				""
			]
		]
	],
	"build_varint": "",
	"command_palette":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"console":
	{
		"height": 0.0,
		"history":
		[
		]
	},
	"distraction_free":
	{
		"menu_visible": true,
		"show_minimap": false,
		"show_open_files": false,
		"show_tabs": false,
		"side_bar_visible": false,
		"status_bar_visible": false
	},
	"expanded_folders":
	[
	],
	"file_history":
	[
		"/D/防江南老版/新江南/工具源码定制神话词条/Main.lua",
		"/D/防江南老版/新江南/工具源码定制神话词条/script/ForYourOwnUse/服务连接.lua",
		"/D/防江南老版/新江南/工具源码定制神话词条/script/初系统/功能界面.lua",
		"/D/防江南老版/新江南/工具源码定制神话词条/script/系统类/自适应.lua",
		"/D/防江南老版/新江南/工具源码定制神话词条/script/初系统/登陆.lua",
		"/D/防江南老版/新江南/客户端111/tp信息记录.lua",
		"/D/JNHT/lua/授权列表.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/战斗准备类.lua",
		"/E/JN20241111/gge/Core/Game/ggeui/控件类.lua",
		"/D/防江南老版/江南修改新/客户端111/Main.lua",
		"/D/防江南老版/江南修改新/工具源码/gge文字类.lua",
		"/D/防江南老版/江南修改新/工具源码/script/初系统/功能界面2.lua",
		"/D/防江南老版/江南修改新/工具源码/script/系统类/自适应.lua",
		"/D/防江南老版/江南修改新/工具源码/script/全局/主控.lua",
		"/D/防江南老版/江南修改新/工具源码/script/全局/主显.lua",
		"/D/防江南老版/江南修改新/工具源码/script/全局/变量2.lua",
		"/D/防江南老版/江南修改新/工具源码/script/全局/自己_专用.LUA",
		"/D/防江南老版/江南修改新/工具源码/script/初系统/登陆.lua",
		"/D/防江南老版/江南修改新/工具源码/script/初系统/提示类.lua",
		"/C/Users/<USER>/Desktop/飞蛾 琴音 加点保存 一键整理/服务端/Script/对话处理类/对话调用/1001.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$1/物品库.lua",
		"/D/防江南老版/江南修改新/工具源码/防江南老版 - 快捷方式.lnk",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/共用.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/main.lua",
		"/C/Users/<USER>/Desktop/新建文件夹/WDF.lua",
		"/C/Users/<USER>/Desktop/新建文件夹/加载类.lua",
		"/C/Users/<USER>/Desktop/新建文件夹/缓存资源.lua",
		"/E/防江南修改/客户端源码/script/资源类/SP.lua",
		"/E/防江南修改/客户端源码/script/资源类/加载类.lua",
		"/E/防江南修改/客户端源码/script/全局/变量1.lua",
		"/E/防江南修改/客户端源码/script/资源类/资源类.lua",
		"/E/防江南修改/客户端源码/script/资源类/gge资源类.lua",
		"/E/防江南修改/客户端源码/script/资源类/锦衣wdf.lua",
		"/E/防江南修改/客户端源码/script/资源类/WDF.lua",
		"/E/防江南修改/客户端源码/script/资源类/动画类锦衣.lua",
		"/E/防江南修改/客户端源码/script/资源类/WAS.lua",
		"/E/防江南修改/客户端源码/script/资源类/WDF类.lua",
		"/E/防江南修改/客户端源码/script/资源类/WDF_风云.lua",
		"/E/防江南修改/客户端源码/script/资源类/文件类_风云.lua",
		"/E/防江南修改/客户端源码/script/资源类/文件类.lua",
		"/E/防江南修改/客户端源码/script/资源类/动画类.lua",
		"/E/防江南修改/客户端源码/script/资源类/动画类_旧版.lua",
		"/E/防江南修改/客户端源码/script/资源类/SP_风云.lua",
		"/C/Users/<USER>/Desktop/新建文件夹/遮罩类.lua",
		"/E/防江南修改/客户端源码/script/txt2wpal.lua",
		"/E/防江南修改/客户端源码/script/数据中心/物品库.lua",
		"/E/防江南修改/客户端源码/script/多重对话类/对话栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/道具行囊.lua",
		"/E/防江南修改/客户端源码/script/场景类/新道具行囊.lua",
		"/E/防江南修改/客户端源码/script/场景类/打造.lua",
		"/E/防江南修改/客户端源码/script/场景类/奇经八脉.lua",
		"/E/防江南修改/客户端源码/script/场景类/人物状态栏.lua",
		"/E/防江南修改/客户端源码/script/全局/主控.lua",
		"/E/防江南修改/客户端源码/script/网络/数据交换.lua",
		"/E/防江南修改/客户端源码/script/场景类/法宝.lua",
		"/E/防江南修改/客户端源码/script/神器类/修复神器.lua",
		"/E/防江南修改/客户端源码/script/场景类/队伍栏.lua",
		"/E/防江南修改/客户端源码/script/积分商店/仙玉商城类.lua",
		"/E/防江南修改/客户端源码/script/全局/玩家.lua",
		"/E/防江南修改/客户端源码/script/功能界面/排行榜类.lua",
		"/E/防江南修改/客户端源码/script/多角色操作/多角色状态栏.lua",
		"/E/防江南修改/客户端源码/script/显示类/技能.lua",
		"/E/防江南修改/客户端源码/script/初系统/设置.lua",
		"/E/防江南修改/客户端源码/script/显示类/提示类.lua",
		"/E/防江南修改/客户端源码/script/显示类/喊话.lua",
		"/E/防江南修改/客户端源码/script/功能界面/摊位购买类.lua",
		"/E/防江南修改/客户端源码/script/功能界面/新摊位购买类.lua",
		"/E/防江南修改/客户端源码/script/初系统/标题.lua",
		"/E/防江南修改/客户端源码/script/功能界面/摊位出售类.lua",
		"/E/9.27防江南/客户端源码/script/资源类/锦衣wdf.lua",
		"/E/9.27防江南/客户端源码/script/资源类/动画类锦衣.lua",
		"/E/xxxx版本/服务端源码/Script/角色处理类/装备符石处理.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$1/装备符石处理.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/符石组合类.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$1/Main.lua",
		"/C/Users/<USER>/AppData/Local/Temp/360zip$Temp/360$0/main.lua",
		"/C/Users/<USER>/Desktop/Main.lua",
		"/C/Users/<USER>/Documents/Tencent Files/627529188/FileRecv/Files/627529188/FileRecv/角色切换.lua",
		"/C/Users/<USER>/Documents/Tencent",
		"/E/防江南修改/客户端源码/script/功能界面/给予类.lua",
		"/E/防江南修改/客户端源码/script/藏宝阁/藏宝阁上架货币.lua",
		"/E/防江南修改/客户端源码/script/初系统/登陆.lua",
		"/E/防江南修改/客户端源码/script/初系统/分区.lua",
		"/E/防江南修改/客户端源码/script/系统类/按钮.lua",
		"/E/防江南修改/客户端源码/script/功能界面/人物界面类.lua",
		"/E/防江南修改/客户端源码/script/显示类/技能_格子.lua",
		"/E/防江南修改/客户端源码/script/初系统/游戏更新说明.lua",
		"/E/防江南修改/客户端源码/script/场景类/宠物合宠栏.lua",
		"/E/防江南修改/客户端源码/script/显示类/物品_格子.lua",
		"/E/防江南修改/客户端源码/script/场景类/宠物打书内丹栏.lua",
		"/E/防江南修改/客户端源码/script/功能界面/世界地图分类小地图.lua",
		"/E/防江南修改/客户端源码/script/场景类/梦幻指引.lua",
		"/E/防江南修改/客户端源码/script/功能界面/时辰.lua",
		"/E/防江南修改/客户端源码/script/显示类/文本栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/帮派点修.lua",
		"/E/防江南修改/客户端源码/script/场景类/召唤兽属性栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/召唤兽查看栏.lua",
		"/E/防江南修改/客户端源码/script/系统类/自适应.lua",
		"/E/防江南修改/客户端源码/script/多角色操作/多角色召唤兽资质栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/宠物洗练栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/孩子技能学习.lua",
		"/E/防江南修改/客户端源码/script/场景类/召唤兽资质栏.lua",
		"/E/防江南修改/客户端源码/script/功能界面/坐骑资质栏.lua",
		"/E/防江南修改/客户端源码/script/功能界面/坐骑技能栏.lua",
		"/E/防江南修改/客户端源码/script/功能界面/世界大地图分类a.lua",
		"/E/防江南修改/客户端源码/script/场景类/组合输入框.lua",
		"/E/防江南修改/客户端源码/script/场景类/打造1.lua",
		"/E/防江南修改/客户端源码/script/场景类/人物称谓栏.lua",
		"/E/防江南修改/客户端源码/script/系统类/列表.lua",
		"/E/防江南修改/客户端源码/script/系统类/丰富文本类外部.lua",
		"/E/防江南修改/客户端源码/script/系统类/丰富文本.lua",
		"/E/防江南修改/客户端源码/script/藏宝阁/藏宝阁购买寄存.lua",
		"/E/防江南修改/客户端源码/script/藏宝阁/藏宝阁出售.lua",
		"/E/防江南修改/客户端源码/script/藏宝阁/藏宝阁出售寄存.lua",
		"/E/防江南修改/客户端源码/script/老摩托/元身打造.lua",
		"/E/防江南修改/客户端源码/script/积分商店/钓鱼积分商店.lua",
		"/E/防江南修改/客户端源码/script/积分商店/每日查看.lua",
		"/E/防江南修改/客户端源码/script/战斗类/多角色自动栏.lua",
		"/E/防江南修改/客户端源码/script/多角色操作/多角色道具行囊.lua",
		"/E/防江南修改/客户端源码/script/多角色操作/多角色召唤兽属性栏.lua",
		"/E/防江南修改/客户端源码/script/多角色操作/多角色仓库类.lua",
		"/E/防江南修改/客户端源码/script/场景类/队伍申请列表类.lua",
		"/E/防江南修改/客户端源码/script/场景类/跑商商店.lua",
		"/E/防江南修改/客户端源码/script/场景类/装备开运.lua",
		"/E/防江南修改/客户端源码/script/场景类/染色.lua",
		"/E/防江南修改/客户端源码/script/场景类/宠物状态栏.lua",
		"/E/防江南修改/客户端源码/script/场景类/商店.lua",
		"/E/防江南修改/客户端源码/script/场景类/召唤兽仓库.lua"
	],
	"find":
	{
		"height": 37.0
	},
	"find_in_files":
	{
		"height": 122.0,
		"where_history":
		[
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script",
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码",
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script",
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码",
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码\\script",
			"C:\\Users\\<USER>\\Desktop\\网关\\GM工具源码",
			"D:\\防江南老版\\新江南\\工具源码定制神话词条",
			"D:\\防江南老版\\江南修改新\\工具源码定制神话词条",
			"E:\\JN20241111\\工具源码定制坐骑功德",
			"D:\\防江南老版\\江南修改新\\工具源码11.4",
			"D:\\防江南老版\\江南修改新\\工具源码",
			"E:\\防江南修改\\客户端源码",
			"C:\\Users\\<USER>\\Desktop\\新建文件夹",
			"C:\\Users\\<USER>\\Desktop\\新建文件夹\\Core",
			"C:\\Users\\<USER>\\Desktop\\新建文件夹",
			"E:\\防江南修改\\客户端源码",
			"D:\\3经脉源码\\客户端源码",
			"F:\\3经脉源码\\客户端源码",
			"F:\\3经脉源码\\客户端源码\\script",
			"F:\\3经脉源码\\客户端源码",
			"F:\\三系经脉\\客户端源码",
			"F:\\三系经脉\\客户端源码\\script",
			"F:\\三系经脉\\客户端源码",
			"F:\\三系经脉\\客户端源码\\script",
			"F:\\三系经脉\\客户端源码",
			"F:\\三系经脉\\客户端源码\\script",
			"F:\\三系经脉\\客户端源码",
			"F:\\复古经脉\\服务端源码",
			"F:\\复古经脉\\服务端源码\\活动攻略",
			"F:\\复古经脉\\服务端源码",
			"F:\\复古经脉\\服务端源码\\自动充值",
			"F:\\复古经脉\\服务端源码",
			"F:\\12门复古定制\\服务端源码",
			"E:\\12门复古定制\\服务端源码",
			"E:\\12门复古定制\\服务端",
			"E:\\12门复古定制\\服务端\\自动充值",
			"E:\\12门复古定制\\服务端",
			"E:\\吊游3\\服务端",
			"E:\\吊游3\\服务端\\功能设置",
			"E:\\吊游3\\服务端",
			"C:\\Users\\<USER>\\Desktop\\吊游3\\MH3\\服务端",
			"C:\\Users\\<USER>\\Desktop\\吊游3\\MH3\\游戏网关",
			"H:\\11.11日切源码AAAAAAAAAAAAAAAAAAAAAAAAA\\游戏网关\\Script"
		]
	},
	"find_state":
	{
		"case_sensitive": false,
		"find_history":
		[
			"Socket",
			"0x76",
			"0xCB",
			"Socket",
			"HPSocket",
			"encryptMap",
			"encodeBase641",
			"jm1",
			"814",
			"55953",
			"连接",
			"连接处理",
			"尝试连接服务器",
			"发送数据",
			"jm1",
			"神话",
			"词条1",
			"宝宝定制文本",
			"数额",
			"词条",
			"技能1",
			"成长",
			"灵力",
			"技能点",
			"技能1",
			">",
			"等级",
			"生成CDK",
			"生产CDK",
			"发送装备",
			"定制装备",
			"已选择宝宝",
			"发送信息",
			"角色修改按钮",
			"发送信息",
			"角色修改按钮",
			"角色修炼输入",
			"角色修炼显示",
			"self.角色修炼显示",
			"角色修炼显示",
			"获取角色信息按钮",
			"获取",
			"游戏按钮",
			"发送广播",
			"发送公告",
			"发送广播",
			"部位",
			"发送装备",
			"定制装备显示",
			"定制装备",
			"[5]",
			"等级",
			"丰富文本说明",
			"定制装备显示",
			"坐骑按钮",
			"密码按钮",
			"账号按钮",
			"下拉",
			"gj",
			"怨灵幻影",
			"发送灵饰",
			"[37]",
			"连接处理",
			"8787",
			"装备内容显示",
			"特效",
			"self.装备输入资源[self.装备内容显示[i]]:置坐标(62 + xx * 170,103 + yy * 40)",
			"zt",
			"装备内容显示",
			"特效",
			"装备内容显示",
			"词条定制文本",
			"装备词条显示",
			"词条定制文本",
			"宝宝定制文本",
			"词条定制文本",
			"词条数额输入[v]",
			"发送装备",
			"定制词条选项",
			"定制灵饰",
			"神话词条输入",
			"装备词条输入",
			"神话词条输入",
			"词条数额输入[v]",
			"词条数额输入",
			"词条数额",
			"[self.词条数额显示[i]]",
			"词条数额输入",
			"装备词条显示",
			"词条数字显示",
			"神话词条输入",
			"词条数额显示",
			"神话词条输入",
			"角色ID",
			"定制宝宝输入",
			"定制宝宝类型",
			"装备内容显示",
			"定制词条显示",
			"装备输入资源",
			"定制词条显示",
			"定制词条",
			"装备内容显示",
			"神话输入",
			"装备内容显示",
			"装备词条显示",
			"装备内容显示",
			"装备输入资源",
			"装备内容显示",
			"词条",
			"神话词条",
			"定制词条部位",
			"装备内容显示",
			"定制词条部位",
			"装备内容显示",
			"定制装备显示",
			"定制装备",
			"for",
			"定制词条部位",
			"装备类型选项",
			"装备类型下拉",
			"资源[11]",
			"装备类型下拉",
			"定制装备显示",
			"定制装备",
			"定制装备选项",
			"定制宝宝部位",
			"定制词条部位",
			"定制宝宝部位"
		],
		"highlight": true,
		"in_selection": false,
		"preserve_case": false,
		"regex": false,
		"replace_history":
		[
			"素材修复.wdf",
			"zts",
			"30002",
			"家具图标.wdf",
			""
		],
		"reverse": false,
		"show_context": true,
		"use_buffer2": true,
		"whole_word": false,
		"wrap": true
	},
	"groups":
	[
		{
			"selected": 12,
			"sheets":
			[
				{
					"buffer": 0,
					"file": "script/资源类/文件类.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 6280,
						"regions":
						{
						},
						"selection":
						[
							[
								418,
								418
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								5,
								14,
								11,
								48,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 13,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 12,
					"type": "text"
				},
				{
					"buffer": 1,
					"file": "script/初系统/功能界面.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 69779,
						"regions":
						{
						},
						"selection":
						[
							[
								21359,
								21359
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								5,
								14,
								11,
								48,
								48,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 533,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"translate_tabs_to_spaces": false
						},
						"translation.x": 0.0,
						"translation.y": 16373.0,
						"zoom_level": 1.0
					},
					"stack_index": 11,
					"type": "text"
				},
				{
					"buffer": 2,
					"file": "script/初系统/登陆.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 2964,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								0
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								16,
								0,
								25,
								31,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 71,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 803.0,
						"zoom_level": 1.0
					},
					"stack_index": 10,
					"type": "text"
				},
				{
					"buffer": 3,
					"file": "Main.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 3327,
						"regions":
						{
						},
						"selection":
						[
							[
								1617,
								1617
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								6,
								16,
								0,
								25,
								31,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 52,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 207.0,
						"zoom_level": 1.0
					},
					"stack_index": 8,
					"type": "text"
				},
				{
					"buffer": 4,
					"file": "script/ForYourOwnUse/PackClient.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 3020,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								3020
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 5,
					"type": "text"
				},
				{
					"buffer": 5,
					"file": "script/ForYourOwnUse/MessagePack.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 29611,
						"regions":
						{
						},
						"selection":
						[
							[
								15449,
								15453
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 430,
							"origin_encoding": "ASCII",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 8807.0,
						"zoom_level": 1.0
					},
					"stack_index": 7,
					"type": "text"
				},
				{
					"buffer": 6,
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 3138,
						"regions":
						{
							"match":
							{
								"flags": 112,
								"regions":
								[
									[
										227,
										233
									],
									[
										364,
										370
									],
									[
										401,
										407
									],
									[
										485,
										491
									],
									[
										635,
										641
									],
									[
										699,
										705
									],
									[
										709,
										715
									],
									[
										796,
										802
									],
									[
										938,
										944
									],
									[
										1124,
										1130
									],
									[
										1143,
										1149
									],
									[
										1176,
										1182
									],
									[
										1193,
										1199
									],
									[
										1226,
										1232
									],
									[
										1246,
										1252
									],
									[
										1279,
										1285
									],
									[
										1298,
										1304
									],
									[
										1596,
										1602
									],
									[
										1857,
										1863
									],
									[
										2141,
										2147
									],
									[
										2294,
										2300
									],
									[
										2576,
										2582
									],
									[
										2846,
										2852
									],
									[
										2887,
										2893
									],
									[
										2906,
										2912
									],
									[
										3056,
										3062
									]
								],
								"scope": ""
							}
						},
						"selection":
						[
							[
								2348,
								2348
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"detect_indentation": false,
							"function_name_status_row": 64,
							"line_numbers": false,
							"output_tag": 1,
							"result_base_dir": "",
							"result_file_regex": "^([^ 	].*):$",
							"result_line_regex": "^ +([0-9]+):",
							"scroll_past_end": true,
							"syntax": "Packages/Default/Find Results.hidden-tmLanguage"
						},
						"translation.x": 0.0,
						"translation.y": 756.0,
						"zoom_level": 1.0
					},
					"stack_index": 2,
					"type": "text"
				},
				{
					"buffer": 7,
					"file": "gge引擎.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 12683,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								0
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"translate_tabs_to_spaces": false
						},
						"translation.x": -0.0,
						"translation.y": 9535.0,
						"zoom_level": 1.0
					},
					"stack_index": 1,
					"type": "text"
				},
				{
					"buffer": 8,
					"file": "script/ForYourOwnUse/TcpClient.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 1592,
						"regions":
						{
						},
						"selection":
						[
							[
								1292,
								1292
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 37,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": -0.0,
						"translation.y": 472.0,
						"zoom_level": 1.0
					},
					"stack_index": 3,
					"type": "text"
				},
				{
					"buffer": 9,
					"file": "script/ForYourOwnUse/PullClient.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 1982,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								1982
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 52.0,
						"zoom_level": 1.0
					},
					"stack_index": 4,
					"type": "text"
				},
				{
					"buffer": 10,
					"file": "script/ForYourOwnUse/Client.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 6856,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								6856
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 4,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 0.0,
						"zoom_level": 1.0
					},
					"stack_index": 6,
					"type": "text"
				},
				{
					"buffer": 11,
					"file": "script/全局/自己_专用.LUA",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 14290,
						"regions":
						{
						},
						"selection":
						[
							[
								0,
								14290
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 0,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax"
						},
						"translation.x": 0.0,
						"translation.y": 7066.0,
						"zoom_level": 1.0
					},
					"stack_index": 9,
					"type": "text"
				},
				{
					"buffer": 12,
					"file": "script/ForYourOwnUse/服务连接.lua",
					"semi_transient": false,
					"settings":
					{
						"buffer_size": 4491,
						"regions":
						{
						},
						"selection":
						[
							[
								3599,
								3599
							]
						],
						"settings":
						{
							"BracketHighlighterBusy": false,
							"bh_regions":
							[
								"bh_unmatched",
								"bh_unmatched_center",
								"bh_unmatched_open",
								"bh_unmatched_close",
								"bh_unmatched_content",
								"bh_angle",
								"bh_angle_center",
								"bh_angle_open",
								"bh_angle_close",
								"bh_angle_content",
								"bh_default",
								"bh_default_center",
								"bh_default_open",
								"bh_default_close",
								"bh_default_content",
								"bh_round",
								"bh_round_center",
								"bh_round_open",
								"bh_round_close",
								"bh_round_content",
								"bh_tag",
								"bh_tag_center",
								"bh_tag_open",
								"bh_tag_close",
								"bh_tag_content",
								"bh_double_quote",
								"bh_double_quote_center",
								"bh_double_quote_open",
								"bh_double_quote_close",
								"bh_double_quote_content",
								"bh_square",
								"bh_square_center",
								"bh_square_open",
								"bh_square_close",
								"bh_square_content",
								"bh_curly",
								"bh_curly_center",
								"bh_curly_open",
								"bh_curly_close",
								"bh_curly_content",
								"bh_single_quote",
								"bh_single_quote_center",
								"bh_single_quote_open",
								"bh_single_quote_close",
								"bh_single_quote_content",
								"bh_regex",
								"bh_regex_center",
								"bh_regex_open",
								"bh_regex_close",
								"bh_regex_content",
								"bh_c_define",
								"bh_c_define_center",
								"bh_c_define_open",
								"bh_c_define_close",
								"bh_c_define_content"
							],
							"c_time":
							[
								128,
								3,
								99,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								100,
								97,
								116,
								101,
								116,
								105,
								109,
								101,
								10,
								113,
								0,
								67,
								10,
								7,
								233,
								7,
								9,
								12,
								14,
								13,
								0,
								0,
								0,
								113,
								1,
								133,
								113,
								2,
								82,
								113,
								3,
								46
							],
							"color_scheme": "Packages/User/Color Highlighter/themes/Monokai.tmTheme",
							"function_name_status_row": 133,
							"origin_encoding": "UTF-8",
							"syntax": "Packages/Lua/Lua.sublime-syntax",
							"tab_size": 2,
							"translate_tabs_to_spaces": true
						},
						"translation.x": 0.0,
						"translation.y": 41.0,
						"zoom_level": 1.0
					},
					"stack_index": 0,
					"type": "text"
				}
			]
		}
	],
	"incremental_find":
	{
		"height": 31.0
	},
	"input":
	{
		"height": 34.0
	},
	"layout":
	{
		"cells":
		[
			[
				0,
				0,
				1,
				1
			]
		],
		"cols":
		[
			0.0,
			1.0
		],
		"rows":
		[
			0.0,
			1.0
		]
	},
	"menu_visible": true,
	"output.exec":
	{
		"height": 661.0
	},
	"output.find_results":
	{
		"height": 0.0
	},
	"pinned_build_system": "Packages/Lua/ggegame.sublime-build",
	"project": "游戏模板.sublime-project",
	"replace":
	{
		"height": 58.0
	},
	"save_all_on_build": true,
	"select_file":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"select_project":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"select_symbol":
	{
		"height": 0.0,
		"last_filter": "",
		"selected_items":
		[
		],
		"width": 0.0
	},
	"selected_group": 0,
	"settings":
	{
	},
	"show_minimap": true,
	"show_open_files": false,
	"show_tabs": true,
	"side_bar_visible": true,
	"side_bar_width": 252.0,
	"status_bar_visible": true,
	"template_settings":
	{
	}
}
