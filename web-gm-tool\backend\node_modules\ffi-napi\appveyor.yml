# http://www.appveyor.com/docs/appveyor-yml

# Test against these versions of Node.js.
environment:
  # Test against these versions of Node.js and io.js
  matrix:
    # node.js
    - nodejs_version: "10"
    - nodejs_version: "12"
    - nodejs_version: "14"

platform:
  - x86
  - x64

# Install scripts. (runs after repo cloning)
install:
  - python -V
  - set PYTHON=python
  - ps: Install-Product node $env:nodejs_version $env:platform
  - node -p process.arch
  - node -p process.version
  - npm install --build-from-source

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  # run tests
  - npm test

after_test:
  - ps: If ($env:nodejs_version -eq "12") { npm run prebuild --v8_enable_pointer_compression=false --v8_enable_31bit_smis_on_64bit_arch=false }

# Don't actually build.
build: off

# Set build version format here instead of in the admin panel.
version: "{build}"

artifacts:
  - path: prebuilds
    name: $(APPVEYOR_REPO_TAG_NAME)-win-$(PLATFORM)
    type: zip

deploy:
  - provider: GitHub
    artifact: /.*\.zip/
    draft: false
    prerelease: true
    auth_token: $(PREBUILD_GITHUB_TOKEN)
    on:
      appveyor_repo_tag: true
      nodejs_version: "12"
