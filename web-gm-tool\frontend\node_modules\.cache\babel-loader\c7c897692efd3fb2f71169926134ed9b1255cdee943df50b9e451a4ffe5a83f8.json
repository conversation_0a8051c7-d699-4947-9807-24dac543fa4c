{"ast": null, "code": "import { tinycolor } from './index.js';\nexport * from './index.js';\nexport * from './css-color-names.js';\nexport * from './readability.js';\nexport * from './to-ms-filter.js';\nexport * from './from-ratio.js';\nexport * from './format-input.js';\nexport * from './random.js';\nexport * from './interfaces.js';\nexport * from './conversion.js';\n// kept for backwards compatability with v1\nexport default tinycolor;", "map": {"version": 3, "names": ["tinycolor"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@ctrl/tinycolor/dist/module/public_api.js"], "sourcesContent": ["import { tinycolor } from './index.js';\nexport * from './index.js';\nexport * from './css-color-names.js';\nexport * from './readability.js';\nexport * from './to-ms-filter.js';\nexport * from './from-ratio.js';\nexport * from './format-input.js';\nexport * from './random.js';\nexport * from './interfaces.js';\nexport * from './conversion.js';\n// kept for backwards compatability with v1\nexport default tinycolor;\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,YAAY;AACtC,cAAc,YAAY;AAC1B,cAAc,sBAAsB;AACpC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB;AACjC,cAAc,iBAAiB;AAC/B,cAAc,mBAAmB;AACjC,cAAc,aAAa;AAC3B,cAAc,iBAAiB;AAC/B,cAAc,iBAAiB;AAC/B;AACA,eAAeA,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}