{"ast": null, "code": "import { supportBigInt } from \"./supportUtil\";\nexport function isEmpty(value) {\n  return !value && value !== 0 && !Number.isNaN(value) || !String(value).trim();\n}\n\n/**\n * Format string number to readable number\n */\nexport function trimNumber(numStr) {\n  var str = numStr.trim();\n  var negative = str.startsWith('-');\n  if (negative) {\n    str = str.slice(1);\n  }\n  str = str\n  // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`\n  .replace(/(\\.\\d*[^0])0*$/, '$1')\n  // Remove useless decimal. `1.` => `1`\n  .replace(/\\.0*$/, '')\n  // Remove integer 0. `0001` => `1`, 000.1' => `.1`\n  .replace(/^0+/, '');\n  if (str.startsWith('.')) {\n    str = \"0\".concat(str);\n  }\n  var trimStr = str || '0';\n  var splitNumber = trimStr.split('.');\n  var integerStr = splitNumber[0] || '0';\n  var decimalStr = splitNumber[1] || '0';\n  if (integerStr === '0' && decimalStr === '0') {\n    negative = false;\n  }\n  var negativeStr = negative ? '-' : '';\n  return {\n    negative: negative,\n    negativeStr: negativeStr,\n    trimStr: trimStr,\n    integerStr: integerStr,\n    decimalStr: decimalStr,\n    fullStr: \"\".concat(negativeStr).concat(trimStr)\n  };\n}\nexport function isE(number) {\n  var str = String(number);\n  return !Number.isNaN(Number(str)) && str.includes('e');\n}\n\n/**\n * [Legacy] Convert 1e-9 to 0.000000001.\n * This may lose some precision if user really want 1e-9.\n */\nexport function getNumberPrecision(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));\n    var decimalMatch = numStr.match(/\\.(\\d+)/);\n    if (decimalMatch !== null && decimalMatch !== void 0 && decimalMatch[1]) {\n      precision += decimalMatch[1].length;\n    }\n    return precision;\n  }\n  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;\n}\n\n/**\n * Convert number (includes scientific notation) to -xxx.yyy format\n */\nexport function num2str(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    if (number > Number.MAX_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);\n    }\n    if (number < Number.MIN_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);\n    }\n    numStr = number.toFixed(getNumberPrecision(numStr));\n  }\n  return trimNumber(numStr).fullStr;\n}\nexport function validateNumber(num) {\n  if (typeof num === 'number') {\n    return !Number.isNaN(num);\n  }\n\n  // Empty\n  if (!num) {\n    return false;\n  }\n  return (\n    // Normal type: 11.28\n    /^\\s*-?\\d+(\\.\\d+)?\\s*$/.test(num) ||\n    // Pre-number: 1.\n    /^\\s*-?\\d+\\.\\s*$/.test(num) ||\n    // Post-number: .1\n    /^\\s*-?\\.\\d+\\s*$/.test(num)\n  );\n}", "map": {"version": 3, "names": ["supportBigInt", "isEmpty", "value", "Number", "isNaN", "String", "trim", "trimNumber", "numStr", "str", "negative", "startsWith", "slice", "replace", "concat", "trimStr", "splitNumber", "split", "integerStr", "decimalStr", "negativeStr", "fullStr", "isE", "number", "includes", "getNumberPrecision", "precision", "indexOf", "decimalMatch", "match", "length", "validateNumber", "num2str", "MAX_SAFE_INTEGER", "BigInt", "toString", "MIN_SAFE_INTEGER", "toFixed", "num", "test"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@rc-component/mini-decimal/es/numberUtil.js"], "sourcesContent": ["import { supportBigInt } from \"./supportUtil\";\nexport function isEmpty(value) {\n  return !value && value !== 0 && !Number.isNaN(value) || !String(value).trim();\n}\n\n/**\n * Format string number to readable number\n */\nexport function trimNumber(numStr) {\n  var str = numStr.trim();\n  var negative = str.startsWith('-');\n  if (negative) {\n    str = str.slice(1);\n  }\n  str = str\n  // Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`\n  .replace(/(\\.\\d*[^0])0*$/, '$1')\n  // Remove useless decimal. `1.` => `1`\n  .replace(/\\.0*$/, '')\n  // Remove integer 0. `0001` => `1`, 000.1' => `.1`\n  .replace(/^0+/, '');\n  if (str.startsWith('.')) {\n    str = \"0\".concat(str);\n  }\n  var trimStr = str || '0';\n  var splitNumber = trimStr.split('.');\n  var integerStr = splitNumber[0] || '0';\n  var decimalStr = splitNumber[1] || '0';\n  if (integerStr === '0' && decimalStr === '0') {\n    negative = false;\n  }\n  var negativeStr = negative ? '-' : '';\n  return {\n    negative: negative,\n    negativeStr: negativeStr,\n    trimStr: trimStr,\n    integerStr: integerStr,\n    decimalStr: decimalStr,\n    fullStr: \"\".concat(negativeStr).concat(trimStr)\n  };\n}\nexport function isE(number) {\n  var str = String(number);\n  return !Number.isNaN(Number(str)) && str.includes('e');\n}\n\n/**\n * [Legacy] Convert 1e-9 to 0.000000001.\n * This may lose some precision if user really want 1e-9.\n */\nexport function getNumberPrecision(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));\n    var decimalMatch = numStr.match(/\\.(\\d+)/);\n    if (decimalMatch !== null && decimalMatch !== void 0 && decimalMatch[1]) {\n      precision += decimalMatch[1].length;\n    }\n    return precision;\n  }\n  return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;\n}\n\n/**\n * Convert number (includes scientific notation) to -xxx.yyy format\n */\nexport function num2str(number) {\n  var numStr = String(number);\n  if (isE(number)) {\n    if (number > Number.MAX_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);\n    }\n    if (number < Number.MIN_SAFE_INTEGER) {\n      return String(supportBigInt() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);\n    }\n    numStr = number.toFixed(getNumberPrecision(numStr));\n  }\n  return trimNumber(numStr).fullStr;\n}\nexport function validateNumber(num) {\n  if (typeof num === 'number') {\n    return !Number.isNaN(num);\n  }\n\n  // Empty\n  if (!num) {\n    return false;\n  }\n  return (\n    // Normal type: 11.28\n    /^\\s*-?\\d+(\\.\\d+)?\\s*$/.test(num) ||\n    // Pre-number: 1.\n    /^\\s*-?\\d+\\.\\s*$/.test(num) ||\n    // Post-number: .1\n    /^\\s*-?\\.\\d+\\s*$/.test(num)\n  );\n}"], "mappings": "AAAA,SAASA,aAAa,QAAQ,eAAe;AAC7C,OAAO,SAASC,OAAOA,CAACC,KAAK,EAAE;EAC7B,OAAO,CAACA,KAAK,IAAIA,KAAK,KAAK,CAAC,IAAI,CAACC,MAAM,CAACC,KAAK,CAACF,KAAK,CAAC,IAAI,CAACG,MAAM,CAACH,KAAK,CAAC,CAACI,IAAI,CAAC,CAAC;AAC/E;;AAEA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,MAAM,EAAE;EACjC,IAAIC,GAAG,GAAGD,MAAM,CAACF,IAAI,CAAC,CAAC;EACvB,IAAII,QAAQ,GAAGD,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC;EAClC,IAAID,QAAQ,EAAE;IACZD,GAAG,GAAGA,GAAG,CAACG,KAAK,CAAC,CAAC,CAAC;EACpB;EACAH,GAAG,GAAGA;EACN;EAAA,CACCI,OAAO,CAAC,gBAAgB,EAAE,IAAI;EAC/B;EAAA,CACCA,OAAO,CAAC,OAAO,EAAE,EAAE;EACpB;EAAA,CACCA,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;EACnB,IAAIJ,GAAG,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE;IACvBF,GAAG,GAAG,GAAG,CAACK,MAAM,CAACL,GAAG,CAAC;EACvB;EACA,IAAIM,OAAO,GAAGN,GAAG,IAAI,GAAG;EACxB,IAAIO,WAAW,GAAGD,OAAO,CAACE,KAAK,CAAC,GAAG,CAAC;EACpC,IAAIC,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG;EACtC,IAAIG,UAAU,GAAGH,WAAW,CAAC,CAAC,CAAC,IAAI,GAAG;EACtC,IAAIE,UAAU,KAAK,GAAG,IAAIC,UAAU,KAAK,GAAG,EAAE;IAC5CT,QAAQ,GAAG,KAAK;EAClB;EACA,IAAIU,WAAW,GAAGV,QAAQ,GAAG,GAAG,GAAG,EAAE;EACrC,OAAO;IACLA,QAAQ,EAAEA,QAAQ;IAClBU,WAAW,EAAEA,WAAW;IACxBL,OAAO,EAAEA,OAAO;IAChBG,UAAU,EAAEA,UAAU;IACtBC,UAAU,EAAEA,UAAU;IACtBE,OAAO,EAAE,EAAE,CAACP,MAAM,CAACM,WAAW,CAAC,CAACN,MAAM,CAACC,OAAO;EAChD,CAAC;AACH;AACA,OAAO,SAASO,GAAGA,CAACC,MAAM,EAAE;EAC1B,IAAId,GAAG,GAAGJ,MAAM,CAACkB,MAAM,CAAC;EACxB,OAAO,CAACpB,MAAM,CAACC,KAAK,CAACD,MAAM,CAACM,GAAG,CAAC,CAAC,IAAIA,GAAG,CAACe,QAAQ,CAAC,GAAG,CAAC;AACxD;;AAEA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACF,MAAM,EAAE;EACzC,IAAIf,MAAM,GAAGH,MAAM,CAACkB,MAAM,CAAC;EAC3B,IAAID,GAAG,CAACC,MAAM,CAAC,EAAE;IACf,IAAIG,SAAS,GAAGvB,MAAM,CAACK,MAAM,CAACI,KAAK,CAACJ,MAAM,CAACmB,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;IAC9D,IAAIC,YAAY,GAAGpB,MAAM,CAACqB,KAAK,CAAC,SAAS,CAAC;IAC1C,IAAID,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;MACvEF,SAAS,IAAIE,YAAY,CAAC,CAAC,CAAC,CAACE,MAAM;IACrC;IACA,OAAOJ,SAAS;EAClB;EACA,OAAOlB,MAAM,CAACgB,QAAQ,CAAC,GAAG,CAAC,IAAIO,cAAc,CAACvB,MAAM,CAAC,GAAGA,MAAM,CAACsB,MAAM,GAAGtB,MAAM,CAACmB,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC;AACrG;;AAEA;AACA;AACA;AACA,OAAO,SAASK,OAAOA,CAACT,MAAM,EAAE;EAC9B,IAAIf,MAAM,GAAGH,MAAM,CAACkB,MAAM,CAAC;EAC3B,IAAID,GAAG,CAACC,MAAM,CAAC,EAAE;IACf,IAAIA,MAAM,GAAGpB,MAAM,CAAC8B,gBAAgB,EAAE;MACpC,OAAO5B,MAAM,CAACL,aAAa,CAAC,CAAC,GAAGkC,MAAM,CAACX,MAAM,CAAC,CAACY,QAAQ,CAAC,CAAC,GAAGhC,MAAM,CAAC8B,gBAAgB,CAAC;IACtF;IACA,IAAIV,MAAM,GAAGpB,MAAM,CAACiC,gBAAgB,EAAE;MACpC,OAAO/B,MAAM,CAACL,aAAa,CAAC,CAAC,GAAGkC,MAAM,CAACX,MAAM,CAAC,CAACY,QAAQ,CAAC,CAAC,GAAGhC,MAAM,CAACiC,gBAAgB,CAAC;IACtF;IACA5B,MAAM,GAAGe,MAAM,CAACc,OAAO,CAACZ,kBAAkB,CAACjB,MAAM,CAAC,CAAC;EACrD;EACA,OAAOD,UAAU,CAACC,MAAM,CAAC,CAACa,OAAO;AACnC;AACA,OAAO,SAASU,cAAcA,CAACO,GAAG,EAAE;EAClC,IAAI,OAAOA,GAAG,KAAK,QAAQ,EAAE;IAC3B,OAAO,CAACnC,MAAM,CAACC,KAAK,CAACkC,GAAG,CAAC;EAC3B;;EAEA;EACA,IAAI,CAACA,GAAG,EAAE;IACR,OAAO,KAAK;EACd;EACA;IACE;IACA,uBAAuB,CAACC,IAAI,CAACD,GAAG,CAAC;IACjC;IACA,iBAAiB,CAACC,IAAI,CAACD,GAAG,CAAC;IAC3B;IACA,iBAAiB,CAACC,IAAI,CAACD,GAAG;EAAC;AAE/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}