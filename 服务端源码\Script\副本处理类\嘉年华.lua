-- @Author: 作者QQ381990860
-- @Date:   2024-01-07 01:18:52
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2025-05-20 02:04:19
local 嘉年华 = class()

function 嘉年华:初始化()
 self.活动开关 = false
end



function 嘉年华:开启活动()

	self:添加NPC()
	发送公告("#G(嘉年华)#P活动开启，嘉年华副本开启,有需求的玩家去长安皇宫门口找超级红孩儿领取,活动时间持续4小时,期间定时投放美味,挑战完成可以获得积分！")
	广播消息({内容=format("#G(嘉年华)#P活动开启，嘉年华副本开启,有需求的玩家去长安皇宫门口找超级红孩儿领取,活动时间持续4小时,期间定时投放美味,挑战完成可以获得积分！"),频道="hd"})
end

function 嘉年华:关闭活动()
		发送公告("#R(嘉年华)#W活动已经结束，已处战斗中的玩家在战斗结束后依然可以获得奖励。感谢大家的参与！")
		广播消息({内容=format("#R(嘉年华)#W活动已经结束，已处战斗中的玩家在战斗结束后依然可以获得奖励。感谢大家的参与！"),频道="hd"})
end


function 嘉年华:更新副本任务(id,任务id,进程,时间)
	if 任务id and 任务数据[任务id] then
		if 进程 then
			任务数据[任务id].进程 = 进程
		end
		if 时间 then
			任务数据[任务id].结束=时间
		end
	end
	if 玩家数据[id].队伍==0 or not 玩家数据[id].队伍 then
		玩家数据[id].角色:刷新任务跟踪()
	else
		local 队伍id=玩家数据[id].队伍
		 for n=1,#队伍数据[队伍id].成员数据 do
		 	玩家数据[队伍数据[队伍id].成员数据[n]].角色:刷新任务跟踪()
		 end
	end
end
function 嘉年华:设置副本任务(id)
  if 玩家数据[id].队伍==0 then
	    添加最后对话(id,"此活动最少需要一人组队参加。")
	    return
  elseif 取队伍人数(id)<1 then
	    添加最后对话(id,"此活动最少需要一人组队参加。")
	    return
  -- elseif 取等级要求(id,60)==false then
	 --    添加最后对话(id,"此活动要求最低等级不能小于60级，队伍中有成员等级未达到此要求。")
	 --    return
   elseif 活动次数查询(id,"嘉年华")==false then
   		 添加最后对话(id,"队伍中有成员等级已完成该活动。")
          return
   elseif 取队伍任务(玩家数据[id].队伍,390) then
	    常规提示(id,"#Y/队伍中已有队员领取过此任务了")
	    return
  end
  local 队伍id=玩家数据[id].队伍
  local id组={}
  for n=1,#队伍数据[队伍id].成员数据 do
      id组[n]=队伍数据[队伍id].成员数据[n]
  end
  local 任务id=取唯一识别码(390)
  任务数据[任务id]={
    id=任务id,
    起始=os.time(),
    结束=14400,
    玩家id=id,
    进程=1,
    类型=390
  }
  玩家数据[id].角色:添加任务(任务id,1)

  --发送数据(玩家数据[id].连接id,1501,{名称="游奕灵官",模型="超级红海儿",对话=format("你成功领取了天降辰星任务，请立即前往寻找#Y/%s#W/接受考验。")})
end

function 嘉年华:NPC对话内容(id,名称,地图,任务id)
	local 对话数据={}
	if 地图==1501 and  名称 == "戏班班主" and 任务数据[任务id].进程==3  then
		对话数据.对话="当然可以，少侠入座，待我唱来。"
    	对话数据.选项={"落座观看!(战斗)","等一下"}
     elseif 地图==1501 and  名称 == "吹牛王" and 任务数据[任务id].进程==5  then
         --do
		对话数据.对话="不瞒少侠，我早已列出了能够帮助老班头的贵人们，少侠们若是能帮我跑一趟，说服她们.那老班头的戏肯定就能大功告成!"
	    对话数据.选项={"这忙当然要帮了!(传送)","让我想想。"}
	 elseif 地图==1033 and  名称 == "小桃红" and 任务数据[任务id].进程==6 then
	 	对话数据.对话="哇，真的吗!少侠，你怎么知道，我其实从小就有一个梦想，就是可以进入戏曲界，把我的歌声展示给所有人听!,帮忙问问妈妈"
	    对话数据.选项={"好的我这就去问问","让我想想。"}
	  elseif 地图==1033 and  名称 == "陈妈妈" and 任务数据[任务id].进程==7 then
	    对话数据.对话="哪来的家伙．竟然想骗走我家闺女!来人啊，把这群家伙赶走!"
	    对话数据.选项={"哇，怎么突然就动手了!(需战斗)","先跑再说。"}
     elseif 地图==1070 and  名称 == "许大娘" and 任务数据[任务id].进程==8 then
	    对话数据.对话="唉，我今天其实就是来把这些送给姑娘们的，反正都不用上咯!我家那个姑娘哦这么多年都没有嫁出去!布匹就在那边，你们就先拿去用吧。"
	    对话数据.选项={}
	    self:更新副本任务(id,任务id,9)
     elseif 地图==1501 and  名称 == "吹牛王" and 任务数据[任务id].进程==11 then
	    对话数据.对话="看来万事俱备。那咱们快去找到戏班头告诉他这个好消息吧。"
	    对话数据.选项={}
	    self:更新副本任务(id,任务id,12)
     elseif 地图==1501 and  名称 == "戏班班主" and 任务数据[任务id].进程==12 then
	    对话数据.对话="谢谢，谢谢你们……其实。这段时间我也一直在改我的戏文。现在，请诸位和我再一次演这出戏目吧!"
	    对话数据.选项={"好那。大家一起来观赏吧!(战斗)","我们再准备一下"}
	end
	return 对话数据

end


function 嘉年华:NPC对话处理(id,名称,事件,地图,任务id)
	if 地图==1501 and 名称 == "戏班班主" and 事件=="落座观看!(战斗)" and 任务数据[任务id].进程==3  then
			战斗准备类:创建战斗(id,100139,任务id)
	elseif 地图==1501 and 名称 == "吹牛王" and 事件=="这忙当然要帮了!(传送)" and 任务数据[任务id].进程==5  then
			地图处理类:跳转地图(id,1033,36,36)
			self:更新副本任务(id,任务id,6)
	elseif 地图==1033 and 名称 == "小桃红" and 事件=="好的我这就去问问" and 任务数据[任务id].进程==6  then
			self:更新副本任务(id,任务id,7)
    elseif 地图==1033 and 名称 == "陈妈妈" and 事件=="哇，怎么突然就动手了!(需战斗)" and 任务数据[任务id].进程==7  then
			战斗准备类:创建战斗(id,100141,任务id)
    elseif 地图==1501 and 名称 == "戏班班主" and 事件=="好那。大家一起来观赏吧!(战斗)" and 任务数据[任务id].进程==12  then
			战斗准备类:创建战斗(id,100143,任务id)

	end

end

function 嘉年华:任务说明(玩家id,任务id)
	 local 名称="嘉年华"
	 local 说明=""
	 local 备注="嘉年华"
	 local 进程=任务数据[任务id].进程
	 local 分钟 = 取分(任务数据[任务id].结束-(os.time()-任务数据[任务id].起始))
	if 进程==1 then
		   说明="去建邺衙门\n找#G建邺城(142,63)#W的#G骨精灵#W\n他似乎有些事情给你做W,剩余时间#R"..分钟.."#W分钟"
    elseif 进程==2 then
    	说明="在东海湾(112,36)的海边寻找到#G影精灵#W,剩余时间#R"..分钟.."#W分钟"
	elseif 进程==3 then
    	说明="去建邺城(84,31)观赏#G戏班班主#W的新戏表演,剩余时间#R"..分钟.."#W分钟"
    elseif 进程==4 then
    	说明="#G癫散班主#W突然出现在#G建邺城(26,98)#W,并且将这出戏批评了一通.剩余时间#R"..分钟.."#W分钟"
    elseif 进程==5 then
    	说明="戏班老板失落的离开了,找到旁边的#G吹牛王[建邺城(88,38)]#W对你们挤眉弄眼,似乎有话要说.剩余时间#R"..分钟.."#W分钟"
    elseif 进程==6 then
    	说明="吹牛王列出的第一个是........,到留香阁(42,39)找#G小桃红#W.剩余时间#R"..分钟.."#W分钟"
	elseif 进程==7 then
    	说明="问问旁边的#G陈妈妈[留香阁(33,33)]#W.剩余时间#R"..分钟.."#W分钟"
	elseif 进程==8 then
    	说明="听说长寿村(80,144)的#G许大娘#W有好的布匹.去问问他制作戏服的布匹.剩余时间#R"..分钟.."#W分钟"
	elseif 进程==9 then
    	说明="找到留香阁(60,25)#G做布匹的木箱#W.制作戏服.剩余时间#R"..分钟.."#W分钟"
	elseif 进程==10 then
    	说明="手艺还不错嘛!和#G影精灵[留香阁(8,23)]#W清点一下准备进度吧!剩余时间#R"..分钟.."#W分钟"
	elseif 进程==11 then
    	说明="带着乐器和戏服回去找#G吹牛王[建邺城(88,38)]#W吧!剩余时间#R"..分钟.."#W分钟"
	elseif 进程==12 then
    	说明="将准备好的一切告诉#G戏班班主[建邺城(84,31)]#W吧!剩余时间#R"..分钟.."#W分钟"
	 elseif 进程==13 then
    	说明="真的完美的演出!#G癫散班主[建邺城(26,98)]#W这回说不出话了.剩余时间#R"..分钟.."#W分钟"
     elseif 进程==14 then
    	说明="到西凉女国(58,45)找负责仙乐琴的#G礼部尚书#W.剩余时间#R"..分钟.."#W分钟"
	 -- elseif 进程==14 then
  --   	说明="咦@居然笑了.剩余时间#R"..分钟.."#H分钟"
		--  sjx = {地图=1506,x=104,y=38,名称="影精灵"}


	   end
	 return {名称,说明,备注}
end


function 嘉年华:添加NPC()
	local 任务id=取唯一识别码(391)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="骨精灵",
		模型="骨精灵",
		x=142,
		y=63,
		方向=0,
		地图编号=1501,
		地图名称=取地图名称(1501),
		小地图名称颜色=6,
		类型=391
	}
	地图处理类:添加单位(任务id)

	任务id=取唯一识别码(392)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="影精灵",
		模型="影精灵",
		x=112,
		y=36,
		方向=0,
		地图编号=1506,
		地图名称=取地图名称(1506),
		小地图名称颜色=6,
		类型=392
	}
	地图处理类:添加单位(任务id)

	任务id=取唯一识别码(393)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="癫散班主",
		模型="男人_老伯",
		x=26,
		y=98,
		方向=0,
		地图编号=1501,
		地图名称=取地图名称(1501),
		小地图名称颜色=6,
		类型=393
	}
	地图处理类:添加单位(任务id)

	任务id=取唯一识别码(394)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="做布匹的木箱",
		模型="宝箱",
		x=60,
		y=25,
		方向=0,
		地图编号=1033,
		地图名称=取地图名称(1033),
		小地图名称颜色=6,
		类型=394
	}
	地图处理类:添加单位(任务id)

	任务id=取唯一识别码(395)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="影精灵",
		模型="影精灵",
		x=8,
		y=23,
		方向=0,
		地图编号=1033,
		地图名称=取地图名称(1033),
		小地图名称颜色=6,
		类型=395
	}
	地图处理类:添加单位(任务id)
  任务id=取唯一识别码(396)
	任务数据[任务id]={
		id=任务id,
		销毁=true,
		起始=os.time(),
		结束=14400,
		名称="礼部尚书",
		模型="考官2",
		x=58,
		y=45,
		方向=0,
		地图编号=1040,
		地图名称=取地图名称(1040),
		小地图名称颜色=6,
		类型=396
	}
	地图处理类:添加单位(任务id)


end

function 嘉年华:刷出怪物()


 local 怪物名称 = {"香醇糕点","彩色珍馐","神秘的色彩珍馐"}
 local 地图坐标 = {1092,1501,1040,1193,1514}
  for n=1,#地图坐标 do
  	local 地图=地图坐标[n]
  	for i=1,取随机数(4,8) do
	      local xy=地图处理类.地图坐标[地图]:取随机点()
	      local 任务id=取唯一识别码(397)
	      随机序列=随机序列+1
	      任务数据[任务id]={
	        id=任务id,
	        起始=os.time(),
	        结束=1800,
	        玩家id=id,
	        名称=怪物名称[取随机数(1,2)],
	        行走开关=true,
	        x=xy.x,
	        y=xy.y,
	        地图编号=地图,
	        战斗显示=true,
	        地图名称=取地图名称(地图),
	        销毁=true,
	        类型=397,
	      }
	      if 任务数据[任务id].名称 == "香醇糕点" then
	      	 	任务数据[任务id].模型="进阶芙蓉仙子"
	      else
	      		任务数据[任务id].模型="蚌精"
	      end
	      地图处理类:添加单位(任务id)
	 end
	 if 取随机数()<=60 then
		 local xy=地图处理类.地图坐标[地图]:取随机点()
		 local 任务id=取唯一识别码(397)
		  随机序列=随机序列+1
		  任务数据[任务id]={
		      id=任务id,
		        起始=os.time(),
		        结束=1800,
		        玩家id=id,
		        名称="神秘的色彩珍馐",
		        模型="蚌精",
		        行走开关=true,
		        x=xy.x,
		        y=xy.y,
		        地图编号=地图,
		        战斗显示=true,
		        地图名称=取地图名称(地图),
		        销毁=true,
		        类型=397,
		     }
		    地图处理类:添加单位(任务id)
	   end
  end


广播消息({内容=format("#P(嘉年华)#Y食物盛典，各种糕点出现在了#G建业，江南野外，西凉女国，傲来国，花果山！".."#"..取随机数(1,110)),频道="xt"})



end





function 嘉年华:怪物对话内容(id,类型,标识,地图)
	local 对话数据={}
	对话数据.模型=任务数据[标识].模型
	对话数据.名称=任务数据[标识].名称
	if 任务数据[标识].战斗 then
		对话数据.对话="正在战斗中，请勿打扰。"
		return 对话数据
	end
	local 名称 = 任务数据[标识].名称
	if 类型==397 then
		对话数据.对话="点我有惊喜"
		对话数据.选项={"战斗","我点错了"}
		-- if 名称 == "香醇糕点" then---分开自己吧屏蔽去掉
 	-- 	对话数据.对话="点我有惊喜"
 	-- 	对话数据.选项={"战斗","我点错了"}

		-- elseif 名称 == "彩色珍馐" then
		-- 	对话数据.选项={"战斗","我点错了"}

		-- elseif 名称 == "神秘的色彩珍馐" then
		-- 	对话数据.选项={"战斗","我点错了"}


		-- end
	else
		if 取队员任务一致(id,390) then
			local 任务id=玩家数据[id].角色:取任务(390)
			if 类型==391 and 任务数据[任务id].进程==1  then
				对话数据.对话="点我有惊喜"
				对话数据.选项={"开启盛典","我点错了"}
             elseif  类型==392 and 任务数据[任务id].进程==2  then
             	对话数据.对话="刚才在建邺城，我听说戏班老板将要表演新戏，走走走，你们的第一站就去这儿吧!"
				对话数据.选项={"出发到看戏去咯!(传送)","等会，我准备一下。"}
 			elseif  类型==393 and 任务数据[任务id].进程==4 then
             	对话数据.对话="哟，小娃娃还不信我的评价?这三界之中，每天可不知道有多少人买票来看我癫散戏班的戏目。看来，可得给你这小娃娃上一课."
				对话数据.选项={"那就请班主赐教了!(需战斗)","等会，我准备一下。"}
			elseif  类型==394 and 任务数据[任务id].进程==9 then
             	对话数据.对话="你......会裁衣吗?"
				对话数据.选项={"我当然会啦!(需战斗)","还是找个裁缝问问吧。"}
            elseif  类型==395 and 任务数据[任务id].进程==10 then
             	对话数据.对话="准备完毕,该回去了"
				对话数据.选项={"返回建邺城!(传送)","等一下再回去吧。"}
             elseif  类型==393 and 任务数据[任务id].进程==13 then
             	对话数据.对话="时间也差不多了。你们该出发前往大唐芙蓉园参加表演了。"
				对话数据.选项={"走咯，出发出发!(传送)","再做点准备吧。"}
             elseif  类型==396 and 任务数据[任务id].进程==14 then
             	对话数据.对话="那是自然!宴会表演即将开始!你们可准备好?。"
				对话数据.选项={"好吧，那请大人赐教了。(需战斗)","让我们准备一下。"}
			end
		else
			 对话数据.对话="你是谁,我认识你么"
		end
	end

	-- -- if not self.活动开关 then
	-- -- 	对话数据.对话="今日嘉年华活动已经结束，再接再厉吧。"
	-- --     return 对话数据
	-- -- end

	-- local 名称 = 任务数据[标识].名称
	-- if 名称=="骠骑大将军" then

	-- end
	return 对话数据
end

function 嘉年华:怪物对话处理(id,名称,事件,类型,标识)
	id = id+0
	if 取队伍人数(id)<1 then 常规提示(id,"#Y/进行嘉年华活动最少必须由一人进行") return  end
	if 取等级要求(id,69)==false then 常规提示(id,"#Y/进行嘉年华活动至少要达到69级") return  end




	if 类型==397 then
		local 战斗类型
		if 名称 == "香醇糕点" and 事件=="战斗" then---分开自己吧屏蔽去掉
			战斗类型=100136
		elseif 名称 == "彩色珍馐" and 事件=="战斗"  then
			战斗类型=100137
		elseif 名称 == "神秘的色彩珍馐" and 事件=="战斗"  then
			战斗类型=100138
		end
		if 战斗类型 then
			战斗准备类:创建战斗(id,战斗类型,玩家数据[id].地图单位.标识)
			任务数据[玩家数据[id].地图单位.标识].战斗=true
			玩家数据[id].地图单位=nil
		end
	else
		if 取队员任务一致(id,390) then
			local 任务id = 玩家数据[id].角色:取任务(390)

			if 类型==391 and 事件=="开启盛典" and 任务数据[任务id].进程==1 then
					self:更新副本任务(id,任务id,2)
			elseif 类型==392 and 事件=="出发到看戏去咯!(传送)" and 任务数据[任务id].进程==2  then
				地图处理类:跳转地图(id,1501,76,37)
				self:更新副本任务(id,任务id,3)
            elseif 类型==393 and 事件=="那就请班主赐教了!(需战斗)" and 任务数据[任务id].进程==4  then
                 战斗准备类:创建战斗(id,100140,任务id)
            elseif 类型==394 and 事件=="我当然会啦!(需战斗)" and 任务数据[任务id].进程==9  then
                 战斗准备类:创建战斗(id,100142,任务id)
			elseif 类型==395 and 事件=="返回建邺城!(传送)" and 任务数据[任务id].进程==10  then
                 地图处理类:跳转地图(id,1501,76,37)
				 self:更新副本任务(id,任务id,11)
			elseif 类型==393  and 事件=="走咯，出发出发!(传送)" and 任务数据[任务id].进程==13  then
                 地图处理类:跳转地图(id,1040,58,70)
				 self:更新副本任务(id,任务id,14)
			elseif 类型==396  and 事件=="好吧，那请大人赐教了。(需战斗)" and 任务数据[任务id].进程==14  then
                 战斗准备类:创建战斗(id,100144,任务id)




			end




		end




	end

end




function 嘉年华:增加积分(id,num)
	if not 玩家数据[id].角色.数据.嘉年华 then
		玩家数据[id].角色.数据.嘉年华=0
	end
	玩家数据[id].角色.数据.嘉年华=玩家数据[id].角色.数据.嘉年华+num
   	常规提示(id,"#Y你获得了#R"..num.."#Y点嘉年华积分")

end


function 嘉年华:战斗胜利(id组,战斗类型,任务id)--在这里配置
	if 任务数据[任务id]==nil then
		return
	end
	for i=1,#id组 do
		local id = id组[i]---不用在写id=啥了 我这里统一写了 好的
		if 战斗类型==100136 then
           玩家数据[id].角色:自定义银子添加("嘉年华投放",1)
	       玩家数据[id].角色:添加活跃积分(3,"嘉年华投放",1)
	 		self:增加积分(id,1)
	        local 获得物品={}
	        for i=1,#自定义数据.嘉年华投放 do
	          if 取随机数()<=自定义数据.嘉年华投放[i].概率 then
	             获得物品[#获得物品+1]=自定义数据.嘉年华投放[i]
	          end
	        end
	        获得物品=删除重复(获得物品)
	        if 获得物品~=nil then
	            local 取编号=取随机数(1,#获得物品)
	            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
	                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
	                广播消息({内容=format("#S/(香醇糕点)#R/%s#Y/最终赶走了#R/%s#Y/，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
	            end
	        end
		elseif 战斗类型==100137 then
				玩家数据[id].角色:自定义银子添加("嘉年华投放",1)
		        玩家数据[id].角色:添加活跃积分(3,"嘉年华投放",1)
		 		self:增加积分(id,1)
		        local 获得物品={}
		        for i=1,#自定义数据.嘉年华投放 do
		          if 取随机数()<=自定义数据.嘉年华投放[i].概率 then
		             获得物品[#获得物品+1]=自定义数据.嘉年华投放[i]
		          end
		        end
		        获得物品=删除重复(获得物品)
		        if 获得物品~=nil then
		            local 取编号=取随机数(1,#获得物品)
		            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
		                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
		                广播消息({内容=format("#S/(彩色珍馐)#R/%s#Y/最终赶走了#R/%s#Y/，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
		            end
		        end
		elseif 战斗类型==100138 then
					玩家数据[id].角色:自定义银子添加("嘉年华投放",1)
			        玩家数据[id].角色:添加活跃积分(3,"嘉年华投放",1)
			 		self:增加积分(id,1)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华投放 do
			          if 取随机数()<=自定义数据.嘉年华投放[i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华投放[i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(神秘的色彩珍馐)#R/%s#Y/最终赶走了#R/%s#Y/，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,任务数据[任务id].名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end
		elseif 战斗类型==100139 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,5)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[1] do
			          if 取随机数()<=自定义数据.嘉年华副本[1][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[1][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end

		elseif 战斗类型==100140 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,6)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[2] do
			          if 取随机数()<=自定义数据.嘉年华副本[2][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[2][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end

        elseif 战斗类型==100141 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,8)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[3] do
			          if 取随机数()<=自定义数据.嘉年华副本[3][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[3][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end

        elseif 战斗类型==100142 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,10)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[4] do
			          if 取随机数()<=自定义数据.嘉年华副本[4][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[4][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end
        elseif 战斗类型==100143 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,12)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[5] do
			          if 取随机数()<=自定义数据.嘉年华副本[5][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[5][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end
        elseif 战斗类型==100144 then
		            玩家数据[id].角色:自定义银子添加("嘉年华副本",1)
			        玩家数据[id].角色:添加活跃积分(5,"嘉年华副本",1)
			 		self:增加积分(id,15)
			        local 获得物品={}
			        for i=1,#自定义数据.嘉年华副本[6] do
			          if 取随机数()<=自定义数据.嘉年华副本[6][i].概率 then
			             获得物品[#获得物品+1]=自定义数据.嘉年华副本[6][i]
			          end
			        end
			        获得物品=删除重复(获得物品)
			        if 获得物品~=nil then
			            local 取编号=取随机数(1,#获得物品)
			            if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
			                玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
			                广播消息({内容=format("#S/(嘉年华)#R/%s#Y/，获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
			            end
			        end
		            添加活动次数(id,"嘉年华")
		            玩家数据[id].角色:取消任务(任务id)
		end
 end


 if 战斗类型==100136 or 战斗类型==100137 or 战斗类型==100138  then
 		地图处理类:删除单位(任务数据[任务id].地图编号,任务数据[任务id].编号)
 		任务数据[任务id]=nil
 elseif 战斗类型==100139 then
 	self:更新副本任务(id组[1],任务id,4)
 elseif 战斗类型==100140 then
 	self:更新副本任务(id组[1],任务id,5)
 elseif 战斗类型==100141 then
 	self:更新副本任务(id组[1],任务id,8)
 elseif 战斗类型==100142 then
 	self:更新副本任务(id组[1],任务id,10)
 elseif 战斗类型==100143 then
 	self:更新副本任务(id组[1],任务id,13)



 	--self:更新副本任务(id组[1],任务id,15)
 end



end

return 嘉年华