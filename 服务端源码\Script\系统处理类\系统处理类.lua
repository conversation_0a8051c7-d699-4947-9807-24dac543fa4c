--======================================================================--

--======================================================================--
local 系统处理类 = class()
local jnzb = require("script/角色处理类/技能类")
local function 积分排序(a,b) return a.积分<b.积分 end
function 系统处理类:初始化() end
function 系统处理类:数据处理(id,序号,内容)
  -- __S服务:输出("开始加载科举题库"..id.."##"..序号)


  if 内容==nil then
    return 0
  end


  if 序号~=5 then
    if 内容~=nil and 内容.ip~=nil and f函数.读配置(程序目录 .. "ip封禁.ini", "ip", 内容.ip)=="1" or f函数.读配置(程序目录 .. "ip封禁.ini", "ip", 内容.ip)==1 then
        __S服务:输出(string.format("封禁ip的客户进入试图进入(%s):%s:%s", 内容.id, 内容.ip,id))
        发送数据(id,7,"您的已被禁止登陆!")
        return 0
    end
  end
  if 序号==1 or 序号==1.1 then
    self:账号验证(id,序号,内容)
  elseif 序号==2 then -- 申请创建角色
    if not 内容.账号 or 内容.账号 =="" then return end
    if f函数.文件是否存在([[data/]]..内容.账号)==false then  --未创建存档
        发送数据(id,7,"#Y/未找到你的账号")
        return 0
    else
        self.临时文件=读入文件([[data/]]..内容.账号..[[/信息.txt]])
        self.写入信息=table.loadstring(self.临时文件)
        if #self.写入信息>=5 then
          发送数据(id,7,"#Y/您无法再创建更多的角色了")
          发送数据(id,2)
          return 0
        end
        发送数据(id,3)
    end
  elseif 序号==3 then --创建新角色
    self:创建角色处理(id,内容)
  elseif 序号==4 then --选择角色进入游戏
    local 数字id=self:取角色选择id(内容.id,内容.账号)
    self:进入游戏(id,内容.账号,数字id,内容.ip,序号)
  elseif 序号==4.1 then --4.2 重连和管理工具都走这边
    local 数字id=self:取角色选择id(内容.id,内容.账号)
    self:断开游戏(数字id)
    self:进入游戏(id,内容.账号,数字id,内容.ip,序号)
  elseif 序号==5 then --选择角色进入游戏
    self:断开游戏(内容+0,id)
  elseif 序号==6 then
   self:宠物领养处理(id,内容)
  elseif 序号==7 then
   发送数据(id,10,玩家数据[内容.数字id].角色:取总数据())
  elseif 序号==8 then
    玩家数据[内容.数字id].角色:添加属性点(内容,id)
    发送数据(id,10,玩家数据[内容.数字id].角色:取总数据())
  elseif 序号==9 then
    玩家数据[内容.数字id].角色:升级处理(id)
  elseif 序号==10 then
    玩家数据[内容.数字id].角色:获取任务信息(id)
  elseif 序号==11 then
    玩家数据[内容.数字id].角色:设置快捷技能(内容)
  elseif 序号==12 then
    发送数据(id,41,玩家数据[内容.数字id].角色.数据.快捷技能)
  elseif 序号==13 then
    玩家数据[内容.数字id].角色:使用快捷技能(内容.序列)
  elseif 序号==13.1 then
    玩家数据[内容.数字id].角色:页面技能使用技能(内容)
  elseif 序号==14 then
    local id=内容.数字id
    if 内容.人物~=nil then
      玩家数据[id].角色.数据.修炼.当前=内容.人物
    end
    if 内容.bb~=nil then
      玩家数据[id].角色.数据.bb修炼.当前=内容.bb
    end
    常规提示(id,"#Y/更换修炼类型成功！")
    刷新修炼数据(id)
  elseif 序号==15 then
    if 玩家数据[内容.数字id].自动遇怪 then
        玩家数据[内容.数字id].自动遇怪 = nil
        常规提示(内容.数字id,"#Y/你关闭了自动遇怪功能")
    else
        玩家数据[内容.数字id].自动遇怪 = os.time()
        常规提示(内容.数字id,"#Y/你开启了自动遇怪功能")
    end
    发送数据(id,48,{遇怪=玩家数据[内容.数字id].自动遇怪})


  elseif 序号==16 then

     玩家数据[内容.数字id].好友:获取数据()
  elseif 序号==17 then --添加好友
    玩家数据[内容.数字id].好友:修改分组名称(内容.编号+0,内容.名称)
   elseif 序号==17.1 then
    玩家数据[内容.数字id].好友:创建新分组()
  elseif 序号==18 then --添加好友
    玩家数据[内容.数字id].好友:添加临时(tonumber(内容.id))


  elseif 序号==19 then --刷新好友数据


     玩家数据[内容.数字id].好友:添加好友(tonumber(内容.id))

  elseif 序号==20 then --更新好友数据

   玩家数据[内容.数字id].好友:删除好友(tonumber(内容.id))

  elseif 序号==21 then

    local 临时数据={}
    临时数据.模型=玩家数据[内容.数字id].角色.数据.模型
    临时数据.名称=玩家数据[内容.数字id].角色.数据.名称
    临时数据.对话="请选择好友队组"
    local 分组选项={}
    for i=1,#玩家数据[内容.数字id].好友.数据.分组 do
        if not 玩家数据[内容.数字id].好友.数据.分组[i].好友[内容.id] then
          分组选项[#分组选项+1]="自定义分组"..i
        end
    end
    if not 玩家数据[内容.数字id].好友.数据.黑名单[内容.id] then
       分组选项[#分组选项+1]="屏蔽名单"
    end
    临时数据.选项=分组选项
    玩家数据[内容.数字id].好友分组=内容.id
    发送数据(玩家数据[内容.数字id].连接id,1501,临时数据)



  elseif 序号==22 then
    local 查找数据 = 玩家数据[内容.数字id].好友:查找角色(内容.名称,内容.id)

    if 查找数据 and 查找数据.名称 and 查找数据.数字ID then
       发送数据(玩家数据[内容.数字id].连接id,53,{数据=查找数据})
     else
       常规提示(内容.数字id,"#Y对方当前不在线")
       return
     end
  elseif 序号==23 then --移除好友
      玩家数据[内容.数字id].好友:加入黑名单(tonumber(内容.id),内容.数据)

  elseif 序号==22.1 then
    self:成就数据处理(内容)

---------------------------------
  elseif 序号==24 then --请求发送消息
    玩家数据[内容.数字id].好友:发送消息(tonumber(内容.id),内容.数据)
  elseif 序号==25 then
    玩家数据[内容.数字id].好友:获取消息(内容.数据)
  elseif 序号==25.2 then
    玩家数据[内容.数字id].好友.正在聊天=tonumber(内容.id)
  elseif 序号==25.3 then
    玩家数据[内容.数字id].好友.正在聊天=nil


----------------------------------------------------------


  elseif 序号==26 then
    self:角色乘骑处理(内容)
  elseif 序号==27 then
    self:角色下骑处理(内容)
  elseif 序号==28 then
    玩家数据[内容.数字id].角色:取快捷技能(内容.数字id)
  elseif 序号==29 then
      仙玉商城类:取仙玉商城商品(内容.数字id,"杂货商品",nil,"银子商城")
  elseif 序号==30 then
    仙玉商城类:取仙玉商城商品(内容.数字id,内容.序列,92,内容.大类)
  elseif 序号==30.1 then
    仙玉商城类:仙玉商城购买(内容.数字id,内容.序列,内容.序列1,内容.数量,内容.大类)

  elseif 序号==31 then
    玩家数据[内容.数字id].角色:更新称谓(tonumber(内容.称谓ID))
  elseif 序号==32 then
  玩家数据[内容.数字id].经脉:增加奇经八脉(内容)
  elseif 序号==33 then
    self:玩家pk处理(内容)
  elseif 序号==34 then
    self:账号注册(id,序号,内容)
  elseif 序号==35 then --玩法介绍分类
    self:超级传送处理(内容)
  elseif 序号==36 then --玩法介绍内容
   self:会员福利领取(内容)
  elseif 序号==37 then
    玩家数据[内容.数字id].角色:删除称谓(玩家数据[内容.数字id].角色.数据.称谓[内容.称谓ID])
  elseif 序号==38 then
    self:生死劫处理(内容.数字id,内容)
  elseif 序号==39 then
    发送数据(玩家数据[内容.数字id].连接id,49,排行榜数据)
  elseif 序号==40 then
   self:梦幻指引处理(内容)
  elseif 序号==41 then
    self:自动寻路处理(内容)
  elseif 序号==42 then
      if 玩家数据[内容.数字id] and 玩家数据[内容.数字id].自动抓鬼 then
          常规提示(内容.数字id,"#Y/自动抓鬼已关闭需要请重新开启")
          玩家数据[内容.数字id].自动抓鬼 =nil
      end
  elseif 序号==43 then
    self:自动回收处理(内容)
  elseif 序号==44 then
    玩家数据[内容.数字id].经脉:切换奇经八脉处理(内容.数字id,内容.经脉)
  elseif 序号==45 then
    --玩家数据[内容.数字id].每日活动:数据处理(内容)
    self:月卡福利领取(内容)
  elseif 序号==46 then
     self:获取签到数据(内容.数字id)
  elseif 序号==47 then
     self:签到处理(内容.数字id,内容)
  elseif 序号==48 then
     self:地图npc传送处理(内容)
  elseif 序号==49 then
    地图处理类:移除玩家(内容.数字id)
  elseif 序号==149 then
    self:召唤兽卸下饰品处理(内容)
  elseif 序号==50 then
    地图处理类:加入玩家(内容.数字id,玩家数据[内容.数字id].角色.数据.地图数据.编号,玩家数据[内容.数字id].角色.数据.地图数据.x,玩家数据[内容.数字id].角色.数据.地图数据.y)
  elseif 序号==53 then --购买商会召唤兽
    self:商会购买召唤兽处理1(内容)
  elseif 序号==53.1 then --购买商会召唤兽
    self:商会购买召唤兽处理(内容)
  elseif 序号==54 then
     添加最后对话(内容.数字id,format("系统每隔两小时会自动刷新出新的召唤兽，当然你也可以在我这里都过耗费银子进行快速刷新。每次刷新都将消耗500000两银子，你是否需要进行刷新？"),{"请帮我刷新","不用了"})
  elseif 序号==54.1 then
     添加最后对话(内容.数字id,format("系统每隔两小时会自动刷新出新的召唤兽，当然你也可以在我这里都过耗费银子进行快速刷新。每次刷新都将消耗800000两银子，你是否需要进行刷新？"),{"刷新变异召唤兽","不用了"})
  elseif 序号==55 then
     self:获取世界BOSS(内容.数字id)
  elseif 序号==56 then
     self:开启世界BOSS挑战(内容.数字id)
  elseif 序号==57 then
     self:领取世界挑战奖励(内容.数字id)
  elseif 序号==60 then
     self:存取银子处理(内容.数字id,内容)
  elseif 序号 == 61 then --上架角色
    self:玩家抽奖处理(内容.数字id,内容)
  elseif 序号 == 62 then --查看角色
      self:嘉年华抽奖处理(内容.数字id,内容)
  elseif 序号==63 then
         多开系统类:切换角色(内容.数字id,内容)
  elseif 序号==65 then --
    取剑会天下数据(内容.数字id)
    local 临时数据 = 剑会天下[内容.数字id]
    发送数据(玩家数据[内容.数字id].连接id,126,临时数据)
  elseif 序号==66 then
    self:剑会匹配处理(内容)
  elseif 序号==67 then
    self:剑会停止匹配处理(内容)
  elseif 序号 == 69 then
     藏宝阁处理类:藏宝阁数据处理(内容)
  elseif 序号 == 80 then
    玩家数据[内容.数字id].道具仓库:数据处理(内容)
  elseif 序号 == 81 then
          self:使用会员卡(内容.数字id)



  elseif 序号==88 then
        玩家数据[内容.数字id].拆分道具=nil
        玩家数据[内容.数字id].道具:道具拆分(内容.数字id,内容)
  elseif 序号 == 89 then
    self:获取坐骑数据处理(内容)
  elseif 序号 == 90 then
    self:坐骑统御处理(内容)
  elseif 序号 == 91 then
    self:坐骑结束统御处理(内容)
  elseif 序号 == 92 then
    self:坐骑驯养处理(内容)
  elseif 序号 == 93 then
    self:坐骑加点处理(内容)
  elseif 序号 == 94 then
    self:坐骑洗点处理(内容)
  elseif 序号 == 95 then
    self:坐骑放生处理(内容)
  elseif 序号 == 96 then
    self:坐骑技能点查询处理(内容)
  elseif 序号 == 97 then
      if not 内容.编号 or tonumber(内容.编号)==nil or tonumber(内容.编号)==0 then 常规提示(内容.数字id,"#Y你没有这样的坐骑") return end
      if 玩家数据[内容.数字id].角色.数据.坐骑列表[tonumber(内容.编号)]==nil then
        常规提示(内容.数字id,"#Y你没有这样的坐骑")
        return
      elseif 玩家数据[内容.数字id].角色.数据.坐骑列表[tonumber(内容.编号)].忠诚 >= 100 then
        常规提示(内容.数字id,"#Y/当前已达最高无需喂养!")
        return
      end
      local 临时数据={}
      临时数据.模型="天兵"
      临时数据.名称="坐骑喂养"
      临时数据.对话="您确认要喂养该坐骑么,喂养坐骑可以随机提高坐骑饱食度,单次喂养消耗1000银子"
      临时数据.选项={"喂养一次","喂养十次","算了算了"}
      发送数据(玩家数据[内容.数字id].连接id,1501,临时数据)
      玩家数据[内容.数字id].喂养坐骑=tonumber(内容.编号)
   -- self:坐骑喂养处理(内容)




  elseif 序号 == 98 then
    self:坐骑技能升级处理(内容)
  elseif 序号 == 99 then
    炼丹查看[内容.数字id] = nil
  elseif 序号 == 99.1 then
    游戏活动类:炼丹下注(内容)
  elseif 序号 == 100 then
     玩家数据[内容.数字id].角色:门派转换(内容.数字id,内容)
  elseif 序号 == 101 and 玩家数据[内容.数字id] and 共享货币[玩家数据[内容.数字id].账号] then
   发送数据(玩家数据[内容.数字id].连接id,76,{仙玉=共享货币[玩家数据[内容.数字id].账号].仙玉})
  elseif 序号 == 102 then
     local id=内容.数字id
    self:师门选择传送(id,内容.参数,内容)





  elseif 序号 == 103 then
    self:功德录处理(内容.数字id,内容)

  elseif 序号==104 then
         self:增加携带上限对话(内容.数字id)
  elseif 序号 == 105 then
     self:开通功德录(内容.数字id)
  elseif 序号 == 116 then
      if 玩家数据[内容.数字id].角色.数据.坐骑==nil then
         常规提示(内容.数字id,"#Y你没有乘骑坐骑")
       elseif not 玩家数据[内容.数字id].角色.数据.坐骑.祥瑞 then
         常规提示(内容.数字id,"#Y你的这只坐骑无法设置飞行技能")
      else
        local 临时数据={}
        临时数据.模型=玩家数据[内容.数字id].角色.数据.模型
        临时数据.名称=玩家数据[内容.数字id].角色.数据.名称
        临时数据.对话="请选择你设置飞行技能的快捷键"
        临时数据.选项={"F1","F2","F3","F4","F5","F6"}
        发送数据(玩家数据[内容.数字id].连接id,1501,临时数据)
        玩家数据[内容.数字id].设置飞行快捷键 = true
     end

  elseif 序号 == 107.1 then-------摩托修改增加防官传音
    self:传音纸鹤处理(内容)
  elseif 序号==111 then
    self:传音纸鹤购买处理(内容)
  elseif 序号==108 then
    self:封号处理(id,序号,内容)
  elseif 序号==200 then



   -- self:图鉴数据查看(内容)
    elseif 序号==201 then
      if 内容.编号~=nil then
         玩家数据[内容.数字id].角色.数据.靓号编号=tonumber(内容.编号)
         if tonumber(内容.编号)~=0 then
             玩家数据[内容.数字id].角色.数据.靓号=金色id(内容.数字id,tonumber(内容.编号))
         else
             玩家数据[内容.数字id].角色.数据.靓号="("..内容.数字id..")"
         end
          常规提示(内容.数字id,"#Y靓号显示已改变")
         发送数据(玩家数据[内容.数字id].连接id,145,{编号=玩家数据[内容.数字id].角色.数据.靓号编号})
      end











   -- self:图鉴提交对话(内容)
  elseif 序号 == 202 then
      if 共享仓库[玩家数据[内容.数字id].账号] then
          共享仓库[玩家数据[内容.数字id].账号]:数据处理(内容)
      end
   -- self:图鉴激活处理1(内容)
  elseif 序号 == 203 then
    --self:图鉴激活处理(内容)
  elseif 序号 == 113 then
  elseif 序号 == 114 then
  elseif 序号 == 118 then
    self:给予开关处理(内容)
  elseif 序号 == 901 then
    -- local id = 内容.数字id
    -- self:刷新交易数据(id,内容)
  elseif 序号 == 902 then
    -- local id = 内容.数字id
    -- self:购买交易中心商品(id,内容)
  elseif 序号 == 903 then
    -- local id = 内容.数字id
    -- self:出售交易中心商品(id,内容)
  end
end



function 系统处理类:使用会员卡(id)
        if not 玩家数据[id] then return end
        local 道具id = 0
        local 道具格子 = 0
        for k,v in pairs(玩家数据[id].角色.数据.道具) do
            if 玩家数据[id].道具.数据[v] and  string.find(玩家数据[id].道具.数据[v].名称, "会员卡") then
               道具id = v
               道具格子 = k
               break
            end
        end
        if  道具id ~=0 and 玩家数据[id].道具.数据[道具id] and  string.find(玩家数据[id].道具.数据[道具id].名称, "会员卡") then
            if 玩家数据[id].道具.数据[道具id].限时 and os.time()>= 玩家数据[id].道具.数据[道具id].限时 then
                   if 玩家数据[id].角色.数据.月卡==nil then
                      玩家数据[id].角色.数据.月卡={购买时间=0,到期时间=0,当前领取=0,开通=false}
                   end
                   玩家数据[id].角色.数据.月卡.开通=false
                   玩家数据[id].道具.数据[道具id] = nil
                   常规提示(id,"道具体验时间已到期,系统自动回收。")
                   道具刷新(id)
              else
                     if not 玩家数据[id].角色.数据.月卡.开通 then
                          玩家数据[id].角色.数据.月卡.到期时间 = os.time()+86400
                          玩家数据[id].角色.数据.月卡.购买时间=os.time()
                          玩家数据[id].角色.数据.月卡.开通=true
                     end

              end
        end
        local  对话内容 = "尊贵的会员玩家，你可以每天在我这边领取一次福利，达到相应等级也可以获取相应福利!#Y每日领取福利:(#R"..自定义数据.月卡数据.显示物品.."#Y/)。自动抓鬼剩余次数: #G[#P"..玩家数据[id].角色.数据.自动抓鬼.."#G]"
        local  对话选项 =  {"领取每日福利","领取等级福利","打开随身仓库","打开共享仓库","场景传送","超级传送",
                                        "自动抓鬼","自动鬼王","靓号设置","门派转换","转换装备","转换武器","八卦炼丹",
                                        "送我回家","送我回帮","会员地图","一键附魔","一键回收","我再想想"
                                      }

        local 对话模型=玩家数据[id].角色.数据.模型
        local 对话名称=玩家数据[id].角色.数据.名称
        发送数据(玩家数据[id].连接id,1501,{模型=对话模型,名称=对话名称,选项=对话选项,对话=对话内容})


end


function 系统处理类:增加携带上限对话(id)
        if 玩家数据[id].角色.数据.携带宠物<10 then
           if 自定义数据.增加宠物上限==nil then
              自定义数据.增加宠物上限={货币类型="仙玉",数量=2000}
           end
           local 临时模型 = 玩家数据[id].角色.数据.模型
           local 临时名称 = 玩家数据[id].角色.数据.名称
           local 数额=自定义数据.增加宠物上限.数量*(玩家数据[id].角色.数据.携带宠物-2)
           local 临时对话 = "本次召唤兽增加上限需#R/"..数额.."#W/"..自定义数据.增加宠物上限.货币类型
           发送数据(玩家数据[id].连接id,1501,{名称=临时名称,模型=临时模型,对话=临时对话,选项={"确定增加上限","我在考虑考虑"}})
        end
end

function 系统处理类:开通功德录(id)


    if 玩家数据[id].角色.数据.功德录.激活 ==false then
    常规提示(id,"#Y你未开通功德录！")
    return
    end
    发送数据(玩家数据[id].连接id,144,玩家数据[id].角色.数据.功德录)

end


function 系统处理类:功德录处理(id,内容)

  local 锁定个数= 内容.锁定数据[1]+内容.锁定数据[2]+内容.锁定数据[3]+内容.锁定数据[4]+内容.锁定数据[5]+内容.锁定数据[6]
  if 锁定个数==#玩家数据[id].角色.数据.功德录.九珠副 then
    常规提示(id,"#Y/您的功德录已经全部锁定，无法进行洗练")
    return
  end
  local  消耗数量 = 3
  if 锁定个数 == 1 then
         消耗数量 = 6
  elseif 锁定个数 == 2 then
         消耗数量 = 12
  elseif 锁定个数 == 3 then
         消耗数量 = 24
  elseif 锁定个数 == 4 then
         消耗数量 = 48
  elseif 锁定个数 == 5 then
         消耗数量 = 96
  elseif 锁定个数 == 6 then
         消耗数量 = 192
  end



if 玩家数据[id].道具:消耗背包道具(id,"功德残卷",消耗数量) then
  local 基础={
          气血={a=98,b=600},
          伤害={a=14,b=180},
          防御={a=14,b=180},
          速度={a=20,b=60},
          穿刺等级={a=22,b=32},
          治疗能力={a=20,b=60},
          固定伤害={a=14,b=180},
          法术伤害={a=14,b=180},
          法术防御={a=14,b=180},
          气血回复效果={a=20,b=60},
          封印命中等级={a=20,b=60},
          抵抗封印等级={a=22,b=32},
          法术暴击等级={a=20,b=60},
          物理暴击等级={a=20,b=60},
          抗法术暴击等级={a=22,b=32},
          抗物理暴击等级={a=22,b=32},

        }
        for n=1,#玩家数据[id].角色.数据.功德录.九珠副 do
          if 内容.锁定数据[n]==0 then
              local  随机类型={"气血","伤害","防御","速度","穿刺等级","治疗能力","固定伤害","法术伤害","法术防御","气血回复效果","封印命中等级","抵抗封印等级","法术暴击等级","物理暴击等级","抗法术暴击等级","抗物理暴击等级"}
              local 主属性 = 随机类型[取随机数(1,#随机类型)]
              local 数值 = 取随机数(基础[主属性].a,基础[主属性].b)*1
              玩家数据[id].角色.数据.功德录.九珠副[n].类型 = 主属性
              玩家数据[id].角色.数据.功德录.九珠副[n].数值 = 数值

          end
        end
        玩家数据[id].角色:刷新信息("6")
        发送数据(玩家数据[id].连接id,144,玩家数据[id].角色.数据.功德录)
        道具刷新(id)
  else
    常规提示(id,"#Y/您的功德残卷数量不足，无法进行洗练")
    return
  end
end


function 系统处理类:创建角色处理(id,内容)
      if not 内容.账号 or 内容.账号 =="" then return end
      if  内容.名称 == ""  or 内容.名称 == nil or string.find(内容.名称, "#") ~= nil or string.find(内容.名称,"/")~= nil
      or string.find(内容.名称, "@") ~= nil or string.find(内容.名称,"*")~= nil
      or string.find(内容.名称, " ") ~= nil or string.find(内容.名称,"~")~= nil
      or string.find(内容.名称, "GM") ~= nil or string.find(内容.名称,"gm")~= nil
      or string.find(内容.名称, "  ") ~= nil or string.find(内容.名称,"充值")~= nil
      or string.find(内容.名称, "游戏管理员") ~= nil or string.find(内容.名称,"·")~= nil
      or string.find(内容.名称,"小风")~= nil  or string.find(内容.名称,"群")~= nil
       or string.find(内容.名称,"裙")~= nil or string.find(内容.名称,"q")~= nil
       or string.find(内容.名称,"Q")~= nil or 判断特殊字符(内容.名称)
     then
        发送数据(id,7,"名称#Y"..内容.名称.."#/存在敏感词,请少侠换一个吧")
        return
      end
      if 敏感字判断(内容.名称,true) then
          发送数据(id,7,"名称#Y"..内容.名称.."#/存在敏感词,请少侠换一个吧")
          return
      end
    内容.名称=tostring(内容.名称)

    for n=1,#名称数据 do
      if 名称数据[n].名称==内容.名称 then
        发送数据(id,7,"#Y/这个名称已经被他人占用了，请重新再想个吧")

        return 0
      end
    end
      if string.len(内容.名称)>=16 then
        发送数据(id,7,"#Y/这个名称太长了请换个名字")
          return 0
      end
      if f函数.文件是否存在([[data/]]..内容.账号)==false then  --未创建存档
        --创建新的账号数据
        if lfs.mkdir([[data/]]..内容.账号)==false then
          发送数据(id,7,"#Y/建立存档失败，错误代号1001")
           发送数据(id,2)
          return 0
        end
        --self.写入数据=[[]]
        写出文件([[data/]]..内容.账号..[[/账号信息.txt]],"")
        临时角色=角色处理类.创建()
        临时角色:创建角色(id,内容.账号,内容.模型,内容.名称,内容.ip,内容.染色ID)
         写出文件([[tysj/名称数据.txt]],table.tostring(名称数据))
        临时角色=nil
        self:取角色选择信息(id,内容.账号)
      else
        self.临时文件=读入文件([[data/]]..内容.账号..[[/信息.txt]])
        self.写入信息=table.loadstring(self.临时文件)
        if #self.写入信息>=5 then
          发送数据(id,7,"#Y/您无法再创建更多的角色了")
          发送数据(id,2)
          return 0
        end
        临时角色=角色处理类.创建()
        临时角色:创建角色(id,内容.账号,内容.模型,内容.名称,内容.ip,内容.染色ID)
        写出文件([[tysj/名称数据.txt]],table.tostring(名称数据))
        临时角色=nil
        self:取角色选择信息(id,内容.账号)
        -- self.临时数据=读入文件()
        -- 服务端参数.id=服务端参数.id+1
      end
end


function 系统处理类:抽奖中奖()
       local 中奖编号 = 0
        local 获得物品={}
        local 可以获得={}
        local 计算概率 ={}
        for i=1,#自定义数据.抽奖配置 do
          计算概率[i]={概率=自定义数据.抽奖配置[i].概率}
        end
        计算概率=删除重复(计算概率)
        table.sort(计算概率,function(a,b) return a.概率>b.概率 end )
        local 获得概率 = 取随机数(1,计算概率[1].概率)
        for i=1,#自定义数据.抽奖配置 do
          if 自定义数据.抽奖配置[i].概率>0 and 获得概率<=自定义数据.抽奖配置[i].概率 then
             获得物品[#获得物品+1]={物品=自定义数据.抽奖配置[i],编号=i}
          end
          if 自定义数据.抽奖配置[i].概率>0 then
             可以获得[#可以获得+1] ={物品=自定义数据.抽奖配置[i],编号=i}
          end
        end
          获得物品=删除重复(获得物品)
          可以获得=删除重复(可以获得)
          if 获得物品~=nil then
              local 取编号=取随机数(1,#获得物品)
              if 获得物品[取编号]~=nil and 获得物品[取编号].物品~=nil and 获得物品[取编号].编号~=nil then
                 中奖编号 = 获得物品[取编号].编号
              end
          end
          if  中奖编号==0 then
               local 可以编号=可以获得[取随机数(1,#可以获得)].编号
               return 可以编号
          else
              return 中奖编号
          end

end

function 系统处理类:嘉年华中奖()
       local 中奖编号 = 0
        local 获得物品={}
        local 可以获得={}
        local 计算概率 ={}
        for i=1,#自定义数据.嘉年华配置 do
          计算概率[i]={概率=自定义数据.嘉年华配置[i].概率}
        end
        计算概率=删除重复(计算概率)
        table.sort(计算概率,function(a,b) return a.概率>b.概率 end )
        local 获得概率 = 取随机数(1,计算概率[1].概率)
        for i=1,#自定义数据.嘉年华配置 do
          if 自定义数据.嘉年华配置[i].概率>0 and 获得概率<=自定义数据.嘉年华配置[i].概率 then
             获得物品[#获得物品+1]={物品=自定义数据.嘉年华配置[i],编号=i}
          end
          if 自定义数据.嘉年华配置[i].概率>0 then
             可以获得[#可以获得+1] ={物品=自定义数据.嘉年华配置[i],编号=i}
          end
        end
          获得物品=删除重复(获得物品)
          可以获得=删除重复(可以获得)
          if 获得物品~=nil then
              local 取编号=取随机数(1,#获得物品)
              if 获得物品[取编号]~=nil and 获得物品[取编号].物品~=nil and 获得物品[取编号].编号~=nil then
                 中奖编号 = 获得物品[取编号].编号
              end
          end
          if  中奖编号==0 then
               local 可以编号=可以获得[取随机数(1,#可以获得)].编号
               return 可以编号
          else
              return 中奖编号
          end

end


function 系统处理类:师门选择传送(id,参数)
          if not 玩家数据[id] then return end
          if 参数 == 3 then
              地图处理类:跳转地图(id,1135,123,35)--方寸山
          elseif 参数 == 12 then
                  地图处理类:跳转地图(id,1513,185,23)--盘丝洞
          elseif 参数 == 13 then
                  地图处理类:跳转地图(id,1111,152,108)--天宫
          elseif 参数 == 5 then
                  地图处理类:跳转地图(id,1250,132,43)--天机城
          elseif 参数 == 6 then
                  地图处理类:跳转地图(id,1142,19,23)--女儿村
          elseif 参数 == 9 then
                  地图处理类:跳转地图(id,1122,32,56)--阴曹地府
          elseif 参数 == 18 then
                  地图处理类:跳转地图(id,1140,18,17)--普陀山
          elseif 参数 == 11 then
                  地图处理类:跳转地图(id,1249,78,26)--女魃墓
          elseif 参数 == 4 then
                  地图处理类:跳转地图(id,1138,50,97)--神木林
          elseif 参数 == 10 then
                  地图处理类:跳转地图(id,1139,58,121)--无底洞
          elseif 参数 == 16 then
                  地图处理类:跳转地图(id,1150,63,36)--凌波城
          elseif 参数 == 17 then
                  地图处理类:跳转地图(id,1251,83,21)--花果山
          elseif 参数 == 2 then
                  地图处理类:跳转地图(id,1002,87,44)--化生寺
          elseif 参数 == 8 then
                  地图处理类:跳转地图(id,1512,85,18)--魔王寨
          elseif 参数 == 15 then
                  地图处理类:跳转地图(id,1146,53,39)--五庄观
          elseif 参数 == 1 then
                  地图处理类:跳转地图(id,1198,80,60)--大唐官府
          elseif 参数 == 7 then
                  地图处理类:跳转地图(id,1131,107,77)--狮驼岭
          elseif 参数 == 14 then
                  地图处理类:跳转地图(id,1116,109,56)--龙宫
          end
          发送数据(玩家数据[id].连接id,155)
end



function 系统处理类:玩家抽奖处理(id,内容)

 if 内容.文本==nil or 内容.文本=="" then return end
  if 内容.文本=="打开" then
     发送数据(玩家数据[id].连接id,133,{道具=自定义数据.抽奖配置,次数= 玩家数据[id].角色.数据.抽奖})
  elseif 内容.文本=="抽奖一次" then
    if 玩家数据[id].角色.数据.抽奖<1 then
        玩家数据[id].抽中编号=nil
        常规提示(id,"#Y/你的抽奖次数好像不够了")
    elseif 玩家数据[id].角色:取道具格子2() <1 then
        玩家数据[id].抽中编号=nil
        常规提示(id,"#Y/你的背包不够了")
      else
         local 抽中编号 = self:抽奖中奖()
          玩家数据[id].抽中编号=抽中编号
        if 玩家数据[id].角色.数据.抽奖>=1 and 自定义数据.抽奖配置~=nil and 自定义数据.抽奖配置[抽中编号]~=nil then
           玩家数据[id].道具:自定义给予道具(id,自定义数据.抽奖配置[抽中编号].名称,自定义数据.抽奖配置[抽中编号].数量,自定义数据.抽奖配置[抽中编号].参数)
           玩家数据[id].角色.数据.抽奖 = 玩家数据[id].角色.数据.抽奖 - 1
        end
        玩家数据[id].抽中编号=nil
        发送数据(玩家数据[id].连接id,133,{道具=自定义数据.抽奖配置,次数=玩家数据[id].角色.数据.抽奖})
     end
  elseif 内容.文本=="抽奖十次" then
         if 玩家数据[id].角色.数据.抽奖<10 then
          玩家数据[id].抽中编号=nil
          常规提示(id,"#Y/你的抽奖次数好像不够了")
        elseif 玩家数据[id].角色:取道具格子2() <10 then
          玩家数据[id].抽中编号=nil
          常规提示(id,"#Y/你的背包不够了")
        else
          for i=1,10 do
             local 抽中编号 = self:抽奖中奖()
              玩家数据[id].抽中编号=抽中编号
              if 玩家数据[id].角色.数据.抽奖>=1 and 自定义数据.抽奖配置~=nil and 自定义数据.抽奖配置[抽中编号]~=nil then
                 玩家数据[id].道具:自定义给予道具(id,自定义数据.抽奖配置[抽中编号].名称,自定义数据.抽奖配置[抽中编号].数量,自定义数据.抽奖配置[抽中编号].参数)
                 玩家数据[id].角色.数据.抽奖 = 玩家数据[id].角色.数据.抽奖 - 1
              end
          end
          玩家数据[id].抽中编号=nil
         发送数据(玩家数据[id].连接id,133,{道具=自定义数据.抽奖配置,次数=玩家数据[id].角色.数据.抽奖})
    end




  end
end



function 系统处理类:嘉年华抽奖处理(id,内容)

 if 内容.文本==nil or 内容.文本=="" then return end
  if 内容.文本=="抽奖一次" then
    if 玩家数据[id].角色.数据.嘉年华<10 then
        玩家数据[id].抽中嘉年华=nil
        常规提示(id,"#Y/你的嘉年华积分好像不够了")
    elseif 玩家数据[id].角色:取道具格子2() <1 then
        玩家数据[id].抽中嘉年华=nil
        常规提示(id,"#Y/你的背包不够了")
      else
          local 抽中编号 = self:嘉年华中奖()
          玩家数据[id].抽中嘉年华=抽中编号
          if 玩家数据[id].角色.数据.嘉年华>=10 and 自定义数据.嘉年华配置~=nil and 自定义数据.嘉年华配置[抽中编号]~=nil then
             玩家数据[id].道具:自定义给予道具(id,自定义数据.嘉年华配置[抽中编号].名称,自定义数据.嘉年华配置[抽中编号].数量,自定义数据.嘉年华配置[抽中编号].参数)
             玩家数据[id].角色.数据.嘉年华 = 玩家数据[id].角色.数据.嘉年华 - 10
          end
          玩家数据[id].抽中嘉年华=nil
          发送数据(玩家数据[id].连接id,147,{道具=自定义数据.嘉年华配置,次数=玩家数据[id].角色.数据.嘉年华})
     end
  end
end

function 系统处理类:存取银子处理(id,内容)
    if 内容.文本==nil or 内容.文本=="" or not 内容.数额 then return end
    内容.数额 = tonumber(内容.数额)
    if not 内容.数额 or 内容.数额<1 or 内容.数额~=math.floor(内容.数额) or isNaN(内容.数额) then
        return
    end
    if 内容.文本=="存钱" then
        if 玩家数据[id].角色.数据.银子<内容.数额 then
          常规提示(id,"你身上的银子不足")
          return
        end
        玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 - 内容.数额
        玩家数据[id].角色.数据.存银 = 玩家数据[id].角色.数据.存银 + 内容.数额
        常规提示(id,"#Y/你存进了#R/"..内容.数额.."#Y/银子")
    else
        if 玩家数据[id].角色.数据.存银<tonumber(内容.数额) then
            常规提示(id,"你身上的存银不足")
            return
        end
        玩家数据[id].角色.数据.存银 = 玩家数据[id].角色.数据.存银 - 内容.数额
        玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 内容.数额
        常规提示(id,"#Y/你取出了#R/"..内容.数额.."#Y/银子")
    end
    道具刷新(id)
end

function 系统处理类:梦幻指引处理(内容)
  local id = 内容.数字id
 if 活跃数据[id]==nil then
    活跃数据[id]={活跃度=0}
 end
 if f函数.读配置(程序目录..[[活动攻略\攻略数据.txt]]) ==false then
   常规提示(id,"#Y/数据问题,请联系管理员")
     return
  end
  local 代码函数=loadstring(读入文件([[活动攻略\攻略数据.txt]]))
  代码函数()

  if 内容.获取 =="打开" then
    if 自定义数据.日常任务==nil then
        常规提示(id,"#Y/数据问题,请联系管理员")
     else
        发送数据(玩家数据[id].连接id,90,{标题="日常任务",文本=自定义数据.日常任务,活跃度=活跃数据[id].活跃度})
     end
  elseif 内容.获取 =="获取其他数据" then
      if 自定义数据[内容.文本]==nil then
        常规提示(id,"#Y/数据问题,请联系管理员")
     else
        发送数据(玩家数据[id].连接id,90,{标题=内容.文本,文本=自定义数据[内容.文本],活跃度=活跃数据[id].活跃度})
     end
  elseif 内容.获取 =="查看攻略" then
    if  自定义数据[内容.文本]==nil then
        常规提示(id,"#Y/数据问题,请联系管理员")
    else
          if f函数.读配置(程序目录..[[活动攻略\]]..内容.文本..[[\]]..内容.文件..".txt") ==false then
             常规提示(id,"#Y/数据问题,请联系管理员")
          else
              local 发送信息 =读入文件([[活动攻略\]]..内容.文本..[[\]]..内容.文件..".txt")
               发送数据(玩家数据[id].连接id,90.1,{标题文字=内容.文件,文本=发送信息})
          end
      end
 elseif 内容.获取 =="活跃度领取" then
       if 自定义数据[内容.文本]==nil then
          常规提示(id,"#Y/数据问题,请联系管理员")
       else
          if 内容.编号==nil or tonumber(内容.编号) ==nil then
          else
             local 领取数额 = tonumber(内容.编号)*100
             if 活跃数据[id].活跃度<领取数额 or 活跃数据[id][领取数额] then
                常规提示(id,"#Y/你未达到领取条件无法领取")
              elseif 自定义数据.活跃度物品 ==nil or 自定义数据.活跃度物品[领取数额] ==nil then
                常规提示(id,"#Y/数据问题,请联系管理员")
              else
                   if 自定义数据.活跃度物品 ~=nil and 自定义数据.活跃度物品[领取数额] ~=nil and not 活跃数据[id][领取数额]then
                      if 自定义数据.活跃度物品[领取数额].经验>0 then
                        玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 自定义数据.活跃度物品[领取数额].经验
                        常规提示(id,"#Y/你获得了#R/"..自定义数据.活跃度物品[领取数额].经验.."#Y/点经验")
                      end
                      if 自定义数据.活跃度物品[领取数额].储备>0 then
                        玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 自定义数据.活跃度物品[领取数额].储备
                        常规提示(id,"#Y/你获得了#R/"..自定义数据.活跃度物品[领取数额].储备.."#Y/两储备")
                      end
                      if 自定义数据.活跃度物品[领取数额].银子>0 then
                        玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 自定义数据.活跃度物品[领取数额].银子
                        常规提示(id,"#Y/你获得了#R/"..自定义数据.活跃度物品[领取数额].银子.."#Y/两银子")
                      end
                      if 自定义数据.活跃度物品[领取数额].仙玉>0 and 共享货币[玩家数据[id].账号] then
                           共享货币[玩家数据[id].账号]:添加仙玉(自定义数据.活跃度物品[领取数额].仙玉,id,"活跃度领取")
                      end
                      if 自定义数据.活跃度物品[领取数额].点卡>0 and 共享货币[玩家数据[id].账号] then
                          共享货币[玩家数据[id].账号]:添加点卡(自定义数据.活跃度物品[领取数额].点卡,id,"活跃度领取")
                      end
                      if 自定义数据.活跃度物品[领取数额].物品数量>0 then
                        for i=1,自定义数据.活跃度物品[领取数额].物品数量 do
                            local 物品名称 = 自定义数据.活跃度物品[领取数额].物品[i].名称
                            local 数量 = tonumber(自定义数据.活跃度物品[领取数额].物品[i].数量)
                             仙玉商城类:仙玉商城商品处理(id,物品名称,数量)
                             常规提示(id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
                          end
                      end
                       活跃数据[id][领取数额] = 1

                    else
                      常规提示(id,"#Y/你未达到领取条件无法领取")
                   end
                  发送数据(玩家数据[id].连接id,90,{标题=内容.文本,文本=自定义数据[内容.文本],活跃度=活跃数据[id].活跃度})
              end
          end
       end


  end


end







function 系统处理类:获取签到数据(id)
  self.当月天数 = os.date("%d",os.time({year=os.date("%Y"),month=os.date("%m")+1,day=0})) --当月天数
  self.月份 =tonumber(os.date("%m", os.time()))
  self.几号 =tonumber(os.date("%d", os.time()))
  if 签到数据[id] ~= nil then
    if 签到数据[id][self.月份] == nil then
      签到数据[id]={[self.月份]={}}
      签到数据[id][self.月份]={
      当月天数=self.当月天数,
      几号=self.几号,
      月份=self.月份,
      累计签到=0
      }
      for i=1,self.当月天数 do
        if 签到数据[id][self.月份][i] == nil then
          签到数据[id][self.月份][i] =false
        end
      end
    end
    if 签到数据[id][self.月份-1]~=nil then
       签到数据[id][self.月份-1] ={}
    end
  else
    签到数据[id]={[self.月份]={}}
    签到数据[id][self.月份]={
      当月天数=self.当月天数,
      几号=self.几号,
      月份=self.月份,
      累计签到=0
    }
    for i=1,self.当月天数 do
      if 签到数据[id][self.月份][i] == nil then
        签到数据[id][self.月份][i] =false
      end
    end
  end
  if 签到数据[id][self.月份].几号 ~= self.几号 then
    签到数据[id][self.月份].几号 = self.几号
  end
  发送数据(玩家数据[id].连接id,106,签到数据[id][self.月份])
end









function 系统处理类:签到处理(id,数据)
  local 累计次数 = 签到数据[id][数据.月份].累计签到
  if not 活跃数据[id] or not 活跃数据[id].活跃度 or 活跃数据[id].活跃度<100 then
    常规提示(id,"#Y签到需每日的活跃度达到100以上，少侠的活跃度不够，无法签到!")
    return
  end
  if 玩家数据[id].角色.数据.等级<30 then
    常规提示(id,"#Y签到需等级达到30级以上，少侠的等级不够，无法签到!")
    return
  end
  if 签到数据[id][数据.月份][数据.几号] == true then
    常规提示(id,"#Y少侠今日已经签到过了!")
    return
  else
    签到数据[id][数据.月份][数据.几号] = true
    签到数据[id][数据.月份].累计签到 = 累计次数 + 1
    累计次数 = 累计次数 + 1
    self:签到奖励处理(id,累计次数)
    发送数据(玩家数据[id].连接id,106,签到数据[id][数据.月份])
  end
end



function 系统处理类:签到奖励处理(id,累计)
  self.经验奖励=math.floor(玩家数据[id].角色.数据.等级*500*累计)
  玩家数据[id].角色:添加经验(self.经验奖励,"签到奖励",1)
  玩家数据[id].角色:添加银子(self.经验奖励,"签到奖励",1)
  常规提示(id,"#Y恭喜你！签到成功获得大量经验银子奖励！")

  if 累计 == 7 or 累计 == 14 or 累计 == 21 or 累计 == 28 then
     if 自定义数据.签到物品~=nil and 自定义数据.签到物品[累计] ~=nil then
        if 自定义数据.签到物品[累计].经验>0 then
            玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 自定义数据.签到物品[累计].经验
            常规提示(id,"#Y/你获得了#R/"..自定义数据.签到物品[累计].经验.."#Y/点经验")
        end
        if 自定义数据.签到物品[累计].储备>0 then
           玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 自定义数据.签到物品[累计].储备
           常规提示(id,"#Y/你获得了#R/"..自定义数据.签到物品[累计].储备.."#Y/两储备")
        end
        if 自定义数据.签到物品[累计].银子>0 then
           玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 自定义数据.签到物品[累计].银子
           常规提示(id,"#Y/你获得了#R/"..自定义数据.签到物品[累计].银子.."#Y/两银子")
        end
        if 自定义数据.签到物品[累计].仙玉>0 and 共享货币[玩家数据[id].账号] then
            共享货币[玩家数据[id].账号]:添加仙玉(自定义数据.签到物品[累计].仙玉,id,"累计签到")
        end
        if 自定义数据.签到物品[累计].点卡>0 and 共享货币[玩家数据[id].账号] then
            共享货币[玩家数据[id].账号]:添加点卡(自定义数据.签到物品[累计].点卡,id,"累计签到")
        end
        if 自定义数据.签到物品[累计].物品数量>0 then
           for i=1,自定义数据.签到物品[累计].物品数量 do
               local 物品名称 = 自定义数据.签到物品[累计].物品[i].名称
               local 数量 = tonumber(自定义数据.签到物品[累计].物品[i].数量)
               仙玉商城类:仙玉商城商品处理(id,物品名称,数量)
               常规提示(id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
           end
        end
      end
  end

end


function 系统处理类:宠物领养处理(id,内容)
 if 玩家数据[内容.数字id].角色.数据.宠物.领养次数==0 then
      玩家数据[内容.数字id].角色.数据.宠物={模型=内容.cw,名称=内容.cw,等级=1,最大等级=120,耐力=5,最大耐力=5,经验=1,最大经验=10,领养次数=1}
      发送数据(id,7,"#Y/领养宠物成功")
      发送数据(id,8,玩家数据[内容.数字id].角色.数据.宠物)
    else
      发送数据(id,7,"#Y/您当前无法再领取宠物了")
    end
end


function 系统处理类:成就数据处理(内容)
  local id=内容.数字id
    if 成就数据[id]==nil then
      成就数据[id]={成就点=0}
    end
    发送数据(玩家数据[id].连接id,22.1,{成就数据=成就数据[id]})
end







function 系统处理类:飞行处理(id)
        if 玩家数据[id].角色.数据.地图数据.编号 == 6003 or 玩家数据[id].角色.数据.地图数据.编号 == 6004 then
           常规提示(id,"#Y/该地图禁止飞行")
            return
        end
        if 玩家数据[id].角色.数据.飞行 then
                if 玩家数据[id].队伍== 0 then
                     玩家数据[id].角色.数据.飞行 =false
                     地图处理类:更新飞行(id,false)
                     发送数据(玩家数据[id].连接id,72)
                else
                    local 队伍id = 玩家数据[id].队伍
                    for i=1,#队伍数据[队伍id].成员数据 do
                        local 临时id = 队伍数据[队伍id].成员数据[i]
                         玩家数据[临时id].角色.数据.飞行 =false
                         地图处理类:更新飞行(临时id,false)
                         发送数据(玩家数据[临时id].连接id,72)
                    end
                end
        else
                if 玩家数据[id].队伍 == 0 then
                    if 玩家数据[id].角色.数据.坐骑==nil or not 玩家数据[id].角色.数据.坐骑.祥瑞 then
                       常规提示(id,"#Y/你没有乘骑祥瑞无法飞行")
                       return
                    end
                     玩家数据[id].角色.数据.飞行 =true
                     地图处理类:更新飞行(id,true)
                     发送数据(玩家数据[id].连接id,71)
                else
                    if 玩家数据[id].队长==false  then
                        常规提示(id,"#Y/你不是队长无法飞行")
                        return
                    end
                    local 队伍id = 玩家数据[id].队伍
                    for i=1,#队伍数据[队伍id].成员数据 do
                      local 临时id = 队伍数据[队伍id].成员数据[i]
                      if 玩家数据[临时id].角色.数据.坐骑==nil or not 玩家数据[临时id].角色.数据.坐骑.祥瑞 then
                          常规提示(id,"#Y/队伍中#R/"..玩家数据[临时id].角色.数据.名称.."#Y/没有乘骑祥瑞无法飞行")
                          return
                      end
                    end
                    for i=1,#队伍数据[队伍id].成员数据 do
                        local 临时id = 队伍数据[队伍id].成员数据[i]
                        玩家数据[临时id].角色.数据.飞行 =true
                        地图处理类:更新飞行(临时id,true)
                        发送数据(玩家数据[临时id].连接id,71)
                    end

                end

        end
end



function 系统处理类:角色乘骑处理(内容,多角色,附角色)
    local 序列=内容.序列
    local id=内容.数字id
    if 附角色~=nil then
       id = 附角色
    end
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑",多角色)
      return
    else
      if 玩家数据[id].角色.数据.飞行 then
         if 玩家数据[id].队伍== 0 then
             玩家数据[id].角色.数据.飞行 =false
             地图处理类:更新飞行(id,false)
             发送数据(玩家数据[id].连接id,72)
             常规提示(id,"#Y/更换坐骑自动降落",多角色)
          else
            local 队伍id = 玩家数据[id].队伍
            for i=1,#队伍数据[队伍id].成员数据 do
              local 临时id = 队伍数据[队伍id].成员数据[i]
               玩家数据[临时id].角色.数据.飞行 =false
               地图处理类:更新飞行(临时id,false)
               发送数据(玩家数据[临时id].连接id,72)
               常规提示(临时id,"#Y/玩家#R/"..玩家数据[id].角色.数据.名称.."#Y/更换坐骑自动降落")
            end
          end
      end
      玩家数据[id].角色.数据.坐骑=DeepCopy(玩家数据[id].角色.数据.坐骑列表[序列])
      常规提示(id,"#Y骑乘坐骑成功！",多角色)
      玩家数据[id].角色:刷新信息(是否,体质,魔力)
      if 多角色~=nil then
           发送数据(玩家数据[多角色].连接id,6016,{角色=id,坐骑=玩家数据[id].角色.数据.坐骑})
      else
          发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.数据.坐骑)
      end
      地图处理类:更新坐骑(id,玩家数据[id].角色.数据.坐骑)
    end
end


function 系统处理类:角色下骑处理(内容,多角色,附角色)
  local id=内容.数字id
  if 附角色~=nil then
       id = 附角色
    end
    if 玩家数据[id].角色.数据.飞行 then
         if 玩家数据[id].队伍== 0 then
               玩家数据[id].角色.数据.飞行 =false
               地图处理类:更新飞行(id,false)
               发送数据(玩家数据[id].连接id,72)
               常规提示(id,"#Y/下掉坐骑自动降落",多角色)
          else
              local 队伍id = 玩家数据[id].队伍
              for i=1,#队伍数据[队伍id].成员数据 do
                local 临时id = 队伍数据[队伍id].成员数据[i]
                 玩家数据[临时id].角色.数据.飞行 =false
                 地图处理类:更新飞行(临时id,false)
                 发送数据(玩家数据[临时id].连接id,72)
                 常规提示(临时id,"#Y/玩家#R/"..玩家数据[id].角色.数据.名称.."#Y/下掉坐骑自动降落")
              end
          end
      end
    玩家数据[id].角色.数据.坐骑=nil
    常规提示(id,"#Y下乘坐骑成功！",多角色)
    玩家数据[id].角色:刷新信息(是否,体质,魔力)
    if 多角色~=nil then
       发送数据(玩家数据[多角色].连接id,6016,{角色=id,坐骑=玩家数据[id].角色.数据.坐骑})
    else
      发送数据(玩家数据[id].连接id,60,玩家数据[id].角色.数据.坐骑)
    end
    地图处理类:更新坐骑(id,玩家数据[id].角色.数据.坐骑)
end



function 系统处理类:玩家pk处理(内容)
    if 玩家数据[内容.数字id].队伍 ~= 0 and not 玩家数据[内容.数字id].队长 then
          常规提示(内容.数字id,"#Y/你不是队长无法观战哟!......")
          return
    end
    local 挑战id = 内容.序列
    if not 玩家数据[内容.序列] then return end
    if 玩家数据[内容.序列].队伍 and 玩家数据[内容.序列].队伍 ~= 0 and not 玩家数据[内容.序列].队长 then
        挑战id = 队伍数据[玩家数据[内容.序列].队伍].成员数据[1]
    end
    local id组 = 取id组(内容.数字id)
    for i=1,#id组 do
        if 玩家数据[id组[i]].战斗 ~= nil and 玩家数据[id组[i]].战斗 ~= 0 and 玩家数据[id组[i]].观战 == nil then
            return
        elseif 玩家数据[id组[i]].观战 ~= nil then
            if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                  战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
            else
                  玩家数据[id组[i]].战斗=0
                  玩家数据[id组[i]].观战=nil
                  发送数据(玩家数据[id组[i]].连接id,5505)
            end
        end
    end
   if 玩家数据[挑战id] ~= nil and 玩家数据[挑战id].战斗 ~= nil and 玩家数据[挑战id].战斗 ~= 0 and 玩家数据[挑战id].观战 == nil  then
      if 玩家数据[内容.数字id].队伍 ~= 0 and not 玩家数据[内容.数字id].队长 then
          常规提示(内容.数字id,"#Y/你不是队长无法观战哟!......")
          return
      else
            local id组 = 取id组(内容.数字id)
            for i=1,#id组 do
                战斗准备类.战斗盒子[玩家数据[挑战id].战斗]:设置观战玩家(挑战id,id组[i])
                玩家数据[id组[i]].战斗 = 玩家数据[挑战id].战斗
                玩家数据[id组[i]].观战 = 1
            end
      end
    elseif 玩家数据[内容.数字id].角色.数据.强P开关 ~= nil and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6003 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6004  then
            玩家数据[内容.数字id].最后对话={}
            添加最后对话(内容.数字id,"你确定要对#R"..玩家数据[挑战id].角色.数据.名称.."#Y强行进行PK么?",{"确定强行PK","我再考虑考虑！"})
            玩家数据[内容.数字id].强P对象 = 挑战id
            return
    elseif 玩家数据[内容.数字id].角色.数据.PK开关 ~= nil and 玩家数据[挑战id].角色.数据.PK开关 ~= nil and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6003 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6004  then
            local id组 = 取id组(挑战id)
            for i=1,#id组 do
                if 玩家数据[id组[i]].观战 ~= nil then
                   if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                        战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
                   else
                          玩家数据[id组[i]].战斗=0
                          玩家数据[id组[i]].观战=nil
                          发送数据(玩家数据[id组[i]].连接id,5505)
                    end
                end
            end
            战斗准备类:创建玩家战斗(内容.数字id, 200007, 挑战id, 1501)
    elseif 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 1001 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6003 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6004 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6005 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6006 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6007 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6008 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6009 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6010  and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6012 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6013 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6014 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6015 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6016 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6017 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6018 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6019 then
      常规提示(内容.数字id,"#Y只可以在长安擂台PK！")
      return 0
    elseif 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6003 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6004 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6005 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6006 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6007 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6008 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6009 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6010  and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6012 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6013 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6014 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6015 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6016 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6017 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6018 and 玩家数据[内容.数字id].角色.数据.地图数据.编号 ~= 6019  and (取两点距离({x=8274,y=3426},{x=玩家数据[内容.数字id].角色.数据.地图数据.x,y=玩家数据[内容.数字id].角色.数据.地图数据.y})>400 or 取两点距离({x=8274,y=3426},{x=玩家数据[挑战id].角色.数据.地图数据.x,y=玩家数据[挑战id].角色.数据.地图数据.y})>400) then
      常规提示(内容.数字id,"#y/只有擂台之上才可以进行切磋")
      return 0
    else
        local id组 = 取id组(挑战id)
        for i=1,#id组 do
            if 玩家数据[id组[i]].战斗 ~= nil and 玩家数据[id组[i]].战斗 ~= 0 and 玩家数据[id组[i]].观战 == nil then
                return
            elseif 玩家数据[id组[i]].观战 ~= nil then
               if 战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]~=nil  then
                  战斗准备类.战斗盒子[玩家数据[id组[i]].战斗]:删除观战玩家(id组[i])
               else
                      玩家数据[id组[i]].战斗=0
                      玩家数据[id组[i]].观战=nil
                      发送数据(玩家数据[id组[i]].连接id,5505)
                end
            end
        end
        if 玩家数据[内容.数字id].角色.数据.地图数据.编号 == 1001 then
              战斗准备类:创建玩家战斗(内容.数字id, 200003, 挑战id, 1501)
        elseif 玩家数据[内容.数字id].角色.数据.地图数据.编号 == 6003 or 玩家数据[内容.数字id].角色.数据.地图数据.编号 == 6004 then
             英雄大会:进入战斗(内容.数字id,挑战id)

        elseif 玩家数据[内容.数字id].角色.数据.地图数据.编号 == 6010 then
             帮战活动类:进入战斗(内容.数字id,挑战id)

        elseif 玩家数据[内容.数字id].角色.数据.地图数据.编号 == 6009 then
            -- if 首席争霸开关 then
            --     战斗准备类:创建玩家战斗(内容.数字id, 200005, 内容.序列, 1501)
            -- else
            --     常规提示(内容.数字id,"当前为首席争霸准备时间，无法进行切磋！")
            --     return 0
            -- end
        end
    end
end

function 系统处理类:超级传送处理(内容)
      if 玩家数据[内容.数字id].最后操作== "会员传送" then
          if 玩家数据[内容.数字id].道具:取飞行限制(内容.数字id) then
             常规提示(内容.数字id,"#Y你当前无法使用飞行道具")
          else
            if 玩家数据[内容.数字id].角色.数据.月卡 == nil  or  玩家数据[内容.数字id].角色.数据.月卡.开通==false then
              if  玩家数据[内容.数字id].角色.数据.银子<20000 then
               常规提示(内容.数字id,"#Y你身上没有那么多的银子")
               return
              end
             玩家数据[内容.数字id].角色:扣除银子(20000,"超级传送",1)
          end
             地图处理类:跳转地图(内容.数字id,内容.地图,内容.飞行坐标.x,内容.飞行坐标.y)
          end
      end
      玩家数据[内容.数字id].最后操作 = nil
end

function 系统处理类:地图npc传送处理(内容)
      if 玩家数据[内容.数字id].最后操作== "会员传送" then
        if 玩家数据[内容.数字id].道具:取飞行限制(内容.数字id) then
             常规提示(内容.数字id,"#Y你当前无法使用飞行道具")
          else
              if 玩家数据[内容.数字id].角色.数据.月卡 == nil  or  玩家数据[内容.数字id].角色.数据.月卡.开通==false then
                  if  玩家数据[内容.数字id].角色.数据.银子<20000 then
                   常规提示(内容.数字id,"#Y你身上没有那么多的银子")
                   return
                  end
                 玩家数据[内容.数字id].角色:扣除银子(20000,"超级传送",1)
              end
              地图处理类:跳转地图(内容.数字id,内容[1],内容[2],内容[3])
          end
      end
      玩家数据[内容.数字id].最后操作 = nil
end




function 系统处理类:自动寻路处理(内容)
      local id = 内容.数字id+0
    if 内容.地图 == nil or 内容.x==nil  or 内容.y ==nil then
      常规提示(内容.数字id,"#Y数据错误,无法自动寻路!!!")
      return
    end
    if  玩家数据[id].角色.数据.地图数据.编号~= 内容.地图+0 then
       if 玩家数据[内容.数字id].角色.数据.月卡 == nil  or  玩家数据[内容.数字id].角色.数据.月卡.开通==false then
        常规提示(内容.数字id,"#Y没有#R会员卡#Y无法使用夸地图寻路!")
        return
      else
        local 临时内容 = {x=内容.x,y=内容.y,数字id = id,距离=0,地图=内容.地图+0}
          地图处理类:移动请求(玩家数据[id].连接id,临时内容,id)
          发送数据(玩家数据[id].连接id,100,临时内容)
          -- 常规提示(内容.数字id,"#Y自动寻路中…………")
      end
    else
      local 临时内容 = {x=内容.x,y=内容.y,数字id = id,距离=0}
      地图处理类:移动请求(玩家数据[id].连接id,临时内容,id)
      发送数据(玩家数据[id].连接id,1001,临时内容)
       常规提示(内容.数字id,"#Y自动寻路中…………")
    end
end

function 系统处理类:召唤兽卸下饰品处理(内容)
  local id=内容.数字id
     local 认证码=内容.序列
     local 编号=玩家数据[id].召唤兽:取编号(认证码)
     if 玩家数据[id].召唤兽.数据[编号]==nil then
       常规提示(id,"你没有这样的召唤兽")
       return
      elseif 玩家数据[id].召唤兽.数据[编号].饰品==nil then
       常规提示(id,"你没有这样的饰品")
       return
      elseif 玩家数据[id].角色:取道具格子2()<=0 then
      常规提示(id,"#Y请先清理下背包")
      return
      else
       玩家数据[id].道具:给予道具(id,玩家数据[id].召唤兽.数据[编号].饰品)
       常规提示(id,"你获得了"..玩家数据[id].召唤兽.数据[编号].饰品)
       玩家数据[id].召唤兽.数据[编号].饰品=nil
      end
end

function 系统处理类:商会购买召唤兽处理1(内容)
   local id=内容.数字id+0
     local 序列=内容.名称
     if 商店bb[id]==nil or 商店bb[id][序列]==nil then
        常规提示(id,"你无法购买这样的召唤兽")
        return
      elseif 玩家数据[id].角色.数据.银子<商店bb[id][序列].价格 then
        常规提示(id,"你没有那么多的银子")
        return
      elseif 玩家数据[id].召唤兽:是否携带上限() then
        常规提示(id,"你当前可携带的召唤兽数量已达上限")
        return
      else
        玩家数据[id].角色:扣除银子(商店bb[id][序列].价格,"商会购买召唤兽",1)
        玩家数据[id].召唤兽:添加召唤兽(商店bb[id][序列].模型,商店bb[id][序列].模型,"野怪")
        table.remove(商店bb[id],序列)
        常规提示(id,"购买召唤兽成功")
        发送数据(玩家数据[id].连接id,131,商店bb[id])
      end
end
function 系统处理类:商会购买召唤兽处理(内容)
  local id=内容.数字id+0
     local 序列=内容.名称
     if 变异商店bb[id]==nil or 变异商店bb[id][序列]==nil then
        常规提示(id,"你无法购买这样的召唤兽")
        return
      elseif 玩家数据[id].角色.数据.银子<变异商店bb[id][序列].价格 then
        常规提示(id,"你没有那么多的银子")
        return
      elseif 玩家数据[id].召唤兽:是否携带上限() then
        常规提示(id,"你当前可携带的召唤兽数量已达上限")
        return
      else
        玩家数据[id].角色:扣除银子(变异商店bb[id][序列].价格,"商会购买召唤兽",1)
        玩家数据[id].召唤兽:添加召唤兽(变异商店bb[id][序列].模型,变异商店bb[id][序列].模型,"变异")
        table.remove(变异商店bb[id],序列)
        常规提示(id,"购买召唤兽成功")
        发送数据(玩家数据[id].连接id,131.1,变异商店bb[id])
      end
end

function 系统处理类:获取坐骑数据处理(内容)
    local 序列=内容.序列
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    elseif 内容.名称 == ""  or 内容.名称 == nil or string.find(内容.名称, "#") ~= nil or string.find(内容.名称,"/")~= nil
      or string.find(内容.名称, "@") ~= nil or string.find(内容.名称,"*")~= nil
      or string.find(内容.名称, " ") ~= nil or string.find(内容.名称,"~")~= nil
      or string.find(内容.名称, "GM") ~= nil or string.find(内容.名称,"gm")~= nil
      or string.find(内容.名称, "  ") ~= nil or string.find(内容.名称,"充值")~= nil
      or string.find(内容.名称, "游戏管理员") ~= nil or string.find(内容.名称,"·")~= nil
      or string.find(内容.名称,"小风")~= nil  or string.find(内容.名称,"群")~= nil
       or string.find(内容.名称,"裙")~= nil or string.find(内容.名称,"q")~= nil
       or string.find(内容.名称,"Q")~= nil or 判断特殊字符(内容.名称) then
        常规提示(id,"名称不能有特殊字符")
      return
    else
      玩家数据[id].角色.数据.坐骑列表[序列].名称 = 内容.名称
    end
end


function 系统处理类:坐骑结束统御处理(内容)
  local 序列=内容.序列
    local id=内容.数字id
    local 召唤兽编号 = 玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽[内容.编号]
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    elseif  玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽[内容.编号] == nil then
      常规提示(id,"#Y统御信息不存在")
    elseif 玩家数据[id].召唤兽.数据[召唤兽编号] == nil then
      table.remove(玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽,内容.编号)
      常规提示(id,"#Y已经结束对该召唤兽的统御")
    else
      玩家数据[id].召唤兽.数据[召唤兽编号].统御 = nil
      玩家数据[id].召唤兽.数据[召唤兽编号].统御属性.力量 = 0
      玩家数据[id].召唤兽.数据[召唤兽编号].统御属性.魔力 = 0
      玩家数据[id].召唤兽.数据[召唤兽编号].统御属性.体质 = 0
      玩家数据[id].召唤兽.数据[召唤兽编号].统御属性.敏捷 = 0
      玩家数据[id].召唤兽.数据[召唤兽编号].统御属性.耐力 = 0
      玩家数据[id].召唤兽:刷新信息(召唤兽编号,"1")
      发送数据(玩家数据[id].连接id,20,玩家数据[id].召唤兽:取存档数据(玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽[内容.编号]))
      table.remove(玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽,内容.编号)
      常规提示(id,"#Y已经结束对该召唤兽的统御")
    end
    发送数据(玩家数据[id].连接id,61.1,{编号=序列,数据=玩家数据[id].角色.数据.坐骑列表[序列]})
end
function 系统处理类:坐骑驯养处理(内容)

    local 序列=内容.编号
    local id=内容.数字id
    local 经验 = 100000*内容.次数
    local 银子 = 100000*内容.次数
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    else
      if 取银子(id)<银子 then
          常规提示(id,"#Y你当前的银子好像不够哟!本次驯养需银子#R"..银子)
          return
      elseif 玩家数据[id].角色.数据.当前经验 < 经验 then
          常规提示(id,"#Y你当前的经验好像不够哟!本次驯养需经验#R"..经验)
          return
      elseif 玩家数据[id].角色.数据.等级 <= 玩家数据[id].角色.数据.坐骑列表[序列].等级 then
          常规提示(id,"#Y/坐骑等级不能超过人物等级")
          return
      end
      玩家数据[id].角色.数据.当前经验=玩家数据[id].角色.数据.当前经验-经验
      玩家数据[id].角色:扣除银子(银子,"坐骑训养",1)
      玩家数据[id].角色:坐骑喂养(序列,经验)
    end
end



function 系统处理类:坐骑加点处理(内容)
   local 序列=内容.编号
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    else
      玩家数据[id].角色:坐骑加点(序列,内容.加点)
    end
end
function 系统处理类:坐骑洗点处理(内容)
  local 序列=内容.编号
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    elseif 取银子(id)<100000 then
      常规提示(id,"#Y/你当前的银子好像不够哟!")
      return
    else
      玩家数据[id].角色:扣除银子(100000,"坐骑洗点",1)
      玩家数据[id].角色:坐骑洗点(序列)
    end
end

function 系统处理类:坐骑放生处理(内容)
   local 序列=内容.编号
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    else
      玩家数据[id].角色:坐骑放生(序列)
    end
end


function 系统处理类:坐骑技能点查询处理(内容)
    local 序列=内容.编号
    local id=内容.数字id
    for i=1,#玩家数据[id].角色.数据.坐骑列表 do
      if 玩家数据[id].角色.数据.坐骑列表[i].技能点 == nil then
        玩家数据[id].角色.数据.坐骑列表[i].技能点 = 0
      end
    end
    发送数据(玩家数据[id].连接id,61.2,玩家数据[id].角色.数据.坐骑列表)
end

function 系统处理类:坐骑喂养处理(id,序列)
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
        常规提示(id,"#Y你没有这样的坐骑")
        return
    elseif 取银子(id)<1000 then
        常规提示(id,"#Y/你当前的银子好像不够哟!")
        return
    elseif 玩家数据[id].角色.数据.坐骑列表[序列].忠诚 >= 100 then
        常规提示(id,"#Y/当前已达最高无需喂养!")
      return
    else
        玩家数据[id].角色:扣除银子(1000,"坐骑喂养",1)
        if 玩家数据[id].角色.数据.坐骑列表[序列].忠诚 >= 50 then
          玩家数据[id].角色.数据.坐骑列表[序列].忠诚 = 玩家数据[id].角色.数据.坐骑列表[序列].忠诚 + 取随机数(3,6)
        else
          玩家数据[id].角色.数据.坐骑列表[序列].忠诚 = 玩家数据[id].角色.数据.坐骑列表[序列].忠诚 + 取随机数(6,10)
        end
        if 玩家数据[id].角色.数据.坐骑列表[序列].忠诚>= 100 then
          玩家数据[id].角色.数据.坐骑列表[序列].忠诚 = 100
        end
        玩家数据[id].角色:坐骑刷新(序列)
    end
end
function 系统处理类:坐骑技能升级处理(内容)
   local 序列=内容.编号
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
        常规提示(id,"#Y你没有这样的坐骑")
        return
    elseif 玩家数据[id].角色.数据.坐骑列表[序列].技能点<1 then
        常规提示(id,"#Y你没有足够的技能点")
        return
    else
        local 编号 = 内容.技能编号
        local 技能名称 = 内容.名称
        if 玩家数据[id].角色.数据.坐骑列表[序列].技能[编号] ~= nil  and 玩家数据[id].角色.数据.坐骑列表[序列].技能[编号] == 技能名称 then
            if 玩家数据[id].角色.数据.坐骑列表[序列].技能等级 == nil then
              玩家数据[id].角色.数据.坐骑列表[序列].技能等级 = {}
            end
            if 玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] == nil then
                玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] = 1
                玩家数据[id].角色.数据.坐骑列表[序列].技能点 = 玩家数据[id].角色.数据.坐骑列表[序列].技能点 -1
                发送数据(玩家数据[id].连接id,61.1,{编号=序列,数据=玩家数据[id].角色.数据.坐骑列表[序列]})
            elseif 玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] >= 3 then
               常规提示(id,"#Y该技能以及升到顶级")
               return
            else
                玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] = 玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] + 1
                玩家数据[id].角色.数据.坐骑列表[序列].技能点 = 玩家数据[id].角色.数据.坐骑列表[序列].技能点 -1
                if  玩家数据[id].角色.数据.坐骑列表[序列].技能等级[编号] >= 3 then
                  玩家数据[id].角色.数据.坐骑列表[序列].技能[编号] = "高级"..技能名称
                end
                发送数据(玩家数据[id].连接id,61.1,{编号=序列,数据=玩家数据[id].角色.数据.坐骑列表[序列]})
                常规提示(id,"#Y技能升级成功")
            end
        end
    end
end

function 系统处理类:传音纸鹤处理(内容)
 local id = 内容.数字id
   if 玩家数据[id] and 共享货币[玩家数据[id].账号] then
      发送数据(玩家数据[id].连接id,59.2,共享货币[玩家数据[id].账号].仙玉)
   end
end

function 系统处理类:传音纸鹤购买处理(内容)
   local id= 内容.数字id
   if not 共享货币[玩家数据[id].账号] then 常规提示(id,"#Y/你没有那么多的仙玉") return end
      local 消耗仙玉=50
      if 共享货币[玩家数据[id].账号].仙玉 <消耗仙玉 then
        常规提示(id,"#Y/你没有那么多的仙玉")
      else
          共享货币[玩家数据[id].账号]:扣除仙玉(消耗仙玉,"购买传音",id)
          local 名称="传音纸鹤"
          玩家数据[id].道具:给予道具(id,名称,1)
          常规提示(id,"#Y/你获得了"..名称)
      end
end


-- function 系统处理类:图鉴数据查看(内容)
--   local id=内容.数字id+0
--     if 图鉴系统[id]==nil then
--        图鉴系统[id]={东海湾={大海龟=0,巨蛙=0,海毛虫=0,章鱼=0,海星=0,激活=100}
--                     ,江南野外={树怪=0,野猪=0,浣熊=0,激活=100}
--                     ,大雁塔={赌徒=0,强盗=0,骷髅怪=0,羊头怪=0,蛤蟆精=0,狐狸精=0,花妖=0,大蝙蝠=0,激活=100}
--                     ,大唐国境={赌徒=0,强盗=0,山贼=0,护卫=0,激活=100}
--                     ,大唐境外={老虎=0,黑熊=0,花妖=0,激活=100}
--                     ,魔王寨={牛妖=0,蛤蟆精=0,激活=100}
--                     ,普陀山={黑熊精=0,蜘蛛精=0,激活=100}
--                     ,盘丝岭={蜘蛛精=0,狐狸精=0,花妖=0,激活=100}
--                     ,狮驼岭={雷鸟人=0,蝴蝶仙子=0,激活=100}
--                     ,西牛贺州={小龙女=0,狼=0,激活=100}
--                     ,花果山={小龙女=0,老虎=0,黑熊=0,激活=100}
--                     ,海底迷宫={虾兵=0,蟹将=0,龟丞相=0,激活=100}
--                     ,地狱迷宫={野鬼=0,骷髅怪=0,僵尸=0,牛头=0,马面=0,激活=100}
--                     ,北俱芦洲={古代瑞兽=0,白熊=0,天将=0,激活=100}
--                     ,龙窟={古代瑞兽=0,黑山老妖=0,天兵=0,风伯=0,蛟龙=0,雨师=0,地狱战神=0,巡游天神=0,星灵仙子=0,激活=100}
--                     ,凤巢={黑山老妖=0,天将=0,天兵=0,雷鸟人=0,地狱战神=0,风伯=0,凤凰=0,如意仙子=0,芙蓉仙子=0,激活=100}
--                     ,无名鬼蜮={幽灵=0,吸血鬼=0,鬼将=0,阴阳伞=0,画魂=0,幽萤娃娃=0,激活=100}
--                     ,小西天={夜罗刹=0,炎魔神=0,噬天虎=0,阴阳伞=0,金铙僧=0,激活=100}
--                     ,女娲神迹={律法女娲=0,灵符女娲=0,净瓶女娲=0,阴阳伞=0,激活=100}
--                     ,小雷音寺={大力金刚=0,雾中仙=0,灵鹤=0,阴阳伞=0,激活=100}
--                     ,蓬莱仙岛={龙龟=0,红萼仙子=0,踏云兽=0,琴仙=0,阴阳伞=0,激活=100}
--                     ,月宫={兔子怪=0,蜘蛛精=0,激活=100}
--                     ,蟠桃园={巨力神猿=0,长眉灵猴=0,混沌兽=0,激活=100}
--                     ,墨家禁地={连弩车=0,巴蛇=0,机关鸟=0,机关兽=0,机关人=0,阴阳伞=0,激活=100}
--                     ,解阳山={犀牛将军人形=0,犀牛将军兽形=0,锦毛貂精=0,千年蛇魅=0,激活=100}
--                     ,子母河底={蚌精=0,碧水夜叉=0,鲛人=0,激活=100}
--                     ,麒麟山={泪妖=0,镜妖=0,鼠先锋=0,百足将军=0,野猪精=0,激活=100}
--                     ,碗子山={蝎子精=0,葫芦宝贝=0,激活=100}
--                     ,波月洞={猫灵人形=0,狂豹人形=0,猫灵兽形=0,狂豹兽形=0,混沌兽=0,激活=100}
--                     ,柳林坡={修罗傀儡鬼=0,蜃气妖=0,蔓藤妖花=0,激活=100}
--                     ,比丘国={金身罗汉=0,修罗傀儡妖=0,曼珠沙华=0,激活=100}
--                     ,须弥东界={持国巡守=0,毗舍童子=0,真陀护法=0,增长巡守=0,灵灯侍者=0,般若天女=0,激活=100}
--                     }
--       end
--       发送数据(玩家数据[id].连接id,3529.1,{图鉴系统[id]})

-- end

-- function 系统处理类:图鉴提交对话(内容)
--   local id=内容.数字id+0
--     local 选项={}
--     玩家数据[id].角色.图鉴提交={地图=内容.地图,名称=内容.名称}
--     local 对话="提交宠物对话自行修改"
--     for n=1,#玩家数据[id].召唤兽.数据 do
--         选项[#选项+1]=玩家数据[id].召唤兽.数据[n].名称
--     end
--     发送数据(玩家数据[id].连接id,1501,{名称=玩家数据[id].角色.数据.名称,模型=玩家数据[id].角色.数据.模型,对话=对话,选项=选项})
-- end


function 系统处理类:给予开关处理(内容)
   local id = 内容.数字id
    if not 玩家数据[id].角色.数据.接受给予 then
      玩家数据[id].角色.数据.接受给予 = true
    else
      玩家数据[id].角色.数据.接受给予 = false
    end
    if 玩家数据[id].角色.数据.接受给予 then
      常规提示(id,"#Y您已开启给予接受开关！")
    else
      常规提示(id,"#Y您已关闭给予接受开关！")
    end
    发送数据(玩家数据[id].连接id,150,{接受给予 = 玩家数据[id].角色.数据.接受给予})
end





function 系统处理类:坐骑统御处理(内容)
   local 序列=内容.序列
    local id=内容.数字id
    if 玩家数据[id].角色.数据.坐骑列表[序列]==nil then
      常规提示(id,"#Y你没有这样的坐骑")
      return
    elseif 玩家数据[id].摊位数据 ~= nil then
      常规提示(id,"#Y摆摊情况下无法进行此操作")
      return
    else
      if 玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽 == nil then
        玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽= {}
      end
      if #玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽 >= 2 then
        常规提示(id,"#Y该坐骑已经无法统御更多的召唤兽了")
        return
      elseif 玩家数据[id].召唤兽.数据[内容.召唤兽编号] == nil then
        常规提示(id,"#Y该召唤兽不存在")
        return
      elseif 玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御 ~= nil then
        local 坐骑编号 = 玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御
        if 玩家数据[id].角色.数据.坐骑列表[坐骑编号] ~= nil and 玩家数据[id].角色.数据.坐骑列表[坐骑编号].统御召唤兽 ~= nil then
          if 玩家数据[id].角色.数据.坐骑列表[坐骑编号].统御召唤兽[1] ~= nil  and 玩家数据[id].角色.数据.坐骑列表[坐骑编号].统御召唤兽[1] == 内容.召唤兽编号 then
            常规提示(id,"#Y该召唤兽被"..玩家数据[id].角色.数据.坐骑列表[玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御].名称.."统御中,请解除统御后再进行此操作")
            return
          elseif 玩家数据[id].角色.数据.坐骑列表[坐骑编号].统御召唤兽[2] ~= nil  and 玩家数据[id].角色.数据.坐骑列表[坐骑编号].统御召唤兽[2] == 内容.召唤兽编号 then
            常规提示(id,"#Y该召唤兽被"..玩家数据[id].角色.数据.坐骑列表[玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御].名称.."统御中,请解除统御后再进行此操作")
            return
          end
        end
      end
      玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽[#玩家数据[id].角色.数据.坐骑列表[序列].统御召唤兽+1] = 内容.召唤兽编号
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御 = 序列
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御属性.力量 =math.ceil(玩家数据[id].角色.数据.坐骑列表[序列].力量*0.1)
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御属性.魔力 =math.ceil(玩家数据[id].角色.数据.坐骑列表[序列].魔力*0.1)
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御属性.体质 =math.ceil(玩家数据[id].角色.数据.坐骑列表[序列].体质*0.1)
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御属性.敏捷 =math.ceil(玩家数据[id].角色.数据.坐骑列表[序列].敏捷*0.1)
      玩家数据[id].召唤兽.数据[内容.召唤兽编号].统御属性.耐力 =math.ceil(玩家数据[id].角色.数据.坐骑列表[序列].耐力*0.1)
      玩家数据[id].召唤兽:刷新信息(内容.召唤兽编号,"1")
      发送数据(玩家数据[id].连接id,20,玩家数据[id].召唤兽:取存档数据(内容.召唤兽编号))
      发送数据(玩家数据[id].连接id,61.1,{编号=序列,数据=玩家数据[id].角色.数据.坐骑列表[序列]})
      常规提示(id,"#Y统御该召唤兽成功")
    end

end


function 系统处理类:剑会停止匹配处理(内容)
  local id=内容.数字id
    local 模式 = 内容.模式
    if 模式=="单人匹配" then
        for i=#剑会天下.单人,1,-1 do
            if 剑会天下.单人[i].id == id then
                table.remove(剑会天下.单人,i)
                break
            end
        end
        发送数据(玩家数据[id].连接id,128)
        常规提示(id,"#Y/你已经取消了匹配！")
    elseif 模式=="三人匹配" then
          if 玩家数据[id].队伍~=0 then
              for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y/队伍取消了匹配！")
                  end
              end
              for i=#剑会天下.三人,1,-1 do
                  if 剑会天下.三人[i].id == 玩家数据[id].队伍 then
                      table.remove(剑会天下.三人,i)
                      break
                  end
              end
          end
    elseif 模式=="五人匹配" then
            if 玩家数据[id].队伍~=0 then
                for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y/队伍取消了匹配！")
                  end
                end
                for i=#剑会天下.五人,1,-1 do
                    if 剑会天下.五人[i].id == 玩家数据[id].队伍 then
                        table.remove(剑会天下.五人,i)
                        break
                    end
                end
            end
    end
end



function 系统处理类:剑会匹配处理(内容)
    local id=内容.数字id
    local 模式 = 内容.模式
    if not 剑会开关 or not 模式 then
        常规提示(id,"#Y/当前不是活动时间,请在等待剑会天下开启时进行匹配！")
        return
    end
    local 玩家地图 =玩家数据[id].角色.数据.地图数据.编号
    if 玩家地图~=1001 and 玩家地图~=1501 and 玩家地图~=1070 and 玩家地图~=1092 and 玩家地图~=1226 and 玩家地图~=1040 then
        常规提示(id,"#Y/该场景不能进行匹配 请玩家切换场景至安全区！")
        return
    end
    if 模式=="单人匹配" then
        if 玩家数据[id].角色.数据.门派=="无门派" then
             常规提示(id,"#Y/还没有拜师呢,不能参加匹配!")
             return
        end
        if 玩家数据[id].角色.数据.等级<69 then
          常规提示(id,"#Y/等级不足无法加入匹配!")
             return
        end
        if 取队伍人数(id)==1 then
            table.insert(剑会天下.单人,{匹配=true,id=id,积分=剑会天下[id].当前积分})
            发送数据(玩家数据[id].连接id,127,{人数=内容.人数,模式=模式})
        else
          常规提示(id,"#Y/单人匹配模式请一个人参加活动！")
          return
        end
    elseif 模式=="三人匹配" then
          if 取队长权限(id) and 取队伍人数(id)==3  then
              for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.门派=="无门派" then
                      常规提示(id,"#Y/队伍中#R/".. 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.名称.."#Y/还没有拜师呢,不能参加匹配!")
                      return
                  end
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.等级<69 then
                      常规提示(id,"#Y/队伍中#R/".. 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.名称.."#Y/等级小于69级,无法匹配三人模式")
                      return
                  end
              end
              local 平均分 = 0
              for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  取剑会天下数据(队伍数据[玩家数据[id].队伍].成员数据[i])
                  平均分=平均分+剑会天下[队伍数据[玩家数据[id].队伍].成员数据[i]].当前积分
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].连接id,127,{人数=内容.人数,模式=模式})
                  end
              end
              平均分=math.floor(平均分/3)
              table.insert(剑会天下.三人,{匹配=true,id=玩家数据[id].队伍,积分=平均分})
          else
              常规提示(id,"#Y/只有队长并且三人才可参加匹配！")
              return
          end
    elseif 模式=="五人匹配" then
        if 取队长权限(id) and 取队伍人数(id)==5 then
              for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.门派=="无门派" then
                      常规提示(id,"#Y/队伍中#R/".. 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.名称.."#Y/还没有拜师呢,不能参加匹配!")
                      return
                  end
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.等级<69 then
                      常规提示(id,"#Y/队伍中#R/".. 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].角色.数据.名称.."#Y/等级小于69级,无法匹配五人模式")
                      return
                  end
              end
              local 平均分 = 0
              for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
                  取剑会天下数据(队伍数据[玩家数据[id].队伍].成员数据[i])
                  if 玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[id].队伍].成员数据[i]].连接id,127,{人数=内容.人数,模式=模式})
                  end
              end
              平均分=math.floor(平均分/5)
              table.insert(剑会天下.五人,{匹配=true,id=玩家数据[id].队伍,积分=平均分})
        else
            常规提示(id,"#Y/只有队长并且5人,才可参加匹配！")
            return
        end
    end
end




function 系统处理类:剑会单人匹配()
          if 判断是否为空表(剑会天下.单人) then
            return
          end
          table.sort(剑会天下.单人,积分排序)
          for i,v in ipairs(剑会天下.单人) do
              if 玩家数据[v.id] and v.匹配 and 玩家数据[v.id].战斗 ==0 and not 玩家数据[v.id].观战 then
                  v.匹配 = false
                  if i%2==1 then
                      if i~=#剑会天下.单人 then
                          local 蓝方id = 剑会天下.单人[i+1].id
                          剑会天下.单人[i+1].匹配=false
                          发送数据(玩家数据[v.id].连接id,128)
                          常规提示(v.id,"#Y/进入剑会天下PK战斗")
                          发送数据(玩家数据[蓝方id].连接id,128)
                          常规提示(蓝方id,"#Y/进入剑会天下PK战斗")
                          战斗准备类:创建玩家战斗(v.id, 410005, 蓝方id, 1501)
                      else
                            local 需求属性 = {"模型","等级","最大气血","最大魔法","伤害","法伤","速度","防御","法防","体质","魔力","敏捷","耐力","力量","武器伤害"}
                            local 假人属性={}
                            假人属性[1]={
                                      位置=1,
                                      角色分类="角色",
                                      名称=取随机中文姓名(取随机数(2,5)),
                                      攻击修炼=玩家数据[v.id].角色.数据.修炼.攻击修炼[1],
                                      法术修炼=玩家数据[v.id].角色.数据.修炼.法术修炼[1],
                                      防御修炼=玩家数据[v.id].角色.数据.修炼.防御修炼[1],
                                      抗法修炼=玩家数据[v.id].角色.数据.修炼.抗法修炼[1],
                                      主动技能 = Q_门派法术[玩家数据[v.id].角色.数据.门派],
                            }
                            for k,z in ipairs(需求属性) do
                                if 玩家数据[v.id].角色.数据[z] then
                                    假人属性[1][z] = 玩家数据[v.id].角色.数据[z]
                                end
                            end
                            if not 判断是否为空表(玩家数据[v.id].角色.数据.参战宝宝) then
                                假人属性[2]={
                                          位置=6,
                                          主动技能 = {},
                                          名称=玩家数据[v.id].角色.数据.参战宝宝.模型,
                                          技能= DeepCopy(玩家数据[v.id].角色.数据.参战宝宝.技能),
                                          攻击修炼=玩家数据[v.id].角色.数据.bb修炼.攻击控制力[1],
                                          法术修炼=玩家数据[v.id].角色.数据.bb修炼.法术控制力[1],
                                          防御修炼=玩家数据[v.id].角色.数据.bb修炼.防御控制力[1],
                                          抗法修炼=玩家数据[v.id].角色.数据.bb修炼.抗法控制力[1],
                                          内丹数据=玩家数据[v.id].角色.数据.参战宝宝.内丹数据,
                                }

                                for k,z in ipairs(需求属性) do
                                    if 玩家数据[v.id].角色.数据.参战宝宝[z] then
                                        假人属性[2][z] = 玩家数据[v.id].角色.数据.参战宝宝[z]
                                    end
                                end
                            end
                            发送数据(玩家数据[v.id].连接id,128)
                            常规提示(v.id,"#Y/进入剑会天下PK战斗")
                            战斗准备类:创建战斗(v.id,110001,0,nil,假人属性)
                      end
                  end
              end
          end
          剑会天下.单人={}
end

function 系统处理类:剑会三人匹配()
          if 判断是否为空表(剑会天下.三人) then
            return
          end
          table.sort(剑会天下.三人,积分排序)
          for i,v in ipairs(剑会天下.三人) do
              if 玩家数据[v.id] and v.匹配 and 玩家数据[v.id].战斗 ==0 and not 玩家数据[v.id].观战 then
                  v.匹配 = false
                  if i%2==1 then
                      if i~=#剑会天下.三人 then
                          local 蓝方id = 剑会天下.三人[i+1].id
                          剑会天下.三人[i+1].匹配=false
                          if 玩家数据[v.id].队伍~=0 then
                              for k,n in ipairs(队伍数据[玩家数据[v.id].队伍].成员数据) do
                                  if 玩家数据[n] then
                                      发送数据(玩家数据[n].连接id,128)
                                      常规提示(n,"#Y/进入剑会天下PK战斗")
                                  end
                              end
                          end
                          if 玩家数据[蓝方id].队伍~=0 then
                              for k,n in ipairs(队伍数据[玩家数据[蓝方id].队伍].成员数据) do
                                  if 玩家数据[n] then
                                      发送数据(玩家数据[n].连接id,128)
                                      常规提示(n,"#Y/进入剑会天下PK战斗")
                                  end
                              end
                          end
                          战斗准备类:创建玩家战斗(v.id, 410005, 蓝方id, 1501)
                      else
                            local 需求属性 = {"模型","等级","最大气血","最大魔法","伤害","法伤","速度","防御","法防","体质","魔力","敏捷","耐力","力量","武器伤害"}
                            local 假人属性={}
                            if 玩家数据[v.id].队伍~=0 then
                                for k,n in ipairs(队伍数据[玩家数据[v.id].队伍].成员数据) do
                                    if 玩家数据[n] then
                                       local 临时表 = {
                                            位置 = k,
                                            角色分类="角色",
                                            名称=取随机中文姓名(取随机数(2,5)),
                                            攻击修炼=玩家数据[n].角色.数据.修炼.攻击修炼[1],
                                            法术修炼=玩家数据[n].角色.数据.修炼.法术修炼[1],
                                            防御修炼=玩家数据[n].角色.数据.修炼.防御修炼[1],
                                            抗法修炼=玩家数据[n].角色.数据.修炼.抗法修炼[1],
                                            主动技能 = Q_门派法术[玩家数据[n].角色.数据.门派],
                                        }
                                        for j,z in ipairs(需求属性) do
                                            if 玩家数据[n].角色.数据[z] then
                                                临时表[z] = 玩家数据[n].角色.数据[z]
                                            end
                                        end
                                        table.insert(假人属性, 临时表)
                                        if not 判断是否为空表(玩家数据[n].角色.数据.参战宝宝) then
                                            local 临时表1 ={
                                                      位置=k+5,
                                                      主动技能 = {},
                                                      名称=玩家数据[n].角色.数据.参战宝宝.模型,
                                                      技能= DeepCopy(玩家数据[n].角色.数据.参战宝宝.技能),
                                                      攻击修炼=玩家数据[n].角色.数据.bb修炼.攻击控制力[1],
                                                      法术修炼=玩家数据[n].角色.数据.bb修炼.法术控制力[1],
                                                      防御修炼=玩家数据[n].角色.数据.bb修炼.防御控制力[1],
                                                      抗法修炼=玩家数据[n].角色.数据.bb修炼.抗法控制力[1],
                                                      内丹数据=玩家数据[n].角色.数据.参战宝宝.内丹数据,
                                            }
                                            for j,z in ipairs(需求属性) do
                                                if 玩家数据[n].角色.数据.参战宝宝[z] then
                                                    临时表1[z] = 玩家数据[n].角色.数据.参战宝宝[z]
                                                end
                                            end
                                             table.insert(假人属性, 临时表1)
                                        end
                                        发送数据(玩家数据[n].连接id,128)
                                        常规提示(n,"#Y/进入剑会天下PK战斗")
                                    end
                                end
                               战斗准备类:创建战斗(v.id,110001,0,1501,假人属性)
                            end
                      end
                  end
              end
          end
        剑会天下.三人={}
end


function 系统处理类:剑会五人匹配()
          if 判断是否为空表(剑会天下.五人) then
            return
          end
          table.sort(剑会天下.五人,积分排序)
          for i,v in ipairs(剑会天下.五人) do
              if 玩家数据[v.id] and v.匹配 and 玩家数据[v.id].战斗 ==0 and not 玩家数据[v.id].观战 then
                  v.匹配 = false
                  if i%2==1 then
                      if i~=#剑会天下.五人 then
                          local 蓝方id = 剑会天下.五人[i+1].id
                          剑会天下.五人[i+1].匹配=false
                          if 玩家数据[v.id].队伍~=0 then
                              for k,n in ipairs(队伍数据[玩家数据[v.id].队伍].成员数据) do
                                  if 玩家数据[n] then
                                      发送数据(玩家数据[n].连接id,128)
                                      常规提示(n,"#Y/进入剑会天下PK战斗")
                                  end
                              end
                          end
                          if 玩家数据[蓝方id].队伍~=0 then
                              for k,n in ipairs(队伍数据[玩家数据[蓝方id].队伍].成员数据) do
                                  if 玩家数据[n] then
                                      发送数据(玩家数据[n].连接id,128)
                                      常规提示(n,"#Y/进入剑会天下PK战斗")
                                  end
                              end
                          end
                          战斗准备类:创建玩家战斗(v.id, 410005, 蓝方id, 1501)
                      else
                            local 需求属性 = {"模型","等级","最大气血","最大魔法","伤害","法伤","速度","防御","法防","体质","魔力","敏捷","耐力","力量","武器伤害"}
                            local 假人属性={}
                            if 玩家数据[v.id].队伍~=0 then
                                for k,n in ipairs(队伍数据[玩家数据[v.id].队伍].成员数据) do
                                    if 玩家数据[n] then
                                       local 临时表 = {
                                            位置 = k,
                                            角色分类="角色",
                                            名称=取随机中文姓名(取随机数(2,5)),
                                            攻击修炼=玩家数据[n].角色.数据.修炼.攻击修炼[1],
                                            法术修炼=玩家数据[n].角色.数据.修炼.法术修炼[1],
                                            防御修炼=玩家数据[n].角色.数据.修炼.防御修炼[1],
                                            抗法修炼=玩家数据[n].角色.数据.修炼.抗法修炼[1],
                                            主动技能 = Q_门派法术[玩家数据[n].角色.数据.门派],
                                        }
                                        for j,z in ipairs(需求属性) do
                                            if 玩家数据[n].角色.数据[z] then
                                                临时表[z] = 玩家数据[n].角色.数据[z]
                                            end
                                        end
                                        table.insert(假人属性, 临时表)
                                        if not 判断是否为空表(玩家数据[n].角色.数据.参战宝宝) then
                                            local 临时表1 ={
                                                      位置=k+5,
                                                      主动技能 = {},
                                                      名称=玩家数据[n].角色.数据.参战宝宝.模型,
                                                      技能= DeepCopy(玩家数据[n].角色.数据.参战宝宝.技能),
                                                      攻击修炼=玩家数据[n].角色.数据.bb修炼.攻击控制力[1],
                                                      法术修炼=玩家数据[n].角色.数据.bb修炼.法术控制力[1],
                                                      防御修炼=玩家数据[n].角色.数据.bb修炼.防御控制力[1],
                                                      抗法修炼=玩家数据[n].角色.数据.bb修炼.抗法控制力[1],
                                                      内丹数据=玩家数据[n].角色.数据.参战宝宝.内丹数据,
                                            }
                                            for j,z in ipairs(需求属性) do
                                                if 玩家数据[n].角色.数据.参战宝宝[z] then
                                                    临时表1[z] = 玩家数据[n].角色.数据.参战宝宝[z]
                                                end
                                            end
                                             table.insert(假人属性, 临时表1)
                                        end
                                        发送数据(玩家数据[n].连接id,128)
                                        常规提示(n,"#Y/进入剑会天下PK战斗")
                                    end
                                end
                               战斗准备类:创建战斗(v.id,110001,0,1501,假人属性)
                            end
                      end
                  end
              end
          end
        剑会天下.五人={}
end



function 系统处理类:自动回收处理(内容)
     local 序列 = 内容.序列
      if 自动回收[内容.数字id] ==nil then
         自动回收[内容.数字id] = {兽决=0,高级兽决=0,二级药=0,五宝=0,宝石=0,超级金柳露=0,环装=0,暗器=0,书铁=0,修练果=0,强化石=0,月华露=0,清灵净瓶=0,九转金丹=0,低级内丹=0,高级内丹=0}
      end

          if 序列 == "兽决" then
                  if 自动回收[内容.数字id].兽决 == 1 then
                    自动回收[内容.数字id].兽决 = 0
                  else
                    自动回收[内容.数字id].兽决 = 1
                  end
               elseif 序列 == "高级兽决" then
                  if 自动回收[内容.数字id].高级兽决 == 1 then
                    自动回收[内容.数字id].高级兽决 = 0
                  else
                    自动回收[内容.数字id].高级兽决 = 1
                  end
              elseif 序列 == "暗器" then
                if 自动回收[内容.数字id].暗器 == 1 then
                    自动回收[内容.数字id].暗器 = 0
                  else
                    自动回收[内容.数字id].暗器 = 1
                  end
              elseif 序列 == "环装" then
                if 自动回收[内容.数字id].环装 == 1 then
                    自动回收[内容.数字id].环装 = 0
                  else
                    自动回收[内容.数字id].环装 = 1
                  end
              elseif 序列 == "二级药" then
                if 自动回收[内容.数字id].二级药 == 1 then
                    自动回收[内容.数字id].二级药 = 0
                  else
                    自动回收[内容.数字id].二级药 = 1
                  end
              elseif 序列 == "五宝" then
                if 自动回收[内容.数字id].五宝 == 1 then
                    自动回收[内容.数字id].五宝 = 0
                  else
                    自动回收[内容.数字id].五宝 = 1
                  end
              elseif 序列 == "宝石" then
                if 自动回收[内容.数字id].宝石 == 1 then
                    自动回收[内容.数字id].宝石 = 0
                  else
                    自动回收[内容.数字id].宝石 = 1
                  end
              elseif 序列 == "超级金柳露" then
                if 自动回收[内容.数字id].超级金柳露 == 1 then
                    自动回收[内容.数字id].超级金柳露 = 0
                  else
                    自动回收[内容.数字id].超级金柳露 = 1
                  end
              elseif 序列 == "书铁" then
                if 自动回收[内容.数字id].书铁 == 1 then
                    自动回收[内容.数字id].书铁 = 0
                  else
                    自动回收[内容.数字id].书铁 = 1
                  end
              elseif 序列 == "修练果" then
                if 自动回收[内容.数字id].修练果 == 1 then
                    自动回收[内容.数字id].修练果 = 0
                  else
                    自动回收[内容.数字id].修练果 = 1
                  end
              elseif 序列 == "强化石" then
                if 自动回收[内容.数字id].强化石 == 1 then
                    自动回收[内容.数字id].强化石 = 0
                  else
                    自动回收[内容.数字id].强化石 = 1
                  end
              elseif 序列 == "月华露" then
                if 自动回收[内容.数字id].月华露 == 1 then
                    自动回收[内容.数字id].月华露 = 0
                  else
                    自动回收[内容.数字id].月华露 = 1
                  end
              elseif 序列 == "清灵净瓶" then
                if 自动回收[内容.数字id].清灵净瓶 == 1 then
                    自动回收[内容.数字id].清灵净瓶 = 0
                  else
                    自动回收[内容.数字id].清灵净瓶 = 1
                  end
              elseif 序列 == "九转金丹" then
                if 自动回收[内容.数字id].九转金丹 == 1 then
                    自动回收[内容.数字id].九转金丹 = 0
                  else
                    自动回收[内容.数字id].九转金丹 = 1
                  end
              elseif 序列 == "低级内丹" then
                if 自动回收[内容.数字id].低级内丹 == 1 then
                    自动回收[内容.数字id].低级内丹 = 0
                  else
                    自动回收[内容.数字id].低级内丹 = 1
                  end

              elseif 序列 == "高级内丹" then
                if 自动回收[内容.数字id].高级内丹 == 1 then
                    自动回收[内容.数字id].高级内丹 = 0
                  else
                    自动回收[内容.数字id].高级内丹 = 1
                  end







            end
            发送数据(玩家数据[内容.数字id].连接id,3700,{自动回收=自动回收[内容.数字id],回收价格 =自定义回收价格})


end



function 系统处理类:自动抓鬼(id)
          if not 玩家数据[id].自动抓鬼 or type(玩家数据[id].自动抓鬼)~="table" or not 玩家数据[id].自动抓鬼.时间
              or not 玩家数据[id].自动抓鬼.事件 or not 玩家数据[id].自动抓鬼.进程  then
              发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件="自动抓鬼"})
              常规提示(id,"#Y/该活动必须组队完成且由队长带领")
              常规提示(id,"#Y/自动抓鬼已关闭需要请重新开启")
              玩家数据[id].自动抓鬼=nil
              return
          end

          if 玩家数据[id].队伍==0 or 玩家数据[id].队长==false  then
              发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=玩家数据[id].自动抓鬼.事件})
              常规提示(id,"#Y/该活动必须组队完成且由队长带领")
              常规提示(id,"#Y/"..玩家数据[id].自动抓鬼.事件.."已关闭需要请重新开启")
              玩家数据[id].自动抓鬼=nil
              return
          end
          if 活动次数查询(id,"抓鬼任务")==false  then
                   发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=玩家数据[id].自动抓鬼.事件})
                   常规提示(id,"#Y今日抓鬼次数已满")
                   玩家数据[id].自动抓鬼=nil
                   return
          end
          local 队伍id1=玩家数据[id].队伍
          for n=1,#队伍数据[队伍id1].成员数据 do
              local 临时id =队伍数据[队伍id1].成员数据[n]
              if not 玩家数据[临时id].角色.数据.自动抓鬼 or 玩家数据[临时id].角色.数据.自动抓鬼<1 then
                   发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=玩家数据[id].自动抓鬼.事件})
                   常规提示(id,"#Y/"..玩家数据[id].自动抓鬼.事件.."每次需要1次自动抓鬼次数,玩家#R"..玩家数据[临时id].角色.数据.名称.."#Y自动抓鬼次数不够")
                   玩家数据[id].自动抓鬼 = nil
                   return
              end
          end
          local 进程 = 玩家数据[id].自动抓鬼.进程
          local 事件 = 玩家数据[id].自动抓鬼.事件
          if 进程==1 then
              if 事件 == "自动抓鬼" then
                    地图处理类:跳转地图(id,1122,52,62)
              else
                    地图处理类:跳转地图(id,1125,30,24)
              end
              玩家数据[id].自动抓鬼.进程 = 2
              玩家数据[id].自动抓鬼.开启 = true
              玩家数据[id].自动抓鬼.时间 =os.time()+2
          elseif 进程==2 then
                    -- if 事件 == "自动抓鬼" then
                    --    发送数据(玩家数据[id].连接id,1501,{名称="钟馗",模型="男人_钟馗",对话="现在做鬼的也不安分，老是有出去闲逛的，你能帮我抓他们回来吗？",选项={"好的 我帮你","我来取消任务","不，我没有空"}})
                    -- else
                    --    发送数据(玩家数据[id].连接id,1501,{名称="黑无常",模型="黑无常",对话="地狱里头的那些鬼王已经无法镇压了，现在正在四处祸害人间。可惜仅凭我一己之力无法将他们全都收服。",选项={"我们来帮你","我怕鬼，再见"}})
                    -- end
                    玩家数据[id].自动抓鬼.进程 = 3
                    玩家数据[id].自动抓鬼.开启 = true
                    玩家数据[id].自动抓鬼.时间 =os.time()+2
          elseif 进程==3 then
                  local 队伍id=玩家数据[id].队伍
                  for n=1,#队伍数据[队伍id].成员数据 do
                      local 临时id =队伍数据[队伍id].成员数据[n]
                      if not 玩家数据[临时id].角色.数据.自动抓鬼 or 玩家数据[临时id].角色.数据.自动抓鬼<1 then
                           发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                           常规提示(id,"#Y/"..事件.."每次需要1次自动抓鬼次数,玩家#R"..玩家数据[临时id].角色.数据.名称.."自动抓鬼次数不够")
                           玩家数据[id].自动抓鬼 = nil
                           return
                      end
                      local 任务id = 玩家数据[临时id].角色:取任务(8)
                      if 任务id and 任务id~=0 then
                          玩家数据[临时id].角色:取消任务(任务id)
                          玩家数据[临时id].角色.数据.捉鬼次数=1
                          常规提示(临时id,"#Y/已经取消任务,同时当前捉鬼次数重置")
                      end
                      任务id = 玩家数据[临时id].角色:取任务(211)
                      if 任务id and 任务id~=0 then
                          玩家数据[临时id].角色:取消任务(任务id)
                          玩家数据[临时id].角色.数据.捉鬼次数=1
                          常规提示(临时id,"#Y/已经取消任务,同时当前捉鬼次数重置")
                      end
                  end
                  if 玩家数据[id].角色.数据.自动抓鬼>=1 then
                      if 事件 == "自动抓鬼" then
                          if 取队伍最低等级(玩家数据[id].队伍,25) then
                              发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                              玩家数据[id].自动抓鬼 = nil
                              常规提示(id,"#Y/等级小于25级的玩家无法领取此任务")
                              return
                          else
                              任务处理类:添加抓鬼任务(id)
                          end
                      else
                          if 取队伍最低等级(玩家数据[id].队伍,100) then
                              发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                              玩家数据[id].自动抓鬼 = nil
                              常规提示(id,"#Y/等级小于100级的玩家无法领取此任务")
                              return
                          else
                              任务处理类:设置鬼王任务(id)
                          end
                      end
                      for n=1,#队伍数据[队伍id].成员数据 do
                          local 临时id =队伍数据[队伍id].成员数据[n]
                          玩家数据[临时id].角色.数据.自动抓鬼 = 玩家数据[临时id].角色.数据.自动抓鬼 - 1
                      end
                      玩家数据[id].自动抓鬼.进程 = 4
                      玩家数据[id].自动抓鬼.开启 = true
                      玩家数据[id].自动抓鬼.时间 =os.time()+2
                      发送数据(玩家数据[id].连接id,101,{进程 = "开启",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                  else
                      发送数据(玩家数据[id].连接id,101,{进程 = "关闭",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                      玩家数据[id].自动抓鬼 = nil
                      常规提示(id,"#Y/自动抓鬼次数不足。")
                      return
                  end
          elseif 进程==4 then
                  local 任务id = 0
                  if 事件 == "自动抓鬼" then
                      任务id =玩家数据[id].角色:取任务(8)
                  else
                      任务id =玩家数据[id].角色:取任务(211)
                  end
                  if 任务id~= 0 then
                      地图处理类:跳转地图(id,任务数据[任务id].地图编号,任务数据[任务id].x,任务数据[任务id].y)
                      玩家数据[id].自动抓鬼.进程 = 5
                      玩家数据[id].自动抓鬼.开启 = true
                      玩家数据[id].自动抓鬼.时间 =os.time()+2
                  else
                      玩家数据[id].自动抓鬼.进程 = 1
                      玩家数据[id].自动抓鬼.开启 = true
                      玩家数据[id].自动抓鬼.时间 =os.time()+2
                      发送数据(玩家数据[id].连接id,101,{进程 = "开启",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                  end
          elseif 进程==5 then
                  local 进入战斗 = true
                  local 队伍id=玩家数据[id].队伍
                  for n=1,#队伍数据[队伍id].成员数据 do
                       if 玩家数据[队伍数据[队伍id].成员数据[n]].战斗 and 玩家数据[队伍数据[队伍id].成员数据[n]].战斗~=0 then
                          进入战斗 = false
                       end
                  end
                  if 进入战斗 then
                      local 任务id =0
                      if 事件 == "自动抓鬼" then
                          任务id =玩家数据[id].角色:取任务(8)
                          if 任务id~=nil and  任务id~= 0  then
                                任务数据[任务id].地图编号=玩家数据[id].角色.数据.地图数据.编号
                                任务数据[任务id].x=math.floor(玩家数据[id].角色.数据.地图数据.x/20)
                                任务数据[任务id].y=math.floor(玩家数据[id].角色.数据.地图数据.y/20)
                                任务数据[任务id].战斗=true
                                战斗准备类:创建战斗(id+0,100008,任务id)
                                玩家数据[id].地图单位=nil
                                玩家数据[id].自动抓鬼.开启 = false
                          else
                                玩家数据[id].自动抓鬼.进程 = 1
                                玩家数据[id].自动抓鬼.开启 = true
                                玩家数据[id].自动抓鬼.时间 =os.time()+2
                                发送数据(玩家数据[id].连接id,101,{进程 = "开启",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                          end
                      else
                          任务id =玩家数据[id].角色:取任务(211)
                           if 任务id~=nil and 任务id~= 0 then
                                任务数据[任务id].地图编号=玩家数据[id].角色.数据.地图数据.编号
                                任务数据[任务id].x=math.floor(玩家数据[id].角色.数据.地图数据.x/20)
                                任务数据[任务id].y=math.floor(玩家数据[id].角色.数据.地图数据.y/20)
                                任务数据[任务id].战斗=true
                                战斗准备类:创建战斗(id+0,100307,任务id)
                                玩家数据[id].地图单位=nil
                                玩家数据[id].自动抓鬼.开启 = false
                          else
                                玩家数据[id].自动抓鬼.进程 = 1
                                玩家数据[id].自动抓鬼.开启 = true
                                玩家数据[id].自动抓鬼.时间 =os.time()+2
                                发送数据(玩家数据[id].连接id,101,{进程 = "开启",仙玉=玩家数据[id].角色.数据.自动抓鬼,事件=事件})
                          end
                      end
                  end

          end








end






function 系统处理类:月卡福利领取(内容)
  local id = 内容.数字id
  if not 玩家数据[id] then return end
  if not 共享货币[玩家数据[id].账号] then
      常规提示(id,"#Y/数据错误")
      return
  end
  if 玩家数据[id].角色.数据.月卡.到期时间 < os.time() then
      玩家数据[id].角色.数据.月卡.开通 = false
  end


   local 发送信息 = 自定义数据.月卡数据
  if 内容.文本=="领取奖励" then
        if not 玩家数据[id].角色.数据.月卡.开通 then
          常规提示(id,"#Y/你还未开通月卡，请开通月卡后在操作")
          return
        end
        if 玩家数据[id].角色.数据.月卡.当前领取 == os.date("%j") then
          常规提示(id,"#Y/你今日奖励已领取")
          return
        end
        if 发送信息.经验>0 then
          玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 发送信息.经验
          常规提示(id,"#Y/你获得了#R/"..发送信息.经验.."#Y/点经验")
        end
        if 发送信息.储备>0 then
          玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 发送信息.储备
          常规提示(id,"#Y/你获得了#R/"..发送信息.储备.."#Y/两储备")
        end
        if 发送信息.银子>0 then
          玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 发送信息.银子
          常规提示(id,"#Y/你获得了#R/"..发送信息.银子.."#Y/两银子")
        end
        if 发送信息.抓鬼>0 then
          玩家数据[id].角色.数据.自动抓鬼 = 玩家数据[id].角色.数据.自动抓鬼 + 发送信息.抓鬼
          常规提示(id,"#Y/你获得了#R/"..发送信息.抓鬼.."#Y/次抓鬼")
        end
        if 发送信息.仙玉>0 then
            共享货币[玩家数据[id].账号]:添加仙玉(发送信息.仙玉,id,"月卡领取")
        end
        if 发送信息.点卡>0 then
            共享货币[玩家数据[id].账号]:添加点卡(发送信息.点卡,id,"月卡领取")
        end
        if 发送信息.物品数量>0 then
          for i=1,发送信息.物品数量 do
              local 物品名称 = 发送信息.物品[i].名称
              local 数量 = tonumber(发送信息.物品[i].数量)
               仙玉商城类:仙玉商城商品处理(id,物品名称,数量)
               常规提示(id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
            end
        end
        玩家数据[id].角色.数据.月卡.当前领取 = os.date("%j")
        广播消息({内容=format("#S/(%s会员卡）#Y玩家#R/%s#Y/领取了会员每日福利，获得了#S/%s#Y/！！",服务端参数.名称,玩家数据[id].角色.数据.名称,自定义数据.月卡数据.显示物品),频道="xt"})
    end
    if 发送信息.月卡货币 == "仙玉" then
        发送信息.玩家货币 = 共享货币[玩家数据[id].账号].仙玉
    else
        发送信息.玩家货币 = 共享货币[玩家数据[id].账号].点卡
    end
    发送信息.月卡 =玩家数据[id].角色.数据.月卡
    发送数据(玩家数据[id].连接id,104,发送信息)
end













function 系统处理类:会员福利领取(内容)
  local id = 内容.数字id
  local 充值领取=f函数.读配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","充值领取")+0
  if not 共享货币[玩家数据[id].账号] then
     常规提示(id,"#Y/数据错误")
      return
  end
  if 内容.领取 == "领取累充" then
    if 内容.文本==nil or 内容.文本=="" or 内容.编号==nil or 内容.编号=="" or tonumber(内容.编号)==nil or tonumber(内容.编号)<=0  then
      常规提示(id,"#Y/数据错误")
      return
    end
    local 编号= tonumber(内容.编号)

    if f函数.文件是否存在([[每日活动/累计充值/]]..内容.文本..".txt")==false    then
          常规提示(id,"#Y/数据错误")
     elseif 自定义数据.累充数据[编号]==nil then
            常规提示(id,"#Y/数据错误")
     elseif 共享货币[玩家数据[id].账号].充值累计<自定义数据.累充数据[编号].需求充值 then
             常规提示(id,"#Y/你的累计充值积分不足")
     elseif 充值领取>=自定义数据.累充数据[编号].需求充值 then
          常规提示(id,"#Y/你已领取过该奖励")
    elseif 玩家数据[id].召唤兽:是否携带上限() then
         常规提示(id,"#Y/你的召唤兽已满无法携带更多")
    elseif 玩家数据[id].角色:取道具格子()<=0 then
       常规提示(id,"您的道具栏物品已经满啦")
    else
      if 自定义数据.累充数据[编号].文本==内容.文本 then
        if 自定义数据.累充数据[编号].货币数量>0 then
             if 自定义数据.累充数据[编号].货币类型 == "经验" then
                玩家数据[id].角色.数据.当前经验 = 玩家数据[self.玩家id].角色.数据.当前经验 + 自定义数据.累充数据[编号].货币数量
                常规提示(id,"#Y/你获得了#R/"..自定义数据.累充数据[编号].货币数量.."#Y/点经验")
             elseif 自定义数据.累充数据[编号].货币类型 == "储备" then
                    玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 自定义数据.累充数据[编号].货币数量
                    常规提示(id,"#Y/你获得了#R/"..自定义数据.累充数据[编号].货币数量.."#Y/两储备")
             elseif 自定义数据.累充数据[编号].货币类型 == "银子" then
                    玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 自定义数据.累充数据[编号].货币数量
                    常规提示(id,"#Y/你获得了#R/"..自定义数据.累充数据[编号].货币数量.."#Y/两银子")
             elseif 自定义数据.累充数据[编号].货币类型 == "仙玉"  then
                    共享货币[玩家数据[id].账号]:添加仙玉(自定义数据.累充数据[编号].货币数量,id,"累充奖励")
             elseif 自定义数据.累充数据[编号].货币类型 == "点卡" then
                    共享货币[玩家数据[id].账号]:添加点卡(自定义数据.累充数据[编号].货币数量,id,"累充奖励")
             end
        end
         if 自定义数据.累充数据[编号].召唤兽~="不给" then
              玩家数据[id].召唤兽:添加召唤兽(自定义数据.累充数据[编号].召唤兽,自定义数据.累充数据[编号].召唤兽,自定义数据.累充数据[编号].召唤兽类型)
              常规提示(id,"#Y/你获得了一只#R/"..自定义数据.累充数据[编号].召唤兽)
         end
         if 自定义数据.累充数据[编号].物品数量>0 then
            for i=1,自定义数据.累充数据[编号].物品数量 do
              local 物品名称 = 自定义数据.累充数据[编号].物品[i].名称
              local 数量 = tonumber(自定义数据.累充数据[编号].物品[i].数量)
               仙玉商城类:仙玉商城商品处理(id,物品名称,数量)
               常规提示(id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
              end
          end
          充值领取=自定义数据.累充数据[编号].需求充值
          f函数.写配置(程序目录..[[data\]]..玩家数据[id].账号..[[\账号信息.txt]],"账号配置","充值领取",自定义数据.累充数据[编号].需求充值)
       else
           常规提示(id,"#Y/数据错误，请联系管理员")
       end
     end

  elseif 内容.领取 == "打开" then
     发送数据(玩家数据[id].连接id,88,{累计充值=共享货币[玩家数据[id].账号].充值累计,首充数据=自定义数据.首充数据,累充数据=自定义数据.累充数据,已领充值=充值领取})
     return

  elseif 内容.领取 == "确定充值" then
    if 内容.卡号 == nil  or 内容.卡号 =="" then  return end---------------
     local 内充组={}
     local z=取文件夹的所有名(程序目录..[[\自动充值]])
     local 已充值 = true
      for i=1,#z do
          if string.find(z[i],"@")~= nil then
            内充组[z[i]]=true
          end
      end
      for n, v in pairs(内充组) do
        local y = 取文件的所有名(程序目录..[[\自动充值\]]..n..[[\]])
        for i=1,#y do
          if 内容.卡号 == y[i] then
            local c = os.remove(程序目录..[[\自动充值\]]..n..[[\]]..内容.卡号..[[.txt]])
            if c then
                local x = 分割文本(n, "@")

               -- if x[1]=="月卡" then
                  -- if 玩家数据[id].角色.数据.月卡.到期时间 <=os.time() then
                  --   玩家数据[id].角色.数据.月卡.到期时间 = os.time()+2592000
                  -- else
                  --   玩家数据[id].角色.数据.月卡.到期时间 = 玩家数据[id].角色.数据.月卡.到期时间 + 2592000
                  -- end
                  -- 玩家数据[id].角色.数据.月卡.购买时间=os.time()
                  -- 玩家数据[id].角色.数据.月卡.开通=true
                  -- 添加仙玉(x[2]+0,id,"卡号充值")
                  -- 玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 20000000
                  -- 常规提示(id,"#Y/你获得了#R/20000000#Y/银子")
                  -- 玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 30000000
                  -- 常规提示(id,"#Y/你获得了#R/30000000#Y/储备")
                  -- 玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 100000000
                  -- 常规提示(id,"#Y/你获得了#R/100000000#Y/经验")
                  -- 玩家数据[id].道具:给予道具(id,"鬼谷子",1)
                  -- 常规提示(id,"#Y/你获得了#R/鬼谷子#Y/1个")
                  -- 玩家数据[id].道具:给予道具(id,"金银锦盒",99)
                  -- 常规提示(id,"#Y/你获得了#R/金银锦盒#Y/99个")
                  -- 玩家数据[id].道具:给予道具(id,"九转金丹",10,400)
                  -- 常规提示(id,"#Y/你获得了#R/九转金丹#Y/10个")

                if x[1]=="会员卡" then
                      玩家数据[id].道具:给予道具(id,服务端参数.名称.."会员卡",1)
                      常规提示(id,"#Y/你获得了#R/"..服务端参数.名称.."会员卡#Y/1张")
                elseif x[1]=="天卡" then
                      玩家数据[id].道具:给予道具(id,"限时会员卡",1)
                      常规提示(id,"#Y/你获得了#R/限时会员卡#Y/1张")
                elseif x[1]=="周卡" then
                       玩家数据[id].道具:给予道具(id,"白银会员卡",1)
                       常规提示(id,"#Y/你获得了#R/白银会员卡#Y/1张")
                elseif x[1]=="月卡" then
                       玩家数据[id].道具:给予道具(id,"黄金会员卡",1)
                       常规提示(id,"#Y/你获得了#R/黄金会员卡#Y/1张")
                elseif x[1]=="年卡" then
                       玩家数据[id].道具:给予道具(id,"钻石会员卡",1)
                       常规提示(id,"#Y/你获得了#R/钻石会员卡#Y/1张")
                elseif x[1]=="仙玉" then
                        共享货币[玩家数据[id].账号]:添加仙玉(x[2]+0,id,"卡号充值")
                elseif x[1]=="银子" then
                      玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子+ x[2]
                      常规提示(id,"#Y/你获得了#R/"..x[2].."#Y/银子")
                elseif x[1]=="抽奖" then
                      玩家数据[id].角色.数据.抽奖 = 玩家数据[id].角色.数据.抽奖+ x[2]
                      常规提示(id,"#Y/你获得了#R/"..x[2].."#Y/次抽奖")
                elseif x[1]=="裁缝技巧" then
                        玩家数据[id].角色.数据.打造熟练度.裁缝技巧 = 玩家数据[id].角色.数据.打造熟练度.裁缝技巧 + x[2]
                        常规提示(id,"你的#R/裁缝技巧#Y/熟练度增加了#R/"..x[2].."#Y/点")
                elseif x[1]=="打造技巧" then
                       玩家数据[id].角色.数据.打造熟练度.打造技巧 = 玩家数据[id].角色.数据.打造熟练度.打造技巧 + x[2]
                       常规提示(id,"你的#R/打造技巧#Y/熟练度增加了#R/"..x[2].."#Y/点")
                elseif x[1]=="淬灵之术" then
                        玩家数据[id].角色.数据.打造熟练度.淬灵之术 = 玩家数据[id].角色.数据.打造熟练度.淬灵之术 + x[2]
                        常规提示(id,"你的#R/淬灵之术#Y/熟练度增加了#R/"..x[2].."#Y/点")
                elseif x[1]=="炼金术" then
                        玩家数据[id].角色.数据.打造熟练度.炼金术 = 玩家数据[id].角色.数据.打造熟练度.炼金术 + x[2]
                        常规提示(id,"你的#R/炼金术#Y/熟练度增加了#R/"..x[2].."#Y/点")
                elseif x[1]=="抓鬼" then
                        玩家数据[id].角色.数据.自动抓鬼 = 玩家数据[id].角色.数据.自动抓鬼+ x[2]
                        常规提示(id,"#Y/你获得了#R/"..x[2].."#Y/次自动抓鬼")
                elseif x[1]=="新人礼包" then
                       if 自定义数据.新人礼包 and 自定义数据.新人礼包[x[2]+0] then
                            local 礼包数据 = 自定义数据.新人礼包[x[2]+0]
                            if 礼包数据.经验 and 礼包数据.经验>0 then
                                玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 礼包数据.经验
                                常规提示(id,"#Y/你获得了#R/"..礼包数据.经验.."#Y/点经验")
                              end
                              if 礼包数据.储备 and  礼包数据.储备>0 then
                                玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 礼包数据.储备
                                常规提示(id,"#Y/你获得了#R/"..礼包数据.储备.."#Y/两储备")
                              end
                              if 礼包数据.银子 and 礼包数据.银子>0 then
                                玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 礼包数据.银子
                                常规提示(id,"#Y/你获得了#R/"..礼包数据.银子.."#Y/两银子")
                              end
                              if 礼包数据.仙玉 and 礼包数据.仙玉>0 then
                                 共享货币[玩家数据[id].账号]:添加仙玉(礼包数据.仙玉,id,"新人礼包")
                              end
                              if 礼包数据.点卡 and 礼包数据.点卡>0 then
                                 共享货币[玩家数据[id].账号]:添加点卡(礼包数据.点卡,id,"新人礼包")
                              end
                              if 礼包数据.召唤兽 and 礼包数据.类型 and 礼包数据.召唤兽~="不给" and #玩家数据[id].召唤兽.数据<7 then
                                 玩家数据[id].召唤兽:添加召唤兽(礼包数据.召唤兽,礼包数据.召唤兽,礼包数据.类型)
                                 常规提示(id,"#Y/你获得了一只#R/"..礼包数据.召唤兽)
                              end
                              if 礼包数据.物品 then
                                 for k,v in pairs(礼包数据.物品) do
                                      仙玉商城类:仙玉商城商品处理(id,v.名称,v.数量)
                                      常规提示(id,"#Y/你获得了#R/"..v.数量.."#Y/个#R/"..v.名称)
                                 end
                              end
                       end
                -- elseif string.find(x[1],"礼包") and x[1]~="新人礼包" then
                --           玩家数据[id].道具:给予道具(id,x[1],1)
                --           常规提示(id,"#Y/你获得了#R/"..x[1])
                elseif x[1]=="首充" then
                        if 自定义数据.首充数据~=nil then
                             if 自定义数据.首充数据.经验>0 then
                                玩家数据[id].角色.数据.当前经验 = 玩家数据[id].角色.数据.当前经验 + 自定义数据.首充数据.经验
                                常规提示(id,"#Y/你获得了#R/"..自定义数据.首充数据.经验.."#Y/点经验")
                              end
                              if 自定义数据.首充数据.储备>0 then
                                玩家数据[id].角色.数据.储备 = 玩家数据[id].角色.数据.储备 + 自定义数据.首充数据.储备
                                常规提示(id,"#Y/你获得了#R/"..自定义数据.首充数据.储备.."#Y/两储备")
                              end
                              if 自定义数据.首充数据.银子>0 then
                                玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子 + 自定义数据.首充数据.银子
                                常规提示(id,"#Y/你获得了#R/"..自定义数据.首充数据.银子.."#Y/两银子")
                              end
                              if 自定义数据.首充数据.仙玉>0 then
                                 共享货币[玩家数据[id].账号]:添加仙玉(自定义数据.首充数据.仙玉,id,"首充礼包")
                              end
                              if 自定义数据.首充数据.点卡>0 then
                                 共享货币[玩家数据[id].账号]:添加点卡(自定义数据.首充数据.点卡,id,"首充礼包")
                              end
                              if 自定义数据.首充数据.召唤兽~="不给" and #玩家数据[id].召唤兽.数据<7 then
                                 玩家数据[id].召唤兽:添加召唤兽(自定义数据.首充数据.召唤兽,自定义数据.首充数据.召唤兽,自定义数据.首充数据.类型)
                                 常规提示(id,"#Y/你获得了一只#R/"..自定义数据.首充数据.召唤兽)
                              end
                              if 自定义数据.首充数据.物品数量>0 then
                                  for i=1,自定义数据.首充数据.物品数量 do
                                      local 物品名称 = 自定义数据.首充数据.物品[i].名称
                                      local 数量 = tonumber(自定义数据.首充数据.物品[i].数量)
                                      仙玉商城类:仙玉商城商品处理(id,物品名称,数量)
                                      常规提示(id,"#Y/你获得了#R/"..数量.."#Y/个#R/"..物品名称)
                                  end
                              end
                        end

                elseif x[1]=="点卡" then
                      共享货币[玩家数据[id].账号]:添加点卡(x[2],id,"cdk充值")
                else
                    仙玉商城类:仙玉商城商品处理(id,x[1],x[2])
                    常规提示(id,"#Y/你获得了#R/"..x[2].."#Y/个"..x[1])
                end
                if x[3]~=nil then
                    共享货币[玩家数据[id].账号]:添加累充(x[3],id,"cdk充值")          ----------------累充开关
                end
                已充值 = false
                常规提示(id,"#Y/你充值的#R/"..x[1].."#Y/已到账，如数据显示不准确请关闭界面重新打开")
                local 添加语句=format("玩家:%s,ID:%s,账号%s充值了%s元的%s,卡号%s",玩家数据[id].角色.数据.名称,id,玩家数据[id].账号,x[3],x[1],内容.卡号)
                添加充值日志(添加语句)
                共享货币[玩家数据[id].账号]:充值记录(添加语句)
                return
            end
          end
        end
      end

        if 已充值 then
           常规提示(id,"CDK无效或已经被兑换")
           return
        end

  end


发送数据(玩家数据[id].连接id,88,{累计充值=共享货币[玩家数据[id].账号].充值累计,首充数据=自定义数据.首充数据,累充数据=自定义数据.累充数据,已领充值=充值领取})
充值领取=nil
end





function 系统处理类:领取世界挑战奖励(id)
          if 世界挑战.最终一击~=nil and 世界挑战.最终一击==id then
               local 获得物品={}
                for i=1,#自定义数据.世界挑战.最终一击 do
                  if 取随机数()<=自定义数据.世界挑战.最终一击[i].概率 then
                     获得物品[#获得物品+1]=自定义数据.世界挑战.最终一击[i]
                  end
                end
                获得物品=删除重复(获得物品)
                if 获得物品~=nil then
                    local 取编号=取随机数(1,#获得物品)
                    if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                        玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                        广播消息({内容=format("#S/(世界挑战)#R/%s#Y/世界BOSS挑战活动触发最终一击，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                    end
                end
               世界挑战.最终一击 = nil
          end
          if #世界挑战.奖励 <= 0  then
             常规提示(id,"#Y当前没有符合要求的奖励可以领取")
             return
          end
         for i=1,3 do
             if 世界挑战.奖励[i] ~= nil and 世界挑战.奖励[i].id == id then
                 if 世界挑战.奖励[i].领取 ~= nil then
                    常规提示(id,"#Y你已经领取过该奖励了")
                    return
                 end
                  世界挑战.奖励[i].领取 = 1
                  if i == 1 then
                        玩家数据[id].角色:添加银子(50000000,"世界挑战",1)
                        --玩家数据[id].道具:给予道具(id,"特效点化石",1)
                      --  常规提示(id,"#Y你获得了一个特效点化石")
                        local 获得物品={}
                          for i=1,#自定义数据.世界挑战.第一名 do
                            if 取随机数()<=自定义数据.世界挑战.第一名[i].概率 then
                               获得物品[#获得物品+1]=自定义数据.世界挑战.第一名[i]
                            end
                          end
                          获得物品=删除重复(获得物品)
                          if 获得物品~=nil then
                              local 取编号=取随机数(1,#获得物品)
                              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                  玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                  广播消息({内容=format("#S/(世界挑战)#R/%s#Y/世界BOSS挑战活动获得了第一名，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                              end
                          end



                       -- 广播消息({内容=format("#S(世界BOOS)#R/%s#Y所在队伍完成了#G%s#Y任务，共计对世界BOOS造成伤害第一名,获得了一个特效点化石和5千万银子。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"司法天神"),频道="xt"})
                        return
                  elseif i == 2 then
                         玩家数据[id].角色:添加银子(30000000,"世界挑战",1)
                        --玩家数据[id].道具:给予道具(id,"灵饰点化石",1)
                      --  常规提示(id,"#Y你获得了一个灵饰点化石")
                         local 获得物品={}
                          for i=1,#自定义数据.世界挑战.第二名 do
                            if 取随机数()<=自定义数据.世界挑战.第二名[i].概率 then
                               获得物品[#获得物品+1]=自定义数据.世界挑战.第二名[i]
                            end
                          end
                          获得物品=删除重复(获得物品)
                          if 获得物品~=nil then
                              local 取编号=取随机数(1,#获得物品)
                              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                  玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                  广播消息({内容=format("#S/(世界挑战)#R/%s#Y/世界BOSS挑战活动获得了第二名，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                              end
                          end






                        --广播消息({内容=format("#S(世界BOOS)#R/%s#Y所在队伍完成了#G%s#Y任务，共计对世界BOOS造成20万以上伤害,获得了一个灵饰点化石和3千万银子。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"司法天神"),频道="xt"})
                        return

                  elseif i == 3 then
                          玩家数据[id].角色:添加银子(10000000,"世界挑战",1)
                         -- 玩家数据[id].道具:给予道具(id,"特技点化石",1)
                           local 获得物品={}
                          for i=1,#自定义数据.世界挑战.第三名 do
                            if 取随机数()<=自定义数据.世界挑战.第三名[i].概率 then
                               获得物品[#获得物品+1]=自定义数据.世界挑战.第三名[i]
                            end
                          end
                          获得物品=删除重复(获得物品)
                          if 获得物品~=nil then
                              local 取编号=取随机数(1,#获得物品)
                              if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                  玩家数据[id].道具:自定义给予道具(id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                  广播消息({内容=format("#S/(世界挑战)#R/%s#Y/世界BOSS挑战活动获得了第三名，因此获得了其奖励的#G/%s#Y/".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                              end
                          end
                          -- 常规提示(id,"#Y你获得了一个特技点化石")
                          -- 广播消息({内容=format("#S(世界BOOS)#R/%s#Y所在队伍完成了#G%s#Y任务，共计对世界BOOS造成20万以上伤害,获得了一个特技点化石和1千万银子。".."#"..取随机数(1,110),玩家数据[id].角色.数据.名称,"司法天神"),频道="xt"})
                        return

                  end
            end
         end
    常规提示(id,"#Y你当前没有符合要求的奖励可以领取")

end


function 系统处理类:获取世界BOSS(id)
    local BOOS排行数据 = {}
    local 临时发送数据 = {}
    for i,v in pairs(世界挑战) do
      if i ~= "开启" and i ~= "气血" and i ~= "最终一击" and i ~= "奖励" then
        BOOS排行数据[#BOOS排行数据+1] = 世界挑战[i]
      end
    end
    if #BOOS排行数据 > 0 then
        table.sort(BOOS排行数据,function(a,b) return a.伤害>b.伤害 end )
    end
    for i=1,8 do
      if BOOS排行数据[i] ~= nil and BOOS排行数据[i].伤害 ~= 0 then
       临时发送数据[#临时发送数据+1] = BOOS排行数据[i]
      end
    end
    发送数据(玩家数据[id].连接id,134,{开启=世界挑战.开启,排行=临时发送数据,气血=世界挑战.气血})
  end

 function 系统处理类:开启世界BOSS挑战(id)
      if 玩家数据[id].队伍 == 0 or not 玩家数据[id].队长 then
        常规提示(id,"#Y该活动要求组队并且队长进行挑战操作")
        return
      elseif 世界挑战.开启 == false then
        常规提示(id,"#Y活动已经结束")
        return
      else
        -- for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
        --   local 临时id = 队伍数据[玩家数据[id].队伍].成员数据[i]
        --   if 世界挑战[临时id] ~= nil then
        --     常规提示(id,"#Y"..玩家数据[临时id].角色.数据.名称.."已经完成过该任务,请等待刷新后再次参与")
        --     return
        --   end
        -- end
        for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
          local 临时id = 队伍数据[玩家数据[id].队伍].成员数据[i]
          世界挑战[临时id] = {伤害=0,id=临时id,名称=玩家数据[临时id].角色.数据.名称,等级=玩家数据[临时id].角色.数据.等级,门派=玩家数据[临时id].角色.数据.门派}
        end
        战斗准备类:创建战斗(id,100308,0)
      end
end


function 系统处理类:获取商店变异召唤兽(id)
  if 变异商店bb[id]==nil then
    self:刷新商店变异召唤兽(id)
    变异商店bb[id].刷新=0
  end
 发送数据(玩家数据[id].连接id,130.1,变异商店bb[id])
end

function 系统处理类:刷新商店变异召唤兽(id)
  变异商店bb[id]={}
  for n=1,12 do
    local 模型=取随机怪(0,175)
    变异商店bb[id][n]={模型=模型[2],价格=取随机数(200000,1000000)}
  end
end

function 系统处理类:获取商店召唤兽(id)
  if 商店bb[id]==nil then
    self:刷新商店召唤兽(id)
    商店bb[id].刷新=0
  end
 发送数据(玩家数据[id].连接id,130,商店bb[id])
end

function 系统处理类:刷新商店召唤兽(id)
  商店bb[id]={}
  for n=1,12 do
    local 模型=取随机怪(0,175)
    商店bb[id][n]={模型=模型[2],价格=取随机数(50000,100000)}
  end
end

-- function 系统处理类:打开交易中心(id)
--   local 分类信息={}
--   table.insert(分类信息,{"100装备相关","110装备相关"})
--   table.insert(分类信息,{"低级魔兽要诀","高级魔兽要诀"})
--   发送数据(玩家数据[id].连接id,3910,分类信息)
-- end

-- function 系统处理类:刷新交易数据(id,内容)
--   local 大类=内容.大类 + 0
--   local 小类=内容.小类
--   local 商品列表,小类序号=获取交易中心列表(大类,小类)
--   发送数据(玩家数据[id].连接id,3911,商品列表)
-- end

-- function 系统处理类:购买交易中心商品(id,内容)
--   local 大类=内容.大类 + 0
--   local 小类=内容.小类
--   local 编号=内容.编号 + 0
--   local 商品列表,小类序号=获取交易中心列表(大类,小类)
--   local 选中商品 = 商品列表[编号]
--   if 选中商品.日涨跌幅 >= 10.0 then
--     发送数据(玩家数据[id].连接id, 7, "#y/该商品已经涨停了，无法购买")
--     return
--   end
--   if 玩家数据[id].角色.数据.银子<选中商品.价格 then
--     常规提示(id,"#Y/你没有那么多的银子")
--     return
--   end
--   local 临时格子=玩家数据[id].角色:取道具格子()
--   if 临时格子==0 then
--     常规提示(id,"#Y/包裹已满，请先整理下包裹吧！")
--     return
--   end
--   玩家数据[id].角色:扣除银子(选中商品.价格,"交易中心购买",1)
--   if 选中商品.名称 == "魔兽要诀" or 选中商品.名称 == "高级魔兽要诀" then
--     玩家数据[id].道具:给予道具(id,选中商品.名称,nil,选中商品.参数一)
--   elseif 选中商品.名称 == "制造指南书" then
--     玩家数据[id].道具:给予道具(id,选中商品.名称,选中商品.参数一+0,选中商品.参数二+0)
--   elseif 选中商品.名称 == "百炼精铁" then
--     玩家数据[id].道具:给予道具(id,选中商品.名称,选中商品.参数一+0)
--   end
--   玩家数据[id].角色:日志记录(format("[交易中心-购买]购买道具[%s][%s]，花费%s两银子：[%s][%s][%s]",选中商品.名称,选中商品.参数一,选中商品.价格,玩家数据[id].账号,id,玩家数据[id].角色.名称))
--   常规提示(id,"#Y/你花费了#R"..选中商品.价格.."#Y两，成功购买了#R"..选中商品.名称.."！")
--   交易中心[大类][小类序号].内容[编号].价格 = math.floor(交易中心[大类][小类序号].内容[编号].价格 / (100+交易中心[大类][小类序号].内容[编号].日涨跌幅) * (100+交易中心[大类][小类序号].内容[编号].日涨跌幅 + 0.25))
--   交易中心[大类][小类序号].内容[编号].日涨跌幅 = 交易中心[大类][小类序号].内容[编号].日涨跌幅 + 0.25
--   发送数据(玩家数据[id].连接id,3911,商品列表)
-- end

-- function 系统处理类:出售交易中心商品(id,内容)
--   local 大类=内容.大类 + 0
--   local 小类=内容.小类
--   local 编号=内容.编号 + 0
--   local 商品列表,小类序号=获取交易中心列表(大类,小类)
--   local 选中商品 = 商品列表[编号]
--   if 选中商品.日涨跌幅 <= -10.0 then
--     发送数据(玩家数据[id].连接id, 7, "#y/该商品已经跌停了，无法出售")
--     return
--   end
--   local 扣除成功=玩家数据[id].道具:扣除道具数量(id,选中商品.名称,1,选中商品.参数一类型,选中商品.参数一,选中商品.参数二类型,选中商品.参数二)
--   if 扣除成功 then
--     local 出售价格=math.floor(选中商品.价格*0.8)
--     常规提示(id,"#Y出售成功，你获得了#R"..出售价格.."#Y两银子")
--     玩家数据[id].角色:添加银子(出售价格,"交易中心出售",1)
--     交易中心[大类][小类序号].内容[编号].价格 = math.floor(交易中心[大类][小类序号].内容[编号].价格 / (100+交易中心[大类][小类序号].内容[编号].日涨跌幅) * (100+交易中心[大类][小类序号].内容[编号].日涨跌幅 - 0.25))
--     交易中心[大类][小类序号].内容[编号].日涨跌幅 = 交易中心[大类][小类序号].内容[编号].日涨跌幅 - 0.25
--     刷新货币(玩家数据[id].连接id,id)
--   else
--     常规提示(id,"出售失败，你没有这个道具")
--   end
--   发送数据(玩家数据[id].连接id,3911,商品列表)
-- end




function 系统处理类:更新消息通知(id)
  if 玩家数据[id]~=nil and #玩家数据[id].好友.数据.留言信息>0  then
      发送数据(玩家数据[id].连接id,56,"1")
  end
  if 玩家数据[id] then
      if 玩家数据[id].摊位数据 then
          玩家数据[id].摊位数据=nil
          地图处理类:取消玩家摊位(id)
      end
      if 玩家数据[id].角色.数据.离线摆摊 then
          玩家数据[id].角色.数据.离线摆摊 =nil
          地图处理类:取消离线摆摊(id)
      end
  end
end


function 系统处理类:断开游戏(数字id,id)
       if 玩家数据[数字id] == nil then
        return
      end

      if id~=nil and id ~= 玩家数据[数字id].连接id then
         return
      end
      if 玩家数据[数字id].摊位数据~=nil then
            if 共享仓库[玩家数据[数字id].账号] then
              共享仓库[玩家数据[数字id].账号]:加入玩家(数字id,true)
            end
            if 共享货币[玩家数据[数字id].账号] then
                共享货币[玩家数据[数字id].账号]:加入玩家(数字id,true)
            end
            玩家数据[数字id].角色.数据.离线摆摊 =true
            地图处理类:设置离线摆摊(数字id)

          return
      end
      if 玩家数据[数字id].管理~=nil then
          玩家数据[数字id] = nil
          return
      end
    if 玩家数据[数字id] and 玩家数据[数字id].战斗 and 玩家数据[数字id].战斗~=0 then
        if 战斗准备类.战斗盒子[玩家数据[数字id].战斗] then
           if not 玩家数据[数字id].观战 then
                战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置断线玩家(数字id)
                return
            else
                 战斗准备类.战斗盒子[玩家数据[数字id].战斗]:删除观战玩家(数字id)
            end
        end
    end
  --if 玩家数据[数字id]~=nil  then
      if 玩家数据[数字id].交易信息~=nil then
        玩家数据[数字id].道具:取消交易(数字id)
      end
      -- if 玩家数据[数字id].队伍~=0 then
      --   队伍处理类:退出队伍(数字id)
      -- end
      if 玩家数据[数字id].队伍~=0 then
          if not 判断是否为空表(剑会天下.三人) then
              for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[数字id].队伍].成员数据[i],"#Y/因为队伍玩家离开取消了匹配！")
                  end
              end
              for n,v in pairs(剑会天下.三人) do
                  if v.id == 玩家数据[数字id].队伍 then
                    table.remove(会天下.三人,n)
                    break
                  end
              end
          end
          if not 判断是否为空表(剑会天下.五人) then
              for i=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                  if 玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]]~=nil then
                    发送数据(玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[i]].连接id,128)
                    常规提示(队伍数据[玩家数据[数字id].队伍].成员数据[i],"#Y/因为队伍玩家离开取消了匹配！")
                  end
              end
              for n,v in pairs(剑会天下.五人) do
                  if v.id == 玩家数据[数字id].队伍 then
                      table.remove(剑会天下.五人,n)
                      break
                  end
              end
          end
          队伍处理类:退出队伍(数字id)
      else
          if not 判断是否为空表(剑会天下.单人) then
              for n,v in pairs(剑会天下.单人) do
                  if v.id == 数字id then
                      table.remove(剑会天下.单人,n)
                      break
                  end
              end
          end
      end
      if  玩家数据[数字id] then
          if 玩家数据[数字id].角色.数据.帮派数据 and 玩家数据[数字id].角色.数据.帮派数据.编号 and 玩家数据[数字id].角色.数据.帮派数据.编号>0 then
            local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
            帮派数据[帮派编号].成员数据[数字id].在线 = false
            帮派数据[帮派编号].成员数据[数字id].离线时间 = os.time()
          end
          玩家数据[数字id].角色.数据.离线时间=os.time()
          玩家数据[数字id].角色:存档()
          if 共享仓库[玩家数据[数字id].账号] then
             共享仓库[玩家数据[数字id].账号]:加入玩家(数字id,true)
          end
          if 共享货币[玩家数据[数字id].账号] then
              共享货币[玩家数据[数字id].账号]:加入玩家(数字id,true)
          end
          -- if 玩家数据[数字id].账号 == nil then
          --     玩家数据[数字id].账号 = 玩家数据[数字id].角色.数据.账号
          --     账号记录[玩家数据[数字id].账号..数字id]=nil
          -- else
          --   账号记录[玩家数据[数字id].账号..数字id]=nil --退出游戏
          -- end
          地图处理类:移除玩家(数字id)
          玩家数据[数字id]=nil
      end
      collectgarbage("collect")
 -- end
end



function 系统处理类:取角色选择id(id,账号)
  if 账号==nil or 账号=="" or id==nil or id==0 then
    return
  end
  local 临时id = 0
  if f函数.文件是否存在([[data/]]..账号..[[/信息.txt]])==false then
    return
  else
    local 临时文件=读入文件([[data/]]..账号..[[/信息.txt]])
    local 写入信息=table.loadstring(临时文件)
    if 写入信息[id+0]~=nil then
      if f函数.文件是否存在([[data/]]..账号..[[/]]..写入信息[id+0]..[[/角色.txt]])==false then
        -- 发送数据(id,7,"#Y/账号并不存在")
        return
      else
        local 读取文件=读入文件([[data/]]..账号..[[/]]..写入信息[id+0]..[[/角色.txt]])
        local 还原数据=table.loadstring(读取文件)
        if not 还原数据 then return 0 end
        临时id = 还原数据.数字id
      end
    end
  end
  return 临时id
end



function 系统处理类:进入游戏(id,账号,数字id,ip,序号)
    if not 账号 or  账号=="" or not 数字id or not tonumber(数字id) or 数字id+0==0 then return end
    数字id=数字id+0
    if 共享仓库[账号]==nil then
        共享仓库[账号]=共享仓库类:创建()
        共享仓库[账号]:加载数据(账号)
    end
    if 共享货币[账号]==nil then
        共享货币[账号]=共享货币类:创建()
        共享货币[账号]:加载数据(账号)
    end


    if 玩家数据[数字id] ~= nil and 玩家数据[数字id].角色 ~= nil then
          -- if 玩家数据[数字id].子角色操作  and 玩家数据[数字id].战斗 and 玩家数据[数字id].战斗~=0 and 战斗准备类.战斗盒子[玩家数据[数字id].战斗] then
          --   return
          -- end
          if 玩家数据[数字id].交易信息~=nil then
            玩家数据[数字id].道具:取消交易(数字id)
          end
          发送数据(玩家数据[数字id].连接id,998,"您的账号已经在其他地方登陆,如不是您本人,请及时修改密码")
          玩家数据[数字id].子角色操作 = nil
          玩家数据[数字id].连接id=id
          玩家数据[数字id].账号=账号
          玩家数据[数字id].房屋:加载数据(账号,数字id)
          玩家数据[数字id].好友:加载数据(账号,数字id)
          --玩家数据[数字id].每日活动:加载数据(账号,数字id)
          共享仓库[账号]:加入玩家(数字id)
          共享货币[账号]:加入玩家(数字id)
          玩家数据[数字id].物品锁时间={时间=os.time(),开关=false}

          if 序号 == nil or 序号==4 then
            发送数据(id,5,玩家数据[数字id].角色:取总数据())
            发送数据(id,16,玩家数据[数字id].召唤兽.数据)
            玩家数据[数字id].角色:刷新任务跟踪()
          end
          发送数据(id,-2,数字id)
          发送数据(id,43,{时辰=时辰信息.当前})
          发送数据(id,42,玩家数据[数字id].角色.数据.快捷技能)
          if 玩家数据[数字id].角色.数据.飞行 then
            发送数据(id,71)
          end

          if 玩家数据[数字id].角色.数据.多角色操作 then
             发送数据(玩家数据[数字id].连接id,113)
          else
             发送数据(玩家数据[数字id].连接id,114)
          end
          发送数据(玩家数据[数字id].连接id,86,{发送=战斗序号.收到,收到=战斗序号.发送})



          if (玩家数据[数字id].角色.数据.地图数据.编号>=1340 and 玩家数据[数字id].角色.数据.地图数据.编号<=1342) or 玩家数据[数字id].角色.数据.地图数据.编号==1332 then
             地图处理类:跳转地图(数字id,1001,514,154) -- 跳回长安重进
          else
             地图处理类:重连加入(数字id,玩家数据[数字id].角色.数据.地图数据.编号,玩家数据[数字id].角色.数据.地图数据.x,玩家数据[数字id].角色.数据.地图数据.y)
          end
          self:更新消息通知(数字id)
          -- if 玩家数据[数字id].战斗 ~= 0 and 战斗准备类.战斗盒子[玩家数据[数字id].战斗]==nil then
          --     玩家数据[数字id].战斗=0
          --     发送数据(玩家数据[数字id].连接id,5505)
          --     玩家数据[数字id].遇怪时间=os.time()+取随机数(5,10)
          --     玩家数据[数字id].道具:重置法宝回合(数字id)
          --     玩家数据[数字id].角色.数据.战斗开关=nil
          -- elseif 玩家数据[数字id].战斗 ~= 0 and 战斗准备类.战斗盒子[玩家数据[数字id].战斗]~=nil and 战斗准备类.战斗盒子[玩家数据[数字id].战斗].参战玩家==nil then
          --     玩家数据[数字id].战斗=0
          --     发送数据(玩家数据[数字id].连接id,5505)
          --     玩家数据[数字id].遇怪时间=os.time()+取随机数(5,10)
          --     玩家数据[数字id].道具:重置法宝回合(数字id)
          --     玩家数据[数字id].角色.数据.战斗开关=nil
          -- end
          if 玩家数据[数字id].战斗 and 玩家数据[数字id].战斗~=0 then
             if 玩家数据[数字id].队伍~=0 then
                  队伍处理类:索取队伍信息(数字id,4004)
                  if 玩家数据[数字id].队长 then
                      发送数据(玩家数据[数字id].连接id,4006)
                  end
              end
              if 战斗准备类.战斗盒子[玩家数据[数字id].战斗] then
                    战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置断线重连(数字id)
                  -- if 序号 == nil or 序号==4 then
                  --     战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置重连玩家(数字id)
                  -- else
                  --     战斗准备类.战斗盒子[玩家数据[数字id].战斗]:设置断线重连(数字id)
                  -- end
              else
                    玩家数据[数字id].战斗=0
                    发送数据(玩家数据[数字id].连接id,5505)
                    玩家数据[数字id].遇怪时间=os.time()+取随机数(5,10)
                    玩家数据[数字id].道具:重置法宝回合(数字id)
                    玩家数据[数字id].角色.数据.战斗开关=nil
              end
          else
              if 玩家数据[数字id].队伍~=0 then
                  队伍处理类:退出队伍(数字id)
              end
              玩家数据[数字id].角色.数据.战斗开关=nil

          end
          发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
          if 玩家数据[数字id].自动抓鬼 then
              常规提示(数字id,"#Y你已开启自动遇怪")
          end



          -- if 玩家数据[数字id]~=nil then
          --     玩家数据[数字id].角色.数据.战斗开关=nil
          --     self:进入事件(数字id,玩家数据[数字id].连接id)
          -- end
    elseif not 玩家数据[数字id] or not 玩家数据[数字id].角色 then
        -- if 账号记录 == nil then
        --   return
        -- end
        -- 账号记录[账号..数字id]=数字id
        玩家数据[数字id]={连接id=id}
        玩家数据[数字id].角色=角色处理类:创建(id)
        玩家数据[数字id].道具=道具处理类:创建(id)
        玩家数据[数字id].装备=装备处理类:创建()
        玩家数据[数字id].经脉=经脉处理类:创建()
        玩家数据[数字id].召唤兽=召唤兽处理类:创建()
        --玩家数据[数字id].每日活动=每日活动类:创建()
        玩家数据[数字id].道具仓库 = 道具仓库类:创建()
        玩家数据[数字id].召唤兽仓库 = 召唤兽仓库类:创建()
        玩家数据[数字id].加锁处理=物品加锁处理类:创建()
        玩家数据[数字id].神器=神器类:创建()            ------神器
        玩家数据[数字id].房屋=房屋处理类:创建()
        玩家数据[数字id].好友=好友处理类:创建()
        玩家数据[数字id].角色:加载数据(账号,数字id)

        -- 玩家数据[数字id].孩子=孩子处理类:创建(数字id)
        -- 玩家数据[数字id].孩子:加载数据(账号,数字id)
        玩家数据[数字id].道具:加载数据(账号,数字id)
        玩家数据[数字id].召唤兽:加载数据(账号,数字id)
        玩家数据[数字id].经脉:加载数据(数字id)
       -- 玩家数据[数字id].每日活动:加载数据(账号,数字id)
        玩家数据[数字id].召唤兽仓库:加载数据(账号,数字id)
        玩家数据[数字id].道具仓库:加载数据(账号,数字id)
        玩家数据[数字id].加锁处理:加载数据(账号,数字id)
        玩家数据[数字id].神器:加载数据(账号,数字id)         ------神器
        玩家数据[数字id].房屋:加载数据(账号,数字id)
        玩家数据[数字id].好友:加载数据(账号,数字id)
        玩家数据[数字id].账号=账号
        玩家数据[数字id].队伍=0
        玩家数据[数字id].当前频道=os.time()
        玩家数据[数字id].世界频道=os.time()
        玩家数据[数字id].传闻频道=os.time()
        玩家数据[数字id].遇怪时间=os.time()+取随机数(10,20)
        玩家数据[数字id].战斗=0
        玩家数据[数字id].ip=ip
        玩家数据[数字id].连接id=id
        玩家数据[数字id].商品列表=0
        玩家数据[数字id].移动数据={}
        玩家数据[数字id].最后事件=""
        玩家数据[数字id].道具操作={}
        玩家数据[数字id].子角色操作 = nil
        玩家数据[数字id].物品锁时间={时间=os.time(),开关=false}
        f函数.写配置(程序目录..[[data\]]..账号..[[\账号信息.txt]],"账号配置","创建ip",ip)
        共享仓库[账号]:加入玩家(数字id)
        共享货币[账号]:加入玩家(数字id)
        self:进入游戏地图处理(数字id)
      --发送角色数据
        发送数据(id,5,玩家数据[数字id].角色:取总数据())
        发送数据(id,5.1,{文本=自定义数据.自定义公告内容})
        self:进入游戏检测(数字id)
        发送数据(id,16,玩家数据[数字id].召唤兽.数据)
  --      发送数据(id,17.2,玩家数据[数字id].孩子:取数据())
        发送数据(id,1,{id=id,用户="正式用户"})
        发送数据(id,43,{时辰=时辰信息.当前})
        发送数据(id,-2,数字id)
        发送数据(id,42,玩家数据[数字id].角色.数据.快捷技能)
        if 玩家数据[数字id].角色.数据.飞行 then
            发送数据(id,71)
        end
        if 玩家数据[数字id].角色.数据.多角色操作 then
          发送数据(玩家数据[数字id].连接id,113)
        else
           发送数据(玩家数据[数字id].连接id,114)
        end
        发送数据(玩家数据[数字id].连接id,86,{发送=战斗序号.收到,收到=战斗序号.发送})

        if (玩家数据[数字id].角色.数据.地图数据.编号>=1340 and 玩家数据[数字id].角色.数据.地图数据.编号<=1342) or 玩家数据[数字id].角色.数据.地图数据.编号==1332 then
           地图处理类:跳转地图(数字id,1001,514,154)
        else
           地图处理类:加入玩家(数字id,玩家数据[数字id].角色.数据.地图数据.编号,玩家数据[数字id].角色.数据.地图数据.x,玩家数据[数字id].角色.数据.地图数据.y)
        end
        玩家数据[数字id].角色:刷新任务跟踪()
        玩家数据[数字id].角色:刷新信息()
        self:更新消息通知(数字id)
        if 玩家数据[数字id].角色.数据.强P开关 ~= nil then
          发送数据(id,94,{开关=true})
          地图处理类:更改强PK(数字id,true)
        elseif 玩家数据[数字id].角色.数据.PK开关 ~= nil then
          发送数据(id,93,{开关=true})
          地图处理类:更改PK(数字id,true)
        end



      --   if 帮派缴纳情况[玩家数据[数字id].角色.数据.帮派数据.编号]==nil then
      --     帮派缴纳情况[玩家数据[数字id].角色.数据.帮派数据.编号] = {}
      --     帮派缴纳情况[玩家数据[数字id].角色.数据.帮派数据.编号].缴费人数 = 0
      --     帮派缴纳情况[玩家数据[数字id].角色.数据.帮派数据.编号].缴费总金额 = 0
      --     帮派缴纳情况[玩家数据[数字id].角色.数据.帮派数据.编号].起始时间 = os.time()
      --   end
          self.找到节日=false
          for n=1,5 do
            self.当前时间=os.date("%m月%d日")
            self.节日时间=f函数.读配置(程序目录.."节日列表.ini","节日时间","节日时间"..n)
            self.节日名称=f函数.读配置(程序目录.."节日列表.ini","节日名称","节日名称"..n)
            if self.当前时间==self.节日时间 then
              常规提示(数字id,"今天是#R/"..self.节日名称.."#W/，请前往长安节日礼物使者#Y/（208，152）#W/处领取节日礼物，祝您天天有个好心情！#77")
              self.找到节日=true
              break
            end
          end
          if self.找到节日 then
              节日开关=true
          else
              节日开关=false
          end


          if 玩家数据[数字id].角色.数据.是否首席==nil or 玩家数据[数字id].角色.数据.是否首席==0 then
              玩家数据[数字id].角色.数据.是否首席=0
              local 是否删除 = false
              for i,v in ipairs(玩家数据[数字id].角色.数据.称谓) do
                  if string.find(v,"首席大弟子") then
                      是否删除=true
                  end
              end
              if 是否删除 then
                玩家数据[数字id].角色:批量删除称谓("首席大弟子")
              end
          end




 --if 玩家数据[数字id]~=nil then
--  local 对话=[[#S1.1更新重点#:等级上限提升至#R109(#Y开启新的梦幻剧情#)
-- 【1.等级突破任务在西凉女儿国王处】
-- 【2.所有活动产出已经更新,详情自己看梦幻指引里面说明】
-- 【3.新的日常剧情战斗在西凉周杰伦处开启(概率会出特殊兽决)】
-- 【4.#Y长安兰虎处#增加#G进阶变身卡兑换#】
-- 【5.#Y长安书店#可免费兑换#G110级装备武器鉴定符#了(打造费用不降低了)】
-- 【6.通天塔经验奖励提高】
-- 【7.迷宫产出提高会有100级装备和105级炼妖石】
-- 【8.宝宝基本全产出商会现在可以购买非变异0-95级宝宝,变异105-125级宝宝】
-- 【9.高级藏宝图产出宝宝窝135-155级满!】
-- 【10.孩子功能开放,在长寿婆婆那里用10万仙玉购买！】
--       ]]
-- --       比武大会活动震撼来袭,每周六周日19点开启比如入场19.10正式开打10分钟的分组组队时间
-- -- 比武活动中无论输赢都讲获得大量银子,仙玉奖励
-- -- 胜利的阵营方：90级武器书铁,60级灵饰书铁,高级藏宝图,5-8级宝石
-- -- 失败的阵营方：70-90级武器书铁,60级灵饰书铁,高级藏宝图,3-8级宝石

--         发送数据(玩家数据[数字id].连接id,1501,{名称=玩家数据[数字id].角色.数据.名称,模型=玩家数据[数字id].角色.数据.模型,对话=对话,选项=选项})
        刷新排行榜(数字id)
   --   end
      发送数据(玩家数据[数字id].连接id,5505)
      发送数据(玩家数据[数字id].连接id,5506,{玩家数据[数字id].角色:取气血数据()})
      if 玩家数据[数字id].自动抓鬼 then
              常规提示(数字id,"#Y你已开启自动遇怪")
      end
      if 玩家数据[数字id]~=nil then
          玩家数据[数字id].角色.数据.战斗开关=nil
          self:进入事件(数字id,玩家数据[数字id].连接id)

      end
    end




end

function 系统处理类:进入游戏地图处理(数字id)
   if 玩家数据[数字id].角色.数据.地图数据.编号>=1600 and 玩家数据[数字id].角色.数据.地图数据.编号<=1620 and 迷宫数据.开关==false then
          玩家数据[数字id].角色.数据.地图数据.编号=1001
          玩家数据[数字id].角色.数据.地图数据.x=365
          玩家数据[数字id].角色.数据.地图数据.y=165

        elseif 玩家数据[数字id].角色.数据.地图数据.编号==5001 then
          玩家数据[数字id].角色.数据.地图数据.编号=1226
          玩家数据[数字id].角色.数据.地图数据.x=115*20
          玩家数据[数字id].角色.数据.地图数据.y=15*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6021 and 玩家数据[数字id].角色.数据.地图数据.编号<=6023 and (玩家数据[数字id].角色:取任务(130) == nil or 玩家数据[数字id].角色:取任务(130) == 0 or 任务数据[玩家数据[数字id].角色:取任务(130)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1070
          玩家数据[数字id].角色.数据.地图数据.x=125*20
          玩家数据[数字id].角色.数据.地图数据.y=144*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6001 and 玩家数据[数字id].角色.数据.地图数据.编号<=6002 and (玩家数据[数字id].角色:取任务(120) == nil or 玩家数据[数字id].角色:取任务(120) == 0 or 任务数据[玩家数据[数字id].角色:取任务(120)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1001
          玩家数据[数字id].角色.数据.地图数据.x=287*20
          玩家数据[数字id].角色.数据.地图数据.y=85*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6024 and 玩家数据[数字id].角色.数据.地图数据.编号<=6026 and (玩家数据[数字id].角色:取任务(120) == nil or 玩家数据[数字id].角色:取任务(120) == 0 or 任务数据[玩家数据[数字id].角色:取任务(120)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1002
          玩家数据[数字id].角色.数据.地图数据.x=34*20
          玩家数据[数字id].角色.数据.地图数据.y=71*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6027 and 玩家数据[数字id].角色.数据.地图数据.编号<=6030 and (玩家数据[数字id].角色:取任务(160) == nil or 玩家数据[数字id].角色:取任务(160) == 0 or 任务数据[玩家数据[数字id].角色:取任务(160)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1070
          玩家数据[数字id].角色.数据.地图数据.x=53*20
          玩家数据[数字id].角色.数据.地图数据.y=70*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6031 and 玩家数据[数字id].角色.数据.地图数据.编号<=6035 and (玩家数据[数字id].角色:取任务(180) == nil or 玩家数据[数字id].角色:取任务(180) == 0 or 任务数据[玩家数据[数字id].角色:取任务(180)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1070
          玩家数据[数字id].角色.数据.地图数据.x=53*20
          玩家数据[数字id].角色.数据.地图数据.y=196*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6036 and 玩家数据[数字id].角色.数据.地图数据.编号<=6039 and (玩家数据[数字id].角色:取任务(191) == nil or 玩家数据[数字id].角色:取任务(191) == 0 or 任务数据[玩家数据[数字id].角色:取任务(191)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1001
          玩家数据[数字id].角色.数据.地图数据.x=365
          玩家数据[数字id].角色.数据.地图数据.y=165
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=6010 and 玩家数据[数字id].角色.数据.地图数据.编号<=6020 then
               玩家数据[数字id].角色.数据.地图数据.编号=1001
               玩家数据[数字id].角色.数据.地图数据.x=200*20
               玩家数据[数字id].角色.数据.地图数据.y=110*20
        elseif 玩家数据[数字id].角色.数据.地图数据.编号>=7001 and 玩家数据[数字id].角色.数据.地图数据.编号<=7004 and (玩家数据[数字id].角色:取任务(7001) == nil or 玩家数据[数字id].角色:取任务(7001) == 0 or 任务数据[玩家数据[数字id].角色:取任务(7001)]==nil) then
          玩家数据[数字id].角色.数据.地图数据.编号=1501
          玩家数据[数字id].角色.数据.地图数据.x=87*20
          玩家数据[数字id].角色.数据.地图数据.y=29*20
        end
        if 玩家数据[数字id].角色.数据.地图数据==nil or 玩家数据[数字id].角色.数据.地图数据.编号 >= 100000   then
          玩家数据[数字id].角色.数据.地图数据.编号=1001
          玩家数据[数字id].角色.数据.地图数据.x=484*20
          玩家数据[数字id].角色.数据.地图数据.y=140*20
        end
      if not 彩虹争霸.活动开关 then
          if 玩家数据[数字id].角色.数据.地图数据==nil or (玩家数据[数字id].角色.数据.地图数据.编号 >= 10000  and 玩家数据[数字id].角色.数据.地图数据.编号 <= 10018) then
            玩家数据[数字id].角色.数据.地图数据.编号=1001
            玩家数据[数字id].角色.数据.地图数据.x=191*20
            玩家数据[数字id].角色.数据.地图数据.y=104*20
            玩家数据[数字id].角色:删除称谓({"云影","虹光"})
          end
      end

      if not 长安保卫战.活动开关 then
          if 玩家数据[数字id].角色.数据.地图数据==nil or 玩家数据[数字id].角色.数据.地图数据.编号 == 5003 then
            玩家数据[数字id].角色.数据.地图数据.编号=1001
            玩家数据[数字id].角色.数据.地图数据.x=191*20
            玩家数据[数字id].角色.数据.地图数据.y=104*20
          end
      end
      if not 英雄大会.开关 then
          if 玩家数据[数字id].角色.数据.地图数据==nil or 玩家数据[数字id].角色.数据.地图数据.编号 == 6003 or  玩家数据[数字id].角色.数据.地图数据.编号 == 6004 then
            玩家数据[数字id].角色.数据.地图数据.编号=1001
            玩家数据[数字id].角色.数据.地图数据.x=191*20
            玩家数据[数字id].角色.数据.地图数据.y=104*20
          end
      end



      self:进入游戏数据表处理(数字id)
end
function 系统处理类:进入游戏数据表处理(数字id)
          if 玩家数据[数字id].角色.数据.帮派数据 == nil  or 玩家数据[数字id].角色.数据.帮派数据.编号==nil then
              玩家数据[数字id].角色.数据.帮派 = "无帮派"
              玩家数据[数字id].角色.数据.帮派数据 = {编号=0,权限=0}
          end
          if 玩家数据[数字id].角色.数据.帮派 ~= "无帮派" and (玩家数据[数字id].角色.数据.帮派数据.编号<=0 or 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号] == nil or 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号].成员数据[数字id] == nil) then
              玩家数据[数字id].角色.数据.帮派 = "无帮派"
              玩家数据[数字id].角色.数据.帮派数据 = {编号=0,权限=0}
              发送数据(数字id,7,"#R你已经被逐出了帮派")
              if 玩家数据[数字id].角色.数据.跑商 then
                  玩家数据[数字id].角色.数据.跑商 = nil
                  玩家数据[数字id].道具:消耗背包道具(数字id,"帮派银票")
              end
          elseif 玩家数据[数字id].角色.数据.帮派 ~= "无帮派" and 玩家数据[数字id].角色.数据.帮派数据 ~= nil and 玩家数据[数字id].角色.数据.帮派数据.编号>0 and 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号] ~= nil and 帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号].成员数据[数字id] ~= nil then
              帮派数据[玩家数据[数字id].角色.数据.帮派数据.编号].成员数据[数字id].在线 = true
          end



          if 成就数据[数字id]==nil then
              成就数据[数字id]={成就点=0}
          end

          if 剑会天下[数字id]==nil then
               剑会天下[数字id]={当前积分=1200,连胜=0,名称=玩家数据[数字id].角色.名称,等级=玩家数据[数字id].角色.等级,数字id=数字id,门派=玩家数据[数字id].角色.门派}
          end
          if 镇妖塔数据[数字id] == nil then
             镇妖塔数据[数字id] = {名称 = 玩家数据[数字id].角色.数据.名称,层数 = 0}
          end
        -- if 轮回境数据[数字id] == nil then
        --    轮回境数据[数字id] = {名称 = 玩家数据[数字id].角色.数据.名称,层数 = 0}
        -- end
        if 活跃数据[数字id]==nil then
           活跃数据[数字id]={活跃度=0}
        end

        if 玩家数据[数字id].角色.数据.抽奖==nil then
           玩家数据[数字id].角色.数据.抽奖 = 0
        end
        if 玩家数据[数字id].角色.数据.打造加成==nil then
           玩家数据[数字id].角色.数据.打造加成 = {赐福=0,双加=0,特效=0,特技=0}
        end
        if 玩家数据[数字id].角色.数据.战斗赐福==nil then
           玩家数据[数字id].角色.数据.战斗赐福 ={伤害结果=0,法伤结果=0,物伤结果=0,固伤结果=0,治疗结果=0,伤害减免=0,物伤减免=0,法伤减免=0,固伤减免=0,技能连击=0}
        end
        if 玩家数据[数字id].角色.数据.神话词条==nil then
           玩家数据[数字id].角色.数据.神话词条 = {}
        end


        if 玩家数据[数字id].角色.数据.神器属性==nil then
           玩家数据[数字id].角色.数据.神器属性 = {速度=0,法术防御=0,防御=0,气血=0,伤害=0,法术伤害=0,固定伤害=0,治疗能力=0,法术暴击等级=0,物理暴击等级=0,封印命中等级=0,抵抗封印等级=0}  ---神器
        end
        if 玩家数据[数字id].角色.数据.自动抓鬼==nil then
           玩家数据[数字id].角色.数据.自动抓鬼 = 0
        end
        if 玩家数据[数字id].角色.数据.出师数量==nil then
           玩家数据[数字id].角色.数据.出师数量 = 0
        end
        if 玩家数据[数字id].角色.数据.妖魔积分==nil then
           玩家数据[数字id].角色.数据.妖魔积分 = 0
        end
        if 玩家数据[数字id].角色.数据.镇妖积分==nil then
           玩家数据[数字id].角色.数据.镇妖积分 = 0
        end
        if 玩家数据[数字id].角色.数据.师门积分==nil then
           玩家数据[数字id].角色.数据.师门积分 = 0
        end
        if 玩家数据[数字id].角色.数据.文韵积分==nil then
           玩家数据[数字id].角色.数据.文韵积分 = 0
        end
        if 玩家数据[数字id].角色.数据.成就积分==nil then
           玩家数据[数字id].角色.数据.成就积分 = 0
        end
        if 玩家数据[数字id].角色.数据.活跃积分==nil then
           玩家数据[数字id].角色.数据.活跃积分 = 0
        end
        if 玩家数据[数字id].角色.数据.副本积分==nil then
           玩家数据[数字id].角色.数据.副本积分 = 0
        end
        if 玩家数据[数字id].角色.数据.仙缘积分==nil then
           玩家数据[数字id].角色.数据.仙缘积分 = 0
        end
        if 玩家数据[数字id].角色.数据.新手奖励==nil then
           玩家数据[数字id].角色.数据.新手奖励 = {}
        end
        if 玩家数据[数字id].角色.数据.文韵次数==nil then
           玩家数据[数字id].角色.数据.文韵次数 = 0
        end
        if 玩家数据[数字id].角色.数据.灵宝==nil then   ----------灵宝
           玩家数据[数字id].角色.数据.灵宝 = {}
        end
        if 玩家数据[数字id].角色.数据.灵宝佩戴==nil then   ----------灵宝
           玩家数据[数字id].角色.数据.灵宝佩戴 = {}
        end
        if 玩家数据[数字id].角色.数据.靓号==nil then
           玩家数据[数字id].角色.数据.靓号 = "("..数字id..")"
        end
        if 玩家数据[数字id].角色.数据.携带宠物==nil then
           玩家数据[数字id].角色.数据.携带宠物 = 3
        end
        if 玩家数据[数字id].角色.数据.功德录 == nil then
           玩家数据[数字id].角色.数据.功德录 = {激活=false,九珠副={[1]={类型="伤害",数值=20},[2]={类型="气血",数值=98},[3]={类型="防御",数值=20},[4]={类型="速度",数值=20},[5]={类型="法术伤害",数值=20},[6]={类型="法术防御",数值=20}}}
        end
        if 自动回收[数字id] ==nil then
           自动回收[数字id] = {兽决=0,高级兽决=0,二级药=0,五宝=0,宝石=0,超级金柳露=0,环装=0,暗器=0,书铁=0,修练果=0,强化石=0,月华露=0,清灵净瓶=0,九转金丹=0,低级内丹=0,高级内丹=0}
        end

         -- if 玩家数据[数字id].角色.数据.道具仓库~=nil then
         --    local 是否有物品 =false
         --    local num=0
         --    local 页数=1
         --    local 临时物品列表={}
         --    for k,v in pairs(玩家数据[数字id].角色.数据.道具仓库) do
         --      if 玩家数据[数字id].角色.数据.道具仓库[k]~=nil and #v>=1 then
         --           for c,z in pairs(v) do
         --              if 玩家数据[数字id].道具.数据[z]~=nil then
         --                临时物品列表[#临时物品列表+1] = z
         --             end
         --           end
         --      end
         --    end
         --    for i=1,#临时物品列表 do
         --      num = num+1
         --      if 玩家数据[数字id].道具仓库.数据[页数]==nil then
         --         玩家数据[数字id].道具仓库.数据[页数] ={}
         --      end
         --      if 玩家数据[数字id].道具仓库.数据[页数][num]==nil and 玩家数据[数字id].道具.数据[临时物品列表[i]]~=nil then
         --        local 存入物品数据=DeepCopy(玩家数据[数字id].道具.数据[临时物品列表[i]])
         --         table.insert( 玩家数据[数字id].道具仓库.数据[页数],存入物品数据)
         --         玩家数据[数字id].道具.数据[临时物品列表[i]] =nil
         --      end
         --      if num==20 then
         --        num=0
         --        页数=页数+1
         --      end
         --    end
         --    玩家数据[数字id].角色.数据.道具仓库={}
         -- end





      -- if 图鉴系统[数字id]==nil then
      --        图鉴系统[数字id]={东海湾={大海龟=false,巨蛙=false,海毛虫=false,章鱼=false,海星=false} --真特么费劲
      --               ,江南野外={树怪=false,野猪=false,浣熊=false}
      --               ,大雁塔={赌徒=false,强盗=false,骷髅怪=false,羊头怪=false,蛤蟆精=false,狐狸精=false,花妖=false,大蝙蝠=false}
      --               ,大唐国境={赌徒=false,强盗=false,山贼=false,护卫=false}
      --               ,大唐境外={老虎=false,黑熊=false,花妖=false}
      --               ,魔王寨={牛妖=false,蛤蟆精=false}
      --               ,普陀山={黑熊精=false,蜘蛛精=false}
      --               ,盘丝岭={蜘蛛精=false,狐狸精=false,花妖=false}
      --               ,狮驼岭={雷鸟人=false,蝴蝶仙子=false}
      --               ,西牛贺州={小龙女=false,狼=false}
      --               ,花果山={小龙女=false,老虎=false,黑熊=false}
      --               ,海底迷宫={虾兵=false,蟹将=false,龟丞相=false}
      --               ,地狱迷宫={野鬼=false,骷髅怪=false,僵尸=false,牛头=false,马面=false}
      --               ,北俱芦洲={古代瑞兽=false,白熊=false,天将=false}
      --               ,龙窟={古代瑞兽=false,黑山老妖=false,天兵=false,风伯=false,蛟龙=false,雨师=false,地狱战神=false,巡游天神=false,星灵仙子=false}
      --               ,凤巢={黑山老妖=false,天将=false,天兵=false,雷鸟人=false,地狱战神=false,风伯=false,凤凰=false,如意仙子=false,芙蓉仙子=false}
      --               ,无名鬼蜮={幽灵=false,吸血鬼=false,鬼将=false,阴阳伞=false,画魂=false,幽萤娃娃=false}
      --               ,小西天={夜罗刹=false,炎魔神=false,噬天虎=false,阴阳伞=false,金铙僧=false}
      --               ,女娲神迹={律法女娲=false,灵符女娲=false,净瓶女娲=false,阴阳伞=false}
      --               ,小雷音寺={大力金刚=false,雾中仙=false,灵鹤=false,阴阳伞=false}
      --               ,蓬莱仙岛={龙龟=false,红萼仙子=false,踏云兽=false,琴仙=false,阴阳伞=false}
      --               ,月宫={兔子怪=false,蜘蛛精=false}
      --               ,蟠桃园={巨力神猿=false,长眉灵猴=false,混沌兽=false}
      --               ,墨家禁地={连弩车=false,巴蛇=false,机关鸟=false,机关兽=false,机关人=false,阴阳伞=false}
      --               ,解阳山={犀牛将军人形=false,犀牛将军兽形=false,锦毛貂精=false,千年蛇魅=false}
      --               ,子母河底={蚌精=false,碧水夜叉=false,鲛人=false}
      --               ,麒麟山={泪妖=false,镜妖=false,鼠先锋=false,百足将军=false,野猪精=false}
      --               ,碗子山={蝎子精=false,葫芦宝贝=false}
      --               ,波月洞={猫灵人形=false,狂豹人形=false,猫灵兽形=false,狂豹兽形=false,混沌兽=false}
      --               ,柳林坡={修罗傀儡鬼=false,蜃气妖=false,蔓藤妖花=false}
      --               ,比丘国={金身罗汉=false,修罗傀儡妖=false,曼珠沙华=false}
      --               ,须弥东界={持国巡守=false,毗舍童子=false,真陀护法=false,增长巡守=false,灵灯侍者=false,般若天女=false}
      --               }
      --  end
      for k,v in pairs(玩家数据[数字id].好友.数据.好友) do
           if 玩家数据[k] and 玩家数据[k].角色 and 玩家数据[k].好友.数据.好友[数字id] then
               玩家数据[k].好友:更新数据()
               发送数据(玩家数据[k].连接id,38,{内容="#W你的好友#Y"..玩家数据[数字id].角色.数据.名称.."#W进入了游戏",频道="xt"})
           end
       end





      if 玩家数据[数字id].角色.数据.帮派数据~=nil and 玩家数据[数字id].角色.数据.帮派数据.编号 > 0 then
        local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
        if 帮派数据[帮派编号].成员数据[数字id].帮贡.当前~= 玩家数据[数字id].角色.数据.帮贡 then
          玩家数据[数字id].角色.数据.帮贡=帮派数据[帮派编号].成员数据[数字id].帮贡.当前
        end
      end
end


function 系统处理类:进入游戏检测(数字id)
    for i=1,4 do
       if 玩家数据[数字id].角色.数据.灵饰[i] ~= nil then
          local 格子 = 玩家数据[数字id].角色.数据.灵饰[i]
          if not 玩家数据[数字id].道具.数据[格子] then
              常规提示(数字id,"#Y你的装备数据问题已被系统回收")
              玩家数据[数字id].角色.数据.灵饰[i] = nil
              玩家数据[数字id].道具.数据[格子] = nil
          elseif 玩家数据[数字id].道具.数据[格子].总类~=2 or not 玩家数据[数字id].道具.数据[格子].灵饰 then
                常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                玩家数据[数字id].角色.数据.灵饰[i] = nil
                玩家数据[数字id].道具.数据[格子] = nil
          end
        end
     end

    for i=1,4 do
       if 玩家数据[数字id].角色.数据.锦衣[i] ~= nil then
          local 格子 = 玩家数据[数字id].角色.数据.锦衣[i]
          if not 玩家数据[数字id].道具.数据[格子] then
              常规提示(数字id,"#Y你的装备数据问题已被系统回收")
              玩家数据[数字id].角色.数据.锦衣[i] = nil
              玩家数据[数字id].道具.数据[格子] = nil
          elseif 玩家数据[数字id].道具.数据[格子].总类~=2 or not 玩家数据[数字id].道具.数据[格子].锦衣 then
                常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                玩家数据[数字id].角色.数据.锦衣[i] = nil
                玩家数据[数字id].道具.数据[格子] = nil
          end
        end
     end
      for i=1,6 do
          if 玩家数据[数字id].角色.数据.装备[i] ~= nil then
              local 格子 = 玩家数据[数字id].角色.数据.装备[i]
              if not 玩家数据[数字id].道具.数据[格子] then
                  常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                  玩家数据[数字id].角色.数据.装备[i] = nil
                  玩家数据[数字id].道具.数据[格子] = nil
               elseif 玩家数据[数字id].道具.数据[格子].总类~=2 then
                    常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                    玩家数据[数字id].角色.数据.锦衣[i] = nil
                    玩家数据[数字id].道具.数据[格子] = nil
              elseif 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
                  玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
                  常规提示(数字id,"#Y你的装备#R"..玩家数据[数字id].道具.数据[格子].名称.."#Y使用时间已到已被系统回收")
                  玩家数据[数字id].角色.数据.装备[i] = nil
                  玩家数据[数字id].道具.数据[格子] = nil
              end
          end
      end

      for i=1,4 do
       if 玩家数据[数字id].角色.数据.法宝佩戴[i] ~= nil then
          local 格子 = 玩家数据[数字id].角色.数据.法宝佩戴[i]
          if not 玩家数据[数字id].道具.数据[格子] then
              常规提示(数字id,"#Y你的装备数据问题已被系统回收")
              玩家数据[数字id].角色.数据.法宝佩戴[i] = nil
              玩家数据[数字id].道具.数据[格子] = nil
          elseif 玩家数据[数字id].道具.数据[格子].总类~=1000  then
                常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                玩家数据[数字id].角色.数据.法宝佩戴[i] = nil
                玩家数据[数字id].道具.数据[格子] = nil
          end
        end
     end


      for i=1,2 do
         if 玩家数据[数字id].角色.数据.灵宝佩戴[i] ~= nil then
              local 格子 = 玩家数据[数字id].角色.数据.灵宝佩戴[i]
              if not 玩家数据[数字id].道具.数据[格子] then
                  常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                  玩家数据[数字id].角色.数据.灵宝佩戴[i] = nil
                  玩家数据[数字id].道具.数据[格子] = nil
              elseif 玩家数据[数字id].道具.数据[格子].总类~=1005 then
                    常规提示(数字id,"#Y你的装备数据问题已被系统回收")
                    玩家数据[数字id].角色.数据.灵宝佩戴[i] = nil
                    玩家数据[数字id].道具.数据[格子] = nil
              end
          end
     end
end

function 系统处理类:进入战斗检测(数字id)
    for i=1,4 do
       if 玩家数据[数字id].角色.数据.灵饰[i] ~= nil then
          local 格子 = 玩家数据[数字id].角色.数据.灵饰[i]
          if not 玩家数据[数字id].道具.数据[格子] then
              常规提示(数字id,"#Y你的装备数据问题已被系统回收")
              玩家数据[数字id].角色.数据.灵饰[i] = nil
              玩家数据[数字id].道具.数据[格子] = nil
          end
        end
     end
  for i=1,6 do
    if 玩家数据[数字id].角色.数据.装备[i] ~= nil then
      local 格子 = 玩家数据[数字id].角色.数据.装备[i]
      if not 玩家数据[数字id].道具.数据[格子] then
          常规提示(数字id,"#Y你的装备数据问题已被系统回收")
          玩家数据[数字id].角色.数据.装备[i] = nil
          玩家数据[数字id].道具.数据[格子] = nil
         return
      end
      if 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
        玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
        发送数据(玩家数据[数字id].连接id,38,{内容="你的装备#R/"..玩家数据[数字id].道具.数据[格子].名称.."#W/使用时间已到已被系统回收",频道="xt"})
        玩家数据[数字id].角色.数据.装备[i] = nil
        玩家数据[数字id].道具.数据[格子] = nil
      end
    end
  end
end

function 系统处理类:退出战斗检测(数字id)
  for i=1,6 do
    if 玩家数据[数字id].角色.数据.装备[i] ~= nil then
      local 格子 = 玩家数据[数字id].角色.数据.装备[i]
      if not 玩家数据[数字id].道具.数据[格子] then
          常规提示(数字id,"#Y你的装备数据问题已被系统回收")
          玩家数据[数字id].角色.数据.装备[i] = nil
          玩家数据[数字id].道具.数据[格子] = nil
         return
      end
      if 玩家数据[数字id].道具.数据[格子].限时 ~= nil and 玩家数据[数字id].道具.数据[格子].限时 < os.time() then
        玩家数据[数字id].角色:卸下装备(玩家数据[数字id].道具.数据[格子],玩家数据[数字id].道具.数据[格子].分类,"0")
        发送数据(玩家数据[数字id].连接id,38,{内容="你的装备#R/"..玩家数据[数字id].道具.数据[格子].名称.."#W/使用时间已到已被系统回收",频道="xt"})
        玩家数据[数字id].角色.数据.装备[i] = nil
        玩家数据[数字id].道具.数据[格子] = nil
      end
    end
  end
end
function 系统处理类:进入事件(id,连接id)

    __gge.print(false,6,时间转换(os.time()).."账号:")
    __gge.print(false,11,玩家数据[id].账号)
    __gge.print(false,6,"ID:")
    __gge.print(false,11,id)
    __gge.print(false,6,"名称:")
    __gge.print(false,11,玩家数据[id].角色.数据.名称)
    __gge.print(false,10," 进入游戏\n")
    ---if 玩家数据[id].角色.数据.等级>=10 and (玩家数据[id].角色.数据.月卡领取==nil or 玩家数据[id].角色.数据.月卡领取<1) then
    if 玩家数据[id].角色.数据.月卡.开通 then
         广播消息({内容="#Y/欢迎#R/"..服务端参数.名称.."#Y/会员#W/"..玩家数据[id].角色.数据.名称..玩家数据[id].角色.数据.靓号.."#Y/进入游戏!",频道="xt"})
    end
    self:获取签到数据(id)





      -- if 玩家数据[id].角色.数据.出生奖励  then
      --    礼包奖励类:新手使者奖励(id,20)
      --    玩家数据[id].角色.数据.出生奖励=false
      -- end


  --  else
  --   广播消息({内容=format("#Z月卡玩家#Y【"..玩家数据[id].角色.数据.名称.."】#G进入游戏\n#G开始叱咤风云,笑傲三界的江湖之旅!"),频道="xt"})
  -- end
   -- 广播消息({内容=format("#Y欢迎#G/%s#Y进入游戏..#81",id),频道="xt"})
  if 玩家数据[id].角色.数据.离线时间~=nil then
    local 离线总时长 = os.time()-玩家数据[id].角色.数据.离线时间
    local 离线经验值 = 0
    local 离线储备 = 0
    if 离线总时长>=300 then  --离线时间5分钟  才会获取离线经验
      if 离线总时长 > 172800 then  --不能超过3天
        离线总时长=172800  --如果超过3天就按照三天算
      end
      离线经验值 = math.floor(离线总时长/300*玩家数据[id].角色.数据.等级*3.5*10) ---每5分钟计算一次经验 按照你等级*3.5*1.5给你经验 你可以自己设定  比方说挂野的
      离线储备 = math.floor(离线总时长/300*玩家数据[id].角色.数据.等级*3.5*10)
      ----离线经验
      if 玩家数据[id].角色:取任务(409)~=0 then
        local 任务id=玩家数据[id].角色:取任务(409)
        任务数据[任务id].离线经验=任务数据[任务id].离线经验 + 离线经验值
        if 任务数据[任务id].离线经验 >= math.floor(172800/300*玩家数据[id].角色.数据.等级*3.5*10) then  --如果离线经验超过你所在等级3天存储的时间就不会再获取了
          任务数据[任务id].离线经验 = math.floor(172800/300*玩家数据[id].角色.数据.等级*3.5*10)
          常规提示(id,"#Y/当前离线经验已满！最多只能获取#R/"..任务数据[任务id].离线经验.."#Y/点离线经验！")
        else
          常规提示(id,"#Y/获取了#R/"..离线经验值.."#Y/点离线经验！")
        end
      else
        if 离线经验值>0 then
           任务处理类:添加离线经验(id,离线经验值)
          常规提示(id,"#Y/获取了#R/"..离线经验值.."#Y/点离线经验！")
        end
      end
      ----离线储备
      if 玩家数据[id].角色:取任务(410)~=0 then
        local 任务id=玩家数据[id].角色:取任务(410)
        任务数据[任务id].离线储备=任务数据[任务id].离线储备 + 离线储备
        if 任务数据[任务id].离线储备 >= math.floor(172800/300*玩家数据[id].角色.数据.等级*3.5*10) then  --如果离线储备超过你所在等级3天存储的时间就不会再获取了
          任务数据[任务id].离线储备 = math.floor(172800/300*玩家数据[id].角色.数据.等级*3.5*10)
          常规提示(id,"#Y/当前离线储备已满！最多只能获取#R/"..任务数据[任务id].离线储备.."#Y/点离线储备！")
        else
          常规提示(id,"#Y/获取了#R/"..离线储备.."#Y/点离线储备！")
        end
      else
        if 离线储备>0 then
           任务处理类:添加离线储备(id,离线储备)
          常规提示(id,"#Y/获取了#R/"..离线储备.."#Y/点离线储备！")
        end
      end
    end
    玩家数据[id].角色.数据.离线时间=nil
  end
  if 玩家数据[id].角色.数据.帮派申请 then
      if 玩家数据[id].角色.数据.帮派 ~= "无帮派" and 玩家数据[id].角色.数据.帮派数据 ~= nil and 玩家数据[id].角色.数据.帮派数据.编号>0 and 帮派数据[玩家数据[id].角色.数据.帮派数据.编号] ~= nil and 帮派数据[玩家数据[id].角色.数据.帮派数据.编号].成员数据[id] ~= nil then
          local 帮派编号 = 玩家数据[id].角色.数据.帮派数据.编号
          发送数据(玩家数据[id].连接id,69,{项目 = "1",编号=帮派编号,权限=0})
          发送数据(玩家数据[id].连接id, 7, "#Y/恭喜你已经被批准加入帮派,赶快和帮派里的小伙伴打声招呼吧。")
          玩家数据[id].角色:添加称谓(帮派数据[帮派编号].帮派名称.."帮众")
      end
      玩家数据[id].角色.数据.帮派申请=nil
  end




  -- for n=1,#玩家数据[id].角色.数据.好友数据.好友 do
  --   local 好友id=玩家数据[id].角色.数据.好友数据.好友[n].id
  --   if 玩家数据[好友id]~=nil and 玩家数据[id].角色.数据.好友数据.好友[n].好友度~=nil and 玩家数据[id].角色.数据.好友数据.好友[n].好友度>=10 then
  --     for i=1,#玩家数据[好友id].角色.数据.好友数据.好友 do
  --       if 玩家数据[好友id].角色.数据.好友数据.好友[i].id==id and 玩家数据[好友id].角色.数据.好友数据.好友[i].好友度~=nil and 玩家数据[好友id].角色.数据.好友数据.好友[i].好友度>=10 then
  --         发送数据(玩家数据[好友id].连接id,38,{内容="#W你的好友#Y"..玩家数据[id].角色.数据.名称.."#W进入了游戏",频道="xt"})
  --       end
  --     end
  --   end
  -- end
  --检测坐骑属性
  -- table.print([2])

  if not 判断是否为空表(玩家数据[id].角色.数据.坐骑列表) then
    for k,v in pairs(玩家数据[id].角色.数据.坐骑列表) do
      local 检测是否成功 = true
      for i,n in pairs(v) do
        if i=="体质" and n<0 then
          检测是否成功=false
        elseif  i=="魔力" and n<0 then
          检测是否成功=false
        elseif  i=="力量" and n<0 then
          检测是否成功=false
        elseif  i=="耐力" and n<0 then
          检测是否成功=false
        elseif  i=="敏捷" and n<0 then
          检测是否成功=false
        end
      end
      if not 检测是否成功 then
        玩家数据[id].角色:坐骑洗点(k)
      end
    end
  end
end










function 系统处理类:账号验证(id,序号,内容)
  if not 内容.账号 or 内容.账号 =="" then return end
  if f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","封禁")=="1" then  --账号是否异常
    发送数据(id,7,"#R该账号已经被禁止登录游戏")

    return false
  elseif f函数.文件是否存在([[data/]]..内容.账号)==false then  --未创建存档
    发送数据(id,7,"#Y该账户未被注册！")
    return false
  else
      local 密码=f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","密码")
      if 密码~=内容.密码 then
        发送数据(id,7,"#Y您输入的密码不正确！")
        return false
      end
      if f函数.文件是否存在([[data/]]..内容.账号..[[/充值记录]])==false then
          lfs.mkdir([[data/]]..内容.账号..[[/充值记录]])
      end
      if f函数.文件是否存在([[data/]]..内容.账号..[[/消费记录]])==false then
          lfs.mkdir([[data/]]..内容.账号..[[/消费记录]])
      end
      local 清理时间=f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","清理时间")
      if not 清理时间 or 清理时间=="空" or 清理时间=="" then
          清理时间=0
      end
      if tonumber(清理时间)~=tonumber(os.date("%d", os.time())) then
           local 充值数量 = 取文件的所有名(程序目录..[[/data/]]..内容.账号..[[/充值记录]])
           for i=1,#充值数量 do
                local 年份 = 分割文本(充值数量[i],"年")
                local 月份 = 分割文本(年份[2],"月")
                local 日份 = 分割文本(月份[2],"日")
                local 时间戳 = os.time({day=日份[1], month=月份[1], year=年份[1], hour=0, minute=0, second=0})
                if os.time() - 时间戳 >= 5184000  then
                    os.remove(程序目录..[[\data\]]..内容.账号..[[\充值记录\]]..充值数量[i]..[[.txt]])
                end
          end
           local 消费数量 = 取文件的所有名(程序目录..[[/data/]]..内容.账号..[[/消费记录]])
           for i=1,#消费数量 do
                local 年份 = 分割文本(消费数量[i],"年")
                local 月份 = 分割文本(年份[2],"月")
                local 日份 = 分割文本(月份[2],"日")
                local 时间戳 = os.time({day=日份[1], month=月份[1], year=年份[1], hour=0, minute=0, second=0})
                if os.time() - 时间戳 >= 5184000  then
                    os.remove(程序目录..[[\data\]]..内容.账号..[[\消费记录\]]..消费数量[i]..[[.txt]])
                end
          end
          f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","清理时间",os.date("%d", os.time()))
      end

      if 序号 ~= 1.1 and 序号 ~= 1.2 then
            self:取角色选择信息(id,内容.账号)
      -- elseif 序号 == 1.2 then
      --       发送数据(id,1.1)
      end
      return true
  end
end



function 系统处理类:封号处理(id,序号,内容)
       print("有人被疯了")
      --f函数.写配置(程序目录 .."ip封禁.ini","ip",__C客户信息[id].IP,1)

       -- 发送数据(id,7,"#Y还TM想盗刷老摩托给爷滚！")
     end
function 系统处理类:ip检测(ip)
  if ip==nil or ip=="" then return true end
  for n=1,#ip封禁表 do
    if ip封禁表[n].ip==ip then return true end
  end
  return false
end





function 系统处理类:账号注册(id,序号,内容)
  if not 内容.账号 then return  end
  if f函数.读配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","封禁")=="1" then  --账号是否异常
    发送数据(id,7,"#Y该账号已经被禁止登录游戏")
    return
  end
  if self:ip检测(内容.ip) then
    发送数据(id,7,"#Y该IP地址已在服务器黑名单内，无法建立连接！")
    return
  end

  if f函数.文件是否存在([[data/]]..内容.账号)==false then  --未创建存档
        if 服务端参数.授权注册 and 服务端参数.授权注册[服务端参数.名称] then
            if f函数.文件是否存在([[授权码]])==false then----------授权码
                lfs.mkdir([[授权码]])
            end
            if not 内容.授权码 or type(内容.授权码)~="string" or 内容.授权码=="string"  and 内容.授权码~="878789" then
                发送数据(id,7,"#Y请输入正确的授权码！")
                return
            end
            if f函数.文件是否存在([[授权码\]]..内容.授权码..[[.txt]])==false then
                发送数据(id,7,"#Y请输入正确的授权码！")
                return
            else
                os.remove(程序目录..[[\授权码\]]..内容.授权码..[[.txt]])
            end
        end
        if  内容.账号 == ""  or 内容.账号 == nil or 判断特殊字符(内容.账号) then
             发送数据(id,7,"#Y账号不能有特殊字符")
            return
        end
        创建目录([[data/]]..内容.账号)
        local file =io.open([[data\]]..内容.账号..[[\信息.txt]],"w")
        file:write([[do local ret={} return ret end]])
        file:close()
        local file =io.open([[data\]]..内容.账号..[[\账号信息.txt]],"w")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","密码",内容.密码)
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","仙玉","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","点数","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","管理","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","安全码","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","创建时间",时间转换(os.time()))
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","创建ip",内容.ip)
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","银子","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","储备","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","清理时间","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","充值编号","1")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","日志编号","1")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","充值当前","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","充值累计","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","充值领取","0")
        f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","特权等级","0")
        if 服务端参数.授权注册 and 服务端参数.授权注册[服务端参数.名称] then
            f函数.写配置(程序目录..[[data\]]..内容.账号..[[\账号信息.txt]],"账号配置","授权码",内容.授权码)
            添加充值日志("注册账号:"..内容.账号..",使用授权码为:"..内容.授权码)
        end
        file:close()
        lfs.mkdir([[data/]]..内容.账号..[[/消费记录]])
        lfs.mkdir([[data/]]..内容.账号..[[/充值记录]])
        发送数据(id,7,"#Y创建账号成功！")
        return
  else
    发送数据(id,7,"#Y该账号已经存在！")
    return
  end
end






function 系统处理类:取角色选择信息(id,账号)
  if not 账号 then 发送数据(id,998,"数据错误,请截图给管理~！") return end
  local 临时文件=读入文件([[data/]]..账号..[[/信息.txt]])
  local 写入信息=table.loadstring(临时文件)
  local 发送信息={}
  for n=1,#写入信息 do
    local 副武器
    local 武器数据
    local 锦衣数据
    local 光环数据
    local 足迹数据
    local 读取文件=table.loadstring(读入文件([[data/]]..账号..[[/]]..写入信息[n]..[[/角色.txt]]))
    if not 读取文件 then 发送数据(id,998,"数据错误,请截图给管理~！") return end
    local 临时道具=table.loadstring(读入文件([[data/]]..账号..[[/]]..写入信息[n]..[[/道具.txt]]))
    if not 临时道具 then 临时道具={} end
    if 读取文件.装备 and 读取文件.装备[3] then
        武器数据=临时道具[读取文件.装备[3]]
    end
    if 读取文件.装备 and 读取文件.装备[4] and 读取文件.模型=="影精灵" then
        副武器=临时道具[读取文件.装备[4]]
    end
    if 读取文件.锦衣 then
        if 读取文件.锦衣[1] then
            锦衣数据 = 临时道具[读取文件.锦衣[1]]
        end
        if 读取文件.锦衣[2] then
            光环数据 = 临时道具[读取文件.锦衣[2]]
        end
        if 读取文件.锦衣[3] then
            足迹数据 = 临时道具[读取文件.锦衣[3]]
        end
    end
    发送信息[n]={锦衣数据=锦衣数据,光环数据=光环数据,足迹数据=足迹数据,名称=读取文件.名称,等级=读取文件.等级,染色方案=读取文件.染色方案,染色组=读取文件.染色组,造型=读取文件.造型,武器数据=武器数据,门派=读取文件.门派,id=读取文件.数字id,副武器=副武器}

  end
  发送数据(id,4,发送信息)
end





function 系统处理类:设置传说物品(id)
  --修炼
  local 任务id=玩家数据[id].角色:取任务(13)
  if 任务id~=0 and 任务数据[任务id]~=nil and 任务数据[任务id].传说==nil then
    任务数据[任务id].传说=1
    if 任务数据[任务id].等级==nil then
      玩家数据[id].道具:给予道具(id,任务数据[任务id].物品,1,30)
      常规提示(id,"#Y你获得了传说中的#R"..任务数据[任务id].物品)
      发送数据(玩家数据[id].连接id,38,{内容="你得到了#R/"..任务数据[任务id].物品})
    else
      玩家数据[id].道具:取随机装备1(id,任务数据[任务id].等级,任务数据[任务id].物品)
      常规提示(id,"#Y你获得了传说中的#R"..任务数据[任务id].物品)
      发送数据(玩家数据[id].连接id,38,{内容="你得到了#R/"..任务数据[任务id].物品})
    end
  end
end

function 系统处理类:生死劫处理(id,数据)
for i=1,#队伍数据[玩家数据[id].队伍].成员数据 do
  if 生死劫数据.次数==nil then
      生死劫数据.次数={}
  end
  if 生死劫数据.次数[队伍数据[玩家数据[id].队伍].成员数据[i]]==nil then
      生死劫数据.次数[队伍数据[玩家数据[id].队伍].成员数据[i]]=0
  end
  if 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]==nil then
    生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]=1
  elseif 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]>9 then
    生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]=1
  end
  if 生死劫数据.次数[队伍数据[玩家数据[id].队伍].成员数据[i]]>=3 then
      添加最后对话(队伍数据[玩家数据[id].队伍].成员数据[i],"你本日可挑战次数已达上限，每日仅可以挑战3次生死劫，请明日再来吧。")
      return
  end
  if 数据.序列=="止戈" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=1 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="清心" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=2 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="雷霆" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=3 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="惜花" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=4 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="忘情" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=5 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="卧龙" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=6 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="天象" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=7 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="轮回" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=8 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  elseif 数据.序列=="娑罗" and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=nil and 生死劫数据[队伍数据[玩家数据[id].队伍].成员数据[i]]~=9 then
    常规提示(队伍数据[玩家数据[id].队伍].成员数据[i],"#Y你已经挑战过该层！")
    return
  end
end
  战斗准备类:创建战斗(id,100055,0)
end











function 系统处理类:显示(x,y) end
return 系统处理类