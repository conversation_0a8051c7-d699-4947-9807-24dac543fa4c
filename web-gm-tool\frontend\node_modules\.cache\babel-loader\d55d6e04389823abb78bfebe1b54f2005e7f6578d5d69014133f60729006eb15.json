{"ast": null, "code": "export function isImageValid(src) {\n  return new Promise(function (resolve) {\n    if (!src) {\n      resolve(false);\n      return;\n    }\n    var img = document.createElement('img');\n    img.onerror = function () {\n      return resolve(false);\n    };\n    img.onload = function () {\n      return resolve(true);\n    };\n    img.src = src;\n  });\n}", "map": {"version": 3, "names": ["isImageValid", "src", "Promise", "resolve", "img", "document", "createElement", "onerror", "onload"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-image/es/util.js"], "sourcesContent": ["export function isImageValid(src) {\n  return new Promise(function (resolve) {\n    if (!src) {\n      resolve(false);\n      return;\n    }\n    var img = document.createElement('img');\n    img.onerror = function () {\n      return resolve(false);\n    };\n    img.onload = function () {\n      return resolve(true);\n    };\n    img.src = src;\n  });\n}"], "mappings": "AAAA,OAAO,SAASA,YAAYA,CAACC,GAAG,EAAE;EAChC,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAE;IACpC,IAAI,CAACF,GAAG,EAAE;MACRE,OAAO,CAAC,KAAK,CAAC;MACd;IACF;IACA,IAAIC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;IACvCF,GAAG,CAACG,OAAO,GAAG,YAAY;MACxB,OAAOJ,OAAO,CAAC,KAAK,CAAC;IACvB,CAAC;IACDC,GAAG,CAACI,MAAM,GAAG,YAAY;MACvB,OAAOL,OAAO,CAAC,IAAI,CAAC;IACtB,CAAC;IACDC,GAAG,CAACH,GAAG,GAAGA,GAAG;EACf,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}