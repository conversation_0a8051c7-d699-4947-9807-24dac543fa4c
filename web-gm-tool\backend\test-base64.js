/**
 * Base64编码测试
 * 对比Node.js标准Base64和我们的实现
 */

const { encodeBase641 } = require('./src/protocol/encryption');

const testData = '112345*-*12345do local ret={["账号"]="888888",["密码"]="888888"} return ret end12345*-*12345';

console.log('=== Base64编码对比测试 ===');
console.log('原始数据:', testData);
console.log('原始数据长度:', testData.length);

// Node.js标准Base64编码
const nodeBase64 = Buffer.from(testData, 'utf8').toString('base64');
console.log('\nNode.js标准Base64:');
console.log('长度:', nodeBase64.length);
console.log('结果:', nodeBase64);

// 我们的Base64编码
const ourBase64 = encodeBase641(testData);
console.log('\n我们的Base64:');
console.log('长度:', ourBase64.length);
console.log('结果:', ourBase64);

// 对比
console.log('\n对比结果:');
console.log('长度相同:', nodeBase64.length === ourBase64.length);
console.log('内容相同:', nodeBase64 === ourBase64);

if (nodeBase64 !== ourBase64) {
    console.log('\n差异分析:');
    const minLen = Math.min(nodeBase64.length, ourBase64.length);
    for (let i = 0; i < minLen; i++) {
        if (nodeBase64[i] !== ourBase64[i]) {
            console.log(`第${i}位不同: Node.js='${nodeBase64[i]}' 我们='${ourBase64[i]}'`);
            break;
        }
    }
    
    if (nodeBase64.length !== ourBase64.length) {
        console.log(`长度不同: Node.js=${nodeBase64.length} 我们=${ourBase64.length}`);
    }
}
