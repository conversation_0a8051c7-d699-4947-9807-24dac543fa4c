
local 功能界面 = class()
local tp,zt,zt1,zt2,sssss

function 功能界面:初始化(根)
	tp = 根
	local 资源 = 根.资源
	local 按钮 = 根._按钮
	local 自适应 = 根._自适应
	local 滑块 = 根._滑块
	local wz = require("gge文字类")
	zt = wz.创建(nil,16,false,false,false)
	zt1 = wz.创建(nil,20,true,true,false)
	zt2 = wz.创建(nil,14,false,false,false)
	zt3 = wz.创建(nil,16,false,false,false)
	zt3:置颜色(黄色)
	zt4 = wz.创建(nil,16,false,false,false)
	zt4:置颜色(黑色)
	self.进程 = "充值操作"
	self.控件类 = require("ggeui/加载类")()
	local 总控件 = self.控件类:创建控件('创建控件')
	总控件:置可视(true,true)
	self.资源={
		[1]= 资源:载入('gj.wdf',"网易WDF动画",0x1000002),
		[2]= 资源:载入('gj.wdf',"网易WDF动画",0xB5FDF1AC),
		[3]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"充值操作"),
		[4]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"账号操作"),
		[5]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"定制装备"),
		[6]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"游戏管理"),
		[7]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"角色管理"),
		[8]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"宝宝管理"),
		[9]=按钮.创建(自适应.创建(13,4,80,23,1,3),0,0,4,true,true,"赠送道具"),
		[10]= 自适应.创建(3,1,40,19,1,3),--------------输入框背景
		[11]=自适应.创建(2,1,430,320,3,3),----丰富文本背景
		[12]=资源:载入('jn.wdf',"网易WDF动画",0xEE59ADD1),
		[13]=资源:载入('jn.wdf',"网易WDF动画",0xFFCCF519),
        [14]=资源:载入('jn.wdf',"网易WDF动画",0x87511120),
        [15]=资源:载入('jn.wdf',"网易WDF动画",0xA1000011),
        [16]=资源:载入('jn.wdf',"网易WDF动画",0x1134B264),
        [17]=资源:载入('jn.wdf',"网易WDF动画",0x08C6FE78)
	}

	self.充值按钮={
	[1]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值仙玉"),
	[2]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值点卡"),
	[3]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值银子"),
	[4]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值储备"),
	[5]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值经验"),
	[6]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值累充"),
	[7]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值帮贡"),
	[8]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值门贡"),
	[9]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"打造熟练"),
	[10]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"裁缝熟练"),
	[11]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"炼金熟练"),
	[12]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"淬灵熟练"),
	[13]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"活跃积分"),
	[14]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"比武积分"),
	[15]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"充值记录"),
     }


     self.账号按钮={
	[1]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"玩家信息"),
	[2]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"踢出战斗"),
	[3]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"强制下线"),
	[4]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"封禁账号"),
	[5]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"解封账号"),
	[6]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"封禁 I P"),
	[7]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"解封 I P"),
	[8]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开通管理"),
	[9]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"关闭管理"),

     }
     self.密码按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"修改密码")
     self.坐骑按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"给予称谓")
     self.发送装备=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"发送装备")
     self.发送灵饰=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"发送灵饰")

     self.游戏按钮={
	[1]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"过街老鼠"),
    [2]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启异界"),
	[3]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启经宝"),
	[4]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"天降灵猴"),
	[5]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出妖魔"),
	[6]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"二八星宿"),
	[7]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"天庭叛逆"),
	[8]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出星宿"),
	[9]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出星官"),
	[10]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出天罡"),
	[11]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出地煞"),
	[12]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"圣兽残魂"),
	[13]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出知了"),
	[14]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出桐人"),
	[15]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"魔化桐人"),
	[16]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启万象"),
	[17]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启财神"),
	[18]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"皇宫飞贼"),
	[19]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启生肖"),
	[20]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"门派开关"),
	[21]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"宝藏开关"),
	[22]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"镖王开关"),
	[23]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"游泳开关"),
	[24]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启帮战"),
	[25]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"结束帮战"),
	[26]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启病毒"),
	[27]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"刷出福利"),
	[28]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启比武"),
	[29]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"比武入场"),
	[30]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"结束比武"),
	[31]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"首席报名"),
	[32]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"首席入场"),
	[33]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启首席"),
	[34]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启剑会"),
	[35]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"结束剑会"),
	[36]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启保卫"),
	[37]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"关闭保卫"),
	[38]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"帮战报名"),
	[39]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"帮战入场"),
	[40]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"结束帮战"),
	[41]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"天降辰星"),
	[42]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"混世魔王"),
	[43]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"开启彩虹"),
	[44]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"关闭彩虹"),
	[45]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"假人走动"),
	[46]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"假人摆摊"),
	[47]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"假人聊天"),
	[48]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"保存数据"),
	[49]=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"关闭游戏"),
     }
     self.发送广播=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"发送广播")
     self.发送公告=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"发送公告")
     self.经验倍率=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"经验倍率")
     self.游戏难度=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"游戏难度")
     self.等级上限=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"等级上限")
     self.聊天监控开关=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"聊天监控开关")
     self.查看聊天记录=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"查看聊天记录")
     self.清空聊天监控=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"清空聊天监控")
     self.清空聊天记录=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"清空聊天记录")
     self.角色修改按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"确定修改")
     self.获取角色信息按钮=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"获取角色信息")
     self.获取宝宝信息按钮=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"获取宝宝信息")
     self.宝宝修改按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"确定修改")
     self.写出本地=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"写到本地")
     self.恢复角色道具按钮=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"恢复角色道具")

    self.道具按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"给予道具")
    self.宝石按钮=按钮.创建(自适应.创建(12,4,80,22,1,3),0,0,4,true,true,"给予宝石")

    self.获取充值类型=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"获取充值类型")
    self.生成CDK卡号=按钮.创建(自适应.创建(12,4,105,22,1,3),0,0,4,true,true,"生成CDK卡号")
    self.获取充值卡号=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"获取充值卡号")
    self.生成自定义CDK卡号=按钮.创建(自适应.创建(12,4,150,22,1,3),0,0,4,true,true,"生成自定义CDK卡号")
    self.新建充值类型=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"新建充值类型")
    self.删除充值类型=按钮.创建(自适应.创建(12,4,110,22,1,3),0,0,4,true,true,"删除充值卡号")



	self.角色id = 总控件:创建输入("角色的ID",0,0,248,14,根,0xFF000000)
	self.角色id:置可视(true,true)
	self.角色id:置限制字数(30)
	self.角色id:置文字颜色(0xFF000000)
	self.角色id:置文本("")
	self.充值数额 = 总控件:创建输入("充值数额",0,0,248,14,根,0xFF000000)
	self.充值数额:置可视(true,true)
	self.充值数额:置限制字数(30)
	self.充值数额:置文字颜色(0xFF000000)
	self.充值数额:置文本("")
	self.修改密码 = 总控件:创建输入("修改密码",0,0,238,14,根,0xFF000000)
	self.修改密码:置可视(true,true)
	self.修改密码:置限制字数(30)
	self.修改密码:置文字颜色(0xFF000000)
	self.修改密码:置文本("")
	self.坐骑名称 = 总控件:创建输入("坐骑名称",0,0,238,14,根,0xFF000000)
	self.坐骑名称:置可视(true,true)
	self.坐骑名称:置限制字数(30)
	self.坐骑名称:置文字颜色(0xFF000000)
	self.坐骑名称:置文本("")

	self.公告输入 = 总控件:创建输入("公告输入",0,0,497,14,根,0xFF000000)
	self.公告输入:置可视(true,true)
	self.公告输入:置限制字数(400)
	self.公告输入:置文字颜色(0xFF000000)
	self.公告输入:置文本("")

	self.服务输入 = 总控件:创建输入("服务输入",0,0,392,14,根,0xFF000000)
	self.服务输入:置可视(true,true)
	self.服务输入:置限制字数(200)
	self.服务输入:置文字颜色(0xFF000000)
	self.服务输入:置文本("")




	self.装备内容显示 = {"等级","类型","气血","魔法","命中","伤害","防御","速度","灵力","体质","魔力","力量","耐力","敏捷","特效","特技","制造","专用"}
    self.装备类型 = {"武器","衣服","头盔","项链","腰带","鞋子"}
    self.装备输入资源={}
	for i=1,#self.装备内容显示 do
		self.装备输入资源[self.装备内容显示[i]]= 总控件:创建输入("装备"..self.装备内容显示[i],0,0,97,14,根,0XFF000000)
		self.装备输入资源[self.装备内容显示[i]]:置限制字数(14)
		if self.装备内容显示[i] == "类型" then
		  self.装备输入资源[self.装备内容显示[i]]:置限制字数(0)
		end
		self.装备输入资源[self.装备内容显示[i]]:置可视(true,true)
		self.装备输入资源[self.装备内容显示[i]]:置文字颜色(0xFF000000)
		self.装备输入资源[self.装备内容显示[i]]:置文本("")
	end
	self.装备类型下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.装备类型选项= 根._小型选项栏.创建(自适应.创建(6,1,80,140,3,9),"") --分类选项
	self.灵饰内容显示 = {"部位","等级","主属","属性","附加1","附加2","附加3","附加4","数值1","数值2","数值3","数值4","特效","制造"}
    self.灵饰部位 = {"戒指","手镯","佩饰","耳饰"}
    self.灵饰输入资源={}
	for i=1,#self.灵饰内容显示 do
		self.灵饰输入资源[self.灵饰内容显示[i]]= 总控件:创建输入("灵饰"..self.灵饰内容显示[i],0,0,137,14,根,0XFF000000)
		self.灵饰输入资源[self.灵饰内容显示[i]]:置限制字数(20)
		if self.灵饰内容显示[i] == "部位" or  self.灵饰内容显示[i] == "主属" or  self.灵饰内容显示[i] == "附加1"
			or  self.灵饰内容显示[i] == "附加2"  or  self.灵饰内容显示[i] == "附加3"  or  self.灵饰内容显示[i] == "附加4" then
		  self.灵饰输入资源[self.灵饰内容显示[i]]:置限制字数(0)
		end
		self.灵饰输入资源[self.灵饰内容显示[i]]:置可视(true,true)
		self.灵饰输入资源[self.灵饰内容显示[i]]:置文字颜色(0xFF000000)
		self.灵饰输入资源[self.灵饰内容显示[i]]:置文本("")
	end
	self.灵饰部位下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰部位选项= 根._小型选项栏.创建(自适应.创建(6,1,140,90,3,9),"") --分类选项
	self.灵饰主属下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰主属选项= 根._小型选项栏.创建(自适应.创建(6,1,140,50,3,9),"") --分类选项
	self.灵饰附加1下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰附加1选项= 根._小型选项栏.创建(自适应.创建(6,1,140,250,3,9),"") --分类选项
	self.灵饰附加2下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰附加2选项= 根._小型选项栏.创建(自适应.创建(6,1,140,250,3,9),"") --分类选项
	self.灵饰附加3下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰附加3选项= 根._小型选项栏.创建(自适应.创建(6,1,140,250,3,9),"") --分类选项
	self.灵饰附加4下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.灵饰附加4选项= 根._小型选项栏.创建(自适应.创建(6,1,140,250,3,9),"") --分类选项


	self.角色修炼显示 = {"攻击修炼","法术修炼","防御修炼","抗法修炼"}
	self.角色生活显示 = {"强身术","冥想","强壮","暗器技巧","中药医理","烹饪技巧","打造技巧","裁缝技巧","炼金术","淬灵之术","养生之道","健身术"}
    self.角色强化显示 = {"人物伤害","人物防御","人物气血","人物法术","人物速度","人物固伤","人物治疗","宠物伤害","宠物防御","宠物气血","宠物灵力","宠物速度"}

    self.召唤兽修炼显示 = {"攻击控制力","法术控制力","防御控制力","抗法控制力","玩家等级"}




     self.角色修炼输入={}
    for i=1,#self.角色修炼显示 do
		self.角色修炼输入[self.角色修炼显示[i]]= 总控件:创建输入("角色"..self.角色修炼显示[i],0,0,100,14,根,0XFF000000)
		self.角色修炼输入[self.角色修炼显示[i]]:置限制字数(20)
		self.角色修炼输入[self.角色修炼显示[i]]:置可视(true,true)
		self.角色修炼输入[self.角色修炼显示[i]]:置文字颜色(0xFF000000)
		self.角色修炼输入[self.角色修炼显示[i]]:置文本("")
	end
	 self.角色生活输入={}
    for i=1,#self.角色生活显示 do
		self.角色生活输入[self.角色生活显示[i]]= 总控件:创建输入("角色"..self.角色生活显示[i],0,0,100,14,根,0XFF000000)
		self.角色生活输入[self.角色生活显示[i]]:置限制字数(20)
		self.角色生活输入[self.角色生活显示[i]]:置可视(true,true)
		self.角色生活输入[self.角色生活显示[i]]:置文字颜色(0xFF000000)
		self.角色生活输入[self.角色生活显示[i]]:置文本("")
	end
	 self.角色强化输入={}
    for i=1,#self.角色强化显示 do
		self.角色强化输入[self.角色强化显示[i]]= 总控件:创建输入("角色"..self.角色强化显示[i],0,0,100,14,根,0XFF000000)
		self.角色强化输入[self.角色强化显示[i]]:置限制字数(20)
		self.角色强化输入[self.角色强化显示[i]]:置可视(true,true)
		self.角色强化输入[self.角色强化显示[i]]:置文字颜色(0xFF000000)
		self.角色强化输入[self.角色强化显示[i]]:置文本("")
	end
	self.召唤兽修炼输入={}
    for i=1,#self.召唤兽修炼显示 do
		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]= 总控件:创建输入("召唤兽"..self.召唤兽修炼显示[i],0,0,130,14,根,0XFF000000)
		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置限制字数(20)
		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置可视(true,true)
		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置文字颜色(0xFF000000)
		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置文本("")
	end



    self.宝宝选择输入= 总控件:创建输入("宝宝选择选项",0,0,130,14,根,0XFF000000)
	self.宝宝选择输入:置限制字数(0)
	self.宝宝选择输入:置可视(true,true)
	self.宝宝选择输入:置文字颜色(0xFF000000)
	self.宝宝选择输入:置文本("获取召唤兽后在操作")
	self.已选择宝宝 = 0
	self.是否获取宝宝 = false

	self.宝宝选择下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.宝宝选择选项= 根._小型选项栏.创建(自适应.创建(6,1,220,250,3,9),"") --分类选项


    self.召唤兽属性显示={"等级","模型","种类","潜力","寿命","成长","攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}

    self.召唤兽技能显示={"技能01","技能02","技能03","技能04","技能05","技能06","技能07","技能08","技能09","技能10","技能11","技能12","技能13","技能14","技能15","技能16","技能17","技能18","技能19","技能20"}

	self.召唤兽属性输入={}
    for i=1,#self.召唤兽属性显示 do
		self.召唤兽属性输入[self.召唤兽属性显示[i]]= 总控件:创建输入("召唤兽"..self.召唤兽属性显示[i],0,0,217,14,根,0XFF000000)
		self.召唤兽属性输入[self.召唤兽属性显示[i]]:置限制字数(20)
		self.召唤兽属性输入[self.召唤兽属性显示[i]]:置可视(true,true)
		self.召唤兽属性输入[self.召唤兽属性显示[i]]:置文字颜色(0xFF000000)
		self.召唤兽属性输入[self.召唤兽属性显示[i]]:置文本("")
	end

	self.召唤兽技能输入={}
    for i=1,#self.召唤兽技能显示 do
		self.召唤兽技能输入[self.召唤兽技能显示[i]]= 总控件:创建输入("召唤兽1"..self.召唤兽技能显示[i],0,0,100,14,根,0XFF000000)
		self.召唤兽技能输入[self.召唤兽技能显示[i]]:置限制字数(20)
		self.召唤兽技能输入[self.召唤兽技能显示[i]]:置可视(true,true)
		self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文字颜色(0xFF000000)
		self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文本("")
	end

	self.召唤兽天生显示={"天生01","天生02","天生03","天生04"}
	self.召唤兽天生输入={}
    for i=1,#self.召唤兽天生显示 do
		self.召唤兽天生输入[self.召唤兽天生显示[i]]= 总控件:创建输入("召唤兽"..self.召唤兽技能显示[i],0,0,100,14,根,0XFF000000)
		self.召唤兽天生输入[self.召唤兽天生显示[i]]:置限制字数(20)
		self.召唤兽天生输入[self.召唤兽天生显示[i]]:置可视(true,true)
		self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文字颜色(0xFF000000)
		self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文本("")
	end
	self.召唤数据={}
	self.宝宝选项列表 = {}

	self.给予物品显示={"名称","数量","参数"}
	self.给予物品输入 = {}
	 for i=1,#self.给予物品显示 do
		self.给予物品输入[self.给予物品显示[i]]= 总控件:创建输入("物品"..self.给予物品显示[i],0,0,377,14,根,0XFF000000)
		self.给予物品输入[self.给予物品显示[i]]:置限制字数(200)
		self.给予物品输入[self.给予物品显示[i]]:置可视(true,true)
		self.给予物品输入[self.给予物品显示[i]]:置文字颜色(0xFF000000)
		self.给予物品输入[self.给予物品显示[i]]:置文本("")
	end


	self.给予低级宝石输入= 总控件:创建输入("宝石低等级",0,0,47,14,根,0XFF000000)
	self.给予低级宝石输入:置可视(true,true)
	self.给予低级宝石输入:置文字颜色(0xFF000000)
	self.给予低级宝石输入:置文本("")
	self.给予高级宝石输入= 总控件:创建输入("宝石高等级",0,0,47,14,根,0XFF000000)
	self.给予高级宝石输入:置可视(true,true)
	self.给予高级宝石输入:置文字颜色(0xFF000000)
	self.给予高级宝石输入:置文本("")
	self.宝石名称 ="选择宝石"
	self.宝石下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.宝石选项= 根._小型选项栏.创建(自适应.创建(6,1,85,176,3,9),"") --分类选项
	self.宝石名称选项 = {"星辉石","光芒石","月亮石","太阳石","舍利子","红玛瑙","黑宝石","神秘石"}


	self.充值卡数量输入= 总控件:创建输入("充值卡数量输入",0,0,67,14,根,0XFF000000)
	self.充值卡数量输入:置可视(true,true)
	self.充值卡数量输入:置文字颜色(0xFF000000)
	self.充值卡数量输入:置文本("")
	self.充值卡位数输入= 总控件:创建输入("充值卡位数输入",0,0,67,14,根,0XFF000000)
	self.充值卡位数输入:置可视(true,true)
	self.充值卡位数输入:置文字颜色(0xFF000000)
	self.充值卡位数输入:置文本("")
	self.充值卡自定义输入= 总控件:创建输入("充值卡自定义输入",0,0,277,14,根,0XFF000000)
	self.充值卡自定义输入:置可视(true,true)
	self.充值卡自定义输入:置文字颜色(0xFF000000)
	self.充值卡自定义输入:置文本("")
	self.充值卡下拉 = 按钮.创建(自适应.创建(21,4,18,19,4,3),0,0,4,true,true) --子类下拉按钮
	self.充值卡选项= 根._小型选项栏.创建(自适应.创建(6,1,330,370,3,9),"") --分类选项

	self.充值卡新建输入= 总控件:创建输入("充值卡新建输入",0,0,330,14,根,0XFF000000)
	self.充值卡新建输入:置可视(true,true)
	self.充值卡新建输入:置文字颜色(0xFF000000)
	self.充值卡新建输入:置文本("输入删除或新建充值类型名称")

	self.丰富文本说明 = 根._丰富文本(388,398)
	self.丰富文本说明:置默认颜色(0XFF000000)
	self.丰富文本说明:添加文本()
	self.丰富文本说明.滚动值 = self.丰富文本说明.行数量
	self.丰富文本说明:滚动(self.丰富文本说明.滚动值)
	self.丰富文本说明:滚动(-18)

	self.丰富文本聊天 = 根._丰富文本(738,110)
	self.丰富文本聊天:置默认颜色(0XFF000000)
	self.丰富文本聊天:添加文本()
	self.丰富文本聊天.滚动值 = self.丰富文本聊天.行数量
	self.丰富文本聊天:滚动(self.丰富文本聊天.滚动值)
	self.丰富文本聊天:滚动(-18)
	self.丰富文本聊天背景=自适应.创建(2,1,740,112,3,3)----丰富文本背景

	self.丰富文本道具 = 根._丰富文本(417,217)
	self.丰富文本道具:置默认颜色(0XFF000000)
	self.丰富文本道具:添加文本()
	self.丰富文本道具.滚动值 = self.丰富文本道具.行数量
	self.丰富文本道具:滚动(self.丰富文本道具.滚动值)
	self.丰富文本道具:滚动(-18)
	self.丰富文本道具背景=自适应.创建(2,1,420,220,3,3)----丰富文本背景

	self.发送信息 = {}
	self.选择数据 = ""
	self.聊天记录 = "聊天记录"
	self.获取卡号 = false
	self.充值类型 = "选择充值卡类型"
	self.卡号类型 = {}
	self.获取卡号类型 = false
	self.充值卡号 = {}



end

function 功能界面:显示(dt,x,y)
	self.资源[1]:显示(0,0)
	self.资源[2]:显示(0,50)
	self.丰富文本说明:更新(dt,x,y)
	self.丰富文本聊天:更新(dt,x,y)
	for i=3,9 do
		self.资源[i]:更新(x,y)
		self.资源[i]:显示(30+(i-3)*110,20,true,nil,nil,self.进程 == self.资源[i]:取文字(),2)

		if self.资源[i]:事件判断() then
			self.进程 =self.资源[i]:取文字()
			self.发送信息 = {}
			self.选择数据 = ""
	        self.充值数额:置可视(false,false)
		    self.充值数额:置文本("")
			self.修改密码:置可视(false,false)
		    self.坐骑名称:置可视(false,false)
		    self.修改密码:置文本("")
		    self.坐骑名称:置文本("")
		    self.公告输入:置可视(false,false)
	        self.服务输入:置可视(false,false)
	        self.公告输入:置文本("")
	        self.服务输入:置文本("")
		    for i=1,#self.装备内容显示 do
			   self.装备输入资源[self.装备内容显示[i]]:置可视(false,false)
			   self.装备输入资源[self.装备内容显示[i]]:置文本("")
	        end
	        for i=1,#self.灵饰内容显示 do
	        	self.灵饰输入资源[self.灵饰内容显示[i]]:置可视(false,false)
				self.灵饰输入资源[self.灵饰内容显示[i]]:置文本("")
			end
		    for i=1,#self.角色修炼显示 do
				self.角色修炼输入[self.角色修炼显示[i]]:置可视(false,false)
				self.角色修炼输入[self.角色修炼显示[i]]:置文本("")
			end
		    for i=1,#self.角色生活显示 do
		    	self.角色生活输入[self.角色生活显示[i]]:置可视(false,false)
				self.角色生活输入[self.角色生活显示[i]]:置文本("")
			end
		    for i=1,#self.角色强化显示 do
				self.角色强化输入[self.角色强化显示[i]]:置可视(false,false)
				self.角色强化输入[self.角色强化显示[i]]:置文本("")
			end
			for i=1,#self.召唤兽修炼显示 do
				self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置可视(false,false)
				self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置文本("")
			end
			self.宝宝选择输入:置可视(false,false)
			self.宝宝选择输入:置文本("获取召唤兽后在操作")
			self.已选择宝宝 = 0
			self.是否获取宝宝 = false
			self.召唤数据={}
		    for i=1,#self.召唤兽属性显示 do
				self.召唤兽属性输入[self.召唤兽属性显示[i]]:置可视(false,false)
				self.召唤兽属性输入[self.召唤兽属性显示[i]]:置文本("")
			end
		    for i=1,#self.召唤兽技能显示 do
				self.召唤兽技能输入[self.召唤兽技能显示[i]]:置可视(false,false)
				self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文本("")
			end
			for i=1,#self.召唤兽天生显示 do
				self.召唤兽天生输入[self.召唤兽天生显示[i]]:置可视(false,false)
				self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文本("")
			end

			 for i=1,#self.给予物品显示 do
				self.给予物品输入[self.给予物品显示[i]]:置可视(false,false)
				self.给予物品输入[self.给予物品显示[i]]:置文本("")
			end
			self.给予低级宝石输入:置可视(false,false)
			self.给予低级宝石输入:置文本("")
			self.给予高级宝石输入:置可视(false,false)
			self.给予高级宝石输入:置文本("")
			self.宝石名称 ="选择宝石"
			self.获取卡号 = false
	        self.充值类型 = "选择充值卡类型"
			self.充值卡数量输入:置可视(false,false)
			self.充值卡数量输入:置文本("")
			self.充值卡位数输入:置可视(false,false)
			self.充值卡位数输入:置文本("")
			self.充值卡自定义输入:置可视(false,false)
			self.充值卡自定义输入:置文本("")
			self.卡号类型 ={}
			self.获取卡号类型 = false
			self.充值卡号 = {}
			self.充值卡新建输入:置可视(false,false)
			self.充值卡新建输入:置文本("输入删除或新建充值类型名称")
			self.丰富文本说明:清空()
			self.丰富文本聊天:清空()
			self.丰富文本道具:清空()





		end
	end
	 if self.进程 ~= "游戏管理" then
		self.资源[10]:置宽高1(250,22)
		self.资源[10]:显示(90,60)
		zt:显示(10,62,"角色 I D:")
		self.角色id:置可视(true,true)
		self.角色id:置坐标(93,63)
	end



	if self.进程 == "充值操作" then --充值系统
	  self:充值操作显示(dt,x,y)
	elseif self.进程 == "账号操作" then
      self:账号操作显示(dt,x,y)
	elseif self.进程 == "定制装备" then
      self:定制装备显示(dt,x,y)
	elseif self.进程 == "游戏管理" then
	  self.角色id:置可视(false,false)
      self:游戏管理显示(dt,x,y)
	elseif self.进程 == "角色管理" then
	  self:角色管理显示(dt,x,y)
	elseif self.进程 == "宝宝管理" then
	  self:宝宝管理显示(dt,x,y)
	elseif self.进程 == "赠送道具" then
	  self:赠送道具显示(dt,x,y)
	end
	self.控件类:更新(dt,x,y)
  	self.控件类:显示(x,y)
end

function 功能界面:赠送道具显示(dt,x,y)
	self.道具按钮:更新(x,y)
	self.道具按钮:显示(700,150,true)
	self.宝石按钮:更新(x,y)
	self.宝石按钮:显示(610,150,true)

	self.给予低级宝石输入:置可视(true,true)
	self.给予高级宝石输入:置可视(true,true)
	self.资源[10]:置宽高1(85,22)
	self.资源[10]:显示(360 ,150)
	self.资源[10]:置宽高1(50,22)
	self.资源[10]:显示(485 ,150)
	self.资源[10]:显示(555 ,150)
	self.给予低级宝石输入:置坐标(487,153)
	self.给予高级宝石输入:置坐标(557,153)
	zt:显示(450,153,"等级       ->")
	zt4:显示(363,153,self.宝石名称)
	zt3:显示(360,190,"    名称必须填写道具实际名称,有等级的一律要在数量写入\n等级,如需填写技能，品质等全部需要在参数填写")
	self.宝石下拉:更新(x,y)
	self.宝石下拉:显示(425,152)
	self.丰富文本道具背景:显示(360,240)
	self.丰富文本道具:显示(362,242)
	self.写出本地:更新(x,y)
	self.获取充值类型:更新(x,y)
	self.生成CDK卡号:更新(x,y)
	self.获取充值卡号:更新(x,y)
	self.生成自定义CDK卡号:更新(x,y)
	self.新建充值类型:更新(x,y)
	self.删除充值类型:更新(x,y)






	if self.丰富文本道具背景:是否选中(x,y) and 引擎.取鼠标滚轮() >0 then--鼠标上滚动
			self.丰富文本道具:滚动(1)
	elseif self.丰富文本道具背景:是否选中(x,y) and 引擎.取鼠标滚轮() <0  then--鼠标下滚动
		   self.丰富文本道具:滚动(-1)
	end
	if self.获取卡号 then
		self.写出本地:显示(700,470,true)
	end




    self.获取充值类型:显示(30,140,true)
    self.获取充值卡号:显示(190,140,true)
    self.生成CDK卡号:显示(235,170,true)
    self.生成自定义CDK卡号:显示(100,230,true)


	zt:显示(113,90,"自动充值操作")
	self.资源[10]:置宽高1(330,22)
	self.资源[10]:显示(10 ,110)
	self.资源[10]:显示(10 ,260)
	zt4:显示(12,113,self.充值类型)
	self.充值卡下拉:更新(x,y)


	zt:显示(10,173,"数量")
	zt:显示(123,173,"位数")
	self.资源[10]:置宽高1(70,22)
	self.资源[10]:显示(45,170)
	self.资源[10]:显示(160,170)
	self.充值卡数量输入:置可视(true,true)
	self.充值卡位数输入:置可视(true,true)
	self.充值卡自定义输入:置可视(true,true)
	self.充值卡新建输入:置可视(true,true)
	self.充值卡数量输入:置坐标(47,173)
	self.充值卡位数输入:置坐标(162,173)
    self.资源[10]:置宽高1(280,22)
	self.资源[10]:显示(60,200)
	zt:显示(10,203,"自定义")
	self.充值卡自定义输入:置坐标(63,203)

    self.充值卡新建输入:置坐标(12,263)
    self.新建充值类型:显示(30,290,true)
    self.删除充值类型:显示(190,290,true)

    zt3:显示(10,350,"   生成cdk卡号、生成自定义卡号需要先获取充\n值类型(充值文件名)，获取文件名后选择需要的\n类型获取卡号，可以在右下角看到当前文件中卡\n号，如需要可以写出到本地，在工具跟目录卡号\n数据文件中查看,删除卡号一样需要先选取充值\n类型，新建类型可以直接操作无需前置条件")





		local xx = 0
	    local yy = 0
		for i=1,#self.给予物品显示 do
			zt:显示(360+ xx * 190,62 + yy * 30,self.给予物品显示[i])
			self.资源[10]:置宽高1(380,22)
	        self.资源[10]:显示(400 + xx * 190,60 + yy * 30)
			self.给予物品输入[self.给予物品显示[i]]:置坐标(402 + xx * 190,63 + yy * 30)
	     	self.给予物品输入[self.给予物品显示[i]]:置可视(true,true)
	     	if self.给予物品显示[i]~="数量" then
	     	    if self.给予物品输入[self.给予物品显示[i]]._已碰撞 and 引擎.按键按住(KEY.CTRL) and 引擎.按键按住(KEY.V) then
	     			self.给予物品输入[self.给予物品显示[i]]:置文本(取剪贴板())
	     		end
	     	end

	        xx = xx + 1
	        if xx >= 1 then
	           xx = 0
	           yy = yy + 1
	         end
		end

    self.宝石选项:显示(360,172,x,y,true)
    if  self.宝石下拉:事件判断() then
      	self.宝石选项:打开(self.宝石名称选项)
    end
    if self.宝石选项:事件判断() then
	   self.宝石名称=self.宝石选项.弹出事件
	   self.宝石选项.弹出事件=nil
	end

	if self.获取卡号类型 then
		 self.充值卡下拉:显示(320,113)
		 self.充值卡选项:显示(10,125,x,y,true)

	    if  self.充值卡下拉:事件判断() then
	      	self.充值卡选项:打开(self.卡号类型)
	    end

	    if self.充值卡选项:事件判断() then
		   self.充值类型=self.充值卡选项.弹出事件
		   self.充值卡选项.弹出事件=nil
		end

	end

	 if  self.道具按钮:事件判断() then
        	self.发送信息 = {}
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
				elseif self.给予物品输入["名称"]:取文本() == ""  then
					tp.提示:写入("#Y/名称还未输入无法操作")
				elseif  self.给予物品输入["数量"]:取文本() ~= "" and tonumber(self.给予物品输入["数量"]:取文本()) == nil then
			  	 tp.提示:写入("#Y/数量输入错误")
			   else
			   	for i=1,#self.给予物品显示 do
				   	if	self.给予物品输入[self.给予物品显示[i]]:取文本() ~= "" and self.给予物品输入[self.给予物品显示[i]]:取文本() ~=nil then
				   		self.发送信息[self.给予物品显示[i]] = self.给予物品输入[self.给予物品显示[i]]:取文本()
				   	end
			   	end
	              发送数据(9,{文本=self.道具按钮:取文字(),玩家id=self.角色id:取文本(),给予数据=self.发送信息})
	           end
	     elseif self.宝石按钮:事件判断() then
	     	self.发送信息 = {}
	     	 if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			  elseif self.宝石名称=="选择宝石" then
			  	 tp.提示:写入("#Y/还未选择宝石")
			  elseif  self.给予低级宝石输入:取文本() == "" or tonumber(self.给予低级宝石输入:取文本()) == nil then
			  	 tp.提示:写入("#Y/最低等级输入错误")
			  elseif  self.给予高级宝石输入:取文本() ~= "" and tonumber(self.给予高级宝石输入:取文本()) == nil then
			  	 tp.提示:写入("#Y/最高等级输入错误")
			   else
			   	self.发送信息.名称 = self.宝石名称
			   	self.发送信息.最小等级 = self.给予低级宝石输入:取文本()
			   	self.发送信息.最大等级 = self.给予高级宝石输入:取文本()
		     	发送数据(9,{文本=self.宝石按钮:取文字(),玩家id=self.角色id:取文本(),给予数据=self.发送信息})
		     end

		 elseif self.写出本地:事件判断() then
		      local 生成CDK文本 =""
		      for n=1,#self.充值卡号 do--个数
		        生成CDK文本 = 生成CDK文本.."\n第"..n.."个:"..self.充值卡号[n]
		      end
             写出文件("卡号数据/"..取年月日(os.time()).."获取的CDK.txt", 生成CDK文本)
             tp.提示:写入("#Y/写出完毕请到跟目录卡号数据中查看")
		 elseif self.获取充值卡号:事件判断() then
		 	if self.充值类型 == "选择充值卡类型" then
         		tp.提示:写入("#Y/请先获取充值卡类型")
           else

           	发送数据(9,{文本=self.获取充值卡号:取文字(),生成文件=self.充值类型})
           end




         elseif self.获取充值类型:事件判断() then
         	发送数据(9,{文本=self.获取充值类型:取文字()})
         elseif self.生成CDK卡号:事件判断() then
         	if self.充值类型 == "选择充值卡类型" then
         		tp.提示:写入("#Y/请先获取充值卡类型")
         	elseif  self.充值卡数量输入:取文本() ~= "" and tonumber(self.充值卡数量输入:取文本()) == nil then
         	    tp.提示:写入("#Y/充值数量输入类型错误")
         	 elseif  self.充值卡位数输入:取文本() ~= "" and tonumber(self.充值卡位数输入:取文本()) == nil then
         	    tp.提示:写入("#Y/充值位数输入类型错误")
           else
           	self.发送信息 = {}
           	self.发送信息.数量 = self.充值卡数量输入:取文本()
           	self.发送信息.位数 = self.充值卡位数输入:取文本()

           	发送数据(9,{文本=self.生成CDK卡号:取文字(),生成数据=self.发送信息,生成文件=self.充值类型})
           end

           elseif self.生成自定义CDK卡号:事件判断() then
           	if self.充值类型 == "选择充值卡类型" then
         		tp.提示:写入("#Y/请先获取充值卡类型")
         	elseif  self.充值卡自定义输入:取文本() == "" or self.充值卡自定义输入:取文本() == nil then
         	    tp.提示:写入("#Y/自定义卡号未输入")
            else
           		self.发送信息 = {}
             	self.发送信息.自定义 = self.充值卡自定义输入:取文本()
             	发送数据(9,{文本=self.生成自定义CDK卡号:取文字(),生成数据=self.发送信息,生成文件=self.充值类型})
            end

            elseif self.新建充值类型:事件判断() then
	            if  self.充值卡新建输入:取文本() == "输入删除或新建充值类型名称" or self.充值卡新建输入:取文本() == "" then
	         	    tp.提示:写入("#Y/充值卡类型未输入")
	            else
	             	发送数据(9,{文本=self.新建充值类型:取文字(),生成文件=self.充值卡新建输入:取文本()})
	            end

             elseif self.删除充值类型:事件判断() then
             	if  self.充值卡新建输入:取文本() == "输入删除或新建充值类型名称" or self.充值卡新建输入:取文本() == "" then
	         	    tp.提示:写入("#Y/充值卡号未输入")
	         	 elseif self.充值类型 == "选择充值卡类型" then
	         	 	tp.提示:写入("#Y/请先获取充值卡类型")
	            else
	             	发送数据(9,{文本=self.删除充值类型:取文字(),生成文件=self.充值类型,生成卡号=self.充值卡新建输入:取文本()})
	            end


        end




end


function 功能界面:宝宝管理显示(dt,x,y)
	self.获取宝宝信息按钮:更新(x,y)
	self.获取宝宝信息按钮:显示(350,62,true)
	self.资源[10]:置宽高1(220,22)
	self.资源[10]:显示(470,62)
	self.宝宝选择输入:置坐标(472,66)
	self.宝宝选择输入:置可视(true,true)
	self.宝宝修改按钮:更新(x,y)
	self.宝宝修改按钮:显示(700,62,true)
	self.宝宝选择下拉:更新(x,y)



    self.资源[2]:显示(0,95)
	    local xx = 0
	    local yy = 0
		for i=1,#self.召唤兽属性显示 do
			zt:显示(20+ xx * 190,114 + yy * 40,self.召唤兽属性显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,110 + yy * 40)
			self.召唤兽属性输入[self.召唤兽属性显示[i]]:置坐标(92 + xx * 190,113 + yy * 40)
	     	self.召唤兽属性输入[self.召唤兽属性显示[i]]:置可视(true,true)
	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
		end

	 self.资源[2]:显示(0,225)
       xx = 0
       yy = 4
		for i=1,#self.召唤兽技能显示 do
			zt:显示(20+ xx * 190,84 + yy * 40,self.召唤兽技能显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,80 + yy * 40)
			self.召唤兽技能输入[self.召唤兽技能显示[i]]:置坐标(92 + xx * 190,83 + yy * 40)
	     	self.召唤兽技能输入[self.召唤兽技能显示[i]]:置可视(true,true)
     		if 	self.召唤兽技能输入[self.召唤兽技能显示[i]]._已碰撞 and 引擎.按键按住(KEY.CTRL) and 引擎.按键按住(KEY.V) then
     			self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文本(取剪贴板())
     		end

	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
		end
	  self.资源[2]:显示(0,440)
	   xx = 0
       yy = 9

	  for i=1,#self.召唤兽天生显示 do
		  zt:显示(20+ xx * 190,104 + yy * 40,self.召唤兽天生显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,100 + yy * 40)
			self.召唤兽天生输入[self.召唤兽天生显示[i]]:置坐标(92 + xx * 190,103 + yy * 40)
	     	self.召唤兽天生输入[self.召唤兽天生显示[i]]:置可视(true,true)
	     	if 	self.召唤兽天生输入[self.召唤兽天生显示[i]]._已碰撞 and 引擎.按键按住(KEY.CTRL) and 引擎.按键按住(KEY.V) then
     			self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文本(取剪贴板())
     		end

	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
	 end

	 if self.是否获取宝宝 and self.召唤数据~=nil then
		self.宝宝选择下拉:显示(670,64)
	    self.宝宝选择选项:显示(470,84,x,y,true)
	    if  self.宝宝选择下拉:事件判断() then
      	  self.宝宝选择选项:打开(self.宝宝选项列表)
       end
      if self.宝宝选择选项:事件判断() then
		  self.宝宝选择输入:置文本(self.宝宝选择选项.弹出事件)
		   local 临时序号=分割文本(self.宝宝选择选项.弹出事件,"-")
		   self.已选择宝宝 =临时序号[1]+0
		   self:召唤兽信息嵌入(self.已选择宝宝)
		  self.宝宝选择选项.弹出事件=nil
	  end
	end



     if  self.宝宝修改按钮:事件判断() then
        	self.发送信息 = {}
        	self.发送信息.属性 = {}
        	self.发送信息.技能 = {}
        	self.发送信息.天生 = {}
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
				elseif self.是否获取宝宝 == false then
					tp.提示:写入("#Y/请重新获取宝宝")
				elseif self.已选择宝宝==0 then
					tp.提示:写入("#Y/请重新选择宝宝")
			   else
			   	   for i=1,#self.召唤兽属性显示 do
						if self.召唤兽属性输入[self.召唤兽属性显示[i]]:取文本() ~= "" and self.召唤兽属性输入[self.召唤兽属性显示[i]]:取文本() ~= nil then
							self.发送信息.属性[self.召唤兽属性显示[i]] = self.召唤兽属性输入[self.召唤兽属性显示[i]]:取文本()
							self.召唤兽属性输入[self.召唤兽属性显示[i]]:置文本("")
						end
					end
					for i=1,#self.召唤兽技能显示 do
						if self.召唤兽技能输入[self.召唤兽技能显示[i]]:取文本() ~= "" and self.召唤兽技能输入[self.召唤兽技能显示[i]]:取文本() ~= nil then
							self.发送信息.技能[i] = self.召唤兽技能输入[self.召唤兽技能显示[i]]:取文本()
							self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文本("")
						end
					end

					for i=1,#self.召唤兽天生显示 do
						if self.召唤兽天生输入[self.召唤兽天生显示[i]]:取文本() ~= "" and self.召唤兽天生输入[self.召唤兽天生显示[i]]:取文本() ~= nil then
							self.发送信息.天生[i] = self.召唤兽天生输入[self.召唤兽天生显示[i]]:取文本()
							self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文本("")
						end
					end


	            	发送数据(8,{文本=self.角色修改按钮:取文字(),玩家id=self.角色id:取文本(),修改数据=self.发送信息,召唤兽编号 = self.已选择宝宝})
	            	self.是否获取宝宝 = false
	            	self.宝宝选择输入:置文本("获取召唤兽后在操作")
	           end
	     elseif self.获取宝宝信息按钮:事件判断() then
	     	 if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			   else
		     	发送数据(8,{文本=self.获取宝宝信息按钮:取文字(),玩家id=self.角色id:取文本()})
		     end

        end








end


function 功能界面:角色管理显示(dt,x,y)
	self.资源[2]:显示(0,95)

	    local xx = 0
	    local yy = 0
		for i=1,#self.角色修炼显示 do
			zt:显示(20+ xx * 190,114 + yy * 40,self.角色修炼显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,110 + yy * 40)
			self.角色修炼输入[self.角色修炼显示[i]]:置坐标(92 + xx * 190,113 + yy * 40)
	     	self.角色修炼输入[self.角色修炼显示[i]]:置可视(true,true)
	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
		end
        self.资源[2]:显示(0,145)
        xx = 0
        yy = 1
		for i=1,#self.角色生活显示 do
			zt:显示(20+ xx * 190,124 + yy * 40,self.角色生活显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,120 + yy * 40)
			self.角色生活输入[self.角色生活显示[i]]:置坐标(92 + xx * 190,123 + yy * 40)
	     	self.角色生活输入[self.角色生活显示[i]]:置可视(true,true)
	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
		end
       self.资源[2]:显示(0,275)
       xx = 0
       yy = 4
		for i=1,#self.角色强化显示 do
			zt:显示(20+ xx * 190,134 + yy * 40,self.角色强化显示[i])
			self.资源[10]:置宽高1(100,22)
	        self.资源[10]:显示(90 + xx * 190,130 + yy * 40)
			self.角色强化输入[self.角色强化显示[i]]:置坐标(92 + xx * 190,133 + yy * 40)
	     	self.角色强化输入[self.角色强化显示[i]]:置可视(true,true)
	        xx = xx + 1
	        if xx >= 4 then
	           xx = 0
	           yy = yy + 1
	         end
		end
      self.资源[2]:显示(0,405)
       xx = 0
       yy = 7
	  for i=1,#self.召唤兽修炼显示 do
			zt:显示(20+ xx * 260,144 + yy * 40,self.召唤兽修炼显示[i])
			self.资源[10]:置宽高1(130,22)
	        self.资源[10]:显示(110 + xx * 260,140 + yy * 40)
			self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置坐标(112 + xx * 260,143 + yy * 40)
	     	self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置可视(true,true)
	        xx = xx + 1
	        if xx >= 3 then
	           xx = 0
	           yy = yy + 1
	         end
		end
    self.角色修改按钮:更新(x,y)
	self.角色修改按钮:显示(680,463,true)
	self.获取角色信息按钮:更新(x,y)
	self.恢复角色道具按钮:更新(x,y)
	self.获取角色信息按钮:显示(350,62,true)
	self.恢复角色道具按钮:显示(470,62,true)


	if  self.角色修改按钮:事件判断() then
        	self.发送信息 = {}
        	self.发送信息.角色修炼 = {}
        	self.发送信息.角色生活 = {}
        	self.发送信息.角色强化 = {}
        	self.发送信息.召唤兽修炼 = {}
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			   else
			   	   for i=1,#self.角色修炼显示 do
						if self.角色修炼输入[self.角色修炼显示[i]]:取文本() ~= "" and tonumber(self.角色修炼输入[self.角色修炼显示[i]]:取文本()) ~= nil then
							self.发送信息.角色修炼[self.角色修炼显示[i]] = self.角色修炼输入[self.角色修炼显示[i]]:取文本()
							self.角色修炼输入[self.角色修炼显示[i]]:置文本("")
						end
					end
					for i=1,#self.角色生活显示 do
						if self.角色生活输入[self.角色生活显示[i]]:取文本() ~= "" and tonumber(self.角色生活输入[self.角色生活显示[i]]:取文本()) ~= nil then
							self.发送信息.角色生活[self.角色生活显示[i]] = self.角色生活输入[self.角色生活显示[i]]:取文本()
							self.角色生活输入[self.角色生活显示[i]]:置文本("")
						end
					end

					for i=1,#self.角色强化显示 do
						if self.角色强化输入[self.角色强化显示[i]]:取文本() ~= "" and tonumber(self.角色强化输入[self.角色强化显示[i]]:取文本()) ~= nil then
							self.发送信息.角色强化[self.角色强化显示[i]] = self.角色强化输入[self.角色强化显示[i]]:取文本()
							self.角色强化输入[self.角色强化显示[i]]:置文本("")
						end
					end
					for i=1,#self.召唤兽修炼显示 do
						if self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:取文本() ~= "" and tonumber(self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:取文本()) ~= nil then
							self.发送信息.召唤兽修炼[self.召唤兽修炼显示[i]] = self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:取文本()
							self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置文本("")
						end
					end

	            	发送数据(7,{文本=self.角色修改按钮:取文字(),玩家id=self.角色id:取文本(),修改数据=self.发送信息})

	           end
	     elseif self.获取角色信息按钮:事件判断() then
	     	 if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			   else
		     	发送数据(7,{文本=self.获取角色信息按钮:取文字(),玩家id=self.角色id:取文本()})
		     end
		elseif self.恢复角色道具按钮:事件判断() then
	     	 if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			   else
		     	发送数据(7,{文本=self.恢复角色道具按钮:取文字(),玩家id=self.角色id:取文本()})
		     end

        end








end


function 功能界面:充值操作显示(dt,x,y)
	self.资源[10]:置宽高1(250,22)
	self.资源[10]:显示(90,90)
	zt:显示(10,92,"充值数额:")
	self.充值数额:置坐标(93,93)
	self.充值数额:置可视(true,true)
	self.资源[11]:置宽高(390,400)
	self.资源[11]:显示(380,80)
	self.丰富文本说明:置高度(398)
	self.丰富文本说明:显示(382,82)
	if self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() >0 then--鼠标上滚动
		if self.丰富文本说明.滚动值 <= #self.丰富文本说明.显示表 -18 then
			self.丰富文本说明:滚动(1)
		end
	elseif self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() <0  then--鼠标下滚动
		if self.丰富文本说明.滚动值 > 0 then
				self.丰富文本说明:滚动(-1)
		end
	end
        local xx = 0
        local yy = 0
        for i=1,#self.充值按钮 do
            self.充值按钮[i]:更新(x,y)
            self.充值按钮[i]:显示(20 + xx * 120,135 + yy * 45,true)
            xx = xx + 1
            if xx > 2 then
                xx = 0
                yy = yy + 1
            end
            if  self.充值按钮[i]:事件判断() then
            	if (self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil) and self.充值按钮[i]:取文字()~= "充值记录" then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
				elseif (self.充值数额:取文本() == "" or tonumber(self.充值数额:取文本()) == nil) and self.充值按钮[i]:取文字()~= "充值记录" then
				    tp.提示:写入("#Y/请输入正确的充值的充值数额,充值数额为纯数字")
				else
	            	发送数据(2,{文本=self.充值按钮[i]:取文字(),玩家id=self.角色id:取文本(),数额=self.充值数额:取文本()})
	            end

            end
        end

    zt3:显示(10,385,"    玩家ID必须填写,非充值记录查询充值数额\n必须填写并是数字。\n    查询充值记录充值数额需填写玩家账号,\nID可以不填写。")


end

function 功能界面:账号操作显示(dt,x,y)
	self.资源[10]:置宽高1(250,22)
	self.资源[10]:显示(90,90)
	zt:显示(10,92,"玩家账号:")
	self.充值数额:置坐标(93,93)
	self.充值数额:置可视(true,true)
	self.资源[11]:置宽高(390,400)
	self.资源[11]:显示(380,80)
    zt:显示(10,275,"密码输入:")
	self.修改密码:置坐标(13,303)
	self.修改密码:置可视(true,true)
	self.资源[10]:置宽高1(240,22)
	self.资源[10]:显示(10,300)
    zt:显示(10,335,"输入称谓:")
	self.坐骑名称:置坐标(13,363)
	self.坐骑名称:置可视(true,true)
	self.资源[10]:置宽高1(240,22)
	self.资源[10]:显示(10,360)
	self.密码按钮:更新(x,y)
	self.坐骑按钮:更新(x,y)
	self.密码按钮:显示(260,300,true)
	self.坐骑按钮:显示(260,360,true)
	self.丰富文本说明:置高度(398)
	self.丰富文本说明:显示(382,82)
	if self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() >0 then--鼠标上滚动
		if self.丰富文本说明.滚动值 <= #self.丰富文本说明.显示表 -18 then
			self.丰富文本说明:滚动(1)
		end
	elseif self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() <0  then--鼠标下滚动
		if self.丰富文本说明.滚动值 > 0 then
				self.丰富文本说明:滚动(-1)
		end
	end
        local xx = 0
        local yy = 0
        for i=1,#self.账号按钮 do
            self.账号按钮[i]:更新(x,y)
            self.账号按钮[i]:显示(20 + xx * 120,135 + yy * 45,true)
            xx = xx + 1
            if xx > 2 then
                xx = 0
                yy = yy + 1
            end
            if  self.账号按钮[i]:事件判断() then
            	if (self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil) and i>=1 and i<=3 then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
				elseif self.充值数额:取文本() == "" and i>3  then
				    tp.提示:写入("#Y/该操作必须输入账号")
				else
	            	发送数据(3,{文本=self.账号按钮[i]:取文字(),玩家id=self.角色id:取文本(),账号=self.充值数额:取文本()})
	            end

            end
        end


        if  self.密码按钮:事件判断() then
        	     if self.充值数额:取文本() == ""  then
				    tp.提示:写入("#Y/该操作必须输入账号")
				  elseif self.修改密码:取文本() == ""   then
				  	 tp.提示:写入("#Y/该操作必须输入修改的密码")
				else
	            	发送数据(3,{文本=self.密码按钮:取文字(),密码=self.修改密码:取文本(),账号=self.充值数额:取文本()})
	            end
        elseif  self.坐骑按钮:事件判断() then
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			    elseif self.坐骑名称:取文本() == ""   then
				  	 tp.提示:写入("#Y/该操作必须输入坐骑名称")
				else
	            	发送数据(3,{文本=self.坐骑按钮:取文字(),玩家id=self.角色id:取文本(),坐骑名称=self.坐骑名称:取文本()})
	            end

        end


    zt3:显示(10,400,"    前3项和给予坐骑功能必须填写玩家ID。\n\n    其他功能必须填写账号才能进行操作。\n\n    解封IP和封禁IP玩家账号需填写操作IP。")


end



function 功能界面:定制装备显示(dt,x,y)
	self.发送装备:更新(x,y)
	self.发送装备:显示(250,460,true)
	self.装备类型下拉:更新(x,y)
	self.灵饰部位下拉:更新(x,y)
	self.灵饰主属下拉:更新(x,y)
	self.灵饰附加1下拉:更新(x,y)
	self.灵饰附加2下拉:更新(x,y)
	self.灵饰附加3下拉:更新(x,y)
	self.灵饰附加4下拉:更新(x,y)
	self.发送灵饰:更新(x,y)
	self.发送灵饰:显示(650,375,true)
	self.资源[11]:置宽高(390,80)
	self.资源[11]:显示(380,410)
	self.丰富文本说明:置高度(80)
	self.丰富文本说明:显示(382,412)



	if self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() >0 then--鼠标上滚动
		if self.丰富文本说明.滚动值 <= #self.丰富文本说明.显示表 -18 then
			self.丰富文本说明:滚动(1)
		end
	elseif self.资源[11]:是否选中(x,y) and 引擎.取鼠标滚轮() <0  then--鼠标下滚动
		if self.丰富文本说明.滚动值 > 0 then
				self.丰富文本说明:滚动(-1)
		end
	end




    local xx = 0
    local yy = 0
	for i=1,#self.装备内容显示 do
		zt:显示(20+ xx * 170,104 + yy * 40,self.装备内容显示[i])
		self.资源[10]:置宽高1(100,22)
        self.资源[10]:显示(60 + xx * 170,100 + yy * 40)
		self.装备输入资源[self.装备内容显示[i]]:置坐标(62 + xx * 170,103 + yy * 40)
     	self.装备输入资源[self.装备内容显示[i]]:置可视(true,true)
     	if self.装备内容显示[i] == "特效" or self.装备内容显示[i]=="特技" or self.装备内容显示[i]=="制造" or self.装备内容显示[i]=="专用" then
     		if 	self.装备输入资源[self.装备内容显示[i]]._已碰撞 and 引擎.按键按住(KEY.CTRL) and 引擎.按键按住(KEY.V) then
     			self.装备输入资源[self.装备内容显示[i]]:置文本(取剪贴板())
     		end
     	end
        xx = xx + 1
        if xx >= 2 then
           xx = 0
           yy = yy + 1
         end
	end


      self.装备类型下拉:显示(310,102)
      self.装备类型选项:显示(235,124,x,y,true)
      if  self.装备类型下拉:事件判断() then
      	 self.装备类型选项:打开(self.装备类型)
      end
      if self.装备类型选项:事件判断() then
		  self.装备输入资源["类型"]:置文本(self.装备类型选项.弹出事件)
		  self.装备类型选项.弹出事件=nil
	  end

        if  self.发送装备:事件判断() then
        	self.发送信息 = {}
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			    elseif self.装备输入资源["等级"]:取文本() == ""  or tonumber(self.装备输入资源["等级"]:取文本()) == nil  then
				  	 tp.提示:写入("#Y/装备等级未输入")
				elseif self.装备输入资源["类型"]:取文本() == ""  then
				  	 tp.提示:写入("#Y/装备类型未选择")
			   else
			   	   for i=1,#self.装备内容显示 do
						if self.装备输入资源[self.装备内容显示[i]]:取文本() ~= "" and self.装备输入资源[self.装备内容显示[i]]:取文本() ~= nil then
							self.发送信息[self.装备内容显示[i]] = self.装备输入资源[self.装备内容显示[i]]:取文本()
						end
					end
	            	发送数据(4,{文本=self.发送装备:取文字(),玩家id=self.角色id:取文本(),装备数据=self.发送信息})
	           end

        end


     zt:显示(540,70 ,"定制灵饰")
     local zx = 0
     local zy = 0
    for i=1,#self.灵饰内容显示 do
    	zt:显示(390+ zx * 200,104 + zy * 40,self.灵饰内容显示[i])
    	self.资源[10]:置宽高1(140,22)
    	self.资源[10]:显示(432 + zx * 200,100 + zy * 40)
    	self.灵饰输入资源[self.灵饰内容显示[i]]:置坐标(433 + zx * 200,103 + zy * 40)
	    self.灵饰输入资源[self.灵饰内容显示[i]]:置可视(true,true)
	    if self.灵饰内容显示[i] == "特效"  or self.灵饰内容显示[i]=="制造"  then
     		if 	self.灵饰输入资源[self.灵饰内容显示[i]]._已碰撞 and 引擎.按键按住(KEY.CTRL) and 引擎.按键按住(KEY.V) then
     			self.灵饰输入资源[self.灵饰内容显示[i]]:置文本(取剪贴板())
     		end
     	end
	    zx = zx + 1
        if zx >= 2 then
           zx = 0
           zy = zy + 1
         end

	end




        self.灵饰部位下拉:显示(552,102)
        self.灵饰部位选项:显示(432,124,x,y,true)
        if  self.灵饰部位下拉:事件判断() then
      	    self.灵饰部位选项:打开(self.灵饰部位)
        end
        if self.灵饰部位选项:事件判断() then
		   self.灵饰输入资源["部位"]:置文本(self.灵饰部位选项.弹出事件)
		   self.选择数据 = self.灵饰部位选项.弹出事件
		   self.灵饰部位选项.弹出事件=nil
	    end
	    if self.选择数据~="" then
	    	self.灵饰主属下拉:显示(552,142)
	        self.灵饰主属选项:显示(432,164,x,y,true)
	        self.灵饰附加1下拉:显示(552,182)
	        self.灵饰附加1选项:显示(432,204,x,y,true)

	        self.灵饰附加2下拉:显示(752,182)
	        self.灵饰附加2选项:显示(632,204,x,y,true)

	        self.灵饰附加3下拉:显示(552,222)
	        self.灵饰附加3选项:显示(432,244,x,y,true)

	        self.灵饰附加4下拉:显示(752,222)
	        self.灵饰附加4选项:显示(632,244,x,y,true)
	        local 主属列表 = {}
	        local 附属列表 = {}
	        if self.选择数据 == "戒指" then
	        	主属列表={"伤害","防御"}
	        	附属列表 ={"固定伤害","法术伤害","伤害","封印命中等级","法术暴击等级","物理暴击等级","狂暴等级","穿刺等级","法术伤害结果","治疗能力","速度"}
	        elseif self.选择数据 == "手镯" then
	        	主属列表={"封印命中等级","抵抗封印等级"}
                附属列表 ={"气血回复效果","气血","防御","抗法术暴击等级","格挡值","法术防御","抗物理暴击等级"}
	        elseif self.选择数据 == "佩饰" then
	        	主属列表={"速度"}
	        	附属列表 ={"气血回复效果","气血","防御","抗法术暴击等级","格挡值","法术防御","抗物理暴击等级"}
	        elseif self.选择数据 == "耳饰" then
	        	主属列表={"法术伤害","法术防御"}
	        	附属列表 ={"固定伤害","法术伤害","伤害","封印命中等级","法术暴击等级","物理暴击等级","狂暴等级","穿刺等级","法术伤害结果","治疗能力","速度"}
	        end
	            --do

		   if  self.灵饰主属下拉:事件判断() then
	      	   self.灵饰主属选项:打开(主属列表)
	       end
	       if self.灵饰主属选项:事件判断() then
			  self.灵饰输入资源["主属"]:置文本(self.灵饰主属选项.弹出事件)
			  self.灵饰主属选项.弹出事件=nil
		   end
		   if  self.灵饰附加1下拉:事件判断() then
	      	   self.灵饰附加1选项:打开(附属列表)
	       end
	       if self.灵饰附加1选项:事件判断() then
			  self.灵饰输入资源["附加1"]:置文本(self.灵饰附加1选项.弹出事件)
			  self.灵饰附加1选项.弹出事件=nil
		   end
		   if  self.灵饰附加2下拉:事件判断() then
	      	   self.灵饰附加2选项:打开(附属列表)
	       end
	       if self.灵饰附加2选项:事件判断() then
			  self.灵饰输入资源["附加2"]:置文本(self.灵饰附加2选项.弹出事件)
			  self.灵饰附加2选项.弹出事件=nil
		   end
		   if  self.灵饰附加3下拉:事件判断() then
	      	   self.灵饰附加3选项:打开(附属列表)
	       end
	       if self.灵饰附加3选项:事件判断() then
			  self.灵饰输入资源["附加3"]:置文本(self.灵饰附加3选项.弹出事件)
			  self.灵饰附加3选项.弹出事件=nil
		   end
		    if  self.灵饰附加4下拉:事件判断() then
	      	   self.灵饰附加4选项:打开(附属列表)
	       end
	       if self.灵饰附加4选项:事件判断() then
			  self.灵饰输入资源["附加4"]:置文本(self.灵饰附加4选项.弹出事件)
			  self.灵饰附加4选项.弹出事件=nil
		   end

	    end


	if  self.发送灵饰:事件判断() then
        	self.发送信息 = {}
        	   if self.角色id:取文本() == "" or tonumber(self.角色id:取文本()) == nil then
				    tp.提示:写入("#Y/请输入正确的角色ID,角色ID为纯数字")
			    elseif self.灵饰输入资源["等级"]:取文本() == ""  or tonumber(self.灵饰输入资源["等级"]:取文本()) == nil  then
				  	 tp.提示:写入("#Y/灵饰等级未输入")
				elseif self.灵饰输入资源["部位"]:取文本() == ""  then
				  	 tp.提示:写入("#Y/灵饰部位未选择")
				elseif self.灵饰输入资源["属性"]:取文本() == ""  or tonumber(self.灵饰输入资源["属性"]:取文本()) == nil then
				  	 tp.提示:写入("#Y/灵饰主属数值未填写")
			   else
			   	   for i=1,#self.灵饰内容显示 do
						if self.灵饰输入资源[self.灵饰内容显示[i]]:取文本() ~= "" and self.灵饰输入资源[self.灵饰内容显示[i]]:取文本() ~= nil then
							self.发送信息[self.灵饰内容显示[i]] = self.灵饰输入资源[self.灵饰内容显示[i]]:取文本()
						end
					end
	            	发送数据(5,{文本=self.发送装备:取文字(),玩家id=self.角色id:取文本(),灵饰数据=self.发送信息})
	           end

        end








  zt3:显示(10,460,"专用栏输入内容会绑定玩家")


end


function 功能界面:游戏管理显示(dt,x,y)
	self.资源[10]:置宽高1(500,22)
    self.资源[10]:显示(90,65)

    zt:显示(10,64,"公告内容:")
	self.资源[10]:置宽高1(395,22)
    self.资源[10]:显示(90,95)
    zt:显示(10,99,"数额倍率:")
    self.公告输入:置坐标(93,68)
	self.公告输入:置可视(true,true)
	self.服务输入:置坐标(93,98)
	self.服务输入:置可视(true,true)
	self.资源[2]:显示(0,125)
	self.发送广播:更新(x,y)
    self.发送公告:更新(x,y)
    self.发送广播:显示(610,68)
    self.发送公告:显示(710,68)
    self.经验倍率:更新(x,y)
    self.游戏难度:更新(x,y)
    self.等级上限:更新(x,y)
    self.经验倍率:显示(510,98)
    self.游戏难度:显示(610,98)
    self.等级上限:显示(710,98)
    self.聊天监控开关:更新(x,y)
    self.聊天监控开关:显示(30,345)
    self.查看聊天记录:更新(x,y)
    self.查看聊天记录:显示(150,345)
    self.清空聊天监控:更新(x,y)
    self.清空聊天监控:显示(270,345)
    self.清空聊天记录:更新(x,y)
    self.清空聊天记录:显示(390,345)
    self.丰富文本聊天背景:显示(30,375)
	self.丰富文本聊天:显示(30,377)


	if self.丰富文本聊天背景:是否选中(x,y) and 引擎.取鼠标滚轮() >0 then--鼠标上滚动
		 self.丰富文本聊天:滚动(1)
	elseif self.丰富文本聊天背景:是否选中(x,y) and 引擎.取鼠标滚轮() <0  then--鼠标下滚动
		 self.丰富文本聊天:滚动(-1)
	end





    if self.发送广播:事件判断() then
    	if self.公告输入:取文本() == ""  then
		  tp.提示:写入("#Y/还未填写公告内容")
		else
	       发送数据(6,{文本=self.发送广播:取文字(),数据 = self.公告输入:取文本() })
	    end
	 elseif self.发送公告:事件判断() then
	 	if self.公告输入:取文本() == ""  then
		  tp.提示:写入("#Y/还未填写公告内容")
		else
	       发送数据(6,{文本=self.发送公告:取文字(),数据 = self.公告输入:取文本() })
	    end
	 elseif self.经验倍率:事件判断() then
	 	if self.服务输入:取文本() == "" or tonumber(self.服务输入:取文本()) == nil then
		  tp.提示:写入("#Y/数额倍率还未填写,数额倍率为纯数字")
		else
	       发送数据(6,{文本=self.经验倍率:取文字(),数据 = self.服务输入:取文本()})
	    end
	 elseif self.游戏难度:事件判断() then
	 	if self.服务输入:取文本() == "" or tonumber(self.服务输入:取文本()) == nil then
		  tp.提示:写入("#Y/数额倍率还未填写,数额倍率为纯数字")
		else
	       发送数据(6,{文本=self.游戏难度:取文字(),数据 = self.服务输入:取文本()})
	    end
	 elseif self.等级上限:事件判断() then
	 	if self.服务输入:取文本() == "" or tonumber(self.服务输入:取文本()) == nil then
		  tp.提示:写入("#Y/数额倍率还未填写,数额倍率为纯数字")
		else
	       发送数据(6,{文本=self.等级上限:取文字(),数据 = self.服务输入:取文本()})
	    end
       elseif self.聊天监控开关:事件判断() then
       	 发送数据(6,{文本=self.聊天监控开关:取文字()})

      elseif self.查看聊天记录:事件判断() then
      	self.丰富文本聊天:清空()
	    self.丰富文本聊天:添加文本(self.聊天记录)
	  elseif self.清空聊天监控:事件判断() then
	  	self.丰富文本聊天:清空()
	  elseif self.清空聊天记录:事件判断() then
	  	self.丰富文本聊天:清空()
	  	self.聊天记录 = "聊天记录"
    end



	    local xx = 0
        local yy = 0
        for i=1,#self.游戏按钮 do
            self.游戏按钮[i]:更新(x,y)
            self.游戏按钮[i]:显示(30 + xx * 110,135 + yy * 35,true)
            xx = xx + 1
            if xx > 6 then
                xx = 0
                yy = yy + 1
            end
            if  self.游戏按钮[i]:事件判断() then
	            发送数据(6,{文本=self.游戏按钮[i]:取文字()})
            end
        end

end




function 功能界面:刷新(内容)
  self.丰富文本说明:清空()
  self.丰富文本聊天:清空()
  self.丰富文本道具:清空()
  self.丰富文本说明:添加文本(内容)
  self.丰富文本聊天:添加文本(内容)
  self.丰富文本道具:添加文本(内容)
end


function 功能界面:道具加入(内容)
  self.丰富文本说明:清空()
  self.丰富文本聊天:清空()
  self.丰富文本道具:清空()
  if 内容.卡号 == nil then
     self.卡号类型 = 内容
      for i=1,#self.卡号类型 do
		self.丰富文本道具:添加文本(self.卡号类型[i])
	  end
	   self.获取卡号类型 = true
  else
     self.充值卡号 = 内容
      for i=1,#self.充值卡号 do
		self.丰富文本道具:添加文本(self.充值卡号[i])
	  end
	  self.获取卡号 = true
      self.丰富文本道具:添加文本(self.充值类型.." 自动充值的文件卡号,共"..#self.充值卡号.."个")
  end

end

function 功能界面:监控加入(内容)
  self.丰富文本聊天:添加文本(内容)
  self.聊天记录=self.聊天记录.."\n"..内容
end
function 功能界面:召唤兽信息获取(内容)
	self.召唤数据=内容
	self.宝宝选项列表  = {}
	table.print(self.召唤数据)
	 for i,n in pairs(self.召唤数据) do
		self.宝宝选项列表[i] = i.."-"..self.召唤数据[i].名称.."  "..self.召唤数据[i].等级.."级"
	end
	self.是否获取宝宝 =true

end

function 功能界面:召唤兽信息嵌入(编号)
	if self.召唤数据[编号]~=nil then
	      for i=1,#self.召唤兽属性显示 do
			 if self.召唤数据[编号][self.召唤兽属性显示[i]]~= nil  then
				self.召唤兽属性输入[self.召唤兽属性显示[i]]:置文本(self.召唤数据[编号][self.召唤兽属性显示[i]])
			 end
		  end
		for i=1,20 do
			if self.召唤数据[编号].技能[i] ~=nil then
				self.召唤兽技能输入[self.召唤兽技能显示[i]]:置文本(self.召唤数据[编号].技能[i])
			end
		end
		for i=1,4 do
			if  self.召唤数据[编号].天生技能 ~=nil and self.召唤数据[编号].天生技能[i] ~=nil then
				self.召唤兽天生输入[self.召唤兽天生显示[i]]:置文本(self.召唤数据[编号].天生技能[i])
			end
		end

	else
		self.是否获取宝宝 = false
	end



end



function 功能界面:角色信息嵌入(内容)
    for i=1,#self.角色修炼显示 do
    	if 内容.修炼[self.角色修炼显示[i]] ~=nil then
    		self.角色修炼输入[self.角色修炼显示[i]]:置文本(内容.修炼[self.角色修炼显示[i]][1])
    	end
	end

  for i=1,#self.召唤兽修炼显示 do
	   	if 内容.bb修炼[self.召唤兽修炼显示[i]] ~=nil then
    		self.召唤兽修炼输入[self.召唤兽修炼显示[i]]:置文本(内容.bb修炼[self.召唤兽修炼显示[i]][1])
    	end
	end


    for i=1,#self.角色生活显示 do
    	if 内容.生活技能[self.角色生活显示[i]] ~=nil then
    		self.角色生活输入[self.角色生活显示[i]]:置文本(内容.生活技能[self.角色生活显示[i]])
    	end
	end

    for i=1,#self.角色强化显示 do
    if 内容.强化技能~=nil then
	  if 内容.强化技能[self.角色强化显示[i]] ~=nil then
			self.角色强化输入[self.角色强化显示[i]]:置文本(内容.强化技能[self.角色强化显示[i]] )
	    end
	 end
	end
end




return 功能界面



