-- @作者: baidwwy
-- @邮箱:  <EMAIL>
-- @创建时间:   2015-01-27 03:51:34
-- @最后修改来自: baidwwy
-- @Last Modified time: 2025-04-20 21:28:49


-- table.tostring = function (t)
--     if t then
--         local mark={}
--         local assign={}
--         local function ser_table(tbl,parent)
--             mark[tbl]=parent
--             local tmp={}
--             for k,v in pairs(tbl) do
--                 local key= type(k)=="number" and string.format("[%s]", k) or k--string.format("[%q]", k)
--                 if type(v)=="table" then
--                     local dotkey= parent.. key
--                     if mark[v] then
--                         table.insert(assign,string.format("%s='%s'", dotkey,mark[v]))
--                     else
--                         table.insert(tmp, string.format("%s=%s", key,ser_table(v,dotkey)))
--                     end
--                 elseif type(v) == "string" then
--                     table.insert(tmp, string.format("%s=%q", key,v))
--                 elseif type(v) == "number" or type(v) == "boolean" then
--                     table.insert(tmp, string.format("%s=%s", key,v))
--                 end
--             end
--             return string.format("{%s}", table.concat(tmp,","))
--         end
--         return string.format("do local ret=%s%s return ret end", ser_table(t,"ret"),table.concat(assign," "))
--     end
--     --return "do local ret={} return ret end"
-- end






function table.tostring(t)
    if not t then return "do local ret={} return ret end" end

    local seen = {} -- 记录已处理的表，避免循环引用
    local function serialize(val, indent)
        if type(val) == "table" then
            if seen[val] then
                return nil -- 返回 nil，让外层跳过这个键值对
            end
            seen[val] = true

            local parts = {}
            local next_indent = indent .. ""
            for k, v in pairs(val) do
                local key_str = type(k)=="number" and string.format("[%s]", k) or k
                -- if type(k) == "string" then
                --     key_str = string.format("[%q]", k)
                -- else
                --     key_str = "[" .. tostring(k) .. "]"
                -- end
                local val_str = serialize(v, next_indent)
                if val_str ~= nil then -- 只有非 nil 的值才会被序列化
                    table.insert(parts, next_indent .. key_str .. "=" .. val_str)
                end
            end

            if #parts == 0 then
                return "{}"
            else
                return "{" .. table.concat(parts, ",") .. indent .. "}"
            end
        elseif type(val) == "string" then
            return string.format("%q", val)
        elseif type(val) == "boolean" or type(val) == "number" then
            return tostring(val)
        else
            return nil -- 其他不支持的类型也返回 nil（跳过）
        end
    end

    local serialized = serialize(t, "")
    return "do local ret=" .. (serialized or "{}") .. " return ret end"
end


table.loadstring = function (t)
	if t then
		local f = loadstring(t)
		if f then
			setfenv(f, {})
			return f()
		end
	end
end

table.copy = function (ori_tab)
    if (type(ori_tab) ~= "table") then
    	error("非table,不能复制.")
    end
    local new_tab = {};
    for i,v in pairs(ori_tab) do
        local vtyp = type(v);
        if (vtyp == "table") then
            new_tab[i] = table.copy(v);
        elseif (vtyp == "thread") then
            error("复制失败,非法类型.")
        elseif (vtyp == "userdata") then
            error("复制失败,非法类型.")
        else
            new_tab[i] = v;
        end
    end
    return new_tab;
end

table.print = function (root)
	local print = print
	local tconcat = table.concat
	local tinsert = table.insert
	local srep = string.rep
	local type = type
	local pairs = pairs
	local tostring = tostring
	local next = next
	local cache = {  [root] = "." }
	local function _dump(t,space,name)
		local temp = {}
		for k,v in pairs(t) do
			local key = tostring(k)
			if cache[v] then
				tinsert(temp,"." .. key .. " {" .. cache[v].."}")
			elseif type(v) == "table" then
				local new_key = name .. "." .. key
				cache[v] = new_key
				tinsert(temp,"." .. key .. _dump(v,space .. (next(t,k) and "|" or " " ).. srep(" ",#key),new_key))
			else
				tinsert(temp,"." .. key .. " [" .. tostring(v).."]")
			end
		end
		return tconcat(temp,"\n"..space)
	end
	print(_dump(root, "",""))
end
string.split = function (str, delimiter)
	if str  then
	    local result = {}
	    str = str..delimiter
	    delimiter = "(.-)"..delimiter
	    for match in str:gmatch(delimiter) do
	        table.insert(result, match)
	    end
	    return result
	end
	return {}
end