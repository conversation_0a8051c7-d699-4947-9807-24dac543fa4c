
f函数=require("ffi函数2")
ffi = require("ffi")
fgf="12345*-*12345"
fgc="12345@+@12345"
星期=  os.date("%w")
随机序列=0
错误日志={}

__C客户信息   = {}
--账号记录={}
队伍数据={}
交易数据={}
捉鬼数据={}
师门数据={}
文韵数据={}
心魔宝珠={}
十二生肖={}
科举数据={}
炼丹查看={}
商品存放={}
聊天监控={}
仙缘次数={}
qz=math.floor
cbg费率=0
异步保存次数=0
在线数据={}
玩家数据={}
双倍数据={}
三倍数据={}
在线时间={}
雪人限制={}
三界题库 = {}
铃铛记录 = {}
科举题库={}
任务数据={}
聊天监控={}
剑会开关=false

帮派缴纳情况 = {}
商店bb={}
变异商店bb={}
自定义数据={}
自定义银子={}
共享仓库={}
共享货币={}




--=========================
节日开关=false
全服禁言 = false

宝箱开关=false
游泳开关=false
网关认证=false
皇宫飞贼={开关=false}
迷宫数据={开关=false}
镖王活动={开关=false}
降妖伏魔={开关=false}
国庆活动={保卫战={开关=false,地图=1110,积分=0,玩家={},起始=os.time(),次数=0}}
副本数据={乌鸡国={进行={},完成={},进行中={}},车迟斗法={进行={},完成={},进行中={}},水陆大会={进行={},完成={},进行中={}},通天河={进行={},完成={},进行中={}},大闹天宫={进行={},完成={},进行中={}},齐天大圣={进行={},完成={},进行中={}},一斛珠={进行={},完成={},进行中={}}}
宝藏山数据={开关=false,起始=os.time(),刷新=0,间隔=600}
闯关参数={开关=false,起始=0,记录={}}
时辰信息={当前=1,刷新=60,起始=os.time()}
昼夜参数=1
剑会匹配=os.time()
商店刷新=os.time()
塔怪刷新=os.time()
可以抓捕=os.time()
保存数据=os.time()
调试模式=false
服务器关闭 = {计时 = 300,开关 = false,起始 = os.time()}
战斗序号={收到=math.random(5600,5699),发送=math.random(5700,5799)}
授权认证={启动时间=os.time(),认证时间=os.time(),认证成功=false,获取地址=""}
新加战斗锦衣 = {冰寒绡月白=1,冰雪玉兔=1,炽云缎墨黑=1,从军行=1,从军行月曜=1,飞天舞=1,飞天舞朝露=1,绯雪织凝霜=1,
官服=1,花间梦=1,蒹葭苍苍寒月=1,浪淘纱=1,浪淘纱墨黑=1,鹿角弯弯=1,萌萌小厨=1,明光宝甲=1,青花瓷=1,
水云归=1,霞姿月韵=1,闲云野鹤=1,雪眸影夜烬=1,夜影=1,羽仙歌=1}
飞行符传送={
    [1]={1001,336,217},
    [2]={1001,358,35},
    [3]={1501,65,112},
    [4]={1092,122,54},
    [5]={1070,106,158},
    [6]={1040,108,98},
    [7]={1226,117,48},
    [8]={1208,128,36},
  }
制造装备={}
制造装备.戒指={[60]="枫华戒",[80]="芙蓉戒",[100]="金麟绕",[120]="悦碧水",[140]="九曜光华",[160]="太虚渺云"}-- gaixia mingzijiuky le
制造装备.手镯={[60]="香木镯",[80]="翡玉镯",[100]="墨影扣",[120]="花映月",[140]="金水菩提",[160]="浮雪幻音"}
制造装备.耳饰={[60]="翠叶环",[80]="明月珰",[100]="玉蝶翩",[120]="点星芒",[140]="凤羽流苏",[160]="焰云霞珠"}
制造装备.佩饰={[60]="芝兰佩",[80]="逸云佩",[100]="莲音玦",[120]="相思染",[140]="玄龙苍珀",[160]="碧海青天"}

家具消耗={
                  文竹={活力=55,体力=35,银子=7000},
                  水仙={活力=75,体力=55,银子=10000},
                  仙人掌={活力=75,体力=55,银子=10000},
                  榛木床={活力=45,体力=25,银子=5000},
                  君子兰={活力=65,体力=30,银子=9000},
                  蝴蝶兰={活力=65,体力=30,银子=9000},
                  银烛台={活力=85,体力=40,银子=12000},
                  踏春图={活力=95,体力=50,银子=14000},
                  神仙帐={活力=105,体力=55,银子=20000},
                  月牙凳={活力=105,体力=55,银子=20000},
                  桦木圆桌={活力=25,体力=10,银子=1000},
                  桦木立柜={活力=35,体力=15,银子=3000},
                  草编地毯={活力=35,体力=15,银子=3000},
                  漆花竹凳={活力=45,体力=20,银子=5000},
                  印花屏风={活力=55,体力=30,银子=7000},
                  玉瓷画瓶={活力=85,体力=45,银子=12000},
                  漆花地板={活力=95,体力=50,银子=14000},
                  彩绘立柜={活力=105,体力=55,银子=20000},
                  麻布地毯={活力=105,体力=55,银子=20000},
                  天山云雪={活力=125,体力=65,银子=30000},
                  长信宫灯={活力=125,体力=65,银子=30000},
                  雕花马桶={活力=125,体力=65,银子=30000},
                  彩绘花瓶={活力=125,体力=65,银子=30000},
                  桦木地板={活力=125,体力=65,银子=30000},
                  兽皮地毯={活力=105,体力=55,银子=20000},
                  黑麻垂曼帘={活力=25,体力=10,银子=1000},
                  蓝绸绣花帘={活力=105,体力=55,银子=20000},
                  白鹤展翅帘={活力=105,体力=55,银子=20000},
                  红木八仙桌={活力=105,体力=55,银子=20000},
                  嵌玉虎纹桌={活力=105,体力=55,银子=20000},
                  双鱼吉庆柜={活力=105,体力=55,银子=20000},
                  桦木靠背椅={活力=105,体力=55,银子=20000},
                  八卦镇邪榻={活力=105,体力=55,银子=20000},
                  狮子图屏风={活力=105,体力=55,银子=20000},
                  花鸟图屏风={活力=105,体力=55,银子=20000},
                  龟鹤延年灯={活力=125,体力=65,银子=30000},
                  夕阳山水图={活力=125,体力=65,银子=30000},
                  猛虎坐山图={活力=125,体力=65,银子=30000},
 }

Q_游泳数据={
  {z=1092,x=208,y=15,f=1}
  ,{z=1514,x=34,y=73,f=1}
  ,{z=1514,x=101,y=103,f=0}
  ,{z=1118,x=53,y=38,f=1}
  ,{z=1119,x=52,y=22,f=1}
  ,{z=1119,x=5,y=21,f=0}
  ,{z=1532,x=58,y=37,f=1}
  ,{z=1532,x=8,y=30,f=0}
  ,{z=1121,x=8,y=7,f=0}
  ,{z=1121,x=34,y=39,f=0}
  ,{z=1120,x=8,y=32,f=0}
  ,{z=1120,x=53,y=29,f=1}
  ,{z=1118,x=25,y=32,f=0}
  ,{z=1116,x=88,y=15,f=0}
  ,{z=1116,x=206,y=61,f=1}
  ,{z=1116,x=78,y=101,f=1}
  ,{z=1506,x=113,y=6,f=0}
  ,{z=1506,x=104,y=63,f=0}
  ,{z=1092,x=132,y=142,f=0}
  ,{z=1092,x=201,y=45,f=1}
}

镖王数据={
  {地图=1193,x=135,y=20,z=1}
  ,{地图=1501,x=186,y=29,z=0}
  ,{地图=1506,x=92,y=36,z=0}
  ,{地图=1092,x=23,y=14,z=1}
  ,{地图=1514,x=130,y=12,z=0}
  ,{地图=1174,x=12,y=142,z=0}
}

Q_首席弟子={
    龙宫={地图=1116,编号=18},
    女儿村={地图=1142,编号=8},
    化生寺={地图=1002,编号=8},
    大唐官府={地图=1198,编号=9},
    普陀山={地图=1140,编号=5},
    五庄观={地图=1146,编号=4},
    盘丝洞={地图=1144,编号=3},
    魔王寨={地图=1512,编号=5},
    狮驼岭={地图=1131,编号=5},
    天宫={地图=1111,编号=14},
    方寸山={地图=1135,编号=6},
    阴曹地府={地图=1122,编号=9},
    凌波城={地图=1150,编号=13},
    神木林={地图=1138,编号=6},
    无底洞={地图=1139,编号=6},
    九黎城 ={地图=2008,编号=5}
}


function isNaN(value)
    return type(value) == "number" and value ~= value
end



function 信息框(msg,title,type)
  ffi.C.MessageBoxA(nil, msg, title or '信息', mtype or 0)
end




潜能果经验={[1]=10000000,[2]=10150000,[3]=10300000,[4]=10450000,[5]=10600000,[6]=10750000,[7]=10900000,[8]=11050000,[9]=11200000,[10]=11350000,
           [11]=11500000,[12]=11650000,[13]=11800000,[14]=11950000,[15]=12100000,[16]=12250000,[17]=12400000,[18]=12550000,[19]=12700000,[20]=12850000,
           [21]=13000000,[22]=13150000,[23]=13300000,[24]=13450000,[25]=13600000,[26]=13750000,[27]=13900000,[28]=14050000,[29]=14200000,[30]=14350000,
           [31]=14500000,[32]=14650000,[33]=14800000,[34]=14950000,[35]=15100000,[36]=15250000,[37]=15400000,[38]=15550000,[39]=15700000,[40]=15850000,
           [41]=16000000,[42]=16150000,[43]=16300000,[44]=16450000,[45]=16600000,[46]=16750000,[47]=16900000,[48]=17050000,[49]=17200000,[50]=17350000,
           [51]=17500000,[52]=17650000,[53]=17800000,[54]=17950000,[55]=18100000,[56]=18250000,[57]=18400000,[58]=18550000,[59]=18700000,[60]=18850000,
           [61]=19000000,[62]=19150000,[63]=19300000,[64]=19450000,[65]=19600000,[66]=19750000,[67]=19900000,[68]=20050000,[69]=20200000,[70]=20350000,
           [71]=20500000,[72]=20650000,[73]=20800000,[74]=20950000,[75]=21100000,[76]=21250000,[77]=21400000,[78]=21550000,[79]=21700000,[80]=21850000,
           [81]=22000000,[82]=22150000,[83]=22300000,[84]=22450000,[85]=22600000,[86]=22750000,[87]=22900000,[88]=23050000,[89]=23200000,[90]=23350000,
           [91]=23500000,[92]=23650000,[93]=23800000,[94]=23950000,[95]=24100000,[96]=24250000,[97]=24400000,[98]=24550000,[99]=24700000,[100]=24850000,
           [101]=25000000,[102]=25150000,[103]=25300000,[104]=25450000,[105]=25600000,[106]=25750000,[107]=25900000,[108]=26050000,[109]=26200000,[110]=26350000,
           [111]=26500000,[112]=26650000,[113]=26800000,[114]=26950000,[115]=27100000,[116]=27250000,[117]=27400000,[118]=27550000,[119]=27700000,[120]=27850000,
           [121]=28000000,[122]=28150000,[123]=28300000,[124]=28450000,[125]=28600000,[126]=28750000,[127]=28900000,[128]=29050000,[129]=29200000,[130]=29350000,
           [131]=29500000,[132]=29650000,[133]=29800000,[134]=29950000,[135]=30100000,[136]=30250000,[137]=30400000,[138]=30550000,[139]=30700000,[140]=30850000,
           [141]=31000000,[142]=31150000,[143]=31300000,[144]=31450000,[145]=31600000,[146]=31750000,[147]=31900000,[148]=32050000,[149]=32200000,[150]=32350000,
           [151]=32500000,[152]=32650000,[153]=32800000,[154]=32950000,[155]=33100000,[156]=33250000,[157]=33400000,[158]=33550000,[159]=33700000,[160]=33850000,
           [161]=34000000,[162]=34150000,[163]=34300000,[164]=34450000,[165]=34600000,[166]=34750000,[167]=34900000,[168]=35050000,[169]=35200000,[170]=35350000,
           [171]=35500000,[172]=35650000,[173]=35800000,[174]=35950000,[175]=36100000,[176]=36250000,[177]=36400000,[178]=36550000,[179]=36700000,[180]=36850000,
           [181]=37000000,[182]=37150000,[183]=37300000,[184]=37450000,[185]=37600000,[186]=37750000,[187]=37900000,[188]=38050000,[189]=38200000,[190]=38350000,
           [191]=38500000,[192]=38650000,[193]=38800000,[194]=38950000,[195]=39100000,[196]=39250000,[197]=39400000,[198]=39550000,[199]=39700000,[200]=39850000,[201]=0}






function 刷新世界挑战()
  世界挑战={开启=true,气血={当前=9999999999,上限=9999999999},奖励={}}
  发送公告("#G世界BOOS已经刷新,玩家可以进行挑战了")
  广播消息({内容="#G/世界BOOS已经刷新,玩家可以进行挑战了。",频道="xt"})
  for i,v in pairs(玩家数据) do
      if 玩家数据[i] ~= nil and 玩家数据[i].战斗 == 0 and 玩家数据[i].摊位数据 == nil then
        发送数据(玩家数据[i].连接id,1501,{模型="男人_道童",名称="世界大使",对话="世界BOOS已经刷新,玩家可以进行挑战了。",选项={"挑战世界BOOS","我只是看看"}})
      end
  end
end

function 结束世界挑战()
  for n, v in pairs(战斗准备类.战斗盒子) do
    if 战斗准备类.战斗盒子[n].战斗类型==100308 then
      战斗准备类.战斗盒子[n]:结束战斗处理(0,0,1)
    end
  end
  世界挑战.开启 = false
  世界挑战.奖励={}
  local BOOS排行数据 = {}
  for i,v in pairs(世界挑战) do
    if i ~= "开启" and i ~= "气血" and i ~= "奖励"  and v.伤害 ~= nil  then
      BOOS排行数据[#BOOS排行数据+1] = DeepCopy(v)
    end
  end
  table.sort(BOOS排行数据,function(a,b) return a.伤害>b.伤害 end )
  for i=1,8 do
    if BOOS排行数据[i] ~= nil and BOOS排行数据[i].伤害 ~= 0 then
     世界挑战.奖励[#世界挑战.奖励+1] = BOOS排行数据[i]
    end
  end
  发送公告("#G世界BOOS挑战已经结束,战斗中的玩家将被T出战斗,当前排名前三的队长可以点击npc领取队伍排名奖励了。")
  广播消息({内容="#G/世界BOOS挑战已经结束,尚在战斗中的玩家将被T出战斗,当前排名前三的队长可以点击npc领取队伍排名奖励了。",频道="xt"})
end


function 取帮派建筑数量(等级)
  if 等级 == 1 then
    return 2
  elseif 等级 == 2 then
    return 5
  elseif 等级 == 3 then
    return 9
  elseif 等级 == 4 then
    return 13
  elseif 等级 == 5 then
    return 18
  elseif 等级 == 6 then
    return 23
  elseif 等级 == 7 then
    return 28
  end
end

function xsjc(id,进程)--刷新任务
 if 玩家数据[id]==nil then return  end
 local 任务id=玩家数据[id].角色:取任务(999)
 if 任务数据[任务id]~=nil then
   任务数据[任务id].进程=进程
   常规提示(id,"#Y您的剧情任务已经更新，请注意及时查看！")
   玩家数据[id].角色:刷新任务跟踪()
   end
 end
function xsjc1(id,进程)
 if 玩家数据[id]==nil then return  end
 local 任务id=玩家数据[id].角色:取任务(998)
 if 任务数据[任务id]~=nil then
   任务数据[任务id].进程=进程
   常规提示(id,"#Y您的剧情任务已经更新，请注意及时查看！")
   玩家数据[id].角色:刷新任务跟踪()
   end
 end
 function xsjc2(id,进程)
 if 玩家数据[id]==nil then return  end
 local 任务id=玩家数据[id].角色:取任务(997)
 if 任务数据[任务id]~=nil then
   任务数据[任务id].进程=进程
   常规提示(id,"#Y您的剧情任务已经更新，请注意及时查看！")
   玩家数据[id].角色:刷新任务跟踪()
   end
 end
  function xsjc3(id,进程)
 if 玩家数据[id]==nil then return  end
 local 任务id=玩家数据[id].角色:取任务(996)
 if 任务数据[任务id]~=nil then
   任务数据[任务id].进程=进程
   常规提示(id,"#Y您的剧情任务已经更新，请注意及时查看！")
   玩家数据[id].角色:刷新任务跟踪()
   end
 end
 function xsjc11(id,进程)
 if 玩家数据[id]==nil then return  end
 local 任务id=玩家数据[id].角色:取任务(898)
 if 任务数据[任务id]~=nil then
   任务数据[任务id].进程=进程
   常规提示(id,"#Y您的剧情任务已经更新，请注意及时查看！")
   玩家数据[id].角色:刷新任务跟踪()
   end
 end

function 银子检查(id,数额)
 if 玩家数据[id].角色.数据.银子 >= 数额 then
    玩家数据[id].角色.数据.银子 = 玩家数据[id].角色.数据.银子-数额
    return true
 end
 return false
end


function 创建目录(dirname)
  -- print(lfs.currentdir(dirname))
  lfs.mkdir(dirname)
end

角色武器类型={
神天兵={1,9},
龙太子={1,7},
舞天姬={5,11},
玄彩娥={5,8},
桃夭夭={5,18},
剑侠客={3,12},
逍遥生={7,3},
飞燕女={4,11},
英女侠={4,10},
羽灵神={14,13},
虎头怪={2,9},
巨魔王={2,12},
骨精灵={8,6},
狐美人={6,10},
鬼潇潇={17,6},
杀破狼={14,15},
偃无师={16,3},
影精灵={8,6},
巫蛮儿={13,15}
}


Q_乾坤袋={
  人={"中原一点红","草上飞前帽","水上飘李达","无影神偷刘欢"}
  ,魔={"千年老妖","南海不死怪","百年小妖","魔眼吸精妖"}
  ,仙={"无上真人","通广上仙","紫金洞大仙","南部大仙"}
}
Q__乾坤袋={---------远方文韵墨香
  人={"中原一点红","草上飞前帽","水上飘李达","无影神偷刘欢"}
  ,魔={"千年老妖","南海不死怪","百年小妖","魔眼吸精妖"}
  ,仙={"无上真人","通广上仙","紫金洞大仙","南部大仙"}
}
Q_随机人物={
{地图=1001,人物={"陈员外","张老财","刘副将","御林军右统领",}},
{地图=1033,人物={"罗百万","陈妈妈","小桃红"}},
{地图=1070,人物={"海老先生","许姑娘","钟书生","蝴蝶女","慧觉和尚",}},
{地图=1135,人物={"觉明","灵儿",}},
{地图=1208,人物={"朱紫校尉","端木娘子","紫阳药师","朱紫侍卫"}},
{地图=1173,人物={"山贼头子","牛将军","白衣人","云游僧","姚太尉","阿紫","玉面狐狸","白鹿精","野猪王","冤魂","偷尸鬼","李彪","刘洪",}},
{地图=1513,人物={"女妖","金琉璃","栗栗儿",}},
{地图=1092,人物={"蝴蝶妹妹",}},
{地图=1093,人物={"慕容先生",}},
{地图=1506,人物={"楚恋依",}},
{地图=1116,人物={"万圣公主","龟太尉","虾将军","蛤蟆勇士","龟千岁","小龙女",}},
{地图=1142,人物={"柳飞絮","栗栗娘","绿儿","翠花",}},
{地图=1174,人物={"青琉璃","莽汉","龙女妹妹",}},
{地图=1111,人物={"守门天兵","顺风耳","天牢守卫","水兵统领",}},
{地图=1114,人物={"吴刚","嫦娥","康太尉",}},
{地图=1110,人物={"山神","白琉璃","小二","者释和尚","业释和尚","海释和尚","虾兵",}},
{地图=1122,人物={"孟婆","追梦鬼","马面",}},
{地图=1127,人物={"幽冥鬼",}},
{地图=1173,人物={"山贼头子","牛将军","白衣人","云游僧","姚太尉","阿紫","玉面狐狸","白鹿精","野猪王","冤魂","偷尸鬼","李彪","刘洪",}},
{地图=1110,人物={"山神","白琉璃","小二","者释和尚","业释和尚","海释和尚","虾兵",}},
{地图=1116,人物={"万圣公主","龟太尉","虾将军","蛤蟆勇士","龟千岁","小龙女",}},
{地图=1114,人物={"吴刚","嫦娥","康太尉",}},
}

Q_随机模型={"剑侠客","逍遥生","飞燕女","英女侠","巫蛮儿","神天兵","龙太子","舞天姬","玄彩娥","桃夭夭","虎头怪","巨魔王","狐美人","骨精灵","杀破狼","羽灵神","偃无师","鬼潇潇"}
Q_星宿名称={
  [1]="角木蛟"
  ,[2]="亢金龙"
  ,[3]="氐土貉"
  ,[4]="房日兔"
  ,[5]="心月狐"
  ,[6]="尾火虎"
  ,[7]="箕水豹"
  ,[8]="井木犴"
  ,[9]="鬼金羊"
  ,[10]="柳土獐"
  ,[11]="星日马"
  ,[12]="张月鹿"
  ,[13]="翼火蛇"
  ,[14]="轸水蚓"
  ,[15]="奎木狼"
  ,[16]="娄金狗"
  ,[17]="胃土雉"
  ,[18]="昴日鸡"
  ,[19]="毕月乌"
  ,[20]="觜火猴"
  ,[21]="参水猿"
  ,[22]="斗木獬"
  ,[23]="牛金牛"
  ,[24]="女土蝠"
  ,[25]="虚日鼠"
  ,[26]="危月燕"
  ,[27]="室火猪"
  ,[28]="壁水貐"
}


宝图名字={}
宝图名字.姓={
  [1]="张",
  [2]="吴",
  [3]="王",
  [4]="郑",
  [5]="周",
  [6]="赵",
  [7]="钱",
  [8]="李",
  [9]="孙",
  [10]="欧阳",
  [11]="南宫",
  [12]="上官",
  [13]="许",
  [14]="西门",
  [15]="毛",
  [16]="韦",
  [17]="曾",
  [18]="江",
  [19]="林",
  [20]="徐"
}

宝图名字.名={
  [1]="大",
  [2]="二",
  [3]="三",
  [4]="发",
  [5]="杰",
  [6]="紫",
  [7]="青",
  [8]="则",
  [9]="玉",
  [10]="宝",
  [11]="若",
  [12]="可",
  [13]="恩",
  [14]="倭",
  [15]="武",
  [16]="金",
  [17]="又",
  [18]="本",
  [19]="复",
  [20]="开"
}

宝图名字.氏={
  [1]="强",
  [2]="伦",
  [3]="空",
  [4]="财",
  [5]="飞",
  [6]="天",
  [7]="井",
  [8]="来",
  [9]="狗",
  [10]="麻",
  [11]="若",
  [12]="佳",
  [13]="文",
  [14]="武",
  [15]="全",
  [16]="不二",
  [17]="男",
  [18]="黑",
  [19]="白",
  [20]="之"
}



Q_天降辰星={
            [1]={地图=1001,x=65,y=139,名称="子鼠",模型="鼠先锋"},
            [2]={地图=1001,x=489,y=41,名称="丑牛",模型="踏云兽"},
            [3]={地图=1193,x=24,y=30,名称="寅虎",模型="噬天虎"},
            [4]={地图=1193,x=132,y=97,名称="卯兔",模型="兔子怪"},
            [5]={地图=1501,x=132,y=132,名称="辰龙",模型="蛟龙"},
            [6]={地图=1501,x=272,y=105,名称="巳蛇",模型="千年蛇魅"},
            [7]={地图=1506,x=74,y=89,名称="午马",模型="马面"},
            [8]={地图=1514,x=76,y=61,名称="未羊",模型="羊头怪"},
            [9]={地图=1514,x=126,y=107,名称="申猴",模型="长眉灵猴"},
            [10]={地图=1173,x=78,y=70,名称="酉鸡",模型="雷鸟人"},
            [11]={地图=1173,x=299,y=61,名称="戌犬",模型="狼"},
            [12]={地图=1173,x=535,y=39,名称="亥猪",模型="野猪"},
}




function 金色id(名字,参数)
   local s=tostring(名字)
   local k=string.len(s)
   local list1={}
   for i=1,k do
    list1[i]=string.sub(s,i,i)
   end
   local 靓号="("
   for n=1,#list1 do
     靓号=靓号.."#"..list1[n]+111+参数*10
   end
   靓号=靓号..")"

   return 靓号
end


function  取门派强化符技能名称(门派)
   if 门派== "大唐官府" then
      return "嗜血"
    elseif  门派== "女儿村" then
      return "轻如鸿毛"
    elseif  门派== "化生寺" then
      return "拈花妙指"
    elseif  门派== "盘丝洞" then
      return "盘丝舞"
    elseif  门派== "五庄观" then
      return "一气化三清"
    elseif  门派== "天宫" then
      return "浩然正气"
    elseif  门派== "龙宫" then
      return "龙附"
    elseif  门派== "方寸山" then
      return "神兵护法"
    elseif  门派== "魔王寨" then
      return "魔王护持"
    elseif  门派== "普陀山" then
      return "莲华妙法"
    elseif  门派== "狮驼岭" then
      return "神力无穷"
    elseif  门派== "阴曹地府" then
      return "尸气漫天"
    elseif  门派== "神木林" then
      return "神木呓语"
    elseif  门派== "凌波城" then
      return "穿云破空"
    elseif  门派== "无底洞" then
      return "元阳护体"
    end
end
初始庭院地图={初级=1420,中级=1421,高级=1422,顶级=1424,园林水榭=1306,欢乐童年=1380,白雪皑皑=1382,农家小院=1885}
初始房屋地图={初级={复古=1401,青砖=1404,大理石=1407,红地板=1410,海洋系=1413,粉红兔=1416,中国风=1330,卡通猫=1333,冰雪屋=1336,蓝色永恒=1953,星空蓝=1958,咖啡屋=1965},
             中级={复古=1402,青砖=1405,大理石=1408,红地板=1411,海洋系=1414,粉红兔=1417,中国风=1331,卡通猫=1334,冰雪屋=1337,蓝色永恒=1954,星空蓝=1959,咖啡屋=1966},
             高级={复古=1403,青砖=1406,大理石=1409,红地板=1412,海洋系=1415,粉红兔=1418,中国风=1332,卡通猫=1335,冰雪屋=1338,蓝色永恒=1955,星空蓝=1960,咖啡屋=1967},
             顶级={复古=1312,青砖=1311,大理石=1310,红地板=1313,海洋系=1314,粉红兔=1315,中国风=1316,卡通猫=1317,冰雪屋=1318,蓝色永恒=1319,星空蓝=1961,咖啡屋=1968},
             华宅={复古=1322,青砖=1321,大理石=1320,红地板=1323,海洋系=1324,粉红兔=1325,中国风=1326,卡通猫=1327,冰雪屋=1328,蓝色永恒=1329,星空蓝=1962,咖啡屋=1969},
             豪门={复古=1937,青砖=1935,大理石=1933,红地板=1939,海洋系=1941,粉红兔=1943,中国风=1945,卡通猫=1947,冰雪屋=1949,蓝色永恒=1951,星空蓝=1963,咖啡屋=1970},
             二楼={复古=1938,青砖=1936,大理石=1934,红地板=1940,海洋系=1942,粉红兔=1944,中国风=1946,卡通猫=1948,冰雪屋=1950,蓝色永恒=1952,星空蓝=1964,咖啡屋=1971},
              }





-- function 随机表格(表格)
--   if 表格==nil  then
--      return
--   end
--   local key = {}
--   local ver ={}
--   local 计数 = 0
--   for k,v in pairs(表格) do
--       计数 = 计数 +1
--       key[计数]=k
--       ver[计数]=v
--   end
--   local 长度 = #key
--   local 临时表格 ={}
--   while 长度>0 do
--         临时=  math.random(1,长度)
--         临时表格[key[临时]] = ver[临时]
--         table.remove(key,临时)
--         table.remove(ver,临时)
--         长度 = 长度 - 1
--   end
--   return 临时表格
-- end



-- function 随机排序数组(表格)
--     if not 表格 or type(表格)~="table" then
--        return
--     end
--     local key = {}
--     local ver ={}
--     local 计数 = 0
--     for k,v in pairs(表格) do
--         计数 = 计数 +1
--         key[计数]=k
--         ver[计数]=v
--     end
--     local 长度 = #key
--     local 临时表格 ={}
--     local 计数2 = 0
--     while 长度>0 do
--           临时=  math.random(1,长度)
--           if type(key[临时]) =="number" then
--             计数2 =计数2 + 1
--             临时表格[计数2] = ver[临时]
--           else
--             临时表格[key[临时]] = ver[临时]
--           end
--           table.remove(key,临时)
--           table.remove(ver,临时)
--           长度 = 长度 - 1
--     end
--   return 临时表格
-- end




function 判断特殊字符(mz)
  if string.find(mz,"[%s%p%c%z%?\\!@#%$%%&%*%(%)%^,%.%+%-/<>;'\"%[%]{}]")~=nil then
      return true
    elseif string.find(mz,"　")~=nil then
      return true
    elseif string.find(mz," ")~=nil then
      return true
    end
    return false
end

