{"ast": null, "code": "'use strict';\n\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, {\n    get: function () {\n      return 7;\n    }\n  })[1] !== 7;\n});", "map": {"version": 3, "names": ["fails", "require", "module", "exports", "Object", "defineProperty", "get"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/core-js-pure/internals/descriptors.js"], "sourcesContent": ["'use strict';\nvar fails = require('../internals/fails');\n\n// Detect IE8's incomplete defineProperty implementation\nmodule.exports = !fails(function () {\n  // eslint-disable-next-line es/no-object-defineproperty -- required for testing\n  return Object.defineProperty({}, 1, { get: function () { return 7; } })[1] !== 7;\n});\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,KAAK,GAAGC,OAAO,CAAC,oBAAoB,CAAC;;AAEzC;AACAC,MAAM,CAACC,OAAO,GAAG,CAACH,KAAK,CAAC,YAAY;EAClC;EACA,OAAOI,MAAM,CAACC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE;IAAEC,GAAG,EAAE,SAAAA,CAAA,EAAY;MAAE,OAAO,CAAC;IAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAClF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}