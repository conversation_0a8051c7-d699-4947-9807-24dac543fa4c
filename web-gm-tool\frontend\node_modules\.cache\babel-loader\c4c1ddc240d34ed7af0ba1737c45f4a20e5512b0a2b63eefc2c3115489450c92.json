{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileWordOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileWordOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileWordOutlined = function FileWordOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileWordOutlinedSvg\n  }));\n};\n\n/**![file-word](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNTI4LjEgNDcyaC0zMi4yYy01LjUgMC0xMC4zIDMuNy0xMS42IDkuMUw0MzQuNiA2ODBsLTQ2LjEtMTk4LjdjLTEuMy01LjQtNi4xLTkuMy0xMS43LTkuM2gtMzUuNGExMi4wMiAxMi4wMiAwIDAwLTExLjYgMTUuMWw3NC4yIDI3NmMxLjQgNS4yIDYuMiA4LjkgMTEuNiA4LjloMzJjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNTIuOC0xOTcgNTIuOCAxOTdjMS40IDUuMiA2LjIgOC45IDExLjYgOC45aDMxLjhjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNzQuNC0yNzZhMTIuMDQgMTIuMDQgMCAwMC0xMS42LTE1LjFINjQ3Yy01LjYgMC0xMC40IDMuOS0xMS43IDkuM2wtNDUuOCAxOTkuMS00OS44LTE5OS4zYy0xLjMtNS40LTYuMS05LjEtMTEuNi05LjF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileWordOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileWordOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "FileWordOutlinedSvg", "AntdIcon", "FileWordOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/FileWordOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport FileWordOutlinedSvg from \"@ant-design/icons-svg/es/asn/FileWordOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar FileWordOutlined = function FileWordOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: FileWordOutlinedSvg\n  }));\n};\n\n/**![file-word](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg1NC42IDI4OC42TDYzOS40IDczLjRjLTYtNi0xNC4xLTkuNC0yMi42LTkuNEgxOTJjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjgzMmMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWMzExLjNjMC04LjUtMy40LTE2LjctOS40LTIyLjd6TTc5MC4yIDMyNkg2MDJWMTM3LjhMNzkwLjIgMzI2em0xLjggNTYySDIzMlYxMzZoMzAydjIxNmE0MiA0MiAwIDAwNDIgNDJoMjE2djQ5NHpNNTI4LjEgNDcyaC0zMi4yYy01LjUgMC0xMC4zIDMuNy0xMS42IDkuMUw0MzQuNiA2ODBsLTQ2LjEtMTk4LjdjLTEuMy01LjQtNi4xLTkuMy0xMS43LTkuM2gtMzUuNGExMi4wMiAxMi4wMiAwIDAwLTExLjYgMTUuMWw3NC4yIDI3NmMxLjQgNS4yIDYuMiA4LjkgMTEuNiA4LjloMzJjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNTIuOC0xOTcgNTIuOCAxOTdjMS40IDUuMiA2LjIgOC45IDExLjYgOC45aDMxLjhjNS40IDAgMTAuMi0zLjYgMTEuNi04LjlsNzQuNC0yNzZhMTIuMDQgMTIuMDQgMCAwMC0xMS42LTE1LjFINjQ3Yy01LjYgMC0xMC40IDMuOS0xMS43IDkuM2wtNDUuOCAxOTkuMS00OS44LTE5OS4zYy0xLjMtNS40LTYuMS05LjEtMTEuNi05LjF6IiAvPjwvc3ZnPg==) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(FileWordOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'FileWordOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,+CAA+C;AAC/E,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EAC3D,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,gBAAgB,CAAC;AAC7D,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,kBAAkB;AAC1C;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}