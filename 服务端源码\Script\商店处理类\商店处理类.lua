
local 商店处理类 = class()

function 商店处理类:初始化()
	local n = {}
		n[1] = {"红缨枪","曲尖枪","锯齿矛","乌金三叉戟","火焰枪","墨杆金钩","玄铁矛","金蛇信","丈八点钢矛","暗夜","梨花","霹雳","刑天之逆","五虎断魂","飞龙在天","天龙破城","弑皇"}
		n[2] = {"青铜斧","开山斧","双面斧","双弦钺","精钢禅钺","黄金钺","乌金鬼头镰","狂魔镰","恶龙之齿","破魄","肃魂","无敌","五丁开山","元神禁锢","护法灭魔","碧血干戚","裂天"}
		n[3] = {"青铜短剑","铁齿剑","吴越剑","青锋剑","龙泉剑","黄金剑","游龙剑","北斗七星剑","碧玉剑","鱼肠","倚天","湛卢","魏武青虹","灵犀神剑","四法青云","霜冷九州","擒龙"}
		n[4] = {"双短剑","镔铁双剑","龙凤双剑","竹节双剑","狼牙双剑","鱼骨双剑","赤焰双剑","墨玉双剑","梅花双剑","阴阳","月光双剑","灵蛇","金龙双剪","连理双树","祖龙对剑","紫电青霜","浮犀"}
		n[5] = {"五色缎带","幻彩银纱","金丝彩带","无极丝","天蚕丝带","云龙绸带","七彩罗刹","缚神绫","九天仙绫","彩虹","流云","碧波","秋水落霞","晃金仙绳","此最相思","揽月摘星","九霄"}
		n[6] = {"铁爪","天狼爪","幽冥鬼爪","青龙牙","勾魂爪","玄冰刺","青刚刺","华光刺","龙鳞刺","撕天","毒牙","胭脂","九阴勾魂","雪蚕之刺","贵霜之牙","忘川三途","离钩"}
		n[7] = {"折扇","铁骨扇","精钢扇","铁面扇","百折扇","劈水扇","神火扇","阴风扇","风云雷电","太极","玉龙","秋风","画龙点睛","秋水人家","逍遥江湖","浩气长舒","星瀚"}
		n[8] = {"细木棒","金丝魔棒","玉如意","点金棒","云龙棒","幽路引魂","满天星","水晶棒","日月光华","沧海","红莲","盘龙","降魔玉杵","青藤玉树","墨玉骷髅","丝萝乔木","醍醐"}
		n[9] = {"松木锤","镔铁锤","八棱金瓜","狼牙锤","烈焰锤","破甲战锤","震天锤","巨灵神锤","天崩地裂","八卦","鬼牙","雷神","混元金锤","九瓣莲花","鬼王蚀日","狂澜碎岳","碎寂"}
		n[10] = {"牛皮鞭","牛筋鞭","乌龙鞭","钢结鞭","蛇骨鞭","玉竹金铃","青藤柳叶鞭","雷鸣嗜血鞭","混元金钩","龙筋","百花","吹雪","游龙惊鸿","仙人指路","血之刺藤","牧云清歌","霜陨"}
		n[11] = {"黄铜圈","精钢日月圈","离情环","金刺轮","风火圈","赤炎环","蛇形月","子母双月","斜月狼牙","如意","乾坤","月光双环","别情离恨","金玉双环","九天金线","无关风月","朝夕"}
		n[12] = {"柳叶刀","苗刀","夜魔弯刀","金背大砍刀","雁翅刀","破天宝刀","狼牙刀","龙鳞宝刀","黑炎魔刀","冷月","屠龙","血刃","偃月青龙","晓风残月","斩妖泣血","业火三灾","鸣鸿"}
		n[13] = {"曲柳杖","红木杖","白椴杖","墨铁拐","玄铁牛角杖","鹰眼法杖","腾云杖","引魂杖","碧玺杖","业焰","玉辉","鹿鸣","庄周梦蝶","凤翼流珠","雪蟒霜寒","碧海潮生","弦月"}
		n[14] = {"硬木弓","铁胆弓","紫檀弓","宝雕长弓","錾金宝弓","玉腰弯弓","连珠神弓","游鱼戏珠","灵犀望月","非攻","幽篁","百鬼","冥火薄天","龙鸣寒水","太极流光","九霄风雷","若木"}
		n[15] = {"琉璃珠","水晶珠","珍宝珠","翡翠珠","莲华珠","夜灵珠","如意宝珠","沧海明珠","无量玉璧","离火","飞星","月华","回风舞雪","紫金葫芦","裂云啸日","云雷万里","赤明"}
		n[16] = {"钝铁重剑","桃印铁刃","赭石巨剑","壁玉长铗","青铜古剑","金错巨刃","惊涛雪","醉浮生","沉戟天戊","鸦九","昆吾","弦歌","墨骨枯麟","腾蛇郁刃","秋水澄流","百辟镇魂","长息"}
		n[18] = {"素纸灯","竹骨灯","红灯笼","鲤鱼灯","芙蓉花灯","如意宫灯","玲珑盏","玉兔盏","冰心盏","蟠龙","云鹤","风荷","金风玉露","凰火燎原","月露清愁","夭桃秾李","荒尘"}
		n[17] = {"油纸伞","红罗伞","紫竹伞","锦绣椎","幽兰帐","琳琅盖","孔雀羽","金刚伞","落梅伞","鬼骨","云梦","枕霞","碧火琉璃","雪羽穿云","月影星痕","浮生归梦","晴雪"}
		n[19] = {{"方巾","簪子"},{"布帽","玉钗",},{"面具","梅花簪子"},{"纶巾","珍珠头带"},{"缨络丝带","凤头钗"},{"羊角盔","媚狐头饰"},{"水晶帽","玉女发冠"},{"乾坤帽","魔女发冠"},{"黑魔冠","七彩花环"},{"白玉龙冠","凤翅金翎"},{"水晶夔帽","寒雉霜蚕"},{"翡翠曜冠","曜月嵌星"},{"金丝黑玉冠","郁金流苏簪"},{"白玉琉璃冠","玉翼附蝉翎"},{"兽鬼珐琅面","鸾羽九凤冠"},{"紫金磐龙冠","金珰紫焰冠"},{"浑天玄火盔","乾元鸣凤冕"}}
		n[20] = {"护身符","五色飞石","珍珠链",{"骷髅吊坠","苍魂珠"},{"江湖夜雨","九宫坠"},{"荧光坠子","高速之星"},{"风月宝链","八卦坠"},{"碧水青龙","鬼牙攫魂"},{"万里卷云","疾风之铃"},"七彩玲珑","黄玉琉佩","鸾飞凤舞","衔珠金凤佩","七璜珠玉佩","鎏金点翠佩","紫金碧玺佩","落霞陨星坠"}
		n[21] = {{"布裙","布衣"},{"丝绸长裙","皮衣"},{"五彩裙","鳞甲"},{"龙鳞羽衣","锁子甲"},{"天香披肩","紧身衣"},{"金缕羽衣","钢甲"},{"霓裳羽衣","夜魔披风"},{"流云素裙","龙骨甲"},{"七宝天衣","死亡斗篷"},{"飞天羽衣","神谕披风"},{"穰花翠裙","珊瑚玉衣"},{"金蚕丝裙","金蚕披风"},{"紫香金乌裙","乾坤护心甲"},{"碧霞彩云衣","蝉翼金丝甲"},{"金丝蝉翼衫","金丝鱼鳞甲"},{"五彩凤翅衣","紫金磐龙甲"},{"鎏金浣月衣","混元一气甲"}}
		n[22] = {"腰带","缎带","银腰带",{"水晶腰带","玉树腰带"},{"琥珀腰链","白面狼牙"},{"乱牙咬","魔童大牙"},{"攫魂铃","双魂引"},{"兽王腰带","百窜云"},{"八卦锻带","圣王坠"},"幻彩玉带","珠翠玉环","金蟾含珠","乾坤紫玉带","琉璃寒玉带","蝉翼鱼佩带","磐龙凤翔带","紫霄云芒带"}
		n[23] = {"布鞋","牛皮靴","马靴","侠客履","神行靴","绿靴","追星踏月","九州履","万里追云履","踏雪无痕","平步青云","追云逐电","乾坤天罡履","七星逐月靴","碧霞流云履","金丝逐日履","辟尘分光履"}
		n[24] = {"竹编护腕","皮腕","针腕","骨镯","青铜护腕","玛瑙护腕","琉璃护腕","镂空银镯","笼玉镯","嵌宝金腕","玳瑁护腕","七星宝腕","缚龙筋","凤翎护腕","织锦彩带","冰蚕丝带"}
		n[25] = {"竹编脖环","钢圈","荆棘环","骨环","青铜颈环","玛瑙石环","琉璃环","九曲环","笼玉环","嵌宝金环","玳瑁环","七星宝环","缚龙圈","鸾尾环","织锦颈圈","冰蚕丝圈"}
		n[26] = {"竹藤甲","皮甲","刺甲","骨排甲","青铜披甲","玛瑙软甲","琉璃罩甲","连环铠甲","笼玉甲","嵌宝金甲","玳瑁衣","七星宝甲","缚龙甲","凤凰彩衣","织锦软褡","冰蚕织甲"}
		n[27] = {"商品棉布","商品佛珠","商品扇子","商品武器"}
		n[28] = {"商品纸钱","商品夜明珠","商品首饰","商品珍珠"}
		n[29] = {"商品帽子","商品盐","商品蜡烛","商品酒"}
		n[30] = {"商品木材","商品鹿茸","商品面粉","商品符"}
		n[31] = {"商品人参","商品铃铛","商品香油","商品麻线"}
	self.商品列表={}
		self.商品列表[1]={"方巾*500","簪子*500","布衣*500","布裙*500","布鞋*500","护身符*500","腰带*500","布帽*1000","玉钗*1000","皮衣*1000","丝绸长裙*1000","牛皮靴*1000","五色飞石*1000","缎带*1000"} --建邺城防具店
		self.商品列表[2]={"四叶花*100","紫丹罗*80","血色茶花*1500","熊胆*2500","丁香水*1500","麝香*2500","金创药*5000","佛光舍利子*20000","枯萎的金莲*100"} --建邺城药店
		self.商品列表[3]={"折扇*500","红缨枪*500","牛皮鞭*500","曲柳杖*500","铁爪*500","松木锤*500","琉璃珠*500","双短剑*500","青铜短剑*500","柳叶刀*500","青铜斧*500","五色缎带*500","黄铜圈*500","硬木弓*500","细木棒*500","钝铁重剑*500","素纸灯*500","油纸伞*500"} --建邺城药店
		self.商品列表[4]={"铁骨扇*1000","曲尖枪*1000","牛筋鞭*1000","红木杖*1000","天狼爪*1000","镔铁锤*1000","水晶珠*1000","镔铁双剑*1000","铁齿剑*1000","苗刀*1000","开山斧*1000","幻彩银纱*1000","精钢日月圈*1000","铁胆弓*1000","金丝魔棒*1000","桃印铁刃*1000","竹骨灯*1000","红罗伞*1000"} --建邺城药店
		self.商品列表[5]={"摄妖香*1000","洞冥草*500","飞行符*500"}
		self.商品列表[6]={"飞行符*500","乾坤袋*7000"}
		self.商品列表[7]={"面具*5000","梅花簪子*5000","鳞甲*5000","五彩裙*5000","马靴*5000"}
		self.商品列表[8]={"精钢扇*5000","锯齿矛*5000","乌龙鞭*5000","白椴杖*5000","幽冥鬼爪*5000","八棱金瓜*5000","珍宝珠*5000","龙凤双剑*5000","吴越剑*5000","夜魔弯刀*5000","双面斧*5000","金丝彩带*5000","离情环*5000","紫檀弓*5000","玉如意*5000","赭石巨剑*5000","红灯笼*5000","紫竹伞*5000"}
		self.商品列表[9]={"包子*100"}
		self.商品列表[10]={"乌金三叉戟*9000","双弦钺*9000","青锋剑*9000","竹节双剑*9000","无极丝*9000","青龙牙*9000","铁面扇*9000","点金棒*9000","狼牙锤*9000","钢结鞭*9000","金刺轮*9000","金背大砍刀*9000","墨铁拐*9000","宝雕长弓*9000","翡翠珠*9000","壁玉长铗*9000","鲤鱼灯*9000","锦绣椎*9000"}
		self.商品列表[11]={"纶巾*9000","珍珠头带*9000","水晶腰带*9000","玉树腰带*9000","侠客履*9000","龙鳞羽衣*9000","锁子甲*9000","骷髅吊坠*9000","苍魂珠*9000"}
		self.商品列表[12]={"白玉骨头*200","天青地白*200","龙须草*140"}
		self.商品列表[13]={"摄妖香*1000","洞冥草*500"}
		self.商品列表[14]={"女儿红*500"}
		self.商品列表[15]={"九宫坠*14000","白面狼牙*14000","江湖夜雨*14000","凤头钗*14000","缨络丝带*14000","神行靴*14000","琥珀腰链*14000","紧身衣*14000","天香披肩*14000"}
		self.商品列表[16]={}
		self.商品列表[17]={"灵脂*200","曼陀罗花*275","鬼切草*200","佛手*80"}
		self.商品列表[18]={"秘制红罗羹*200000","秘制绿罗羹*20000"}
		self.商品列表[19]={"香叶*140","百色花*140","鬼切草*200","佛手*80","七叶莲*150"}
		self.商品列表[20]={"银腰带*5000","珍珠链*5000","腰带*500","护身符*500"}
		self.商品列表[24]={"高级魔兽要诀*15000000","魔兽要诀*4000000","灵饰指南书*8000000","元灵晶石*5000000"}
		self.商品列表[25]={"四叶花*100","紫丹罗*80"}
		self.商品列表[26]={"青龙石*50","白虎石*50","朱雀石*50","玄武石*50","超级金柳露*100","魔兽要诀*100","高级魔兽要诀*200"}
	  self.商品列表[50]={"旋复花*80","曼陀罗花*275","灵脂*200"}
	  self.商品列表[51]={"太阳石*100000","红玛瑙*100000","月亮石*100000","黑宝石*100000","舍利子*100000","光芒石*100000","星辉石*150000","玉葫灵髓*250000","清灵净瓶*300000","易经丹*300000","碎星锤*1000000","超级碎星锤*10000000"}--宝石商人
    self.商会三药={"小还丹*80000","金香玉*80000","定神香*80000","五龙丹*80000","蛇蝎美人*80000","风水混元丹*80000","佛光舍利子*80000","九转回魂丹*80000","十香返生丸*80000","千年保心丹*80000"}
    self.商会烹饪={"烤鸭*80000","烤肉*80000","蛇胆酒*80000","百味酒*80000","梅花酒*80000","长寿面*80000","翡翠豆腐*80000","桂花丸*80000","珍露酒*80000","豆斋果*80000","臭豆腐*80000","佛跳墙*80000","虎骨酒*80000","女儿红*80000","醉生梦死*80000"}
    self.一级仙灵店铺={"飞行符*450","摄妖香*450","洞冥草*450","天眼通符*20000"}
    self.二级仙灵店铺={"飞行符*450","摄妖香*450","洞冥草*450","天眼通符*20000","蓝色导标旗*8000","黄色导标旗*8000","红色导标旗*8000","绿色导标旗*8000","白色导标旗*8000"}
    self.三级仙灵店铺={"飞行符*450","摄妖香*450","洞冥草*450","天眼通符*20000","蓝色导标旗*8000","黄色导标旗*8000","红色导标旗*8000","绿色导标旗*8000","白色导标旗*8000","金创药*4000","佛光舍利子*15000"}
    self.四级仙灵店铺={"飞行符*450","摄妖香*450","洞冥草*450","天眼通符*20000","蓝色导标旗*8000","黄色导标旗*8000","红色导标旗*8000","绿色导标旗*8000","白色导标旗*8000","金创药*4000","佛光舍利子*15000","秘制红罗羹*200000","秘制绿罗羹*200000"}
    self.五级仙灵店铺={"飞行符*450","摄妖香*450","洞冥草*450","天眼通符*20000","蓝色导标旗*8000","黄色导标旗*8000","红色导标旗*8000","绿色导标旗*8000","白色导标旗*8000","金创药*4000","佛光舍利子*15000","秘制红罗羹*200000","秘制绿罗羹*200000","金柳露*100000","青龙石*100000","白虎石*100000","朱雀石*100000","玄武石*100000","光芒石*100000","月亮石*100000","太阳石*100000","舍利子*100000","红玛瑙*100000","黑宝石*100000","空白强化符*500"}
    self.仙缘商店={"一级仙缘*5","二级仙缘*10","三级仙缘*15","四级仙缘*20","五级仙缘*25","六级仙缘*30","七级仙缘*35","八级仙缘*40","九级仙缘*45","十级仙缘*50"}
    self.文韵商店={"青龙石*50","白虎石*50","朱雀石*50","玄武石*50","超级金柳露*100","魔兽要诀*100","高级魔兽要诀*200"}



	--21 23 为珍品商人
	for i=1,17 do
		self.商品列表[16][i]=n[i][5].."*14000"
	end
end

function 商店处理类:更新商店价格数据()
     if 自定义数据.钓鱼积分~=nil then
          self.商品列表[26]={}
          for i=1,#自定义数据.钓鱼积分 do
            if 自定义数据.钓鱼积分[i].名称~=nil and 自定义数据.钓鱼积分[i].价格~=nil then
              self.商品列表[26][#self.商品列表[26]+1]=自定义数据.钓鱼积分[i].名称.."*"..自定义数据.钓鱼积分[i].价格
            end
          end
      end
   if 自定义数据.建业杂货~=nil then
          self.商品列表[5]={}
          for i=1,#自定义数据.建业杂货 do
            if 自定义数据.建业杂货[i].名称~=nil and 自定义数据.建业杂货[i].价格~=nil then
              self.商品列表[5][#self.商品列表[5]+1]=自定义数据.建业杂货[i].名称.."*"..自定义数据.建业杂货[i].价格
            end
          end
      end
      if 自定义数据.建业飞儿~=nil then
          self.商品列表[9]={}
          for i=1,#自定义数据.建业飞儿 do
            if 自定义数据.建业飞儿[i].名称~=nil and 自定义数据.建业飞儿[i].价格~=nil then
              self.商品列表[9][#self.商品列表[9]+1]=自定义数据.建业飞儿[i].名称.."*"..自定义数据.建业飞儿[i].价格
            end
          end
      end
      if 自定义数据.酒店老板~=nil then
          self.商品列表[18]={}
          for i=1,#自定义数据.酒店老板 do
            if 自定义数据.酒店老板[i].名称~=nil and 自定义数据.酒店老板[i].价格~=nil then
              self.商品列表[18][#self.商品列表[18]+1]=自定义数据.酒店老板[i].名称.."*"..自定义数据.酒店老板[i].价格
            end
          end
      end
      if 自定义数据.宝石商人~=nil then
          self.商品列表[51]={}
          for i=1,#自定义数据.宝石商人 do
            if 自定义数据.宝石商人[i].名称~=nil and 自定义数据.宝石商人[i].价格~=nil then
              self.商品列表[51][#self.商品列表[51]+1]=自定义数据.宝石商人[i].名称.."*"..自定义数据.宝石商人[i].价格
            end
          end
      end
       if 自定义数据.云游道人~=nil then
          self.商品列表[24]={}
          for i=1,#自定义数据.云游道人 do
            if 自定义数据.云游道人[i].名称~=nil and 自定义数据.云游道人[i].价格~=nil then
              self.商品列表[24][#self.商品列表[24]+1]=自定义数据.云游道人[i].名称.."*"..自定义数据.云游道人[i].价格
            end
          end
      end
       if 自定义数据.傲来杂货~=nil then
          self.商品列表[13]={}
          for i=1,#自定义数据.傲来杂货 do
            if 自定义数据.傲来杂货[i].名称~=nil and 自定义数据.傲来杂货[i].价格~=nil then
              self.商品列表[13][#self.商品列表[13]+1]=自定义数据.傲来杂货[i].名称.."*"..自定义数据.傲来杂货[i].价格
            end
          end
      end
      if 自定义数据.长安罗道人~=nil then
          self.商品列表[6]={}
          for i=1,#自定义数据.长安罗道人 do
            if 自定义数据.长安罗道人[i].名称~=nil and 自定义数据.长安罗道人[i].价格~=nil then
              self.商品列表[6][#self.商品列表[6]+1]=自定义数据.长安罗道人[i].名称.."*"..自定义数据.长安罗道人[i].价格
            end
          end
      end
      if 自定义数据.商会三药~=nil then
         self.商会三药={}
         for i=1,#自定义数据.商会三药 do
             if 自定义数据.商会三药[i].名称~=nil and 自定义数据.商会三药[i].价格~=nil then
                self.商会三药[#self.商会三药+1]=自定义数据.商会三药[i].名称.."*"..自定义数据.商会三药[i].价格
             end
          end
      end
      if 自定义数据.商会烹饪~=nil then
         self.商会烹饪={}
         for i=1,#自定义数据.商会烹饪 do
             if 自定义数据.商会烹饪[i].名称~=nil and 自定义数据.商会烹饪[i].价格~=nil then
                self.商会烹饪[#self.商会烹饪+1]=自定义数据.商会烹饪[i].名称.."*"..自定义数据.商会烹饪[i].价格
             end
          end
      end
      if 自定义数据.一级仙灵店铺~=nil then
         self.一级仙灵店铺={}
         for i=1,#自定义数据.一级仙灵店铺 do
             if 自定义数据.一级仙灵店铺[i].名称~=nil and 自定义数据.一级仙灵店铺[i].价格~=nil then
                self.一级仙灵店铺[#self.一级仙灵店铺+1]=自定义数据.一级仙灵店铺[i].名称.."*"..自定义数据.一级仙灵店铺[i].价格
             end
          end
      end
      if 自定义数据.二级仙灵店铺~=nil then
         self.二级仙灵店铺={}
         for i=1,#自定义数据.二级仙灵店铺 do
             if 自定义数据.二级仙灵店铺[i].名称~=nil and 自定义数据.二级仙灵店铺[i].价格~=nil then
                self.二级仙灵店铺[#self.二级仙灵店铺+1]=自定义数据.二级仙灵店铺[i].名称.."*"..自定义数据.二级仙灵店铺[i].价格
             end
          end
      end
      if 自定义数据.三级仙灵店铺~=nil then
         self.三级仙灵店铺={}
         for i=1,#自定义数据.三级仙灵店铺 do
             if 自定义数据.三级仙灵店铺[i].名称~=nil and 自定义数据.三级仙灵店铺[i].价格~=nil then
                self.三级仙灵店铺[#self.三级仙灵店铺+1]=自定义数据.三级仙灵店铺[i].名称.."*"..自定义数据.三级仙灵店铺[i].价格
             end
          end
      end
      if 自定义数据.四级仙灵店铺~=nil then
         self.四级仙灵店铺={}
         for i=1,#自定义数据.四级仙灵店铺 do
             if 自定义数据.四级仙灵店铺[i].名称~=nil and 自定义数据.四级仙灵店铺[i].价格~=nil then
                self.四级仙灵店铺[#self.四级仙灵店铺+1]=自定义数据.四级仙灵店铺[i].名称.."*"..自定义数据.四级仙灵店铺[i].价格
             end
          end
      end
      if 自定义数据.五级仙灵店铺~=nil then
         self.五级仙灵店铺={}
         for i=1,#自定义数据.五级仙灵店铺 do
             if 自定义数据.五级仙灵店铺[i].名称~=nil and 自定义数据.五级仙灵店铺[i].价格~=nil then
                self.五级仙灵店铺[#self.五级仙灵店铺+1]=自定义数据.五级仙灵店铺[i].名称.."*"..自定义数据.五级仙灵店铺[i].价格
             end
          end
      end
    if 自定义数据.文韵商店~=nil then
           self.文韵商店={}
           for i=1,#自定义数据.文韵商店 do
               if 自定义数据.文韵商店[i].名称~=nil and 自定义数据.文韵商店[i].价格~=nil then
                  self.文韵商店[#self.文韵商店+1]=自定义数据.文韵商店[i].名称.."*"..自定义数据.文韵商店[i].价格
               end
            end
        end




end

function 商店处理类:刷新跑商商品买入价格()
	self.商品列表[27] = {"商品棉布*"..取随机数(3200,3800),"商品佛珠*"..取随机数(6200,8000),"商品扇子*"..取随机数(3500,4200),"商品武器*"..取随机数(3600,4500)}
	self.商品列表[28] = {"商品棉布*"..取随机数(3200,3800),"商品佛珠*"..取随机数(6200,8000),"商品扇子*"..取随机数(3500,4200),"商品武器*"..取随机数(3600,4500)}
	self.商品列表[29] = {"商品纸钱*"..取随机数(2600,3400),"商品夜明珠*"..取随机数(7600,9000),"商品首饰*"..取随机数(3600,4800),"商品珍珠*"..取随机数(5000,6000)}
	self.商品列表[30] = {"商品纸钱*"..取随机数(2600,3400),"商品夜明珠*"..取随机数(7600,9000),"商品首饰*"..取随机数(3600,4800),"商品珍珠*"..取随机数(5000,6000)}
	self.商品列表[31] = {"商品帽子*"..取随机数(3000,4000),"商品盐*"..取随机数(4800,6000),"商品蜡烛*"..取随机数(1500,2500),"商品酒*"..取随机数(3200,4500)}
	self.商品列表[32] = {"商品帽子*"..取随机数(3000,4000),"商品盐*"..取随机数(4800,6000),"商品蜡烛*"..取随机数(1500,2500),"商品酒*"..取随机数(3200,4500)}
	self.商品列表[33] = {"商品木材*"..取随机数(3200,5000),"商品鹿茸*"..取随机数(6800,8500),"商品面粉*"..取随机数(2500,3500),"商品符*"..取随机数(4500,6000)}
	self.商品列表[34] = {"商品木材*"..取随机数(3200,5000),"商品鹿茸*"..取随机数(6800,8500),"商品面粉*"..取随机数(2500,3500),"商品符*"..取随机数(4500,6000)}
	self.商品列表[35] = {"商品人参*"..取随机数(6500,9000),"商品铃铛*"..取随机数(3200,4800),"商品香油*"..取随机数(3200,5000),"商品麻线*"..取随机数(2000,3800)}
	self.商品列表[36] = {"商品人参*"..取随机数(6500,9000),"商品铃铛*"..取随机数(3200,4800),"商品香油*"..取随机数(3200,5000),"商品麻线*"..取随机数(2000,3800)}
end

function 商店处理类:刷新跑商商品卖出价格()
	self.商品列表[37] = {"商品棉布*"..取随机数(3200,3800),"商品佛珠*"..取随机数(6200,8000),"商品扇子*"..取随机数(3500,4200),"商品武器*"..取随机数(3600,4500)}
	self.商品列表[38] = {"商品纸钱*"..取随机数(2600,3400),"商品夜明珠*"..取随机数(7600,9000),"商品首饰*"..取随机数(3600,4800),"商品珍珠*"..取随机数(5000,6000)}
	self.商品列表[39] = {"商品帽子*"..取随机数(3000,4000),"商品盐*"..取随机数(4800,6000),"商品蜡烛*"..取随机数(1500,2500),"商品酒*"..取随机数(3200,4500)}
	self.商品列表[40] = {"商品木材*"..取随机数(3200,5000),"商品鹿茸*"..取随机数(6800,8500),"商品面粉*"..取随机数(2500,3500),"商品符*"..取随机数(4500,6000)}
	self.商品列表[41] = {"商品人参*"..取随机数(6500,9000),"商品铃铛*"..取随机数(3200,4800),"商品香油*"..取随机数(3200,5000),"商品麻线*"..取随机数(2000,3800)}
end


function 商店处理类:设置抽奖物品(id)
  if 自定义数据.抽奖配置~=nil and 自定义数据.抽奖配置[24] then
  	  local 中奖编号 = 0
  	  local 总概率 = 0
      神秘宝箱[id]=自定义数据.抽奖配置
      local 获得物品={}
      local 可以获得={}
      for i=1,24 do
      	总概率 = 总概率+自定义数据.抽奖配置[i].概率
      end
      for i=1,24 do
      	if 自定义数据.抽奖配置[i].概率>0 and 取随机数(1,总概率)<=自定义数据.抽奖配置[i].概率 then
      	   获得物品[#获得物品+1]={物品=自定义数据.抽奖配置[i],编号=i}
      	end
      	if 自定义数据.抽奖配置[i].概率>0 then
      	   可以获得[#可以获得+1] ={物品=自定义数据.抽奖配置[i],编号=i}
      	end
      end
        获得物品=删除重复(获得物品)
        if 获得物品~=nil then
            local 取编号=取随机数(1,#获得物品)
            if 获得物品[取编号]~=nil and 获得物品[取编号].物品~=nil and 获得物品[取编号].编号~=nil then
               中奖编号 = 获得物品[取编号].编号
            end
        end
        if  中奖编号==0 then
        	local 可以编号=可以获得[取随机数(1,#可以获得)].编号
        	神秘宝箱[id].中奖=可以编号
        else
            神秘宝箱[id].中奖=中奖编号
        end
       神秘宝箱[id].抽奖类型="自定义抽奖"
   else
      self:设置神秘宝箱(id)
   end

end




function 商店处理类:设置神秘宝箱(id)
 	神秘宝箱[id]={}
 	local 名称=""
 	local 参数={}
 	local 备注=""
 	local 奖励=取随机数(1,420)
 	local 说明=""
 	local 中奖 = 0
    if 奖励<=5 then
	    中奖=3
	elseif 奖励<=10 then
		中奖=23
	elseif 奖励<=30 then
		中奖=17
	elseif 奖励<=40 then
		中奖=20
	elseif 奖励<=10 then
		中奖=21
	elseif 奖励<=60 then
		中奖=18
	elseif 奖励<=70 then
		中奖=19
	elseif 奖励<=90 then
		中奖=1
	elseif 奖励<=110 then
		中奖=2
	elseif 奖励<=130 then
		中奖=5
	elseif 奖励<=150 then
		中奖=6
	elseif 奖励<=170 then
		中奖=7
	elseif 奖励<=190 then
		中奖=8
	elseif 奖励<=210 then
		中奖=10
	elseif 奖励<=230 then
		中奖=11
	elseif 奖励<=250 then
		中奖=12
	elseif 奖励<=270 then
		中奖=13
	elseif 奖励<=310 then
		中奖=14
	elseif 奖励<=330 then
		中奖=15
	elseif 奖励<=350 then
		中奖=16
	elseif 奖励<=370 then
		中奖=22
	elseif 奖励<=390 then
		中奖=17
    elseif 奖励<=400 then
		中奖=4
	elseif 奖励<=410 then
		中奖=9
	else
		中奖=24
	end

  	神秘宝箱[id]={
  		[1]={
 			名称="仙玉锦囊",
 			说明="10W仙玉",
 			备注="10W仙玉"
  		},
  		[2]={
  			名称="150精铁",
 			说明="150级装备制造精铁",
 			备注="150百炼精铁"
  		},

  		[3]={
  		    名称="120自选",
 			说明="120装备自选礼包",
 			备注="120装备自选礼包"

  		},
  		[4]={
  			名称="高内丹",
 			说明="高级召唤兽内丹",
 			备注="高级召唤兽内丹"
  		},
  		[5]={
  		    名称="星辉石",
 			说明="5-8级星辉石",
 			备注="5-8级星辉石"

  		},

  		[6]={
  		    名称="仙丹",
 			说明="1E经验",
 			备注="1E经验"

  		},
  		[7]={
  			名称="太阳石",
 			说明="12-15级太阳石",
 			备注="12-15级太阳石"
  		},
  		[8]={
  			名称="150指南",
 			说明="随机类型\n150级装备制造书",
 			备注="150制造指南书"
  		},

  		[9]={
  			名称="黑宝石",
 			说明="12-15级黑宝石",
 			备注="12-15级黑宝石"
  		},
  		[10]={
  		    名称="内丹",
 			说明="召唤兽内丹",
 			备注="召唤兽内丹"

  		},
  		[11]={

 			名称="要诀",
 			说明="特殊魔兽要诀",
 			备注="特殊魔兽要诀"
  		},
  		[12]={
  			名称="舍利子",
 			说明="12-15级舍利子",
 			备注="12-15级舍利子"
  		},
  		[13]={
  			名称="月亮石",
 			说明="12-15级月亮石",
 			备注="12-15级月亮石"
  		},
  		[14]={

 			名称="金砖",
 			说明="1E银子",
 			备注="1E银子"
  		},
  		[15]={
  			名称="星辉石",
 			说明="5-8级星辉石",
 			备注="5-8级星辉石"
  		},
  		[16]={
  			名称="光芒石",
 			说明="12-15级光芒石",
 			备注="12-15级光芒石"
  		},
  		[17]={
  			名称="仙丹",
 			说明="1E经验",
 			备注="1E经验"
  		},
  		[18]={
  			名称="高内丹",
 			说明="高级召唤兽内丹",
 			备注="高级召唤兽内丹"
  		},
  		[19]={
  			名称="红玛瑙",
 			说明="12-15级红玛瑙",
 			备注="12-15级红玛瑙"
  		},
  		[20]={
  			名称="光芒石",
 			说明="12-15级光芒石",
 			备注="光芒石"
  		},
  		[21]={
  			名称="160指南",
 			说明="随机类型\n160级装备制造书",
 			备注="制造指南书"
  		},
  		[22]={
  			名称="金砖",
 			说明="1E银子",
 			备注="1E银子"
  		},
  		[23]={
  			名称="130自选",
 			说明="130装备自选礼包",
 			备注="自选礼包"
  		},
  		[24]={

 			名称="160精铁",
 			说明="160级装备制造精铁",
 			备注="1百炼精铁"
  		},
  		中奖=中奖,
  		抽奖类型 = "神秘宝箱",
	}
end

function 商店处理类:刷新珍品()
	local 等级范围={5,7,6,8,5,6}
	for n=21,23 do
		self.商品列表[n]={}
		for i=1,取随机数(25,25) do
			local 等级=等级范围[取随机数(1,#等级范围)]
			local 名称=取随机装备名称(等级)
			local 价格=0
			if 等级==5 then
				价格=25000
			elseif 等级==6 then
				价格=50000
			elseif 等级==7 then
				价格=100000
			elseif 等级>=8 then
				价格=150000
			end
			self.商品列表[n][i]=名称.."*"..价格
		end
	end
end






return 商店处理类