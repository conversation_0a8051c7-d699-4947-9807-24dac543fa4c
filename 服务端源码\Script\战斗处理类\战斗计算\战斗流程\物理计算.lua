


function 战斗处理类:取基础物理伤害(编号,目标,名称,等级,系数)
          --系数= {初始系数=1,叠加系数=0,初始伤害=0,防御系数=1,忽视防御=0, ---初始计算
          --       暴击系数=1,暴击增加=0,暴伤系数=1,暴伤增加=0,            ---暴击计算
          --       结果系数=1,结果伤害=0,特效={}}                          ---最终计算
          if not 系数.特效 then 系数.特效={} end
          if self.参战单位[编号].奇经八脉.不惊 then
             local 锻造数量= self:取装备宝石数量(编号,3,"红玛瑙")
             锻造数量 = 锻造数量 + self:取装备宝石数量(编号,3,"太阳石")
             系数.防御系数 = 系数.防御系数 - (锻造数量*0.5+3)/100
          end

          系数.防御系数 = 系数.防御系数 + self:取抗物特性(self.参战单位[目标].队伍)
          if 名称=="反击" and self.参战单位[编号].超级反击 then
                系数.防御系数 = 系数.防御系数 - 0.1
          end
          if self.参战单位[编号].符石技能.天降大任 and  self.参战单位[目标].类型=="bb" then
              系数.防御系数 = 系数.防御系数 - self.参战单位[编号].符石技能.天降大任 / 100
          end
          if self.参战单位[编号].奇经八脉.不动 and self:取是否单独门派(编号) then
             系数.防御系数 = 系数.防御系数 - (1-self.参战单位[编号].气血/self.参战单位[编号].最大气血)*0.1
          end

          if self.参战单位[编号].类型=="角色" and self.参战单位[目标].法术状态.天地同寿 and self.参战单位[目标].法术状态.天地同寿.同辉 then
             系数.防御系数 = 系数.防御系数 - 0.15
          end
          if self.参战单位[目标].奇经八脉轰鸣 then
             系数.忽视防御 = 系数.忽视防御 +  self.参战单位[目标].等级*1.6
          end
          if self.参战单位[编号].力破特性 then
              if self.参战单位[目标].类型=="角色" then
                  系数.忽视防御 = 系数.忽视防御 + (self.参战单位[编号].力破特性*40+40)
              elseif self.参战单位[目标].类型=="bb" then
                   系数.结果系数 = 系数.结果系数 - 0.05
              end
          end
          if self.参战单位[目标].奇经八脉.普照 then
              for k,v in pairs(self.参战单位) do
                 if v.队伍~=self.参战单位[目标].队伍 and v.法术状态.普渡众生 then
                    系数.忽视防御 = 系数.忽视防御 - 15
                 end
              end
          end
          if self.参战单位[目标].奇经八脉.庄严 then
              for k,v in pairs(self.参战单位) do
                 if v.队伍~=self.参战单位[目标].队伍 and v.法术状态.紧箍咒 then
                    系数.忽视防御 = 系数.忽视防御 - 15
                 end
              end
          end
          if self.参战单位[编号].奇经八脉.无极 and self.参战单位[编号].法术状态.生命之泉~=nil and self.参战单位[编号].法术状态.炼气化神~=nil then
             系数.初始系数 = 系数.初始系数 + 0.09
          end
          if self.参战单位[编号].奇经八脉.混元 and self.参战单位[编号].门派=="五庄观" and self.参战单位[编号].气血>= self.参战单位[编号].最大气血*0.7 then
             系数.初始系数 = 系数.初始系数 +  0.05
          end
          if self.参战单位[编号].奇经八脉.道果 and self.参战单位[编号].人参娃娃~=nil and self.参战单位[编号].人参娃娃.回合>0 and self.参战单位[编号].人参娃娃.层数>0 then
             系数.初始系数 = 系数.初始系数 + 0.04 * self.参战单位[编号].人参娃娃.层数
          end

          if self.参战单位[编号].神器技能惊锋~=nil then
             系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].神器技能惊锋.数额*self.参战单位[编号].神器技能惊锋.层数
          end
          if self.参战单位[编号].神器技能狂战~=nil then
             系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].神器技能狂战.数额*self.参战单位[编号].神器技能狂战.层数
          end
          if self.参战单位[编号].奇经八脉.夜行 and 昼夜参数==1 then
             系数.初始伤害 = 系数.初始伤害 + 40
          end
          if self.参战单位[编号].奇经八脉啸傲 then
              系数.初始伤害 = 系数.初始伤害 + 15 * self.参战单位[编号].奇经八脉啸傲
          end
          if self.参战单位[编号].奇经八脉.凌弱 and  self:取异常数量(目标)>=2 then
             系数.初始伤害 = 系数.初始伤害 + 80
          end

          if self.参战单位[编号].奇经八脉狂袭 and self.参战单位[编号].奇经八脉狂袭.回合>0 then
              系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].奇经八脉狂袭.加成
          end
          if self.参战单位[编号].奇经八脉.煞气 and self.参战单位[编号].经脉流派 ~= "风云战将" then
              系数.初始伤害 = 系数.初始伤害 + 150
              系数.结果系数 = 系数.结果系数 + 0.06
          end

          if self.参战单位[编号].奇经八脉.怒火 and self.参战单位[编号].门派=="凌波城" and self.参战单位[编号].战意 and self.参战单位[编号].战意>0 then
             系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].战意*self.参战单位[编号].战意*6
          end
          if self.参战单位[编号].奇经八脉.凝神 and self.参战单位[编号].人参娃娃 and self.参战单位[编号].人参娃娃.回合>0 and self.参战单位[编号].人参娃娃.层数>0 then
             系数.初始伤害 = 系数.初始伤害 + 120
          end
          if self.参战单位[编号].奇经八脉.气盛 and self.参战单位[编号].人参娃娃 and self.参战单位[编号].人参娃娃.回合>0 and self.参战单位[编号].人参娃娃.层数>0 then
             系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].人参娃娃.层数*60
          end

          -----------------开始计算

          local 防御 = self.参战单位[目标].防御
          local 伤害 = self.参战单位[编号].伤害
          防御 = math.floor(防御 * 系数.防御系数 - 系数.忽视防御)
          伤害 = math.floor(伤害 * 系数.初始系数 + 系数.初始伤害 + self.参战单位[编号].穿刺等级)
          local 结果 = 0
          if 伤害>防御 then
              local 比例加成 = 100-math.floor(防御/伤害*100)
              if 比例加成<=50 then
                  伤害= math.floor(伤害 *1.04)
              elseif 比例加成<=40 then
                    伤害= math.floor(伤害 *1.08)
              elseif 比例加成<=30 then
                    伤害= math.floor(伤害 *1.12)
              elseif 比例加成<=20 then
                    伤害= math.floor(伤害 *1.16)
              elseif 比例加成<=10 then
                    伤害= math.floor(伤害 *1.2)
              end
                结果 = 伤害-防御
          else
             结果 = math.floor(伤害*0.05)
          end
          结果 = math.floor(结果*(1+self.参战单位[编号].攻击修炼*0.025-self.参战单位[目标].防御修炼*0.025)+(self.参战单位[编号].攻击修炼*6)-(self.参战单位[目标].防御修炼*6))
          if self.参战单位[编号].门派=="九黎城"
            and (名称=="枫影二刃" or 名称=="一斧开天"
            or 名称=="铁血生风" or 名称=="三荒尽灭"
            or 名称=="力劈苍穹" or 名称=="魔神之刃") then
              结果 = math.floor(结果 * 0.4)
          end
          结果 = math.floor(结果*取随机数(95,105)/100)
          return 结果
end









function 战斗处理类:取物理动作计算(编号,目标,名称,等级,系数)
          --系数= {暴击系数=1,暴击增加=0,暴伤系数=1,暴伤增加=0,            ---暴击计算
          --       结果系数=1,结果伤害=0,特效={}}                          ---最终计算 --其他动作
----------------------------------------------------------------------其他加成
            if not 系数.特效 then 系数.特效={} end
          if self.参战单位[目标].灵身 then
              系数.结果系数 = 系数.结果系数 + 0.5
          end
          if self.参战单位[编号].奇经八脉恶焰 then
             系数.结果系数 = 系数.结果系数 + 0.12
          end
          if self.参战单位[编号].奇经八脉入魂 then
             系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].奇经八脉存雄 then
             系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].奇经八脉莲音 then
             系数.结果系数 = 系数.结果系数 + 0.2
          end
          if self.参战单位[编号].奇经八脉强袭 then
             系数.结果系数 = 系数.结果系数 + 0.16
          end
          if self.参战单位[编号].乐韵加成 then
             if self.参战单位[编号].奇经八脉.天籁 then
                 系数.结果系数 = 系数.结果系数 + 0.4
             else
                 系数.结果系数 = 系数.结果系数 + 0.3
             end
          end
          if self.参战单位[编号].奇经特效.返璞 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[目标].法术状态.天地同寿 then
             系数.结果系数 = 系数.结果系数 + 1
          end

          if self.参战单位[编号].从天而降 and 取随机数() <= 30 then
              系数.结果系数 = 系数.结果系数  + 0.3
          end
          if self.参战单位[编号].合纵 then --重写
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].合纵
          end
          if self.参战单位[编号].偷袭 then --重写
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].偷袭
          end
           if self.参战单位[编号].奇经八脉.风姿 and 取随机数()<=50 then
             系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].鬼魂 and self.参战单位[目标].信仰 then
              系数.结果系数 = 系数.结果系数  + 0.2
          end
          if self.参战单位[编号].无畏 and self.参战单位[目标].反震 then --重写
              系数.结果系数 = 系数.结果系数  + self.参战单位[编号].无畏
          end
          if self.参战单位[编号].愤恨 and self.参战单位[目标].幸运 then --重写
              系数.结果系数 = 系数.结果系数  +  self.参战单位[编号].愤恨
          end
          if self.参战单位[目标].鬼魂 and self.参战单位[编号].驱鬼 then --重写
              系数.结果系数 = 系数.结果系数  + self.参战单位[编号].驱鬼
          end
          if self.参战单位[编号].法术状态.灵刃 then
              if self.参战单位[目标].鬼魂 or self.参战单位[目标].神佑 then
                  系数.结果系数 = 系数.结果系数 + 0.1
              else
                  系数.结果系数 = 系数.结果系数 + 0.5
              end
          end

          if self.参战单位[编号].灵宝乾坤金卷  then
             系数.结果系数 = 系数.结果系数 + self.参战单位[编号].灵宝乾坤金卷
          end
          if self.参战单位[目标].抗法特性 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[目标].抗法特性*3*0.01
          end
          if self.参战单位[编号].轻歌飘舞加成 then
               系数.结果系数 = 系数.结果系数  + self.参战单位[编号].轻歌飘舞加成*0.05
          end
          if self:取指定法宝(编号,"七杀",1) then
             系数.结果系数 = 系数.结果系数 + self:取指定法宝(编号,"七杀")*0.02
          end
          if self.参战单位[编号].超级战意消耗 then
               系数.结果系数 = 系数.结果系数 + 0.2*self.参战单位[编号].超级战意消耗
               if self.参战单位[编号].奇经八脉.惊涛   then
                  if self:取装备五行(编号,3)=="水" then
                     系数.结果系数 = 系数.结果系数 + 0.04*self.参战单位[编号].超级战意消耗
                  end
                  if self:取装备五行(编号,4)=="水" then
                     系数.结果系数 = 系数.结果系数 + 0.04*self.参战单位[编号].超级战意消耗
                  end
               end
               if self.参战单位[编号].奇经八脉.追袭 then
                  self.参战单位[目标].奇经八脉追袭 = 1
               end
          end

          if self.参战单位[编号].奇经八脉.业障 and self.参战单位[目标].类型~="角色" then
             系数.结果系数 = 系数.结果系数 +  0.2
          end
          if  self.参战单位[编号].门派 == "凌波城" and self:取指定法宝(编号,"天煞",1) then
              系数.结果系数 = 系数.结果系数 + self:取指定法宝(编号,"天煞") * 0.025
          end
          if self.参战单位[编号].奇经八脉傲视 and self.参战单位[编号].奇经八脉傲视<=3 then
              系数.结果系数 = 系数.结果系数 + 0.36
          end

          if self.参战单位[编号].驱怪 and self.参战单位[编号].驱怪~=0 then
                if self.参战单位[编号].驱怪==1 then
                    系数.结果系数 = 系数.结果系数 +  0.1
                elseif self.参战单位[编号].驱怪==2 then
                        系数.结果系数 = 系数.结果系数 +  0.2
                elseif self.参战单位[编号].驱怪==3 then
                        系数.结果系数 = 系数.结果系数 +  0.25
                        if 取随机数(1,1000) <= 10 and not self.参战单位[编号].超级驱怪 then
                            系数.结果伤害 = self.参战单位[目标].气血
                            self.参战单位[编号].超级驱怪=5
                        end
                end
          end
          if self.参战单位[编号].奇经八脉.杀戮 and  self.参战单位[目标].法术状态.天罗地网 then
             系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].战斗赐福~=nil and self.参战单位[编号].战斗赐福.物伤结果>0 then
                系数.结果系数 = 系数.结果系数 + self.参战单位[编号].战斗赐福.物伤结果/100
          end
          if self.参战单位[编号].奇经八脉.伤魂 and self.参战单位[编号].经脉流派=="六道魍魉" then
              if self:取异常数量(目标)>=1 then
                  系数.结果系数 = 系数.结果系数 + 0.05
              end
          end
          if self.参战单位[编号].法术状态.北冥之渊~=nil and self.参战单位[编号].类型~="角色" then
              系数.结果系数 = 系数.结果系数 + 0.3
          end
          if self.参战单位[编号].法术状态.金刚镯 and self.参战单位[编号].法术状态.金刚镯.套索 then
             系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[目标].奇经八脉勇武加成 and self.参战单位[目标].奇经八脉勇武加成==编号 then
             系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].风起龙游 and self.参战单位[编号].速度> self.参战单位[目标].速度 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end
          if self.参战单位[编号].超级强力  and not self.参战单位[目标].防御被动 and 取随机数()<=10 then
              self.参战单位[目标].强力眩晕=2
          end

          if self.参战单位[编号].神话词条 then
              if self.参战单位[编号].门派=="阴曹地府"  and self.参战单位[编号].神话词条.钟馗转生 then
                    系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.钟馗转生*0.1
              elseif self.参战单位[编号].门派=="大唐官府"  and self.参战单位[编号].神话词条.无双剑意 then
                      系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.无双剑意*0.08
              elseif self.参战单位[编号].门派=="狮驼岭"  and self.参战单位[编号].神话词条.鹰抟九天 then
                      系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.鹰抟九天*0.1
              elseif self.参战单位[编号].门派=="九黎城" and self.参战单位[编号].神话词条.逐鹿中原  then
                      系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.逐鹿中原*0.05
              end
              if self.参战单位[编号].神话词条.洪荒力魄 then
                    系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.洪荒力魄*0.04
              end
          end

          if self.参战单位[编号].奇经八脉.剑势 and  self.参战单位[目标].气血>self.参战单位[目标].最大气血*0.9 then
             系数.结果系数 = 系数.结果系数 + 0.09
          end
          if self.参战单位[编号].奇经八脉.矫健 and self.参战单位[编号].气血>=self.参战单位[编号].最大气血*0.7 then
             if self.参战单位[编号].门派=="女儿村" and self.参战单位[编号].气血>=self.参战单位[编号].最大气血*0.9 then
                系数.结果系数 = 系数.结果系数 + 0.1
             end
             if self.参战单位[编号].门派=="狮驼岭" then
                 系数.结果系数 = 系数.结果系数 + 0.1
             end
          end
          if self.参战单位[编号].奇经八脉飞扬 and self.参战单位[编号].取消浮空 and #self.参战单位[编号].取消浮空>=5 then
              local 倍数=6*(self.参战单位[编号].奇经八脉飞扬-1)
              系数.结果系数 = 系数.结果系数 +倍数/100
          end
          if self.参战单位[编号].类型~="角色" and self.参战单位[编号].主人 and self.参战单位[self.参战单位[编号].主人].神话词条~=nil
              and self.参战单位[self.参战单位[编号].主人].神话词条.兽语御灵 then
               系数.结果系数 = 系数.结果系数 + self.参战单位[self.参战单位[编号].主人].神话词条.兽语御灵*0.08
          end
          if self.参战单位[编号].奇经八脉.协战 and self.参战单位[编号].召唤兽 and self.参战单位[self.参战单位[编号].召唤兽] and self.参战单位[self.参战单位[编号].召唤兽].气血>0 then
               系数.结果系数 = 系数.结果系数 + 0.07
          end
          if self.参战单位[编号].类型~="角色" and self.参战单位[编号].主人 and self.参战单位[self.参战单位[编号].主人]
            and self.参战单位[self.参战单位[编号].主人].奇经八脉.协同 and  self:取是否单独门派(self.参战单位[编号].主人)
            and self:取封印状态(目标) then
             系数.结果系数 = 系数.结果系数 + 0.25
          end
          if self.参战单位[编号].奇经八脉.威震 and (self.参战单位[编号].法术状态.天眼 or self.参战单位[编号].法术状态.怒眼 or self.参战单位[编号].法术状态.智眼) then
              系数.结果系数 = 系数.结果系数 + 0.04
          end
------------------------------------加减
          if self.参战单位[编号].狂莽一击 then
              if 取随机数()<= 70 then
                  系数.结果系数 = 系数.结果系数 + 取随机数(1,3)/10
              else
                  系数.结果系数 = 系数.结果系数 - 0.3
              end
          end
          if self.参战单位[编号].争锋特性 then
              if self.参战单位[目标].类型~="角色" then
                  系数.结果系数 = 系数.结果系数 + self.参战单位[编号].争锋特性*0.04
              else
                  系数.结果系数 = 系数.结果系数 - 0.1
              end
          end
          if self.参战单位[目标].招架 then
              if self.参战单位[目标].超级招架 and not self.参战单位[目标].已招架 then
                  系数.结果系数 = 系数.结果系数 - 0.8
                  self.参战单位[目标].已招架=1
               else
                  系数.结果系数 = 系数.结果系数 - self.参战单位[目标].招架
               end
          end

------------------------------------------------------削弱
          if self.参战单位[编号].精神 then
              系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[编号].强力眩晕 then
              系数.结果系数 = 系数.结果系数 - 0.5
          end
           if self.参战单位[编号].奇经八脉.陌宝 then
             系数.结果系数 = 系数.结果系数 - 0.15
          end

          if self.参战单位[目标].符石技能.暗渡陈仓 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].符石技能.暗渡陈仓/100
          end
          if self.参战单位[目标].心源 and  self.参战单位[目标].心源>0 then
                系数.结果系数 = 系数.结果系数 - self.参战单位[目标].心源/100
          end
          if self.参战单位[编号].超级隐身 or self.参战单位[目标].超级隐身 then
              系数.结果系数 = 系数.结果系数  - 0.1
          end
          if self.参战单位[编号].强力被动  and self.参战单位[目标].防御被动 then --重写
              系数.结果系数 = 系数.结果系数 - self.参战单位[编号].强力被动
          end
          if self.参战单位[目标].神话词条 and self.参战单位[目标].神话词条.不灭金身 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].神话词条.不灭金身*0.04
          end
          if self.参战单位[目标].战斗赐福~=nil and self.参战单位[目标].战斗赐福.物伤减免>0 then
                系数.结果系数 = 系数.结果系数 -self.参战单位[目标].战斗赐福.物伤减免/100
          end
          if self.参战单位[目标].神器技能~=nil and self.参战单位[目标].神器技能.名称=="骇神"
            and self.参战单位[编号].伤害<self.参战单位[目标].伤害 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].神器技能.等级/10
          end



          if self.参战单位[目标].玉砥柱  and self.参战单位[编号].类型~="角色" and 名称 ~= "高级连击"
              and 名称 ~= "连击" and 名称 ~= "超级连击" and 名称 ~= "普通攻击" then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].玉砥柱
          end

          if self.参战单位[目标].类型~="角色" and self.参战单位[目标].主人 and self.参战单位[self.参战单位[目标].主人].神话词条~=nil
              and self.参战单位[self.参战单位[目标].主人].神话词条.奇经山海 then
                系数.结果系数 = 系数.结果系数 - self.参战单位[self.参战单位[目标].主人].神话词条.奇经山海*0.08
          end
          if 昼夜参数==1 and self.参战单位[编号].夜战~=2 and self.参战单位[编号].夜战~=1 and self.参战单位[编号].门派~="阴曹地府" then
              系数.结果系数 = 系数.结果系数 - 0.2
          end
          系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].格挡值
          if self.参战单位[编号].符石技能.百步穿杨 and 取随机数()<=20 then
              系数.结果伤害 = 系数.结果伤害 + self.参战单位[编号].符石技能.百步穿杨
          end
          if self.参战单位[目标].符石技能.心随我动 and 取随机数()<=20 then
                系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].符石技能.心随我动
          end
          if self.参战单位[编号].撞击 and self.参战单位[编号].撞击>0 then
                系数.结果伤害 = 系数.结果伤害 + 5 * self.参战单位[编号].撞击
          end
          if self.参战单位[编号].舍身击 and self.参战单位[编号].舍身击>0 then
              系数.结果伤害 = 系数.结果伤害 + self.参战单位[编号].舍身击
          end
          if self.参战单位[目标].法术状态.爪印 and self.参战单位[目标].法术状态.爪印.层数 and self.参战单位[目标].法术状态.爪印.编号==编号 then
              系数.结果伤害 = 系数.结果伤害 +  self.参战单位[目标].法术状态.爪印.层数 * self.参战单位[编号].等级
          end

----------------------------------------------------------------------暴击加成
          if self.参战单位[目标].幸运 then
              系数.暴击系数 = 系数.暴击系数 - self.参战单位[目标].幸运 ---重写
          end
          if self.参战单位[编号].奇经八脉.蛮横 then
              系数.暴击系数 = 系数.暴击系数 + 0.03
          end
          if self.参战单位[编号].奇经八脉.破击 then
             系数.暴击系数 = 系数.暴击系数 + 1
          end
          if self.参战单位[编号].奇经八脉.风神 then
             系数.暴击系数 = 系数.暴击系数 + 0.2
          end
          if self.参战单位[编号].法术状态.天眼 then ---奇经八脉
               系数.暴击系数 = 系数.暴击系数 + 0.1
          end
          if self.参战单位[编号].奇经八脉.贪心 and self.参战单位[目标].类型 == "角色" then
             local 加成必杀=false
             if self.参战单位[self.参战单位[目标].召唤兽]==nil then
                加成必杀 = true
             else
                if self.参战单位[self.参战单位[目标].召唤兽].气血<=0 then
                   加成必杀 = true
                end
             end
             if 加成必杀 then
                系数.暴击系数 = 系数.暴击系数 + 0.12
             end
          end
          if self.参战单位[编号].奇经八脉.静岳 and self.参战单位[编号].法术状态.护盾 then
              系数.暴击系数 = 系数.暴击系数 + 0.04
          end
          if self.参战单位[编号].奇经八脉.毒炽 and self.参战单位[目标].法术状态.尸腐毒 then
             系数.暴击系数 = 系数.暴击系数 + 0.12
          end
          if self.参战单位[编号].奇经八脉.癫狂 and self.参战单位[编号].法术状态.天魔解体 then
             系数.暴击系数 = 系数.暴击系数 + 0.12
          end

          if self.参战单位[编号].骤雨~=nil and self.参战单位[编号].骤雨.回合>0 then
              if self.参战单位[编号].奇经八脉.滂沱 then
                  系数.暴击增加 = 系数.暴击增加 + 5 * self.参战单位[编号].骤雨.层数
                  系数.结果系数 = 系数.结果系数 + 0.05 * self.参战单位[编号].骤雨.层数
              else
                  系数.暴击增加 = 系数.暴击增加 + 2 * self.参战单位[编号].骤雨.层数
                  系数.结果系数 = 系数.结果系数 + 0.02 * self.参战单位[编号].骤雨.层数
              end
          end



-------------------------------------------------------------------------------------------暴击计算
          local 暴击 = false
          local 暴击几率 = 1
          local 暴击数额 = self.参战单位[编号].物理暴击等级
          if self.参战单位[编号].神器技能酣战~=nil then
              暴击数额 = 暴击数额 + self.参战单位[编号].神器技能酣战.数额*self.参战单位[编号].神器技能酣战.层数
          end
          if 暴击数额>0 then
               暴击几率 = 暴击几率 + 暴击数额/30
          end
          if self.参战单位[目标].抗物理暴击等级>0 then
              暴击几率 = 暴击几率 - self.参战单位[目标].抗物理暴击等级/30
          end
          暴击几率 = math.floor(暴击几率*系数.暴击系数+ 系数.暴击增加 + (self.参战单位[编号].必杀 or 0))
          if 暴击几率>=98 then
             暴击几率 = 98
          end
          if 暴击几率>=取随机数(1,100) then
              暴击 = true
              if self.参战单位[编号].超级必杀 and 取随机数()<=25 then
                  系数.暴伤系数 = 系数.暴伤系数 + 取随机数(2,3)
              elseif self.参战单位[编号].奇经八脉.破击 then
                    系数.暴伤系数 = 系数.暴伤系数 + 0.65
              else
                   系数.暴伤系数 = 系数.暴伤系数 + 1
              end
              if self.参战单位[编号].狂怒 then
                系数.暴伤增加 =系数.暴伤增加 + self.参战单位[编号].狂怒
              end
              if self.参战单位[编号].凭风借力 then
                  系数.暴伤系数 = 系数.暴伤系数 + 0.125
              end
              if self.参战单位[编号].奇经八脉.杀意 then
                 系数.暴伤系数= 系数.暴伤系数 + 0.2
              end
              if self.参战单位[目标].奇经八脉.灵身 then
                  系数.暴伤系数= 系数.暴伤系数 - 0.4
              end
              if self.参战单位[编号].虎虎生威 then
                  系数.暴伤系数 = 系数.暴伤系数 + self.参战单位[编号].力量/self.参战单位[编号].等级*0.01
              end
              if self.参战单位[编号].神器技能~=nil and self.参战单位[编号].神器技能.名称=="威服天下" then
                  系数.暴伤系数 = 系数.暴伤系数 + self.参战单位[编号].神器技能.等级*0.12
              end
              if self.参战单位[编号].奇经八脉.巧变 and self.参战单位[编号].法术状态.镇魂诀 then
                 self.参战单位[编号].战意 = self.参战单位[编号].战意 + 1
                 self:添加提示(self.参战单位[编号].玩家id,编号,"#Y/你当前可使用的战意为#R/"..self.参战单位[编号].战意.."#Y/点")
              end
              if self.参战单位[编号].奇经八脉.狂暴 and self.参战单位[编号].九黎连击  then
                  self.参战单位[编号].九黎连击 =self.参战单位[编号].九黎连击 +1
                  if self.参战单位[编号].门派=="九黎城" and self.参战单位[编号].神话词条 and self.参战单位[编号].神话词条.五马分尸 and 取随机数()<=self.参战单位[编号].神话词条.五马分尸*8 then
                      self.参战单位[编号].九黎连击 = self.参战单位[编号].九黎连击 + self.参战单位[编号].神话词条.五马分尸
                  end
              end
      end
------------------------------------------------------------------------狂暴计算
      local 狂暴数额 = self.参战单位[编号].狂暴等级
       if self.参战单位[编号].神器技能鸣空~=nil then
          狂暴数额 = 狂暴数额 + self.参战单位[编号].神器技能鸣空.数额*self.参战单位[编号].神器技能鸣空.层数
          系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神器技能鸣空.层数 * 0.02
      end
      if self.参战单位[编号].神器技能 and self.参战单位[编号].神器技能.名称=="蛮血" then
            local 临时数额 = 1+((1-self.参战单位[编号].气血/self.参战单位[编号].最大气血)*self.参战单位[编号].神器技能.等级*0.08)
            狂暴数额 = 狂暴数额 * 临时数额
      end
      if not 暴击 and math.floor(狂暴数额/25)>=取随机数() then
           系数.结果系数 = 系数.结果系数 + 0.5
      end

------------------------------------------------------------------------保护
      local 保护 = false
      local 保护编号 = 0
      if 系数.不可保护 or self.参战单位[目标].法术状态.惊魂掌 or
            (self.参战单位[编号].奇经八脉.难保 and self.参战单位[目标].法术状态.浮空) then
      else
          for k,v in pairs(self.参战单位) do
              if 保护编号 == 0 and self:取行动状态(k) and v.指令.类型=="保护" and v.队伍==self.参战单位[目标].队伍 and v.指令.目标==目标 then
                  保护=true
                  保护编号=k
                  v.指令.类型=""
                  self.执行等待=self.执行等待+5
              end
          end
      end

------------------------------------------------------------------------动作动画

      local 动作 = "挨打"
      local 境界= self:取指定法宝(目标,"金甲仙衣",1)
      if 境界 then
          系数.结果系数 = 系数.结果系数 - 境界 * 0.04
          table.insert(系数.特效, "金甲仙衣")
      end
      if self.参战单位[目标].符石技能.点石成金 and 取随机数(1,100)<=5 then
          系数.结果系数 = 系数.结果系数 - self.参战单位[目标].符石技能.点石成金/100
          动作 = "防御"
          table.insert(系数.特效, "点石成金")
      end
      if self.参战单位[目标].指令.类型=="防御" and self:取行动状态(目标) and self:取可否防御(目标) then
          系数.结果系数 = 系数.结果系数 - 0.5
          动作 = "防御"
          table.insert(系数.特效, "防御")
      end
      if self.参战单位[目标].腾挪劲 and 取随机数(1,100) <= self.参战单位[目标].腾挪劲*4  then
          系数.结果系数 = 系数.结果系数 - 0.5
          table.insert(系数.特效, "腾挪劲")
      end
-------------------------------------------------------------------------------------扣除耐久
      if self.参战单位[编号].类型=="角色" and 玩家数据[self.参战单位[编号].玩家id] then
          玩家数据[self.参战单位[编号].玩家id].角色:耐久处理(self.参战单位[编号].玩家id,1)
      end
      if self.参战单位[目标].类型=="角色" and 玩家数据[self.参战单位[目标].玩家id] then
          玩家数据[self.参战单位[目标].玩家id].角色:耐久处理(self.参战单位[目标].玩家id,2)
      elseif self.参战单位[目标].玩家id~=0 and self.参战单位[目标].类型=="bb" and self.参战单位[目标].分类~="野怪" and 玩家数据[self.参战单位[目标].玩家id] then
              玩家数据[self.参战单位[目标].玩家id].召唤兽:耐久处理(self.参战单位[目标].玩家id,self.参战单位[目标].认证码)
      end
      return {暴击=暴击,动作=动作,保护=保护,编号=保护编号}
end



function 战斗处理类:取物理结束计算(编号,目标,伤害,保护,保护编号,流程,挨打,系数,群体数据)
        if 保护 and 保护编号 and 保护编号~=0 then
             local 保伤=math.floor(伤害*0.7)
             if self.参战单位[编号].奇经八脉.破空 then
                保伤 = math.floor(保伤*1.15)
             end
             if self.参战单位[编号].奇经八脉.破军 and self.参战单位[编号].剑意>=2  then
                   保伤 = math.floor(保伤*1.05)
             end
             if 保伤>0 then
                local 保护死亡=self:减少气血(保护编号,保伤,目标,"保护")
                 伤害 = math.floor(伤害*0.3)
                 if 群体数据 then
                    if not 群体数据.保护数据 then 群体数据.保护数据 = {} end
                    table.insert(群体数据.保护数据, {编号=保护编号,伤害=保伤,死亡=保护死亡})
                 else
                   self.战斗流程[流程].保护数据={编号=保护编号,伤害=保伤,死亡=保护死亡}
                 end
             end
        else
            if (self:取是否反震(编号,目标) and not 系数.不可反震) or 系数.直接反震 then --触发反震 有保护的情况下不会触发反震、反击
                  local 震伤 = math.floor(伤害 * (self.参战单位[目标].反震 or 0))
                  if self.参战单位[目标].法术状态.修罗咒 then
                      震伤 = math.floor(震伤 + 伤害 * 0.5)
                  end
                  if self.参战单位[目标].法术状态.混元伞 then
                      震伤 = math.floor(震伤 + 伤害 * (self.参战单位[目标].法术状态.混元伞.境界*0.03+0.1))
                  end

                  震伤 = 震伤 + (self.参战单位[目标].反震1 or 0)
                  if 震伤 > 0 then
                      if self.参战单位[编号].奇经八脉狂袭~=nil then
                         self.参战单位[编号].奇经八脉狂袭.回合 = 2
                         self.参战单位[编号].奇经八脉狂袭.加成 = self.参战单位[编号].奇经八脉狂袭.加成 + 震伤 * 0.04
                      end
                      if 群体数据 then
                          群体数据.反震伤害 = 群体数据.反震伤害 + 震伤
                          群体数据.反震死亡 = self:减少气血(编号,震伤,目标,"反震")
                      else
                        self.战斗流程[流程].反震伤害 = 震伤
                        self.战斗流程[流程].反震死亡 = self:减少气血(编号,震伤,目标,"反震")
                      end

                      if self.参战单位[目标].法术状态.混元伞 then
                          table.insert(self.战斗流程[流程].挨打方[挨打].特效,"混元伞")
                      else
                          table.insert(self.战斗流程[流程].挨打方[挨打].特效,"反震")
                      end
                      self.执行等待=self.执行等待+3
                  end

            elseif (self:取是否反击(编号,目标) and self:取行动状态(目标) and not 系数.不可反击) or 系数.直接反击 then
                      local 基础 = DeepCopy(self.计算属性)
                      local 计算 = self:取基础物理伤害(目标,编号,"普通攻击",self.参战单位[目标].等级,基础) --{伤害=结果,系数=系数}
                      if self.参战单位[目标].反击 then
                          计算 = 计算 * self.参战单位[目标].反击
                      end
                      local 输出 = self:取物理动作计算(目标,编号,"普通攻击",self.参战单位[目标].等级,基础) ---{暴击=暴击,动作=动作,保护=保护,编号=保护编号}
                      local 结果 = self:取计算结果(目标,编号,计算,nil,流程,1,基础,"普通攻击",self.参战单位[编号].等级,"物伤")
                      if 结果.类型==2 then
                          self:增加气血(编号,结果.伤害)
                          self.战斗流程[流程].气血恢复 = 结果.伤害
                      else
                          if 群体数据 then
                              群体数据.反击伤害 = 群体数据.反击伤害 + 结果.伤害
                              群体数据.反击死亡 = self:减少气血(编号,结果.伤害,目标,"反击")
                          else
                              self.战斗流程[流程].反击伤害 = 结果.伤害
                              self.战斗流程[流程].反击死亡 = self:减少气血(编号,结果.伤害,目标,"反击")
                          end
                      end
            end
        end


        if (self.参战单位[编号].吸血 or self.参战单位[编号].法术状态.移魂化骨) and not self.参战单位[目标].鬼魂 then
            local 吸伤 = math.floor(伤害 * (self.参战单位[编号].吸血 or 0))
            if self.参战单位[编号].法术状态.移魂化骨 then
                吸伤 = math.floor(吸伤 + 伤害 * self.参战单位[编号].法术状态.移魂化骨.等级/580)
            end

            if 吸伤>0 then
                if self.参战单位[编号].超级吸血 then
                    local 溢出=0
                    if self.参战单位[编号].最大气血-self.参战单位[编号].气血<吸伤 then
                        溢出=math.floor(吸伤-(self.参战单位[编号].最大气血-self.参战单位[编号].气血))
                    end
                    if 溢出>0 then
                        吸伤=吸伤-溢出
                        self:添加状态("护盾",编号,编号,溢出)
                        self:处理流程状态(self.战斗流程[流程],"护盾",编号)
                    end
                end
                self:增加气血(编号,吸伤)
                if 群体数据 then
                    群体数据.增加气血 = 群体数据.增加气血 + 吸伤
                else
                    self.战斗流程[流程].增加气血 = 吸伤
                end
            end
        end
        if self.参战单位[编号].奇经八脉.爪印 then
            local 层数 = 1
            if self.参战单位[目标].法术状态.爪印 and self.参战单位[目标].法术状态.层数 then
                  层数 = self.参战单位[目标].法术状态.爪印.层数+1
            end
            self:取消状态("爪印",目标)
            self:添加状态("爪印",目标,编号,层数)
            self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"爪印",目标)
        end
        if self.参战单位[目标].奇经八脉.爪印 then
            local 层数 = 1
            if self.参战单位[编号].法术状态.爪印 and self.参战单位[编号].法术状态.层数 then
                  层数 = self.参战单位[编号].法术状态.爪印.层数+1
            end
            self:取消状态("爪印",编号)
            self:添加状态("爪印",编号,目标,层数)
            self:处理流程状态(self.战斗流程[流程],"爪印",编号)
        end
        if self.参战单位[编号].碎甲刃 and self.参战单位[编号].碎甲刃>0 and 取随机数()<=30 then
            self:添加状态("碎甲刃",目标,编号,self.参战单位[编号].碎甲刃)
            self:处理流程状态(self.战斗流程[流程].挨打方[挨打],"碎甲刃",目标)
        end
        if 伤害>0 and self.参战单位[编号].奇经八脉.突进 then
             self.参战单位[编号].伤害 = self.参战单位[编号].伤害 + 2
        end
        if self.参战单位[编号].神器技能~=nil and self.参战单位[编号].神器技能.名称=="惊锋"  then
            local 伤害加成 = self.参战单位[编号].神器技能.等级*10
            if self.参战单位[编号].神器技能惊锋 == nil then
                self.参战单位[编号].神器技能惊锋={数额=伤害加成,层数=1}
            else
              self.参战单位[编号].神器技能惊锋.层数 = self.参战单位[编号].神器技能惊锋.层数 + 1
              if self.参战单位[编号].神器技能惊锋.层数>=12 then
                  self.参战单位[编号].神器技能惊锋.层数 = 12
              end
            end
        end
        if self.参战单位[编号].神器技能~=nil and self.参战单位[编号].神器技能.名称=="魂魇" then
              local 临时数额 = self.参战单位[编号].神器技能.等级*100
              self.参战单位[目标].神器技能魂魇 = 临时数额
        end

        return math.floor(伤害)
end



