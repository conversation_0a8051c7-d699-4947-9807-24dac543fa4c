{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TrademarkCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/TrademarkCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TrademarkCircleOutlined = function TrademarkCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TrademarkCircleOutlinedSvg\n  }));\n};\n\n/**![trademark-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptODcuNS0zMzQuN2MzNC44LTEyLjggNzguNC00OSA3OC40LTExOS4yIDAtNzEuMi00NS41LTEzMS4xLTE0NC4yLTEzMS4xSDM3OGMtNC40IDAtOCAzLjYtOCA4djQxMGMwIDQuNCAzLjYgOCA4IDhoNTQuNWM0LjQgMCA4LTMuNiA4LThWNTYxLjJoODguN2w3NC42IDE1OS4yYzEuMyAyLjggNC4xIDQuNiA3LjIgNC42aDYyYTcuOSA3LjkgMCAwMDcuMS0xMS41bC04MC42LTE2NC4yek01MjIgNTA1aC04MS41VjM1N2g4My40YzQ4IDAgODAuOSAyNS4zIDgwLjkgNzUuNSAwIDQ2LjktMjkuOCA3Mi41LTgyLjggNzIuNXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TrademarkCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TrademarkCircleOutlined';\n}\nexport default RefIcon;", "map": {"version": 3, "names": ["_extends", "React", "TrademarkCircleOutlinedSvg", "AntdIcon", "TrademarkCircleOutlined", "props", "ref", "createElement", "icon", "RefIcon", "forwardRef", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/antd/node_modules/@ant-design/icons/es/icons/TrademarkCircleOutlined.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// GENERATE BY ./scripts/generate.ts\n// DON NOT EDIT IT MANUALLY\n\nimport * as React from 'react';\nimport TrademarkCircleOutlinedSvg from \"@ant-design/icons-svg/es/asn/TrademarkCircleOutlined\";\nimport AntdIcon from \"../components/AntdIcon\";\nvar TrademarkCircleOutlined = function TrademarkCircleOutlined(props, ref) {\n  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {\n    ref: ref,\n    icon: TrademarkCircleOutlinedSvg\n  }));\n};\n\n/**![trademark-circle](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMiA2NEMyNjQuNiA2NCA2NCAyNjQuNiA2NCA1MTJzMjAwLjYgNDQ4IDQ0OCA0NDggNDQ4LTIwMC42IDQ0OC00NDhTNzU5LjQgNjQgNTEyIDY0em0wIDgyMGMtMjA1LjQgMC0zNzItMTY2LjYtMzcyLTM3MnMxNjYuNi0zNzIgMzcyLTM3MiAzNzIgMTY2LjYgMzcyIDM3Mi0xNjYuNiAzNzItMzcyIDM3MnptODcuNS0zMzQuN2MzNC44LTEyLjggNzguNC00OSA3OC40LTExOS4yIDAtNzEuMi00NS41LTEzMS4xLTE0NC4yLTEzMS4xSDM3OGMtNC40IDAtOCAzLjYtOCA4djQxMGMwIDQuNCAzLjYgOCA4IDhoNTQuNWM0LjQgMCA4LTMuNiA4LThWNTYxLjJoODguN2w3NC42IDE1OS4yYzEuMyAyLjggNC4xIDQuNiA3LjIgNC42aDYyYTcuOSA3LjkgMCAwMDcuMS0xMS41bC04MC42LTE2NC4yek01MjIgNTA1aC04MS41VjM1N2g4My40YzQ4IDAgODAuOSAyNS4zIDgwLjkgNzUuNSAwIDQ2LjktMjkuOCA3Mi41LTgyLjggNzIuNXoiIC8+PC9zdmc+) */\nvar RefIcon = /*#__PURE__*/React.forwardRef(TrademarkCircleOutlined);\nif (process.env.NODE_ENV !== 'production') {\n  RefIcon.displayName = 'TrademarkCircleOutlined';\n}\nexport default RefIcon;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;;AAEA,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,0BAA0B,MAAM,sDAAsD;AAC7F,OAAOC,QAAQ,MAAM,wBAAwB;AAC7C,IAAIC,uBAAuB,GAAG,SAASA,uBAAuBA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACzE,OAAO,aAAaL,KAAK,CAACM,aAAa,CAACJ,QAAQ,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEK,KAAK,EAAE;IACpEC,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEN;EACR,CAAC,CAAC,CAAC;AACL,CAAC;;AAED;AACA,IAAIO,OAAO,GAAG,aAAaR,KAAK,CAACS,UAAU,CAACN,uBAAuB,CAAC;AACpE,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,OAAO,CAACK,WAAW,GAAG,yBAAyB;AACjD;AACA,eAAeL,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}