/**
 * 测试GM工具桥接器
 */

const { GMBridgeClient } = require('./src/native/gm-bridge');

async function testGMBridge() {
    console.log('=== 测试GM工具桥接器 ===');
    
    // 检查GM工具是否可用
    if (!GMBridgeClient.isAvailable()) {
        console.error('❌ GM工具不可用');
        return;
    }
    
    console.log('✅ GM工具可用');
    
    // 创建桥接客户端
    const client = new GMBridgeClient();
    
    try {
        console.log('\n--- 发送登录数据 ---');
        
        const result = await client.sendLoginData('888888', '888888');
        
        if (result.success) {
            console.log('✅ 登录数据发送成功');
            console.log('输出:', result.output);
            
            if (result.error) {
                console.log('错误输出:', result.error);
            }
        } else {
            console.error('❌ 登录数据发送失败');
        }
        
    } catch (error) {
        console.error('❌ 测试过程中发生错误:', error.message);
    }
    
    console.log('\n✅ 测试完成');
}

// 运行测试
testGMBridge().catch(console.error);
