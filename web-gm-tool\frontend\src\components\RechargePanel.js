import React, { useState } from 'react';
import { Card, Form, Input, InputNumber, Select, Button, Row, Col, Typography, Space, Divider, Alert } from 'antd';
import { DollarOutlined, UserOutlined, SendOutlined } from '@ant-design/icons';
import { useSocket } from '../contexts/SocketContext';

const { Title, Text } = Typography;
const { Option } = Select;

const RechargePanel = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const socket = useSocket();

  // 充值类型配置
  const rechargeTypes = [
    { value: 101, label: '充值仙玉', icon: '💎', description: '游戏内主要货币' },
    { value: 102, label: '充值点卡', icon: '🎫', description: '点卡充值' },
    { value: 103, label: '充值银子', icon: '🪙', description: '游戏内银两' },
    { value: 104, label: '充值储备', icon: '📦', description: '储备资源' },
    { value: 105, label: '充值经验', icon: '⭐', description: '角色经验值' },
    { value: 106, label: '充值累充', icon: '🎁', description: '累计充值记录' },
    { value: 107, label: '充值帮贡', icon: '🏛️', description: '帮派贡献度' },
    { value: 108, label: '充值门贡', icon: '🏫', description: '门派贡献度' },
    { value: 113, label: '活跃积分', icon: '🎯', description: '活跃度积分' },
    { value: 114, label: '比武积分', icon: '⚔️', description: 'PVP积分' },
  ];

  // 快捷金额
  const quickAmounts = [100, 500, 1000, 5000, 10000, 50000];

  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      socket.emit('recharge', {
        type: values.type,
        account: values.account,
        amount: values.amount
      });
    } catch (error) {
      console.error('充值请求失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickAmount = (amount) => {
    form.setFieldsValue({ amount });
  };

  const selectedType = form.getFieldValue('type');
  const selectedTypeInfo = rechargeTypes.find(type => type.value === selectedType);

  return (
    <div>
      <Title level={3} style={{ marginBottom: 24 }}>
        <DollarOutlined /> 充值操作
      </Title>

      <Alert
        message="充值说明"
        description="请谨慎操作充值功能，确认账号和金额无误后再提交。充值操作不可撤销。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        <Col xs={24} lg={16}>
          <Card title="充值操作" bordered={false}>
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
              initialValues={{
                type: 101,
                amount: 1000
              }}
            >
              <Row gutter={16}>
                <Col xs={24} sm={12}>
                  <Form.Item
                    name="account"
                    label="目标账号"
                    rules={[
                      { required: true, message: '请输入目标账号' },
                      { min: 3, message: '账号长度至少3位' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="请输入要充值的账号"
                      size="large"
                    />
                  </Form.Item>
                </Col>

                <Col xs={24} sm={12}>
                  <Form.Item
                    name="type"
                    label="充值类型"
                    rules={[{ required: true, message: '请选择充值类型' }]}
                  >
                    <Select size="large" placeholder="选择充值类型">
                      {rechargeTypes.map(type => (
                        <Option key={type.value} value={type.value}>
                          <Space>
                            <span>{type.icon}</span>
                            <span>{type.label}</span>
                          </Space>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="amount"
                label="充值数量"
                rules={[
                  { required: true, message: '请输入充值数量' },
                  { type: 'number', min: 1, message: '充值数量必须大于0' }
                ]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  size="large"
                  placeholder="请输入充值数量"
                  formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
                  parser={value => value.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>

              {/* 快捷金额按钮 */}
              <Form.Item label="快捷金额">
                <Space wrap>
                  {quickAmounts.map(amount => (
                    <Button
                      key={amount}
                      size="small"
                      onClick={() => handleQuickAmount(amount)}
                    >
                      {amount.toLocaleString()}
                    </Button>
                  ))}
                </Space>
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  size="large"
                  icon={<SendOutlined />}
                  block
                >
                  {loading ? '处理中...' : '确认充值'}
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          {/* 充值类型说明 */}
          <Card title="充值类型说明" size="small">
            {selectedTypeInfo && (
              <div style={{ marginBottom: 16, padding: 12, background: '#f6ffed', borderRadius: 6 }}>
                <Space>
                  <span style={{ fontSize: '20px' }}>{selectedTypeInfo.icon}</span>
                  <div>
                    <Text strong>{selectedTypeInfo.label}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {selectedTypeInfo.description}
                    </Text>
                  </div>
                </Space>
              </div>
            )}

            <Divider style={{ margin: '12px 0' }} />

            <Space direction="vertical" size="small" style={{ width: '100%' }}>
              {rechargeTypes.map(type => (
                <div key={type.value} style={{ 
                  padding: '8px', 
                  background: selectedType === type.value ? '#e6f7ff' : '#fafafa',
                  borderRadius: '4px',
                  cursor: 'pointer'
                }} onClick={() => form.setFieldsValue({ type: type.value })}>
                  <Space size="small">
                    <span>{type.icon}</span>
                    <Text style={{ fontSize: '12px' }}>{type.label}</Text>
                  </Space>
                </div>
              ))}
            </Space>
          </Card>

          {/* 操作记录 */}
          <Card title="最近操作" size="small" style={{ marginTop: 16 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              暂无操作记录
            </Text>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default RechargePanel;
