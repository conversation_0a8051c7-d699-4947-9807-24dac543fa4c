{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Size info need loop query for the `heights` which will has the perf issue.\n * Let cache result for each render phase.\n */\nexport function useGetSize(mergedData, getKey, heights, itemHeight) {\n  var _React$useMemo = React.useMemo(function () {\n      return [new Map(), []];\n    }, [mergedData, heights.id, itemHeight]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    key2Index = _React$useMemo2[0],\n    bottomList = _React$useMemo2[1];\n  var getSize = function getSize(startKey) {\n    var endKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : startKey;\n    // Get from cache first\n    var startIndex = key2Index.get(startKey);\n    var endIndex = key2Index.get(endKey);\n\n    // Loop to fill the cache\n    if (startIndex === undefined || endIndex === undefined) {\n      var dataLen = mergedData.length;\n      for (var i = bottomList.length; i < dataLen; i += 1) {\n        var _heights$get;\n        var item = mergedData[i];\n        var key = getKey(item);\n        key2Index.set(key, i);\n        var cacheHeight = (_heights$get = heights.get(key)) !== null && _heights$get !== void 0 ? _heights$get : itemHeight;\n        bottomList[i] = (bottomList[i - 1] || 0) + cacheHeight;\n        if (key === startKey) {\n          startIndex = i;\n        }\n        if (key === endKey) {\n          endIndex = i;\n        }\n        if (startIndex !== undefined && endIndex !== undefined) {\n          break;\n        }\n      }\n    }\n    return {\n      top: bottomList[startIndex - 1] || 0,\n      bottom: bottomList[endIndex]\n    };\n  };\n  return getSize;\n}", "map": {"version": 3, "names": ["_slicedToArray", "React", "useGetSize", "mergedData", "<PERSON><PERSON><PERSON>", "heights", "itemHeight", "_React$useMemo", "useMemo", "Map", "id", "_React$useMemo2", "key2Index", "bottomList", "getSize", "startKey", "<PERSON><PERSON><PERSON>", "arguments", "length", "undefined", "startIndex", "get", "endIndex", "dataLen", "i", "_heights$get", "item", "key", "set", "cacheHeight", "top", "bottom"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-virtual-list/es/hooks/useGetSize.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Size info need loop query for the `heights` which will has the perf issue.\n * Let cache result for each render phase.\n */\nexport function useGetSize(mergedData, getKey, heights, itemHeight) {\n  var _React$useMemo = React.useMemo(function () {\n      return [new Map(), []];\n    }, [mergedData, heights.id, itemHeight]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    key2Index = _React$useMemo2[0],\n    bottomList = _React$useMemo2[1];\n  var getSize = function getSize(startKey) {\n    var endKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : startKey;\n    // Get from cache first\n    var startIndex = key2Index.get(startKey);\n    var endIndex = key2Index.get(endKey);\n\n    // Loop to fill the cache\n    if (startIndex === undefined || endIndex === undefined) {\n      var dataLen = mergedData.length;\n      for (var i = bottomList.length; i < dataLen; i += 1) {\n        var _heights$get;\n        var item = mergedData[i];\n        var key = getKey(item);\n        key2Index.set(key, i);\n        var cacheHeight = (_heights$get = heights.get(key)) !== null && _heights$get !== void 0 ? _heights$get : itemHeight;\n        bottomList[i] = (bottomList[i - 1] || 0) + cacheHeight;\n        if (key === startKey) {\n          startIndex = i;\n        }\n        if (key === endKey) {\n          endIndex = i;\n        }\n        if (startIndex !== undefined && endIndex !== undefined) {\n          break;\n        }\n      }\n    }\n    return {\n      top: bottomList[startIndex - 1] || 0,\n      bottom: bottomList[endIndex]\n    };\n  };\n  return getSize;\n}"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AACA;AACA;AACA;AACA,OAAO,SAASC,UAAUA,CAACC,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAE;EAClE,IAAIC,cAAc,GAAGN,KAAK,CAACO,OAAO,CAAC,YAAY;MAC3C,OAAO,CAAC,IAAIC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC;IACxB,CAAC,EAAE,CAACN,UAAU,EAAEE,OAAO,CAACK,EAAE,EAAEJ,UAAU,CAAC,CAAC;IACxCK,eAAe,GAAGX,cAAc,CAACO,cAAc,EAAE,CAAC,CAAC;IACnDK,SAAS,GAAGD,eAAe,CAAC,CAAC,CAAC;IAC9BE,UAAU,GAAGF,eAAe,CAAC,CAAC,CAAC;EACjC,IAAIG,OAAO,GAAG,SAASA,OAAOA,CAACC,QAAQ,EAAE;IACvC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAGF,QAAQ;IACzF;IACA,IAAIK,UAAU,GAAGR,SAAS,CAACS,GAAG,CAACN,QAAQ,CAAC;IACxC,IAAIO,QAAQ,GAAGV,SAAS,CAACS,GAAG,CAACL,MAAM,CAAC;;IAEpC;IACA,IAAII,UAAU,KAAKD,SAAS,IAAIG,QAAQ,KAAKH,SAAS,EAAE;MACtD,IAAII,OAAO,GAAGpB,UAAU,CAACe,MAAM;MAC/B,KAAK,IAAIM,CAAC,GAAGX,UAAU,CAACK,MAAM,EAAEM,CAAC,GAAGD,OAAO,EAAEC,CAAC,IAAI,CAAC,EAAE;QACnD,IAAIC,YAAY;QAChB,IAAIC,IAAI,GAAGvB,UAAU,CAACqB,CAAC,CAAC;QACxB,IAAIG,GAAG,GAAGvB,MAAM,CAACsB,IAAI,CAAC;QACtBd,SAAS,CAACgB,GAAG,CAACD,GAAG,EAAEH,CAAC,CAAC;QACrB,IAAIK,WAAW,GAAG,CAACJ,YAAY,GAAGpB,OAAO,CAACgB,GAAG,CAACM,GAAG,CAAC,MAAM,IAAI,IAAIF,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAGnB,UAAU;QACnHO,UAAU,CAACW,CAAC,CAAC,GAAG,CAACX,UAAU,CAACW,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAIK,WAAW;QACtD,IAAIF,GAAG,KAAKZ,QAAQ,EAAE;UACpBK,UAAU,GAAGI,CAAC;QAChB;QACA,IAAIG,GAAG,KAAKX,MAAM,EAAE;UAClBM,QAAQ,GAAGE,CAAC;QACd;QACA,IAAIJ,UAAU,KAAKD,SAAS,IAAIG,QAAQ,KAAKH,SAAS,EAAE;UACtD;QACF;MACF;IACF;IACA,OAAO;MACLW,GAAG,EAAEjB,UAAU,CAACO,UAAU,GAAG,CAAC,CAAC,IAAI,CAAC;MACpCW,MAAM,EAAElB,UAAU,CAACS,QAAQ;IAC7B,CAAC;EACH,CAAC;EACD,OAAOR,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}