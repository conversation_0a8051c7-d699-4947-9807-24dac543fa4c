
function 战斗处理类:取基础法伤伤害(编号,目标,名称,等级,系数)
          --系数= {初始系数=1,叠加系数=0,初始伤害=0,防御系数=1,忽视防御=0, ---初始计算
          --       暴击系数=1,暴击增加=0,暴伤系数=1,暴伤增加=0,            ---暴击计算
          --       结果系数=1,结果伤害=0,特效={}}                         ---最终计算
          if not 系数.特效 then 系数.特效={} end
          系数.防御系数 = 系数.防御系数 + self:取抗法特性(self.参战单位[目标].队伍)
          if self.参战单位[目标].奇经八脉.普照 then
              for k,v in pairs(self.参战单位) do
                 if v.队伍~=self.参战单位[目标].队伍 and v.法术状态.普渡众生 then
                    系数.忽视防御 = 系数.忽视防御 - 15
                 end
              end
          end
          if self.参战单位[目标].奇经八脉.庄严 then
              for k,v in pairs(self.参战单位) do
                 if v.队伍~=self.参战单位[目标].队伍 and v.法术状态.紧箍咒 then
                    系数.忽视防御 = 系数.忽视防御 - 15
                 end
              end
          end
          if self.参战单位[编号].通灵法 then
              系数.防御系数 = 系数.防御系数-0.01* self.参战单位[编号].通灵法
          end
          if self.参战单位[编号].弑神特性 and self.参战单位[目标].神佑效果 then
            系数.初始伤害 = 系数.初始伤害 + (self.参战单位[编号].弑神特性*3+3)*10
          end
          if self.参战单位[编号].符石技能.隔山打牛 and 取随机数()<= 20 then
              系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].符石技能.隔山打牛
          end
          if self.参战单位[编号].神器技能 and self.参战单位[编号].神器技能.名称=="沧浪赋" and self.参战单位[目标].气血<=self.参战单位[目标].最大气血*0.3 then
              系数.初始伤害 = 系数.初始伤害 + self.参战单位[编号].神器技能.等级*120
          end
          if self.参战单位[目标].奇经八脉.回旋 then
             系数.初始伤害 = 系数.初始伤害 - 65
          end

          if self.参战单位[编号].顺势特性~=nil  then
              if self.参战单位[目标].气血<=self.参战单位[目标].最大气血*0.7 then
                 系数.初始伤害 = 系数.初始伤害 +  (self.参战单位[编号].顺势特性*3+3)*10
              else
                 系数.初始伤害 = 系数.初始伤害 - 90
              end
          end
          -----------------开始计算
          local 防御 = self.参战单位[目标].法防
          local 伤害 = self.参战单位[编号].法伤
          防御 = math.floor(防御 * 系数.防御系数 - 系数.忽视防御)
          伤害 = math.floor(伤害 * 系数.初始系数 + 系数.初始伤害 + self.参战单位[编号].穿刺等级)
          local 结果 = 0
          if 伤害 >防御  then
              local 比例加成 = 100-math.floor(防御/伤害*100)
              if 比例加成<=50 then
                  伤害= math.floor(伤害 *1.03)
              elseif 比例加成<=40 then
                    伤害= math.floor(伤害 *1.06)
              elseif 比例加成<=30 then
                    伤害= math.floor(伤害 *1.09)
              elseif 比例加成<=20 then
                    伤害= math.floor(伤害 *1.12)
              elseif 比例加成<=10 then
                    伤害= math.floor(伤害 *1.15)
              end
              结果 = 伤害 - 防御
          else
              结果=math.floor(伤害*0.05)
          end
          结果 = 结果*(1+self.参战单位[编号].法术修炼*0.02-self.参战单位[目标].抗法修炼*0.02)+(self.参战单位[编号].法术修炼*5)-(self.参战单位[目标].抗法修炼*5)
          结果 = math.floor(结果*取随机数(95,105)/100)
          -----------------计算介绍
          return 结果
end









function 战斗处理类:取法伤动作计算(编号,目标,名称,等级,系数)
          --系数= {暴击系数=1,暴击增加=0,暴伤系数=1,暴伤增加=0,            ---暴击计算
          --       结果系数=1,结果伤害=0,特效={}}                          ---最终计算 --其他动作
----------------------------------------------------------------------其他加成
          if not 系数.特效 then 系数.特效={} end
          if self.参战单位[目标].钢化 then
              系数.结果系数 = 系数.结果系数 + 0.1
          end

          if self.参战单位[编号].奇经八脉破浪 then
             系数.结果系数 = 系数.结果系数 + 0.06
          end
          if self.参战单位[目标].奇经八脉龙啸 then
             系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].奇经八脉蚀天 then
             系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].奇经八脉.奇门 then
             系数.结果系数 = 系数.结果系数 + 0.03
          end
          if self.参战单位[编号].奇经八脉.霜雷 then
             系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[编号].奇经八脉.天劫 then
             系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[编号].奇经八脉.崩摧 then
             系数.结果系数 = 系数.结果系数 + 0.05
          end
          if self.参战单位[编号].奇经八脉.神木  then
             if self:取装备五行(编号,3)=="木" then
                系数.结果系数 = 系数.结果系数 + 0.04
             end
             if self:取装备五行(编号,4)=="木" then
                系数.结果系数 = 系数.结果系数 + 0.04
             end
          end

          if self.参战单位[目标].法术状态.雷浪穿云 then
             系数.结果系数 = 系数.结果系数 + 0.3
          end

          if self.参战单位[编号].法术状态.龙魂 then
            local 加成 = 0.03
             if self.参战单位[编号].奇经八脉.叱咤 then
                加成=加成+0.06
             end
             if self.参战单位[编号].奇经八脉盘龙 then
                加成=加成 * (1+0.02*self.参战单位[编号].奇经八脉盘龙)
             end
             系数.结果系数 = 系数.结果系数 + 加成
          end
          if self.参战单位[编号].魔之心~=nil then
            系数.结果系数 = 系数.结果系数 + self.参战单位[编号].魔之心
          end

          if self.参战单位[目标].鬼魂 and self.参战单位[编号].驱鬼 then --重写
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].驱鬼
          end
          if self.参战单位[编号].奇经八脉.顺势 then
             local 加成必杀=false
                if self.参战单位[self.参战单位[目标].召唤兽]==nil then
                   加成必杀 = true
                else
                  if self.参战单位[self.参战单位[目标].召唤兽].气血<=0 then
                    加成必杀 = true
                  end
                end
                if 加成必杀 then
                  系数.结果系数 = 系数.结果系数 + 0.1
                end
          end
          if self.参战单位[编号].灵宝乾坤火卷 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].灵宝乾坤火卷
          end
          if self.参战单位[目标].灵宝天雷音鼓 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[目标].灵宝天雷音鼓
          end


          if self.参战单位[目标].抗物特性 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[目标].抗物特性*3*0.01
          end

          if self.参战单位[目标].法术状态.九霄龙锥 then
              系数.结果系数 = 系数.结果系数+self.参战单位[目标].法术状态.九霄龙锥.境界
          end
          if self.参战单位[编号].潜龙在渊加成 then
              系数.结果系数 = 系数.结果系数 +self.参战单位[编号].潜龙在渊加成.龙魂*0.01
          end
          if self.参战单位[编号].奇经八脉.坐禅  then
             local 增益数量 = 1 + self:取增益数量(编号)/100
             系数.结果系数 = 系数.结果系数 + self:取增益数量(编号)*0.01
          end
          if self.参战单位[编号].奇经八脉.造化 and 取随机数()<=30 then
             系数.结果系数 = 系数.结果系数 + 0.2
          end
          if self.参战单位[编号].奇经八脉.囚笼 and self:取封印状态(目标) then
             系数.结果系数 = 系数.结果系数 + 0.05
          end
          local 境界 = self:取指定法宝(编号,"慈悲",1)
          if self.参战单位[编号].奇经八脉.悲悯 and 境界 and 境界>0 then
              系数.结果系数 = 系数.结果系数 + 境界/1.5 * 0.01
          end

          if self.参战单位[目标].奇经八脉清吟 and self.参战单位[目标].奇经八脉清吟<=3 then
             系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[编号].战斗赐福 and self.参战单位[编号].战斗赐福.法伤结果>0 then
             系数.结果系数 = 系数.结果系数 + self.参战单位[编号].战斗赐福.法伤结果/100
          end
          if self.参战单位[编号].法术状态.神焰 then   ------------未完成
              local 加成 = 0.4
              if self.参战单位[编号].奇经八脉升温 then
                 加成 = 加成*(1+0.06*self.参战单位[编号].奇经八脉升温)

              end
              系数.结果系数 = 系数.结果系数 + 加成
              if self.参战单位[编号].奇经八脉.威吓 and self.参战单位[目标].类型~="角色" then
                 系数.结果系数 = 系数.结果系数 + 0.42
              end
          end
          if self.参战单位[编号].奇经八脉.批亢 and self.参战单位[编号].法术状态.分身术 then
              系数.结果系数 = 系数.结果系数 + 0.2
          end
          if  self.参战单位[编号].门派=="方寸山" and self:取指定法宝(编号,"救命毫毛",1) then
                local 境界 = self:取指定法宝(编号,"救命毫毛")
                if self.参战单位[编号].奇经八脉.宝诀 then
                   系数.结果系数 = 系数.结果系数 + 境界 * 0.5 * 0.01
                end
                if self.参战单位[编号].奇经八脉.妙用 then
                   系数.结果系数 = 系数.结果系数 + 境界 * 0.5 * 0.01
                end

                if self.参战单位[编号].奇经八脉.不灭 then
                    系数.结果系数 = 系数.结果系数 + 境界*0.5*0.01
                end
          end
          if self.参战单位[编号].法术状态.蜜润 and self.参战单位[编号].法术状态.蜜润.滋养 then
             系数.结果系数 = 系数.结果系数 + 0.15
          end
          if self.参战单位[目标].法术状态.凋零之歌 then
             系数.结果系数 = 系数.结果系数+0.16+0.02*self.参战单位[目标].法术状态.凋零之歌.等级
          end
          if self.参战单位[编号].法术状态.灵动九天 and self.参战单位[编号].法术状态.灵动九天.法华 then
             系数.结果系数 = 系数.结果系数 + 0.06
          end
          if self.参战单位[编号].神话词条 then
              if self.参战单位[编号].门派=="方寸山" and self.参战单位[编号].神话词条.雷法天尊 then
                    系数.结果系数 = 系数.结果系数 +self.参战单位[编号].神话词条.雷法天尊*0.08
              elseif self.参战单位[编号].门派=="魔王寨" and self.参战单位[编号].神话词条.五火神焰 then
                  系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.五火神焰*0.1
              end
              if self.参战单位[编号].神话词条.太初神念 then
                  系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神话词条.太初神念*0.04
              end
          end
          if self.参战单位[编号].法波 and self.参战单位[编号].法术波动 then
              local 法波系数 = (self.参战单位[编号].法术波动-100)/20
              if 取随机数()<=10 then
                  系数.结果系数 = 系数.结果系数 + 取随机数(100+法波系数*10,self.参战单位[编号].法术波动) * 0.01
              elseif 取随机数()<=40 then
                  系数.结果系数 = 系数.结果系数 + 取随机数(100,100+法波系数*10) * 0.01
              else
                  系数.结果系数 = 系数.结果系数 - (1- 取随机数(self.参战单位[编号].法波,100) * 0.01)
              end
          end


          if self.参战单位[编号].神器技能 and self.参战单位[编号].神器技能.名称=="业焰明光" and 取随机数()<=25 then
              系数.结果系数 = 系数.结果系数 + self.参战单位[编号].神器技能.等级 * 0.25
          end
          if self.参战单位[编号].类型~="角色" and self.参战单位[编号].主人 and self.参战单位[self.参战单位[编号].主人].神话词条
            and self.参战单位[self.参战单位[编号].主人].神话词条.兽语御灵 then
              系数.结果系数 = 系数.结果系数 +self.参战单位[self.参战单位[编号].主人].神话词条.兽语御灵*0.08
          end

          if self.参战单位[编号].门派 == "龙宫" and  self.参战单位[编号].类型=="角色" and self:取指定法宝(编号,"伏魔天书",1) then
              local 加成 = self:取指定法宝(编号,"伏魔天书") * 0.025
              if self.参战单位[编号].奇经八脉.伏魔 then
                 加成 = 加成 * 1.2
              end
              系数.结果系数 = 系数.结果系数 + 加成
          end

          if self.参战单位[编号].门派 == "龙宫" and  self.参战单位[编号].类型=="角色" and self:取指定法宝(编号,"镇海珠",1) then
              local 加成 = self:取指定法宝(编号,"镇海珠")* 0.025
              if self.参战单位[编号].奇经八脉.龙珠 then
                 加成 = 加成 * 1.1
              end
              系数.结果系数 = 系数.结果系数 + 加成
          end

          if self.参战单位[编号].奇经八脉.炼魂 and self.参战单位[目标].类型=="角色"   and self:取是否单独门派(编号) and not self.参战单位[目标].炼魂虚弱 then
             系数.结果系数 = 系数.结果系数 + 0.05
             for i=1,#self.参战单位 do
                if self.参战单位[i].类型=="角色" and self.参战单位[i].队伍~=self.参战单位[编号].队伍 and  self.参战单位[i].炼魂虚弱 then
                    系数.结果系数 = 系数.结果系数 +  0.05
                end
             end
          end
          if self.参战单位[编号].奇经八脉.回灵 and self.参战单位[编号].法术状态.龙魂 and (self.参战单位[编号].法术状态.龙骇龙腾 or self.参战单位[编号].法术状态.龙骇龙卷) then
            系数.结果系数 = 系数.结果系数 + 0.16
          end
------------------------------------------------------削弱
          if self.参战单位[目标].超级抵抗 then
               系数.结果系数 = 系数.结果系数 - 0.5
          end
          if self.参战单位[编号].奇经特效.返璞 then
              系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[编号].奇经八脉.震慑 then ---介绍-10%
             系数.结果系数 = 系数.结果系数 - 0.1
          end
          if self.参战单位[目标].奇经八脉破浪 then
             系数.结果系数 = 系数.结果系数 - 0.09
          end
          if self.参战单位[目标].奇经八脉.守中 then ----介绍-6%
              系数.结果系数 = 系数.结果系数 - 0.06
          end
          local 境界 = self:取指定法宝(目标,"降魔斗篷",1)
          if 境界 then
              系数.结果系数 = 系数.结果系数 - 境界 * 0.04
              table.insert(系数.特效, "降魔斗篷")
          end
          if self.参战单位[目标].法术状态.颠倒五行 then
             local  基础几率=50
             if self.参战单位[目标].法术状态.颠倒五行.五行制化 then
                  基础几率 = 基础几率 + 15
             end
             if self.参战单位[目标].法术状态.颠倒五行.万象 then
                  基础几率 = 基础几率 + 15
             end
             if 取随机数()<= 基础几率 then
                 系数.结果系数 = 系数.结果系数 - 0.3
             end
          end
          if self.参战单位[目标].信仰  and self.参战单位[目标].信仰==2 then
             系数.结果系数 = 系数.结果系数 - 0.2
          end
          if self.参战单位[目标].符石技能.化敌为友 then
            系数.结果系数 = 系数.结果系数-self.参战单位[目标].符石技能.化敌为友/100
          end

          if self.参战单位[目标].通真达灵 and  self.参战单位[目标].通真达灵>0 then
                系数.结果系数 = 系数.结果系数 - self.参战单位[目标].通真达灵/100
          end
          if self.参战单位[目标].神话词条 and self.参战单位[目标].神话词条.法天象地 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].神话词条.法天象地*0.04
          end
          if self.参战单位[目标].战斗赐福 and self.参战单位[目标].战斗赐福.法伤减免>0 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[目标].战斗赐福.法伤减免/100
          end
          if self.参战单位[编号].奇经特效.药灵 then
              if self.参战单位[编号].奇经八脉.木魂 and self.参战单位[目标].类型~="角色" then
              else
                  系数.结果系数 = 系数.结果系数 - 0.5
              end
          end

          if self.参战单位[目标].法术状态.安神诀 and self.参战单位[目标].法术状态.安神诀.奇经八脉安神 then
              系数.结果系数 = 系数.结果系数 - 0.5
          end


          if self.参战单位[目标].类型~="角色" and self.参战单位[目标].主人 and self.参战单位[self.参战单位[目标].主人].神话词条
            and self.参战单位[self.参战单位[目标].主人].神话词条.奇经山海 then
              系数.结果系数 = 系数.结果系数 - self.参战单位[self.参战单位[目标].主人].神话词条.奇经山海*0.08
          end

          if self.参战单位[编号].奇经八脉.龙慑  then
             if self:取装备五行(编号,3)=="水" then
                系数.结果伤害 = 系数.结果伤害 + 120
             end
             if self:取装备五行(编号,4)=="水" then
                系数.结果伤害 = 系数.结果伤害 + 120
             end
          end
          if self.参战单位[编号].神器技能凭虚御风 then
             系数.结果伤害 = 系数.结果伤害 + self.参战单位[编号].神器技能凭虚御风.数额 * self.参战单位[编号].神器技能凭虚御风.层数
          end


          系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].法术伤害结果
          if self.参战单位[目标].奇经八脉天照~=nil then
             系数.结果伤害 = 系数.结果伤害 - 40
          end
          if self.参战单位[编号].神器技能魂魇 then
             系数.结果伤害 = 系数.结果伤害 - self.参战单位[编号].神器技能魂魇
          end
          if self.参战单位[目标].符石技能.云随风舞 and 取随机数()<= 20 then
              系数.结果伤害 = 系数.结果伤害 - self.参战单位[目标].符石技能.云随风舞
          end
----------------------------------------------------------------------暴击加成
          if self.参战单位[编号].奇经八脉.神躯 then
             系数.暴击系数 = 系数.暴击系数 - 0.2
          end
          if self.参战单位[编号].奇经八脉.焚尽 then
             if self:取装备五行(编号,3)=="火" then
                系数.暴击系数 = 系数.暴击系数 + 0.04
             end
             if self:取装备五行(编号,4)=="火" then
                系数.暴击系数 = 系数.暴击系数 + 0.04
             end
          end
          if self.参战单位[编号].奇经八脉.风神 then
             系数.暴击系数 = 系数.暴击系数 + 0.2
          end
          if self.参战单位[目标].幸运 then
              系数.暴击系数 = 系数.暴击系数 - self.参战单位[目标].幸运 ---重写
          end
          if self.参战单位[编号].奇经八脉.法身 and self.参战单位[编号].风灵>0 then
             系数.暴击系数 = 系数.暴击系数 + 0.02*self.参战单位[编号].风灵
          end
          if self.参战单位[编号].奇经八脉龙息 then
             系数.暴击系数 = 系数.暴击系数 + 0.002 * self.参战单位[编号].奇经八脉龙息
          end

          if self.参战单位[编号].奇经八脉.静岳 and self.参战单位[编号].法术状态.护盾 then
              系数.暴击系数 = 系数.暴击系数 + 0.04
          end
          if self.参战单位[编号].奇经八脉.呼风 then
               local 百分比 = self.参战单位[目标].魔法/self.参战单位[目标].最大魔法*100
               if 百分比<30  then
                  系数.暴击增加 = 系数.暴击增加 + 6
               end
          end
          if self.参战单位[编号].奇经八脉.毒炽 and self.参战单位[目标].法术状态.尸腐毒 then
             系数.暴击系数 = 系数.暴击系数 + 0.12
          end
-------------------------------------------------------------------------------------------暴击计算
          local 暴击 = false
          local 暴击几率 = 1
          local 暴击数额 = self.参战单位[编号].法术暴击等级
          if 暴击数额>0 then
               暴击几率 = 暴击几率 + 暴击数额/30
          end
          if self.参战单位[目标].抗法术暴击等级>0 then
              暴击几率 = 暴击几率 - self.参战单位[目标].抗法术暴击等级/30
          end
          暴击几率 = math.floor(暴击几率*系数.暴击系数 + 系数.暴击增加 + (self.参战单位[编号].法暴 or 0))
          if 暴击几率>=98 then
             暴击几率 = 98
          end
          if 暴击几率>=取随机数(1,100) then
              暴击 = true
              if self.参战单位[编号].超级法暴 and 取随机数()<=15 then
                  系数.暴伤系数 = 系数.暴伤系数 + 取随机数(2,3)
              else
                   系数.暴伤系数 = 系数.暴伤系数 + 1
              end
              if self.参战单位[编号].灵身 then
                  系数.暴伤系数 = 系数.暴伤系数 + 0.07 * self.参战单位[编号].灵身
              end
              if self.参战单位[目标].奇经八脉.灵身  then
                 系数.暴伤系数 = 系数.暴伤系数 - 0.4
              end
              if self.参战单位[编号].奇经八脉.火神 and self:取指定法宝(编号,"五火神焰印",1) then
                 系数.暴伤系数 = 系数.暴伤系数 + 0.14
              end

              if self.参战单位[目标].神器技能 and self.参战单位[目标].神器技能.名称=="定风波" then
                  系数.暴伤系数 = 系数.暴伤系数 - self.参战单位[目标].神器技能.等级*0.3
              end
          end
------------------------------------------------------------------------动作动画
          local 减免 = false
          if ((self.参战单位[目标].法术状态.罗汉金钟 or self.参战单位[目标].法术状态.太极护法
              or self.参战单位[目标].法术状态.法术防御) and not 减免 and not 系数.不可减免) or 系数.直接减免 then
                   减免 = true
                  local 减免系数 = 0.5
                  if 系数.减免系数 then
                      减免系数 = 减免系数 * 系数.减免系数
                  elseif 系数.减免增加 then
                      系数.结果伤害 = 系数.结果伤害 + 系数.减免增加
                      减免 = false
                  end
                  if self.参战单位[编号].奇经八脉.汹涌 then
                      减免系数 = 减免系数 * 0.92
                  end
                  if 减免 then
                      系数.结果系数 = 系数.结果系数 - 减免系数
                  end
          end
          if self.参战单位[编号].奇经八脉.云霄 then
                self:减少魔法(目标,50)
          end
          if self.参战单位[编号].奇经八脉.摧心 then
                  self.参战单位[目标].奇经八脉摧心前置 = 1
          end
          if self.参战单位[编号].奇经八脉.炼魂 and self.参战单位[目标].类型=="角色" and self:取是否单独门派(编号)  then
                 self.参战单位[目标].奇经八脉炼魂 = 1
          end
-------------------------------------------------------------------------------------扣除耐久
          return {暴击=暴击,减免=减免}
end




-- function 战斗处理类:取法伤结束计算(编号,目标,伤害,流程,挨打,系数)

--         return math.floor(伤害)
-- end


