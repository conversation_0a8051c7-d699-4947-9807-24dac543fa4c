/**
 * GM工具协议加密解密模块
 * 基于原始Lua代码的精确JavaScript实现
 */

// Base64字符表
const B64_CHARS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';

// 字符替换映射表（从原始Lua代码提取）
const CHAR_KEY_MAP = {
    "B": "Cb,", "S": "3C,", "5": "6D,", "D": "2W,", "c": "dc,", "E": "cj,", "b": "vt,", 
    "3": "Iv,", "s": "j1,", "N": "23,", "d": "mP,", "6": "wd,", "7": "7R,", "e": "ET,", 
    "t": "nB,", "8": "9v,", "4": "yP,", "W": "j6,", "9": "Wa,", "H": "D2,", "G": "Ve,", 
    "g": "JA,", "I": "Au,", "X": "NR,", "m": "DG,", "w": "Cx,", "Y": "Qi,", "V": "es,", 
    "F": "pF,", "z": "CO,", "K": "XC,", "f": "aW,", "J": "DT,", "x": "S9,", "y": "xi,", 
    "v": "My,", "L": "PW,", "u": "Aa,", "k": "Yx,", "M": "qL,", "j": "ab,", "r": "fN,", 
    "q": "0W,", "T": "de,", "l": "P8,", "0": "q6,", "n": "Hu,", "O": "A2,", "1": "VP,", 
    "i": "hY,", "h": "Uc,", "C": "cK,", "A": "f4,", "P": "is,", "U": "u2,", "o": "m9,", 
    "Q": "vd,", "R": "gZ,", "2": "Zu,", "Z": "Pf,", "a": "Lq,", "p": "Sw,"
};

// 字符表用于解密
const MAB = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/*=.，';

/**
 * Base64编码函数（精确复制Lua实现）
 * 注意：Lua使用Latin-1编码，不是UTF-8
 */
function encodeBase641(sourceStr) {
    console.log('[Base64] === 开始Base64编码 ===');
    console.log('[Base64] 原始字符串长度:', sourceStr.length);
    console.log('[Base64] 原始字符串:', sourceStr);

    // 使用Latin-1编码（与Lua的string.byte行为一致）
    const latin1Bytes = Buffer.from(sourceStr, 'latin1');
    console.log('[Base64] Latin-1字节数组长度:', latin1Bytes.length);
    console.log('[Base64] Latin-1前20字节:', Array.from(latin1Bytes.slice(0, 20)).map(b => b.toString(16).padStart(2, '0')).join(' '));

    let s64 = '';

    for (let i = 0; i < latin1Bytes.length; i += 3) {
        let bytesNum = 0;
        let buf = 0;

        // 处理3个字节
        for (let byteCnt = 0; byteCnt < 3; byteCnt++) {
            buf = buf * 256;
            if (i + byteCnt < latin1Bytes.length) {
                buf = buf + latin1Bytes[i + byteCnt];
                bytesNum = bytesNum + 1;
            }
        }

        // 生成Base64字符
        for (let groupCnt = 0; groupCnt < (bytesNum + 1); groupCnt++) {
            const b64char = Math.floor(buf / 262144) % 64;
            s64 = s64 + B64_CHARS.charAt(b64char);
            buf = buf * 64;
        }

        // 添加填充字符
        for (let fillCnt = 0; fillCnt < (3 - bytesNum); fillCnt++) {
            s64 = s64 + '=';
        }
    }

    console.log('[Base64] 编码结果长度:', s64.length);
    console.log('[Base64] 编码结果前50字符:', s64.substring(0, 50));
    console.log('[Base64] === Base64编码完成 ===');

    return s64;
}

/**
 * Base64解码函数（精确复制Lua实现，支持UTF-8）
 */
function decodeBase641(str64) {
    const temp = {};
    for (let i = 0; i < 64; i++) {
        temp[B64_CHARS.charAt(i)] = i + 1;
    }
    temp['='] = 0;

    const bytes = [];
    for (let i = 0; i < str64.length; i += 4) {
        if (i >= str64.length) break;

        let data = 0;
        let strCount = 0;

        for (let j = 0; j < 4; j++) {
            const str1 = str64.charAt(i + j);
            if (!temp.hasOwnProperty(str1)) {
                return null;
            }
            if (temp[str1] < 1) {
                data = data * 64;
            } else {
                data = data * 64 + temp[str1] - 1;
                strCount = strCount + 1;
            }
        }

        for (let j = 16; j >= 0; j -= 8) {
            if (strCount > 0) {
                const byteValue = Math.floor(data / Math.pow(2, j));
                if (byteValue !== 0) { // 跳过0字节
                    bytes.push(byteValue);
                }
                data = data % Math.pow(2, j);
                strCount = strCount - 1;
            }
        }
    }

    // 将字节数组转换为UTF-8字符串
    try {
        return Buffer.from(bytes).toString('utf8');
    } catch (error) {
        console.error('UTF-8解码失败:', error);
        return null;
    }
}

/**
 * 加密函数（jm函数的JavaScript实现）
 */
function jm(data) {
    console.log('[加密] === 开始加密过程 ===');
    console.log('[加密] 原始数据长度:', data.length);
    console.log('[加密] 原始数据前50字符:', data.substring(0, 50));

    const base64Data = encodeBase641(data);
    console.log('[加密] Base64编码后长度:', base64Data.length);
    console.log('[加密] Base64前50字符:', base64Data.substring(0, 50));

    let result = '';

    for (let i = 0; i < base64Data.length; i++) {
        const char = base64Data.charAt(i);
        if (char !== '' && CHAR_KEY_MAP[char]) {
            result += CHAR_KEY_MAP[char];
        } else {
            result += char;
        }
    }

    console.log('[加密] 字符替换后长度:', result.length);
    console.log('[加密] === 加密过程完成 ===');

    return result;
}

/**
 * 解密函数（jm1函数的JavaScript实现）
 */
function jm1(data) {
    let result = data;
    
    // 字符替换解密
    for (let i = 0; i < MAB.length; i++) {
        const char = MAB.charAt(i);
        if (CHAR_KEY_MAP[char]) {
            result = result.replace(new RegExp(escapeRegExp(CHAR_KEY_MAP[char]), 'g'), char);
        }
    }
    
    return decodeBase641(result);
}

/**
 * 转义正则表达式特殊字符
 */
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

/**
 * 数据格式化函数（精确模拟Lua的table.tostring）
 * 生成格式：do local ret={["key"]="value"} return ret end
 */
function tableToString(data) {
    if (typeof data === 'object' && data !== null) {
        const parts = [];
        for (const key in data) {
            if (data.hasOwnProperty(key)) {
                // 使用Lua格式：["key"]="value"
                parts.push(`["${key}"]="${data[key]}"`);
            }
        }
        return `do local ret={${parts.join(',')}} return ret end`;
    }
    return String(data);
}

module.exports = {
    encodeBase641,
    decodeBase641,
    jm,
    jm1,
    tableToString,
    CHAR_KEY_MAP
};
