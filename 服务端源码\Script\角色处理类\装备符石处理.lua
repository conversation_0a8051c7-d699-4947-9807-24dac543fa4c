local 装备符石处理 = class()
function 装备符石处理:初始化()
  self.头盔符石 =  {为官之道符石 = "为官之道",黄庭经符石 = "黄庭经",小乘佛法符石 = "小乘佛法",
                   毒经符石 = "毒经",天罡气符石 = "天罡气",九龙诀符石 = "九龙诀",周易学符石 = "周易学",
                   灵性符石 = "灵性",灵通术符石 = "灵通术",牛逼神功符石 = "牛逼神功",魔兽神功符石 = "魔兽神功",
                   蛛丝阵法符石 = "蛛丝阵法",诛魔符石 = "诛魔",燃灯灵宝符石 = "燃灯灵宝",巫咒符石 = "巫咒",
                   兵铸乾坤符石 = "兵铸乾坤"}



  self.项链符石 ={紫薇之术符石 = "紫薇之术",神道无念符石 = "神道无念",大慈大悲符石 = "大慈大悲",
                香飘兰麝符石 = "香飘兰麝",清明自在符石 = "清明自在",龙腾符石 = "龙腾",混元道果符石 ="混元道果",
                观音咒符石 = "观音咒",九幽阴魂符石 = "九幽阴魂",火云术符石 = "火云术",训兽诀符石 = "训兽诀",
                天外魔音符石 = "天外魔音",枯骨心法符石 = "枯骨心法",万灵诸念符石 = "万灵诸念",九转玄功符石 = "九转玄功" ,
                魂枫战舞符石 = "魂枫战舞"}



  self.武器符石 ={神兵鉴赏符石 = "神兵鉴赏",霹雳咒符石 = "霹雳咒",诵经符石 = "诵经",沉鱼落雁符石 = "沉鱼落雁",
                宁气诀符石 = "宁气诀",破浪诀符石 = "破浪诀",潇湘仙雨符石 = "潇湘仙雨",五行学说符石 = "五行学说",
                尸腐恶符石 = "尸腐恶",牛虱阵符石 = "牛虱阵",阴阳二气诀符石 = "阴阳二气诀",姊妹相随符石 = "姊妹相随",
                无双一击符石 = "无双一击", 磬龙灭法符石 = "磬龙灭法",金刚伏魔符石 = "金刚伏魔",玉质冰肌符石 = "玉质冰肌",
                混天术符石 = "混天术",龙附符石 = "龙附",修仙术符石 = "修仙术",护法金刚符石 = "护法金刚",六道轮回符石 = "六道轮回",
                震天诀符石 = "震天诀",狂兽诀符石 = "狂兽诀",秋波暗送符石 = "秋波暗送",秘影迷踪符石 = "秘影迷踪",混元神功符石 = "混元神功",
                天人庇护符石 = "天人庇护",神木恩泽符石 = "神木恩泽",天地无极符石 = "天地无极",啸傲符石 = "啸傲",
                九黎战歌符石 = "九黎战歌",战火雄魂符石 = "战火雄魂"}




  self.衣服符石 ={十方无敌符石 = "十方无敌",符之术符石 = "符之术",歧黄之术符石 = "歧黄之术",闭月羞花符石 = "闭月羞花",
                 乾坤塔符石 = "乾坤塔",呼风唤雨符石 = "呼风唤雨",乾坤袖符石 = "乾坤袖",金刚经符石 = "金刚经",
                 幽冥术符石 = "幽冥术",火牛阵符石 = "火牛阵",生死搏符石 = "生死搏",催情大法符石 = "催情大法",
                 阴风绝章符石 = "阴风绝章",瞬息万变符石 = "瞬息万变",气吞山河符石 = "气吞山河",燃铁飞花符石= "燃铁飞花"}

   self.腰带符石 ={文韬武略符石 = "文韬武略",归元心法符石 = "归元心法",佛光普照符石 = "佛光普照",倾国倾城符石 = "倾国倾城",
                傲世诀符石 = "傲世诀",逆鳞符石 = "逆鳞",明性修身符石 = "明性修身",五行扭转符石 = "五行扭转",
                拘魂诀符石 = "拘魂诀",回身击符石 = "回身击",魔兽反嗜符石 = "魔兽反嗜",盘丝大法符石 = "盘丝大法",
                地冥妙法符石 = "地冥妙法",万物轮转符石 = "万物轮转",武神显圣符石 = "武神显圣",魔神降世符石 = "魔神降世"}


   self.鞋子符石 ={疾风步符石 = "疾风步",斜月步符石 = "斜月步",渡世步符石 = "渡世步",清歌妙舞符石 = "清歌妙舞",
                云霄步符石 = "云霄步",游龙术符石 = "游龙术",七星遁符石 = "七星遁",莲花宝座符石 = "莲花宝座",
                无常步符石 = "无常步",裂石步符石 = "裂石步",大鹏展翅符石 = "大鹏展翅",移形换影符石 = "移形换影",
                法天象地符石 = "法天象地",驭灵咒符石 = "驭灵咒",鬼蛊灵蕴符石 = "鬼蛊灵蕴",风行九黎符石 = "风行九黎"}
end



function 装备符石处理:穿戴装备符文组合(id,装备,格子)
        if 装备.总类 == 2 then
              local 技能id = 0
              local 添加等级 = 0
              if 装备.星位.组合等级 ==1 or  装备.星位.组合等级 == 2 then
                添加等级 = 2
              elseif 装备.星位.组合等级 == 3 then
                    添加等级 = 4
              elseif 装备.星位.组合等级 >= 4 then
                      添加等级 = 6
              end
              for n,v in ipairs(玩家数据[id].角色.数据.师门技能) do
                    if (装备.分类 == 1 and v.名称==self.头盔符石[装备.星位.组合])
                    or (装备.分类 == 2 and v.名称==self.项链符石[装备.星位.组合])
                    or (装备.分类 == 3 and v.名称==self.武器符石[装备.星位.组合])
                    or (装备.分类 == 4 and v.名称==self.衣服符石[装备.星位.组合])
                    or (装备.分类 == 5 and v.名称==self.腰带符石[装备.星位.组合])
                    or (装备.分类 == 6 and v.名称==self.鞋子符石[装备.星位.组合]) then
                        技能id=n
                    end
                    if not v.名称 then
                        技能id = 0
                    end
              end
              if 技能id ~=0 then
                  玩家数据[id].角色.数据.师门技能[技能id].等级 = 玩家数据[id].角色.数据.师门技能[技能id].等级+添加等级
                  for i=1,#玩家数据[id].角色.数据.师门技能[技能id].包含技能 do
                      玩家数据[id].角色.数据.师门技能[技能id].包含技能[i].等级=玩家数据[id].角色.数据.师门技能[技能id].等级
                  end
              end
        end
end


  function 装备符石处理:卸下装备符文组合(id,装备,格子)
           if 装备.总类 == 2 then
                  local 技能id = 0
                  local 添加等级 = 0
                  if 装备.星位.组合等级 ==1 or  装备.星位.组合等级 == 2 then
                    添加等级 = 2
                  elseif 装备.星位.组合等级 == 3 then
                        添加等级 = 4
                  elseif 装备.星位.组合等级 >= 4 then
                          添加等级 = 6
                  end
                  for n,v in ipairs(玩家数据[id].角色.数据.师门技能) do
                        if (装备.分类 == 1 and v.名称==self.头盔符石[装备.星位.组合])
                        or (装备.分类 == 2 and v.名称==self.项链符石[装备.星位.组合])
                        or (装备.分类 == 3 and v.名称==self.武器符石[装备.星位.组合])
                        or (装备.分类 == 4 and v.名称==self.衣服符石[装备.星位.组合])
                        or (装备.分类 == 5 and v.名称==self.腰带符石[装备.星位.组合])
                        or (装备.分类 == 6 and v.名称==self.鞋子符石[装备.星位.组合]) then
                            技能id=n
                        end
                        if not v.名称 then
                            技能id = 0
                        end
                  end
                  if 技能id ~=0 then
                      玩家数据[id].角色.数据.师门技能[技能id].等级 = 玩家数据[id].角色.数据.师门技能[技能id].等级 - 添加等级
                      for i=1,#玩家数据[id].角色.数据.师门技能[技能id].包含技能 do
                          玩家数据[id].角色.数据.师门技能[技能id].包含技能[i].等级=玩家数据[id].角色.数据.师门技能[技能id].等级
                      end
                  end
            end
  end





return 装备符石处理

