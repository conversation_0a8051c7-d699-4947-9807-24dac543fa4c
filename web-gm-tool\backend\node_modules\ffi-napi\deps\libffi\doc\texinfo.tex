% texinfo.tex -- TeX macros to handle Texinfo files.
%
% Load plain if necessary, i.e., if running under initex.
\expandafter\ifx\csname fmtname\endcsname\relax\input plain\fi
%
\def\texinfoversion{2020-10-24.12}
%
% Copyright 1985, 1986, 1988, 1990-2020 Free Software Foundation, Inc.
%
% This texinfo.tex file is free software: you can redistribute it and/or
% modify it under the terms of the GNU General Public License as
% published by the Free Software Foundation, either version 3 of the
% License, or (at your option) any later version.
%
% This texinfo.tex file is distributed in the hope that it will be
% useful, but WITHOUT ANY WARRANTY; without even the implied warranty
% of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
% General Public License for more details.
%
% You should have received a copy of the GNU General Public License
% along with this program.  If not, see <https://www.gnu.org/licenses/>.
%
% As a special exception, when this file is read by TeX when processing
% a Texinfo source document, you may use the result without
% restriction. This Exception is an additional permission under section 7
% of the GNU General Public License, version 3 ("GPLv3").
%
% Please try the latest version of texinfo.tex before submitting bug
% reports; you can get the latest version from:
%   https://ftp.gnu.org/gnu/texinfo/ (the Texinfo release area), or
%   https://ftpmirror.gnu.org/texinfo/ (same, via a mirror), or
%   https://www.gnu.org/software/texinfo/ (the Texinfo home page)
% The texinfo.tex in any given distribution could well be out
% of date, so if that's what you're using, please check.
%
% Send bug <NAME_EMAIL>.  Please include a
% complete document in each bug report with which we can reproduce the
% problem.  Patches are, of course, greatly appreciated.
%
% To process a Texinfo manual with TeX, it's most reliable to use the
% texi2dvi shell script that comes with the distribution.  For a simple
% manual foo.texi, however, you can get away with this:
%   tex foo.texi
%   texindex foo.??
%   tex foo.texi
%   tex foo.texi
%   dvips foo.dvi -o  # or whatever; this makes foo.ps.
% The extra TeX runs get the cross-reference information correct.
% Sometimes one run after texindex suffices, and sometimes you need more
% than two; texi2dvi does it as many times as necessary.
%
% It is possible to adapt texinfo.tex for other languages, to some
% extent.  You can get the existing language-specific files from the
% full Texinfo distribution.
%
% The GNU Texinfo home page is https://www.gnu.org/software/texinfo.


\message{Loading texinfo [version \texinfoversion]:}

% If in a .fmt file, print the version number
% and turn on active characters that we couldn't do earlier because
% they might have appeared in the input file name.
\everyjob{\message{[Texinfo version \texinfoversion]}%
  \catcode`+=\active \catcode`\_=\active}

% LaTeX's \typeout.  This ensures that the messages it is used for
% are identical in format to the corresponding ones from latex/pdflatex.
\def\typeout{\immediate\write17}%

\chardef\other=12

% We never want plain's \outer definition of \+ in Texinfo.
% For @tex, we can use \tabalign.
\let\+ = \relax

% Save some plain tex macros whose names we will redefine.
\let\ptexb=\b
\let\ptexbullet=\bullet
\let\ptexc=\c
\let\ptexcomma=\,
\let\ptexdot=\.
\let\ptexdots=\dots
\let\ptexend=\end
\let\ptexequiv=\equiv
\let\ptexexclam=\!
\let\ptexfootnote=\footnote
\let\ptexgtr=>
\let\ptexhat=^
\let\ptexi=\i
\let\ptexindent=\indent
\let\ptexinsert=\insert
\let\ptexlbrace=\{
\let\ptexless=<
\let\ptexnewwrite\newwrite
\let\ptexnoindent=\noindent
\let\ptexplus=+
\let\ptexraggedright=\raggedright
\let\ptexrbrace=\}
\let\ptexslash=\/
\let\ptexsp=\sp
\let\ptexstar=\*
\let\ptexsup=\sup
\let\ptext=\t
\let\ptextop=\top
{\catcode`\'=\active \global\let\ptexquoteright'}% active in plain's math mode

% If this character appears in an error message or help string, it
% starts a new line in the output.
\newlinechar = `^^J

% Use TeX 3.0's \inputlineno to get the line number, for better error
% messages, but if we're using an old version of TeX, don't do anything.
%
\ifx\inputlineno\thisisundefined
  \let\linenumber = \empty % Pre-3.0.
\else
  \def\linenumber{l.\the\inputlineno:\space}
\fi

% Set up fixed words for English if not already set.
\ifx\putwordAppendix\undefined  \gdef\putwordAppendix{Appendix}\fi
\ifx\putwordChapter\undefined   \gdef\putwordChapter{Chapter}\fi
\ifx\putworderror\undefined     \gdef\putworderror{error}\fi
\ifx\putwordfile\undefined      \gdef\putwordfile{file}\fi
\ifx\putwordin\undefined        \gdef\putwordin{in}\fi
\ifx\putwordIndexIsEmpty\undefined       \gdef\putwordIndexIsEmpty{(Index is empty)}\fi
\ifx\putwordIndexNonexistent\undefined   \gdef\putwordIndexNonexistent{(Index is nonexistent)}\fi
\ifx\putwordInfo\undefined      \gdef\putwordInfo{Info}\fi
\ifx\putwordInstanceVariableof\undefined \gdef\putwordInstanceVariableof{Instance Variable of}\fi
\ifx\putwordMethodon\undefined  \gdef\putwordMethodon{Method on}\fi
\ifx\putwordNoTitle\undefined   \gdef\putwordNoTitle{No Title}\fi
\ifx\putwordof\undefined        \gdef\putwordof{of}\fi
\ifx\putwordon\undefined        \gdef\putwordon{on}\fi
\ifx\putwordpage\undefined      \gdef\putwordpage{page}\fi
\ifx\putwordsection\undefined   \gdef\putwordsection{section}\fi
\ifx\putwordSection\undefined   \gdef\putwordSection{Section}\fi
\ifx\putwordsee\undefined       \gdef\putwordsee{see}\fi
\ifx\putwordSee\undefined       \gdef\putwordSee{See}\fi
\ifx\putwordShortTOC\undefined  \gdef\putwordShortTOC{Short Contents}\fi
\ifx\putwordTOC\undefined       \gdef\putwordTOC{Table of Contents}\fi
%
\ifx\putwordMJan\undefined \gdef\putwordMJan{January}\fi
\ifx\putwordMFeb\undefined \gdef\putwordMFeb{February}\fi
\ifx\putwordMMar\undefined \gdef\putwordMMar{March}\fi
\ifx\putwordMApr\undefined \gdef\putwordMApr{April}\fi
\ifx\putwordMMay\undefined \gdef\putwordMMay{May}\fi
\ifx\putwordMJun\undefined \gdef\putwordMJun{June}\fi
\ifx\putwordMJul\undefined \gdef\putwordMJul{July}\fi
\ifx\putwordMAug\undefined \gdef\putwordMAug{August}\fi
\ifx\putwordMSep\undefined \gdef\putwordMSep{September}\fi
\ifx\putwordMOct\undefined \gdef\putwordMOct{October}\fi
\ifx\putwordMNov\undefined \gdef\putwordMNov{November}\fi
\ifx\putwordMDec\undefined \gdef\putwordMDec{December}\fi
%
\ifx\putwordDefmac\undefined    \gdef\putwordDefmac{Macro}\fi
\ifx\putwordDefspec\undefined   \gdef\putwordDefspec{Special Form}\fi
\ifx\putwordDefvar\undefined    \gdef\putwordDefvar{Variable}\fi
\ifx\putwordDefopt\undefined    \gdef\putwordDefopt{User Option}\fi
\ifx\putwordDeffunc\undefined   \gdef\putwordDeffunc{Function}\fi

% Give the space character the catcode for a space.
\def\spaceisspace{\catcode`\ =10\relax}

% Likewise for ^^M, the end of line character.
\def\endlineisspace{\catcode13=10\relax}

\chardef\dashChar  = `\-
\chardef\slashChar = `\/
\chardef\underChar = `\_

% Ignore a token.
%
\def\gobble#1{}

% The following is used inside several \edef's.
\def\makecsname#1{\expandafter\noexpand\csname#1\endcsname}

% Hyphenation fixes.
\hyphenation{
  Flor-i-da Ghost-script Ghost-view Mac-OS Post-Script
  ap-pen-dix bit-map bit-maps
  data-base data-bases eshell fall-ing half-way long-est man-u-script
  man-u-scripts mini-buf-fer mini-buf-fers over-view par-a-digm
  par-a-digms rath-er rec-tan-gu-lar ro-bot-ics se-vere-ly set-up spa-ces
  spell-ing spell-ings
  stand-alone strong-est time-stamp time-stamps which-ever white-space
  wide-spread wrap-around
}

% Sometimes it is convenient to have everything in the transcript file
% and nothing on the terminal.  We don't just call \tracingall here,
% since that produces some useless output on the terminal.  We also make
% some effort to order the tracing commands to reduce output in the log
% file; cf. trace.sty in LaTeX.
%
\def\gloggingall{\begingroup \globaldefs = 1 \loggingall \endgroup}%
\def\loggingall{%
  \tracingstats2
  \tracingpages1
  \tracinglostchars2  % 2 gives us more in etex
  \tracingparagraphs1
  \tracingoutput1
  \tracingmacros2
  \tracingrestores1
  \showboxbreadth\maxdimen \showboxdepth\maxdimen
  \ifx\eTeXversion\thisisundefined\else % etex gives us more logging
    \tracingscantokens1
    \tracingifs1
    \tracinggroups1
    \tracingnesting2
    \tracingassigns1
  \fi
  \tracingcommands3  % 3 gives us more in etex
  \errorcontextlines16
}%

% @errormsg{MSG}.  Do the index-like expansions on MSG, but if things
% aren't perfect, it's not the end of the world, being an error message,
% after all.
%
\def\errormsg{\begingroup \indexnofonts \doerrormsg}
\def\doerrormsg#1{\errmessage{#1}}

% add check for \lastpenalty to plain's definitions.  If the last thing
% we did was a \nobreak, we don't want to insert more space.
%
\def\smallbreak{\ifnum\lastpenalty<10000\par\ifdim\lastskip<\smallskipamount
  \removelastskip\penalty-50\smallskip\fi\fi}
\def\medbreak{\ifnum\lastpenalty<10000\par\ifdim\lastskip<\medskipamount
  \removelastskip\penalty-100\medskip\fi\fi}
\def\bigbreak{\ifnum\lastpenalty<10000\par\ifdim\lastskip<\bigskipamount
  \removelastskip\penalty-200\bigskip\fi\fi}

% Output routine
%

% For a final copy, take out the rectangles
% that mark overfull boxes (in case you have decided
% that the text looks ok even though it passes the margin).
%
\def\finalout{\overfullrule=0pt }

\newdimen\outerhsize \newdimen\outervsize % set by the paper size routines
\newdimen\topandbottommargin \topandbottommargin=.75in

% Output a mark which sets \thischapter, \thissection and \thiscolor.
% We dump everything together because we only have one kind of mark.
% This works because we only use \botmark / \topmark, not \firstmark.
%
% A mark contains a subexpression of the \ifcase ... \fi construct.
% \get*marks macros below extract the needed part using \ifcase.
%
% Another complication is to let the user choose whether \thischapter
% (\thissection) refers to the chapter (section) in effect at the top
% of a page, or that at the bottom of a page.

% \domark is called twice inside \chapmacro, to add one
% mark before the section break, and one after.
%   In the second call \prevchapterdefs is the same as \currentchapterdefs,
% and \prevsectiondefs is the same as \currentsectiondefs.
%   Then if the page is not broken at the mark, some of the previous
% section appears on the page, and we can get the name of this section
% from \firstmark for @everyheadingmarks top.
%   @everyheadingmarks bottom uses \botmark.
%
% See page 260 of The TeXbook.
\def\domark{%
  \toks0=\expandafter{\currentchapterdefs}%
  \toks2=\expandafter{\currentsectiondefs}%
  \toks4=\expandafter{\prevchapterdefs}%
  \toks6=\expandafter{\prevsectiondefs}%
  \toks8=\expandafter{\currentcolordefs}%
  \mark{%
                   \the\toks0 \the\toks2  % 0: marks for @everyheadingmarks top
      \noexpand\or \the\toks4 \the\toks6  % 1: for @everyheadingmarks bottom
    \noexpand\else \the\toks8             % 2: color marks
  }%
}

% \gettopheadingmarks, \getbottomheadingmarks,
% \getcolormarks - extract needed part of mark.
%
% \topmark doesn't work for the very first chapter (after the title
% page or the contents), so we use \firstmark there -- this gets us
% the mark with the chapter defs, unless the user sneaks in, e.g.,
% @setcolor (or @url, or @link, etc.) between @contents and the very
% first @chapter.
\def\gettopheadingmarks{%
  \ifcase0\the\savedtopmark\fi
  \ifx\thischapter\empty \ifcase0\firstmark\fi \fi
}
\def\getbottomheadingmarks{\ifcase1\botmark\fi}
\def\getcolormarks{\ifcase2\the\savedtopmark\fi}

% Avoid "undefined control sequence" errors.
\def\currentchapterdefs{}
\def\currentsectiondefs{}
\def\currentsection{}
\def\prevchapterdefs{}
\def\prevsectiondefs{}
\def\currentcolordefs{}

% Margin to add to right of even pages, to left of odd pages.
\newdimen\bindingoffset
\newdimen\normaloffset
\newdimen\txipagewidth \newdimen\txipageheight

% Main output routine.
%
\chardef\PAGE = 255
\newtoks\defaultoutput
\defaultoutput = {\savetopmark\onepageout{\pagecontents\PAGE}}
\output=\expandafter{\the\defaultoutput}

\newbox\headlinebox
\newbox\footlinebox

% When outputting the double column layout for indices, an output routine
% is run several times, which hides the original value of \topmark.  This
% can lead to a page heading being output and duplicating the chapter heading
% of the index.  Hence, save the contents of \topmark at the beginning of
% the output routine.  The saved contents are valid until we actually
% \shipout a page.
%
% (We used to run a short output routine to actually set \topmark and
% \firstmark to the right values, but if this was called with an empty page
% containing whatsits for writing index entries, the whatsits would be thrown
% away and the index auxiliary file would remain empty.)
%
\newtoks\savedtopmark
\newif\iftopmarksaved
\topmarksavedtrue
\def\savetopmark{%
  \iftopmarksaved\else
    \global\savedtopmark=\expandafter{\topmark}%
    \global\topmarksavedtrue
  \fi
}

% \onepageout takes a vbox as an argument.
% \shipout a vbox for a single page, adding an optional header, footer
% and footnote.  This also causes index entries for this page to be written
% to the auxiliary files.
%
\def\onepageout#1{%
  \hoffset=\normaloffset
  %
  \ifodd\pageno  \advance\hoffset by \bindingoffset
  \else \advance\hoffset by -\bindingoffset\fi
  %
  \checkchapterpage
  %
  % Retrieve the information for the headings from the marks in the page,
  % and call Plain TeX's \makeheadline and \makefootline, which use the
  % values in \headline and \footline.
  %
  % Common context changes for both heading and footing.
  % Do this outside of the \shipout so @code etc. will be expanded in
  % the headline as they should be, not taken literally (outputting ''code).
  \def\commonheadfootline{\let\hsize=\txipagewidth \texinfochars}
  %
  \ifodd\pageno \getoddheadingmarks \else \getevenheadingmarks \fi
  \global\setbox\headlinebox = \vbox{\commonheadfootline \makeheadline}%
  \ifodd\pageno \getoddfootingmarks \else \getevenfootingmarks \fi
  \global\setbox\footlinebox = \vbox{\commonheadfootline \makefootline}%
  %
  {%
    % Set context for writing to auxiliary files like index files.
    % Have to do this stuff outside the \shipout because we want it to
    % take effect in \write's, yet the group defined by the \vbox ends
    % before the \shipout runs.
    %
    \atdummies         % don't expand commands in the output.
    \turnoffactive
    \shipout\vbox{%
      % Do this early so pdf references go to the beginning of the page.
      \ifpdfmakepagedest \pdfdest name{\the\pageno} xyz\fi
      %
      \unvbox\headlinebox
      \pagebody{#1}%
      \ifdim\ht\footlinebox > 0pt
        % Only leave this space if the footline is nonempty.
        % (We lessened \vsize for it in \oddfootingyyy.)
        % The \baselineskip=24pt in plain's \makefootline has no effect.
        \vskip 24pt
        \unvbox\footlinebox
      \fi
      %
    }%
  }%
  \global\topmarksavedfalse
  \advancepageno
  \ifnum\outputpenalty>-20000 \else\dosupereject\fi
}

\newinsert\margin \dimen\margin=\maxdimen

% Main part of page, including any footnotes
\def\pagebody#1{\vbox to\txipageheight{\boxmaxdepth=\maxdepth #1}}
{\catcode`\@ =11
\gdef\pagecontents#1{\ifvoid\topins\else\unvbox\topins\fi
% marginal hacks, <EMAIL> (Juha Takala)
\ifvoid\margin\else % marginal info is present
  \rlap{\kern\hsize\vbox to\z@{\kern1pt\box\margin \vss}}\fi
\dimen@=\dp#1\relax \unvbox#1\relax
\ifvoid\footins\else\vskip\skip\footins\footnoterule \unvbox\footins\fi
\ifr@ggedbottom \kern-\dimen@ \vfil \fi}
}

% Check if we are on the first page of a chapter.  Used for printing headings.
\newif\ifchapterpage
\def\checkchapterpage{%
  % Get the chapter that was current at the end of the last page
  \ifcase1\the\savedtopmark\fi
  \let\prevchaptername\thischaptername
  %
  \ifodd\pageno \getoddheadingmarks \else \getevenheadingmarks \fi
  \let\curchaptername\thischaptername
  %
  \ifx\curchaptername\prevchaptername
    \chapterpagefalse
  \else
    \chapterpagetrue
  \fi
}

% Argument parsing

% Parse an argument, then pass it to #1.  The argument is the rest of
% the input line (except we remove a trailing comment).  #1 should be a
% macro which expects an ordinary undelimited TeX argument.
% For example, \def\foo{\parsearg\fooxxx}.
%
\def\parsearg{\parseargusing{}}
\def\parseargusing#1#2{%
  \def\argtorun{#2}%
  \begingroup
    \obeylines
    \spaceisspace
    #1%
    \parseargline\empty% Insert the \empty token, see \finishparsearg below.
}

{\obeylines %
  \gdef\parseargline#1^^M{%
    \endgroup % End of the group started in \parsearg.
    \argremovecomment #1\comment\ArgTerm%
  }%
}

% First remove any @comment, then any @c comment.  Pass the result on to
% \argcheckspaces.
\def\argremovecomment#1\comment#2\ArgTerm{\argremovec #1\c\ArgTerm}
\def\argremovec#1\c#2\ArgTerm{\argcheckspaces#1\^^M\ArgTerm}

% Each occurrence of `\^^M' or `<space>\^^M' is replaced by a single space.
%
% \argremovec might leave us with trailing space, e.g.,
%    @end itemize  @c foo
% This space token undergoes the same procedure and is eventually removed
% by \finishparsearg.
%
\def\argcheckspaces#1\^^M{\argcheckspacesX#1\^^M \^^M}
\def\argcheckspacesX#1 \^^M{\argcheckspacesY#1\^^M}
\def\argcheckspacesY#1\^^M#2\^^M#3\ArgTerm{%
  \def\temp{#3}%
  \ifx\temp\empty
    % Do not use \next, perhaps the caller of \parsearg uses it; reuse \temp:
    \let\temp\finishparsearg
  \else
    \let\temp\argcheckspaces
  \fi
  % Put the space token in:
  \temp#1 #3\ArgTerm
}

% If a _delimited_ argument is enclosed in braces, they get stripped; so
% to get _exactly_ the rest of the line, we had to prevent such situation.
% We prepended an \empty token at the very beginning and we expand it now,
% just before passing the control to \argtorun.
% (Similarly, we have to think about #3 of \argcheckspacesY above: it is
% either the null string, or it ends with \^^M---thus there is no danger
% that a pair of braces would be stripped.
%
% But first, we have to remove the trailing space token.
%
\def\finishparsearg#1 \ArgTerm{\expandafter\argtorun\expandafter{#1}}


% \parseargdef - define a command taking an argument on the line
%
% \parseargdef\foo{...}
%	is roughly equivalent to
% \def\foo{\parsearg\Xfoo}
% \def\Xfoo#1{...}
\def\parseargdef#1{%
  \expandafter \doparseargdef \csname\string#1\endcsname #1%
}
\def\doparseargdef#1#2{%
  \def#2{\parsearg#1}%
  \def#1##1%
}

% Several utility definitions with active space:
{
  \obeyspaces
  \gdef\obeyedspace{ }

  % Make each space character in the input produce a normal interword
  % space in the output.  Don't allow a line break at this space, as this
  % is used only in environments like @example, where each line of input
  % should produce a line of output anyway.
  %
  \gdef\sepspaces{\obeyspaces\let =\tie}

  % If an index command is used in an @example environment, any spaces
  % therein should become regular spaces in the raw index file, not the
  % expansion of \tie (\leavevmode \penalty \@M \ ).
  \gdef\unsepspaces{\let =\space}
}


\def\flushcr{\ifx\par\lisppar \def\next##1{}\else \let\next=\relax \fi \next}

% Define the framework for environments in texinfo.tex.  It's used like this:
%
%   \envdef\foo{...}
%   \def\Efoo{...}
%
% It's the responsibility of \envdef to insert \begingroup before the
% actual body; @end closes the group after calling \Efoo.  \envdef also
% defines \thisenv, so the current environment is known; @end checks
% whether the environment name matches.  The \checkenv macro can also be
% used to check whether the current environment is the one expected.
%
% Non-false conditionals (@iftex, @ifset) don't fit into this, so they
% are not treated as environments; they don't open a group.  (The
% implementation of @end takes care not to call \endgroup in this
% special case.)


% At run-time, environments start with this:
\def\startenvironment#1{\begingroup\def\thisenv{#1}}
% initialize
\let\thisenv\empty

% ... but they get defined via ``\envdef\foo{...}'':
\long\def\envdef#1#2{\def#1{\startenvironment#1#2}}
\def\envparseargdef#1#2{\parseargdef#1{\startenvironment#1#2}}

% Check whether we're in the right environment:
\def\checkenv#1{%
  \def\temp{#1}%
  \ifx\thisenv\temp
  \else
    \badenverr
  \fi
}

% Environment mismatch, #1 expected:
\def\badenverr{%
  \errhelp = \EMsimple
  \errmessage{This command can appear only \inenvironment\temp,
    not \inenvironment\thisenv}%
}
\def\inenvironment#1{%
  \ifx#1\empty
    outside of any environment%
  \else
    in environment \expandafter\string#1%
  \fi
}

% @end foo executes the definition of \Efoo.
% But first, it executes a specialized version of \checkenv
%
\parseargdef\end{%
  \if 1\csname iscond.#1\endcsname
  \else
    % The general wording of \badenverr may not be ideal.
    \expandafter\checkenv\csname#1\endcsname
    \csname E#1\endcsname
    \endgroup
  \fi
}

\newhelp\EMsimple{Press RETURN to continue.}


% Be sure we're in horizontal mode when doing a tie, since we make space
% equivalent to this in @example-like environments. Otherwise, a space
% at the beginning of a line will start with \penalty -- and
% since \penalty is valid in vertical mode, we'd end up putting the
% penalty on the vertical list instead of in the new paragraph.
{\catcode`@ = 11
 % Avoid using \@M directly, because that causes trouble
 % if the definition is written into an index file.
 \global\let\tiepenalty = \@M
 \gdef\tie{\leavevmode\penalty\tiepenalty\ }
}

% @: forces normal size whitespace following.
\def\:{\spacefactor=1000 }

% @* forces a line break.
\def\*{\unskip\hfil\break\hbox{}\ignorespaces}

% @/ allows a line break.
\let\/=\allowbreak

% @. is an end-of-sentence period.
\def\.{.\spacefactor=\endofsentencespacefactor\space}

% @! is an end-of-sentence bang.
\def\!{!\spacefactor=\endofsentencespacefactor\space}

% @? is an end-of-sentence query.
\def\?{?\spacefactor=\endofsentencespacefactor\space}

% @frenchspacing on|off  says whether to put extra space after punctuation.
%
\def\onword{on}
\def\offword{off}
%
\parseargdef\frenchspacing{%
  \def\temp{#1}%
  \ifx\temp\onword \plainfrenchspacing
  \else\ifx\temp\offword \plainnonfrenchspacing
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @frenchspacing option `\temp', must be on|off}%
  \fi\fi
}

% @w prevents a word break.  Without the \leavevmode, @w at the
% beginning of a paragraph, when TeX is still in vertical mode, would
% produce a whole line of output instead of starting the paragraph.
\def\w#1{\leavevmode\hbox{#1}}

% @group ... @end group forces ... to be all on one page, by enclosing
% it in a TeX vbox.  We use \vtop instead of \vbox to construct the box
% to keep its height that of a normal line.  According to the rules for
% \topskip (p.114 of the TeXbook), the glue inserted is
% max (\topskip - \ht (first item), 0).  If that height is large,
% therefore, no glue is inserted, and the space between the headline and
% the text is small, which looks bad.
%
% Another complication is that the group might be very large.  This can
% cause the glue on the previous page to be unduly stretched, because it
% does not have much material.  In this case, it's better to add an
% explicit \vfill so that the extra space is at the bottom.  The
% threshold for doing this is if the group is more than \vfilllimit
% percent of a page (\vfilllimit can be changed inside of @tex).
%
\newbox\groupbox
\def\vfilllimit{0.7}
%
\envdef\group{%
  \ifnum\catcode`\^^M=\active \else
    \errhelp = \groupinvalidhelp
    \errmessage{@group invalid in context where filling is enabled}%
  \fi
  \startsavinginserts
  %
  \setbox\groupbox = \vtop\bgroup
    % Do @comment since we are called inside an environment such as
    % @example, where each end-of-line in the input causes an
    % end-of-line in the output.  We don't want the end-of-line after
    % the `@group' to put extra space in the output.  Since @group
    % should appear on a line by itself (according to the Texinfo
    % manual), we don't worry about eating any user text.
    \comment
}
%
% The \vtop produces a box with normal height and large depth; thus, TeX puts
% \baselineskip glue before it, and (when the next line of text is done)
% \lineskip glue after it.  Thus, space below is not quite equal to space
% above.  But it's pretty close.
\def\Egroup{%
    % To get correct interline space between the last line of the group
    % and the first line afterwards, we have to propagate \prevdepth.
    \endgraf % Not \par, as it may have been set to \lisppar.
    \global\dimen1 = \prevdepth
  \egroup           % End the \vtop.
  \addgroupbox
  \prevdepth = \dimen1
  \checkinserts
}

\def\addgroupbox{
  % \dimen0 is the vertical size of the group's box.
  \dimen0 = \ht\groupbox  \advance\dimen0 by \dp\groupbox
  % \dimen2 is how much space is left on the page (more or less).
  \dimen2 = \txipageheight   \advance\dimen2 by -\pagetotal
  % if the group doesn't fit on the current page, and it's a big big
  % group, force a page break.
  \ifdim \dimen0 > \dimen2
    \ifdim \pagetotal < \vfilllimit\txipageheight
      \page
    \fi
  \fi
  \box\groupbox
}

%
% TeX puts in an \escapechar (i.e., `@') at the beginning of the help
% message, so this ends up printing `@group can only ...'.
%
\newhelp\groupinvalidhelp{%
group can only be used in environments such as @example,^^J%
where each line of input produces a line of output.}

% @need space-in-mils
% forces a page break if there is not space-in-mils remaining.

\newdimen\mil  \mil=0.001in

\parseargdef\need{%
  % Ensure vertical mode, so we don't make a big box in the middle of a
  % paragraph.
  \par
  %
  % If the @need value is less than one line space, it's useless.
  \dimen0 = #1\mil
  \dimen2 = \ht\strutbox
  \advance\dimen2 by \dp\strutbox
  \ifdim\dimen0 > \dimen2
    %
    % Do a \strut just to make the height of this box be normal, so the
    % normal leading is inserted relative to the preceding line.
    % And a page break here is fine.
    \vtop to #1\mil{\strut\vfil}%
    %
    % TeX does not even consider page breaks if a penalty added to the
    % main vertical list is 10000 or more.  But in order to see if the
    % empty box we just added fits on the page, we must make it consider
    % page breaks.  On the other hand, we don't want to actually break the
    % page after the empty box.  So we use a penalty of 9999.
    %
    % There is an extremely small chance that TeX will actually break the
    % page at this \penalty, if there are no other feasible breakpoints in
    % sight.  (If the user is using lots of big @group commands, which
    % almost-but-not-quite fill up a page, TeX will have a hard time doing
    % good page breaking, for example.)  However, I could not construct an
    % example where a page broke at this \penalty; if it happens in a real
    % document, then we can reconsider our strategy.
    \penalty9999
    %
    % Back up by the size of the box, whether we did a page break or not.
    \kern -#1\mil
    %
    % Do not allow a page break right after this kern.
    \nobreak
  \fi
}

% @br   forces paragraph break (and is undocumented).

\let\br = \par

% @page forces the start of a new page.
%
\def\page{\par\vfill\supereject}

% @exdent text....
% outputs text on separate line in roman font, starting at standard page margin

% This records the amount of indent in the innermost environment.
% That's how much \exdent should take out.
\newskip\exdentamount

% This defn is used inside fill environments such as @defun.
\parseargdef\exdent{\hfil\break\hbox{\kern -\exdentamount{\rm#1}}\hfil\break}

% This defn is used inside nofill environments such as @example.
\parseargdef\nofillexdent{{\advance \leftskip by -\exdentamount
  \leftline{\hskip\leftskip{\rm#1}}}}

% @inmargin{WHICH}{TEXT} puts TEXT in the WHICH margin next to the current
% paragraph.  For more general purposes, use the \margin insertion
% class.  WHICH is `l' or `r'.  Not documented, written for gawk manual.
%
\newskip\inmarginspacing \inmarginspacing=1cm
\def\strutdepth{\dp\strutbox}
%
\def\doinmargin#1#2{\strut\vadjust{%
  \nobreak
  \kern-\strutdepth
  \vtop to \strutdepth{%
    \baselineskip=\strutdepth
    \vss
    % if you have multiple lines of stuff to put here, you'll need to
    % make the vbox yourself of the appropriate size.
    \ifx#1l%
      \llap{\ignorespaces #2\hskip\inmarginspacing}%
    \else
      \rlap{\hskip\hsize \hskip\inmarginspacing \ignorespaces #2}%
    \fi
    \null
  }%
}}
\def\inleftmargin{\doinmargin l}
\def\inrightmargin{\doinmargin r}
%
% @inmargin{TEXT [, RIGHT-TEXT]}
% (if RIGHT-TEXT is given, use TEXT for left page, RIGHT-TEXT for right;
% else use TEXT for both).
%
\def\inmargin#1{\parseinmargin #1,,\finish}
\def\parseinmargin#1,#2,#3\finish{% not perfect, but better than nothing.
  \setbox0 = \hbox{\ignorespaces #2}%
  \ifdim\wd0 > 0pt
    \def\lefttext{#1}%  have both texts
    \def\righttext{#2}%
  \else
    \def\lefttext{#1}%  have only one text
    \def\righttext{#1}%
  \fi
  %
  \ifodd\pageno
    \def\temp{\inrightmargin\righttext}% odd page -> outside is right margin
  \else
    \def\temp{\inleftmargin\lefttext}%
  \fi
  \temp
}

% @include FILE -- \input text of FILE.
%
\def\include{\parseargusing\filenamecatcodes\includezzz}
\def\includezzz#1{%
  \pushthisfilestack
  \def\thisfile{#1}%
  {%
    \makevalueexpandable  % we want to expand any @value in FILE.
    \turnoffactive        % and allow special characters in the expansion
    \indexnofonts         % Allow `@@' and other weird things in file names.
    \wlog{texinfo.tex: doing @include of #1^^J}%
    \edef\temp{\noexpand\input #1 }%
    %
    % This trickery is to read FILE outside of a group, in case it makes
    % definitions, etc.
    \expandafter
  }\temp
  \popthisfilestack
}
\def\filenamecatcodes{%
  \catcode`\\=\other
  \catcode`~=\other
  \catcode`^=\other
  \catcode`_=\other
  \catcode`|=\other
  \catcode`<=\other
  \catcode`>=\other
  \catcode`+=\other
  \catcode`-=\other
  \catcode`\`=\other
  \catcode`\'=\other
}

\def\pushthisfilestack{%
  \expandafter\pushthisfilestackX\popthisfilestack\StackTerm
}
\def\pushthisfilestackX{%
  \expandafter\pushthisfilestackY\thisfile\StackTerm
}
\def\pushthisfilestackY #1\StackTerm #2\StackTerm {%
  \gdef\popthisfilestack{\gdef\thisfile{#1}\gdef\popthisfilestack{#2}}%
}

\def\popthisfilestack{\errthisfilestackempty}
\def\errthisfilestackempty{\errmessage{Internal error:
  the stack of filenames is empty.}}
%
\def\thisfile{}

% @center line
% outputs that line, centered.
%
\parseargdef\center{%
  \ifhmode
    \let\centersub\centerH
  \else
    \let\centersub\centerV
  \fi
  \centersub{\hfil \ignorespaces#1\unskip \hfil}%
  \let\centersub\relax % don't let the definition persist, just in case
}
\def\centerH#1{{%
  \hfil\break
  \advance\hsize by -\leftskip
  \advance\hsize by -\rightskip
  \line{#1}%
  \break
}}
%
\newcount\centerpenalty
\def\centerV#1{%
  % The idea here is the same as in \startdefun, \cartouche, etc.: if
  % @center is the first thing after a section heading, we need to wipe
  % out the negative parskip inserted by \sectionheading, but still
  % prevent a page break here.
  \centerpenalty = \lastpenalty
  \ifnum\centerpenalty>10000 \vskip\parskip \fi
  \ifnum\centerpenalty>9999 \penalty\centerpenalty \fi
  \line{\kern\leftskip #1\kern\rightskip}%
}

% @sp n   outputs n lines of vertical space
%
\parseargdef\sp{\vskip #1\baselineskip}

% @comment ...line which is ignored...
% @c is the same as @comment
% @ignore ... @end ignore  is another way to write a comment


\def\c{\begingroup \catcode`\^^M=\active%
\catcode`\@=\other \catcode`\{=\other \catcode`\}=\other%
\cxxx}
{\catcode`\^^M=\active \gdef\cxxx#1^^M{\endgroup}}
%
\let\comment\c

% @paragraphindent NCHARS
% We'll use ems for NCHARS, close enough.
% NCHARS can also be the word `asis' or `none'.
% We cannot feasibly implement @paragraphindent asis, though.
%
\def\asisword{asis} % no translation, these are keywords
\def\noneword{none}
%
\parseargdef\paragraphindent{%
  \def\temp{#1}%
  \ifx\temp\asisword
  \else
    \ifx\temp\noneword
      \defaultparindent = 0pt
    \else
      \defaultparindent = #1em
    \fi
  \fi
  \parindent = \defaultparindent
}

% @exampleindent NCHARS
% We'll use ems for NCHARS like @paragraphindent.
% It seems @exampleindent asis isn't necessary, but
% I preserve it to make it similar to @paragraphindent.
\parseargdef\exampleindent{%
  \def\temp{#1}%
  \ifx\temp\asisword
  \else
    \ifx\temp\noneword
      \lispnarrowing = 0pt
    \else
      \lispnarrowing = #1em
    \fi
  \fi
}

% @firstparagraphindent WORD
% If WORD is `none', then suppress indentation of the first paragraph
% after a section heading.  If WORD is `insert', then do indent at such
% paragraphs.
%
% The paragraph indentation is suppressed or not by calling
% \suppressfirstparagraphindent, which the sectioning commands do.
% We switch the definition of this back and forth according to WORD.
% By default, we suppress indentation.
%
\def\suppressfirstparagraphindent{\dosuppressfirstparagraphindent}
\def\insertword{insert}
%
\parseargdef\firstparagraphindent{%
  \def\temp{#1}%
  \ifx\temp\noneword
    \let\suppressfirstparagraphindent = \dosuppressfirstparagraphindent
  \else\ifx\temp\insertword
    \let\suppressfirstparagraphindent = \relax
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @firstparagraphindent option `\temp'}%
  \fi\fi
}

% Here is how we actually suppress indentation.  Redefine \everypar to
% \kern backwards by \parindent, and then reset itself to empty.
%
% We also make \indent itself not actually do anything until the next
% paragraph.
%
\gdef\dosuppressfirstparagraphindent{%
  \gdef\indent  {\restorefirstparagraphindent \indent}%
  \gdef\noindent{\restorefirstparagraphindent \noindent}%
  \global\everypar = {\kern -\parindent \restorefirstparagraphindent}%
}
%
\gdef\restorefirstparagraphindent{%
  \global\let\indent = \ptexindent
  \global\let\noindent = \ptexnoindent
  \global\everypar = {}%
}


% @refill is a no-op.
\let\refill=\relax

% @setfilename INFO-FILENAME - ignored
\let\setfilename=\comment

% @bye.
\outer\def\bye{\chappager\pagelabels\tracingstats=1\ptexend}


\message{pdf,}
% adobe `portable' document format
\newcount\tempnum
\newcount\lnkcount
\newtoks\filename
\newcount\filenamelength
\newcount\pgn
\newtoks\toksA
\newtoks\toksB
\newtoks\toksC
\newtoks\toksD
\newbox\boxA
\newbox\boxB
\newcount\countA
\newif\ifpdf
\newif\ifpdfmakepagedest

%
% For LuaTeX
%

\newif\iftxiuseunicodedestname
\txiuseunicodedestnamefalse % For pdfTeX etc.

\ifx\luatexversion\thisisundefined
\else
  % Use Unicode destination names
  \txiuseunicodedestnametrue
  % Escape PDF strings with converting UTF-16 from UTF-8
  \begingroup
    \catcode`\%=12
    \directlua{
      function UTF16oct(str)
        tex.sprint(string.char(0x5c) .. '376' .. string.char(0x5c) .. '377')
        for c in string.utfvalues(str) do
          if c < 0x10000 then
            tex.sprint(
              string.format(string.char(0x5c) .. string.char(0x25) .. '03o' ..
                            string.char(0x5c) .. string.char(0x25) .. '03o',
                            math.floor(c / 256), math.floor(c % 256)))
          else
            c = c - 0x10000
            local c_hi = c / 1024 + 0xd800
            local c_lo = c % 1024 + 0xdc00
            tex.sprint(
              string.format(string.char(0x5c) .. string.char(0x25) .. '03o' ..
                            string.char(0x5c) .. string.char(0x25) .. '03o' ..
                            string.char(0x5c) .. string.char(0x25) .. '03o' ..
                            string.char(0x5c) .. string.char(0x25) .. '03o',
                            math.floor(c_hi / 256), math.floor(c_hi % 256),
                            math.floor(c_lo / 256), math.floor(c_lo % 256)))
          end
        end
      end
    }
  \endgroup
  \def\pdfescapestrutfsixteen#1{\directlua{UTF16oct('\luaescapestring{#1}')}}
  % Escape PDF strings without converting
  \begingroup
    \directlua{
      function PDFescstr(str)
        for c in string.bytes(str) do
          if c <= 0x20 or c >= 0x80 or c == 0x28 or c == 0x29 or c == 0x5c then
            tex.sprint(-2,
              string.format(string.char(0x5c) .. string.char(0x25) .. '03o',
                            c))
          else
            tex.sprint(-2, string.char(c))
          end
        end
      end
    }
    % The -2 in the arguments here gives all the input to TeX catcode 12
    % (other) or 10 (space), preventing undefined control sequence errors. See
    % https://lists.gnu.org/archive/html/bug-texinfo/2019-08/msg00031.html
    %
  \endgroup
  \def\pdfescapestring#1{\directlua{PDFescstr('\luaescapestring{#1}')}}
  \ifnum\luatexversion>84
    % For LuaTeX >= 0.85
    \def\pdfdest{\pdfextension dest}
    \let\pdfoutput\outputmode
    \def\pdfliteral{\pdfextension literal}
    \def\pdfcatalog{\pdfextension catalog}
    \def\pdftexversion{\numexpr\pdffeedback version\relax}
    \let\pdfximage\saveimageresource
    \let\pdfrefximage\useimageresource
    \let\pdflastximage\lastsavedimageresourceindex
    \def\pdfendlink{\pdfextension endlink\relax}
    \def\pdfoutline{\pdfextension outline}
    \def\pdfstartlink{\pdfextension startlink}
    \def\pdffontattr{\pdfextension fontattr}
    \def\pdfobj{\pdfextension obj}
    \def\pdflastobj{\numexpr\pdffeedback lastobj\relax}
    \let\pdfpagewidth\pagewidth
    \let\pdfpageheight\pageheight
    \edef\pdfhorigin{\pdfvariable horigin}
    \edef\pdfvorigin{\pdfvariable vorigin}
  \fi
\fi

% when pdftex is run in dvi mode, \pdfoutput is defined (so \pdfoutput=1
% can be set).  So we test for \relax and 0 as well as being undefined.
\ifx\pdfoutput\thisisundefined
\else
  \ifx\pdfoutput\relax
  \else
    \ifcase\pdfoutput
    \else
      \pdftrue
    \fi
  \fi
\fi

\newif\ifpdforxetex
\pdforxetexfalse
\ifpdf
  \pdforxetextrue
\fi
\ifx\XeTeXrevision\thisisundefined\else
  \pdforxetextrue
\fi


% Output page labels information.
% See PDF reference v.1.7 p.594, section 8.3.1.
\ifpdf
\def\pagelabels{%
  \def\title{0 << /P (T-) /S /D >>}%
  \edef\roman{\the\romancount << /S /r >>}%
  \edef\arabic{\the\arabiccount << /S /D >>}%
  %
  % Page label ranges must be increasing.  Remove any duplicates.
  % (There is a slight chance of this being wrong if e.g. there is
  % a @contents but no @titlepage, etc.)
  %
  \ifnum\romancount=0 \def\roman{}\fi
  \ifnum\arabiccount=0 \def\title{}%
  \else
    \ifnum\romancount=\arabiccount \def\roman{}\fi
  \fi
  %
  \ifnum\romancount<\arabiccount
    \pdfcatalog{/PageLabels << /Nums [\title \roman \arabic ] >> }\relax
  \else
    \pdfcatalog{/PageLabels << /Nums [\title \arabic \roman ] >> }\relax
  \fi
}
\else
  \let\pagelabels\relax
\fi

\newcount\pagecount \pagecount=0
\newcount\romancount \romancount=0
\newcount\arabiccount \arabiccount=0
\ifpdf
  \let\ptxadvancepageno\advancepageno
  \def\advancepageno{%
    \ptxadvancepageno\global\advance\pagecount by 1
  }
\fi


% PDF uses PostScript string constants for the names of xref targets,
% for display in the outlines, and in other places.  Thus, we have to
% double any backslashes.  Otherwise, a name like "\node" will be
% interpreted as a newline (\n), followed by o, d, e.  Not good.
%
% See http://www.ntg.nl/pipermail/ntg-pdftex/2004-July/000654.html and
% related messages.  The final outcome is that it is up to the TeX user
% to double the backslashes and otherwise make the string valid, so
% that's what we do.  pdftex 1.30.0 (ca.2005) introduced a primitive to
% do this reliably, so we use it.

% #1 is a control sequence in which to do the replacements,
% which we \xdef.
\def\txiescapepdf#1{%
  \ifx\pdfescapestring\thisisundefined
    % No primitive available; should we give a warning or log?
    % Many times it won't matter.
    \xdef#1{#1}%
  \else
    % The expandable \pdfescapestring primitive escapes parentheses,
    % backslashes, and other special chars.
    \xdef#1{\pdfescapestring{#1}}%
  \fi
}
\def\txiescapepdfutfsixteen#1{%
  \ifx\pdfescapestrutfsixteen\thisisundefined
    % No UTF-16 converting macro available.
    \txiescapepdf{#1}%
  \else
    \xdef#1{\pdfescapestrutfsixteen{#1}}%
  \fi
}

\newhelp\nopdfimagehelp{Texinfo supports .png, .jpg, .jpeg, and .pdf images
with PDF output, and none of those formats could be found.  (.eps cannot
be supported due to the design of the PDF format; use regular TeX (DVI
output) for that.)}

\ifpdf
  %
  % Color manipulation macros using ideas from pdfcolor.tex,
  % except using rgb instead of cmyk; the latter is said to render as a
  % very dark gray on-screen and a very dark halftone in print, instead
  % of actual black. The dark red here is dark enough to print on paper as
  % nearly black, but still distinguishable for online viewing.  We use
  % black by default, though.
  \def\rgbDarkRed{0.50 0.09 0.12}
  \def\rgbBlack{0 0 0}
  %
  % rg sets the color for filling (usual text, etc.);
  % RG sets the color for stroking (thin rules, e.g., normal _'s).
  \def\pdfsetcolor#1{\pdfliteral{#1 rg  #1 RG}}
  %
  % Set color, and create a mark which defines \thiscolor accordingly,
  % so that \makeheadline knows which color to restore.
  \def\setcolor#1{%
    \xdef\currentcolordefs{\gdef\noexpand\thiscolor{#1}}%
    \domark
    \pdfsetcolor{#1}%
  }
  %
  \def\maincolor{\rgbBlack}
  \pdfsetcolor{\maincolor}
  \edef\thiscolor{\maincolor}
  \def\currentcolordefs{}
  %
  \def\makefootline{%
    \baselineskip24pt
    \line{\pdfsetcolor{\maincolor}\the\footline}%
  }
  %
  \def\makeheadline{%
    \vbox to 0pt{%
      \vskip-22.5pt
      \line{%
        \vbox to8.5pt{}%
        % Extract \thiscolor definition from the marks.
        \getcolormarks
        % Typeset the headline with \maincolor, then restore the color.
        \pdfsetcolor{\maincolor}\the\headline\pdfsetcolor{\thiscolor}%
      }%
      \vss
    }%
    \nointerlineskip
  }
  %
  %
  \pdfcatalog{/PageMode /UseOutlines}
  %
  % #1 is image name, #2 width (might be empty/whitespace), #3 height (ditto).
  \def\dopdfimage#1#2#3{%
    \def\pdfimagewidth{#2}\setbox0 = \hbox{\ignorespaces #2}%
    \def\pdfimageheight{#3}\setbox2 = \hbox{\ignorespaces #3}%
    %
    % pdftex (and the PDF format) support .pdf, .png, .jpg (among
    % others).  Let's try in that order, PDF first since if
    % someone has a scalable image, presumably better to use that than a
    % bitmap.
    \let\pdfimgext=\empty
    \begingroup
      \openin 1 #1.pdf \ifeof 1
        \openin 1 #1.PDF \ifeof 1
          \openin 1 #1.png \ifeof 1
            \openin 1 #1.jpg \ifeof 1
              \openin 1 #1.jpeg \ifeof 1
                \openin 1 #1.JPG \ifeof 1
                  \errhelp = \nopdfimagehelp
                  \errmessage{Could not find image file #1 for pdf}%
                \else \gdef\pdfimgext{JPG}%
                \fi
              \else \gdef\pdfimgext{jpeg}%
              \fi
            \else \gdef\pdfimgext{jpg}%
            \fi
          \else \gdef\pdfimgext{png}%
          \fi
        \else \gdef\pdfimgext{PDF}%
        \fi
      \else \gdef\pdfimgext{pdf}%
      \fi
      \closein 1
    \endgroup
    %
    % without \immediate, ancient pdftex seg faults when the same image is
    % included twice.  (Version 3.14159-pre-1.0-unofficial-20010704.)
    \ifnum\pdftexversion < 14
      \immediate\pdfimage
    \else
      \immediate\pdfximage
    \fi
      \ifdim \wd0 >0pt width \pdfimagewidth \fi
      \ifdim \wd2 >0pt height \pdfimageheight \fi
      \ifnum\pdftexversion<13
         #1.\pdfimgext
       \else
         {#1.\pdfimgext}%
       \fi
    \ifnum\pdftexversion < 14 \else
      \pdfrefximage \pdflastximage
    \fi}
  %
  \def\setpdfdestname#1{{%
    % We have to set dummies so commands such as @code, and characters
    % such as \, aren't expanded when present in a section title.
    \indexnofonts
    \makevalueexpandable
    \turnoffactive
    \iftxiuseunicodedestname
      \ifx \declaredencoding \latone
        % Pass through Latin-1 characters.
        % LuaTeX with byte wise I/O converts Latin-1 characters to Unicode.
      \else
        \ifx \declaredencoding \utfeight
          % Pass through Unicode characters.
        \else
          % Use ASCII approximations in destination names.
          \passthroughcharsfalse
        \fi
      \fi
    \else
      % Use ASCII approximations in destination names.
      \passthroughcharsfalse
    \fi
    \def\pdfdestname{#1}%
    \txiescapepdf\pdfdestname
  }}
  %
  \def\setpdfoutlinetext#1{{%
    \indexnofonts
    \makevalueexpandable
    \turnoffactive
    \ifx \declaredencoding \latone
      % The PDF format can use an extended form of Latin-1 in bookmark
      % strings.  See Appendix D of the PDF Reference, Sixth Edition, for
      % the "PDFDocEncoding".
      \passthroughcharstrue
      % Pass through Latin-1 characters.
      %   LuaTeX: Convert to Unicode
      %   pdfTeX: Use Latin-1 as PDFDocEncoding
      \def\pdfoutlinetext{#1}%
    \else
      \ifx \declaredencoding \utfeight
        \ifx\luatexversion\thisisundefined
          % For pdfTeX  with UTF-8.
          % TODO: the PDF format can use UTF-16 in bookmark strings,
          % but the code for this isn't done yet.
          % Use ASCII approximations.
          \passthroughcharsfalse
          \def\pdfoutlinetext{#1}%
        \else
          % For LuaTeX with UTF-8.
          % Pass through Unicode characters for title texts.
          \passthroughcharstrue
          \def\pdfoutlinetext{#1}%
        \fi
      \else
        % For non-Latin-1 or non-UTF-8 encodings.
        % Use ASCII approximations.
        \passthroughcharsfalse
        \def\pdfoutlinetext{#1}%
      \fi
    \fi
    % LuaTeX: Convert to UTF-16
    % pdfTeX: Use Latin-1 as PDFDocEncoding
    \txiescapepdfutfsixteen\pdfoutlinetext
  }}
  %
  \def\pdfmkdest#1{%
    \setpdfdestname{#1}%
    \safewhatsit{\pdfdest name{\pdfdestname} xyz}%
  }
  %
  % used to mark target names; must be expandable.
  \def\pdfmkpgn#1{#1}
  %
  % by default, use black for everything.
  \def\urlcolor{\rgbBlack}
  \def\linkcolor{\rgbBlack}
  \def\endlink{\setcolor{\maincolor}\pdfendlink}
  %
  % Adding outlines to PDF; macros for calculating structure of outlines
  % come from Petr Olsak
  \def\expnumber#1{\expandafter\ifx\csname#1\endcsname\relax 0%
    \else \csname#1\endcsname \fi}
  \def\advancenumber#1{\tempnum=\expnumber{#1}\relax
    \advance\tempnum by 1
    \expandafter\xdef\csname#1\endcsname{\the\tempnum}}
  %
  % #1 is the section text, which is what will be displayed in the
  % outline by the pdf viewer.  #2 is the pdf expression for the number
  % of subentries (or empty, for subsubsections).  #3 is the node text,
  % which might be empty if this toc entry had no corresponding node.
  % #4 is the page number
  %
  \def\dopdfoutline#1#2#3#4{%
    % Generate a link to the node text if that exists; else, use the
    % page number.  We could generate a destination for the section
    % text in the case where a section has no node, but it doesn't
    % seem worth the trouble, since most documents are normally structured.
    \setpdfoutlinetext{#1}
    \setpdfdestname{#3}
    \ifx\pdfdestname\empty
      \def\pdfdestname{#4}%
    \fi
    %
    \pdfoutline goto name{\pdfmkpgn{\pdfdestname}}#2{\pdfoutlinetext}%
  }
  %
  \def\pdfmakeoutlines{%
    \begingroup
      % Read toc silently, to get counts of subentries for \pdfoutline.
      \def\partentry##1##2##3##4{}% ignore parts in the outlines
      \def\numchapentry##1##2##3##4{%
	\def\thischapnum{##2}%
	\def\thissecnum{0}%
	\def\thissubsecnum{0}%
      }%
      \def\numsecentry##1##2##3##4{%
	\advancenumber{chap\thischapnum}%
	\def\thissecnum{##2}%
	\def\thissubsecnum{0}%
      }%
      \def\numsubsecentry##1##2##3##4{%
	\advancenumber{sec\thissecnum}%
	\def\thissubsecnum{##2}%
      }%
      \def\numsubsubsecentry##1##2##3##4{%
	\advancenumber{subsec\thissubsecnum}%
      }%
      \def\thischapnum{0}%
      \def\thissecnum{0}%
      \def\thissubsecnum{0}%
      %
      % use \def rather than \let here because we redefine \chapentry et
      % al. a second time, below.
      \def\appentry{\numchapentry}%
      \def\appsecentry{\numsecentry}%
      \def\appsubsecentry{\numsubsecentry}%
      \def\appsubsubsecentry{\numsubsubsecentry}%
      \def\unnchapentry{\numchapentry}%
      \def\unnsecentry{\numsecentry}%
      \def\unnsubsecentry{\numsubsecentry}%
      \def\unnsubsubsecentry{\numsubsubsecentry}%
      \readdatafile{toc}%
      %
      % Read toc second time, this time actually producing the outlines.
      % The `-' means take the \expnumber as the absolute number of
      % subentries, which we calculated on our first read of the .toc above.
      %
      % We use the node names as the destinations.
      %
      % Currently we prefix the section name with the section number
      % for chapter and appendix headings only in order to avoid too much
      % horizontal space being required in the PDF viewer.
      \def\numchapentry##1##2##3##4{%
        \dopdfoutline{##2 ##1}{count-\expnumber{chap##2}}{##3}{##4}}%
      \def\unnchapentry##1##2##3##4{%
        \dopdfoutline{##1}{count-\expnumber{chap##2}}{##3}{##4}}%
      \def\numsecentry##1##2##3##4{%
        \dopdfoutline{##1}{count-\expnumber{sec##2}}{##3}{##4}}%
      \def\numsubsecentry##1##2##3##4{%
        \dopdfoutline{##1}{count-\expnumber{subsec##2}}{##3}{##4}}%
      \def\numsubsubsecentry##1##2##3##4{% count is always zero
        \dopdfoutline{##1}{}{##3}{##4}}%
      %
      % PDF outlines are displayed using system fonts, instead of
      % document fonts.  Therefore we cannot use special characters,
      % since the encoding is unknown.  For example, the eogonek from
      % Latin 2 (0xea) gets translated to a | character.  Info from
      % Staszek Wawrykiewicz, 19 Jan 2004 04:09:24 +0100.
      %
      % TODO this right, we have to translate 8-bit characters to
      % their "best" equivalent, based on the @documentencoding.  Too
      % much work for too little return.  Just use the ASCII equivalents
      % we use for the index sort strings.
      %
      \indexnofonts
      \setupdatafile
      % We can have normal brace characters in the PDF outlines, unlike
      % Texinfo index files.  So set that up.
      \def\{{\lbracecharliteral}%
      \def\}{\rbracecharliteral}%
      \catcode`\\=\active \otherbackslash
      \input \tocreadfilename
    \endgroup
  }
  {\catcode`[=1 \catcode`]=2
   \catcode`{=\other \catcode`}=\other
   \gdef\lbracecharliteral[{]%
   \gdef\rbracecharliteral[}]%
  ]
  %
  \def\skipspaces#1{\def\PP{#1}\def\D{|}%
    \ifx\PP\D\let\nextsp\relax
    \else\let\nextsp\skipspaces
      \addtokens{\filename}{\PP}%
      \advance\filenamelength by 1
    \fi
    \nextsp}
  \def\getfilename#1{%
    \filenamelength=0
    % If we don't expand the argument now, \skipspaces will get
    % snagged on things like "@value{foo}".
    \edef\temp{#1}%
    \expandafter\skipspaces\temp|\relax
  }
  \ifnum\pdftexversion < 14
    \let \startlink \pdfannotlink
  \else
    \let \startlink \pdfstartlink
  \fi
  % make a live url in pdf output.
  \def\pdfurl#1{%
    \begingroup
      % it seems we really need yet another set of dummies; have not
      % tried to figure out what each command should do in the context
      % of @url.  for now, just make @/ a no-op, that's the only one
      % people have actually reported a problem with.
      %
      \normalturnoffactive
      \def\@{@}%
      \let\/=\empty
      \makevalueexpandable
      % do we want to go so far as to use \indexnofonts instead of just
      % special-casing \var here?
      \def\var##1{##1}%
      %
      \leavevmode\setcolor{\urlcolor}%
      \startlink attr{/Border [0 0 0]}%
        user{/Subtype /Link /A << /S /URI /URI (#1) >>}%
    \endgroup}
  % \pdfgettoks - Surround page numbers in #1 with @pdflink.  #1 may
  % be a simple number, or a list of numbers in the case of an index
  % entry.
  \def\pdfgettoks#1.{\setbox\boxA=\hbox{\toksA={#1.}\toksB={}\maketoks}}
  \def\addtokens#1#2{\edef\addtoks{\noexpand#1={\the#1#2}}\addtoks}
  \def\adn#1{\addtokens{\toksC}{#1}\global\countA=1\let\next=\maketoks}
  \def\poptoks#1#2|ENDTOKS|{\let\first=#1\toksD={#1}\toksA={#2}}
  \def\maketoks{%
    \expandafter\poptoks\the\toksA|ENDTOKS|\relax
    \ifx\first0\adn0
    \else\ifx\first1\adn1 \else\ifx\first2\adn2 \else\ifx\first3\adn3
    \else\ifx\first4\adn4 \else\ifx\first5\adn5 \else\ifx\first6\adn6
    \else\ifx\first7\adn7 \else\ifx\first8\adn8 \else\ifx\first9\adn9
    \else
      \ifnum0=\countA\else\makelink\fi
      \ifx\first.\let\next=\done\else
        \let\next=\maketoks
        \addtokens{\toksB}{\the\toksD}
        \ifx\first,\addtokens{\toksB}{\space}\fi
      \fi
    \fi\fi\fi\fi\fi\fi\fi\fi\fi\fi
    \next}
  \def\makelink{\addtokens{\toksB}%
    {\noexpand\pdflink{\the\toksC}}\toksC={}\global\countA=0}
  \def\pdflink#1{%
    \startlink attr{/Border [0 0 0]} goto name{\pdfmkpgn{#1}}
    \setcolor{\linkcolor}#1\endlink}
  \def\done{\edef\st{\global\noexpand\toksA={\the\toksB}}\st}
\else
  % non-pdf mode
  \let\pdfmkdest = \gobble
  \let\pdfurl = \gobble
  \let\endlink = \relax
  \let\setcolor = \gobble
  \let\pdfsetcolor = \gobble
  \let\pdfmakeoutlines = \relax
\fi  % \ifx\pdfoutput

%
% For XeTeX
%
\ifx\XeTeXrevision\thisisundefined
\else
  %
  % XeTeX version check
  %
  \ifnum\strcmp{\the\XeTeXversion\XeTeXrevision}{0.99996}>-1
    % TeX Live 2016 contains XeTeX 0.99996 and xdvipdfmx 20160307.
    % It can use the `dvipdfmx:config' special (from TeX Live SVN r40941).
    % For avoiding PDF destination name replacement, we use this special
    % instead of xdvipdfmx's command line option `-C 0x0010'.
    \special{dvipdfmx:config C 0x0010}
    % XeTeX 0.99995+ comes with xdvipdfmx 20160307+.
    % It can handle Unicode destination names for PDF.
    \txiuseunicodedestnametrue
  \else
    % XeTeX < 0.99996 (TeX Live < 2016) cannot use the
    % `dvipdfmx:config' special.
    % So for avoiding PDF destination name replacement,
    % xdvipdfmx's command line option `-C 0x0010' is necessary.
    %
    % XeTeX < 0.99995 can not handle Unicode destination names for PDF
    % because xdvipdfmx 20150315 has a UTF-16 conversion issue.
    % It is fixed by xdvipdfmx 20160106 (TeX Live SVN r39753).
    \txiuseunicodedestnamefalse
  \fi
  %
  % Color support
  %
  \def\rgbDarkRed{0.50 0.09 0.12}
  \def\rgbBlack{0 0 0}
  %
  \def\pdfsetcolor#1{\special{pdf:scolor [#1]}}
  %
  % Set color, and create a mark which defines \thiscolor accordingly,
  % so that \makeheadline knows which color to restore.
  \def\setcolor#1{%
    \xdef\currentcolordefs{\gdef\noexpand\thiscolor{#1}}%
    \domark
    \pdfsetcolor{#1}%
  }
  %
  \def\maincolor{\rgbBlack}
  \pdfsetcolor{\maincolor}
  \edef\thiscolor{\maincolor}
  \def\currentcolordefs{}
  %
  \def\makefootline{%
    \baselineskip24pt
    \line{\pdfsetcolor{\maincolor}\the\footline}%
  }
  %
  \def\makeheadline{%
    \vbox to 0pt{%
      \vskip-22.5pt
      \line{%
        \vbox to8.5pt{}%
        % Extract \thiscolor definition from the marks.
        \getcolormarks
        % Typeset the headline with \maincolor, then restore the color.
        \pdfsetcolor{\maincolor}\the\headline\pdfsetcolor{\thiscolor}%
      }%
      \vss
    }%
    \nointerlineskip
  }
  %
  % PDF outline support
  %
  % Emulate pdfTeX primitive
  \def\pdfdest name#1 xyz{%
    \special{pdf:dest (#1) [@thispage /XYZ @xpos @ypos null]}%
  }
  %
  \def\setpdfdestname#1{{%
    % We have to set dummies so commands such as @code, and characters
    % such as \, aren't expanded when present in a section title.
    \indexnofonts
    \makevalueexpandable
    \turnoffactive
    \iftxiuseunicodedestname
      % Pass through Unicode characters.
    \else
      % Use ASCII approximations in destination names.
      \passthroughcharsfalse
    \fi
    \def\pdfdestname{#1}%
    \txiescapepdf\pdfdestname
  }}
  %
  \def\setpdfoutlinetext#1{{%
    \turnoffactive
    % Always use Unicode characters in title texts.
    \def\pdfoutlinetext{#1}%
    % For XeTeX, xdvipdfmx converts to UTF-16.
    % So we do not convert.
    \txiescapepdf\pdfoutlinetext
  }}
  %
  \def\pdfmkdest#1{%
    \setpdfdestname{#1}%
    \safewhatsit{\pdfdest name{\pdfdestname} xyz}%
  }
  %
  % by default, use black for everything.
  \def\urlcolor{\rgbBlack}
  \def\linkcolor{\rgbBlack}
  \def\endlink{\setcolor{\maincolor}\pdfendlink}
  %
  \def\dopdfoutline#1#2#3#4{%
    \setpdfoutlinetext{#1}
    \setpdfdestname{#3}
    \ifx\pdfdestname\empty
      \def\pdfdestname{#4}%
    \fi
    %
    \special{pdf:out [-] #2 << /Title (\pdfoutlinetext) /A
      << /S /GoTo /D (\pdfdestname) >> >> }%
  }
  %
  \def\pdfmakeoutlines{%
    \begingroup
      %
      % For XeTeX, counts of subentries are not necessary.
      % Therefore, we read toc only once.
      %
      % We use node names as destinations.
      %
      % Currently we prefix the section name with the section number
      % for chapter and appendix headings only in order to avoid too much
      % horizontal space being required in the PDF viewer.
      \def\partentry##1##2##3##4{}% ignore parts in the outlines
      \def\numchapentry##1##2##3##4{%
        \dopdfoutline{##2 ##1}{1}{##3}{##4}}%
      \def\numsecentry##1##2##3##4{%
        \dopdfoutline{##1}{2}{##3}{##4}}%
      \def\numsubsecentry##1##2##3##4{%
        \dopdfoutline{##1}{3}{##3}{##4}}%
      \def\numsubsubsecentry##1##2##3##4{%
        \dopdfoutline{##1}{4}{##3}{##4}}%
      %
      \let\appentry\numchapentry%
      \let\appsecentry\numsecentry%
      \let\appsubsecentry\numsubsecentry%
      \let\appsubsubsecentry\numsubsubsecentry%
      \def\unnchapentry##1##2##3##4{%
        \dopdfoutline{##1}{1}{##3}{##4}}%
      \let\unnsecentry\numsecentry%
      \let\unnsubsecentry\numsubsecentry%
      \let\unnsubsubsecentry\numsubsubsecentry%
      %
      % For XeTeX, xdvipdfmx converts strings to UTF-16.
      % Therefore, the encoding and the language may not be considered.
      %
      \indexnofonts
      \setupdatafile
      % We can have normal brace characters in the PDF outlines, unlike
      % Texinfo index files.  So set that up.
      \def\{{\lbracecharliteral}%
      \def\}{\rbracecharliteral}%
      \catcode`\\=\active \otherbackslash
      \input \tocreadfilename
    \endgroup
  }
  {\catcode`[=1 \catcode`]=2
   \catcode`{=\other \catcode`}=\other
   \gdef\lbracecharliteral[{]%
   \gdef\rbracecharliteral[}]%
  ]

  \special{pdf:docview << /PageMode /UseOutlines >> }
  % ``\special{pdf:tounicode ...}'' is not necessary
  % because xdvipdfmx converts strings from UTF-8 to UTF-16 without it.
  % However, due to a UTF-16 conversion issue of xdvipdfmx 20150315,
  % ``\special{pdf:dest ...}'' cannot handle non-ASCII strings.
  % It is fixed by xdvipdfmx 20160106 (TeX Live SVN r39753).
%
  \def\skipspaces#1{\def\PP{#1}\def\D{|}%
    \ifx\PP\D\let\nextsp\relax
    \else\let\nextsp\skipspaces
      \addtokens{\filename}{\PP}%
      \advance\filenamelength by 1
    \fi
    \nextsp}
  \def\getfilename#1{%
    \filenamelength=0
    % If we don't expand the argument now, \skipspaces will get
    % snagged on things like "@value{foo}".
    \edef\temp{#1}%
    \expandafter\skipspaces\temp|\relax
  }
  % make a live url in pdf output.
  \def\pdfurl#1{%
    \begingroup
      % it seems we really need yet another set of dummies; have not
      % tried to figure out what each command should do in the context
      % of @url.  for now, just make @/ a no-op, that's the only one
      % people have actually reported a problem with.
      %
      \normalturnoffactive
      \def\@{@}%
      \let\/=\empty
      \makevalueexpandable
      % do we want to go so far as to use \indexnofonts instead of just
      % special-casing \var here?
      \def\var##1{##1}%
      %
      \leavevmode\setcolor{\urlcolor}%
      \special{pdf:bann << /Border [0 0 0]
        /Subtype /Link /A << /S /URI /URI (#1) >> >>}%
    \endgroup}
  \def\endlink{\setcolor{\maincolor}\special{pdf:eann}}
  \def\pdfgettoks#1.{\setbox\boxA=\hbox{\toksA={#1.}\toksB={}\maketoks}}
  \def\addtokens#1#2{\edef\addtoks{\noexpand#1={\the#1#2}}\addtoks}
  \def\adn#1{\addtokens{\toksC}{#1}\global\countA=1\let\next=\maketoks}
  \def\poptoks#1#2|ENDTOKS|{\let\first=#1\toksD={#1}\toksA={#2}}
  \def\maketoks{%
    \expandafter\poptoks\the\toksA|ENDTOKS|\relax
    \ifx\first0\adn0
    \else\ifx\first1\adn1 \else\ifx\first2\adn2 \else\ifx\first3\adn3
    \else\ifx\first4\adn4 \else\ifx\first5\adn5 \else\ifx\first6\adn6
    \else\ifx\first7\adn7 \else\ifx\first8\adn8 \else\ifx\first9\adn9
    \else
      \ifnum0=\countA\else\makelink\fi
      \ifx\first.\let\next=\done\else
        \let\next=\maketoks
        \addtokens{\toksB}{\the\toksD}
        \ifx\first,\addtokens{\toksB}{\space}\fi
      \fi
    \fi\fi\fi\fi\fi\fi\fi\fi\fi\fi
    \next}
  \def\makelink{\addtokens{\toksB}%
    {\noexpand\pdflink{\the\toksC}}\toksC={}\global\countA=0}
  \def\pdflink#1{%
    \special{pdf:bann << /Border [0 0 0]
      /Type /Annot /Subtype /Link /A << /S /GoTo /D (#1) >> >>}%
    \setcolor{\linkcolor}#1\endlink}
  \def\done{\edef\st{\global\noexpand\toksA={\the\toksB}}\st}
%
  %
  % @image support
  %
  % #1 is image name, #2 width (might be empty/whitespace), #3 height (ditto).
  \def\doxeteximage#1#2#3{%
    \def\xeteximagewidth{#2}\setbox0 = \hbox{\ignorespaces #2}%
    \def\xeteximageheight{#3}\setbox2 = \hbox{\ignorespaces #3}%
    %
    % XeTeX (and the PDF format) supports .pdf, .png, .jpg (among
    % others).  Let's try in that order, PDF first since if
    % someone has a scalable image, presumably better to use that than a
    % bitmap.
    \let\xeteximgext=\empty
    \begingroup
      \openin 1 #1.pdf \ifeof 1
        \openin 1 #1.PDF \ifeof 1
          \openin 1 #1.png \ifeof 1
            \openin 1 #1.jpg \ifeof 1
              \openin 1 #1.jpeg \ifeof 1
                \openin 1 #1.JPG \ifeof 1
                  \errmessage{Could not find image file #1 for XeTeX}%
                \else \gdef\xeteximgext{JPG}%
                \fi
              \else \gdef\xeteximgext{jpeg}%
              \fi
            \else \gdef\xeteximgext{jpg}%
            \fi
          \else \gdef\xeteximgext{png}%
          \fi
        \else \gdef\xeteximgext{PDF}%
        \fi
      \else \gdef\xeteximgext{pdf}%
      \fi
      \closein 1
    \endgroup
    %
    \def\xetexpdfext{pdf}%
    \ifx\xeteximgext\xetexpdfext
      \XeTeXpdffile "#1".\xeteximgext ""
    \else
      \def\xetexpdfext{PDF}%
      \ifx\xeteximgext\xetexpdfext
        \XeTeXpdffile "#1".\xeteximgext ""
      \else
        \XeTeXpicfile "#1".\xeteximgext ""
      \fi
    \fi
    \ifdim \wd0 >0pt width \xeteximagewidth \fi
    \ifdim \wd2 >0pt height \xeteximageheight \fi \relax
  }
\fi


%
\message{fonts,}

% Set the baselineskip to #1, and the lineskip and strut size
% correspondingly.  There is no deep meaning behind these magic numbers
% used as factors; they just match (closely enough) what Knuth defined.
%
\def\lineskipfactor{.08333}
\def\strutheightpercent{.70833}
\def\strutdepthpercent {.29167}
%
% can get a sort of poor man's double spacing by redefining this.
\def\baselinefactor{1}
%
\newdimen\textleading
\def\setleading#1{%
  \dimen0 = #1\relax
  \normalbaselineskip = \baselinefactor\dimen0
  \normallineskip = \lineskipfactor\normalbaselineskip
  \normalbaselines
  \setbox\strutbox =\hbox{%
    \vrule width0pt height\strutheightpercent\baselineskip
                    depth \strutdepthpercent \baselineskip
  }%
}

% PDF CMaps.  See also LaTeX's t1.cmap.
%
% do nothing with this by default.
\expandafter\let\csname cmapOT1\endcsname\gobble
\expandafter\let\csname cmapOT1IT\endcsname\gobble
\expandafter\let\csname cmapOT1TT\endcsname\gobble

% if we are producing pdf, and we have \pdffontattr, then define cmaps.
% (\pdffontattr was introduced many years ago, but people still run
% older pdftex's; it's easy to conditionalize, so we do.)
\ifpdf \ifx\pdffontattr\thisisundefined \else
  \begingroup
    \catcode`\^^M=\active \def^^M{^^J}% Output line endings as the ^^J char.
    \catcode`\%=12 \immediate\pdfobj stream {%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (TeX-OT1-0)
%%Title: (TeX-OT1-0 TeX OT1 0)
%%Version: 1.000
%%EndComments
/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo
<< /Registry (TeX)
/Ordering (OT1)
/Supplement 0
>> def
/CMapName /TeX-OT1-0 def
/CMapType 2 def
1 begincodespacerange
<00> <7F>
endcodespacerange
8 beginbfrange
<00> <01> <0393>
<09> <0A> <03A8>
<23> <26> <0023>
<28> <3B> <0028>
<3F> <5B> <003F>
<5D> <5E> <005D>
<61> <7A> <0061>
<7B> <7C> <2013>
endbfrange
40 beginbfchar
<02> <0398>
<03> <039B>
<04> <039E>
<05> <03A0>
<06> <03A3>
<07> <03D2>
<08> <03A6>
<0B> <00660066>
<0C> <00660069>
<0D> <0066006C>
<0E> <006600660069>
<0F> <00660066006C>
<10> <0131>
<11> <0237>
<12> <0060>
<13> <00B4>
<14> <02C7>
<15> <02D8>
<16> <00AF>
<17> <02DA>
<18> <00B8>
<19> <00DF>
<1A> <00E6>
<1B> <0153>
<1C> <00F8>
<1D> <00C6>
<1E> <0152>
<1F> <00D8>
<21> <0021>
<22> <201D>
<27> <2019>
<3C> <00A1>
<3D> <003D>
<3E> <00BF>
<5C> <201C>
<5F> <02D9>
<60> <2018>
<7D> <02DD>
<7E> <007E>
<7F> <00A8>
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end
%%EndResource
%%EOF
    }\endgroup
  \expandafter\edef\csname cmapOT1\endcsname#1{%
    \pdffontattr#1{/ToUnicode \the\pdflastobj\space 0 R}%
  }%
%
% \cmapOT1IT
  \begingroup
    \catcode`\^^M=\active \def^^M{^^J}% Output line endings as the ^^J char.
    \catcode`\%=12 \immediate\pdfobj stream {%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (TeX-OT1IT-0)
%%Title: (TeX-OT1IT-0 TeX OT1IT 0)
%%Version: 1.000
%%EndComments
/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo
<< /Registry (TeX)
/Ordering (OT1IT)
/Supplement 0
>> def
/CMapName /TeX-OT1IT-0 def
/CMapType 2 def
1 begincodespacerange
<00> <7F>
endcodespacerange
8 beginbfrange
<00> <01> <0393>
<09> <0A> <03A8>
<25> <26> <0025>
<28> <3B> <0028>
<3F> <5B> <003F>
<5D> <5E> <005D>
<61> <7A> <0061>
<7B> <7C> <2013>
endbfrange
42 beginbfchar
<02> <0398>
<03> <039B>
<04> <039E>
<05> <03A0>
<06> <03A3>
<07> <03D2>
<08> <03A6>
<0B> <00660066>
<0C> <00660069>
<0D> <0066006C>
<0E> <006600660069>
<0F> <00660066006C>
<10> <0131>
<11> <0237>
<12> <0060>
<13> <00B4>
<14> <02C7>
<15> <02D8>
<16> <00AF>
<17> <02DA>
<18> <00B8>
<19> <00DF>
<1A> <00E6>
<1B> <0153>
<1C> <00F8>
<1D> <00C6>
<1E> <0152>
<1F> <00D8>
<21> <0021>
<22> <201D>
<23> <0023>
<24> <00A3>
<27> <2019>
<3C> <00A1>
<3D> <003D>
<3E> <00BF>
<5C> <201C>
<5F> <02D9>
<60> <2018>
<7D> <02DD>
<7E> <007E>
<7F> <00A8>
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end
%%EndResource
%%EOF
    }\endgroup
  \expandafter\edef\csname cmapOT1IT\endcsname#1{%
    \pdffontattr#1{/ToUnicode \the\pdflastobj\space 0 R}%
  }%
%
% \cmapOT1TT
  \begingroup
    \catcode`\^^M=\active \def^^M{^^J}% Output line endings as the ^^J char.
    \catcode`\%=12 \immediate\pdfobj stream {%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (TeX-OT1TT-0)
%%Title: (TeX-OT1TT-0 TeX OT1TT 0)
%%Version: 1.000
%%EndComments
/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo
<< /Registry (TeX)
/Ordering (OT1TT)
/Supplement 0
>> def
/CMapName /TeX-OT1TT-0 def
/CMapType 2 def
1 begincodespacerange
<00> <7F>
endcodespacerange
5 beginbfrange
<00> <01> <0393>
<09> <0A> <03A8>
<21> <26> <0021>
<28> <5F> <0028>
<61> <7E> <0061>
endbfrange
32 beginbfchar
<02> <0398>
<03> <039B>
<04> <039E>
<05> <03A0>
<06> <03A3>
<07> <03D2>
<08> <03A6>
<0B> <2191>
<0C> <2193>
<0D> <0027>
<0E> <00A1>
<0F> <00BF>
<10> <0131>
<11> <0237>
<12> <0060>
<13> <00B4>
<14> <02C7>
<15> <02D8>
<16> <00AF>
<17> <02DA>
<18> <00B8>
<19> <00DF>
<1A> <00E6>
<1B> <0153>
<1C> <00F8>
<1D> <00C6>
<1E> <0152>
<1F> <00D8>
<20> <2423>
<27> <2019>
<60> <2018>
<7F> <00A8>
endbfchar
endcmap
CMapName currentdict /CMap defineresource pop
end
end
%%EndResource
%%EOF
    }\endgroup
  \expandafter\edef\csname cmapOT1TT\endcsname#1{%
    \pdffontattr#1{/ToUnicode \the\pdflastobj\space 0 R}%
  }%
\fi\fi


% Set the font macro #1 to the font named \fontprefix#2.
% #3 is the font's design size, #4 is a scale factor, #5 is the CMap
% encoding (only OT1, OT1IT and OT1TT are allowed, or empty to omit).
% Example:
% #1 = \textrm
% #2 = \rmshape
% #3 = 10
% #4 = \mainmagstep
% #5 = OT1
%
\def\setfont#1#2#3#4#5{%
  \font#1=\fontprefix#2#3 scaled #4
  \csname cmap#5\endcsname#1%
}
% This is what gets called when #5 of \setfont is empty.
\let\cmap\gobble
%
% (end of cmaps)

% Use cm as the default font prefix.
% To specify the font prefix, you must define \fontprefix
% before you read in texinfo.tex.
\ifx\fontprefix\thisisundefined
\def\fontprefix{cm}
\fi
% Support font families that don't use the same naming scheme as CM.
\def\rmshape{r}
\def\rmbshape{bx}               % where the normal face is bold
\def\bfshape{b}
\def\bxshape{bx}
\def\ttshape{tt}
\def\ttbshape{tt}
\def\ttslshape{sltt}
\def\itshape{ti}
\def\itbshape{bxti}
\def\slshape{sl}
\def\slbshape{bxsl}
\def\sfshape{ss}
\def\sfbshape{ss}
\def\scshape{csc}
\def\scbshape{csc}

% Definitions for a main text size of 11pt.  (The default in Texinfo.)
%
\def\definetextfontsizexi{%
% Text fonts (11.2pt, magstep1).
\def\textnominalsize{11pt}
\edef\mainmagstep{\magstephalf}
\setfont\textrm\rmshape{10}{\mainmagstep}{OT1}
\setfont\texttt\ttshape{10}{\mainmagstep}{OT1TT}
\setfont\textbf\bfshape{10}{\mainmagstep}{OT1}
\setfont\textit\itshape{10}{\mainmagstep}{OT1IT}
\setfont\textsl\slshape{10}{\mainmagstep}{OT1}
\setfont\textsf\sfshape{10}{\mainmagstep}{OT1}
\setfont\textsc\scshape{10}{\mainmagstep}{OT1}
\setfont\textttsl\ttslshape{10}{\mainmagstep}{OT1TT}
\font\texti=cmmi10 scaled \mainmagstep
\font\textsy=cmsy10 scaled \mainmagstep
\def\textecsize{1095}

% A few fonts for @defun names and args.
\setfont\defbf\bfshape{10}{\magstep1}{OT1}
\setfont\deftt\ttshape{10}{\magstep1}{OT1TT}
\setfont\defsl\slshape{10}{\magstep1}{OT1}
\setfont\defttsl\ttslshape{10}{\magstep1}{OT1TT}
\def\df{\let\ttfont=\deftt \let\bffont = \defbf
\let\ttslfont=\defttsl \let\slfont=\defsl \bf}

% Fonts for indices, footnotes, small examples (9pt).
\def\smallnominalsize{9pt}
\setfont\smallrm\rmshape{9}{1000}{OT1}
\setfont\smalltt\ttshape{9}{1000}{OT1TT}
\setfont\smallbf\bfshape{10}{900}{OT1}
\setfont\smallit\itshape{9}{1000}{OT1IT}
\setfont\smallsl\slshape{9}{1000}{OT1}
\setfont\smallsf\sfshape{9}{1000}{OT1}
\setfont\smallsc\scshape{10}{900}{OT1}
\setfont\smallttsl\ttslshape{10}{900}{OT1TT}
\font\smalli=cmmi9
\font\smallsy=cmsy9
\def\smallecsize{0900}

% Fonts for small examples (8pt).
\def\smallernominalsize{8pt}
\setfont\smallerrm\rmshape{8}{1000}{OT1}
\setfont\smallertt\ttshape{8}{1000}{OT1TT}
\setfont\smallerbf\bfshape{10}{800}{OT1}
\setfont\smallerit\itshape{8}{1000}{OT1IT}
\setfont\smallersl\slshape{8}{1000}{OT1}
\setfont\smallersf\sfshape{8}{1000}{OT1}
\setfont\smallersc\scshape{10}{800}{OT1}
\setfont\smallerttsl\ttslshape{10}{800}{OT1TT}
\font\smalleri=cmmi8
\font\smallersy=cmsy8
\def\smallerecsize{0800}

% Fonts for math mode superscripts (7pt).
\def\sevennominalsize{7pt}
\setfont\sevenrm\rmshape{7}{1000}{OT1}
\setfont\seventt\ttshape{10}{700}{OT1TT}
\setfont\sevenbf\bfshape{10}{700}{OT1}
\setfont\sevenit\itshape{7}{1000}{OT1IT}
\setfont\sevensl\slshape{10}{700}{OT1}
\setfont\sevensf\sfshape{10}{700}{OT1}
\setfont\sevensc\scshape{10}{700}{OT1}
\setfont\seventtsl\ttslshape{10}{700}{OT1TT}
\font\seveni=cmmi7
\font\sevensy=cmsy7
\def\sevenecsize{0700}

% Fonts for title page (20.4pt):
\def\titlenominalsize{20pt}
\setfont\titlerm\rmbshape{12}{\magstep3}{OT1}
\setfont\titleit\itbshape{10}{\magstep4}{OT1IT}
\setfont\titlesl\slbshape{10}{\magstep4}{OT1}
\setfont\titlett\ttbshape{12}{\magstep3}{OT1TT}
\setfont\titlettsl\ttslshape{10}{\magstep4}{OT1TT}
\setfont\titlesf\sfbshape{17}{\magstep1}{OT1}
\let\titlebf=\titlerm
\setfont\titlesc\scbshape{10}{\magstep4}{OT1}
\font\titlei=cmmi12 scaled \magstep3
\font\titlesy=cmsy10 scaled \magstep4
\def\titleecsize{2074}

% Chapter (and unnumbered) fonts (17.28pt).
\def\chapnominalsize{17pt}
\setfont\chaprm\rmbshape{12}{\magstep2}{OT1}
\setfont\chapit\itbshape{10}{\magstep3}{OT1IT}
\setfont\chapsl\slbshape{10}{\magstep3}{OT1}
\setfont\chaptt\ttbshape{12}{\magstep2}{OT1TT}
\setfont\chapttsl\ttslshape{10}{\magstep3}{OT1TT}
\setfont\chapsf\sfbshape{17}{1000}{OT1}
\let\chapbf=\chaprm
\setfont\chapsc\scbshape{10}{\magstep3}{OT1}
\font\chapi=cmmi12 scaled \magstep2
\font\chapsy=cmsy10 scaled \magstep3
\def\chapecsize{1728}

% Section fonts (14.4pt).
\def\secnominalsize{14pt}
\setfont\secrm\rmbshape{12}{\magstep1}{OT1}
\setfont\secrmnotbold\rmshape{12}{\magstep1}{OT1}
\setfont\secit\itbshape{10}{\magstep2}{OT1IT}
\setfont\secsl\slbshape{10}{\magstep2}{OT1}
\setfont\sectt\ttbshape{12}{\magstep1}{OT1TT}
\setfont\secttsl\ttslshape{10}{\magstep2}{OT1TT}
\setfont\secsf\sfbshape{12}{\magstep1}{OT1}
\let\secbf\secrm
\setfont\secsc\scbshape{10}{\magstep2}{OT1}
\font\seci=cmmi12 scaled \magstep1
\font\secsy=cmsy10 scaled \magstep2
\def\sececsize{1440}

% Subsection fonts (13.15pt).
\def\ssecnominalsize{13pt}
\setfont\ssecrm\rmbshape{12}{\magstephalf}{OT1}
\setfont\ssecit\itbshape{10}{1315}{OT1IT}
\setfont\ssecsl\slbshape{10}{1315}{OT1}
\setfont\ssectt\ttbshape{12}{\magstephalf}{OT1TT}
\setfont\ssecttsl\ttslshape{10}{1315}{OT1TT}
\setfont\ssecsf\sfbshape{12}{\magstephalf}{OT1}
\let\ssecbf\ssecrm
\setfont\ssecsc\scbshape{10}{1315}{OT1}
\font\sseci=cmmi12 scaled \magstephalf
\font\ssecsy=cmsy10 scaled 1315
\def\ssececsize{1200}

% Reduced fonts for @acronym in text (10pt).
\def\reducednominalsize{10pt}
\setfont\reducedrm\rmshape{10}{1000}{OT1}
\setfont\reducedtt\ttshape{10}{1000}{OT1TT}
\setfont\reducedbf\bfshape{10}{1000}{OT1}
\setfont\reducedit\itshape{10}{1000}{OT1IT}
\setfont\reducedsl\slshape{10}{1000}{OT1}
\setfont\reducedsf\sfshape{10}{1000}{OT1}
\setfont\reducedsc\scshape{10}{1000}{OT1}
\setfont\reducedttsl\ttslshape{10}{1000}{OT1TT}
\font\reducedi=cmmi10
\font\reducedsy=cmsy10
\def\reducedecsize{1000}

\textleading = 13.2pt % line spacing for 11pt CM
\textfonts            % reset the current fonts
\rm
} % end of 11pt text font size definitions, \definetextfontsizexi


% Definitions to make the main text be 10pt Computer Modern, with
% section, chapter, etc., sizes following suit.  This is for the GNU
% Press printing of the Emacs 22 manual.  Maybe other manuals in the
% future.  Used with @smallbook, which sets the leading to 12pt.
%
\def\definetextfontsizex{%
% Text fonts (10pt).
\def\textnominalsize{10pt}
\edef\mainmagstep{1000}
\setfont\textrm\rmshape{10}{\mainmagstep}{OT1}
\setfont\texttt\ttshape{10}{\mainmagstep}{OT1TT}
\setfont\textbf\bfshape{10}{\mainmagstep}{OT1}
\setfont\textit\itshape{10}{\mainmagstep}{OT1IT}
\setfont\textsl\slshape{10}{\mainmagstep}{OT1}
\setfont\textsf\sfshape{10}{\mainmagstep}{OT1}
\setfont\textsc\scshape{10}{\mainmagstep}{OT1}
\setfont\textttsl\ttslshape{10}{\mainmagstep}{OT1TT}
\font\texti=cmmi10 scaled \mainmagstep
\font\textsy=cmsy10 scaled \mainmagstep
\def\textecsize{1000}

% A few fonts for @defun names and args.
\setfont\defbf\bfshape{10}{\magstephalf}{OT1}
\setfont\deftt\ttshape{10}{\magstephalf}{OT1TT}
\setfont\defsl\slshape{10}{\magstephalf}{OT1}
\setfont\defttsl\ttslshape{10}{\magstephalf}{OT1TT}
\def\df{\let\ttfont=\deftt \let\bffont = \defbf
\let\slfont=\defsl \let\ttslfont=\defttsl \bf}

% Fonts for indices, footnotes, small examples (9pt).
\def\smallnominalsize{9pt}
\setfont\smallrm\rmshape{9}{1000}{OT1}
\setfont\smalltt\ttshape{9}{1000}{OT1TT}
\setfont\smallbf\bfshape{10}{900}{OT1}
\setfont\smallit\itshape{9}{1000}{OT1IT}
\setfont\smallsl\slshape{9}{1000}{OT1}
\setfont\smallsf\sfshape{9}{1000}{OT1}
\setfont\smallsc\scshape{10}{900}{OT1}
\setfont\smallttsl\ttslshape{10}{900}{OT1TT}
\font\smalli=cmmi9
\font\smallsy=cmsy9
\def\smallecsize{0900}

% Fonts for small examples (8pt).
\def\smallernominalsize{8pt}
\setfont\smallerrm\rmshape{8}{1000}{OT1}
\setfont\smallertt\ttshape{8}{1000}{OT1TT}
\setfont\smallerbf\bfshape{10}{800}{OT1}
\setfont\smallerit\itshape{8}{1000}{OT1IT}
\setfont\smallersl\slshape{8}{1000}{OT1}
\setfont\smallersf\sfshape{8}{1000}{OT1}
\setfont\smallersc\scshape{10}{800}{OT1}
\setfont\smallerttsl\ttslshape{10}{800}{OT1TT}
\font\smalleri=cmmi8
\font\smallersy=cmsy8
\def\smallerecsize{0800}

% Fonts for math mode superscripts (7pt).
\def\sevennominalsize{7pt}
\setfont\sevenrm\rmshape{7}{1000}{OT1}
\setfont\seventt\ttshape{10}{700}{OT1TT}
\setfont\sevenbf\bfshape{10}{700}{OT1}
\setfont\sevenit\itshape{7}{1000}{OT1IT}
\setfont\sevensl\slshape{10}{700}{OT1}
\setfont\sevensf\sfshape{10}{700}{OT1}
\setfont\sevensc\scshape{10}{700}{OT1}
\setfont\seventtsl\ttslshape{10}{700}{OT1TT}
\font\seveni=cmmi7
\font\sevensy=cmsy7
\def\sevenecsize{0700}

% Fonts for title page (20.4pt):
\def\titlenominalsize{20pt}
\setfont\titlerm\rmbshape{12}{\magstep3}{OT1}
\setfont\titleit\itbshape{10}{\magstep4}{OT1IT}
\setfont\titlesl\slbshape{10}{\magstep4}{OT1}
\setfont\titlett\ttbshape{12}{\magstep3}{OT1TT}
\setfont\titlettsl\ttslshape{10}{\magstep4}{OT1TT}
\setfont\titlesf\sfbshape{17}{\magstep1}{OT1}
\let\titlebf=\titlerm
\setfont\titlesc\scbshape{10}{\magstep4}{OT1}
\font\titlei=cmmi12 scaled \magstep3
\font\titlesy=cmsy10 scaled \magstep4
\def\titleecsize{2074}

% Chapter fonts (14.4pt).
\def\chapnominalsize{14pt}
\setfont\chaprm\rmbshape{12}{\magstep1}{OT1}
\setfont\chapit\itbshape{10}{\magstep2}{OT1IT}
\setfont\chapsl\slbshape{10}{\magstep2}{OT1}
\setfont\chaptt\ttbshape{12}{\magstep1}{OT1TT}
\setfont\chapttsl\ttslshape{10}{\magstep2}{OT1TT}
\setfont\chapsf\sfbshape{12}{\magstep1}{OT1}
\let\chapbf\chaprm
\setfont\chapsc\scbshape{10}{\magstep2}{OT1}
\font\chapi=cmmi12 scaled \magstep1
\font\chapsy=cmsy10 scaled \magstep2
\def\chapecsize{1440}

% Section fonts (12pt).
\def\secnominalsize{12pt}
\setfont\secrm\rmbshape{12}{1000}{OT1}
\setfont\secit\itbshape{10}{\magstep1}{OT1IT}
\setfont\secsl\slbshape{10}{\magstep1}{OT1}
\setfont\sectt\ttbshape{12}{1000}{OT1TT}
\setfont\secttsl\ttslshape{10}{\magstep1}{OT1TT}
\setfont\secsf\sfbshape{12}{1000}{OT1}
\let\secbf\secrm
\setfont\secsc\scbshape{10}{\magstep1}{OT1}
\font\seci=cmmi12
\font\secsy=cmsy10 scaled \magstep1
\def\sececsize{1200}

% Subsection fonts (10pt).
\def\ssecnominalsize{10pt}
\setfont\ssecrm\rmbshape{10}{1000}{OT1}
\setfont\ssecit\itbshape{10}{1000}{OT1IT}
\setfont\ssecsl\slbshape{10}{1000}{OT1}
\setfont\ssectt\ttbshape{10}{1000}{OT1TT}
\setfont\ssecttsl\ttslshape{10}{1000}{OT1TT}
\setfont\ssecsf\sfbshape{10}{1000}{OT1}
\let\ssecbf\ssecrm
\setfont\ssecsc\scbshape{10}{1000}{OT1}
\font\sseci=cmmi10
\font\ssecsy=cmsy10
\def\ssececsize{1000}

% Reduced fonts for @acronym in text (9pt).
\def\reducednominalsize{9pt}
\setfont\reducedrm\rmshape{9}{1000}{OT1}
\setfont\reducedtt\ttshape{9}{1000}{OT1TT}
\setfont\reducedbf\bfshape{10}{900}{OT1}
\setfont\reducedit\itshape{9}{1000}{OT1IT}
\setfont\reducedsl\slshape{9}{1000}{OT1}
\setfont\reducedsf\sfshape{9}{1000}{OT1}
\setfont\reducedsc\scshape{10}{900}{OT1}
\setfont\reducedttsl\ttslshape{10}{900}{OT1TT}
\font\reducedi=cmmi9
\font\reducedsy=cmsy9
\def\reducedecsize{0900}

\divide\parskip by 2  % reduce space between paragraphs
\textleading = 12pt   % line spacing for 10pt CM
\textfonts            % reset the current fonts
\rm
} % end of 10pt text font size definitions, \definetextfontsizex

% Fonts for short table of contents.
\setfont\shortcontrm\rmshape{12}{1000}{OT1}
\setfont\shortcontbf\bfshape{10}{\magstep1}{OT1}  % no cmb12
\setfont\shortcontsl\slshape{12}{1000}{OT1}
\setfont\shortconttt\ttshape{12}{1000}{OT1TT}


% We provide the user-level command
%   @fonttextsize 10
% (or 11) to redefine the text font size.  pt is assumed.
%
\def\xiword{11}
\def\xword{10}
\def\xwordpt{10pt}
%
\parseargdef\fonttextsize{%
  \def\textsizearg{#1}%
  %\wlog{doing @fonttextsize \textsizearg}%
  %
  % Set \globaldefs so that documents can use this inside @tex, since
  % makeinfo 4.8 does not support it, but we need it nonetheless.
  %
 \begingroup \globaldefs=1
  \ifx\textsizearg\xword \definetextfontsizex
  \else \ifx\textsizearg\xiword \definetextfontsizexi
  \else
    \errhelp=\EMsimple
    \errmessage{@fonttextsize only supports `10' or `11', not `\textsizearg'}
  \fi\fi
 \endgroup
}

%
% Change the current font style to #1, remembering it in \curfontstyle.
% For now, we do not accumulate font styles: @b{@i{foo}} prints foo in
% italics, not bold italics.
%
\def\setfontstyle#1{%
  \def\curfontstyle{#1}% not as a control sequence, because we are \edef'd.
  \csname #1font\endcsname  % change the current font
}

\def\rm{\fam=0 \setfontstyle{rm}}
\def\it{\fam=\itfam \setfontstyle{it}}
\def\sl{\fam=\slfam \setfontstyle{sl}}
\def\bf{\fam=\bffam \setfontstyle{bf}}\def\bfstylename{bf}
\def\tt{\fam=\ttfam \setfontstyle{tt}}\def\ttstylename{tt}

% Texinfo sort of supports the sans serif font style, which plain TeX does not.
% So we set up a \sf.
\newfam\sffam
\def\sf{\fam=\sffam \setfontstyle{sf}}

% We don't need math for this font style.
\def\ttsl{\setfontstyle{ttsl}}


% In order for the font changes to affect most math symbols and letters,
% we have to define the \textfont of the standard families.
% We don't bother to reset \scriptscriptfont; awaiting user need.
%
\def\resetmathfonts{%
  \textfont0=\rmfont \textfont1=\ifont \textfont2=\syfont
  \textfont\itfam=\itfont \textfont\slfam=\slfont \textfont\bffam=\bffont
  \textfont\ttfam=\ttfont \textfont\sffam=\sffont
  %
  % Fonts for superscript.  Note that the 7pt fonts are used regardless
  % of the current font size.
  \scriptfont0=\sevenrm \scriptfont1=\seveni \scriptfont2=\sevensy
  \scriptfont\itfam=\sevenit \scriptfont\slfam=\sevensl
  \scriptfont\bffam=\sevenbf \scriptfont\ttfam=\seventt
  \scriptfont\sffam=\sevensf
}

%

% The font-changing commands (all called \...fonts) redefine the meanings
% of \STYLEfont, instead of just \STYLE.  We do this because \STYLE needs
% to also set the current \fam for math mode.  Our \STYLE (e.g., \rm)
% commands hardwire \STYLEfont to set the current font.
%
% The fonts used for \ifont are for "math italics"  (\itfont is for italics
% in regular text).  \syfont is also used in math mode only.
%
% Each font-changing command also sets the names \lsize (one size lower)
% and \lllsize (three sizes lower).  These relative commands are used
% in, e.g., the LaTeX logo and acronyms.
%
% This all needs generalizing, badly.
%

\def\assignfonts#1{%
  \expandafter\let\expandafter\rmfont\csname #1rm\endcsname
  \expandafter\let\expandafter\itfont\csname #1it\endcsname
  \expandafter\let\expandafter\slfont\csname #1sl\endcsname
  \expandafter\let\expandafter\bffont\csname #1bf\endcsname
  \expandafter\let\expandafter\ttfont\csname #1tt\endcsname
  \expandafter\let\expandafter\smallcaps\csname #1sc\endcsname
  \expandafter\let\expandafter\sffont  \csname #1sf\endcsname
  \expandafter\let\expandafter\ifont   \csname #1i\endcsname
  \expandafter\let\expandafter\syfont  \csname #1sy\endcsname
  \expandafter\let\expandafter\ttslfont\csname #1ttsl\endcsname
}

\newif\ifrmisbold

% Select smaller font size with the current style.  Used to change font size
% in, e.g., the LaTeX logo and acronyms.  If we are using bold fonts for
% normal roman text, also use bold fonts for roman text in the smaller size.
\def\switchtolllsize{%
   \expandafter\assignfonts\expandafter{\lllsize}%
   \ifrmisbold
     \let\rmfont\bffont
   \fi
   \csname\curfontstyle\endcsname
}%

\def\switchtolsize{%
   \expandafter\assignfonts\expandafter{\lsize}%
   \ifrmisbold
     \let\rmfont\bffont
   \fi
   \csname\curfontstyle\endcsname
}%

\def\definefontsetatsize#1#2#3#4#5{%
\expandafter\def\csname #1fonts\endcsname{%
  \def\curfontsize{#1}%
  \def\lsize{#2}\def\lllsize{#3}%
  \csname rmisbold#5\endcsname
  \assignfonts{#1}%
  \resetmathfonts
  \setleading{#4}%
}}

\definefontsetatsize{text}   {reduced}{smaller}{\textleading}{false}
\definefontsetatsize{title}  {chap}   {subsec} {27pt}  {true}
\definefontsetatsize{chap}   {sec}    {text}   {19pt}  {true}
\definefontsetatsize{sec}    {subsec} {reduced}{17pt}  {true}
\definefontsetatsize{ssec}   {text}   {small}  {15pt}  {true}
\definefontsetatsize{reduced}{small}  {smaller}{10.5pt}{false}
\definefontsetatsize{small}  {smaller}{smaller}{10.5pt}{false}
\definefontsetatsize{smaller}{smaller}{smaller}{9.5pt} {false}

\def\titlefont#1{{\titlefonts\rm #1}}
\let\subsecfonts = \ssecfonts
\let\subsubsecfonts = \ssecfonts

% Define these just so they can be easily changed for other fonts.
\def\angleleft{$\langle$}
\def\angleright{$\rangle$}

% Set the fonts to use with the @small... environments.
\let\smallexamplefonts = \smallfonts

% About \smallexamplefonts.  If we use \smallfonts (9pt), @smallexample
% can fit this many characters:
%   8.5x11=86   smallbook=72  a4=90  a5=69
% If we use \scriptfonts (8pt), then we can fit this many characters:
%   8.5x11=90+  smallbook=80  a4=90+  a5=77
% For me, subjectively, the few extra characters that fit aren't worth
% the additional smallness of 8pt.  So I'm making the default 9pt.
%
% By the way, for comparison, here's what fits with @example (10pt):
%   8.5x11=71  smallbook=60  a4=75  a5=58
% --karl, 24jan03.

% Set up the default fonts, so we can use them for creating boxes.
%
\definetextfontsizexi


\message{markup,}

% Check if we are currently using a typewriter font.  Since all the
% Computer Modern typewriter fonts have zero interword stretch (and
% shrink), and it is reasonable to expect all typewriter fonts to have
% this property, we can check that font parameter.
%
\def\ifmonospace{\ifdim\fontdimen3\font=0pt }

% Markup style infrastructure.  \defmarkupstylesetup\INITMACRO will
% define and register \INITMACRO to be called on markup style changes.
% \INITMACRO can check \currentmarkupstyle for the innermost
% style.

\let\currentmarkupstyle\empty

\def\setupmarkupstyle#1{%
  \def\currentmarkupstyle{#1}%
  \markupstylesetup
}

\let\markupstylesetup\empty

\def\defmarkupstylesetup#1{%
  \expandafter\def\expandafter\markupstylesetup
    \expandafter{\markupstylesetup #1}%
  \def#1%
}

% Markup style setup for left and right quotes.
\defmarkupstylesetup\markupsetuplq{%
  \expandafter\let\expandafter \temp
    \csname markupsetuplq\currentmarkupstyle\endcsname
  \ifx\temp\relax \markupsetuplqdefault \else \temp \fi
}

\defmarkupstylesetup\markupsetuprq{%
  \expandafter\let\expandafter \temp
    \csname markupsetuprq\currentmarkupstyle\endcsname
  \ifx\temp\relax \markupsetuprqdefault \else \temp \fi
}

{
\catcode`\'=\active
\catcode`\`=\active

\gdef\markupsetuplqdefault{\let`\lq}
\gdef\markupsetuprqdefault{\let'\rq}

\gdef\markupsetcodequoteleft{\let`\codequoteleft}
\gdef\markupsetcodequoteright{\let'\codequoteright}
}

\let\markupsetuplqcode \markupsetcodequoteleft
\let\markupsetuprqcode \markupsetcodequoteright
%
\let\markupsetuplqexample \markupsetcodequoteleft
\let\markupsetuprqexample \markupsetcodequoteright
%
\let\markupsetuplqkbd     \markupsetcodequoteleft
\let\markupsetuprqkbd     \markupsetcodequoteright
%
\let\markupsetuplqsamp \markupsetcodequoteleft
\let\markupsetuprqsamp \markupsetcodequoteright
%
\let\markupsetuplqverb \markupsetcodequoteleft
\let\markupsetuprqverb \markupsetcodequoteright
%
\let\markupsetuplqverbatim \markupsetcodequoteleft
\let\markupsetuprqverbatim \markupsetcodequoteright

% Allow an option to not use regular directed right quote/apostrophe
% (char 0x27), but instead the undirected quote from cmtt (char 0x0d).
% The undirected quote is ugly, so don't make it the default, but it
% works for pasting with more pdf viewers (at least evince), the
% lilypond developers report.  xpdf does work with the regular 0x27.
%
\def\codequoteright{%
  \ifmonospace
    \expandafter\ifx\csname SETtxicodequoteundirected\endcsname\relax
      \expandafter\ifx\csname SETcodequoteundirected\endcsname\relax
        '%
      \else \char'15 \fi
    \else \char'15 \fi
   \else
     '%
   \fi
}
%
% and a similar option for the left quote char vs. a grave accent.
% Modern fonts display ASCII 0x60 as a grave accent, so some people like
% the code environments to do likewise.
%
\def\codequoteleft{%
  \ifmonospace
    \expandafter\ifx\csname SETtxicodequotebacktick\endcsname\relax
      \expandafter\ifx\csname SETcodequotebacktick\endcsname\relax
        % [Knuth] pp. 380,381,391
        % \relax disables Spanish ligatures ?` and !` of \tt font.
        \relax`%
      \else \char'22 \fi
    \else \char'22 \fi
   \else
     \relax`%
   \fi
}

% Commands to set the quote options.
%
\parseargdef\codequoteundirected{%
  \def\temp{#1}%
  \ifx\temp\onword
    \expandafter\let\csname SETtxicodequoteundirected\endcsname
      = t%
  \else\ifx\temp\offword
    \expandafter\let\csname SETtxicodequoteundirected\endcsname
      = \relax
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @codequoteundirected value `\temp', must be on|off}%
  \fi\fi
}
%
\parseargdef\codequotebacktick{%
  \def\temp{#1}%
  \ifx\temp\onword
    \expandafter\let\csname SETtxicodequotebacktick\endcsname
      = t%
  \else\ifx\temp\offword
    \expandafter\let\csname SETtxicodequotebacktick\endcsname
      = \relax
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @codequotebacktick value `\temp', must be on|off}%
  \fi\fi
}

% [Knuth] pp. 380,381,391, disable Spanish ligatures ?` and !` of \tt font.
\def\noligaturesquoteleft{\relax\lq}

% Count depth in font-changes, for error checks
\newcount\fontdepth \fontdepth=0

% Font commands.

% #1 is the font command (\sl or \it), #2 is the text to slant.
% If we are in a monospaced environment, however, 1) always use \ttsl,
% and 2) do not add an italic correction.
\def\dosmartslant#1#2{%
  \ifusingtt
    {{\ttsl #2}\let\next=\relax}%
    {\def\next{{#1#2}\futurelet\next\smartitaliccorrection}}%
  \next
}
\def\smartslanted{\dosmartslant\sl}
\def\smartitalic{\dosmartslant\it}

% Output an italic correction unless \next (presumed to be the following
% character) is such as not to need one.
\def\smartitaliccorrection{%
  \ifx\next,%
  \else\ifx\next-%
  \else\ifx\next.%
  \else\ifx\next\.%
  \else\ifx\next\comma%
  \else\ptexslash
  \fi\fi\fi\fi\fi
  \aftersmartic
}

% Unconditional use \ttsl, and no ic.  @var is set to this for defuns.
\def\ttslanted#1{{\ttsl #1}}

% @cite is like \smartslanted except unconditionally use \sl.  We never want
% ttsl for book titles, do we?
\def\cite#1{{\sl #1}\futurelet\next\smartitaliccorrection}

\def\aftersmartic{}
\def\var#1{%
  \let\saveaftersmartic = \aftersmartic
  \def\aftersmartic{\null\let\aftersmartic=\saveaftersmartic}%
  \smartslanted{#1}%
}

\let\i=\smartitalic
\let\slanted=\smartslanted
\let\dfn=\smartslanted
\let\emph=\smartitalic

% Explicit font changes: @r, @sc, undocumented @ii.
\def\r#1{{\rm #1}}              % roman font
\def\sc#1{{\smallcaps#1}}       % smallcaps font
\def\ii#1{{\it #1}}             % italic font

% @b, explicit bold.  Also @strong.
\def\b#1{{\bf #1}}
\let\strong=\b

% @sansserif, explicit sans.
\def\sansserif#1{{\sf #1}}

% We can't just use \exhyphenpenalty, because that only has effect at
% the end of a paragraph.  Restore normal hyphenation at the end of the
% group within which \nohyphenation is presumably called.
%
\def\nohyphenation{\hyphenchar\font = -1  \aftergroup\restorehyphenation}
\def\restorehyphenation{\hyphenchar\font = `- }

% Set sfcode to normal for the chars that usually have another value.
% Can't use plain's \frenchspacing because it uses the `\x notation, and
% sometimes \x has an active definition that messes things up.
%
\catcode`@=11
  \def\plainfrenchspacing{%
    \sfcode`\.=\@m \sfcode`\?=\@m \sfcode`\!=\@m
    \sfcode`\:=\@m \sfcode`\;=\@m \sfcode`\,=\@m
    \def\endofsentencespacefactor{1000}% for @. and friends
  }
  \def\plainnonfrenchspacing{%
    \sfcode`\.3000\sfcode`\?3000\sfcode`\!3000
    \sfcode`\:2000\sfcode`\;1500\sfcode`\,1250
    \def\endofsentencespacefactor{3000}% for @. and friends
  }
\catcode`@=\other
\def\endofsentencespacefactor{3000}% default

% @t, explicit typewriter.
\def\t#1{%
  {\tt \plainfrenchspacing #1}%
  \null
}

% @samp.
\def\samp#1{{\setupmarkupstyle{samp}\lq\tclose{#1}\rq\null}}

% @indicateurl is \samp, that is, with quotes.
\let\indicateurl=\samp

% @code (and similar) prints in typewriter, but with spaces the same
% size as normal in the surrounding text, without hyphenation, etc.
% This is a subroutine for that.
\def\tclose#1{%
  {%
    % Change normal interword space to be same as for the current font.
    \spaceskip = \fontdimen2\font
    %
    % Switch to typewriter.
    \tt
    %
    % But `\ ' produces the large typewriter interword space.
    \def\ {{\spaceskip = 0pt{} }}%
    %
    % Turn off hyphenation.
    \nohyphenation
    %
    \plainfrenchspacing
    #1%
  }%
  \null % reset spacefactor to 1000
}

% We *must* turn on hyphenation at `-' and `_' in @code.
% (But see \codedashfinish below.)
% Otherwise, it is too hard to avoid overfull hboxes
% in the Emacs manual, the Library manual, etc.
%
% Unfortunately, TeX uses one parameter (\hyphenchar) to control
% both hyphenation at - and hyphenation within words.
% We must therefore turn them both off (\tclose does that)
% and arrange explicitly to hyphenate at a dash. -- rms.
{
  \catcode`\-=\active \catcode`\_=\active
  \catcode`\'=\active \catcode`\`=\active
  \global\let'=\rq \global\let`=\lq  % default definitions
  %
  \global\def\code{\begingroup
    \setupmarkupstyle{code}%
    % The following should really be moved into \setupmarkupstyle handlers.
    \catcode\dashChar=\active  \catcode\underChar=\active
    \ifallowcodebreaks
     \let-\codedash
     \let_\codeunder
    \else
     \let-\normaldash
     \let_\realunder
    \fi
    % Given -foo (with a single dash), we do not want to allow a break
    % after the hyphen.
    \global\let\codedashprev=\codedash
    %
    \codex
  }
  %
  \gdef\codedash{\futurelet\next\codedashfinish}
  \gdef\codedashfinish{%
    \normaldash % always output the dash character itself.
    %
    % Now, output a discretionary to allow a line break, unless
    % (a) the next character is a -, or
    % (b) the preceding character is a -.
    % E.g., given --posix, we do not want to allow a break after either -.
    % Given --foo-bar, we do want to allow a break between the - and the b.
    \ifx\next\codedash \else
      \ifx\codedashprev\codedash
      \else \discretionary{}{}{}\fi
    \fi
    % we need the space after the = for the case when \next itself is a
    % space token; it would get swallowed otherwise.  As in @code{- a}.
    \global\let\codedashprev= \next
  }
}
\def\normaldash{-}
%
\def\codex #1{\tclose{#1}\endgroup}

\def\codeunder{%
  % this is all so @math{@code{var_name}+1} can work.  In math mode, _
  % is "active" (mathcode"8000) and \normalunderscore (or \char95, etc.)
  % will therefore expand the active definition of _, which is us
  % (inside @code that is), therefore an endless loop.
  \ifusingtt{\ifmmode
               \mathchar"075F % class 0=ordinary, family 7=ttfam, pos 0x5F=_.
             \else\normalunderscore \fi
             \discretionary{}{}{}}%
            {\_}%
}

% An additional complication: the above will allow breaks after, e.g.,
% each of the four underscores in __typeof__.  This is bad.
% @allowcodebreaks provides a document-level way to turn breaking at -
% and _ on and off.
%
\newif\ifallowcodebreaks  \allowcodebreakstrue

\def\keywordtrue{true}
\def\keywordfalse{false}

\parseargdef\allowcodebreaks{%
  \def\txiarg{#1}%
  \ifx\txiarg\keywordtrue
    \allowcodebreakstrue
  \else\ifx\txiarg\keywordfalse
    \allowcodebreaksfalse
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @allowcodebreaks option `\txiarg', must be true|false}%
  \fi\fi
}

% For @command, @env, @file, @option quotes seem unnecessary,
% so use \code rather than \samp.
\let\command=\code
\let\env=\code
\let\file=\code
\let\option=\code

% @uref (abbreviation for `urlref') aka @url takes an optional
% (comma-separated) second argument specifying the text to display and
% an optional third arg as text to display instead of (rather than in
% addition to) the url itself.  First (mandatory) arg is the url.

% TeX-only option to allow changing PDF output to show only the second
% arg (if given), and not the url (which is then just the link target).
\newif\ifurefurlonlylink

% The default \pretolerance setting stops the penalty inserted in
% \urefallowbreak being a discouragement to line breaking.  Set it to
% a negative value for this paragraph only.  Hopefully this does not
% conflict with redefinitions of \par done elsewhere.
\def\nopretolerance{%
\pretolerance=-1
\def\par{\endgraf\pretolerance=100 \let\par\endgraf}%
}

% The main macro is \urefbreak, which allows breaking at expected
% places within the url.
\def\urefbreak{\nopretolerance \begingroup \urefcatcodes \dourefbreak}
\let\uref=\urefbreak
%
\def\dourefbreak#1{\urefbreakfinish #1,,,\finish}
\def\urefbreakfinish#1,#2,#3,#4\finish{% doesn't work in @example
  \unsepspaces
  \pdfurl{#1}%
  \setbox0 = \hbox{\ignorespaces #3}%
  \ifdim\wd0 > 0pt
    \unhbox0 % third arg given, show only that
  \else
    \setbox0 = \hbox{\ignorespaces #2}% look for second arg
    \ifdim\wd0 > 0pt
      \ifpdf
        % For pdfTeX and LuaTeX
        \ifurefurlonlylink
          % PDF plus option to not display url, show just arg
          \unhbox0
        \else
          % PDF, normally display both arg and url for consistency,
          % visibility, if the pdf is eventually used to print, etc.
          \unhbox0\ (\urefcode{#1})%
        \fi
      \else
        \ifx\XeTeXrevision\thisisundefined
          \unhbox0\ (\urefcode{#1})% DVI, always show arg and url
        \else
          % For XeTeX
          \ifurefurlonlylink
            % PDF plus option to not display url, show just arg
            \unhbox0
          \else
            % PDF, normally display both arg and url for consistency,
            % visibility, if the pdf is eventually used to print, etc.
            \unhbox0\ (\urefcode{#1})%
          \fi
        \fi
      \fi
    \else
      \urefcode{#1}% only url given, so show it
    \fi
  \fi
  \endlink
\endgroup}

% Allow line breaks around only a few characters (only).
\def\urefcatcodes{%
  \catcode`\&=\active \catcode`\.=\active
  \catcode`\#=\active \catcode`\?=\active
  \catcode`\/=\active
}
{
  \urefcatcodes
  %
  \global\def\urefcode{\begingroup
    \setupmarkupstyle{code}%
    \urefcatcodes
    \let&\urefcodeamp
    \let.\urefcodedot
    \let#\urefcodehash
    \let?\urefcodequest
    \let/\urefcodeslash
    \codex
  }
  %
  % By default, they are just regular characters.
  \global\def&{\normalamp}
  \global\def.{\normaldot}
  \global\def#{\normalhash}
  \global\def?{\normalquest}
  \global\def/{\normalslash}
}

\def\urefcodeamp{\urefprebreak \&\urefpostbreak}
\def\urefcodedot{\urefprebreak .\urefpostbreak}
\def\urefcodehash{\urefprebreak \#\urefpostbreak}
\def\urefcodequest{\urefprebreak ?\urefpostbreak}
\def\urefcodeslash{\futurelet\next\urefcodeslashfinish}
{
  \catcode`\/=\active
  \global\def\urefcodeslashfinish{%
    \urefprebreak \slashChar
    % Allow line break only after the final / in a sequence of
    % slashes, to avoid line break between the slashes in http://.
    \ifx\next/\else \urefpostbreak \fi
  }
}

% By default we'll break after the special characters, but some people like to
% break before the special chars, so allow that.  Also allow no breaking at
% all, for manual control.
%
\parseargdef\urefbreakstyle{%
  \def\txiarg{#1}%
  \ifx\txiarg\wordnone
    \def\urefprebreak{\nobreak}\def\urefpostbreak{\nobreak}
  \else\ifx\txiarg\wordbefore
    \def\urefprebreak{\urefallowbreak}\def\urefpostbreak{\nobreak}
  \else\ifx\txiarg\wordafter
    \def\urefprebreak{\nobreak}\def\urefpostbreak{\urefallowbreak}
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @urefbreakstyle setting `\txiarg'}%
  \fi\fi\fi
}
\def\wordafter{after}
\def\wordbefore{before}
\def\wordnone{none}

% Allow a ragged right output to aid breaking long URL's.  There can
% be a break at the \allowbreak with no extra glue (if the existing stretch in
% the line is sufficient), a break at the \penalty with extra glue added
% at the end of the line, or no break at all here.
%   Changing the value of the penalty and/or the amount of stretch affects how
% preferable one choice is over the other.
\def\urefallowbreak{%
  \penalty0\relax
  \hskip 0pt plus 2 em\relax
  \penalty1000\relax
  \hskip 0pt plus -2 em\relax
}

\urefbreakstyle after

% @url synonym for @uref, since that's how everyone uses it.
%
\let\url=\uref

% rms does not like angle brackets --karl, 17may97.
% So now @email is just like @uref, unless we are pdf.
%
%\def\email#1{\angleleft{\tt #1}\angleright}
\ifpdforxetex
  \def\email#1{\doemail#1,,\finish}
  \def\doemail#1,#2,#3\finish{\begingroup
    \unsepspaces
    \pdfurl{mailto:#1}%
    \setbox0 = \hbox{\ignorespaces #2}%
    \ifdim\wd0>0pt\unhbox0\else\code{#1}\fi
    \endlink
  \endgroup}
\else
  \let\email=\uref
\fi

% @kbdinputstyle -- arg is `distinct' (@kbd uses slanted tty font always),
%   `example' (@kbd uses ttsl only inside of @example and friends),
%   or `code' (@kbd uses normal tty font always).
\parseargdef\kbdinputstyle{%
  \def\txiarg{#1}%
  \ifx\txiarg\worddistinct
    \gdef\kbdexamplefont{\ttsl}\gdef\kbdfont{\ttsl}%
  \else\ifx\txiarg\wordexample
    \gdef\kbdexamplefont{\ttsl}\gdef\kbdfont{\tt}%
  \else\ifx\txiarg\wordcode
    \gdef\kbdexamplefont{\tt}\gdef\kbdfont{\tt}%
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @kbdinputstyle setting `\txiarg'}%
  \fi\fi\fi
}
\def\worddistinct{distinct}
\def\wordexample{example}
\def\wordcode{code}

% Default is `distinct'.
\kbdinputstyle distinct

% @kbd is like @code, except that if the argument is just one @key command,
% then @kbd has no effect.
\def\kbd#1{{\def\look{#1}\expandafter\kbdsub\look??\par}}

\def\xkey{\key}
\def\kbdsub#1#2#3\par{%
  \def\one{#1}\def\three{#3}\def\threex{??}%
  \ifx\one\xkey\ifx\threex\three \key{#2}%
  \else{\tclose{\kbdfont\setupmarkupstyle{kbd}\look}}\fi
  \else{\tclose{\kbdfont\setupmarkupstyle{kbd}\look}}\fi
}

% definition of @key that produces a lozenge.  Doesn't adjust to text size.
%\setfont\keyrm\rmshape{8}{1000}{OT1}
%\font\keysy=cmsy9
%\def\key#1{{\keyrm\textfont2=\keysy \leavevmode\hbox{%
%  \raise0.4pt\hbox{\angleleft}\kern-.08em\vtop{%
%    \vbox{\hrule\kern-0.4pt
%     \hbox{\raise0.4pt\hbox{\vphantom{\angleleft}}#1}}%
%    \kern-0.4pt\hrule}%
%  \kern-.06em\raise0.4pt\hbox{\angleright}}}}

% definition of @key with no lozenge.  If the current font is already
% monospace, don't change it; that way, we respect @kbdinputstyle.  But
% if it isn't monospace, then use \tt.
%
\def\key#1{{\setupmarkupstyle{key}%
  \nohyphenation
  \ifmonospace\else\tt\fi
  #1}\null}

% @clicksequence{File @click{} Open ...}
\def\clicksequence#1{\begingroup #1\endgroup}

% @clickstyle @arrow   (by default)
\parseargdef\clickstyle{\def\click{#1}}
\def\click{\arrow}

% Typeset a dimension, e.g., `in' or `pt'.  The only reason for the
% argument is to make the input look right: @dmn{pt} instead of @dmn{}pt.
%
\def\dmn#1{\thinspace #1}

% @acronym for "FBI", "NATO", and the like.
% We print this one point size smaller, since it's intended for
% all-uppercase.
%
\def\acronym#1{\doacronym #1,,\finish}
\def\doacronym#1,#2,#3\finish{%
  {\switchtolsize #1}%
  \def\temp{#2}%
  \ifx\temp\empty \else
    \space ({\unsepspaces \ignorespaces \temp \unskip})%
  \fi
  \null % reset \spacefactor=1000
}

% @abbr for "Comput. J." and the like.
% No font change, but don't do end-of-sentence spacing.
%
\def\abbr#1{\doabbr #1,,\finish}
\def\doabbr#1,#2,#3\finish{%
  {\plainfrenchspacing #1}%
  \def\temp{#2}%
  \ifx\temp\empty \else
    \space ({\unsepspaces \ignorespaces \temp \unskip})%
  \fi
  \null % reset \spacefactor=1000
}

% @asis just yields its argument.  Used with @table, for example.
%
\def\asis#1{#1}

% @math outputs its argument in math mode.
%
% One complication: _ usually means subscripts, but it could also mean
% an actual _ character, as in @math{@var{some_variable} + 1}.  So make
% _ active, and distinguish by seeing if the current family is \slfam,
% which is what @var uses.
{
  \catcode`\_ = \active
  \gdef\mathunderscore{%
    \catcode`\_=\active
    \def_{\ifnum\fam=\slfam \_\else\sb\fi}%
  }
}
% Another complication: we want \\ (and @\) to output a math (or tt) \.
% FYI, plain.tex uses \\ as a temporary control sequence (for no
% particular reason), but this is not advertised and we don't care.
%
% The \mathchar is class=0=ordinary, family=7=ttfam, position=5C=\.
\def\mathbackslash{\ifnum\fam=\ttfam \mathchar"075C \else\backslash \fi}
%
\def\math{%
  \ifmmode\else % only go into math if not in math mode already
    \tex
    \mathunderscore
    \let\\ = \mathbackslash
    \mathactive
    % make the texinfo accent commands work in math mode
    \let\"=\ddot
    \let\'=\acute
    \let\==\bar
    \let\^=\hat
    \let\`=\grave
    \let\u=\breve
    \let\v=\check
    \let\~=\tilde
    \let\dotaccent=\dot
    % have to provide another name for sup operator
    \let\mathopsup=\sup
  $\expandafter\finishmath\fi
}
\def\finishmath#1{#1$\endgroup}  % Close the group opened by \tex.

% Some active characters (such as <) are spaced differently in math.
% We have to reset their definitions in case the @math was an argument
% to a command which sets the catcodes (such as @item or @section).
%
{
  \catcode`^ = \active
  \catcode`< = \active
  \catcode`> = \active
  \catcode`+ = \active
  \catcode`' = \active
  \gdef\mathactive{%
    \let^ = \ptexhat
    \let< = \ptexless
    \let> = \ptexgtr
    \let+ = \ptexplus
    \let' = \ptexquoteright
  }
}

% for @sub and @sup, if in math mode, just do a normal sub/superscript.
% If in text, use math to place as sub/superscript, but switch
% into text mode, with smaller fonts.  This is a different font than the
% one used for real math sub/superscripts (8pt vs. 7pt), but let's not
% fix it (significant additions to font machinery) until someone notices.
%
\def\sub{\ifmmode \expandafter\sb \else \expandafter\finishsub\fi}
\def\finishsub#1{$\sb{\hbox{\switchtolllsize #1}}$}%
%
\def\sup{\ifmmode \expandafter\ptexsp \else \expandafter\finishsup\fi}
\def\finishsup#1{$\ptexsp{\hbox{\switchtolllsize #1}}$}%

% provide this command from LaTeX as it is very common
\def\frac#1#2{{{#1}\over{#2}}}

% @displaymath.
% \globaldefs is needed to recognize the end lines in \tex and
% \end tex.  Set \thisenv as @end displaymath is seen before @end tex.
{\obeylines
\globaldefs=1
\envdef\displaymath{%
\tex
\def\thisenv{\displaymath}%
$$%
}

\def\Edisplaymath{$$
\def\thisenv{\tex}%
\end tex
}}

% @inlinefmt{FMTNAME,PROCESSED-TEXT} and @inlineraw{FMTNAME,RAW-TEXT}.
% Ignore unless FMTNAME == tex; then it is like @iftex and @tex,
% except specified as a normal braced arg, so no newlines to worry about.
%
\def\outfmtnametex{tex}
%
\long\def\inlinefmt#1{\doinlinefmt #1,\finish}
\long\def\doinlinefmt#1,#2,\finish{%
  \def\inlinefmtname{#1}%
  \ifx\inlinefmtname\outfmtnametex \ignorespaces #2\fi
}
%
% @inlinefmtifelse{FMTNAME,THEN-TEXT,ELSE-TEXT} expands THEN-TEXT if
% FMTNAME is tex, else ELSE-TEXT.
\long\def\inlinefmtifelse#1{\doinlinefmtifelse #1,,,\finish}
\long\def\doinlinefmtifelse#1,#2,#3,#4,\finish{%
  \def\inlinefmtname{#1}%
  \ifx\inlinefmtname\outfmtnametex \ignorespaces #2\else \ignorespaces #3\fi
}
%
% For raw, must switch into @tex before parsing the argument, to avoid
% setting catcodes prematurely.  Doing it this way means that, for
% example, @inlineraw{html, foo{bar} gets a parse error instead of being
% ignored.  But this isn't important because if people want a literal
% *right* brace they would have to use a command anyway, so they may as
% well use a command to get a left brace too.  We could re-use the
% delimiter character idea from \verb, but it seems like overkill.
%
\long\def\inlineraw{\tex \doinlineraw}
\long\def\doinlineraw#1{\doinlinerawtwo #1,\finish}
\def\doinlinerawtwo#1,#2,\finish{%
  \def\inlinerawname{#1}%
  \ifx\inlinerawname\outfmtnametex \ignorespaces #2\fi
  \endgroup % close group opened by \tex.
}

% @inlineifset{VAR, TEXT} expands TEXT if VAR is @set.
%
\long\def\inlineifset#1{\doinlineifset #1,\finish}
\long\def\doinlineifset#1,#2,\finish{%
  \def\inlinevarname{#1}%
  \expandafter\ifx\csname SET\inlinevarname\endcsname\relax
  \else\ignorespaces#2\fi
}

% @inlineifclear{VAR, TEXT} expands TEXT if VAR is not @set.
%
\long\def\inlineifclear#1{\doinlineifclear #1,\finish}
\long\def\doinlineifclear#1,#2,\finish{%
  \def\inlinevarname{#1}%
  \expandafter\ifx\csname SET\inlinevarname\endcsname\relax \ignorespaces#2\fi
}


\message{glyphs,}
% and logos.

% @@ prints an @, as does @atchar{}.
\def\@{\char64 }
\let\atchar=\@

% @{ @} @lbracechar{} @rbracechar{} all generate brace characters.
\def\lbracechar{{\ifmonospace\char123\else\ensuremath\lbrace\fi}}
\def\rbracechar{{\ifmonospace\char125\else\ensuremath\rbrace\fi}}
\let\{=\lbracechar
\let\}=\rbracechar

% @comma{} to avoid , parsing problems.
\let\comma = ,

% Accents: @, @dotaccent @ringaccent @ubaraccent @udotaccent
% Others are defined by plain TeX: @` @' @" @^ @~ @= @u @v @H.
\let\, = \ptexc
\let\dotaccent = \ptexdot
\def\ringaccent#1{{\accent23 #1}}
\let\tieaccent = \ptext
\let\ubaraccent = \ptexb
\let\udotaccent = \d

% Other special characters: @questiondown @exclamdown @ordf @ordm
% Plain TeX defines: @AA @AE @O @OE @L (plus lowercase versions) @ss.
\def\questiondown{?`}
\def\exclamdown{!`}
\def\ordf{\leavevmode\raise1ex\hbox{\switchtolllsize \underbar{a}}}
\def\ordm{\leavevmode\raise1ex\hbox{\switchtolllsize \underbar{o}}}

% Dotless i and dotless j, used for accents.
\def\imacro{i}
\def\jmacro{j}
\def\dotless#1{%
  \def\temp{#1}%
  \ifx\temp\imacro \ifmmode\imath \else\ptexi \fi
  \else\ifx\temp\jmacro \ifmmode\jmath \else\j \fi
  \else \errmessage{@dotless can be used only with i or j}%
  \fi\fi
}

% The \TeX{} logo, as in plain, but resetting the spacing so that a
% period following counts as ending a sentence.  (Idea found in latex.)
%
\edef\TeX{\TeX \spacefactor=1000 }

% @LaTeX{} logo.  Not quite the same results as the definition in
% latex.ltx, since we use a different font for the raised A; it's most
% convenient for us to use an explicitly smaller font, rather than using
% the \scriptstyle font (since we don't reset \scriptstyle and
% \scriptscriptstyle).
%
\def\LaTeX{%
  L\kern-.36em
  {\setbox0=\hbox{T}%
   \vbox to \ht0{\hbox{%
     \ifx\textnominalsize\xwordpt
       % for 10pt running text, lllsize (8pt) is too small for the A in LaTeX.
       % Revert to plain's \scriptsize, which is 7pt.
       \count255=\the\fam $\fam\count255 \scriptstyle A$%
     \else
       % For 11pt, we can use our lllsize.
       \switchtolllsize A%
     \fi
     }%
     \vss
  }}%
  \kern-.15em
  \TeX
}

% Some math mode symbols.  Define \ensuremath to switch into math mode
% unless we are already there.  Expansion tricks may not be needed here,
% but safer, and can't hurt.
\def\ensuremath{\ifmmode \expandafter\asis \else\expandafter\ensuredmath \fi}
\def\ensuredmath#1{$\relax#1$}
%
\def\bullet{\ensuremath\ptexbullet}
\def\geq{\ensuremath\ge}
\def\leq{\ensuremath\le}
\def\minus{\ensuremath-}

% @dots{} outputs an ellipsis using the current font.
% We do .5em per period so that it has the same spacing in the cm
% typewriter fonts as three actual period characters; on the other hand,
% in other typewriter fonts three periods are wider than 1.5em.  So do
% whichever is larger.
%
\def\dots{%
  \leavevmode
  \setbox0=\hbox{...}% get width of three periods
  \ifdim\wd0 > 1.5em
    \dimen0 = \wd0
  \else
    \dimen0 = 1.5em
  \fi
  \hbox to \dimen0{%
    \hskip 0pt plus.25fil
    .\hskip 0pt plus1fil
    .\hskip 0pt plus1fil
    .\hskip 0pt plus.5fil
  }%
}

% @enddots{} is an end-of-sentence ellipsis.
%
\def\enddots{%
  \dots
  \spacefactor=\endofsentencespacefactor
}

% @point{}, @result{}, @expansion{}, @print{}, @equiv{}.
%
% Since these characters are used in examples, they should be an even number of
% \tt widths. Each \tt character is 1en, so two makes it 1em.
%
\def\point{$\star$}
\def\arrow{\leavevmode\raise.05ex\hbox to 1em{\hfil$\rightarrow$\hfil}}
\def\result{\leavevmode\raise.05ex\hbox to 1em{\hfil$\Rightarrow$\hfil}}
\def\expansion{\leavevmode\hbox to 1em{\hfil$\mapsto$\hfil}}
\def\print{\leavevmode\lower.1ex\hbox to 1em{\hfil$\dashv$\hfil}}
\def\equiv{\leavevmode\hbox to 1em{\hfil$\ptexequiv$\hfil}}

% The @error{} command.
% Adapted from the TeXbook's \boxit.
%
\newbox\errorbox
%
{\ttfont \global\dimen0 = 3em}% Width of the box.
\dimen2 = .55pt % Thickness of rules
% The text. (`r' is open on the right, `e' somewhat less so on the left.)
\setbox0 = \hbox{\kern-.75pt \reducedsf \putworderror\kern-1.5pt}
%
\setbox\errorbox=\hbox to \dimen0{\hfil
   \hsize = \dimen0 \advance\hsize by -5.8pt % Space to left+right.
   \advance\hsize by -2\dimen2 % Rules.
   \vbox{%
      \hrule height\dimen2
      \hbox{\vrule width\dimen2 \kern3pt          % Space to left of text.
         \vtop{\kern2.4pt \box0 \kern2.4pt}% Space above/below.
         \kern3pt\vrule width\dimen2}% Space to right.
      \hrule height\dimen2}
    \hfil}
%
\def\error{\leavevmode\lower.7ex\copy\errorbox}

% @pounds{} is a sterling sign, which Knuth put in the CM italic font.
%
\def\pounds{\ifmonospace{\ecfont\char"BF}\else{\it\$}\fi}

% @euro{} comes from a separate font, depending on the current style.
% We use the free feym* fonts from the eurosym package by Henrik
% Theiling, which support regular, slanted, bold and bold slanted (and
% "outlined" (blackboard board, sort of) versions, which we don't need).
% It is available from http://www.ctan.org/tex-archive/fonts/eurosym.
%
% Although only regular is the truly official Euro symbol, we ignore
% that.  The Euro is designed to be slightly taller than the regular
% font height.
%
% feymr - regular
% feymo - slanted
% feybr - bold
% feybo - bold slanted
%
% There is no good (free) typewriter version, to my knowledge.
% A feymr10 euro is ~7.3pt wide, while a normal cmtt10 char is ~5.25pt wide.
% Hmm.
%
% Also doesn't work in math.  Do we need to do math with euro symbols?
% Hope not.
%
%
\def\euro{{\eurofont e}}
\def\eurofont{%
  % We set the font at each command, rather than predefining it in
  % \textfonts and the other font-switching commands, so that
  % installations which never need the symbol don't have to have the
  % font installed.
  %
  % There is only one designed size (nominal 10pt), so we always scale
  % that to the current nominal size.
  %
  % By the way, simply using "at 1em" works for cmr10 and the like, but
  % does not work for cmbx10 and other extended/shrunken fonts.
  %
  \def\eurosize{\csname\curfontsize nominalsize\endcsname}%
  %
  \ifx\curfontstyle\bfstylename
    % bold:
    \font\thiseurofont = \ifusingit{feybo10}{feybr10} at \eurosize
  \else
    % regular:
    \font\thiseurofont = \ifusingit{feymo10}{feymr10} at \eurosize
  \fi
  \thiseurofont
}

% Glyphs from the EC fonts.  We don't use \let for the aliases, because
% sometimes we redefine the original macro, and the alias should reflect
% the redefinition.
%
% Use LaTeX names for the Icelandic letters.
\def\DH{{\ecfont \char"D0}} % Eth
\def\dh{{\ecfont \char"F0}} % eth
\def\TH{{\ecfont \char"DE}} % Thorn
\def\th{{\ecfont \char"FE}} % thorn
%
\def\guillemetleft{{\ecfont \char"13}}
\def\guillemotleft{\guillemetleft}
\def\guillemetright{{\ecfont \char"14}}
\def\guillemotright{\guillemetright}
\def\guilsinglleft{{\ecfont \char"0E}}
\def\guilsinglright{{\ecfont \char"0F}}
\def\quotedblbase{{\ecfont \char"12}}
\def\quotesinglbase{{\ecfont \char"0D}}
%
% This positioning is not perfect (see the ogonek LaTeX package), but
% we have the precomposed glyphs for the most common cases.  We put the
% tests to use those glyphs in the single \ogonek macro so we have fewer
% dummy definitions to worry about for index entries, etc.
%
% ogonek is also used with other letters in Lithuanian (IOU), but using
% the precomposed glyphs for those is not so easy since they aren't in
% the same EC font.
\def\ogonek#1{{%
  \def\temp{#1}%
  \ifx\temp\macrocharA\Aogonek
  \else\ifx\temp\macrochara\aogonek
  \else\ifx\temp\macrocharE\Eogonek
  \else\ifx\temp\macrochare\eogonek
  \else
    \ecfont \setbox0=\hbox{#1}%
    \ifdim\ht0=1ex\accent"0C #1%
    \else\ooalign{\unhbox0\crcr\hidewidth\char"0C \hidewidth}%
    \fi
  \fi\fi\fi\fi
  }%
}
\def\Aogonek{{\ecfont \char"81}}\def\macrocharA{A}
\def\aogonek{{\ecfont \char"A1}}\def\macrochara{a}
\def\Eogonek{{\ecfont \char"86}}\def\macrocharE{E}
\def\eogonek{{\ecfont \char"A6}}\def\macrochare{e}
%
% Use the European Computer Modern fonts (cm-super in outline format)
% for non-CM glyphs.  That is ec* for regular text and tc* for the text
% companion symbols (LaTeX TS1 encoding).  Both are part of the ec
% package and follow the same conventions.
%
\def\ecfont{\etcfont{e}}
\def\tcfont{\etcfont{t}}
%
\def\etcfont#1{%
  % We can't distinguish serif/sans and italic/slanted, but this
  % is used for crude hacks anyway (like adding French and German
  % quotes to documents typeset with CM, where we lose kerning), so
  % hopefully nobody will notice/care.
  \edef\ecsize{\csname\curfontsize ecsize\endcsname}%
  \edef\nominalsize{\csname\curfontsize nominalsize\endcsname}%
  \ifmonospace
    % typewriter:
    \font\thisecfont = #1ctt\ecsize \space at \nominalsize
  \else
    \ifx\curfontstyle\bfstylename
      % bold:
      \font\thisecfont = #1cb\ifusingit{i}{x}\ecsize \space at \nominalsize
    \else
      % regular:
      \font\thisecfont = #1c\ifusingit{ti}{rm}\ecsize \space at \nominalsize
    \fi
  \fi
  \thisecfont
}

% @registeredsymbol - R in a circle.  The font for the R should really
% be smaller yet, but lllsize is the best we can do for now.
% Adapted from the plain.tex definition of \copyright.
%
\def\registeredsymbol{%
  $^{{\ooalign{\hfil\raise.07ex\hbox{\switchtolllsize R}%
               \hfil\crcr\Orb}}%
    }$%
}

% @textdegree - the normal degrees sign.
%
\def\textdegree{$^\circ$}

% Laurent Siebenmann reports \Orb undefined with:
%  Textures 1.7.7 (preloaded format=plain 93.10.14)  (68K)  16 APR 2004 02:38
% so we'll define it if necessary.
%
\ifx\Orb\thisisundefined
\def\Orb{\mathhexbox20D}
\fi

% Quotes.
\chardef\quoteleft=`\`
\chardef\quoteright=`\'

% only change font for tt for correct kerning and to avoid using
% \ecfont unless necessary.
\def\quotedblleft{%
  \ifmonospace{\ecfont\char"10}\else{\char"5C}\fi
}

\def\quotedblright{%
  \ifmonospace{\ecfont\char"11}\else{\char`\"}\fi
}


\message{page headings,}

\newskip\titlepagetopglue \titlepagetopglue = 1.5in
\newskip\titlepagebottomglue \titlepagebottomglue = 2pc

% First the title page.  Must do @settitle before @titlepage.
\newif\ifseenauthor
\newif\iffinishedtitlepage

% @setcontentsaftertitlepage used to do an implicit @contents or
% @shortcontents after @end titlepage, but it is now obsolete.
\def\setcontentsaftertitlepage{%
  \errmessage{@setcontentsaftertitlepage has been removed as a Texinfo
              command; move your @contents command if you want the contents
              after the title page.}}%
\def\setshortcontentsaftertitlepage{%
  \errmessage{@setshortcontentsaftertitlepage has been removed as a Texinfo
              command; move your @shortcontents and @contents commands if you
              want the contents after the title page.}}%

\parseargdef\shorttitlepage{%
  \begingroup \hbox{}\vskip 1.5in \chaprm \centerline{#1}%
  \endgroup\page\hbox{}\page}

\envdef\titlepage{%
  % Open one extra group, as we want to close it in the middle of \Etitlepage.
  \begingroup
    \parindent=0pt \textfonts
    % Leave some space at the very top of the page.
    \vglue\titlepagetopglue
    % No rule at page bottom unless we print one at the top with @title.
    \finishedtitlepagetrue
    %
    % Most title ``pages'' are actually two pages long, with space
    % at the top of the second.  We don't want the ragged left on the second.
    \let\oldpage = \page
    \def\page{%
      \iffinishedtitlepage\else
	 \finishtitlepage
      \fi
      \let\page = \oldpage
      \page
      \null
    }%
}

\def\Etitlepage{%
    \iffinishedtitlepage\else
	\finishtitlepage
    \fi
    % It is important to do the page break before ending the group,
    % because the headline and footline are only empty inside the group.
    % If we use the new definition of \page, we always get a blank page
    % after the title page, which we certainly don't want.
    \oldpage
  \endgroup
  %
  % Need this before the \...aftertitlepage checks so that if they are
  % in effect the toc pages will come out with page numbers.
  \HEADINGSon
}

\def\finishtitlepage{%
  \vskip4pt \hrule height 2pt width \hsize
  \vskip\titlepagebottomglue
  \finishedtitlepagetrue
}

% Settings used for typesetting titles: no hyphenation, no indentation,
% don't worry much about spacing, ragged right.  This should be used
% inside a \vbox, and fonts need to be set appropriately first. \par should
% be specified before the end of the \vbox, since a vbox is a group.
%
\def\raggedtitlesettings{%
  \rm
  \hyphenpenalty=10000
  \parindent=0pt
  \tolerance=5000
  \ptexraggedright
}

% Macros to be used within @titlepage:

\let\subtitlerm=\rmfont
\def\subtitlefont{\subtitlerm \normalbaselineskip = 13pt \normalbaselines}

\parseargdef\title{%
  \checkenv\titlepage
  \vbox{\titlefonts \raggedtitlesettings #1\par}%
  % print a rule at the page bottom also.
  \finishedtitlepagefalse
  \vskip4pt \hrule height 4pt width \hsize \vskip4pt
}

\parseargdef\subtitle{%
  \checkenv\titlepage
  {\subtitlefont \rightline{#1}}%
}

% <AUTHOR> come last, but may come many times.
% It can also be used inside @quotation.
%
\parseargdef\author{%
  \def\temp{\quotation}%
  \ifx\thisenv\temp
    \def\quotationauthor{#1}% printed in \Equotation.
  \else
    \checkenv\titlepage
    \ifseenauthor\else \vskip 0pt plus 1filll \seenauthortrue \fi
    {\secfonts\rm \leftline{#1}}%
  \fi
}


% Set up page headings and footings.

\let\thispage=\folio

\newtoks\evenheadline    % headline on even pages
\newtoks\oddheadline     % headline on odd pages
\newtoks\evenchapheadline% headline on even pages with a new chapter
\newtoks\oddchapheadline % headline on odd pages with a new chapter
\newtoks\evenfootline    % footline on even pages
\newtoks\oddfootline     % footline on odd pages

% Now make \makeheadline and \makefootline in Plain TeX use those variables
\headline={{\textfonts\rm
            \ifchapterpage
              \ifodd\pageno\the\oddchapheadline\else\the\evenchapheadline\fi
            \else
              \ifodd\pageno\the\oddheadline\else\the\evenheadline\fi
            \fi}}

\footline={{\textfonts\rm \ifodd\pageno \the\oddfootline
                            \else \the\evenfootline \fi}\HEADINGShook}
\let\HEADINGShook=\relax

% Commands to set those variables.
% For example, this is what  @headings on  does
% @evenheading @thistitle|@thispage|@thischapter
% @oddheading @thischapter|@thispage|@thistitle
% @evenfooting @thisfile||
% @oddfooting ||@thisfile


\def\evenheading{\parsearg\evenheadingxxx}
\def\evenheadingxxx #1{\evenheadingyyy #1\|\|\|\|\finish}
\def\evenheadingyyy #1\|#2\|#3\|#4\finish{%
  \global\evenheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}
  \global\evenchapheadline=\evenheadline}

\def\oddheading{\parsearg\oddheadingxxx}
\def\oddheadingxxx #1{\oddheadingyyy #1\|\|\|\|\finish}
\def\oddheadingyyy #1\|#2\|#3\|#4\finish{%
  \global\oddheadline={\rlap{\centerline{#2}}\line{#1\hfil#3}}%
  \global\oddchapheadline=\oddheadline}

\parseargdef\everyheading{\oddheadingxxx{#1}\evenheadingxxx{#1}}%

\def\evenfooting{\parsearg\evenfootingxxx}
\def\evenfootingxxx #1{\evenfootingyyy #1\|\|\|\|\finish}
\def\evenfootingyyy #1\|#2\|#3\|#4\finish{%
\global\evenfootline={\rlap{\centerline{#2}}\line{#1\hfil#3}}}

\def\oddfooting{\parsearg\oddfootingxxx}
\def\oddfootingxxx #1{\oddfootingyyy #1\|\|\|\|\finish}
\def\oddfootingyyy #1\|#2\|#3\|#4\finish{%
  \global\oddfootline = {\rlap{\centerline{#2}}\line{#1\hfil#3}}%
  %
  % Leave some space for the footline.  Hopefully ok to assume
  % @evenfooting will not be used by itself.
  \global\advance\txipageheight by -12pt
  \global\advance\vsize by -12pt
}

\parseargdef\everyfooting{\oddfootingxxx{#1}\evenfootingxxx{#1}}

% @evenheadingmarks top     \thischapter <- chapter at the top of a page
% @evenheadingmarks bottom  \thischapter <- chapter at the bottom of a page
%
% The same set of arguments for:
%
% @oddheadingmarks
% @evenfootingmarks
% @oddfootingmarks
% @everyheadingmarks
% @everyfootingmarks

% These define \getoddheadingmarks, \getevenheadingmarks,
% \getoddfootingmarks, and \getevenfootingmarks, each to one of
% \gettopheadingmarks, \getbottomheadingmarks.
%
\def\evenheadingmarks{\headingmarks{even}{heading}}
\def\oddheadingmarks{\headingmarks{odd}{heading}}
\def\evenfootingmarks{\headingmarks{even}{footing}}
\def\oddfootingmarks{\headingmarks{odd}{footing}}
\parseargdef\everyheadingmarks{\headingmarks{even}{heading}{#1}
                          \headingmarks{odd}{heading}{#1} }
\parseargdef\everyfootingmarks{\headingmarks{even}{footing}{#1}
                          \headingmarks{odd}{footing}{#1} }
% #1 = even/odd, #2 = heading/footing, #3 = top/bottom.
\def\headingmarks#1#2#3 {%
  \expandafter\let\expandafter\temp \csname get#3headingmarks\endcsname
  \global\expandafter\let\csname get#1#2marks\endcsname \temp
}

\everyheadingmarks bottom
\everyfootingmarks bottom

% @headings double      turns headings on for double-sided printing.
% @headings single      turns headings on for single-sided printing.
% @headings off         turns them off.
% @headings on          same as @headings double, retained for compatibility.
% @headings after       turns on double-sided headings after this page.
% @headings doubleafter turns on double-sided headings after this page.
% @headings singleafter turns on single-sided headings after this page.
% By default, they are off at the start of a document,
% and turned `on' after @end titlepage.

\parseargdef\headings{\csname HEADINGS#1\endcsname}

\def\headingsoff{% non-global headings elimination
  \evenheadline={\hfil}\evenfootline={\hfil}\evenchapheadline={\hfil}%
   \oddheadline={\hfil}\oddfootline={\hfil}\oddchapheadline={\hfil}%
}

\def\HEADINGSoff{{\globaldefs=1 \headingsoff}} % global setting
\HEADINGSoff  % it's the default

% When we turn headings on, set the page number to 1.
\def\pageone{
  \global\pageno=1
  \global\arabiccount = \pagecount
}

% For double-sided printing, put current file name in lower left corner,
% chapter name on inside top of right hand pages, document
% title on inside top of left hand pages, and page numbers on outside top
% edge of all pages.
\def\HEADINGSdouble{%
\pageone
\HEADINGSdoublex
}
\let\contentsalignmacro = \chappager

% For single-sided printing, chapter title goes across top left of page,
% page number on top right.
\def\HEADINGSsingle{%
\pageone
\HEADINGSsinglex
}
\def\HEADINGSon{\HEADINGSdouble}

\def\HEADINGSafter{\let\HEADINGShook=\HEADINGSdoublex}
\let\HEADINGSdoubleafter=\HEADINGSafter
\def\HEADINGSdoublex{%
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\folio\hfil\thistitle}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
\global\evenchapheadline={\line{\folio\hfil}}
\global\oddchapheadline={\line{\hfil\folio}}
\global\let\contentsalignmacro = \chapoddpage
}

\def\HEADINGSsingleafter{\let\HEADINGShook=\HEADINGSsinglex}
\def\HEADINGSsinglex{%
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\thischapter\hfil\folio}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
\global\evenchapheadline={\line{\hfil\folio}}
\global\oddchapheadline={\line{\hfil\folio}}
\global\let\contentsalignmacro = \chappager
}

% for @setchapternewpage off
\def\HEADINGSsinglechapoff{%
\pageone
\global\evenfootline={\hfil}
\global\oddfootline={\hfil}
\global\evenheadline={\line{\thischapter\hfil\folio}}
\global\oddheadline={\line{\thischapter\hfil\folio}}
\global\evenchapheadline=\evenheadline
\global\oddchapheadline=\oddheadline
\global\let\contentsalignmacro = \chappager
}

% Subroutines used in generating headings
% This produces Day Month Year style of output.
% Only define if not already defined, in case a txi-??.tex file has set
% up a different format (e.g., txi-cs.tex does this).
\ifx\today\thisisundefined
\def\today{%
  \number\day\space
  \ifcase\month
  \or\putwordMJan\or\putwordMFeb\or\putwordMMar\or\putwordMApr
  \or\putwordMMay\or\putwordMJun\or\putwordMJul\or\putwordMAug
  \or\putwordMSep\or\putwordMOct\or\putwordMNov\or\putwordMDec
  \fi
  \space\number\year}
\fi

% @settitle line...  specifies the title of the document, for headings.
% It generates no output of its own.
\def\thistitle{\putwordNoTitle}
\def\settitle{\parsearg{\gdef\thistitle}}


\message{tables,}
% Tables -- @table, @ftable, @vtable, @item(x).

% default indentation of table text
\newdimen\tableindent \tableindent=.8in
% default indentation of @itemize and @enumerate text
\newdimen\itemindent  \itemindent=.3in
% margin between end of table item and start of table text.
\newdimen\itemmargin  \itemmargin=.1in

% used internally for \itemindent minus \itemmargin
\newdimen\itemmax

% Note @table, @ftable, and @vtable define @item, @itemx, etc., with
% these defs.
% They also define \itemindex
% to index the item name in whatever manner is desired (perhaps none).

\newif\ifitemxneedsnegativevskip

\def\itemxpar{\par\ifitemxneedsnegativevskip\nobreak\vskip-\parskip\nobreak\fi}

\def\internalBitem{\smallbreak \parsearg\itemzzz}
\def\internalBitemx{\itemxpar \parsearg\itemzzz}

\def\itemzzz #1{\begingroup %
  \advance\hsize by -\rightskip
  \advance\hsize by -\tableindent
  \setbox0=\hbox{\itemindicate{#1}}%
  \itemindex{#1}%
  \nobreak % This prevents a break before @itemx.
  %
  % If the item text does not fit in the space we have, put it on a line
  % by itself, and do not allow a page break either before or after that
  % line.  We do not start a paragraph here because then if the next
  % command is, e.g., @kindex, the whatsit would get put into the
  % horizontal list on a line by itself, resulting in extra blank space.
  \ifdim \wd0>\itemmax
    %
    % Make this a paragraph so we get the \parskip glue and wrapping,
    % but leave it ragged-right.
    \begingroup
      \advance\leftskip by-\tableindent
      \advance\hsize by\tableindent
      \advance\rightskip by0pt plus1fil\relax
      \leavevmode\unhbox0\par
    \endgroup
    %
    % We're going to be starting a paragraph, but we don't want the
    % \parskip glue -- logically it's part of the @item we just started.
    \nobreak \vskip-\parskip
    %
    % Stop a page break at the \parskip glue coming up.  However, if
    % what follows is an environment such as @example, there will be no
    % \parskip glue; then the negative vskip we just inserted would
    % cause the example and the item to crash together.  So we use this
    % bizarre value of 10001 as a signal to \aboveenvbreak to insert
    % \parskip glue after all.  Section titles are handled this way also.
    %
    \penalty 10001
    \endgroup
    \itemxneedsnegativevskipfalse
  \else
    % The item text fits into the space.  Start a paragraph, so that the
    % following text (if any) will end up on the same line.
    \noindent
    % Do this with kerns and \unhbox so that if there is a footnote in
    % the item text, it can migrate to the main vertical list and
    % eventually be printed.
    \nobreak\kern-\tableindent
    \dimen0 = \itemmax  \advance\dimen0 by \itemmargin \advance\dimen0 by -\wd0
    \unhbox0
    \nobreak\kern\dimen0
    \endgroup
    \itemxneedsnegativevskiptrue
  \fi
}

\def\item{\errmessage{@item while not in a list environment}}
\def\itemx{\errmessage{@itemx while not in a list environment}}

% @table, @ftable, @vtable.
\envdef\table{%
  \let\itemindex\gobble
  \tablecheck{table}%
}
\envdef\ftable{%
  \def\itemindex ##1{\doind {fn}{\code{##1}}}%
  \tablecheck{ftable}%
}
\envdef\vtable{%
  \def\itemindex ##1{\doind {vr}{\code{##1}}}%
  \tablecheck{vtable}%
}
\def\tablecheck#1{%
  \ifnum \the\catcode`\^^M=\active
    \endgroup
    \errmessage{This command won't work in this context; perhaps the problem is
      that we are \inenvironment\thisenv}%
    \def\next{\doignore{#1}}%
  \else
    \let\next\tablex
  \fi
  \next
}
\def\tablex#1{%
  \def\itemindicate{#1}%
  \parsearg\tabley
}
\def\tabley#1{%
  {%
    \makevalueexpandable
    \edef\temp{\noexpand\tablez #1\space\space\space}%
    \expandafter
  }\temp \endtablez
}
\def\tablez #1 #2 #3 #4\endtablez{%
  \aboveenvbreak
  \ifnum 0#1>0 \advance \leftskip by #1\mil \fi
  \ifnum 0#2>0 \tableindent=#2\mil \fi
  \ifnum 0#3>0 \advance \rightskip by #3\mil \fi
  \itemmax=\tableindent
  \advance \itemmax by -\itemmargin
  \advance \leftskip by \tableindent
  \exdentamount=\tableindent
  \parindent = 0pt
  \parskip = \smallskipamount
  \ifdim \parskip=0pt \parskip=2pt \fi
  \let\item = \internalBitem
  \let\itemx = \internalBitemx
}
\def\Etable{\endgraf\afterenvbreak}
\let\Eftable\Etable
\let\Evtable\Etable
\let\Eitemize\Etable
\let\Eenumerate\Etable

% This is the counter used by @enumerate, which is really @itemize

\newcount \itemno

\envdef\itemize{\parsearg\doitemize}

\def\doitemize#1{%
  \aboveenvbreak
  \itemmax=\itemindent
  \advance\itemmax by -\itemmargin
  \advance\leftskip by \itemindent
  \exdentamount=\itemindent
  \parindent=0pt
  \parskip=\smallskipamount
  \ifdim\parskip=0pt \parskip=2pt \fi
  %
  % Try typesetting the item mark so that if the document erroneously says
  % something like @itemize @samp (intending @table), there's an error
  % right away at the @itemize.  It's not the best error message in the
  % world, but it's better than leaving it to the @item.  This means if
  % the user wants an empty mark, they have to say @w{} not just @w.
  \def\itemcontents{#1}%
  \setbox0 = \hbox{\itemcontents}%
  %
  % @itemize with no arg is equivalent to @itemize @bullet.
  \ifx\itemcontents\empty\def\itemcontents{\bullet}\fi
  %
  \let\item=\itemizeitem
}

% Definition of @item while inside @itemize and @enumerate.
%
\def\itemizeitem{%
  \advance\itemno by 1  % for enumerations
  {\let\par=\endgraf \smallbreak}% reasonable place to break
  {%
   % If the document has an @itemize directly after a section title, a
   % \nobreak will be last on the list, and \sectionheading will have
   % done a \vskip-\parskip.  In that case, we don't want to zero
   % parskip, or the item text will crash with the heading.  On the
   % other hand, when there is normal text preceding the item (as there
   % usually is), we do want to zero parskip, or there would be too much
   % space.  In that case, we won't have a \nobreak before.  At least
   % that's the theory.
   \ifnum\lastpenalty<10000 \parskip=0in \fi
   \noindent
   \hbox to 0pt{\hss \itemcontents \kern\itemmargin}%
   %
   \ifinner\else
     \vadjust{\penalty 1200}% not good to break after first line of item.
   \fi
   % We can be in inner vertical mode in a footnote, although an
   % @itemize looks awful there.
  }%
  \flushcr
}

% \splitoff TOKENS\endmark defines \first to be the first token in
% TOKENS, and \rest to be the remainder.
%
\def\splitoff#1#2\endmark{\def\first{#1}\def\rest{#2}}%

% Allow an optional argument of an uppercase letter, lowercase letter,
% or number, to specify the first label in the enumerated list.  No
% argument is the same as `1'.
%
\envparseargdef\enumerate{\enumeratey #1  \endenumeratey}
\def\enumeratey #1 #2\endenumeratey{%
  % If we were given no argument, pretend we were given `1'.
  \def\thearg{#1}%
  \ifx\thearg\empty \def\thearg{1}\fi
  %
  % Detect if the argument is a single token.  If so, it might be a
  % letter.  Otherwise, the only valid thing it can be is a number.
  % (We will always have one token, because of the test we just made.
  % This is a good thing, since \splitoff doesn't work given nothing at
  % all -- the first parameter is undelimited.)
  \expandafter\splitoff\thearg\endmark
  \ifx\rest\empty
    % Only one token in the argument.  It could still be anything.
    % A ``lowercase letter'' is one whose \lccode is nonzero.
    % An ``uppercase letter'' is one whose \lccode is both nonzero, and
    %   not equal to itself.
    % Otherwise, we assume it's a number.
    %
    % We need the \relax at the end of the \ifnum lines to stop TeX from
    % continuing to look for a <number>.
    %
    \ifnum\lccode\expandafter`\thearg=0\relax
      \numericenumerate % a number (we hope)
    \else
      % It's a letter.
      \ifnum\lccode\expandafter`\thearg=\expandafter`\thearg\relax
        \lowercaseenumerate % lowercase letter
      \else
        \uppercaseenumerate % uppercase letter
      \fi
    \fi
  \else
    % Multiple tokens in the argument.  We hope it's a number.
    \numericenumerate
  \fi
}

% An @enumerate whose labels are integers.  The starting integer is
% given in \thearg.
%
\def\numericenumerate{%
  \itemno = \thearg
  \startenumeration{\the\itemno}%
}

% The starting (lowercase) letter is in \thearg.
\def\lowercaseenumerate{%
  \itemno = \expandafter`\thearg
  \startenumeration{%
    % Be sure we're not beyond the end of the alphabet.
    \ifnum\itemno=0
      \errmessage{No more lowercase letters in @enumerate; get a bigger
                  alphabet}%
    \fi
    \char\lccode\itemno
  }%
}

% The starting (uppercase) letter is in \thearg.
\def\uppercaseenumerate{%
  \itemno = \expandafter`\thearg
  \startenumeration{%
    % Be sure we're not beyond the end of the alphabet.
    \ifnum\itemno=0
      \errmessage{No more uppercase letters in @enumerate; get a bigger
                  alphabet}
    \fi
    \char\uccode\itemno
  }%
}

% Call \doitemize, adding a period to the first argument and supplying the
% common last two arguments.  Also subtract one from the initial value in
% \itemno, since @item increments \itemno.
%
\def\startenumeration#1{%
  \advance\itemno by -1
  \doitemize{#1.}\flushcr
}

% @alphaenumerate and @capsenumerate are abbreviations for giving an arg
% to @enumerate.
%
\def\alphaenumerate{\enumerate{a}}
\def\capsenumerate{\enumerate{A}}
\def\Ealphaenumerate{\Eenumerate}
\def\Ecapsenumerate{\Eenumerate}


% @multitable macros
% Amy Hendrickson, 8/18/94, 3/6/96
%
% @multitable ... @end multitable will make as many columns as desired.
% Contents of each column will wrap at width given in preamble.  Width
% can be specified either with sample text given in a template line,
% or in percent of \hsize, the current width of text on page.

% Table can continue over pages but will only break between lines.

% To make preamble:
%
% Either define widths of columns in terms of percent of \hsize:
%   @multitable @columnfractions .25 .3 .45
%   @item ...
%
%   Numbers following @columnfractions are the percent of the total
%   current hsize to be used for each column. You may use as many
%   columns as desired.


% Or use a template:
%   @multitable {Column 1 template} {Column 2 template} {Column 3 template}
%   @item ...
%   using the widest term desired in each column.

% Each new table line starts with @item, each subsequent new column
% starts with @tab. Empty columns may be produced by supplying @tab's
% with nothing between them for as many times as empty columns are needed,
% ie, @tab@tab@tab will produce two empty columns.

% @item, @tab do not need to be on their own lines, but it will not hurt
% if they are.

% Sample multitable:

%   @multitable {Column 1 template} {Column 2 template} {Column 3 template}
%   @item first col stuff @tab second col stuff @tab third col
%   @item
%   first col stuff
%   @tab
%   second col stuff
%   @tab
%   third col
%   @item first col stuff @tab second col stuff
%   @tab Many paragraphs of text may be used in any column.
%
%         They will wrap at the width determined by the template.
%   @item@tab@tab This will be in third column.
%   @end multitable

% Default dimensions may be reset by user.
% @multitableparskip is vertical space between paragraphs in table.
% @multitableparindent is paragraph indent in table.
% @multitablecolmargin is horizontal space to be left between columns.
% @multitablelinespace is space to leave between table items, baseline
%                                                            to baseline.
%   0pt means it depends on current normal line spacing.
%
\newskip\multitableparskip
\newskip\multitableparindent
\newdimen\multitablecolspace
\newskip\multitablelinespace
\multitableparskip=0pt
\multitableparindent=6pt
\multitablecolspace=12pt
\multitablelinespace=0pt

% Macros used to set up halign preamble:
%
\let\endsetuptable\relax
\def\xendsetuptable{\endsetuptable}
\let\columnfractions\relax
\def\xcolumnfractions{\columnfractions}
\newif\ifsetpercent

% #1 is the @columnfraction, usually a decimal number like .5, but might
% be just 1.  We just use it, whatever it is.
%
\def\pickupwholefraction#1 {%
  \global\advance\colcount by 1
  \expandafter\xdef\csname col\the\colcount\endcsname{#1\hsize}%
  \setuptable
}

\newcount\colcount
\def\setuptable#1{%
  \def\firstarg{#1}%
  \ifx\firstarg\xendsetuptable
    \let\go = \relax
  \else
    \ifx\firstarg\xcolumnfractions
      \global\setpercenttrue
    \else
      \ifsetpercent
         \let\go\pickupwholefraction
      \else
         \global\advance\colcount by 1
         \setbox0=\hbox{#1\unskip\space}% Add a normal word space as a
                   % separator; typically that is always in the input, anyway.
         \expandafter\xdef\csname col\the\colcount\endcsname{\the\wd0}%
      \fi
    \fi
    \ifx\go\pickupwholefraction
      % Put the argument back for the \pickupwholefraction call, so
      % we'll always have a period there to be parsed.
      \def\go{\pickupwholefraction#1}%
    \else
      \let\go = \setuptable
    \fi%
  \fi
  \go
}

% multitable-only commands.
%
% @headitem starts a heading row, which we typeset in bold.  Assignments
% have to be global since we are inside the implicit group of an
% alignment entry.  \everycr below resets \everytab so we don't have to
% undo it ourselves.
\def\headitemfont{\b}% for people to use in the template row; not changeable
\def\headitem{%
  \checkenv\multitable
  \crcr
  \gdef\headitemcrhook{\nobreak}% attempt to avoid page break after headings
  \global\everytab={\bf}% can't use \headitemfont since the parsing differs
  \the\everytab % for the first item
}%
%
% default for tables with no headings.
\let\headitemcrhook=\relax
%
% A \tab used to include \hskip1sp.  But then the space in a template
% line is not enough.  That is bad.  So let's go back to just `&' until
% we again encounter the problem the 1sp was intended to solve.
%					--karl, <EMAIL>, 20apr99.
\def\tab{\checkenv\multitable &\the\everytab}%

% @multitable ... @end multitable definitions:
%
\newtoks\everytab  % insert after every tab.
%
\envdef\multitable{%
  \vskip\parskip
  \startsavinginserts
  %
  % @item within a multitable starts a normal row.
  % We use \def instead of \let so that if one of the multitable entries
  % contains an @itemize, we don't choke on the \item (seen as \crcr aka
  % \endtemplate) expanding \doitemize.
  \def\item{\crcr}%
  %
  \tolerance=9500
  \hbadness=9500
  \setmultitablespacing
  \parskip=\multitableparskip
  \parindent=\multitableparindent
  \overfullrule=0pt
  \global\colcount=0
  %
  \everycr = {%
    \noalign{%
      \global\everytab={}% Reset from possible headitem.
      \global\colcount=0 % Reset the column counter.
      %
      % Check for saved footnotes, etc.:
      \checkinserts
      %
      % Perhaps a \nobreak, then reset:
      \headitemcrhook
      \global\let\headitemcrhook=\relax
    }%
  }%
  %
  \parsearg\domultitable
}
\def\domultitable#1{%
  % To parse everything between @multitable and @item:
  \setuptable#1 \endsetuptable
  %
  % This preamble sets up a generic column definition, which will
  % be used as many times as user calls for columns.
  % \vtop will set a single line and will also let text wrap and
  % continue for many paragraphs if desired.
  \halign\bgroup &%
    \global\advance\colcount by 1
    \multistrut
    \vtop{%
      % Use the current \colcount to find the correct column width:
      \hsize=\expandafter\csname col\the\colcount\endcsname
      %
      % In order to keep entries from bumping into each other
      % we will add a \leftskip of \multitablecolspace to all columns after
      % the first one.
      %
      % If a template has been used, we will add \multitablecolspace
      % to the width of each template entry.
      %
      % If the user has set preamble in terms of percent of \hsize we will
      % use that dimension as the width of the column, and the \leftskip
      % will keep entries from bumping into each other.  Table will start at
      % left margin and final column will justify at right margin.
      %
      % Make sure we don't inherit \rightskip from the outer environment.
      \rightskip=0pt
      \ifnum\colcount=1
	% The first column will be indented with the surrounding text.
	\advance\hsize by\leftskip
      \else
	\ifsetpercent \else
	  % If user has not set preamble in terms of percent of \hsize
	  % we will advance \hsize by \multitablecolspace.
	  \advance\hsize by \multitablecolspace
	\fi
       % In either case we will make \leftskip=\multitablecolspace:
      \leftskip=\multitablecolspace
      \fi
      % Ignoring space at the beginning and end avoids an occasional spurious
      % blank line, when TeX decides to break the line at the space before the
      % box from the multistrut, so the strut ends up on a line by itself.
      % For example:
      % @multitable @columnfractions .11 .89
      % @item @code{#}
      % @tab Legal holiday which is valid in major parts of the whole country.
      % Is automatically provided with highlighting sequences respectively
      % marking characters.
      \noindent\ignorespaces##\unskip\multistrut
    }\cr
}
\def\Emultitable{%
  \crcr
  \egroup % end the \halign
  \global\setpercentfalse
}

\def\setmultitablespacing{%
  \def\multistrut{\strut}% just use the standard line spacing
  %
  % Compute \multitablelinespace (if not defined by user) for use in
  % \multitableparskip calculation.  We used define \multistrut based on
  % this, but (ironically) that caused the spacing to be off.
  % See bug-texinfo report from Werner Lemberg, 31 Oct 2004 12:52:20 +0100.
\ifdim\multitablelinespace=0pt
\setbox0=\vbox{X}\global\multitablelinespace=\the\baselineskip
\global\advance\multitablelinespace by-\ht0
\fi
% Test to see if parskip is larger than space between lines of
% table. If not, do nothing.
%        If so, set to same dimension as multitablelinespace.
\ifdim\multitableparskip>\multitablelinespace
\global\multitableparskip=\multitablelinespace
\global\advance\multitableparskip-7pt % to keep parskip somewhat smaller
                                      % than skip between lines in the table.
\fi%
\ifdim\multitableparskip=0pt
\global\multitableparskip=\multitablelinespace
\global\advance\multitableparskip-7pt % to keep parskip somewhat smaller
                                      % than skip between lines in the table.
\fi}


\message{conditionals,}

% @iftex, @ifnotdocbook, @ifnothtml, @ifnotinfo, @ifnotplaintext,
% @ifnotxml always succeed.  They currently do nothing; we don't
% attempt to check whether the conditionals are properly nested.  But we
% have to remember that they are conditionals, so that @end doesn't
% attempt to close an environment group.
%
\def\makecond#1{%
  \expandafter\let\csname #1\endcsname = \relax
  \expandafter\let\csname iscond.#1\endcsname = 1
}
\makecond{iftex}
\makecond{ifnotdocbook}
\makecond{ifnothtml}
\makecond{ifnotinfo}
\makecond{ifnotplaintext}
\makecond{ifnotxml}

% Ignore @ignore, @ifhtml, @ifinfo, and the like.
%
\def\direntry{\doignore{direntry}}
\def\documentdescription{\doignore{documentdescription}}
\def\docbook{\doignore{docbook}}
\def\html{\doignore{html}}
\def\ifdocbook{\doignore{ifdocbook}}
\def\ifhtml{\doignore{ifhtml}}
\def\ifinfo{\doignore{ifinfo}}
\def\ifnottex{\doignore{ifnottex}}
\def\ifplaintext{\doignore{ifplaintext}}
\def\ifxml{\doignore{ifxml}}
\def\ignore{\doignore{ignore}}
\def\menu{\doignore{menu}}
\def\xml{\doignore{xml}}

% Ignore text until a line `@end #1', keeping track of nested conditionals.
%
% A count to remember the depth of nesting.
\newcount\doignorecount

\def\doignore#1{\begingroup
  % Scan in ``verbatim'' mode:
  \obeylines
  \catcode`\@ = \other
  \catcode`\{ = \other
  \catcode`\} = \other
  %
  % Make sure that spaces turn into tokens that match what \doignoretext wants.
  \spaceisspace
  %
  % Count number of #1's that we've seen.
  \doignorecount = 0
  %
  % Swallow text until we reach the matching `@end #1'.
  \dodoignore{#1}%
}

{ \catcode`_=11 % We want to use \_STOP_ which cannot appear in texinfo source.
  \obeylines %
  %
  \gdef\dodoignore#1{%
    % #1 contains the command name as a string, e.g., `ifinfo'.
    %
    % Define a command to find the next `@end #1'.
    \long\def\doignoretext##1^^M@end #1{%
      \doignoretextyyy##1^^M@#1\_STOP_}%
    %
    % And this command to find another #1 command, at the beginning of a
    % line.  (Otherwise, we would consider a line `@c @ifset', for
    % example, to count as an @ifset for nesting.)
    \long\def\doignoretextyyy##1^^M@#1##2\_STOP_{\doignoreyyy{##2}\_STOP_}%
    %
    % And now expand that command.
    \doignoretext ^^M%
  }%
}

\def\doignoreyyy#1{%
  \def\temp{#1}%
  \ifx\temp\empty			% Nothing found.
    \let\next\doignoretextzzz
  \else					% Found a nested condition, ...
    \advance\doignorecount by 1
    \let\next\doignoretextyyy		% ..., look for another.
    % If we're here, #1 ends with ^^M\ifinfo (for example).
  \fi
  \next #1% the token \_STOP_ is present just after this macro.
}

% We have to swallow the remaining "\_STOP_".
%
\def\doignoretextzzz#1{%
  \ifnum\doignorecount = 0	% We have just found the outermost @end.
    \let\next\enddoignore
  \else				% Still inside a nested condition.
    \advance\doignorecount by -1
    \let\next\doignoretext      % Look for the next @end.
  \fi
  \next
}

% Finish off ignored text.
{ \obeylines%
  % Ignore anything after the last `@end #1'; this matters in verbatim
  % environments, where otherwise the newline after an ignored conditional
  % would result in a blank line in the output.
  \gdef\enddoignore#1^^M{\endgroup\ignorespaces}%
}


% @set VAR sets the variable VAR to an empty value.
% @set VAR REST-OF-LINE sets VAR to the value REST-OF-LINE.
%
% Since we want to separate VAR from REST-OF-LINE (which might be
% empty), we can't just use \parsearg; we have to insert a space of our
% own to delimit the rest of the line, and then take it out again if we
% didn't need it.
% We rely on the fact that \parsearg sets \catcode`\ =10.
%
\parseargdef\set{\setyyy#1 \endsetyyy}
\def\setyyy#1 #2\endsetyyy{%
  {%
    \makevalueexpandable
    \def\temp{#2}%
    \edef\next{\gdef\makecsname{SET#1}}%
    \ifx\temp\empty
      \next{}%
    \else
      \setzzz#2\endsetzzz
    \fi
  }%
}
% Remove the trailing space \setxxx inserted.
\def\setzzz#1 \endsetzzz{\next{#1}}

% @clear VAR clears (i.e., unsets) the variable VAR.
%
\parseargdef\clear{%
  {%
    \makevalueexpandable
    \global\expandafter\let\csname SET#1\endcsname=\relax
  }%
}

% @value{foo} gets the text saved in variable foo.
\def\value{\begingroup\makevalueexpandable\valuexxx}
\def\valuexxx#1{\expandablevalue{#1}\endgroup}
{
  \catcode`\-=\active \catcode`\_=\active
  %
  \gdef\makevalueexpandable{%
    \let\value = \expandablevalue
    % We don't want these characters active, ...
    \catcode`\-=\other \catcode`\_=\other
    % ..., but we might end up with active ones in the argument if
    % we're called from @code, as @code{@value{foo-bar_}}, though.
    % So \let them to their normal equivalents.
    \let-\normaldash \let_\normalunderscore
  }
}

\def\expandablevalue#1{%
  \expandafter\ifx\csname SET#1\endcsname\relax
    {[No value for ``#1'']}%
    \message{Variable `#1', used in @value, is not set.}%
  \else
    \csname SET#1\endcsname
  \fi
}

% Like \expandablevalue, but completely expandable (the \message in the
% definition above operates at the execution level of TeX).  Used when
% writing to auxiliary files, due to the expansion that \write does.
% If flag is undefined, pass through an unexpanded @value command: maybe it
% will be set by the time it is read back in.
%
% NB flag names containing - or _ may not work here.
\def\dummyvalue#1{%
  \expandafter\ifx\csname SET#1\endcsname\relax
    \string\value{#1}%
  \else
    \csname SET#1\endcsname
  \fi
}

% Used for @value's in index entries to form the sort key: expand the @value
% if possible, otherwise sort late.
\def\indexnofontsvalue#1{%
  \expandafter\ifx\csname SET#1\endcsname\relax
    ZZZZZZZ%
  \else
    \csname SET#1\endcsname
  \fi
}

% @ifset VAR ... @end ifset reads the `...' iff VAR has been defined
% with @set.
%
% To get the special treatment we need for `@end ifset,' we call
% \makecond and then redefine.
%
\makecond{ifset}
\def\ifset{\parsearg{\doifset{\let\next=\ifsetfail}}}
\def\doifset#1#2{%
  {%
    \makevalueexpandable
    \let\next=\empty
    \expandafter\ifx\csname SET#2\endcsname\relax
      #1% If not set, redefine \next.
    \fi
    \expandafter
  }\next
}
\def\ifsetfail{\doignore{ifset}}

% @ifclear VAR ... @end executes the `...' iff VAR has never been
% defined with @set, or has been undefined with @clear.
%
% The `\else' inside the `\doifset' parameter is a trick to reuse the
% above code: if the variable is not set, do nothing, if it is set,
% then redefine \next to \ifclearfail.
%
\makecond{ifclear}
\def\ifclear{\parsearg{\doifset{\else \let\next=\ifclearfail}}}
\def\ifclearfail{\doignore{ifclear}}

% @ifcommandisdefined CMD ... @end executes the `...' if CMD (written
% without the @) is in fact defined.  We can only feasibly check at the
% TeX level, so something like `mathcode' is going to considered
% defined even though it is not a Texinfo command.
%
\makecond{ifcommanddefined}
\def\ifcommanddefined{\parsearg{\doifcmddefined{\let\next=\ifcmddefinedfail}}}
%
\def\doifcmddefined#1#2{{%
    \makevalueexpandable
    \let\next=\empty
    \expandafter\ifx\csname #2\endcsname\relax
      #1% If not defined, \let\next as above.
    \fi
    \expandafter
  }\next
}
\def\ifcmddefinedfail{\doignore{ifcommanddefined}}

% @ifcommandnotdefined CMD ... handled similar to @ifclear above.
\makecond{ifcommandnotdefined}
\def\ifcommandnotdefined{%
  \parsearg{\doifcmddefined{\else \let\next=\ifcmdnotdefinedfail}}}
\def\ifcmdnotdefinedfail{\doignore{ifcommandnotdefined}}

% Set the `txicommandconditionals' variable, so documents have a way to
% test if the @ifcommand...defined conditionals are available.
\set txicommandconditionals

% @dircategory CATEGORY  -- specify a category of the dir file
% which this file should belong to.  Ignore this in TeX.
\let\dircategory=\comment

% @defininfoenclose.
\let\definfoenclose=\comment


\message{indexing,}
% Index generation facilities

% Define \newwrite to be identical to plain tex's \newwrite
% except not \outer, so it can be used within macros and \if's.
\edef\newwrite{\makecsname{ptexnewwrite}}

% \newindex {foo} defines an index named IX.
% It automatically defines \IXindex such that
% \IXindex ...rest of line... puts an entry in the index IX.
% It also defines \IXindfile to be the number of the output channel for
% the file that accumulates this index.  The file's extension is IX.
% The name of an index should be no more than 2 characters long
% for the sake of vms.
%
\def\newindex#1{%
  \expandafter\chardef\csname#1indfile\endcsname=0
  \expandafter\xdef\csname#1index\endcsname{%     % Define @#1index
    \noexpand\doindex{#1}}
}

% @defindex foo  ==  \newindex{foo}
%
\def\defindex{\parsearg\newindex}

% Define @defcodeindex, like @defindex except put all entries in @code.
%
\def\defcodeindex{\parsearg\newcodeindex}
%
\def\newcodeindex#1{%
  \expandafter\chardef\csname#1indfile\endcsname=0
  \expandafter\xdef\csname#1index\endcsname{%
    \noexpand\docodeindex{#1}}%
}

% The default indices:
\newindex{cp}%      concepts,
\newcodeindex{fn}%  functions,
\newcodeindex{vr}%  variables,
\newcodeindex{tp}%  types,
\newcodeindex{ky}%  keys
\newcodeindex{pg}%  and programs.


% @synindex foo bar    makes index foo feed into index bar.
% Do this instead of @defindex foo if you don't want it as a separate index.
%
% @syncodeindex foo bar   similar, but put all entries made for index foo
% inside @code.
%
\def\synindex#1 #2 {\dosynindex\doindex{#1}{#2}}
\def\syncodeindex#1 #2 {\dosynindex\docodeindex{#1}{#2}}

% #1 is \doindex or \docodeindex, #2 the index getting redefined (foo),
% #3 the target index (bar).
\def\dosynindex#1#2#3{%
  \requireopenindexfile{#3}%
  % redefine \fooindfile:
  \expandafter\let\expandafter\temp\expandafter=\csname#3indfile\endcsname
  \expandafter\let\csname#2indfile\endcsname=\temp
  % redefine \fooindex:
  \expandafter\xdef\csname#2index\endcsname{\noexpand#1{#3}}%
}

% Define \doindex, the driver for all index macros.
% Argument #1 is generated by the calling \fooindex macro,
% and it is the two-letter name of the index.

\def\doindex#1{\edef\indexname{#1}\parsearg\doindexxxx}
\def\doindexxxx #1{\doind{\indexname}{#1}}

% like the previous two, but they put @code around the argument.
\def\docodeindex#1{\edef\indexname{#1}\parsearg\docodeindexxxx}
\def\docodeindexxxx #1{\docind{\indexname}{#1}}


% Used for the aux, toc and index files to prevent expansion of Texinfo
% commands.
%
\def\atdummies{%
  \definedummyletter\@%
  \definedummyletter\ %
  \definedummyletter\{%
  \definedummyletter\}%
  \definedummyletter\&%
  %
  % Do the redefinitions.
  \definedummies
  \otherbackslash
}

% \definedummyword defines \#1 as \string\#1\space, thus effectively
% preventing its expansion.  This is used only for control words,
% not control letters, because the \space would be incorrect for
% control characters, but is needed to separate the control word
% from whatever follows.
%
% These can be used both for control words that take an argument and
% those that do not.  If it is followed by {arg} in the input, then
% that will dutifully get written to the index (or wherever).
%
% For control letters, we have \definedummyletter, which omits the
% space.
%
\def\definedummyword  #1{\def#1{\string#1\space}}%
\def\definedummyletter#1{\def#1{\string#1}}%
\let\definedummyaccent\definedummyletter

% Called from \atdummies to prevent the expansion of commands.
%
\def\definedummies{%
  %
  \let\commondummyword\definedummyword
  \let\commondummyletter\definedummyletter
  \let\commondummyaccent\definedummyaccent
  \commondummiesnofonts
  %
  \definedummyletter\_%
  \definedummyletter\-%
  %
  % Non-English letters.
  \definedummyword\AA
  \definedummyword\AE
  \definedummyword\DH
  \definedummyword\L
  \definedummyword\O
  \definedummyword\OE
  \definedummyword\TH
  \definedummyword\aa
  \definedummyword\ae
  \definedummyword\dh
  \definedummyword\exclamdown
  \definedummyword\l
  \definedummyword\o
  \definedummyword\oe
  \definedummyword\ordf
  \definedummyword\ordm
  \definedummyword\questiondown
  \definedummyword\ss
  \definedummyword\th
  %
  % Although these internal commands shouldn't show up, sometimes they do.
  \definedummyword\bf
  \definedummyword\gtr
  \definedummyword\hat
  \definedummyword\less
  \definedummyword\sf
  \definedummyword\sl
  \definedummyword\tclose
  \definedummyword\tt
  %
  \definedummyword\LaTeX
  \definedummyword\TeX
  %
  % Assorted special characters.
  \definedummyword\ampchar
  \definedummyword\atchar
  \definedummyword\arrow
  \definedummyword\backslashchar
  \definedummyword\bullet
  \definedummyword\comma
  \definedummyword\copyright
  \definedummyword\registeredsymbol
  \definedummyword\dots
  \definedummyword\enddots
  \definedummyword\entrybreak
  \definedummyword\equiv
  \definedummyword\error
  \definedummyword\euro
  \definedummyword\expansion
  \definedummyword\geq
  \definedummyword\guillemetleft
  \definedummyword\guillemetright
  \definedummyword\guilsinglleft
  \definedummyword\guilsinglright
  \definedummyword\lbracechar
  \definedummyword\leq
  \definedummyword\mathopsup
  \definedummyword\minus
  \definedummyword\ogonek
  \definedummyword\pounds
  \definedummyword\point
  \definedummyword\print
  \definedummyword\quotedblbase
  \definedummyword\quotedblleft
  \definedummyword\quotedblright
  \definedummyword\quoteleft
  \definedummyword\quoteright
  \definedummyword\quotesinglbase
  \definedummyword\rbracechar
  \definedummyword\result
  \definedummyword\sub
  \definedummyword\sup
  \definedummyword\textdegree
  %
  \definedummyword\subentry
  %
  % We want to disable all macros so that they are not expanded by \write.
  \macrolist
  \let\value\dummyvalue
  %
  \normalturnoffactive
}

% \commondummiesnofonts: common to \definedummies and \indexnofonts.
% Define \commondummyletter, \commondummyaccent and \commondummyword before
% using.  Used for accents, font commands, and various control letters.
%
\def\commondummiesnofonts{%
  % Control letters and accents.
  \commondummyletter\!%
  \commondummyaccent\"%
  \commondummyaccent\'%
  \commondummyletter\*%
  \commondummyaccent\,%
  \commondummyletter\.%
  \commondummyletter\/%
  \commondummyletter\:%
  \commondummyaccent\=%
  \commondummyletter\?%
  \commondummyaccent\^%
  \commondummyaccent\`%
  \commondummyaccent\~%
  \commondummyword\u
  \commondummyword\v
  \commondummyword\H
  \commondummyword\dotaccent
  \commondummyword\ogonek
  \commondummyword\ringaccent
  \commondummyword\tieaccent
  \commondummyword\ubaraccent
  \commondummyword\udotaccent
  \commondummyword\dotless
  %
  % Texinfo font commands.
  \commondummyword\b
  \commondummyword\i
  \commondummyword\r
  \commondummyword\sansserif
  \commondummyword\sc
  \commondummyword\slanted
  \commondummyword\t
  %
  % Commands that take arguments.
  \commondummyword\abbr
  \commondummyword\acronym
  \commondummyword\anchor
  \commondummyword\cite
  \commondummyword\code
  \commondummyword\command
  \commondummyword\dfn
  \commondummyword\dmn
  \commondummyword\email
  \commondummyword\emph
  \commondummyword\env
  \commondummyword\file
  \commondummyword\image
  \commondummyword\indicateurl
  \commondummyword\inforef
  \commondummyword\kbd
  \commondummyword\key
  \commondummyword\math
  \commondummyword\option
  \commondummyword\pxref
  \commondummyword\ref
  \commondummyword\samp
  \commondummyword\strong
  \commondummyword\tie
  \commondummyword\U
  \commondummyword\uref
  \commondummyword\url
  \commondummyword\var
  \commondummyword\verb
  \commondummyword\w
  \commondummyword\xref
}

\let\indexlbrace\relax
\let\indexrbrace\relax
\let\indexatchar\relax
\let\indexbackslash\relax

{\catcode`\@=0
\catcode`\\=13
  @gdef@backslashdisappear{@def\{}}
}

{
\catcode`\<=13
\catcode`\-=13
\catcode`\`=13
  \gdef\indexnonalnumdisappear{%
    \expandafter\ifx\csname SETtxiindexlquoteignore\endcsname\relax\else
      % @set txiindexlquoteignore makes us ignore left quotes in the sort term.
      % (Introduced for FSFS 2nd ed.)
      \let`=\empty
    \fi
    %
    \expandafter\ifx\csname SETtxiindexbackslashignore\endcsname\relax\else
      \backslashdisappear
    \fi
    %
    \expandafter\ifx\csname SETtxiindexhyphenignore\endcsname\relax\else
      \def-{}%
    \fi
    \expandafter\ifx\csname SETtxiindexlessthanignore\endcsname\relax\else
      \def<{}%
    \fi
    \expandafter\ifx\csname SETtxiindexatsignignore\endcsname\relax\else
      \def\@{}%
    \fi
  }

  \gdef\indexnonalnumreappear{%
    \let-\normaldash
    \let<\normalless
  }
}


% \indexnofonts is used when outputting the strings to sort the index
% by, and when constructing control sequence names.  It eliminates all
% control sequences and just writes whatever the best ASCII sort string
% would be for a given command (usually its argument).
%
\def\indexnofonts{%
  % Accent commands should become @asis.
  \def\commondummyaccent##1{\let##1\asis}%
  % We can just ignore other control letters.
  \def\commondummyletter##1{\let##1\empty}%
  % All control words become @asis by default; overrides below.
  \let\commondummyword\commondummyaccent
  \commondummiesnofonts
  %
  % Don't no-op \tt, since it isn't a user-level command
  % and is used in the definitions of the active chars like <, >, |, etc.
  % Likewise with the other plain tex font commands.
  %\let\tt=\asis
  %
  \def\ { }%
  \def\@{@}%
  \def\_{\normalunderscore}%
  \def\-{}% @- shouldn't affect sorting
  %
  \uccode`\1=`\{ \uppercase{\def\{{1}}%
  \uccode`\1=`\} \uppercase{\def\}{1}}%
  \let\lbracechar\{%
  \let\rbracechar\}%
  %
  %
  \let\do\indexnofontsdef
  %
  % Non-English letters.
  \do\AA{AA}%
  \do\AE{AE}%
  \do\DH{DZZ}%
  \do\L{L}%
  \do\OE{OE}%
  \do\O{O}%
  \do\TH{TH}%
  \do\aa{aa}%
  \do\ae{ae}%
  \do\dh{dzz}%
  \do\exclamdown{!}%
  \do\l{l}%
  \do\oe{oe}%
  \do\ordf{a}%
  \do\ordm{o}%
  \do\o{o}%
  \do\questiondown{?}%
  \do\ss{ss}%
  \do\th{th}%
  %
  \do\LaTeX{LaTeX}%
  \do\TeX{TeX}%
  %
  % Assorted special characters.
  \do\atchar{@}%
  \do\arrow{->}%
  \do\bullet{bullet}%
  \do\comma{,}%
  \do\copyright{copyright}%
  \do\dots{...}%
  \do\enddots{...}%
  \do\equiv{==}%
  \do\error{error}%
  \do\euro{euro}%
  \do\expansion{==>}%
  \do\geq{>=}%
  \do\guillemetleft{<<}%
  \do\guillemetright{>>}%
  \do\guilsinglleft{<}%
  \do\guilsinglright{>}%
  \do\leq{<=}%
  \do\lbracechar{\{}%
  \do\minus{-}%
  \do\point{.}%
  \do\pounds{pounds}%
  \do\print{-|}%
  \do\quotedblbase{"}%
  \do\quotedblleft{"}%
  \do\quotedblright{"}%
  \do\quoteleft{`}%
  \do\quoteright{'}%
  \do\quotesinglbase{,}%
  \do\rbracechar{\}}%
  \do\registeredsymbol{R}%
  \do\result{=>}%
  \do\textdegree{o}%
  %
  % We need to get rid of all macros, leaving only the arguments (if present).
  % Of course this is not nearly correct, but it is the best we can do for now.
  % makeinfo does not expand macros in the argument to @deffn, which ends up
  % writing an index entry, and texindex isn't prepared for an index sort entry
  % that starts with \.
  %
  % Since macro invocations are followed by braces, we can just redefine them
  % to take a single TeX argument.  The case of a macro invocation that
  % goes to end-of-line is not handled.
  %
  \macrolist
  \let\value\indexnofontsvalue
}

% Give the control sequence a definition that removes the {} that follows
% its use, e.g. @AA{} -> AA
\def\indexnofontsdef#1#2{\def#1##1{#2}}%




% #1 is the index name, #2 is the entry text.
\def\doind#1#2{%
  \iflinks
  {%
    %
    \requireopenindexfile{#1}%
    \edef\writeto{\csname#1indfile\endcsname}%
    %
    \def\indextext{#2}%
    \safewhatsit\doindwrite
  }%
  \fi
}

% Same as \doind, but for code indices
\def\docind#1#2{%
  \iflinks
  {%
    %
    \requireopenindexfile{#1}%
    \edef\writeto{\csname#1indfile\endcsname}%
    %
    \def\indextext{#2}%
    \safewhatsit\docindwrite
  }%
  \fi
}

% Check if an index file has been opened, and if not, open it.
\def\requireopenindexfile#1{%
\ifnum\csname #1indfile\endcsname=0
  \expandafter\newwrite \csname#1indfile\endcsname
  \edef\suffix{#1}%
  % A .fls suffix would conflict with the file extension for the output
  % of -recorder, so use .f1s instead.
  \ifx\suffix\indexisfl\def\suffix{f1}\fi
  % Open the file
  \immediate\openout\csname#1indfile\endcsname \jobname.\suffix
  % Using \immediate above here prevents an object entering into the current
  % box, which could confound checks such as those in \safewhatsit for
  % preceding skips.
  \typeout{Writing index file \jobname.\suffix}%
\fi}
\def\indexisfl{fl}

% Definition for writing index entry sort key.
{
\catcode`\-=13
\gdef\indexwritesortas{%
  \begingroup
  \indexnonalnumreappear
  \indexwritesortasxxx}
\gdef\indexwritesortasxxx#1{%
  \xdef\indexsortkey{#1}\endgroup}
}

\def\indexwriteseealso#1{
  \gdef\pagenumbertext{\string\seealso{#1}}%
}
\def\indexwriteseeentry#1{
  \gdef\pagenumbertext{\string\seeentry{#1}}%
}

% The default definitions
\def\sortas#1{}%
\def\seealso#1{\i{\putwordSeeAlso}\ #1}% for sorted index file only
\def\putwordSeeAlso{See also}
\def\seeentry#1{\i{\putwordSee}\ #1}% for sorted index file only


% Given index entry text like "aaa @subentry bbb @sortas{ZZZ}":
%   * Set \bracedtext to "{aaa}{bbb}"
%   * Set \fullindexsortkey to "aaa @subentry ZZZ"
%   * If @seealso occurs, set \pagenumbertext
%
\def\splitindexentry#1{%
  \gdef\fullindexsortkey{}%
  \xdef\bracedtext{}%
  \def\sep{}%
  \def\seealso##1{}%
  \def\seeentry##1{}%
  \expandafter\doindexsegment#1\subentry\finish\subentry
}

% append the results from the next segment
\def\doindexsegment#1\subentry{%
  \def\segment{#1}%
  \ifx\segment\isfinish
  \else
    %
    % Fully expand the segment, throwing away any @sortas directives, and
    % trim spaces.
    \edef\trimmed{\segment}%
    \edef\trimmed{\expandafter\eatspaces\expandafter{\trimmed}}%
    \ifincodeindex
      \edef\trimmed{\noexpand\code{\trimmed}}%
    \fi
    %
    \xdef\bracedtext{\bracedtext{\trimmed}}%
    %
    % Get the string to sort by.  Process the segment with all
    % font commands turned off.
    \bgroup
      \let\sortas\indexwritesortas
      \let\seealso\indexwriteseealso
      \let\seeentry\indexwriteseeentry
      \indexnofonts
      % The braces around the commands are recognized by texindex.
      \def\lbracechar{{\string\indexlbrace}}%
      \def\rbracechar{{\string\indexrbrace}}%
      \let\{=\lbracechar
      \let\}=\rbracechar
      \def\@{{\string\indexatchar}}%
      \def\atchar##1{\@}%
      \def\backslashchar{{\string\indexbackslash}}%
      \uccode`\~=`\\ \uppercase{\let~\backslashchar}%
      %
      \let\indexsortkey\empty
      \global\let\pagenumbertext\empty
      % Execute the segment and throw away the typeset output.  This executes
      % any @sortas or @seealso commands in this segment.
      \setbox\dummybox = \hbox{\segment}%
      \ifx\indexsortkey\empty{%
        \indexnonalnumdisappear
        \xdef\trimmed{\segment}%
        \xdef\trimmed{\expandafter\eatspaces\expandafter{\trimmed}}%
        \xdef\indexsortkey{\trimmed}%
        \ifx\indexsortkey\empty\xdef\indexsortkey{ }\fi
      }\fi
      %
      % Append to \fullindexsortkey.
      \edef\tmp{\gdef\noexpand\fullindexsortkey{%
                  \fullindexsortkey\sep\indexsortkey}}%
      \tmp
    \egroup
    \def\sep{\subentry}%
    %
    \expandafter\doindexsegment
  \fi
}
\def\isfinish{\finish}%
\newbox\dummybox % used above

\let\subentry\relax

% Use \ instead of @ in index files.  To support old texi2dvi and texindex.
% This works without changing the escape character used in the toc or aux
% files because the index entries are fully expanded here, and \string uses
% the current value of \escapechar.
\def\escapeisbackslash{\escapechar=`\\}

% Use \ in index files by default.  texi2dvi didn't support @ as the escape
% character (as it checked for "\entry" in the files, and not "@entry").  When
% the new version of texi2dvi has had a chance to become more prevalent, then
% the escape character can change back to @ again.  This should be an easy
% change to make now because both @ and \ are only used as escape characters in
% index files, never standing for themselves.
%
\set txiindexescapeisbackslash

% Write the entry in \indextext to the index file.
%

\newif\ifincodeindex
\def\doindwrite{\incodeindexfalse\doindwritex}
\def\docindwrite{\incodeindextrue\doindwritex}

\def\doindwritex{%
  \maybemarginindex
  %
  \atdummies
  %
  \expandafter\ifx\csname SETtxiindexescapeisbackslash\endcsname\relax\else
    \escapeisbackslash
  \fi
  %
  % For texindex which always views { and } as separators.
  \def\{{\lbracechar{}}%
  \def\}{\rbracechar{}}%
  \uccode`\~=`\\ \uppercase{\def~{\backslashchar{}}}%
  %
  % Split the entry into primary entry and any subentries, and get the index
  % sort key.
  \splitindexentry\indextext
  %
  % Set up the complete index entry, with both the sort key and
  % the original text, including any font commands.  We write
  % three arguments to \entry to the .?? file (four in the
  % subentry case), texindex reduces to two when writing the .??s
  % sorted result.
  %
  \edef\temp{%
    \write\writeto{%
      \string\entry{\fullindexsortkey}%
        {\ifx\pagenumbertext\empty\noexpand\folio\else\pagenumbertext\fi}%
        \bracedtext}%
  }%
  \temp
}

% Put the index entry in the margin if desired (undocumented).
\def\maybemarginindex{%
  \ifx\SETmarginindex\relax\else
    \insert\margin{\hbox{\vrule height8pt depth3pt width0pt \relax\indextext}}%
  \fi
}
\let\SETmarginindex=\relax


% Take care of unwanted page breaks/skips around a whatsit:
%
% If a skip is the last thing on the list now, preserve it
% by backing up by \lastskip, doing the \write, then inserting
% the skip again.  Otherwise, the whatsit generated by the
% \write or \pdfdest will make \lastskip zero.  The result is that
% sequences like this:
% @end defun
% @tindex whatever
% @defun ...
% will have extra space inserted, because the \medbreak in the
% start of the @defun won't see the skip inserted by the @end of
% the previous defun.
%
% But don't do any of this if we're not in vertical mode.  We
% don't want to do a \vskip and prematurely end a paragraph.
%
% Avoid page breaks due to these extra skips, too.
%
% But wait, there is a catch there:
% We'll have to check whether \lastskip is zero skip.  \ifdim is not
% sufficient for this purpose, as it ignores stretch and shrink parts
% of the skip.  The only way seems to be to check the textual
% representation of the skip.
%
% The following is almost like \def\zeroskipmacro{0.0pt} except that
% the ``p'' and ``t'' characters have catcode \other, not 11 (letter).
%
\edef\zeroskipmacro{\expandafter\the\csname z@skip\endcsname}
%
\newskip\whatsitskip
\newcount\whatsitpenalty
%
% ..., ready, GO:
%
\def\safewhatsit#1{\ifhmode
  #1%
 \else
  % \lastskip and \lastpenalty cannot both be nonzero simultaneously.
  \whatsitskip = \lastskip
  \edef\lastskipmacro{\the\lastskip}%
  \whatsitpenalty = \lastpenalty
  %
  % If \lastskip is nonzero, that means the last item was a
  % skip.  And since a skip is discardable, that means this
  % -\whatsitskip glue we're inserting is preceded by a
  % non-discardable item, therefore it is not a potential
  % breakpoint, therefore no \nobreak needed.
  \ifx\lastskipmacro\zeroskipmacro
  \else
    \vskip-\whatsitskip
  \fi
  %
  #1%
  %
  \ifx\lastskipmacro\zeroskipmacro
    % If \lastskip was zero, perhaps the last item was a penalty, and
    % perhaps it was >=10000, e.g., a \nobreak.  In that case, we want
    % to re-insert the same penalty (values >10000 are used for various
    % signals); since we just inserted a non-discardable item, any
    % following glue (such as a \parskip) would be a breakpoint.  For example:
    %   @deffn deffn-whatever
    %   @vindex index-whatever
    %   Description.
    % would allow a break between the index-whatever whatsit
    % and the "Description." paragraph.
    \ifnum\whatsitpenalty>9999 \penalty\whatsitpenalty \fi
  \else
    % On the other hand, if we had a nonzero \lastskip,
    % this make-up glue would be preceded by a non-discardable item
    % (the whatsit from the \write), so we must insert a \nobreak.
    \nobreak\vskip\whatsitskip
  \fi
\fi}

% The index entry written in the file actually looks like
%  \entry {sortstring}{page}{topic}
% or
%  \entry {sortstring}{page}{topic}{subtopic}
% The texindex program reads in these files and writes files
% containing these kinds of lines:
%  \initial {c}
%     before the first topic whose initial is c
%  \entry {topic}{pagelist}
%     for a topic that is used without subtopics
%  \primary {topic}
%  \entry {topic}{}
%     for the beginning of a topic that is used with subtopics
%  \secondary {subtopic}{pagelist}
%     for each subtopic.
%  \secondary {subtopic}{}
%     for a subtopic with sub-subtopics
%  \tertiary {subtopic}{subsubtopic}{pagelist}
%     for each sub-subtopic.

% Define the user-accessible indexing commands
% @findex, @vindex, @kindex, @cindex.

\def\findex {\fnindex}
\def\kindex {\kyindex}
\def\cindex {\cpindex}
\def\vindex {\vrindex}
\def\tindex {\tpindex}
\def\pindex {\pgindex}

% Define the macros used in formatting output of the sorted index material.

% @printindex causes a particular index (the ??s file) to get printed.
% It does not print any chapter heading (usually an @unnumbered).
%
\parseargdef\printindex{\begingroup
  \dobreak \chapheadingskip{10000}%
  %
  \smallfonts \rm
  \tolerance = 9500
  \plainfrenchspacing
  \everypar = {}% don't want the \kern\-parindent from indentation suppression.
  %
  % See comment in \requireopenindexfile.
  \def\indexname{#1}\ifx\indexname\indexisfl\def\indexname{f1}\fi
  %
  % See if the index file exists and is nonempty.
  \openin 1 \jobname.\indexname s
  \ifeof 1
    % \enddoublecolumns gets confused if there is no text in the index,
    % and it loses the chapter title and the aux file entries for the
    % index.  The easiest way to prevent this problem is to make sure
    % there is some text.
    \putwordIndexNonexistent
    \typeout{No file \jobname.\indexname s.}%
  \else
    % If the index file exists but is empty, then \openin leaves \ifeof
    % false.  We have to make TeX try to read something from the file, so
    % it can discover if there is anything in it.
    \read 1 to \thisline
    \ifeof 1
      \putwordIndexIsEmpty
    \else
      \expandafter\printindexzz\thisline\relax\relax\finish%
    \fi
  \fi
  \closein 1
\endgroup}

% If the index file starts with a backslash, forgo reading the index
% file altogether.  If somebody upgrades texinfo.tex they may still have
% old index files using \ as the escape character.  Reading this would
% at best lead to typesetting garbage, at worst a TeX syntax error.
\def\printindexzz#1#2\finish{%
  \expandafter\ifx\csname SETtxiindexescapeisbackslash\endcsname\relax
    \uccode`\~=`\\ \uppercase{\if\noexpand~}\noexpand#1
      \expandafter\ifx\csname SETtxiskipindexfileswithbackslash\endcsname\relax
\errmessage{%
ERROR: A sorted index file in an obsolete format was skipped.
To fix this problem, please upgrade your version of 'texi2dvi'
or 'texi2pdf' to that at <https://ftp.gnu.org/gnu/texinfo>.
If you are using an old version of 'texindex' (part of the Texinfo
distribution), you may also need to upgrade to a newer version (at least 6.0).
You may be able to typeset the index if you run
'texindex \jobname.\indexname' yourself.
You could also try setting the 'txiindexescapeisbackslash' flag by
running a command like
'texi2dvi -t "@set txiindexescapeisbackslash" \jobname.texi'.  If you do
this, Texinfo will try to use index files in the old format.
If you continue to have problems, deleting the index files and starting again
might help (with 'rm \jobname.?? \jobname.??s')%
}%
      \else
        (Skipped sorted index file in obsolete format)
      \fi
    \else
      \begindoublecolumns
      \input \jobname.\indexname s
      \enddoublecolumns
    \fi
  \else
    \begindoublecolumns
    \catcode`\\=0\relax
    %
    % Make @ an escape character to give macros a chance to work.  This
    % should work because we (hopefully) don't otherwise use @ in index files.
    %\catcode`\@=12\relax
    \catcode`\@=0\relax
    \input \jobname.\indexname s
    \enddoublecolumns
  \fi
}

% These macros are used by the sorted index file itself.
% Change them to control the appearance of the index.

{\catcode`\/=13 \catcode`\-=13 \catcode`\^=13 \catcode`\~=13 \catcode`\_=13
\catcode`\|=13 \catcode`\<=13 \catcode`\>=13 \catcode`\+=13 \catcode`\"=13
\catcode`\$=3
\gdef\initialglyphs{%
  % special control sequences used in the index sort key
  \let\indexlbrace\{%
  \let\indexrbrace\}%
  \let\indexatchar\@%
  \def\indexbackslash{\math{\backslash}}%
  %
  % Some changes for non-alphabetic characters.  Using the glyphs from the
  % math fonts looks more consistent than the typewriter font used elsewhere
  % for these characters.
  \uccode`\~=`\\ \uppercase{\def~{\math{\backslash}}}
  %
  % In case @\ is used for backslash
  \uppercase{\let\\=~}
  % Can't get bold backslash so don't use bold forward slash
  \catcode`\/=13
  \def/{{\secrmnotbold \normalslash}}%
  \def-{{\normaldash\normaldash}}% en dash `--'
  \def^{{\chapbf \normalcaret}}%
  \def~{{\chapbf \normaltilde}}%
  \def\_{%
     \leavevmode \kern.07em \vbox{\hrule width.3em height.1ex}\kern .07em }%
  \def|{$\vert$}%
  \def<{$\less$}%
  \def>{$\gtr$}%
  \def+{$\normalplus$}%
}}

\def\initial{%
  \bgroup
  \initialglyphs
  \initialx
}

\def\initialx#1{%
  % Remove any glue we may have, we'll be inserting our own.
  \removelastskip
  %
  % We like breaks before the index initials, so insert a bonus.
  % The glue before the bonus allows a little bit of space at the
  % bottom of a column to reduce an increase in inter-line spacing.
  \nobreak
  \vskip 0pt plus 5\baselineskip
  \penalty -300
  \vskip 0pt plus -5\baselineskip
  %
  % Typeset the initial.  Making this add up to a whole number of
  % baselineskips increases the chance of the dots lining up from column
  % to column.  It still won't often be perfect, because of the stretch
  % we need before each entry, but it's better.
  %
  % No shrink because it confuses \balancecolumns.
  \vskip 1.67\baselineskip plus 1\baselineskip
  \leftline{\secfonts \kern-0.05em \secbf #1}%
  % \secfonts is inside the argument of \leftline so that the change of
  % \baselineskip will not affect any glue inserted before the vbox that
  % \leftline creates.
  % Do our best not to break after the initial.
  \nobreak
  \vskip .33\baselineskip plus .1\baselineskip
  \egroup % \initialglyphs
}

\newdimen\entryrightmargin
\entryrightmargin=0pt

% \entry typesets a paragraph consisting of the text (#1), dot leaders, and
% then page number (#2) flushed to the right margin.  It is used for index
% and table of contents entries.  The paragraph is indented by \leftskip.
%
\def\entry{%
  \begingroup
    %
    % Start a new paragraph if necessary, so our assignments below can't
    % affect previous text.
    \par
    %
    % No extra space above this paragraph.
    \parskip = 0in
    %
    % When reading the text of entry, convert explicit line breaks
    % from @* into spaces.  The user might give these in long section
    % titles, for instance.
    \def\*{\unskip\space\ignorespaces}%
    \def\entrybreak{\hfil\break}% An undocumented command
    %
    % Swallow the left brace of the text (first parameter):
    \afterassignment\doentry
    \let\temp =
}
\def\entrybreak{\unskip\space\ignorespaces}%
\def\doentry{%
    % Save the text of the entry
    \global\setbox\boxA=\hbox\bgroup
    \bgroup % Instead of the swallowed brace.
      \noindent
      \aftergroup\finishentry
      % And now comes the text of the entry.
      % Not absorbing as a macro argument reduces the chance of problems
      % with catcodes occurring.
}
{\catcode`\@=11
\gdef\finishentry#1{%
    \egroup % end box A
    \dimen@ = \wd\boxA % Length of text of entry
    \global\setbox\boxA=\hbox\bgroup
      \unhbox\boxA
      % #1 is the page number.
      %
      % Get the width of the page numbers, and only use
      % leaders if they are present.
      \global\setbox\boxB = \hbox{#1}%
      \ifdim\wd\boxB = 0pt
        \null\nobreak\hfill\ %
      \else
        %
        \null\nobreak\indexdotfill % Have leaders before the page number.
        %
        \ifpdforxetex
          \pdfgettoks#1.%
          \hskip\skip\thinshrinkable\the\toksA
        \else
          \hskip\skip\thinshrinkable #1%
        \fi
      \fi
    \egroup % end \boxA
    \ifdim\wd\boxB = 0pt
      \noindent\unhbox\boxA\par
      \nobreak
    \else\bgroup
      % We want the text of the entries to be aligned to the left, and the
      % page numbers to be aligned to the right.
      %
      \parindent = 0pt
      \advance\leftskip by 0pt plus 1fil
      \advance\leftskip by 0pt plus -1fill
      \rightskip = 0pt plus -1fil
      \advance\rightskip by 0pt plus 1fill
      % Cause last line, which could consist of page numbers on their own
      % if the list of page numbers is long, to be aligned to the right.
      \parfillskip=0pt plus -1fill
      %
      \advance\rightskip by \entryrightmargin
      % Determine how far we can stretch into the margin.
      % This allows, e.g., "Appendix H  GNU Free Documentation License" to
      % fit on one line in @letterpaper format.
      \ifdim\entryrightmargin>2.1em
        \dimen@i=2.1em
      \else
        \dimen@i=0em
      \fi
      \advance \parfillskip by 0pt minus 1\dimen@i
      %
      \dimen@ii = \hsize
      \advance\dimen@ii by -1\leftskip
      \advance\dimen@ii by -1\entryrightmargin
      \advance\dimen@ii by 1\dimen@i
      \ifdim\wd\boxA > \dimen@ii % If the entry doesn't fit in one line
      \ifdim\dimen@ > 0.8\dimen@ii   % due to long index text
        % Try to split the text roughly evenly.  \dimen@ will be the length of
        % the first line.
        \dimen@ = 0.7\dimen@
        \dimen@ii = \hsize
        \ifnum\dimen@>\dimen@ii
          % If the entry is too long (for example, if it needs more than
          % two lines), use all the space in the first line.
          \dimen@ = \dimen@ii
        \fi
        \advance\leftskip by 0pt plus 1fill % ragged right
        \advance \dimen@ by 1\rightskip
        \parshape = 2 0pt \dimen@ 0em \dimen@ii
        % Ideally we'd add a finite glue at the end of the first line only,
        % instead of using \parshape with explicit line lengths, but TeX
        % doesn't seem to provide a way to do such a thing.
        %
        % Indent all lines but the first one.
        \advance\leftskip by 1em
        \advance\parindent by -1em
      \fi\fi
      \indent % start paragraph
      \unhbox\boxA
      %
      % Do not prefer a separate line ending with a hyphen to fewer lines.
      \finalhyphendemerits = 0
      %
      % Word spacing - no stretch
      \spaceskip=\fontdimen2\font minus \fontdimen4\font
      %
      \linepenalty=1000  % Discourage line breaks.
      \hyphenpenalty=5000  % Discourage hyphenation.
      %
      \par % format the paragraph
    \egroup % The \vbox
    \fi
  \endgroup
}}

\newskip\thinshrinkable
\skip\thinshrinkable=.15em minus .15em

% Like plain.tex's \dotfill, except uses up at least 1 em.
% The filll stretch here overpowers both the fil and fill stretch to push
% the page number to the right.
\def\indexdotfill{\cleaders
  \hbox{$\mathsurround=0pt \mkern1.5mu.\mkern1.5mu$}\hskip 1em plus 1filll}


\def\primary #1{\line{#1\hfil}}

\def\secondary{\indententry{0.5cm}}
\def\tertiary{\indententry{1cm}}

\def\indententry#1#2#3{%
  \bgroup
  \leftskip=#1
  \entry{#2}{#3}%
  \egroup
}

% Define two-column mode, which we use to typeset indexes.
% Adapted from the TeXbook, page 416, which is to say,
% the manmac.tex format used to print the TeXbook itself.
\catcode`\@=11  % private names

\newbox\partialpage
\newdimen\doublecolumnhsize

\def\begindoublecolumns{\begingroup % ended by \enddoublecolumns
  % If not much space left on page, start a new page.
  \ifdim\pagetotal>0.8\vsize\vfill\eject\fi
  %
  % Grab any single-column material above us.
  \output = {%
    \savetopmark
    %
    \global\setbox\partialpage = \vbox{%
      % Unvbox the main output page.
      \unvbox\PAGE
      \kern-\topskip \kern\baselineskip
    }%
  }%
  \eject % run that output routine to set \partialpage
  %
  % Use the double-column output routine for subsequent pages.
  \output = {\doublecolumnout}%
  %
  % Change the page size parameters.  We could do this once outside this
  % routine, in each of @smallbook, @afourpaper, and the default 8.5x11
  % format, but then we repeat the same computation.  Repeating a couple
  % of assignments once per index is clearly meaningless for the
  % execution time, so we may as well do it in one place.
  %
  % First we halve the line length, less a little for the gutter between
  % the columns.  We compute the gutter based on the line length, so it
  % changes automatically with the paper format.  The magic constant
  % below is chosen so that the gutter has the same value (well, +-<1pt)
  % as it did when we hard-coded it.
  %
  % We put the result in a separate register, \doublecolumhsize, so we
  % can restore it in \pagesofar, after \hsize itself has (potentially)
  % been clobbered.
  %
  \doublecolumnhsize = \hsize
    \advance\doublecolumnhsize by -.04154\hsize
    \divide\doublecolumnhsize by 2
  \hsize = \doublecolumnhsize
  %
  % Get the available space for the double columns -- the normal
  % (undoubled) page height minus any material left over from the
  % previous page.
  \advance\vsize by -\ht\partialpage
  \vsize = 2\vsize
  %
  % For the benefit of balancing columns
  \advance\baselineskip by 0pt plus 0.5pt
}

% The double-column output routine for all double-column pages except
% the last, which is done by \balancecolumns.
%
\def\doublecolumnout{%
  %
  \savetopmark
  \splittopskip=\topskip \splitmaxdepth=\maxdepth
  \dimen@ = \vsize
  \divide\dimen@ by 2
  %
  % box0 will be the left-hand column, box2 the right.
  \setbox0=\vsplit\PAGE to\dimen@ \setbox2=\vsplit\PAGE to\dimen@
  \global\advance\vsize by 2\ht\partialpage
  \onepageout\pagesofar % empty except for the first time we are called
  \unvbox\PAGE
  \penalty\outputpenalty
}
%
% Re-output the contents of the output page -- any previous material,
% followed by the two boxes we just split, in box0 and box2.
\def\pagesofar{%
  \unvbox\partialpage
  %
  \hsize = \doublecolumnhsize
  \wd0=\hsize \wd2=\hsize
  \hbox to\txipagewidth{\box0\hfil\box2}%
}


% Finished with double columns.
\def\enddoublecolumns{%
  % The following penalty ensures that the page builder is exercised
  % _before_ we change the output routine.  This is necessary in the
  % following situation:
  %
  % The last section of the index consists only of a single entry.
  % Before this section, \pagetotal is less than \pagegoal, so no
  % break occurs before the last section starts.  However, the last
  % section, consisting of \initial and the single \entry, does not
  % fit on the page and has to be broken off.  Without the following
  % penalty the page builder will not be exercised until \eject
  % below, and by that time we'll already have changed the output
  % routine to the \balancecolumns version, so the next-to-last
  % double-column page will be processed with \balancecolumns, which
  % is wrong:  The two columns will go to the main vertical list, with
  % the broken-off section in the recent contributions.  As soon as
  % the output routine finishes, TeX starts reconsidering the page
  % break.  The two columns and the broken-off section both fit on the
  % page, because the two columns now take up only half of the page
  % goal.  When TeX sees \eject from below which follows the final
  % section, it invokes the new output routine that we've set after
  % \balancecolumns below; \onepageout will try to fit the two columns
  % and the final section into the vbox of \txipageheight (see
  % \pagebody), causing an overfull box.
  %
  % Note that glue won't work here, because glue does not exercise the
  % page builder, unlike penalties (see The TeXbook, pp. 280-281).
  \penalty0
  %
  \output = {%
    % Split the last of the double-column material.
    \savetopmark
    \balancecolumns
  }%
  \eject % call the \output just set
  \ifdim\pagetotal=0pt
    % Having called \balancecolumns once, we do not
    % want to call it again.  Therefore, reset \output to its normal
    % definition right away.
    \global\output=\expandafter{\the\defaultoutput}
    %
    \endgroup % started in \begindoublecolumns
    % Leave the double-column material on the current page, no automatic
    % page break.
    \box\balancedcolumns
    %
    % \pagegoal was set to the doubled \vsize above, since we restarted
    % the current page.  We're now back to normal single-column
    % typesetting, so reset \pagegoal to the normal \vsize.
    \global\vsize = \txipageheight %
    \pagegoal = \txipageheight %
  \else
    % We had some left-over material.  This might happen when \doublecolumnout
    % is called in \balancecolumns.  Try again.
    \expandafter\enddoublecolumns
  \fi
}
\newbox\balancedcolumns
\setbox\balancedcolumns=\vbox{shouldnt see this}%
%
% Only called for the last of the double column material.  \doublecolumnout
% does the others.
\def\balancecolumns{%
  \setbox0 = \vbox{\unvbox\PAGE}% like \box255 but more efficient, see p.120.
  \dimen@ = \ht0
  \ifdim\dimen@<7\baselineskip
    % Don't split a short final column in two.
    \setbox2=\vbox{}%
    \global\setbox\balancedcolumns=\vbox{\pagesofar}%
  \else
    % double the leading vertical space
    \advance\dimen@ by \topskip
    \advance\dimen@ by-\baselineskip
    \divide\dimen@ by 2 % target to split to
    \dimen@ii = \dimen@
    \splittopskip = \topskip
    % Loop until left column is at least as high as the right column.
    {%
      \vbadness = 10000
      \loop
        \global\setbox3 = \copy0
        \global\setbox1 = \vsplit3 to \dimen@
      \ifdim\ht1<\ht3
        \global\advance\dimen@ by 1pt
      \repeat
    }%
    % Now the left column is in box 1, and the right column in box 3.
    %
    % Check whether the left column has come out higher than the page itself.
    % (Note that we have doubled \vsize for the double columns, so
    % the actual height of the page is 0.5\vsize).
    \ifdim2\ht1>\vsize
      % It appears that we have been called upon to balance too much material.
      % Output some of it with \doublecolumnout, leaving the rest on the page.
      \setbox\PAGE=\box0
      \doublecolumnout
    \else
      % Compare the heights of the two columns.
      \ifdim4\ht1>5\ht3
        % Column heights are too different, so don't make their bottoms
        % flush with each other.
        \setbox2=\vbox to \ht1 {\unvbox3\vfill}%
        \setbox0=\vbox to \ht1 {\unvbox1\vfill}%
      \else
        % Make column bottoms flush with each other.
        \setbox2=\vbox to\ht1{\unvbox3\unskip}%
        \setbox0=\vbox to\ht1{\unvbox1\unskip}%
      \fi
      \global\setbox\balancedcolumns=\vbox{\pagesofar}%
    \fi
  \fi
  %
}
\catcode`\@ = \other


\message{sectioning,}
% Chapters, sections, etc.

% Let's start with @part.
\outer\parseargdef\part{\partzzz{#1}}
\def\partzzz#1{%
  \chapoddpage
  \null
  \vskip.3\vsize  % move it down on the page a bit
  \begingroup
    \noindent \titlefonts\rm #1\par % the text
    \let\lastnode=\empty      % no node to associate with
    \writetocentry{part}{#1}{}% but put it in the toc
    \headingsoff              % no headline or footline on the part page
    % This outputs a mark at the end of the page that clears \thischapter
    % and \thissection, as is done in \startcontents.
    \let\pchapsepmacro\relax
    \chapmacro{}{Yomitfromtoc}{}%
    \chapoddpage
  \endgroup
}

% \unnumberedno is an oxymoron.  But we count the unnumbered
% sections so that we can refer to them unambiguously in the pdf
% outlines by their "section number".  We avoid collisions with chapter
% numbers by starting them at 10000.  (If a document ever has 10000
% chapters, we're in trouble anyway, I'm sure.)
\newcount\unnumberedno \unnumberedno = 10000
\newcount\chapno
\newcount\secno        \secno=0
\newcount\subsecno     \subsecno=0
\newcount\subsubsecno  \subsubsecno=0

% This counter is funny since it counts through charcodes of letters A, B, ...
\newcount\appendixno  \appendixno = `\@
%
% \def\appendixletter{\char\the\appendixno}
% We do the following ugly conditional instead of the above simple
% construct for the sake of pdftex, which needs the actual
% letter in the expansion, not just typeset.
%
\def\appendixletter{%
  \ifnum\appendixno=`A A%
  \else\ifnum\appendixno=`B B%
  \else\ifnum\appendixno=`C C%
  \else\ifnum\appendixno=`D D%
  \else\ifnum\appendixno=`E E%
  \else\ifnum\appendixno=`F F%
  \else\ifnum\appendixno=`G G%
  \else\ifnum\appendixno=`H H%
  \else\ifnum\appendixno=`I I%
  \else\ifnum\appendixno=`J J%
  \else\ifnum\appendixno=`K K%
  \else\ifnum\appendixno=`L L%
  \else\ifnum\appendixno=`M M%
  \else\ifnum\appendixno=`N N%
  \else\ifnum\appendixno=`O O%
  \else\ifnum\appendixno=`P P%
  \else\ifnum\appendixno=`Q Q%
  \else\ifnum\appendixno=`R R%
  \else\ifnum\appendixno=`S S%
  \else\ifnum\appendixno=`T T%
  \else\ifnum\appendixno=`U U%
  \else\ifnum\appendixno=`V V%
  \else\ifnum\appendixno=`W W%
  \else\ifnum\appendixno=`X X%
  \else\ifnum\appendixno=`Y Y%
  \else\ifnum\appendixno=`Z Z%
  % The \the is necessary, despite appearances, because \appendixletter is
  % expanded while writing the .toc file.  \char\appendixno is not
  % expandable, thus it is written literally, thus all appendixes come out
  % with the same letter (or @) in the toc without it.
  \else\char\the\appendixno
  \fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi
  \fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi\fi}

% Each @chapter defines these (using marks) as the number+name, number
% and name of the chapter.  Page headings and footings can use
% these.  @section does likewise.
\def\thischapter{}
\def\thischapternum{}
\def\thischaptername{}
\def\thissection{}
\def\thissectionnum{}
\def\thissectionname{}

\newcount\absseclevel % used to calculate proper heading level
\newcount\secbase\secbase=0 % @raisesections/@lowersections modify this count

% @raisesections: treat @section as chapter, @subsection as section, etc.
\def\raisesections{\global\advance\secbase by -1}

% @lowersections: treat @chapter as section, @section as subsection, etc.
\def\lowersections{\global\advance\secbase by 1}

% we only have subsub.
\chardef\maxseclevel = 3
%
% A numbered section within an unnumbered changes to unnumbered too.
% To achieve this, remember the "biggest" unnum. sec. we are currently in:
\chardef\unnlevel = \maxseclevel
%
% Trace whether the current chapter is an appendix or not:
% \chapheadtype is "N" or "A", unnumbered chapters are ignored.
\def\chapheadtype{N}

% Choose a heading macro
% #1 is heading type
% #2 is heading level
% #3 is text for heading
\def\genhead#1#2#3{%
  % Compute the abs. sec. level:
  \absseclevel=#2
  \advance\absseclevel by \secbase
  % Make sure \absseclevel doesn't fall outside the range:
  \ifnum \absseclevel < 0
    \absseclevel = 0
  \else
    \ifnum \absseclevel > 3
      \absseclevel = 3
    \fi
  \fi
  % The heading type:
  \def\headtype{#1}%
  \if \headtype U%
    \ifnum \absseclevel < \unnlevel
      \chardef\unnlevel = \absseclevel
    \fi
  \else
    % Check for appendix sections:
    \ifnum \absseclevel = 0
      \edef\chapheadtype{\headtype}%
    \else
      \if \headtype A\if \chapheadtype N%
	\errmessage{@appendix... within a non-appendix chapter}%
      \fi\fi
    \fi
    % Check for numbered within unnumbered:
    \ifnum \absseclevel > \unnlevel
      \def\headtype{U}%
    \else
      \chardef\unnlevel = 3
    \fi
  \fi
  % Now print the heading:
  \if \headtype U%
    \ifcase\absseclevel
	\unnumberedzzz{#3}%
    \or \unnumberedseczzz{#3}%
    \or \unnumberedsubseczzz{#3}%
    \or \unnumberedsubsubseczzz{#3}%
    \fi
  \else
    \if \headtype A%
      \ifcase\absseclevel
	  \appendixzzz{#3}%
      \or \appendixsectionzzz{#3}%
      \or \appendixsubseczzz{#3}%
      \or \appendixsubsubseczzz{#3}%
      \fi
    \else
      \ifcase\absseclevel
	  \chapterzzz{#3}%
      \or \seczzz{#3}%
      \or \numberedsubseczzz{#3}%
      \or \numberedsubsubseczzz{#3}%
      \fi
    \fi
  \fi
  \suppressfirstparagraphindent
}

% an interface:
\def\numhead{\genhead N}
\def\apphead{\genhead A}
\def\unnmhead{\genhead U}

% @chapter, @appendix, @unnumbered.  Increment top-level counter, reset
% all lower-level sectioning counters to zero.
%
% Also set \chaplevelprefix, which we prepend to @float sequence numbers
% (e.g., figures), q.v.  By default (before any chapter), that is empty.
\let\chaplevelprefix = \empty
%
\outer\parseargdef\chapter{\numhead0{#1}} % normally numhead0 calls chapterzzz
\def\chapterzzz#1{%
  % section resetting is \global in case the chapter is in a group, such
  % as an @include file.
  \global\secno=0 \global\subsecno=0 \global\subsubsecno=0
    \global\advance\chapno by 1
  %
  % Used for \float.
  \gdef\chaplevelprefix{\the\chapno.}%
  \resetallfloatnos
  %
  % \putwordChapter can contain complex things in translations.
  \toks0=\expandafter{\putwordChapter}%
  \message{\the\toks0 \space \the\chapno}%
  %
  % Write the actual heading.
  \chapmacro{#1}{Ynumbered}{\the\chapno}%
  %
  % So @section and the like are numbered underneath this chapter.
  \global\let\section = \numberedsec
  \global\let\subsection = \numberedsubsec
  \global\let\subsubsection = \numberedsubsubsec
}

\outer\parseargdef\appendix{\apphead0{#1}} % normally calls appendixzzz
%
\def\appendixzzz#1{%
  \global\secno=0 \global\subsecno=0 \global\subsubsecno=0
    \global\advance\appendixno by 1
  \gdef\chaplevelprefix{\appendixletter.}%
  \resetallfloatnos
  %
  % \putwordAppendix can contain complex things in translations.
  \toks0=\expandafter{\putwordAppendix}%
  \message{\the\toks0 \space \appendixletter}%
  %
  \chapmacro{#1}{Yappendix}{\appendixletter}%
  %
  \global\let\section = \appendixsec
  \global\let\subsection = \appendixsubsec
  \global\let\subsubsection = \appendixsubsubsec
}

% normally unnmhead0 calls unnumberedzzz:
\outer\parseargdef\unnumbered{\unnmhead0{#1}}
\def\unnumberedzzz#1{%
  \global\secno=0 \global\subsecno=0 \global\subsubsecno=0
    \global\advance\unnumberedno by 1
  %
  % Since an unnumbered has no number, no prefix for figures.
  \global\let\chaplevelprefix = \empty
  \resetallfloatnos
  %
  % This used to be simply \message{#1}, but TeX fully expands the
  % argument to \message.  Therefore, if #1 contained @-commands, TeX
  % expanded them.  For example, in `@unnumbered The @cite{Book}', TeX
  % expanded @cite (which turns out to cause errors because \cite is meant
  % to be executed, not expanded).
  %
  % Anyway, we don't want the fully-expanded definition of @cite to appear
  % as a result of the \message, we just want `@cite' itself.  We use
  % \the<toks register> to achieve this: TeX expands \the<toks> only once,
  % simply yielding the contents of <toks register>.  (We also do this for
  % the toc entries.)
  \toks0 = {#1}%
  \message{(\the\toks0)}%
  %
  \chapmacro{#1}{Ynothing}{\the\unnumberedno}%
  %
  \global\let\section = \unnumberedsec
  \global\let\subsection = \unnumberedsubsec
  \global\let\subsubsection = \unnumberedsubsubsec
}

% @centerchap is like @unnumbered, but the heading is centered.
\outer\parseargdef\centerchap{%
  \let\centerparametersmaybe = \centerparameters
  \unnmhead0{#1}%
  \let\centerparametersmaybe = \relax
}

% @top is like @unnumbered.
\let\top\unnumbered

% Sections.
%
\outer\parseargdef\numberedsec{\numhead1{#1}} % normally calls seczzz
\def\seczzz#1{%
  \global\subsecno=0 \global\subsubsecno=0  \global\advance\secno by 1
  \sectionheading{#1}{sec}{Ynumbered}{\the\chapno.\the\secno}%
}

% normally calls appendixsectionzzz:
\outer\parseargdef\appendixsection{\apphead1{#1}}
\def\appendixsectionzzz#1{%
  \global\subsecno=0 \global\subsubsecno=0  \global\advance\secno by 1
  \sectionheading{#1}{sec}{Yappendix}{\appendixletter.\the\secno}%
}
\let\appendixsec\appendixsection

% normally calls unnumberedseczzz:
\outer\parseargdef\unnumberedsec{\unnmhead1{#1}}
\def\unnumberedseczzz#1{%
  \global\subsecno=0 \global\subsubsecno=0  \global\advance\secno by 1
  \sectionheading{#1}{sec}{Ynothing}{\the\unnumberedno.\the\secno}%
}

% Subsections.
%
% normally calls numberedsubseczzz:
\outer\parseargdef\numberedsubsec{\numhead2{#1}}
\def\numberedsubseczzz#1{%
  \global\subsubsecno=0  \global\advance\subsecno by 1
  \sectionheading{#1}{subsec}{Ynumbered}{\the\chapno.\the\secno.\the\subsecno}%
}

% normally calls appendixsubseczzz:
\outer\parseargdef\appendixsubsec{\apphead2{#1}}
\def\appendixsubseczzz#1{%
  \global\subsubsecno=0  \global\advance\subsecno by 1
  \sectionheading{#1}{subsec}{Yappendix}%
                 {\appendixletter.\the\secno.\the\subsecno}%
}

% normally calls unnumberedsubseczzz:
\outer\parseargdef\unnumberedsubsec{\unnmhead2{#1}}
\def\unnumberedsubseczzz#1{%
  \global\subsubsecno=0  \global\advance\subsecno by 1
  \sectionheading{#1}{subsec}{Ynothing}%
                 {\the\unnumberedno.\the\secno.\the\subsecno}%
}

% Subsubsections.
%
% normally numberedsubsubseczzz:
\outer\parseargdef\numberedsubsubsec{\numhead3{#1}}
\def\numberedsubsubseczzz#1{%
  \global\advance\subsubsecno by 1
  \sectionheading{#1}{subsubsec}{Ynumbered}%
                 {\the\chapno.\the\secno.\the\subsecno.\the\subsubsecno}%
}

% normally appendixsubsubseczzz:
\outer\parseargdef\appendixsubsubsec{\apphead3{#1}}
\def\appendixsubsubseczzz#1{%
  \global\advance\subsubsecno by 1
  \sectionheading{#1}{subsubsec}{Yappendix}%
                 {\appendixletter.\the\secno.\the\subsecno.\the\subsubsecno}%
}

% normally unnumberedsubsubseczzz:
\outer\parseargdef\unnumberedsubsubsec{\unnmhead3{#1}}
\def\unnumberedsubsubseczzz#1{%
  \global\advance\subsubsecno by 1
  \sectionheading{#1}{subsubsec}{Ynothing}%
                 {\the\unnumberedno.\the\secno.\the\subsecno.\the\subsubsecno}%
}

% These macros control what the section commands do, according
% to what kind of chapter we are in (ordinary, appendix, or unnumbered).
% Define them by default for a numbered chapter.
\let\section = \numberedsec
\let\subsection = \numberedsubsec
\let\subsubsection = \numberedsubsubsec

% Define @majorheading, @heading and @subheading

\def\majorheading{%
  {\advance\chapheadingskip by 10pt \chapbreak }%
  \parsearg\chapheadingzzz
}

\def\chapheading{\chapbreak \parsearg\chapheadingzzz}
\def\chapheadingzzz#1{%
  \vbox{\chapfonts \raggedtitlesettings #1\par}%
  \nobreak\bigskip \nobreak
  \suppressfirstparagraphindent
}

% @heading, @subheading, @subsubheading.
\parseargdef\heading{\sectionheading{#1}{sec}{Yomitfromtoc}{}
  \suppressfirstparagraphindent}
\parseargdef\subheading{\sectionheading{#1}{subsec}{Yomitfromtoc}{}
  \suppressfirstparagraphindent}
\parseargdef\subsubheading{\sectionheading{#1}{subsubsec}{Yomitfromtoc}{}
  \suppressfirstparagraphindent}

% These macros generate a chapter, section, etc. heading only
% (including whitespace, linebreaking, etc. around it),
% given all the information in convenient, parsed form.

% Args are the skip and penalty (usually negative)
\def\dobreak#1#2{\par\ifdim\lastskip<#1\removelastskip\penalty#2\vskip#1\fi}

% Parameter controlling skip before chapter headings (if needed)
\newskip\chapheadingskip

% Define plain chapter starts, and page on/off switching for it.
\def\chapbreak{\dobreak \chapheadingskip {-4000}}

% Start a new page
\def\chappager{\par\vfill\supereject}

% \chapoddpage - start on an odd page for a new chapter
% Because \domark is called before \chapoddpage, the filler page will
% get the headings for the next chapter, which is wrong.  But we don't
% care -- we just disable all headings on the filler page.
\def\chapoddpage{%
  \chappager
  \ifodd\pageno \else
    \begingroup
      \headingsoff
      \null
      \chappager
    \endgroup
  \fi
}

\parseargdef\setchapternewpage{\csname CHAPPAG#1\endcsname}

\def\CHAPPAGoff{%
\global\let\contentsalignmacro = \chappager
\global\let\pchapsepmacro=\chapbreak
\global\def\HEADINGSon{\HEADINGSsinglechapoff}}

\def\CHAPPAGon{%
\global\let\contentsalignmacro = \chappager
\global\let\pchapsepmacro=\chappager
\global\def\HEADINGSon{\HEADINGSsingle}}

\def\CHAPPAGodd{%
\global\let\contentsalignmacro = \chapoddpage
\global\let\pchapsepmacro=\chapoddpage
\global\def\HEADINGSon{\HEADINGSdouble}}

\CHAPPAGon

% \chapmacro - Chapter opening.
%
% #1 is the text, #2 is the section type (Ynumbered, Ynothing,
% Yappendix, Yomitfromtoc), #3 the chapter number.
% Not used for @heading series.
%
% To test against our argument.
\def\Ynothingkeyword{Ynothing}
\def\Yappendixkeyword{Yappendix}
\def\Yomitfromtockeyword{Yomitfromtoc}
%
\def\chapmacro#1#2#3{%
  \expandafter\ifx\thisenv\titlepage\else
    \checkenv{}% chapters, etc., should not start inside an environment.
  \fi
  % Insert the first mark before the heading break (see notes for \domark).
  \let\prevchapterdefs=\currentchapterdefs
  \let\prevsectiondefs=\currentsectiondefs
  \gdef\currentsectiondefs{\gdef\thissectionname{}\gdef\thissectionnum{}%
                        \gdef\thissection{}}%
  %
  \def\temptype{#2}%
  \ifx\temptype\Ynothingkeyword
    \gdef\currentchapterdefs{\gdef\thischaptername{#1}\gdef\thischapternum{}%
                          \gdef\thischapter{\thischaptername}}%
  \else\ifx\temptype\Yomitfromtockeyword
    \gdef\currentchapterdefs{\gdef\thischaptername{#1}\gdef\thischapternum{}%
                          \gdef\thischapter{}}%
  \else\ifx\temptype\Yappendixkeyword
    \toks0={#1}%
    \xdef\currentchapterdefs{%
      \gdef\noexpand\thischaptername{\the\toks0}%
      \gdef\noexpand\thischapternum{\appendixletter}%
      % \noexpand\putwordAppendix avoids expanding indigestible
      % commands in some of the translations.
      \gdef\noexpand\thischapter{\noexpand\putwordAppendix{}
                                 \noexpand\thischapternum:
                                 \noexpand\thischaptername}%
    }%
  \else
    \toks0={#1}%
    \xdef\currentchapterdefs{%
      \gdef\noexpand\thischaptername{\the\toks0}%
      \gdef\noexpand\thischapternum{\the\chapno}%
      % \noexpand\putwordChapter avoids expanding indigestible
      % commands in some of the translations.
      \gdef\noexpand\thischapter{\noexpand\putwordChapter{}
                                 \noexpand\thischapternum:
                                 \noexpand\thischaptername}%
    }%
  \fi\fi\fi
  %
  % Output the mark.  Pass it through \safewhatsit, to take care of
  % the preceding space.
  \safewhatsit\domark
  %
  % Insert the chapter heading break.
  \pchapsepmacro
  %
  % Now the second mark, after the heading break.  No break points
  % between here and the heading.
  \let\prevchapterdefs=\currentchapterdefs
  \let\prevsectiondefs=\currentsectiondefs
  \domark
  %
  {%
    \chapfonts \rm
    \let\footnote=\errfootnoteheading % give better error message
    %
    % Have to define \currentsection before calling \donoderef, because the
    % xref code eventually uses it.  On the other hand, it has to be called
    % after \pchapsepmacro, or the headline will change too soon.
    \gdef\currentsection{#1}%
    %
    % Only insert the separating space if we have a chapter/appendix
    % number, and don't print the unnumbered ``number''.
    \ifx\temptype\Ynothingkeyword
      \setbox0 = \hbox{}%
      \def\toctype{unnchap}%
    \else\ifx\temptype\Yomitfromtockeyword
      \setbox0 = \hbox{}% contents like unnumbered, but no toc entry
      \def\toctype{omit}%
    \else\ifx\temptype\Yappendixkeyword
      \setbox0 = \hbox{\putwordAppendix{} #3\enspace}%
      \def\toctype{app}%
    \else
      \setbox0 = \hbox{#3\enspace}%
      \def\toctype{numchap}%
    \fi\fi\fi
    %
    % Write the toc entry for this chapter.  Must come before the
    % \donoderef, because we include the current node name in the toc
    % entry, and \donoderef resets it to empty.
    \writetocentry{\toctype}{#1}{#3}%
    %
    % For pdftex, we have to write out the node definition (aka, make
    % the pdfdest) after any page break, but before the actual text has
    % been typeset.  If the destination for the pdf outline is after the
    % text, then jumping from the outline may wind up with the text not
    % being visible, for instance under high magnification.
    \donoderef{#2}%
    %
    % Typeset the actual heading.
    \nobreak % Avoid page breaks at the interline glue.
    \vbox{\raggedtitlesettings \hangindent=\wd0 \centerparametersmaybe
          \unhbox0 #1\par}%
  }%
  \nobreak\bigskip % no page break after a chapter title
  \nobreak
}

% @centerchap -- centered and unnumbered.
\let\centerparametersmaybe = \relax
\def\centerparameters{%
  \advance\rightskip by 3\rightskip
  \leftskip = \rightskip
  \parfillskip = 0pt
}


% Section titles.  These macros combine the section number parts and
% call the generic \sectionheading to do the printing.
%
\newskip\secheadingskip
\def\secheadingbreak{\dobreak \secheadingskip{-1000}}

% Subsection titles.
\newskip\subsecheadingskip
\def\subsecheadingbreak{\dobreak \subsecheadingskip{-500}}

% Subsubsection titles.
\def\subsubsecheadingskip{\subsecheadingskip}
\def\subsubsecheadingbreak{\subsecheadingbreak}


% Print any size, any type, section title.
%
% #1 is the text of the title,
% #2 is the section level (sec/subsec/subsubsec),
% #3 is the section type (Ynumbered, Ynothing, Yappendix, Yomitfromtoc),
% #4 is the section number.
%
\def\seckeyword{sec}
%
\def\sectionheading#1#2#3#4{%
  {%
    \def\sectionlevel{#2}%
    \def\temptype{#3}%
    %
    % It is ok for the @heading series commands to appear inside an
    % environment (it's been historically allowed, though the logic is
    % dubious), but not the others.
    \ifx\temptype\Yomitfromtockeyword\else
      \checkenv{}% non-@*heading should not be in an environment.
    \fi
    \let\footnote=\errfootnoteheading
    %
    % Switch to the right set of fonts.
    \csname #2fonts\endcsname \rm
    %
    % Insert first mark before the heading break (see notes for \domark).
    \let\prevsectiondefs=\currentsectiondefs
    \ifx\temptype\Ynothingkeyword
      \ifx\sectionlevel\seckeyword
        \gdef\currentsectiondefs{\gdef\thissectionname{#1}\gdef\thissectionnum{}%
                              \gdef\thissection{\thissectionname}}%
      \fi
    \else\ifx\temptype\Yomitfromtockeyword
      % Don't redefine \thissection.
    \else\ifx\temptype\Yappendixkeyword
      \ifx\sectionlevel\seckeyword
        \toks0={#1}%
        \xdef\currentsectiondefs{%
          \gdef\noexpand\thissectionname{\the\toks0}%
          \gdef\noexpand\thissectionnum{#4}%
          % \noexpand\putwordSection avoids expanding indigestible
          % commands in some of the translations.
          \gdef\noexpand\thissection{\noexpand\putwordSection{}
                                     \noexpand\thissectionnum:
                                     \noexpand\thissectionname}%
        }%
      \fi
    \else
      \ifx\sectionlevel\seckeyword
        \toks0={#1}%
        \xdef\currentsectiondefs{%
          \gdef\noexpand\thissectionname{\the\toks0}%
          \gdef\noexpand\thissectionnum{#4}%
          % \noexpand\putwordSection avoids expanding indigestible
          % commands in some of the translations.
          \gdef\noexpand\thissection{\noexpand\putwordSection{}
                                     \noexpand\thissectionnum:
                                     \noexpand\thissectionname}%
        }%
      \fi
    \fi\fi\fi
    %
    % Go into vertical mode.  Usually we'll already be there, but we
    % don't want the following whatsit to end up in a preceding paragraph
    % if the document didn't happen to have a blank line.
    \par
    %
    % Output the mark.  Pass it through \safewhatsit, to take care of
    % the preceding space.
    \safewhatsit\domark
    %
    % Insert space above the heading.
    \csname #2headingbreak\endcsname
    %
    % Now the second mark, after the heading break.  No break points
    % between here and the heading.
    \global\let\prevsectiondefs=\currentsectiondefs
    \domark
    %
    % Only insert the space after the number if we have a section number.
    \ifx\temptype\Ynothingkeyword
      \setbox0 = \hbox{}%
      \def\toctype{unn}%
      \gdef\currentsection{#1}%
    \else\ifx\temptype\Yomitfromtockeyword
      % for @headings -- no section number, don't include in toc,
      % and don't redefine \currentsection.
      \setbox0 = \hbox{}%
      \def\toctype{omit}%
      \let\sectionlevel=\empty
    \else\ifx\temptype\Yappendixkeyword
      \setbox0 = \hbox{#4\enspace}%
      \def\toctype{app}%
      \gdef\currentsection{#1}%
    \else
      \setbox0 = \hbox{#4\enspace}%
      \def\toctype{num}%
      \gdef\currentsection{#1}%
    \fi\fi\fi
    %
    % Write the toc entry (before \donoderef).  See comments in \chapmacro.
    \writetocentry{\toctype\sectionlevel}{#1}{#4}%
    %
    % Write the node reference (= pdf destination for pdftex).
    % Again, see comments in \chapmacro.
    \donoderef{#3}%
    %
    % Interline glue will be inserted when the vbox is completed.
    % That glue will be a valid breakpoint for the page, since it'll be
    % preceded by a whatsit (usually from the \donoderef, or from the
    % \writetocentry if there was no node).  We don't want to allow that
    % break, since then the whatsits could end up on page n while the
    % section is on page n+1, thus toc/etc. are wrong.  Debian bug 276000.
    \nobreak
    %
    % Output the actual section heading.
    \vbox{\hyphenpenalty=10000 \tolerance=5000 \parindent=0pt \ptexraggedright
          \hangindent=\wd0  % zero if no section number
          \unhbox0 #1}%
  }%
  % Add extra space after the heading -- half of whatever came above it.
  % Don't allow stretch, though.
  \kern .5 \csname #2headingskip\endcsname
  %
  % Do not let the kern be a potential breakpoint, as it would be if it
  % was followed by glue.
  \nobreak
  %
  % We'll almost certainly start a paragraph next, so don't let that
  % glue accumulate.  (Not a breakpoint because it's preceded by a
  % discardable item.)  However, when a paragraph is not started next
  % (\startdefun, \cartouche, \center, etc.), this needs to be wiped out
  % or the negative glue will cause weirdly wrong output, typically
  % obscuring the section heading with something else.
  \vskip-\parskip
  %
  % This is so the last item on the main vertical list is a known
  % \penalty > 10000, so \startdefun, etc., can recognize the situation
  % and do the needful.
  \penalty 10001
}


\message{toc,}
% Table of contents.
\newwrite\tocfile

% Write an entry to the toc file, opening it if necessary.
% Called from @chapter, etc.
%
% Example usage: \writetocentry{sec}{Section Name}{\the\chapno.\the\secno}
% We append the current node name (if any) and page number as additional
% arguments for the \{chap,sec,...}entry macros which will eventually
% read this.  The node name is used in the pdf outlines as the
% destination to jump to.
%
% We open the .toc file for writing here instead of at @setfilename (or
% any other fixed time) so that @contents can be anywhere in the document.
% But if #1 is `omit', then we don't do anything.  This is used for the
% table of contents chapter openings themselves.
%
\newif\iftocfileopened
\def\omitkeyword{omit}%
%
\def\writetocentry#1#2#3{%
  \edef\writetoctype{#1}%
  \ifx\writetoctype\omitkeyword \else
    \iftocfileopened\else
      \immediate\openout\tocfile = \jobname.toc
      \global\tocfileopenedtrue
    \fi
    %
    \iflinks
      {\atdummies
       \edef\temp{%
         \write\tocfile{@#1entry{#2}{#3}{\lastnode}{\noexpand\folio}}}%
       \temp
      }%
    \fi
  \fi
  %
  % Tell \shipout to create a pdf destination on each page, if we're
  % writing pdf.  These are used in the table of contents.  We can't
  % just write one on every page because the title pages are numbered
  % 1 and 2 (the page numbers aren't printed), and so are the first
  % two pages of the document.  Thus, we'd have two destinations named
  % `1', and two named `2'.
  \ifpdforxetex
    \global\pdfmakepagedesttrue
  \fi
}


% These characters do not print properly in the Computer Modern roman
% fonts, so we must take special care.  This is more or less redundant
% with the Texinfo input format setup at the end of this file.
%
\def\activecatcodes{%
  \catcode`\"=\active
  \catcode`\$=\active
  \catcode`\<=\active
  \catcode`\>=\active
  \catcode`\\=\active
  \catcode`\^=\active
  \catcode`\_=\active
  \catcode`\|=\active
  \catcode`\~=\active
}


% Read the toc file, which is essentially Texinfo input.
\def\readtocfile{%
  \setupdatafile
  \activecatcodes
  \input \tocreadfilename
}

\newskip\contentsrightmargin \contentsrightmargin=1in
\newcount\savepageno
\newcount\lastnegativepageno \lastnegativepageno = -1

% Prepare to read what we've written to \tocfile.
%
\def\startcontents#1{%
  % If @setchapternewpage on, and @headings double, the contents should
  % start on an odd page, unlike chapters.
  \contentsalignmacro
  \immediate\closeout\tocfile
  %
  % Don't need to put `Contents' or `Short Contents' in the headline.
  % It is abundantly clear what they are.
  \chapmacro{#1}{Yomitfromtoc}{}%
  %
  \savepageno = \pageno
  \begingroup                  % Set up to handle contents files properly.
    \raggedbottom              % Worry more about breakpoints than the bottom.
    \entryrightmargin=\contentsrightmargin % Don't use the full line length.
    %
    % Roman numerals for page numbers.
    \ifnum \pageno>0 \global\pageno = \lastnegativepageno \fi
    \def\thistitle{}% no title in double-sided headings
    % Record where the Roman numerals started.
    \ifnum\romancount=0 \global\romancount=\pagecount \fi
}

% redefined for the two-volume lispref.  We always output on
% \jobname.toc even if this is redefined.
%
\def\tocreadfilename{\jobname.toc}

% Normal (long) toc.
%
\def\contents{%
  \startcontents{\putwordTOC}%
    \openin 1 \tocreadfilename\space
    \ifeof 1 \else
      \readtocfile
    \fi
    \vfill \eject
    \contentsalignmacro % in case @setchapternewpage odd is in effect
    \ifeof 1 \else
      \pdfmakeoutlines
    \fi
    \closein 1
  \endgroup
  \contentsendroman
}

% And just the chapters.
\def\summarycontents{%
  \startcontents{\putwordShortTOC}%
    %
    \let\partentry = \shortpartentry
    \let\numchapentry = \shortchapentry
    \let\appentry = \shortchapentry
    \let\unnchapentry = \shortunnchapentry
    % We want a true roman here for the page numbers.
    \secfonts
    \let\rm=\shortcontrm \let\bf=\shortcontbf
    \let\sl=\shortcontsl \let\tt=\shortconttt
    \rm
    \hyphenpenalty = 10000
    \advance\baselineskip by 1pt % Open it up a little.
    \def\numsecentry##1##2##3##4{}
    \let\appsecentry = \numsecentry
    \let\unnsecentry = \numsecentry
    \let\numsubsecentry = \numsecentry
    \let\appsubsecentry = \numsecentry
    \let\unnsubsecentry = \numsecentry
    \let\numsubsubsecentry = \numsecentry
    \let\appsubsubsecentry = \numsecentry
    \let\unnsubsubsecentry = \numsecentry
    \openin 1 \tocreadfilename\space
    \ifeof 1 \else
      \readtocfile
    \fi
    \closein 1
    \vfill \eject
    \contentsalignmacro % in case @setchapternewpage odd is in effect
  \endgroup
  \contentsendroman
}
\let\shortcontents = \summarycontents

% Get ready to use Arabic numerals again
\def\contentsendroman{%
  \lastnegativepageno = \pageno
  \global\pageno = \savepageno
  %
  % If \romancount > \arabiccount, the contents are at the end of the
  % document.  Otherwise, advance where the Arabic numerals start for
  % the page numbers.
  \ifnum\romancount>\arabiccount\else\global\arabiccount=\pagecount\fi
}

% Typeset the label for a chapter or appendix for the short contents.
% The arg is, e.g., `A' for an appendix, or `3' for a chapter.
%
\def\shortchaplabel#1{%
  % This space should be enough, since a single number is .5em, and the
  % widest letter (M) is 1em, at least in the Computer Modern fonts.
  % But use \hss just in case.
  % (This space doesn't include the extra space that gets added after
  % the label; that gets put in by \shortchapentry above.)
  %
  % We'd like to right-justify chapter numbers, but that looks strange
  % with appendix letters.  And right-justifying numbers and
  % left-justifying letters looks strange when there is less than 10
  % chapters.  Have to read the whole toc once to know how many chapters
  % there are before deciding ...
  \hbox to 1em{#1\hss}%
}

% These macros generate individual entries in the table of contents.
% The first argument is the chapter or section name.
% The last argument is the page number.
% The arguments in between are the chapter number, section number, ...

% Parts, in the main contents.  Replace the part number, which doesn't
% exist, with an empty box.  Let's hope all the numbers have the same width.
% Also ignore the page number, which is conventionally not printed.
\def\numeralbox{\setbox0=\hbox{8}\hbox to \wd0{\hfil}}
\def\partentry#1#2#3#4{%
  % Add stretch and a bonus for breaking the page before the part heading.
  % This reduces the chance of the page being broken immediately after the
  % part heading, before a following chapter heading.
  \vskip 0pt plus 5\baselineskip
  \penalty-300
  \vskip 0pt plus -5\baselineskip
  \dochapentry{\numeralbox\labelspace#1}{}%
}
%
% Parts, in the short toc.
\def\shortpartentry#1#2#3#4{%
  \penalty-300
  \vskip.5\baselineskip plus.15\baselineskip minus.1\baselineskip
  \shortchapentry{{\bf #1}}{\numeralbox}{}{}%
}

% Chapters, in the main contents.
\def\numchapentry#1#2#3#4{\dochapentry{#2\labelspace#1}{#4}}

% Chapters, in the short toc.
% See comments in \dochapentry re vbox and related settings.
\def\shortchapentry#1#2#3#4{%
  \tocentry{\shortchaplabel{#2}\labelspace #1}{\doshortpageno\bgroup#4\egroup}%
}

% Appendices, in the main contents.
% Need the word Appendix, and a fixed-size box.
%
\def\appendixbox#1{%
  % We use M since it's probably the widest letter.
  \setbox0 = \hbox{\putwordAppendix{} M}%
  \hbox to \wd0{\putwordAppendix{} #1\hss}}
%
\def\appentry#1#2#3#4{\dochapentry{\appendixbox{#2}\hskip.7em#1}{#4}}

% Unnumbered chapters.
\def\unnchapentry#1#2#3#4{\dochapentry{#1}{#4}}
\def\shortunnchapentry#1#2#3#4{\tocentry{#1}{\doshortpageno\bgroup#4\egroup}}

% Sections.
\def\numsecentry#1#2#3#4{\dosecentry{#2\labelspace#1}{#4}}
\let\appsecentry=\numsecentry
\def\unnsecentry#1#2#3#4{\dosecentry{#1}{#4}}

% Subsections.
\def\numsubsecentry#1#2#3#4{\dosubsecentry{#2\labelspace#1}{#4}}
\let\appsubsecentry=\numsubsecentry
\def\unnsubsecentry#1#2#3#4{\dosubsecentry{#1}{#4}}

% And subsubsections.
\def\numsubsubsecentry#1#2#3#4{\dosubsubsecentry{#2\labelspace#1}{#4}}
\let\appsubsubsecentry=\numsubsubsecentry
\def\unnsubsubsecentry#1#2#3#4{\dosubsubsecentry{#1}{#4}}

% This parameter controls the indentation of the various levels.
% Same as \defaultparindent.
\newdimen\tocindent \tocindent = 15pt

% Now for the actual typesetting. In all these, #1 is the text and #2 is the
% page number.
%
% If the toc has to be broken over pages, we want it to be at chapters
% if at all possible; hence the \penalty.
\def\dochapentry#1#2{%
   \penalty-300 \vskip1\baselineskip plus.33\baselineskip minus.25\baselineskip
   \begingroup
     % Move the page numbers slightly to the right
     \advance\entryrightmargin by -0.05em
     \chapentryfonts
     \tocentry{#1}{\dopageno\bgroup#2\egroup}%
   \endgroup
   \nobreak\vskip .25\baselineskip plus.1\baselineskip
}

\def\dosecentry#1#2{\begingroup
  \secentryfonts \leftskip=\tocindent
  \tocentry{#1}{\dopageno\bgroup#2\egroup}%
\endgroup}

\def\dosubsecentry#1#2{\begingroup
  \subsecentryfonts \leftskip=2\tocindent
  \tocentry{#1}{\dopageno\bgroup#2\egroup}%
\endgroup}

\def\dosubsubsecentry#1#2{\begingroup
  \subsubsecentryfonts \leftskip=3\tocindent
  \tocentry{#1}{\dopageno\bgroup#2\egroup}%
\endgroup}

% We use the same \entry macro as for the index entries.
\let\tocentry = \entry

% Space between chapter (or whatever) number and the title.
\def\labelspace{\hskip1em \relax}

\def\dopageno#1{{\rm #1}}
\def\doshortpageno#1{{\rm #1}}

\def\chapentryfonts{\secfonts \rm}
\def\secentryfonts{\textfonts}
\def\subsecentryfonts{\textfonts}
\def\subsubsecentryfonts{\textfonts}


\message{environments,}
% @foo ... @end foo.

% @tex ... @end tex    escapes into raw TeX temporarily.
% One exception: @ is still an escape character, so that @end tex works.
% But \@ or @@ will get a plain @ character.

\envdef\tex{%
  \setupmarkupstyle{tex}%
  \catcode `\\=0 \catcode `\{=1 \catcode `\}=2
  \catcode `\$=3 \catcode `\&=4 \catcode `\#=6
  \catcode `\^=7 \catcode `\_=8 \catcode `\~=\active \let~=\tie
  \catcode `\%=14
  \catcode `\+=\other
  \catcode `\"=\other
  \catcode `\|=\other
  \catcode `\<=\other
  \catcode `\>=\other
  \catcode `\`=\other
  \catcode `\'=\other
  %
  % ' is active in math mode (mathcode"8000).  So reset it, and all our
  % other math active characters (just in case), to plain's definitions.
  \mathactive
  %
  % Inverse of the list at the beginning of the file.
  \let\b=\ptexb
  \let\bullet=\ptexbullet
  \let\c=\ptexc
  \let\,=\ptexcomma
  \let\.=\ptexdot
  \let\dots=\ptexdots
  \let\equiv=\ptexequiv
  \let\!=\ptexexclam
  \let\i=\ptexi
  \let\indent=\ptexindent
  \let\noindent=\ptexnoindent
  \let\{=\ptexlbrace
  \let\+=\tabalign
  \let\}=\ptexrbrace
  \let\/=\ptexslash
  \let\sp=\ptexsp
  \let\*=\ptexstar
  %\let\sup=\ptexsup % do not redefine, we want @sup to work in math mode
  \let\t=\ptext
  \expandafter \let\csname top\endcsname=\ptextop  % we've made it outer
  \let\frenchspacing=\plainfrenchspacing
  %
  \def\endldots{\mathinner{\ldots\ldots\ldots\ldots}}%
  \def\enddots{\relax\ifmmode\endldots\else$\mathsurround=0pt \endldots\,$\fi}%
  \def\@{@}%
}
% There is no need to define \Etex.

% Define @lisp ... @end lisp.
% @lisp environment forms a group so it can rebind things,
% including the definition of @end lisp (which normally is erroneous).

% Amount to narrow the margins by for @lisp.
\newskip\lispnarrowing \lispnarrowing=0.4in

% This is the definition that ^^M gets inside @lisp, @example, and other
% such environments.  \null is better than a space, since it doesn't
% have any width.
\def\lisppar{\null\endgraf}

% This space is always present above and below environments.
\newskip\envskipamount \envskipamount = 0pt

% Make spacing and below environment symmetrical.  We use \parskip here
% to help in doing that, since in @example-like environments \parskip
% is reset to zero; thus the \afterenvbreak inserts no space -- but the
% start of the next paragraph will insert \parskip.
%
\def\aboveenvbreak{{%
  % =10000 instead of <10000 because of a special case in \itemzzz and
  % \sectionheading, q.v.
  \ifnum \lastpenalty=10000 \else
    \advance\envskipamount by \parskip
    \endgraf
    \ifdim\lastskip<\envskipamount
      \removelastskip
      \ifnum\lastpenalty<10000
        % Penalize breaking before the environment, because preceding text
        % often leads into it.
        \penalty100
      \fi
      \vskip\envskipamount
    \fi
  \fi
}}

\def\afterenvbreak{{%
  % =10000 instead of <10000 because of a special case in \itemzzz and
  % \sectionheading, q.v.
  \ifnum \lastpenalty=10000 \else
    \advance\envskipamount by \parskip
    \endgraf
    \ifdim\lastskip<\envskipamount
      \removelastskip
      % it's not a good place to break if the last penalty was \nobreak
      % or better ...
      \ifnum\lastpenalty<10000 \penalty-50 \fi
      \vskip\envskipamount
    \fi
  \fi
}}

% \nonarrowing is a flag.  If "set", @lisp etc don't narrow margins; it will
% also clear it, so that its embedded environments do the narrowing again.
\let\nonarrowing=\relax

% @cartouche ... @end cartouche: draw rectangle w/rounded corners around
% environment contents.

%
\def\ctl{{\circle\char'013\hskip -6pt}}% 6pt from pl file: 1/2charwidth
\def\ctr{{\hskip 6pt\circle\char'010}}
\def\cbl{{\circle\char'012\hskip -6pt}}
\def\cbr{{\hskip 6pt\circle\char'011}}
\def\carttop{\hbox to \cartouter{\hskip\lskip
        \ctl\leaders\hrule height\circthick\hfil\ctr
        \hskip\rskip}}
\def\cartbot{\hbox to \cartouter{\hskip\lskip
        \cbl\leaders\hrule height\circthick\hfil\cbr
        \hskip\rskip}}
%
\newskip\lskip\newskip\rskip

% only require the font if @cartouche is actually used
\def\cartouchefontdefs{%
  \font\circle=lcircle10\relax
  \circthick=\fontdimen8\circle
}
\newdimen\circthick
\newdimen\cartouter\newdimen\cartinner
\newskip\normbskip\newskip\normpskip\newskip\normlskip


\envdef\cartouche{%
  \cartouchefontdefs
  \ifhmode\par\fi  % can't be in the midst of a paragraph.
  \startsavinginserts
  \lskip=\leftskip \rskip=\rightskip
  \leftskip=0pt\rightskip=0pt % we want these *outside*.
  \cartinner=\hsize \advance\cartinner by-\lskip
  \advance\cartinner by-\rskip
  \cartouter=\hsize
  \advance\cartouter by 18.4pt	% allow for 3pt kerns on either
				% side, and for 6pt waste from
				% each corner char, and rule thickness
  \normbskip=\baselineskip \normpskip=\parskip \normlskip=\lineskip
  %
  % If this cartouche directly follows a sectioning command, we need the
  % \parskip glue (backspaced over by default) or the cartouche can
  % collide with the section heading.
  \ifnum\lastpenalty>10000 \vskip\parskip \penalty\lastpenalty \fi
  %
  \setbox\groupbox=\vbox\bgroup
      \baselineskip=0pt\parskip=0pt\lineskip=0pt
      \carttop
      \hbox\bgroup
	  \hskip\lskip
	  \vrule\kern3pt
	  \vbox\bgroup
	      \kern3pt
	      \hsize=\cartinner
	      \baselineskip=\normbskip
	      \lineskip=\normlskip
	      \parskip=\normpskip
	      \vskip -\parskip
	      \comment % For explanation, see the end of def\group.
}
\def\Ecartouche{%
              \ifhmode\par\fi
	      \kern3pt
	  \egroup
	  \kern3pt\vrule
	  \hskip\rskip
      \egroup
      \cartbot
  \egroup
  \addgroupbox
  \checkinserts
}


% This macro is called at the beginning of all the @example variants,
% inside a group.
\newdimen\nonfillparindent
\def\nonfillstart{%
  \aboveenvbreak
  \ifdim\hfuzz < 12pt \hfuzz = 12pt \fi % Don't be fussy
  \sepspaces % Make spaces be word-separators rather than space tokens.
  \let\par = \lisppar % don't ignore blank lines
  \obeylines % each line of input is a line of output
  \parskip = 0pt
  % Turn off paragraph indentation but redefine \indent to emulate
  % the normal \indent.
  \nonfillparindent=\parindent
  \parindent = 0pt
  \let\indent\nonfillindent
  %
  \emergencystretch = 0pt % don't try to avoid overfull boxes
  \ifx\nonarrowing\relax
    \advance \leftskip by \lispnarrowing
    \exdentamount=\lispnarrowing
  \else
    \let\nonarrowing = \relax
  \fi
  \let\exdent=\nofillexdent
}

\begingroup
\obeyspaces
% We want to swallow spaces (but not other tokens) after the fake
% @indent in our nonfill-environments, where spaces are normally
% active and set to @tie, resulting in them not being ignored after
% @indent.
\gdef\nonfillindent{\futurelet\temp\nonfillindentcheck}%
\gdef\nonfillindentcheck{%
\ifx\temp %
\expandafter\nonfillindentgobble%
\else%
\leavevmode\nonfillindentbox%
\fi%
}%
\endgroup
\def\nonfillindentgobble#1{\nonfillindent}
\def\nonfillindentbox{\hbox to \nonfillparindent{\hss}}

% If you want all examples etc. small: @set dispenvsize small.
% If you want even small examples the full size: @set dispenvsize nosmall.
% This affects the following displayed environments:
%    @example, @display, @format, @lisp
%
\def\smallword{small}
\def\nosmallword{nosmall}
\let\SETdispenvsize\relax
\def\setnormaldispenv{%
  \ifx\SETdispenvsize\smallword
    % end paragraph for sake of leading, in case document has no blank
    % line.  This is redundant with what happens in \aboveenvbreak, but
    % we need to do it before changing the fonts, and it's inconvenient
    % to change the fonts afterward.
    \ifnum \lastpenalty=10000 \else \endgraf \fi
    \smallexamplefonts \rm
  \fi
}
\def\setsmalldispenv{%
  \ifx\SETdispenvsize\nosmallword
  \else
    \ifnum \lastpenalty=10000 \else \endgraf \fi
    \smallexamplefonts \rm
  \fi
}

% We often define two environments, @foo and @smallfoo.
% Let's do it in one command.  #1 is the env name, #2 the definition.
\def\makedispenvdef#1#2{%
  \expandafter\envdef\csname#1\endcsname {\setnormaldispenv #2}%
  \expandafter\envdef\csname small#1\endcsname {\setsmalldispenv #2}%
  \expandafter\let\csname E#1\endcsname \afterenvbreak
  \expandafter\let\csname Esmall#1\endcsname \afterenvbreak
}

% Define two environment synonyms (#1 and #2) for an environment.
\def\maketwodispenvdef#1#2#3{%
  \makedispenvdef{#1}{#3}%
  \makedispenvdef{#2}{#3}%
}
%
% @lisp: indented, narrowed, typewriter font;
% @example: same as @lisp.
%
% @smallexample and @smalllisp: use smaller fonts.
% Originally contributed by Pavel@xerox.
%
\maketwodispenvdef{lisp}{example}{%
  \nonfillstart
  \tt\setupmarkupstyle{example}%
  \let\kbdfont = \kbdexamplefont % Allow @kbd to do something special.
  \gobble % eat return
}
% @display/@smalldisplay: same as @lisp except keep current font.
%
\makedispenvdef{display}{%
  \nonfillstart
  \gobble
}

% @format/@smallformat: same as @display except don't narrow margins.
%
\makedispenvdef{format}{%
  \let\nonarrowing = t%
  \nonfillstart
  \gobble
}

% @flushleft: same as @format, but doesn't obey \SETdispenvsize.
\envdef\flushleft{%
  \let\nonarrowing = t%
  \nonfillstart
  \gobble
}
\let\Eflushleft = \afterenvbreak

% @flushright.
%
\envdef\flushright{%
  \let\nonarrowing = t%
  \nonfillstart
  \advance\leftskip by 0pt plus 1fill\relax
  \gobble
}
\let\Eflushright = \afterenvbreak


% @raggedright does more-or-less normal line breaking but no right
% justification.  From plain.tex.
\envdef\raggedright{%
  \rightskip0pt plus2.4em \spaceskip.3333em \xspaceskip.5em\relax
}
\let\Eraggedright\par

\envdef\raggedleft{%
  \parindent=0pt \leftskip0pt plus2em
  \spaceskip.3333em \xspaceskip.5em \parfillskip=0pt
  \hbadness=10000 % Last line will usually be underfull, so turn off
                  % badness reporting.
}
\let\Eraggedleft\par

\envdef\raggedcenter{%
  \parindent=0pt \rightskip0pt plus1em \leftskip0pt plus1em
  \spaceskip.3333em \xspaceskip.5em \parfillskip=0pt
  \hbadness=10000 % Last line will usually be underfull, so turn off
                  % badness reporting.
}
\let\Eraggedcenter\par


% @quotation does normal linebreaking (hence we can't use \nonfillstart)
% and narrows the margins.  We keep \parskip nonzero in general, since
% we're doing normal filling.  So, when using \aboveenvbreak and
% \afterenvbreak, temporarily make \parskip 0.
%
\makedispenvdef{quotation}{\quotationstart}
%
\def\quotationstart{%
  \indentedblockstart % same as \indentedblock, but increase right margin too.
  \ifx\nonarrowing\relax
    \advance\rightskip by \lispnarrowing
  \fi
  \parsearg\quotationlabel
}

% We have retained a nonzero parskip for the environment, since we're
% doing normal filling.
%
\def\Equotation{%
  \par
  \ifx\quotationauthor\thisisundefined\else
    % indent a bit.
    \leftline{\kern 2\leftskip \sl ---\quotationauthor}%
  \fi
  {\parskip=0pt \afterenvbreak}%
}
\def\Esmallquotation{\Equotation}

% If we're given an argument, typeset it in bold with a colon after.
\def\quotationlabel#1{%
  \def\temp{#1}%
  \ifx\temp\empty \else
    {\bf #1: }%
  \fi
}

% @indentedblock is like @quotation, but indents only on the left and
% has no optional argument.
%
\makedispenvdef{indentedblock}{\indentedblockstart}
%
\def\indentedblockstart{%
  {\parskip=0pt \aboveenvbreak}% because \aboveenvbreak inserts \parskip
  \parindent=0pt
  %
  % @cartouche defines \nonarrowing to inhibit narrowing at next level down.
  \ifx\nonarrowing\relax
    \advance\leftskip by \lispnarrowing
    \exdentamount = \lispnarrowing
  \else
    \let\nonarrowing = \relax
  \fi
}

% Keep a nonzero parskip for the environment, since we're doing normal filling.
%
\def\Eindentedblock{%
  \par
  {\parskip=0pt \afterenvbreak}%
}
\def\Esmallindentedblock{\Eindentedblock}


% LaTeX-like @verbatim...@end verbatim and @verb{<char>...<char>}
% If we want to allow any <char> as delimiter,
% we need the curly braces so that makeinfo sees the @verb command, eg:
% `@verbx...x' would look like the '@verbx' command.  --<EMAIL>
%
% [Knuth]: Donald Ervin Knuth, 1996.  The TeXbook.
%
% [Knuth] p.344; only we need to do the other characters Texinfo sets
% active too.  Otherwise, they get lost as the first character on a
% verbatim line.
\def\dospecials{%
  \do\ \do\\\do\{\do\}\do\$\do\&%
  \do\#\do\^\do\^^K\do\_\do\^^A\do\%\do\~%
  \do\<\do\>\do\|\do\@\do+\do\"%
  % Don't do the quotes -- if we do, @set txicodequoteundirected and
  % @set txicodequotebacktick will not have effect on @verb and
  % @verbatim, and ?` and !` ligatures won't get disabled.
  %\do\`\do\'%
}
%
% [Knuth] p. 380
\def\uncatcodespecials{%
  \def\do##1{\catcode`##1=\other}\dospecials}
%
% Setup for the @verb command.
%
% Eight spaces for a tab
\begingroup
  \catcode`\^^I=\active
  \gdef\tabeightspaces{\catcode`\^^I=\active\def^^I{\ \ \ \ \ \ \ \ }}
\endgroup
%
\def\setupverb{%
  \tt  % easiest (and conventionally used) font for verbatim
  \def\par{\leavevmode\endgraf}%
  \setupmarkupstyle{verb}%
  \tabeightspaces
  % Respect line breaks,
  % print special symbols as themselves, and
  % make each space count
  % must do in this order:
  \obeylines \uncatcodespecials \sepspaces
}

% Setup for the @verbatim environment
%
% Real tab expansion.
\newdimen\tabw \setbox0=\hbox{\tt\space} \tabw=8\wd0 % tab amount
%
% We typeset each line of the verbatim in an \hbox, so we can handle
% tabs.
\newbox\verbbox
\def\starttabbox{\setbox\verbbox=\hbox\bgroup}
%
\begingroup
  \catcode`\^^I=\active
  \gdef\tabexpand{%
    \catcode`\^^I=\active
    \def^^I{\leavevmode\egroup
      \dimen\verbbox=\wd\verbbox % the width so far, or since the previous tab
      \divide\dimen\verbbox by\tabw
      \multiply\dimen\verbbox by\tabw % compute previous multiple of \tabw
      \advance\dimen\verbbox by\tabw  % advance to next multiple of \tabw
      \wd\verbbox=\dimen\verbbox
      \leavevmode\box\verbbox \starttabbox
    }%
  }
\endgroup

% start the verbatim environment.
\def\setupverbatim{%
  \let\nonarrowing = t%
  \nonfillstart
  \tt % easiest (and conventionally used) font for verbatim
  \def\par{\egroup\leavevmode\box\verbbox\endgraf\starttabbox}%
  \tabexpand
  \setupmarkupstyle{verbatim}%
  % Respect line breaks,
  % print special symbols as themselves, and
  % make each space count.
  % Must do in this order:
  \obeylines \uncatcodespecials \sepspaces
}

% Do the @verb magic: verbatim text is quoted by unique
% delimiter characters.  Before first delimiter expect a
% right brace, after last delimiter expect closing brace:
%
%    \def\doverb'{'<char>#1<char>'}'{#1}
%
% [Knuth] p. 382; only eat outer {}
\begingroup
  \catcode`[=1\catcode`]=2\catcode`\{=\other\catcode`\}=\other
  \gdef\doverb{#1[\def\next##1#1}[##1\endgroup]\next]
\endgroup
%
\def\verb{\begingroup\setupverb\doverb}
%
%
% Do the @verbatim magic: define the macro \doverbatim so that
% the (first) argument ends when '@end verbatim' is reached, ie:
%
%     \def\doverbatim#1@end verbatim{#1}
%
% For Texinfo it's a lot easier than for LaTeX,
% because texinfo's \verbatim doesn't stop at '\end{verbatim}':
% we need not redefine '\', '{' and '}'.
%
% Inspired by LaTeX's verbatim command set [latex.ltx]
%
\begingroup
  \catcode`\ =\active
  \obeylines %
  % ignore everything up to the first ^^M, that's the newline at the end
  % of the @verbatim input line itself.  Otherwise we get an extra blank
  % line in the output.
  \xdef\doverbatim#1^^M#2@end verbatim{%
    \starttabbox#2\egroup\noexpand\end\gobble verbatim}%
  % We really want {...\end verbatim} in the body of the macro, but
  % without the active space; thus we have to use \xdef and \gobble.
  % The \egroup ends the \verbbox started at the end of the last line in
  % the block.
\endgroup
%
\envdef\verbatim{%
    \setnormaldispenv\setupverbatim\doverbatim
}
\let\Everbatim = \afterenvbreak


% @verbatiminclude FILE - insert text of file in verbatim environment.
%
\def\verbatiminclude{\parseargusing\filenamecatcodes\doverbatiminclude}
%
\def\doverbatiminclude#1{%
  {%
    \makevalueexpandable
    \setupverbatim
    {%
      \indexnofonts       % Allow `@@' and other weird things in file names.
      \wlog{texinfo.tex: doing @verbatiminclude of #1^^J}%
      \edef\tmp{\noexpand\input #1 }
      \expandafter
    }\expandafter\starttabbox\tmp\egroup
    \afterenvbreak
  }%
}

% @copying ... @end copying.
% Save the text away for @insertcopying later.
%
% We save the uninterpreted tokens, rather than creating a box.
% Saving the text in a box would be much easier, but then all the
% typesetting commands (@smallbook, font changes, etc.) have to be done
% beforehand -- and a) we want @copying to be done first in the source
% file; b) letting users define the frontmatter in as flexible order as
% possible is desirable.
%
\def\copying{\checkenv{}\begingroup\scanargctxt\docopying}
\def\docopying#1@end copying{\endgroup\def\copyingtext{#1}}
%
\def\insertcopying{%
  \begingroup
    \parindent = 0pt  % paragraph indentation looks wrong on title page
    \scanexp\copyingtext
  \endgroup
}


\message{defuns,}
% @defun etc.

\newskip\defbodyindent \defbodyindent=.4in
\newskip\defargsindent \defargsindent=50pt
\newskip\deflastargmargin \deflastargmargin=18pt
\newcount\defunpenalty

% Start the processing of @deffn:
\def\startdefun{%
  \ifnum\lastpenalty<10000
    \medbreak
    \defunpenalty=10003 % Will keep this @deffn together with the
                        % following @def command, see below.
  \else
    % If there are two @def commands in a row, we'll have a \nobreak,
    % which is there to keep the function description together with its
    % header.  But if there's nothing but headers, we need to allow a
    % break somewhere.  Check specifically for penalty 10002, inserted
    % by \printdefunline, instead of 10000, since the sectioning
    % commands also insert a nobreak penalty, and we don't want to allow
    % a break between a section heading and a defun.
    %
    % As a further refinement, we avoid "club" headers by signalling
    % with penalty of 10003 after the very first @deffn in the
    % sequence (see above), and penalty of 10002 after any following
    % @def command.
    \ifnum\lastpenalty=10002 \penalty2000 \else \defunpenalty=10002 \fi
    %
    % Similarly, after a section heading, do not allow a break.
    % But do insert the glue.
    \medskip  % preceded by discardable penalty, so not a breakpoint
  \fi
  %
  \parindent=0in
  \advance\leftskip by \defbodyindent
  \exdentamount=\defbodyindent
}

\def\dodefunx#1{%
  % First, check whether we are in the right environment:
  \checkenv#1%
  %
  % As above, allow line break if we have multiple x headers in a row.
  % It's not a great place, though.
  \ifnum\lastpenalty=10002 \penalty3000 \else \defunpenalty=10002 \fi
  %
  % And now, it's time to reuse the body of the original defun:
  \expandafter\gobbledefun#1%
}
\def\gobbledefun#1\startdefun{}

% \printdefunline \deffnheader{text}
%
\def\printdefunline#1#2{%
  \begingroup
    % call \deffnheader:
    #1#2 \endheader
    % common ending:
    \interlinepenalty = 10000
    \advance\rightskip by 0pt plus 1fil\relax
    \endgraf
    \nobreak\vskip -\parskip
    \penalty\defunpenalty  % signal to \startdefun and \dodefunx
    % Some of the @defun-type tags do not enable magic parentheses,
    % rendering the following check redundant.  But we don't optimize.
    \checkparencounts
  \endgroup
}

\def\Edefun{\endgraf\medbreak}

% \makedefun{deffn} creates \deffn, \deffnx and \Edeffn;
% the only thing remaining is to define \deffnheader.
%
\def\makedefun#1{%
  \expandafter\let\csname E#1\endcsname = \Edefun
  \edef\temp{\noexpand\domakedefun
    \makecsname{#1}\makecsname{#1x}\makecsname{#1header}}%
  \temp
}

% \domakedefun \deffn \deffnx \deffnheader { (defn. of \deffnheader) }
%
% Define \deffn and \deffnx, without parameters.
% \deffnheader has to be defined explicitly.
%
\def\domakedefun#1#2#3{%
  \envdef#1{%
    \startdefun
    \doingtypefnfalse    % distinguish typed functions from all else
    \parseargusing\activeparens{\printdefunline#3}%
  }%
  \def#2{\dodefunx#1}%
  \def#3%
}

\newif\ifdoingtypefn       % doing typed function?
\newif\ifrettypeownline    % typeset return type on its own line?

% @deftypefnnewline on|off says whether the return type of typed functions
% are printed on their own line.  This affects @deftypefn, @deftypefun,
% @deftypeop, and @deftypemethod.
%
\parseargdef\deftypefnnewline{%
  \def\temp{#1}%
  \ifx\temp\onword
    \expandafter\let\csname SETtxideftypefnnl\endcsname
      = \empty
  \else\ifx\temp\offword
    \expandafter\let\csname SETtxideftypefnnl\endcsname
      = \relax
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @txideftypefnnl value `\temp',
                must be on|off}%
  \fi\fi
}

% \dosubind {index}{topic}{subtopic}
%
% If SUBTOPIC is present, precede it with a space, and call \doind.
% (At some time during the 20th century, this made a two-level entry in an
% index such as the operation index.  Nobody seemed to notice the change in
% behaviour though.)
\def\dosubind#1#2#3{%
  \def\thirdarg{#3}%
  \ifx\thirdarg\empty
    \doind{#1}{#2}%
  \else
    \doind{#1}{#2\space#3}%
  \fi
}

% Untyped functions:

% @deffn category name args
\makedefun{deffn}{\deffngeneral{}}

% @deffn category class name args
\makedefun{defop}#1 {\defopon{#1\ \putwordon}}

% \defopon {category on}class name args
\def\defopon#1#2 {\deffngeneral{\putwordon\ \code{#2}}{#1\ \code{#2}} }

% \deffngeneral {subind}category name args
%
\def\deffngeneral#1#2 #3 #4\endheader{%
  \dosubind{fn}{\code{#3}}{#1}%
  \defname{#2}{}{#3}\magicamp\defunargs{#4\unskip}%
}

% Typed functions:

% @deftypefn category type name args
\makedefun{deftypefn}{\deftypefngeneral{}}

% @deftypeop category class type name args
\makedefun{deftypeop}#1 {\deftypeopon{#1\ \putwordon}}

% \deftypeopon {category on}class type name args
\def\deftypeopon#1#2 {\deftypefngeneral{\putwordon\ \code{#2}}{#1\ \code{#2}} }

% \deftypefngeneral {subind}category type name args
%
\def\deftypefngeneral#1#2 #3 #4 #5\endheader{%
  \dosubind{fn}{\code{#4}}{#1}%
  \doingtypefntrue
  \defname{#2}{#3}{#4}\defunargs{#5\unskip}%
}

% Typed variables:

% @deftypevr category type var args
\makedefun{deftypevr}{\deftypecvgeneral{}}

% @deftypecv category class type var args
\makedefun{deftypecv}#1 {\deftypecvof{#1\ \putwordof}}

% \deftypecvof {category of}class type var args
\def\deftypecvof#1#2 {\deftypecvgeneral{\putwordof\ \code{#2}}{#1\ \code{#2}} }

% \deftypecvgeneral {subind}category type var args
%
\def\deftypecvgeneral#1#2 #3 #4 #5\endheader{%
  \dosubind{vr}{\code{#4}}{#1}%
  \defname{#2}{#3}{#4}\defunargs{#5\unskip}%
}

% Untyped variables:

% @defvr category var args
\makedefun{defvr}#1 {\deftypevrheader{#1} {} }

% @defcv category class var args
\makedefun{defcv}#1 {\defcvof{#1\ \putwordof}}

% \defcvof {category of}class var args
\def\defcvof#1#2 {\deftypecvof{#1}#2 {} }

% Types:

% @deftp category name args
\makedefun{deftp}#1 #2 #3\endheader{%
  \doind{tp}{\code{#2}}%
  \defname{#1}{}{#2}\defunargs{#3\unskip}%
}

% Remaining @defun-like shortcuts:
\makedefun{defun}{\deffnheader{\putwordDeffunc} }
\makedefun{defmac}{\deffnheader{\putwordDefmac} }
\makedefun{defspec}{\deffnheader{\putwordDefspec} }
\makedefun{deftypefun}{\deftypefnheader{\putwordDeffunc} }
\makedefun{defvar}{\defvrheader{\putwordDefvar} }
\makedefun{defopt}{\defvrheader{\putwordDefopt} }
\makedefun{deftypevar}{\deftypevrheader{\putwordDefvar} }
\makedefun{defmethod}{\defopon\putwordMethodon}
\makedefun{deftypemethod}{\deftypeopon\putwordMethodon}
\makedefun{defivar}{\defcvof\putwordInstanceVariableof}
\makedefun{deftypeivar}{\deftypecvof\putwordInstanceVariableof}

% \defname, which formats the name of the @def (not the args).
% #1 is the category, such as "Function".
% #2 is the return type, if any.
% #3 is the function name.
%
% We are followed by (but not passed) the arguments, if any.
%
\def\defname#1#2#3{%
  \par
  % Get the values of \leftskip and \rightskip as they were outside the @def...
  \advance\leftskip by -\defbodyindent
  %
  % Determine if we are typesetting the return type of a typed function
  % on a line by itself.
  \rettypeownlinefalse
  \ifdoingtypefn  % doing a typed function specifically?
    % then check user option for putting return type on its own line:
    \expandafter\ifx\csname SETtxideftypefnnl\endcsname\relax \else
      \rettypeownlinetrue
    \fi
  \fi
  %
  % How we'll format the category name.  Putting it in brackets helps
  % distinguish it from the body text that may end up on the next line
  % just below it.
  \def\temp{#1}%
  \setbox0=\hbox{\kern\deflastargmargin \ifx\temp\empty\else [\rm\temp]\fi}
  %
  % Figure out line sizes for the paragraph shape.  We'll always have at
  % least two.
  \tempnum = 2
  %
  % The first line needs space for \box0; but if \rightskip is nonzero,
  % we need only space for the part of \box0 which exceeds it:
  \dimen0=\hsize  \advance\dimen0 by -\wd0  \advance\dimen0 by \rightskip
  %
  % If doing a return type on its own line, we'll have another line.
  \ifrettypeownline
    \advance\tempnum by 1
    \def\maybeshapeline{0in \hsize}%
  \else
    \def\maybeshapeline{}%
  \fi
  %
  % The continuations:
  \dimen2=\hsize  \advance\dimen2 by -\defargsindent
  %
  % The final paragraph shape:
  \parshape \tempnum  0in \dimen0  \maybeshapeline  \defargsindent \dimen2
  %
  % Put the category name at the right margin.
  \noindent
  \hbox to 0pt{%
    \hfil\box0 \kern-\hsize
    % \hsize has to be shortened this way:
    \kern\leftskip
    % Intentionally do not respect \rightskip, since we need the space.
  }%
  %
  % Allow all lines to be underfull without complaint:
  \tolerance=10000 \hbadness=10000
  \exdentamount=\defbodyindent
  {%
    % defun fonts. We use typewriter by default (used to be bold) because:
    % . we're printing identifiers, they should be in tt in principle.
    % . in languages with many accents, such as Czech or French, it's
    %   common to leave accents off identifiers.  The result looks ok in
    %   tt, but exceedingly strange in rm.
    % . we don't want -- and --- to be treated as ligatures.
    % . this still does not fix the ?` and !` ligatures, but so far no
    %   one has made identifiers using them :).
    \df \tt
    \def\temp{#2}% text of the return type
    \ifx\temp\empty\else
      \tclose{\temp}% typeset the return type
      \ifrettypeownline
        % put return type on its own line; prohibit line break following:
        \hfil\vadjust{\nobreak}\break
      \else
        \space  % type on same line, so just followed by a space
      \fi
    \fi           % no return type
    #3% output function name
  }%
  {\rm\enskip}% hskip 0.5 em of \rmfont
  %
  \boldbrax
  % arguments will be output next, if any.
}

% Print arguments in slanted roman (not ttsl), inconsistently with using
% tt for the name.  This is because literal text is sometimes needed in
% the argument list (groff manual), and ttsl and tt are not very
% distinguishable.  Prevent hyphenation at `-' chars.
%
\def\defunargs#1{%
  % use sl by default (not ttsl),
  % tt for the names.
  \df \sl \hyphenchar\font=0
  %
  % On the other hand, if an argument has two dashes (for instance), we
  % want a way to get ttsl.  We used to recommend @var for that, so
  % leave the code in, but it's strange for @var to lead to typewriter.
  % Nowadays we recommend @code, since the difference between a ttsl hyphen
  % and a tt hyphen is pretty tiny.  @code also disables ?` !`.
  \def\var##1{{\setupmarkupstyle{var}\ttslanted{##1}}}%
  #1%
  \sl\hyphenchar\font=45
}

% We want ()&[] to print specially on the defun line.
%
\def\activeparens{%
  \catcode`\(=\active \catcode`\)=\active
  \catcode`\[=\active \catcode`\]=\active
  \catcode`\&=\active
}

% Make control sequences which act like normal parenthesis chars.
\let\lparen = ( \let\rparen = )

% Be sure that we always have a definition for `(', etc.  For example,
% if the fn name has parens in it, \boldbrax will not be in effect yet,
% so TeX would otherwise complain about undefined control sequence.
{
  \activeparens
  \global\let(=\lparen \global\let)=\rparen
  \global\let[=\lbrack \global\let]=\rbrack
  \global\let& = \&

  \gdef\boldbrax{\let(=\opnr\let)=\clnr\let[=\lbrb\let]=\rbrb}
  \gdef\magicamp{\let&=\amprm}
}
\let\ampchar\&

\newcount\parencount

% If we encounter &foo, then turn on ()-hacking afterwards
\newif\ifampseen
\def\amprm#1 {\ampseentrue{\bf\&#1 }}

\def\parenfont{%
  \ifampseen
    % At the first level, print parens in roman,
    % otherwise use the default font.
    \ifnum \parencount=1 \rm \fi
  \else
    % The \sf parens (in \boldbrax) actually are a little bolder than
    % the contained text.  This is especially needed for [ and ] .
    \sf
  \fi
}
\def\infirstlevel#1{%
  \ifampseen
    \ifnum\parencount=1
      #1%
    \fi
  \fi
}
\def\bfafterword#1 {#1 \bf}

\def\opnr{%
  \global\advance\parencount by 1
  {\parenfont(}%
  \infirstlevel \bfafterword
}
\def\clnr{%
  {\parenfont)}%
  \infirstlevel \sl
  \global\advance\parencount by -1
}

\newcount\brackcount
\def\lbrb{%
  \global\advance\brackcount by 1
  {\bf[}%
}
\def\rbrb{%
  {\bf]}%
  \global\advance\brackcount by -1
}

\def\checkparencounts{%
  \ifnum\parencount=0 \else \badparencount \fi
  \ifnum\brackcount=0 \else \badbrackcount \fi
}
% these should not use \errmessage; the glibc manual, at least, actually
% has such constructs (when documenting function pointers).
\def\badparencount{%
  \message{Warning: unbalanced parentheses in @def...}%
  \global\parencount=0
}
\def\badbrackcount{%
  \message{Warning: unbalanced square brackets in @def...}%
  \global\brackcount=0
}


\message{macros,}
% @macro.

% To do this right we need a feature of e-TeX, \scantokens,
% which we arrange to emulate with a temporary file in ordinary TeX.
\ifx\eTeXversion\thisisundefined
  \newwrite\macscribble
  \def\scantokens#1{%
    \toks0={#1}%
    \immediate\openout\macscribble=\jobname.tmp
    \immediate\write\macscribble{\the\toks0}%
    \immediate\closeout\macscribble
    \input \jobname.tmp
  }
\fi

% Used at the time of macro expansion.
% Argument is macro body with arguments substituted
\def\scanmacro#1{%
  \newlinechar`\^^M
  \def\xeatspaces{\eatspaces}%
  %
  % Process the macro body under the current catcode regime.
  \scantokens{#1@comment}%
  %
  % The \comment is to remove the \newlinechar added by \scantokens, and
  % can be noticed by \parsearg.  Note \c isn't used because this means cedilla
  % in math mode.
}

% Used for copying and captions
\def\scanexp#1{%
  \expandafter\scanmacro\expandafter{#1}%
}

\newcount\paramno   % Count of parameters
\newtoks\macname    % Macro name
\newif\ifrecursive  % Is it recursive?

% List of all defined macros in the form
%    \commondummyword\macro1\commondummyword\macro2...
% Currently is also contains all @aliases; the list can be split
% if there is a need.
\def\macrolist{}

% Add the macro to \macrolist
\def\addtomacrolist#1{\expandafter \addtomacrolistxxx \csname#1\endcsname}
\def\addtomacrolistxxx#1{%
     \toks0 = \expandafter{\macrolist\commondummyword#1}%
     \xdef\macrolist{\the\toks0}%
}

% Utility routines.
% This does \let #1 = #2, with \csnames; that is,
%   \let \csname#1\endcsname = \csname#2\endcsname
% (except of course we have to play expansion games).
%
\def\cslet#1#2{%
  \expandafter\let
  \csname#1\expandafter\endcsname
  \csname#2\endcsname
}

% Trim leading and trailing spaces off a string.
% Concepts from aro-bend problem 15 (see CTAN).
{\catcode`\@=11
\gdef\eatspaces #1{\expandafter\trim@\expandafter{#1 }}
\gdef\trim@ #1{\trim@@ @#1 @ #1 @ @@}
\gdef\trim@@ #1@ #2@ #3@@{\trim@@@\empty #2 @}
\def\unbrace#1{#1}
\unbrace{\gdef\trim@@@ #1 } #2@{#1}
}

% Trim a single trailing ^^M off a string.
{\catcode`\^^M=\other \catcode`\Q=3%
\gdef\eatcr #1{\eatcra #1Q^^MQ}%
\gdef\eatcra#1^^MQ{\eatcrb#1Q}%
\gdef\eatcrb#1Q#2Q{#1}%
}

% Macro bodies are absorbed as an argument in a context where
% all characters are catcode 10, 11 or 12, except \ which is active
% (as in normal texinfo). It is necessary to change the definition of \
% to recognize macro arguments; this is the job of \mbodybackslash.
%
% Non-ASCII encodings make 8-bit characters active, so un-activate
% them to avoid their expansion.  Must do this non-globally, to
% confine the change to the current group.
%
% It's necessary to have hard CRs when the macro is executed. This is
% done by making ^^M (\endlinechar) catcode 12 when reading the macro
% body, and then making it the \newlinechar in \scanmacro.
%
\def\scanctxt{% used as subroutine
  \catcode`\"=\other
  \catcode`\+=\other
  \catcode`\<=\other
  \catcode`\>=\other
  \catcode`\^=\other
  \catcode`\_=\other
  \catcode`\|=\other
  \catcode`\~=\other
  \passthroughcharstrue
}

\def\scanargctxt{% used for copying and captions, not macros.
  \scanctxt
  \catcode`\@=\other
  \catcode`\\=\other
  \catcode`\^^M=\other
}

\def\macrobodyctxt{% used for @macro definitions
  \scanctxt
  \catcode`\ =\other
  \catcode`\@=\other
  \catcode`\{=\other
  \catcode`\}=\other
  \catcode`\^^M=\other
  \usembodybackslash
}

% Used when scanning braced macro arguments.  Note, however, that catcode
% changes here are ineffectual if the macro invocation was nested inside
% an argument to another Texinfo command.
\def\macroargctxt{%
  \scanctxt
  \catcode`\ =\active
  \catcode`\@=\other
  \catcode`\^^M=\other
  \catcode`\\=\active
}

\def\macrolineargctxt{% used for whole-line arguments without braces
  \scanctxt
  \catcode`\@=\other
  \catcode`\{=\other
  \catcode`\}=\other
}

% \mbodybackslash is the definition of \ in @macro bodies.
% It maps \foo\ => \csname macarg.foo\endcsname => #N
% where N is the macro parameter number.
% We define \csname macarg.\endcsname to be \realbackslash, so
% \\ in macro replacement text gets you a backslash.
%
{\catcode`@=0 @catcode`@\=@active
 @gdef@usembodybackslash{@let\=@mbodybackslash}
 @gdef@mbodybackslash#1\{@csname macarg.#1@endcsname}
}
\expandafter\def\csname macarg.\endcsname{\realbackslash}

\def\margbackslash#1{\char`\#1 }

\def\macro{\recursivefalse\parsearg\macroxxx}
\def\rmacro{\recursivetrue\parsearg\macroxxx}

\def\macroxxx#1{%
  \getargs{#1}% now \macname is the macname and \argl the arglist
  \ifx\argl\empty       % no arguments
     \paramno=0\relax
  \else
     \expandafter\parsemargdef \argl;%
     \if\paramno>256\relax
       \ifx\eTeXversion\thisisundefined
         \errhelp = \EMsimple
         \errmessage{You need eTeX to compile a file with macros with more than 256 arguments}
       \fi
     \fi
  \fi
  \if1\csname ismacro.\the\macname\endcsname
     \message{Warning: redefining \the\macname}%
  \else
     \expandafter\ifx\csname \the\macname\endcsname \relax
     \else \errmessage{Macro name \the\macname\space already defined}\fi
     \global\cslet{macsave.\the\macname}{\the\macname}%
     \global\expandafter\let\csname ismacro.\the\macname\endcsname=1%
     \addtomacrolist{\the\macname}%
  \fi
  \begingroup \macrobodyctxt
  \ifrecursive \expandafter\parsermacbody
  \else \expandafter\parsemacbody
  \fi}

\parseargdef\unmacro{%
  \if1\csname ismacro.#1\endcsname
    \global\cslet{#1}{macsave.#1}%
    \global\expandafter\let \csname ismacro.#1\endcsname=0%
    % Remove the macro name from \macrolist:
    \begingroup
      \expandafter\let\csname#1\endcsname \relax
      \let\commondummyword\unmacrodo
      \xdef\macrolist{\macrolist}%
    \endgroup
  \else
    \errmessage{Macro #1 not defined}%
  \fi
}

% Called by \do from \dounmacro on each macro.  The idea is to omit any
% macro definitions that have been changed to \relax.
%
\def\unmacrodo#1{%
  \ifx #1\relax
    % remove this
  \else
    \noexpand\commondummyword \noexpand#1%
  \fi
}

% \getargs -- Parse the arguments to a @macro line.  Set \macname to
% the name of the macro, and \argl to the braced argument list.
\def\getargs#1{\getargsxxx#1{}}
\def\getargsxxx#1#{\getmacname #1 \relax\getmacargs}
\def\getmacname#1 #2\relax{\macname={#1}}
\def\getmacargs#1{\def\argl{#1}}
% This made use of the feature that if the last token of a
% <parameter list> is #, then the preceding argument is delimited by
% an opening brace, and that opening brace is not consumed.

% Parse the optional {params} list to @macro or @rmacro.
% Set \paramno to the number of arguments,
% and \paramlist to a parameter text for the macro (e.g. #1,#2,#3 for a
% three-param macro.)  Define \macarg.BLAH for each BLAH in the params
% list to some hook where the argument is to be expanded.  If there are
% less than 10 arguments that hook is to be replaced by ##N where N
% is the position in that list, that is to say the macro arguments are to be
% defined `a la TeX in the macro body.
%
% That gets used by \mbodybackslash (above).
%
% If there are 10 or more arguments, a different technique is used: see
% \parsemmanyargdef.
%
\def\parsemargdef#1;{%
  \paramno=0\def\paramlist{}%
  \let\hash\relax
  % \hash is redefined to `#' later to get it into definitions
  \let\xeatspaces\relax
  \parsemargdefxxx#1,;,%
  \ifnum\paramno<10\relax\else
    \paramno0\relax
    \parsemmanyargdef@@#1,;,% 10 or more arguments
  \fi
}
\def\parsemargdefxxx#1,{%
  \if#1;\let\next=\relax
  \else \let\next=\parsemargdefxxx
    \advance\paramno by 1
    \expandafter\edef\csname macarg.\eatspaces{#1}\endcsname
        {\xeatspaces{\hash\the\paramno}}%
    \edef\paramlist{\paramlist\hash\the\paramno,}%
  \fi\next}

% \parsemacbody, \parsermacbody
%
% Read recursive and nonrecursive macro bodies. (They're different since
% rec and nonrec macros end differently.)
%
% We are in \macrobodyctxt, and the \xdef causes backslashshes in the macro
% body to be transformed.
% Set \macrobody to the body of the macro, and call \defmacro.
%
{\catcode`\ =\other\long\gdef\parsemacbody#1@end macro{%
\xdef\macrobody{\eatcr{#1}}\endgroup\defmacro}}%
{\catcode`\ =\other\long\gdef\parsermacbody#1@end rmacro{%
\xdef\macrobody{\eatcr{#1}}\endgroup\defmacro}}%

% Make @ a letter, so that we can make private-to-Texinfo macro names.
\edef\texiatcatcode{\the\catcode`\@}
\catcode `@=11\relax

%%%%%%%%%%%%%% Code for > 10 arguments only   %%%%%%%%%%%%%%%%%%

% If there are 10 or more arguments, a different technique is used, where the
% hook remains in the body, and when macro is to be expanded the body is
% processed again to replace the arguments.
%
% In that case, the hook is \the\toks N-1, and we simply set \toks N-1 to the
% argument N value and then \edef the body (nothing else will expand because of
% the catcode regime under which the body was input).
%
% If you compile with TeX (not eTeX), and you have macros with 10 or more
% arguments, no macro can have more than 256 arguments (else error).
%
% In case that there are 10 or more arguments we parse again the arguments
% list to set new definitions for the \macarg.BLAH macros corresponding to
% each BLAH argument. It was anyhow needed to parse already once this list
% in order to count the arguments, and as macros with at most 9 arguments
% are by far more frequent than macro with 10 or more arguments, defining
% twice the \macarg.BLAH macros does not cost too much processing power.
\def\parsemmanyargdef@@#1,{%
  \if#1;\let\next=\relax
  \else
    \let\next=\parsemmanyargdef@@
    \edef\tempb{\eatspaces{#1}}%
    \expandafter\def\expandafter\tempa
       \expandafter{\csname macarg.\tempb\endcsname}%
    % Note that we need some extra \noexpand\noexpand, this is because we
    % don't want \the  to be expanded in the \parsermacbody  as it uses an
    % \xdef .
    \expandafter\edef\tempa
      {\noexpand\noexpand\noexpand\the\toks\the\paramno}%
    \advance\paramno by 1\relax
  \fi\next}


\let\endargs@\relax
\let\nil@\relax
\def\nilm@{\nil@}%
\long\def\nillm@{\nil@}%

% This macro is expanded during the Texinfo macro expansion, not during its
% definition.  It gets all the arguments' values and assigns them to macros
% macarg.ARGNAME
%
% #1 is the macro name
% #2 is the list of argument names
% #3 is the list of argument values
\def\getargvals@#1#2#3{%
  \def\macargdeflist@{}%
  \def\saveparamlist@{#2}% Need to keep a copy for parameter expansion.
  \def\paramlist{#2,\nil@}%
  \def\macroname{#1}%
  \begingroup
  \macroargctxt
  \def\argvaluelist{#3,\nil@}%
  \def\@tempa{#3}%
  \ifx\@tempa\empty
    \setemptyargvalues@
  \else
    \getargvals@@
  \fi
}
\def\getargvals@@{%
  \ifx\paramlist\nilm@
      % Some sanity check needed here that \argvaluelist is also empty.
      \ifx\argvaluelist\nillm@
      \else
        \errhelp = \EMsimple
        \errmessage{Too many arguments in macro `\macroname'!}%
      \fi
      \let\next\macargexpandinbody@
  \else
    \ifx\argvaluelist\nillm@
       % No more arguments values passed to macro.  Set remaining named-arg
       % macros to empty.
       \let\next\setemptyargvalues@
    \else
      % pop current arg name into \@tempb
      \def\@tempa##1{\pop@{\@tempb}{\paramlist}##1\endargs@}%
      \expandafter\@tempa\expandafter{\paramlist}%
       % pop current argument value into \@tempc
      \def\@tempa##1{\longpop@{\@tempc}{\argvaluelist}##1\endargs@}%
      \expandafter\@tempa\expandafter{\argvaluelist}%
       % Here \@tempb is the current arg name and \@tempc is the current arg value.
       % First place the new argument macro definition into \@tempd
       \expandafter\macname\expandafter{\@tempc}%
       \expandafter\let\csname macarg.\@tempb\endcsname\relax
       \expandafter\def\expandafter\@tempe\expandafter{%
         \csname macarg.\@tempb\endcsname}%
       \edef\@tempd{\long\def\@tempe{\the\macname}}%
       \push@\@tempd\macargdeflist@
       \let\next\getargvals@@
    \fi
  \fi
  \next
}

\def\push@#1#2{%
  \expandafter\expandafter\expandafter\def
  \expandafter\expandafter\expandafter#2%
  \expandafter\expandafter\expandafter{%
  \expandafter#1#2}%
}

% Replace arguments by their values in the macro body, and place the result
% in macro \@tempa.
%
\def\macvalstoargs@{%
  %  To do this we use the property that token registers that are \the'ed
  % within an \edef  expand only once. So we are going to place all argument
  % values into respective token registers.
  %
  % First we save the token context, and initialize argument numbering.
  \begingroup
    \paramno0\relax
    % Then, for each argument number #N, we place the corresponding argument
    % value into a new token list register \toks#N
    \expandafter\putargsintokens@\saveparamlist@,;,%
    % Then, we expand the body so that argument are replaced by their
    % values. The trick for values not to be expanded themselves is that they
    % are within tokens and that tokens expand only once in an \edef .
    \edef\@tempc{\csname mac.\macroname .body\endcsname}%
    % Now we restore the token stack pointer to free the token list registers
    % which we have used, but we make sure that expanded body is saved after
    % group.
    \expandafter
  \endgroup
  \expandafter\def\expandafter\@tempa\expandafter{\@tempc}%
  }

% Define the named-macro outside of this group and then close this group.
%
\def\macargexpandinbody@{%
  \expandafter
  \endgroup
  \macargdeflist@
  % First the replace in body the macro arguments by their values, the result
  % is in \@tempa .
  \macvalstoargs@
  % Then we point at the \norecurse or \gobble (for recursive) macro value
  % with \@tempb .
  \expandafter\let\expandafter\@tempb\csname mac.\macroname .recurse\endcsname
  % Depending on whether it is recursive or not, we need some tailing
  % \egroup .
  \ifx\@tempb\gobble
     \let\@tempc\relax
  \else
     \let\@tempc\egroup
  \fi
  % And now we do the real job:
  \edef\@tempd{\noexpand\@tempb{\macroname}\noexpand\scanmacro{\@tempa}\@tempc}%
  \@tempd
}

\def\putargsintokens@#1,{%
  \if#1;\let\next\relax
  \else
    \let\next\putargsintokens@
    % First we allocate the new token list register, and give it a temporary
    % alias \@tempb .
    \toksdef\@tempb\the\paramno
    % Then we place the argument value into that token list register.
    \expandafter\let\expandafter\@tempa\csname macarg.#1\endcsname
    \expandafter\@tempb\expandafter{\@tempa}%
    \advance\paramno by 1\relax
  \fi
  \next
}

% Trailing missing arguments are set to empty.
%
\def\setemptyargvalues@{%
  \ifx\paramlist\nilm@
    \let\next\macargexpandinbody@
  \else
    \expandafter\setemptyargvaluesparser@\paramlist\endargs@
    \let\next\setemptyargvalues@
  \fi
  \next
}

\def\setemptyargvaluesparser@#1,#2\endargs@{%
  \expandafter\def\expandafter\@tempa\expandafter{%
    \expandafter\def\csname macarg.#1\endcsname{}}%
  \push@\@tempa\macargdeflist@
  \def\paramlist{#2}%
}

% #1 is the element target macro
% #2 is the list macro
% #3,#4\endargs@ is the list value
\def\pop@#1#2#3,#4\endargs@{%
   \def#1{#3}%
   \def#2{#4}%
}
\long\def\longpop@#1#2#3,#4\endargs@{%
   \long\def#1{#3}%
   \long\def#2{#4}%
}


%%%%%%%%%%%%%% End of code for > 10 arguments %%%%%%%%%%%%%%%%%%


% This defines a Texinfo @macro or @rmacro, called by \parsemacbody.
%    \macrobody has the body of the macro in it, with placeholders for
% its parameters, looking like "\xeatspaces{\hash 1}".
%    \paramno is the number of parameters
%    \paramlist is a TeX parameter text, e.g. "#1,#2,#3,"
% There are four cases: macros of zero, one, up to nine, and many arguments.
% \xdef is used so that macro definitions will survive the file
% they're defined in: @include reads the file inside a group.
%
\def\defmacro{%
  \let\hash=##% convert placeholders to macro parameter chars
  \ifnum\paramno=1
    \def\xeatspaces##1{##1}%
    % This removes the pair of braces around the argument.  We don't
    % use \eatspaces, because this can cause ends of lines to be lost
    % when the argument to \eatspaces is read, leading to line-based
    % commands like "@itemize" not being read correctly.
  \else
    \let\xeatspaces\relax % suppress expansion
  \fi
  \ifcase\paramno
  % 0
    \expandafter\xdef\csname\the\macname\endcsname{%
      \bgroup
        \noexpand\spaceisspace
        \noexpand\endlineisspace
        \noexpand\expandafter % skip any whitespace after the macro name.
        \expandafter\noexpand\csname\the\macname @@@\endcsname}%
    \expandafter\xdef\csname\the\macname @@@\endcsname{%
      \egroup
      \noexpand\scanmacro{\macrobody}}%
  \or % 1
    \expandafter\xdef\csname\the\macname\endcsname{%
       \bgroup
       \noexpand\braceorline
       \expandafter\noexpand\csname\the\macname @@@\endcsname}%
    \expandafter\xdef\csname\the\macname @@@\endcsname##1{%
      \egroup
      \noexpand\scanmacro{\macrobody}%
      }%
  \else % at most 9
    \ifnum\paramno<10\relax
      % @MACNAME sets the context for reading the macro argument
      % @MACNAME@@ gets the argument, processes backslashes and appends a
      % comma.
      % @MACNAME@@@ removes braces surrounding the argument list.
      % @MACNAME@@@@ scans the macro body with arguments substituted.
      \expandafter\xdef\csname\the\macname\endcsname{%
        \bgroup
        \noexpand\expandafter  % This \expandafter skip any spaces after the
        \noexpand\macroargctxt % macro before we change the catcode of space.
        \noexpand\expandafter
        \expandafter\noexpand\csname\the\macname @@\endcsname}%
      \expandafter\xdef\csname\the\macname @@\endcsname##1{%
          \noexpand\passargtomacro
          \expandafter\noexpand\csname\the\macname @@@\endcsname{##1,}}%
      \expandafter\xdef\csname\the\macname @@@\endcsname##1{%
          \expandafter\noexpand\csname\the\macname @@@@\endcsname ##1}%
      \expandafter\expandafter
      \expandafter\xdef
      \expandafter\expandafter
        \csname\the\macname @@@@\endcsname\paramlist{%
          \egroup\noexpand\scanmacro{\macrobody}}%
    \else % 10 or more:
      \expandafter\xdef\csname\the\macname\endcsname{%
        \noexpand\getargvals@{\the\macname}{\argl}%
      }%
      \global\expandafter\let\csname mac.\the\macname .body\endcsname\macrobody
      \global\expandafter\let\csname mac.\the\macname .recurse\endcsname\gobble
    \fi
  \fi}

\catcode `\@\texiatcatcode\relax % end private-to-Texinfo catcodes

\def\norecurse#1{\bgroup\cslet{#1}{macsave.#1}}


%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%
%
{\catcode`\@=0 \catcode`\\=13  % We need to manipulate \ so use @ as escape
@catcode`@_=11  % private names
@catcode`@!=11  % used as argument separator

% \passargtomacro#1#2 -
% Call #1 with a list of tokens #2, with any doubled backslashes in #2
% compressed to one.
%
% This implementation works by expansion, and not execution (so we cannot use
% \def or similar).  This reduces the risk of this failing in contexts where
% complete expansion is done with no execution (for example, in writing out to
% an auxiliary file for an index entry).
%
% State is kept in the input stream: the argument passed to
% @look_ahead, @gobble_and_check_finish and @add_segment is
%
% THE_MACRO ARG_RESULT ! {PENDING_BS} NEXT_TOKEN  (... rest of input)
%
% where:
% THE_MACRO - name of the macro we want to call
% ARG_RESULT - argument list we build to pass to that macro
% PENDING_BS - either a backslash or nothing
% NEXT_TOKEN - used to look ahead in the input stream to see what's coming next

@gdef@passargtomacro#1#2{%
  @add_segment #1!{}@relax#2\@_finish\%
}
@gdef@_finish{@_finishx} @global@let@_finishx@relax

% #1 - THE_MACRO ARG_RESULT
% #2 - PENDING_BS
% #3 - NEXT_TOKEN
% #4 used to look ahead
%
% If the next token is not a backslash, process the rest of the argument;
% otherwise, remove the next token.
@gdef@look_ahead#1!#2#3#4{%
  @ifx#4\%
   @expandafter@gobble_and_check_finish
  @else
   @expandafter@add_segment
  @fi#1!{#2}#4#4%
}

% #1 - THE_MACRO ARG_RESULT
% #2 - PENDING_BS
% #3 - NEXT_TOKEN
% #4 should be a backslash, which is gobbled.
% #5 looks ahead
%
% Double backslash found.  Add a single backslash, and look ahead.
@gdef@gobble_and_check_finish#1!#2#3#4#5{%
  @add_segment#1\!{}#5#5%
}

@gdef@is_fi{@fi}

% #1 - THE_MACRO ARG_RESULT
% #2 - PENDING_BS
% #3 - NEXT_TOKEN
% #4 is input stream until next backslash
%
% Input stream is either at the start of the argument, or just after a
% backslash sequence, either a lone backslash, or a doubled backslash.
% NEXT_TOKEN contains the first token in the input stream: if it is \finish,
% finish; otherwise, append to ARG_RESULT the segment of the argument up until
% the next backslash.  PENDING_BACKSLASH contains a backslash to represent
% a backslash just before the start of the input stream that has not been
% added to ARG_RESULT.
@gdef@add_segment#1!#2#3#4\{%
@ifx#3@_finish
  @call_the_macro#1!%
@else
  % append the pending backslash to the result, followed by the next segment
  @expandafter@is_fi@look_ahead#1#2#4!{\}@fi
  % this @fi is discarded by @look_ahead.
  % we can't get rid of it with \expandafter because we don't know how
  % long #4 is.
}

% #1 - THE_MACRO
% #2 - ARG_RESULT
% #3 discards the res of the conditional in @add_segment, and @is_fi ends the
% conditional.
@gdef@call_the_macro#1#2!#3@fi{@is_fi #1{#2}}

}
%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%

% \braceorline MAC is used for a one-argument macro MAC.  It checks
% whether the next non-whitespace character is a {.  It sets the context
% for reading the argument (slightly different in the two cases).  Then,
% to read the argument, in the whole-line case, it then calls the regular
% \parsearg MAC; in the lbrace case, it calls \passargtomacro MAC.
%
\def\braceorline#1{\let\macnamexxx=#1\futurelet\nchar\braceorlinexxx}
\def\braceorlinexxx{%
  \ifx\nchar\bgroup
    \macroargctxt
    \expandafter\passargtomacro
  \else
    \macrolineargctxt\expandafter\parsearg
  \fi \macnamexxx}


% @alias.
% We need some trickery to remove the optional spaces around the equal
% sign.  Make them active and then expand them all to nothing.
%
\def\alias{\parseargusing\obeyspaces\aliasxxx}
\def\aliasxxx #1{\aliasyyy#1\relax}
\def\aliasyyy #1=#2\relax{%
  {%
    \expandafter\let\obeyedspace=\empty
    \addtomacrolist{#1}%
    \xdef\next{\global\let\makecsname{#1}=\makecsname{#2}}%
  }%
  \next
}


\message{cross references,}

\newwrite\auxfile
\newif\ifhavexrefs    % True if xref values are known.
\newif\ifwarnedxrefs  % True if we warned once that they aren't known.

% @inforef is relatively simple.
\def\inforef #1{\inforefzzz #1,,,,**}
\def\inforefzzz #1,#2,#3,#4**{%
  \putwordSee{} \putwordInfo{} \putwordfile{} \file{\ignorespaces #3{}},
  node \samp{\ignorespaces#1{}}}

% @node's only job in TeX is to define \lastnode, which is used in
% cross-references.  The @node line might or might not have commas, and
% might or might not have spaces before the first comma, like:
% @node foo , bar , ...
% We don't want such trailing spaces in the node name.
%
\parseargdef\node{\checkenv{}\donode #1 ,\finishnodeparse}
%
% also remove a trailing comma, in case of something like this:
% @node Help-Cross,  ,  , Cross-refs
\def\donode#1 ,#2\finishnodeparse{\dodonode #1,\finishnodeparse}
\def\dodonode#1,#2\finishnodeparse{\gdef\lastnode{#1}\omittopnode}

% Used so that the @top node doesn't have to be wrapped in an @ifnottex
% conditional.
% \doignore goes to more effort to skip nested conditionals but we don't need
% that here.
\def\omittopnode{%
   \ifx\lastnode\wordTop
   \expandafter\ignorenode\fi
}
\def\wordTop{Top}

% Until the next @node or @bye command, divert output to a box that is not
% output.
\def\ignorenode{\setbox\dummybox\vbox\bgroup\def\node{\egroup\node}%
\ignorenodebye
}

{\let\bye\relax
\gdef\ignorenodebye{\let\bye\ignorenodebyedef}
\gdef\ignorenodebyedef{\egroup(`Top' node ignored)\bye}}
% The redefinition of \bye here is because it is declared \outer

\let\lastnode=\empty

% Write a cross-reference definition for the current node.  #1 is the
% type (Ynumbered, Yappendix, Ynothing).
%
\def\donoderef#1{%
  \ifx\lastnode\empty\else
    \setref{\lastnode}{#1}%
    \global\let\lastnode=\empty
  \fi
}

% @anchor{NAME} -- define xref target at arbitrary point.
%
\newcount\savesfregister
%
\def\savesf{\relax \ifhmode \savesfregister=\spacefactor \fi}
\def\restoresf{\relax \ifhmode \spacefactor=\savesfregister \fi}
\def\anchor#1{\savesf \setref{#1}{Ynothing}\restoresf \ignorespaces}

% \setref{NAME}{SNT} defines a cross-reference point NAME (a node or an
% anchor), which consists of three parts:
% 1) NAME-title - the current sectioning name taken from \currentsection,
%                 or the anchor name.
% 2) NAME-snt   - section number and type, passed as the SNT arg, or
%                 empty for anchors.
% 3) NAME-pg    - the page number.
%
% This is called from \donoderef, \anchor, and \dofloat.  In the case of
% floats, there is an additional part, which is not written here:
% 4) NAME-lof   - the text as it should appear in a @listoffloats.
%
\def\setref#1#2{%
  \pdfmkdest{#1}%
  \iflinks
    {%
      \requireauxfile
      \atdummies  % preserve commands, but don't expand them
      % match definition in \xrdef, \refx, \xrefX.
      \def\value##1{##1}%
      \edef\writexrdef##1##2{%
	\write\auxfile{@xrdef{#1-% #1 of \setref, expanded by the \edef
	  ##1}{##2}}% these are parameters of \writexrdef
      }%
      \toks0 = \expandafter{\currentsection}%
      \immediate \writexrdef{title}{\the\toks0 }%
      \immediate \writexrdef{snt}{\csname #2\endcsname}% \Ynumbered etc.
      \safewhatsit{\writexrdef{pg}{\folio}}% will be written later, at \shipout
    }%
  \fi
}

% @xrefautosectiontitle on|off says whether @section(ing) names are used
% automatically in xrefs, if the third arg is not explicitly specified.
% This was provided as a "secret" @set xref-automatic-section-title
% variable, now it's official.
%
\parseargdef\xrefautomaticsectiontitle{%
  \def\temp{#1}%
  \ifx\temp\onword
    \expandafter\let\csname SETxref-automatic-section-title\endcsname
      = \empty
  \else\ifx\temp\offword
    \expandafter\let\csname SETxref-automatic-section-title\endcsname
      = \relax
  \else
    \errhelp = \EMsimple
    \errmessage{Unknown @xrefautomaticsectiontitle value `\temp',
                must be on|off}%
  \fi\fi
}

%
% @xref, @pxref, and @ref generate cross-references.  For \xrefX, #1 is
% the node name, #2 the name of the Info cross-reference, #3 the printed
% node name, #4 the name of the Info file, #5 the name of the printed
% manual.  All but the node name can be omitted.
%
\def\pxref{\putwordsee{} \xrefXX}
\def\xref{\putwordSee{} \xrefXX}
\def\ref{\xrefXX}

\def\xrefXX#1{\def\xrefXXarg{#1}\futurelet\tokenafterxref\xrefXXX}
\def\xrefXXX{\expandafter\xrefX\expandafter[\xrefXXarg,,,,,,,]}
%
\newbox\toprefbox
\newbox\printedrefnamebox
\newbox\infofilenamebox
\newbox\printedmanualbox
%
\def\xrefX[#1,#2,#3,#4,#5,#6]{\begingroup
  \unsepspaces
  %
  % Get args without leading/trailing spaces.
  \def\printedrefname{\ignorespaces #3}%
  \setbox\printedrefnamebox = \hbox{\printedrefname\unskip}%
  %
  \def\infofilename{\ignorespaces #4}%
  \setbox\infofilenamebox = \hbox{\infofilename\unskip}%
  %
  \def\printedmanual{\ignorespaces #5}%
  \setbox\printedmanualbox  = \hbox{\printedmanual\unskip}%
  %
  % If the printed reference name (arg #3) was not explicitly given in
  % the @xref, figure out what we want to use.
  \ifdim \wd\printedrefnamebox = 0pt
    % No printed node name was explicitly given.
    \expandafter\ifx\csname SETxref-automatic-section-title\endcsname \relax
      % Not auto section-title: use node name inside the square brackets.
      \def\printedrefname{\ignorespaces #1}%
    \else
      % Auto section-title: use chapter/section title inside
      % the square brackets if we have it.
      \ifdim \wd\printedmanualbox > 0pt
        % It is in another manual, so we don't have it; use node name.
        \def\printedrefname{\ignorespaces #1}%
      \else
        \ifhavexrefs
          % We (should) know the real title if we have the xref values.
          \def\printedrefname{\refx{#1-title}{}}%
        \else
          % Otherwise just copy the Info node name.
          \def\printedrefname{\ignorespaces #1}%
        \fi%
      \fi
    \fi
  \fi
  %
  % Make link in pdf output.
  \ifpdf
    % For pdfTeX and LuaTeX
    {\indexnofonts
     \makevalueexpandable
     \turnoffactive
     % This expands tokens, so do it after making catcode changes, so _
     % etc. don't get their TeX definitions.  This ignores all spaces in
     % #4, including (wrongly) those in the middle of the filename.
     \getfilename{#4}%
     %
     % This (wrongly) does not take account of leading or trailing
     % spaces in #1, which should be ignored.
     \setpdfdestname{#1}%
     %
     \ifx\pdfdestname\empty
       \def\pdfdestname{Top}% no empty targets
     \fi
     %
     \leavevmode
     \startlink attr{/Border [0 0 0]}%
     \ifnum\filenamelength>0
       goto file{\the\filename.pdf} name{\pdfdestname}%
     \else
       goto name{\pdfmkpgn{\pdfdestname}}%
     \fi
    }%
    \setcolor{\linkcolor}%
  \else
    \ifx\XeTeXrevision\thisisundefined
    \else
      % For XeTeX
      {\indexnofonts
       \makevalueexpandable
       \turnoffactive
       % This expands tokens, so do it after making catcode changes, so _
       % etc. don't get their TeX definitions.  This ignores all spaces in
       % #4, including (wrongly) those in the middle of the filename.
       \getfilename{#4}%
       %
       % This (wrongly) does not take account of leading or trailing
       % spaces in #1, which should be ignored.
       \setpdfdestname{#1}%
       %
       \ifx\pdfdestname\empty
         \def\pdfdestname{Top}% no empty targets
       \fi
       %
       \leavevmode
       \ifnum\filenamelength>0
         % With default settings,
         % XeTeX (xdvipdfmx) replaces link destination names with integers.
         % In this case, the replaced destination names of
         % remote PDFs are no longer known.  In order to avoid a replacement,
         % you can use xdvipdfmx's command line option `-C 0x0010'.
         % If you use XeTeX 0.99996+ (TeX Live 2016+),
         % this command line option is no longer necessary
         % because we can use the `dvipdfmx:config' special.
         \special{pdf:bann << /Border [0 0 0] /Type /Annot /Subtype /Link /A
           << /S /GoToR /F (\the\filename.pdf) /D (\pdfdestname) >> >>}%
       \else
         \special{pdf:bann << /Border [0 0 0] /Type /Annot /Subtype /Link /A
           << /S /GoTo /D (\pdfdestname) >> >>}%
       \fi
      }%
      \setcolor{\linkcolor}%
    \fi
  \fi
  {%
    % Have to otherify everything special to allow the \csname to
    % include an _ in the xref name, etc.
    \indexnofonts
    \turnoffactive
    \def\value##1{##1}%
    \expandafter\global\expandafter\let\expandafter\Xthisreftitle
      \csname XR#1-title\endcsname
  }%
  %
  % Float references are printed completely differently: "Figure 1.2"
  % instead of "[somenode], p.3".  \iffloat distinguishes them by
  % \Xthisreftitle being set to a magic string.
  \iffloat\Xthisreftitle
    % If the user specified the print name (third arg) to the ref,
    % print it instead of our usual "Figure 1.2".
    \ifdim\wd\printedrefnamebox = 0pt
      \refx{#1-snt}{}%
    \else
      \printedrefname
    \fi
    %
    % If the user also gave the printed manual name (fifth arg), append
    % "in MANUALNAME".
    \ifdim \wd\printedmanualbox > 0pt
      \space \putwordin{} \cite{\printedmanual}%
    \fi
  \else
    % node/anchor (non-float) references.
    %
    % If we use \unhbox to print the node names, TeX does not insert
    % empty discretionaries after hyphens, which means that it will not
    % find a line break at a hyphen in a node names.  Since some manuals
    % are best written with fairly long node names, containing hyphens,
    % this is a loss.  Therefore, we give the text of the node name
    % again, so it is as if TeX is seeing it for the first time.
    %
    \ifdim \wd\printedmanualbox > 0pt
      % Cross-manual reference with a printed manual name.
      %
      \crossmanualxref{\cite{\printedmanual\unskip}}%
    %
    \else\ifdim \wd\infofilenamebox > 0pt
      % Cross-manual reference with only an info filename (arg 4), no
      % printed manual name (arg 5).  This is essentially the same as
      % the case above; we output the filename, since we have nothing else.
      %
      \crossmanualxref{\code{\infofilename\unskip}}%
    %
    \else
      % Reference within this manual.
      %
      % Only output a following space if the -snt ref is nonempty; for
      % @unnumbered and @anchor, it won't be.
      \setbox2 = \hbox{\ignorespaces \refx{#1-snt}{}}%
      \ifdim \wd2 > 0pt \refx{#1-snt}\space\fi
      %
      % output the `[mynode]' via the macro below so it can be overridden.
      \xrefprintnodename\printedrefname
      %
      % But we always want a comma and a space:
      ,\space
      %
      % output the `page 3'.
      \turnoffactive \putwordpage\tie\refx{#1-pg}{}%
      % Add a , if xref followed by a space
      \if\space\noexpand\tokenafterxref ,%
      \else\ifx\	\tokenafterxref ,% @TAB
      \else\ifx\*\tokenafterxref ,%   @*
      \else\ifx\ \tokenafterxref ,%   @SPACE
      \else\ifx\
                \tokenafterxref ,%    @NL
      \else\ifx\tie\tokenafterxref ,% @tie
      \fi\fi\fi\fi\fi\fi
    \fi\fi
  \fi
  \endlink
\endgroup}

% Output a cross-manual xref to #1.  Used just above (twice).
%
% Only include the text "Section ``foo'' in" if the foo is neither
% missing or Top.  Thus, @xref{,,,foo,The Foo Manual} outputs simply
% "see The Foo Manual", the idea being to refer to the whole manual.
%
% But, this being TeX, we can't easily compare our node name against the
% string "Top" while ignoring the possible spaces before and after in
% the input.  By adding the arbitrary 7sp below, we make it much less
% likely that a real node name would have the same width as "Top" (e.g.,
% in a monospaced font).  Hopefully it will never happen in practice.
%
% For the same basic reason, we retypeset the "Top" at every
% reference, since the current font is indeterminate.
%
\def\crossmanualxref#1{%
  \setbox\toprefbox = \hbox{Top\kern7sp}%
  \setbox2 = \hbox{\ignorespaces \printedrefname \unskip \kern7sp}%
  \ifdim \wd2 > 7sp  % nonempty?
    \ifdim \wd2 = \wd\toprefbox \else  % same as Top?
      \putwordSection{} ``\printedrefname'' \putwordin{}\space
    \fi
  \fi
  #1%
}

% This macro is called from \xrefX for the `[nodename]' part of xref
% output.  It's a separate macro only so it can be changed more easily,
% since square brackets don't work well in some documents.  Particularly
% one that Bob is working on :).
%
\def\xrefprintnodename#1{[#1]}

% Things referred to by \setref.
%
\def\Ynothing{}
\def\Yomitfromtoc{}
\def\Ynumbered{%
  \ifnum\secno=0
    \putwordChapter@tie \the\chapno
  \else \ifnum\subsecno=0
    \putwordSection@tie \the\chapno.\the\secno
  \else \ifnum\subsubsecno=0
    \putwordSection@tie \the\chapno.\the\secno.\the\subsecno
  \else
    \putwordSection@tie \the\chapno.\the\secno.\the\subsecno.\the\subsubsecno
  \fi\fi\fi
}
\def\Yappendix{%
  \ifnum\secno=0
     \putwordAppendix@tie @char\the\appendixno{}%
  \else \ifnum\subsecno=0
     \putwordSection@tie @char\the\appendixno.\the\secno
  \else \ifnum\subsubsecno=0
    \putwordSection@tie @char\the\appendixno.\the\secno.\the\subsecno
  \else
    \putwordSection@tie
      @char\the\appendixno.\the\secno.\the\subsecno.\the\subsubsecno
  \fi\fi\fi
}

% \refx{NAME}{SUFFIX} - reference a cross-reference string named NAME.  SUFFIX
% is output afterwards if non-empty.
\def\refx#1#2{%
  \requireauxfile
  {%
    \indexnofonts
    \turnoffactive
    \def\value##1{##1}%
    \expandafter\global\expandafter\let\expandafter\thisrefX
      \csname XR#1\endcsname
  }%
  \ifx\thisrefX\relax
    % If not defined, say something at least.
    \angleleft un\-de\-fined\angleright
    \iflinks
      \ifhavexrefs
        {\toks0 = {#1}% avoid expansion of possibly-complex value
         \message{\linenumber Undefined cross reference `\the\toks0'.}}%
      \else
        \ifwarnedxrefs\else
          \global\warnedxrefstrue
          \message{Cross reference values unknown; you must run TeX again.}%
        \fi
      \fi
    \fi
  \else
    % It's defined, so just use it.
    \thisrefX
  \fi
  #2% Output the suffix in any case.
}

% This is the macro invoked by entries in the aux file.  Define a control
% sequence for a cross-reference target (we prepend XR to the control sequence
% name to avoid collisions).  The value is the page number.  If this is a float
% type, we have more work to do.
%
\def\xrdef#1#2{%
  {% Expand the node or anchor name to remove control sequences.
   % \turnoffactive stops 8-bit characters being changed to commands
   % like @'e.  \refx does the same to retrieve the value in the definition.
    \indexnofonts
    \turnoffactive
    \def\value##1{##1}%
    \xdef\safexrefname{#1}%
  }%
  %
  \bgroup
    \expandafter\gdef\csname XR\safexrefname\endcsname{#2}%
  \egroup
  % We put the \gdef inside a group to avoid the definitions building up on
  % TeX's save stack, which can cause it to run out of space for aux files with
  % thousands of lines.  \gdef doesn't use the save stack, but \csname does
  % when it defines an unknown control sequence as \relax.
  %
  % Was that xref control sequence that we just defined for a float?
  \expandafter\iffloat\csname XR\safexrefname\endcsname
    % it was a float, and we have the (safe) float type in \iffloattype.
    \expandafter\let\expandafter\floatlist
      \csname floatlist\iffloattype\endcsname
    %
    % Is this the first time we've seen this float type?
    \expandafter\ifx\floatlist\relax
      \toks0 = {\do}% yes, so just \do
    \else
      % had it before, so preserve previous elements in list.
      \toks0 = \expandafter{\floatlist\do}%
    \fi
    %
    % Remember this xref in the control sequence \floatlistFLOATTYPE,
    % for later use in \listoffloats.
    \expandafter\xdef\csname floatlist\iffloattype\endcsname{\the\toks0
      {\safexrefname}}%
  \fi
}

% If working on a large document in chapters, it is convenient to
% be able to disable indexing, cross-referencing, and contents, for test runs.
% This is done with @novalidate at the beginning of the file.
%
\newif\iflinks \linkstrue % by default we want the aux files.
\let\novalidate = \linksfalse

% Used when writing to the aux file, or when using data from it.
\def\requireauxfile{%
  \iflinks
    \tryauxfile
    % Open the new aux file.  TeX will close it automatically at exit.
    \immediate\openout\auxfile=\jobname.aux
  \fi
  \global\let\requireauxfile=\relax   % Only do this once.
}

% Read the last existing aux file, if any.  No error if none exists.
%
\def\tryauxfile{%
  \openin 1 \jobname.aux
  \ifeof 1 \else
    \readdatafile{aux}%
    \global\havexrefstrue
  \fi
  \closein 1
}

\def\setupdatafile{%
  \catcode`\^^@=\other
  \catcode`\^^A=\other
  \catcode`\^^B=\other
  \catcode`\^^C=\other
  \catcode`\^^D=\other
  \catcode`\^^E=\other
  \catcode`\^^F=\other
  \catcode`\^^G=\other
  \catcode`\^^H=\other
  \catcode`\^^K=\other
  \catcode`\^^L=\other
  \catcode`\^^N=\other
  \catcode`\^^P=\other
  \catcode`\^^Q=\other
  \catcode`\^^R=\other
  \catcode`\^^S=\other
  \catcode`\^^T=\other
  \catcode`\^^U=\other
  \catcode`\^^V=\other
  \catcode`\^^W=\other
  \catcode`\^^X=\other
  \catcode`\^^Z=\other
  \catcode`\^^[=\other
  \catcode`\^^\=\other
  \catcode`\^^]=\other
  \catcode`\^^^=\other
  \catcode`\^^_=\other
  \catcode`\^=\other
  %
  % Special characters.  Should be turned off anyway, but...
  \catcode`\~=\other
  \catcode`\[=\other
  \catcode`\]=\other
  \catcode`\"=\other
  \catcode`\_=\other
  \catcode`\|=\other
  \catcode`\<=\other
  \catcode`\>=\other
  \catcode`\$=\other
  \catcode`\#=\other
  \catcode`\&=\other
  \catcode`\%=\other
  \catcode`+=\other % avoid \+ for paranoia even though we've turned it off
  %
  \catcode`\\=\active
  %
  % @ is our escape character in .aux files, and we need braces.
  \catcode`\{=1
  \catcode`\}=2
  \catcode`\@=0
}

\def\readdatafile#1{%
\begingroup
  \setupdatafile
  \input\jobname.#1
\endgroup}


\message{insertions,}
% including footnotes.

\newcount \footnoteno

% The trailing space in the following definition for supereject is
% vital for proper filling; pages come out unaligned when you do a
% pagealignmacro call if that space before the closing brace is
% removed. (Generally, numeric constants should always be followed by a
% space to prevent strange expansion errors.)
\def\supereject{\par\penalty -20000\footnoteno =0 }

% @footnotestyle is meaningful for Info output only.
\let\footnotestyle=\comment

{\catcode `\@=11
%
% Auto-number footnotes.  Otherwise like plain.
\gdef\footnote{%
  \global\advance\footnoteno by \@ne
  \edef\thisfootno{$^{\the\footnoteno}$}%
  %
  % In case the footnote comes at the end of a sentence, preserve the
  % extra spacing after we do the footnote number.
  \let\@sf\empty
  \ifhmode\edef\@sf{\spacefactor\the\spacefactor}\ptexslash\fi
  %
  % Remove inadvertent blank space before typesetting the footnote number.
  \unskip
  \thisfootno\@sf
  \dofootnote
}%

% Don't bother with the trickery in plain.tex to not require the
% footnote text as a parameter.  Our footnotes don't need to be so general.
%
% Oh yes, they do; otherwise, @ifset (and anything else that uses
% \parseargline) fails inside footnotes because the tokens are fixed when
% the footnote is read.  --karl, 16nov96.
%
\gdef\dofootnote{%
  \insert\footins\bgroup
  %
  % Nested footnotes are not supported in TeX, that would take a lot
  % more work.  (\startsavinginserts does not suffice.)
  \let\footnote=\errfootnotenest
  %
  % We want to typeset this text as a normal paragraph, even if the
  % footnote reference occurs in (for example) a display environment.
  % So reset some parameters.
  \hsize=\txipagewidth
  \interlinepenalty\interfootnotelinepenalty
  \splittopskip\ht\strutbox % top baseline for broken footnotes
  \splitmaxdepth\dp\strutbox
  \floatingpenalty\@MM
  \leftskip\z@skip
  \rightskip\z@skip
  \spaceskip\z@skip
  \xspaceskip\z@skip
  \parindent\defaultparindent
  %
  \smallfonts \rm
  %
  % Because we use hanging indentation in footnotes, a @noindent appears
  % to exdent this text, so make it be a no-op.  makeinfo does not use
  % hanging indentation so @noindent can still be needed within footnote
  % text after an @example or the like (not that this is good style).
  \let\noindent = \relax
  %
  % Hang the footnote text off the number.  Use \everypar in case the
  % footnote extends for more than one paragraph.
  \everypar = {\hang}%
  \textindent{\thisfootno}%
  %
  % Don't crash into the line above the footnote text.  Since this
  % expands into a box, it must come within the paragraph, lest it
  % provide a place where TeX can split the footnote.
  \footstrut
  %
  % Invoke rest of plain TeX footnote routine.
  \futurelet\next\fo@t
}
}%end \catcode `\@=11

\def\errfootnotenest{%
  \errhelp=\EMsimple
  \errmessage{Nested footnotes not supported in texinfo.tex,
    even though they work in makeinfo; sorry}
}

\def\errfootnoteheading{%
  \errhelp=\EMsimple
  \errmessage{Footnotes in chapters, sections, etc., are not supported}
}

% In case a @footnote appears in a vbox, save the footnote text and create
% the real \insert just after the vbox finished.  Otherwise, the insertion
% would be lost.
% Similarly, if a @footnote appears inside an alignment, save the footnote
% text to a box and make the \insert when a row of the table is finished.
% And the same can be done for other insert classes.  --kasal, 16nov03.
%
% Replace the \insert primitive by a cheating macro.
% Deeper inside, just make sure that the saved insertions are not spilled
% out prematurely.
%
\def\startsavinginserts{%
  \ifx \insert\ptexinsert
    \let\insert\saveinsert
  \else
    \let\checkinserts\relax
  \fi
}

% This \insert replacement works for both \insert\footins{foo} and
% \insert\footins\bgroup foo\egroup, but it doesn't work for \insert27{foo}.
%
\def\saveinsert#1{%
  \edef\next{\noexpand\savetobox \makeSAVEname#1}%
  \afterassignment\next
  % swallow the left brace
  \let\temp =
}
\def\makeSAVEname#1{\makecsname{SAVE\expandafter\gobble\string#1}}
\def\savetobox#1{\global\setbox#1 = \vbox\bgroup \unvbox#1}

\def\checksaveins#1{\ifvoid#1\else \placesaveins#1\fi}

\def\placesaveins#1{%
  \ptexinsert \csname\expandafter\gobblesave\string#1\endcsname
    {\box#1}%
}

% eat @SAVE -- beware, all of them have catcode \other:
{
  \def\dospecials{\do S\do A\do V\do E} \uncatcodespecials  %  ;-)
  \gdef\gobblesave @SAVE{}
}

% initialization:
\def\newsaveins #1{%
  \edef\next{\noexpand\newsaveinsX \makeSAVEname#1}%
  \next
}
\def\newsaveinsX #1{%
  \csname newbox\endcsname #1%
  \expandafter\def\expandafter\checkinserts\expandafter{\checkinserts
    \checksaveins #1}%
}

% initialize:
\let\checkinserts\empty
\newsaveins\footins
\newsaveins\margin


% @image.  We use the macros from epsf.tex to support this.
% If epsf.tex is not installed and @image is used, we complain.
%
% Check for and read epsf.tex up front.  If we read it only at @image
% time, we might be inside a group, and then its definitions would get
% undone and the next image would fail.
\openin 1 = epsf.tex
\ifeof 1 \else
  % Do not bother showing banner with epsf.tex v2.7k (available in
  % doc/epsf.tex and on ctan).
  \def\epsfannounce{\toks0 = }%
  \input epsf.tex
\fi
\closein 1
%
% We will only complain once about lack of epsf.tex.
\newif\ifwarnednoepsf
\newhelp\noepsfhelp{epsf.tex must be installed for images to
  work.  It is also included in the Texinfo distribution, or you can get
  it from https://ctan.org/texarchive/macros/texinfo/texinfo/doc/epsf.tex.}
%
\def\image#1{%
  \ifx\epsfbox\thisisundefined
    \ifwarnednoepsf \else
      \errhelp = \noepsfhelp
      \errmessage{epsf.tex not found, images will be ignored}%
      \global\warnednoepsftrue
    \fi
  \else
    \imagexxx #1,,,,,\finish
  \fi
}
%
% Arguments to @image:
% #1 is (mandatory) image filename; we tack on .eps extension.
% #2 is (optional) width, #3 is (optional) height.
% #4 is (ignored optional) html alt text.
% #5 is (ignored optional) extension.
% #6 is just the usual extra ignored arg for parsing stuff.
\newif\ifimagevmode
\def\imagexxx#1,#2,#3,#4,#5,#6\finish{\begingroup
  \catcode`\^^M = 5     % in case we're inside an example
  \normalturnoffactive  % allow _ et al. in names
  \def\xprocessmacroarg{\eatspaces}% in case we are being used via a macro
  % If the image is by itself, center it.
  \ifvmode
    \imagevmodetrue
  \else \ifx\centersub\centerV
    % for @center @image, we need a vbox so we can have our vertical space
    \imagevmodetrue
    \vbox\bgroup % vbox has better behavior than vtop herev
  \fi\fi
  %
  \ifimagevmode
    \nobreak\medskip
    % Usually we'll have text after the image which will insert
    % \parskip glue, so insert it here too to equalize the space
    % above and below.
    \nobreak\vskip\parskip
    \nobreak
  \fi
  %
  % Leave vertical mode so that indentation from an enclosing
  %  environment such as @quotation is respected.
  % However, if we're at the top level, we don't want the
  %  normal paragraph indentation.
  % On the other hand, if we are in the case of @center @image, we don't
  %  want to start a paragraph, which will create a hsize-width box and
  %  eradicate the centering.
  \ifx\centersub\centerV\else \noindent \fi
  %
  % Output the image.
  \ifpdf
    % For pdfTeX and LuaTeX <= 0.80
    \dopdfimage{#1}{#2}{#3}%
  \else
    \ifx\XeTeXrevision\thisisundefined
      % For epsf.tex
      % \epsfbox itself resets \epsf?size at each figure.
      \setbox0 = \hbox{\ignorespaces #2}%
        \ifdim\wd0 > 0pt \epsfxsize=#2\relax \fi
      \setbox0 = \hbox{\ignorespaces #3}%
        \ifdim\wd0 > 0pt \epsfysize=#3\relax \fi
      \epsfbox{#1.eps}%
    \else
      % For XeTeX
      \doxeteximage{#1}{#2}{#3}%
    \fi
  \fi
  %
  \ifimagevmode
    \medskip  % space after a standalone image
  \fi
  \ifx\centersub\centerV \egroup \fi
\endgroup}


% @float FLOATTYPE,LABEL,LOC ... @end float for displayed figures, tables,
% etc.  We don't actually implement floating yet, we always include the
% float "here".  But it seemed the best name for the future.
%
\envparseargdef\float{\eatcommaspace\eatcommaspace\dofloat#1, , ,\finish}

% There may be a space before second and/or third parameter; delete it.
\def\eatcommaspace#1, {#1,}

% #1 is the optional FLOATTYPE, the text label for this float, typically
% "Figure", "Table", "Example", etc.  Can't contain commas.  If omitted,
% this float will not be numbered and cannot be referred to.
%
% #2 is the optional xref label.  Also must be present for the float to
% be referable.
%
% #3 is the optional positioning argument; for now, it is ignored.  It
% will somehow specify the positions allowed to float to (here, top, bottom).
%
% We keep a separate counter for each FLOATTYPE, which we reset at each
% chapter-level command.
\let\resetallfloatnos=\empty
%
\def\dofloat#1,#2,#3,#4\finish{%
  \let\thiscaption=\empty
  \let\thisshortcaption=\empty
  %
  % don't lose footnotes inside @float.
  %
  % BEWARE: when the floats start float, we have to issue warning whenever an
  % insert appears inside a float which could possibly float. --kasal, 26may04
  %
  \startsavinginserts
  %
  % We can't be used inside a paragraph.
  \par
  %
  \vtop\bgroup
    \def\floattype{#1}%
    \def\floatlabel{#2}%
    \def\floatloc{#3}% we do nothing with this yet.
    %
    \ifx\floattype\empty
      \let\safefloattype=\empty
    \else
      {%
        % the floattype might have accents or other special characters,
        % but we need to use it in a control sequence name.
        \indexnofonts
        \turnoffactive
        \xdef\safefloattype{\floattype}%
      }%
    \fi
    %
    % If label is given but no type, we handle that as the empty type.
    \ifx\floatlabel\empty \else
      % We want each FLOATTYPE to be numbered separately (Figure 1,
      % Table 1, Figure 2, ...).  (And if no label, no number.)
      %
      \expandafter\getfloatno\csname\safefloattype floatno\endcsname
      \global\advance\floatno by 1
      %
      {%
        % This magic value for \currentsection is output by \setref as the
        % XREFLABEL-title value.  \xrefX uses it to distinguish float
        % labels (which have a completely different output format) from
        % node and anchor labels.  And \xrdef uses it to construct the
        % lists of floats.
        %
        \edef\currentsection{\floatmagic=\safefloattype}%
        \setref{\floatlabel}{Yfloat}%
      }%
    \fi
    %
    % start with \parskip glue, I guess.
    \vskip\parskip
    %
    % Don't suppress indentation if a float happens to start a section.
    \restorefirstparagraphindent
}

% we have these possibilities:
% @float Foo,lbl & @caption{Cap}: Foo 1.1: Cap
% @float Foo,lbl & no caption:    Foo 1.1
% @float Foo & @caption{Cap}:     Foo: Cap
% @float Foo & no caption:        Foo
% @float ,lbl & Caption{Cap}:     1.1: Cap
% @float ,lbl & no caption:       1.1
% @float & @caption{Cap}:         Cap
% @float & no caption:
%
\def\Efloat{%
    \let\floatident = \empty
    %
    % In all cases, if we have a float type, it comes first.
    \ifx\floattype\empty \else \def\floatident{\floattype}\fi
    %
    % If we have an xref label, the number comes next.
    \ifx\floatlabel\empty \else
      \ifx\floattype\empty \else % if also had float type, need tie first.
        \appendtomacro\floatident{\tie}%
      \fi
      % the number.
      \appendtomacro\floatident{\chaplevelprefix\the\floatno}%
    \fi
    %
    % Start the printed caption with what we've constructed in
    % \floatident, but keep it separate; we need \floatident again.
    \let\captionline = \floatident
    %
    \ifx\thiscaption\empty \else
      \ifx\floatident\empty \else
        \appendtomacro\captionline{: }% had ident, so need a colon between
      \fi
      %
      % caption text.
      \appendtomacro\captionline{\scanexp\thiscaption}%
    \fi
    %
    % If we have anything to print, print it, with space before.
    % Eventually this needs to become an \insert.
    \ifx\captionline\empty \else
      \vskip.5\parskip
      \captionline
      %
      % Space below caption.
      \vskip\parskip
    \fi
    %
    % If have an xref label, write the list of floats info.  Do this
    % after the caption, to avoid chance of it being a breakpoint.
    \ifx\floatlabel\empty \else
      % Write the text that goes in the lof to the aux file as
      % \floatlabel-lof.  Besides \floatident, we include the short
      % caption if specified, else the full caption if specified, else nothing.
      {%
        \requireauxfile
        \atdummies
        %
        \ifx\thisshortcaption\empty
          \def\gtemp{\thiscaption}%
        \else
          \def\gtemp{\thisshortcaption}%
        \fi
        \immediate\write\auxfile{@xrdef{\floatlabel-lof}{\floatident
          \ifx\gtemp\empty \else : \gtemp \fi}}%
      }%
    \fi
  \egroup  % end of \vtop
  %
  \checkinserts
}

% Append the tokens #2 to the definition of macro #1, not expanding either.
%
\def\appendtomacro#1#2{%
  \expandafter\def\expandafter#1\expandafter{#1#2}%
}

% @caption, @shortcaption
%
\def\caption{\docaption\thiscaption}
\def\shortcaption{\docaption\thisshortcaption}
\def\docaption{\checkenv\float \bgroup\scanargctxt\defcaption}
\def\defcaption#1#2{\egroup \def#1{#2}}

% The parameter is the control sequence identifying the counter we are
% going to use.  Create it if it doesn't exist and assign it to \floatno.
\def\getfloatno#1{%
  \ifx#1\relax
      % Haven't seen this figure type before.
      \csname newcount\endcsname #1%
      %
      % Remember to reset this floatno at the next chap.
      \expandafter\gdef\expandafter\resetallfloatnos
        \expandafter{\resetallfloatnos #1=0 }%
  \fi
  \let\floatno#1%
}

% \setref calls this to get the XREFLABEL-snt value.  We want an @xref
% to the FLOATLABEL to expand to "Figure 3.1".  We call \setref when we
% first read the @float command.
%
\def\Yfloat{\floattype@tie \chaplevelprefix\the\floatno}%

% Magic string used for the XREFLABEL-title value, so \xrefX can
% distinguish floats from other xref types.
\def\floatmagic{!!float!!}

% #1 is the control sequence we are passed; we expand into a conditional
% which is true if #1 represents a float ref.  That is, the magic
% \currentsection value which we \setref above.
%
\def\iffloat#1{\expandafter\doiffloat#1==\finish}
%
% #1 is (maybe) the \floatmagic string.  If so, #2 will be the
% (safe) float type for this float.  We set \iffloattype to #2.
%
\def\doiffloat#1=#2=#3\finish{%
  \def\temp{#1}%
  \def\iffloattype{#2}%
  \ifx\temp\floatmagic
}

% @listoffloats FLOATTYPE - print a list of floats like a table of contents.
%
\parseargdef\listoffloats{%
  \def\floattype{#1}% floattype
  {%
    % the floattype might have accents or other special characters,
    % but we need to use it in a control sequence name.
    \indexnofonts
    \turnoffactive
    \xdef\safefloattype{\floattype}%
  }%
  %
  % \xrdef saves the floats as a \do-list in \floatlistSAFEFLOATTYPE.
  \expandafter\ifx\csname floatlist\safefloattype\endcsname \relax
    \ifhavexrefs
      % if the user said @listoffloats foo but never @float foo.
      \message{\linenumber No `\safefloattype' floats to list.}%
    \fi
  \else
    \begingroup
      \leftskip=\tocindent  % indent these entries like a toc
      \let\do=\listoffloatsdo
      \csname floatlist\safefloattype\endcsname
    \endgroup
  \fi
}

% This is called on each entry in a list of floats.  We're passed the
% xref label, in the form LABEL-title, which is how we save it in the
% aux file.  We strip off the -title and look up \XRLABEL-lof, which
% has the text we're supposed to typeset here.
%
% Figures without xref labels will not be included in the list (since
% they won't appear in the aux file).
%
\def\listoffloatsdo#1{\listoffloatsdoentry#1\finish}
\def\listoffloatsdoentry#1-title\finish{{%
  % Can't fully expand XR#1-lof because it can contain anything.  Just
  % pass the control sequence.  On the other hand, XR#1-pg is just the
  % page number, and we want to fully expand that so we can get a link
  % in pdf output.
  \toksA = \expandafter{\csname XR#1-lof\endcsname}%
  %
  % use the same \entry macro we use to generate the TOC and index.
  \edef\writeentry{\noexpand\entry{\the\toksA}{\csname XR#1-pg\endcsname}}%
  \writeentry
}}


\message{localization,}

% For single-language documents, @documentlanguage is usually given very
% early, just after @documentencoding.  Single argument is the language
% (de) or locale (de_DE) abbreviation.
%
{
  \catcode`\_ = \active
  \globaldefs=1
\parseargdef\documentlanguage{%
  \tex % read txi-??.tex file in plain TeX.
    % Read the file by the name they passed if it exists.
    \let_ = \normalunderscore  % normal _ character for filename test
    \openin 1 txi-#1.tex
    \ifeof 1
      \documentlanguagetrywithoutunderscore #1_\finish
    \else
      \globaldefs = 1  % everything in the txi-LL files needs to persist
      \input txi-#1.tex
    \fi
    \closein 1
  \endgroup % end raw TeX
}
%
% If they passed de_DE, and txi-de_DE.tex doesn't exist,
% try txi-de.tex.
%
\gdef\documentlanguagetrywithoutunderscore#1_#2\finish{%
  \openin 1 txi-#1.tex
  \ifeof 1
    \errhelp = \nolanghelp
    \errmessage{Cannot read language file txi-#1.tex}%
  \else
    \globaldefs = 1  % everything in the txi-LL files needs to persist
    \input txi-#1.tex
  \fi
  \closein 1
}
}% end of special _ catcode
%
\newhelp\nolanghelp{The given language definition file cannot be found or
is empty.  Maybe you need to install it?  Putting it in the current
directory should work if nowhere else does.}

% This macro is called from txi-??.tex files; the first argument is the
% \language name to set (without the "\lang@" prefix), the second and
% third args are \{left,right}hyphenmin.
%
% The language names to pass are determined when the format is built.
% See the etex.log file created at that time, e.g.,
% /usr/local/texlive/2008/texmf-var/web2c/pdftex/etex.log.
%
% With TeX Live 2008, etex now includes hyphenation patterns for all
% available languages.  This means we can support hyphenation in
% Texinfo, at least to some extent.  (This still doesn't solve the
% accented characters problem.)
%
\catcode`@=11
\def\txisetlanguage#1#2#3{%
  % do not set the language if the name is undefined in the current TeX.
  \expandafter\ifx\csname lang@#1\endcsname \relax
    \message{no patterns for #1}%
  \else
    \global\language = \csname lang@#1\endcsname
  \fi
  % but there is no harm in adjusting the hyphenmin values regardless.
  \global\lefthyphenmin = #2\relax
  \global\righthyphenmin = #3\relax
}

% XeTeX and LuaTeX can handle Unicode natively.
% Their default I/O uses UTF-8 sequences instead of a byte-wise operation.
% Other TeX engines' I/O (pdfTeX, etc.) is byte-wise.
%
\newif\iftxinativeunicodecapable
\newif\iftxiusebytewiseio

\ifx\XeTeXrevision\thisisundefined
  \ifx\luatexversion\thisisundefined
    \txinativeunicodecapablefalse
    \txiusebytewiseiotrue
  \else
    \txinativeunicodecapabletrue
    \txiusebytewiseiofalse
  \fi
\else
  \txinativeunicodecapabletrue
  \txiusebytewiseiofalse
\fi

% Set I/O by bytes instead of UTF-8 sequence for XeTeX and LuaTex
% for non-UTF-8 (byte-wise) encodings.
%
\def\setbytewiseio{%
  \ifx\XeTeXrevision\thisisundefined
  \else
    \XeTeXdefaultencoding "bytes"  % For subsequent files to be read
    \XeTeXinputencoding "bytes"  % For document root file
    % Unfortunately, there seems to be no corresponding XeTeX command for
    % output encoding.  This is a problem for auxiliary index and TOC files.
    % The only solution would be perhaps to write out @U{...} sequences in
    % place of non-ASCII characters.
  \fi

  \ifx\luatexversion\thisisundefined
  \else
    \directlua{
    local utf8_char, byte, gsub = unicode.utf8.char, string.byte, string.gsub
    local function convert_char (char)
      return utf8_char(byte(char))
    end

    local function convert_line (line)
      return gsub(line, ".", convert_char)
    end

    callback.register("process_input_buffer", convert_line)

    local function convert_line_out (line)
      local line_out = ""
      for c in string.utfvalues(line) do
         line_out = line_out .. string.char(c)
      end
      return line_out
    end

    callback.register("process_output_buffer", convert_line_out)
    }
  \fi

  \txiusebytewiseiotrue
}


% Helpers for encodings.
% Set the catcode of characters 128 through 255 to the specified number.
%
\def\setnonasciicharscatcode#1{%
   \count255=128
   \loop\ifnum\count255<256
      \global\catcode\count255=#1\relax
      \advance\count255 by 1
   \repeat
}

\def\setnonasciicharscatcodenonglobal#1{%
   \count255=128
   \loop\ifnum\count255<256
      \catcode\count255=#1\relax
      \advance\count255 by 1
   \repeat
}

% @documentencoding sets the definition of non-ASCII characters
% according to the specified encoding.
%
\def\documentencoding{\parseargusing\filenamecatcodes\documentencodingzzz}
\def\documentencodingzzz#1{%
  %
  % Encoding being declared for the document.
  \def\declaredencoding{\csname #1.enc\endcsname}%
  %
  % Supported encodings: names converted to tokens in order to be able
  % to compare them with \ifx.
  \def\ascii{\csname US-ASCII.enc\endcsname}%
  \def\latnine{\csname ISO-8859-15.enc\endcsname}%
  \def\latone{\csname ISO-8859-1.enc\endcsname}%
  \def\lattwo{\csname ISO-8859-2.enc\endcsname}%
  \def\utfeight{\csname UTF-8.enc\endcsname}%
  %
  \ifx \declaredencoding \ascii
     \asciichardefs
  %
  \else \ifx \declaredencoding \lattwo
     \iftxinativeunicodecapable
       \setbytewiseio
     \fi
     \setnonasciicharscatcode\active
     \lattwochardefs
  %
  \else \ifx \declaredencoding \latone
     \iftxinativeunicodecapable
       \setbytewiseio
     \fi
     \setnonasciicharscatcode\active
     \latonechardefs
  %
  \else \ifx \declaredencoding \latnine
     \iftxinativeunicodecapable
       \setbytewiseio
     \fi
     \setnonasciicharscatcode\active
     \latninechardefs
  %
  \else \ifx \declaredencoding \utfeight
     \iftxinativeunicodecapable
       % For native Unicode handling (XeTeX and LuaTeX)
       \nativeunicodechardefs
     \else
       % For treating UTF-8 as byte sequences (TeX, eTeX and pdfTeX)
       \setnonasciicharscatcode\active
       % since we already invoked \utfeightchardefs at the top level
       % (below), do not re-invoke it, otherwise our check for duplicated
       % definitions gets triggered.  Making non-ascii chars active is
       % sufficient.
     \fi
  %
  \else
    \message{Ignoring unknown document encoding: #1.}%
  %
  \fi % utfeight
  \fi % latnine
  \fi % latone
  \fi % lattwo
  \fi % ascii
  %
  \ifx\XeTeXrevision\thisisundefined
  \else
    \ifx \declaredencoding \utfeight
    \else
      \ifx \declaredencoding \ascii
      \else
        \message{Warning: XeTeX with non-UTF-8 encodings cannot handle %
        non-ASCII characters in auxiliary files.}%
      \fi
    \fi
  \fi
}

% emacs-page
% A message to be logged when using a character that isn't available
% the default font encoding (OT1).
%
\def\missingcharmsg#1{\message{Character missing, sorry: #1.}}

% Take account of \c (plain) vs. \, (Texinfo) difference.
\def\cedilla#1{\ifx\c\ptexc\c{#1}\else\,{#1}\fi}

% First, make active non-ASCII characters in order for them to be
% correctly categorized when TeX reads the replacement text of
% macros containing the character definitions.
\setnonasciicharscatcode\active
%

\def\gdefchar#1#2{%
\gdef#1{%
   \ifpassthroughchars
     \string#1%
   \else
     #2%
   \fi
}}

% Latin1 (ISO-8859-1) character definitions.
\def\latonechardefs{%
  \gdefchar^^a0{\tie}
  \gdefchar^^a1{\exclamdown}
  \gdefchar^^a2{{\tcfont \char162}} % cent
  \gdefchar^^a3{\pounds{}}
  \gdefchar^^a4{{\tcfont \char164}} % currency
  \gdefchar^^a5{{\tcfont \char165}} % yen
  \gdefchar^^a6{{\tcfont \char166}} % broken bar
  \gdefchar^^a7{\S}
  \gdefchar^^a8{\"{}}
  \gdefchar^^a9{\copyright{}}
  \gdefchar^^aa{\ordf}
  \gdefchar^^ab{\guillemetleft{}}
  \gdefchar^^ac{\ensuremath\lnot}
  \gdefchar^^ad{\-}
  \gdefchar^^ae{\registeredsymbol{}}
  \gdefchar^^af{\={}}
  %
  \gdefchar^^b0{\textdegree}
  \gdefchar^^b1{$\pm$}
  \gdefchar^^b2{$^2$}
  \gdefchar^^b3{$^3$}
  \gdefchar^^b4{\'{}}
  \gdefchar^^b5{$\mu$}
  \gdefchar^^b6{\P}
  \gdefchar^^b7{\ensuremath\cdot}
  \gdefchar^^b8{\cedilla\ }
  \gdefchar^^b9{$^1$}
  \gdefchar^^ba{\ordm}
  \gdefchar^^bb{\guillemetright{}}
  \gdefchar^^bc{$1\over4$}
  \gdefchar^^bd{$1\over2$}
  \gdefchar^^be{$3\over4$}
  \gdefchar^^bf{\questiondown}
  %
  \gdefchar^^c0{\`A}
  \gdefchar^^c1{\'A}
  \gdefchar^^c2{\^A}
  \gdefchar^^c3{\~A}
  \gdefchar^^c4{\"A}
  \gdefchar^^c5{\ringaccent A}
  \gdefchar^^c6{\AE}
  \gdefchar^^c7{\cedilla C}
  \gdefchar^^c8{\`E}
  \gdefchar^^c9{\'E}
  \gdefchar^^ca{\^E}
  \gdefchar^^cb{\"E}
  \gdefchar^^cc{\`I}
  \gdefchar^^cd{\'I}
  \gdefchar^^ce{\^I}
  \gdefchar^^cf{\"I}
  %
  \gdefchar^^d0{\DH}
  \gdefchar^^d1{\~N}
  \gdefchar^^d2{\`O}
  \gdefchar^^d3{\'O}
  \gdefchar^^d4{\^O}
  \gdefchar^^d5{\~O}
  \gdefchar^^d6{\"O}
  \gdefchar^^d7{$\times$}
  \gdefchar^^d8{\O}
  \gdefchar^^d9{\`U}
  \gdefchar^^da{\'U}
  \gdefchar^^db{\^U}
  \gdefchar^^dc{\"U}
  \gdefchar^^dd{\'Y}
  \gdefchar^^de{\TH}
  \gdefchar^^df{\ss}
  %
  \gdefchar^^e0{\`a}
  \gdefchar^^e1{\'a}
  \gdefchar^^e2{\^a}
  \gdefchar^^e3{\~a}
  \gdefchar^^e4{\"a}
  \gdefchar^^e5{\ringaccent a}
  \gdefchar^^e6{\ae}
  \gdefchar^^e7{\cedilla c}
  \gdefchar^^e8{\`e}
  \gdefchar^^e9{\'e}
  \gdefchar^^ea{\^e}
  \gdefchar^^eb{\"e}
  \gdefchar^^ec{\`{\dotless i}}
  \gdefchar^^ed{\'{\dotless i}}
  \gdefchar^^ee{\^{\dotless i}}
  \gdefchar^^ef{\"{\dotless i}}
  %
  \gdefchar^^f0{\dh}
  \gdefchar^^f1{\~n}
  \gdefchar^^f2{\`o}
  \gdefchar^^f3{\'o}
  \gdefchar^^f4{\^o}
  \gdefchar^^f5{\~o}
  \gdefchar^^f6{\"o}
  \gdefchar^^f7{$\div$}
  \gdefchar^^f8{\o}
  \gdefchar^^f9{\`u}
  \gdefchar^^fa{\'u}
  \gdefchar^^fb{\^u}
  \gdefchar^^fc{\"u}
  \gdefchar^^fd{\'y}
  \gdefchar^^fe{\th}
  \gdefchar^^ff{\"y}
}

% Latin9 (ISO-8859-15) encoding character definitions.
\def\latninechardefs{%
  % Encoding is almost identical to Latin1.
  \latonechardefs
  %
  \gdefchar^^a4{\euro{}}
  \gdefchar^^a6{\v S}
  \gdefchar^^a8{\v s}
  \gdefchar^^b4{\v Z}
  \gdefchar^^b8{\v z}
  \gdefchar^^bc{\OE}
  \gdefchar^^bd{\oe}
  \gdefchar^^be{\"Y}
}

% Latin2 (ISO-8859-2) character definitions.
\def\lattwochardefs{%
  \gdefchar^^a0{\tie}
  \gdefchar^^a1{\ogonek{A}}
  \gdefchar^^a2{\u{}}
  \gdefchar^^a3{\L}
  \gdefchar^^a4{\missingcharmsg{CURRENCY SIGN}}
  \gdefchar^^a5{\v L}
  \gdefchar^^a6{\'S}
  \gdefchar^^a7{\S}
  \gdefchar^^a8{\"{}}
  \gdefchar^^a9{\v S}
  \gdefchar^^aa{\cedilla S}
  \gdefchar^^ab{\v T}
  \gdefchar^^ac{\'Z}
  \gdefchar^^ad{\-}
  \gdefchar^^ae{\v Z}
  \gdefchar^^af{\dotaccent Z}
  %
  \gdefchar^^b0{\textdegree{}}
  \gdefchar^^b1{\ogonek{a}}
  \gdefchar^^b2{\ogonek{ }}
  \gdefchar^^b3{\l}
  \gdefchar^^b4{\'{}}
  \gdefchar^^b5{\v l}
  \gdefchar^^b6{\'s}
  \gdefchar^^b7{\v{}}
  \gdefchar^^b8{\cedilla\ }
  \gdefchar^^b9{\v s}
  \gdefchar^^ba{\cedilla s}
  \gdefchar^^bb{\v t}
  \gdefchar^^bc{\'z}
  \gdefchar^^bd{\H{}}
  \gdefchar^^be{\v z}
  \gdefchar^^bf{\dotaccent z}
  %
  \gdefchar^^c0{\'R}
  \gdefchar^^c1{\'A}
  \gdefchar^^c2{\^A}
  \gdefchar^^c3{\u A}
  \gdefchar^^c4{\"A}
  \gdefchar^^c5{\'L}
  \gdefchar^^c6{\'C}
  \gdefchar^^c7{\cedilla C}
  \gdefchar^^c8{\v C}
  \gdefchar^^c9{\'E}
  \gdefchar^^ca{\ogonek{E}}
  \gdefchar^^cb{\"E}
  \gdefchar^^cc{\v E}
  \gdefchar^^cd{\'I}
  \gdefchar^^ce{\^I}
  \gdefchar^^cf{\v D}
  %
  \gdefchar^^d0{\DH}
  \gdefchar^^d1{\'N}
  \gdefchar^^d2{\v N}
  \gdefchar^^d3{\'O}
  \gdefchar^^d4{\^O}
  \gdefchar^^d5{\H O}
  \gdefchar^^d6{\"O}
  \gdefchar^^d7{$\times$}
  \gdefchar^^d8{\v R}
  \gdefchar^^d9{\ringaccent U}
  \gdefchar^^da{\'U}
  \gdefchar^^db{\H U}
  \gdefchar^^dc{\"U}
  \gdefchar^^dd{\'Y}
  \gdefchar^^de{\cedilla T}
  \gdefchar^^df{\ss}
  %
  \gdefchar^^e0{\'r}
  \gdefchar^^e1{\'a}
  \gdefchar^^e2{\^a}
  \gdefchar^^e3{\u a}
  \gdefchar^^e4{\"a}
  \gdefchar^^e5{\'l}
  \gdefchar^^e6{\'c}
  \gdefchar^^e7{\cedilla c}
  \gdefchar^^e8{\v c}
  \gdefchar^^e9{\'e}
  \gdefchar^^ea{\ogonek{e}}
  \gdefchar^^eb{\"e}
  \gdefchar^^ec{\v e}
  \gdefchar^^ed{\'{\dotless{i}}}
  \gdefchar^^ee{\^{\dotless{i}}}
  \gdefchar^^ef{\v d}
  %
  \gdefchar^^f0{\dh}
  \gdefchar^^f1{\'n}
  \gdefchar^^f2{\v n}
  \gdefchar^^f3{\'o}
  \gdefchar^^f4{\^o}
  \gdefchar^^f5{\H o}
  \gdefchar^^f6{\"o}
  \gdefchar^^f7{$\div$}
  \gdefchar^^f8{\v r}
  \gdefchar^^f9{\ringaccent u}
  \gdefchar^^fa{\'u}
  \gdefchar^^fb{\H u}
  \gdefchar^^fc{\"u}
  \gdefchar^^fd{\'y}
  \gdefchar^^fe{\cedilla t}
  \gdefchar^^ff{\dotaccent{}}
}

% UTF-8 character definitions.
%
% This code to support UTF-8 is based on LaTeX's utf8.def, with some
% changes for Texinfo conventions.  It is included here under the GPL by
% permission from Frank Mittelbach and the LaTeX team.
%
\newcount\countUTFx
\newcount\countUTFy
\newcount\countUTFz

\gdef\UTFviiiTwoOctets#1#2{\expandafter
   \UTFviiiDefined\csname u8:#1\string #2\endcsname}
%
\gdef\UTFviiiThreeOctets#1#2#3{\expandafter
   \UTFviiiDefined\csname u8:#1\string #2\string #3\endcsname}
%
\gdef\UTFviiiFourOctets#1#2#3#4{\expandafter
   \UTFviiiDefined\csname u8:#1\string #2\string #3\string #4\endcsname}

\gdef\UTFviiiDefined#1{%
  \ifx #1\relax
    \message{\linenumber Unicode char \string #1 not defined for Texinfo}%
  \else
    \expandafter #1%
  \fi
}

% Give non-ASCII bytes the active definitions for processing UTF-8 sequences
\begingroup
  \catcode`\~13
  \catcode`\$12
  \catcode`\"12

  % Loop from \countUTFx to \countUTFy, performing \UTFviiiTmp
  % substituting ~ and $ with a character token of that value.
  \def\UTFviiiLoop{%
    \global\catcode\countUTFx\active
    \uccode`\~\countUTFx
    \uccode`\$\countUTFx
    \uppercase\expandafter{\UTFviiiTmp}%
    \advance\countUTFx by 1
    \ifnum\countUTFx < \countUTFy
      \expandafter\UTFviiiLoop
    \fi}

  % For bytes other than the first in a UTF-8 sequence.  Not expected to
  % be expanded except when writing to auxiliary files.
  \countUTFx = "80
  \countUTFy = "C2
  \def\UTFviiiTmp{%
    \gdef~{%
        \ifpassthroughchars $\fi}}%
  \UTFviiiLoop

  \countUTFx = "C2
  \countUTFy = "E0
  \def\UTFviiiTmp{%
    \gdef~{%
        \ifpassthroughchars $%
        \else\expandafter\UTFviiiTwoOctets\expandafter$\fi}}%
  \UTFviiiLoop

  \countUTFx = "E0
  \countUTFy = "F0
  \def\UTFviiiTmp{%
    \gdef~{%
        \ifpassthroughchars $%
        \else\expandafter\UTFviiiThreeOctets\expandafter$\fi}}%
  \UTFviiiLoop

  \countUTFx = "F0
  \countUTFy = "F4
  \def\UTFviiiTmp{%
    \gdef~{%
        \ifpassthroughchars $%
        \else\expandafter\UTFviiiFourOctets\expandafter$\fi
        }}%
  \UTFviiiLoop
\endgroup

\def\globallet{\global\let} % save some \expandafter's below

% @U{xxxx} to produce U+xxxx, if we support it.
\def\U#1{%
  \expandafter\ifx\csname uni:#1\endcsname \relax
    \iftxinativeunicodecapable
      % All Unicode characters can be used if native Unicode handling is
      % active.  However, if the font does not have the glyph,
      % letters are missing.
      \begingroup
        \uccode`\.="#1\relax
        \uppercase{.}
      \endgroup
    \else
      \errhelp = \EMsimple
      \errmessage{Unicode character U+#1 not supported, sorry}%
    \fi
  \else
    \csname uni:#1\endcsname
  \fi
}

% These macros are used here to construct the name of a control
% sequence to be defined.
\def\UTFviiiTwoOctetsName#1#2{%
  \csname u8:#1\string #2\endcsname}%
\def\UTFviiiThreeOctetsName#1#2#3{%
  \csname u8:#1\string #2\string #3\endcsname}%
\def\UTFviiiFourOctetsName#1#2#3#4{%
  \csname u8:#1\string #2\string #3\string #4\endcsname}%

% For UTF-8 byte sequences (TeX, e-TeX and pdfTeX),
% provide a definition macro to replace a Unicode character;
% this gets used by the @U command
%
\begingroup
  \catcode`\"=12
  \catcode`\<=12
  \catcode`\.=12
  \catcode`\,=12
  \catcode`\;=12
  \catcode`\!=12
  \catcode`\~=13
  \gdef\DeclareUnicodeCharacterUTFviii#1#2{%
    \countUTFz = "#1\relax
    \begingroup
      \parseXMLCharref

      % Give \u8:... its definition.  The sequence of seven \expandafter's
      % expands after the \gdef three times, e.g.
      %
      % 1.  \UTFviiTwoOctetsName B1 B2
      % 2.  \csname u8:B1 \string B2 \endcsname
      % 3.  \u8: B1 B2  (a single control sequence token)
      %
      \expandafter\expandafter
      \expandafter\expandafter
      \expandafter\expandafter
      \expandafter\gdef       \UTFviiiTmp{#2}%
      %
      \expandafter\ifx\csname uni:#1\endcsname \relax \else
       \message{Internal error, already defined: #1}%
      \fi
      %
      % define an additional control sequence for this code point.
      \expandafter\globallet\csname uni:#1\endcsname \UTFviiiTmp
    \endgroup}
  %
  % Given the value in \countUTFz as a Unicode code point, set \UTFviiiTmp
  % to the corresponding UTF-8 sequence.
  \gdef\parseXMLCharref{%
    \ifnum\countUTFz < "A0\relax
      \errhelp = \EMsimple
      \errmessage{Cannot define Unicode char value < 00A0}%
    \else\ifnum\countUTFz < "800\relax
      \parseUTFviiiA,%
      \parseUTFviiiB C\UTFviiiTwoOctetsName.,%
    \else\ifnum\countUTFz < "10000\relax
      \parseUTFviiiA;%
      \parseUTFviiiA,%
      \parseUTFviiiB E\UTFviiiThreeOctetsName.{,;}%
    \else
      \parseUTFviiiA;%
      \parseUTFviiiA,%
      \parseUTFviiiA!%
      \parseUTFviiiB F\UTFviiiFourOctetsName.{!,;}%
    \fi\fi\fi
  }

  % Extract a byte from the end of the UTF-8 representation of \countUTFx.
  % It must be a non-initial byte in the sequence.
  % Change \uccode of #1 for it to be used in \parseUTFviiiB as one
  % of the bytes.
  \gdef\parseUTFviiiA#1{%
    \countUTFx = \countUTFz
    \divide\countUTFz by 64
    \countUTFy = \countUTFz  % Save to be the future value of \countUTFz.
    \multiply\countUTFz by 64

    % \countUTFz is now \countUTFx with the last 5 bits cleared.  Subtract
    % in order to get the last five bits.
    \advance\countUTFx by -\countUTFz

    % Convert this to the byte in the UTF-8 sequence.
    \advance\countUTFx by 128
    \uccode `#1\countUTFx
    \countUTFz = \countUTFy}

  % Used to put a UTF-8 byte sequence into \UTFviiiTmp
  % #1 is the increment for \countUTFz to yield a the first byte of the UTF-8
  %    sequence.
  % #2 is one of the \UTFviii*OctetsName macros.
  % #3 is always a full stop (.)
  % #4 is a template for the other bytes in the sequence.  The values for these
  %    bytes is substituted in here with \uppercase using the \uccode's.
  \gdef\parseUTFviiiB#1#2#3#4{%
    \advance\countUTFz by "#10\relax
    \uccode `#3\countUTFz
    \uppercase{\gdef\UTFviiiTmp{#2#3#4}}}
\endgroup

% For native Unicode handling (XeTeX and LuaTeX),
% provide a definition macro that sets a catcode to `other' non-globally
%
\def\DeclareUnicodeCharacterNativeOther#1#2{%
  \catcode"#1=\other
}

% https://en.wikipedia.org/wiki/Plane_(Unicode)#Basic_M
% U+0000..U+007F = https://en.wikipedia.org/wiki/Basic_Latin_(Unicode_block)
% U+0080..U+00FF = https://en.wikipedia.org/wiki/Latin-1_Supplement_(Unicode_block)
% U+0100..U+017F = https://en.wikipedia.org/wiki/Latin_Extended-A
% U+0180..U+024F = https://en.wikipedia.org/wiki/Latin_Extended-B
%
% Many of our renditions are less than wonderful, and all the missing
% characters are available somewhere.  Loading the necessary fonts
% awaits user request.  We can't truly support Unicode without
% reimplementing everything that's been done in LaTeX for many years,
% plus probably using luatex or xetex, and who knows what else.
% We won't be doing that here in this simple file.  But we can try to at
% least make most of the characters not bomb out.
%
\def\unicodechardefs{%
  \DeclareUnicodeCharacter{00A0}{\tie}%
  \DeclareUnicodeCharacter{00A1}{\exclamdown}%
  \DeclareUnicodeCharacter{00A2}{{\tcfont \char162}}% 0242=cent
  \DeclareUnicodeCharacter{00A3}{\pounds{}}%
  \DeclareUnicodeCharacter{00A4}{{\tcfont \char164}}% 0244=currency
  \DeclareUnicodeCharacter{00A5}{{\tcfont \char165}}% 0245=yen
  \DeclareUnicodeCharacter{00A6}{{\tcfont \char166}}% 0246=brokenbar
  \DeclareUnicodeCharacter{00A7}{\S}%
  \DeclareUnicodeCharacter{00A8}{\"{ }}%
  \DeclareUnicodeCharacter{00A9}{\copyright{}}%
  \DeclareUnicodeCharacter{00AA}{\ordf}%
  \DeclareUnicodeCharacter{00AB}{\guillemetleft{}}%
  \DeclareUnicodeCharacter{00AC}{\ensuremath\lnot}%
  \DeclareUnicodeCharacter{00AD}{\-}%
  \DeclareUnicodeCharacter{00AE}{\registeredsymbol{}}%
  \DeclareUnicodeCharacter{00AF}{\={ }}%
  %
  \DeclareUnicodeCharacter{00B0}{\ringaccent{ }}%
  \DeclareUnicodeCharacter{00B1}{\ensuremath\pm}%
  \DeclareUnicodeCharacter{00B2}{$^2$}%
  \DeclareUnicodeCharacter{00B3}{$^3$}%
  \DeclareUnicodeCharacter{00B4}{\'{ }}%
  \DeclareUnicodeCharacter{00B5}{$\mu$}%
  \DeclareUnicodeCharacter{00B6}{\P}%
  \DeclareUnicodeCharacter{00B7}{\ensuremath\cdot}%
  \DeclareUnicodeCharacter{00B8}{\cedilla{ }}%
  \DeclareUnicodeCharacter{00B9}{$^1$}%
  \DeclareUnicodeCharacter{00BA}{\ordm}%
  \DeclareUnicodeCharacter{00BB}{\guillemetright{}}%
  \DeclareUnicodeCharacter{00BC}{$1\over4$}%
  \DeclareUnicodeCharacter{00BD}{$1\over2$}%
  \DeclareUnicodeCharacter{00BE}{$3\over4$}%
  \DeclareUnicodeCharacter{00BF}{\questiondown}%
  %
  \DeclareUnicodeCharacter{00C0}{\`A}%
  \DeclareUnicodeCharacter{00C1}{\'A}%
  \DeclareUnicodeCharacter{00C2}{\^A}%
  \DeclareUnicodeCharacter{00C3}{\~A}%
  \DeclareUnicodeCharacter{00C4}{\"A}%
  \DeclareUnicodeCharacter{00C5}{\AA}%
  \DeclareUnicodeCharacter{00C6}{\AE}%
  \DeclareUnicodeCharacter{00C7}{\cedilla{C}}%
  \DeclareUnicodeCharacter{00C8}{\`E}%
  \DeclareUnicodeCharacter{00C9}{\'E}%
  \DeclareUnicodeCharacter{00CA}{\^E}%
  \DeclareUnicodeCharacter{00CB}{\"E}%
  \DeclareUnicodeCharacter{00CC}{\`I}%
  \DeclareUnicodeCharacter{00CD}{\'I}%
  \DeclareUnicodeCharacter{00CE}{\^I}%
  \DeclareUnicodeCharacter{00CF}{\"I}%
  %
  \DeclareUnicodeCharacter{00D0}{\DH}%
  \DeclareUnicodeCharacter{00D1}{\~N}%
  \DeclareUnicodeCharacter{00D2}{\`O}%
  \DeclareUnicodeCharacter{00D3}{\'O}%
  \DeclareUnicodeCharacter{00D4}{\^O}%
  \DeclareUnicodeCharacter{00D5}{\~O}%
  \DeclareUnicodeCharacter{00D6}{\"O}%
  \DeclareUnicodeCharacter{00D7}{\ensuremath\times}%
  \DeclareUnicodeCharacter{00D8}{\O}%
  \DeclareUnicodeCharacter{00D9}{\`U}%
  \DeclareUnicodeCharacter{00DA}{\'U}%
  \DeclareUnicodeCharacter{00DB}{\^U}%
  \DeclareUnicodeCharacter{00DC}{\"U}%
  \DeclareUnicodeCharacter{00DD}{\'Y}%
  \DeclareUnicodeCharacter{00DE}{\TH}%
  \DeclareUnicodeCharacter{00DF}{\ss}%
  %
  \DeclareUnicodeCharacter{00E0}{\`a}%
  \DeclareUnicodeCharacter{00E1}{\'a}%
  \DeclareUnicodeCharacter{00E2}{\^a}%
  \DeclareUnicodeCharacter{00E3}{\~a}%
  \DeclareUnicodeCharacter{00E4}{\"a}%
  \DeclareUnicodeCharacter{00E5}{\aa}%
  \DeclareUnicodeCharacter{00E6}{\ae}%
  \DeclareUnicodeCharacter{00E7}{\cedilla{c}}%
  \DeclareUnicodeCharacter{00E8}{\`e}%
  \DeclareUnicodeCharacter{00E9}{\'e}%
  \DeclareUnicodeCharacter{00EA}{\^e}%
  \DeclareUnicodeCharacter{00EB}{\"e}%
  \DeclareUnicodeCharacter{00EC}{\`{\dotless{i}}}%
  \DeclareUnicodeCharacter{00ED}{\'{\dotless{i}}}%
  \DeclareUnicodeCharacter{00EE}{\^{\dotless{i}}}%
  \DeclareUnicodeCharacter{00EF}{\"{\dotless{i}}}%
  %
  \DeclareUnicodeCharacter{00F0}{\dh}%
  \DeclareUnicodeCharacter{00F1}{\~n}%
  \DeclareUnicodeCharacter{00F2}{\`o}%
  \DeclareUnicodeCharacter{00F3}{\'o}%
  \DeclareUnicodeCharacter{00F4}{\^o}%
  \DeclareUnicodeCharacter{00F5}{\~o}%
  \DeclareUnicodeCharacter{00F6}{\"o}%
  \DeclareUnicodeCharacter{00F7}{\ensuremath\div}%
  \DeclareUnicodeCharacter{00F8}{\o}%
  \DeclareUnicodeCharacter{00F9}{\`u}%
  \DeclareUnicodeCharacter{00FA}{\'u}%
  \DeclareUnicodeCharacter{00FB}{\^u}%
  \DeclareUnicodeCharacter{00FC}{\"u}%
  \DeclareUnicodeCharacter{00FD}{\'y}%
  \DeclareUnicodeCharacter{00FE}{\th}%
  \DeclareUnicodeCharacter{00FF}{\"y}%
  %
  \DeclareUnicodeCharacter{0100}{\=A}%
  \DeclareUnicodeCharacter{0101}{\=a}%
  \DeclareUnicodeCharacter{0102}{\u{A}}%
  \DeclareUnicodeCharacter{0103}{\u{a}}%
  \DeclareUnicodeCharacter{0104}{\ogonek{A}}%
  \DeclareUnicodeCharacter{0105}{\ogonek{a}}%
  \DeclareUnicodeCharacter{0106}{\'C}%
  \DeclareUnicodeCharacter{0107}{\'c}%
  \DeclareUnicodeCharacter{0108}{\^C}%
  \DeclareUnicodeCharacter{0109}{\^c}%
  \DeclareUnicodeCharacter{010A}{\dotaccent{C}}%
  \DeclareUnicodeCharacter{010B}{\dotaccent{c}}%
  \DeclareUnicodeCharacter{010C}{\v{C}}%
  \DeclareUnicodeCharacter{010D}{\v{c}}%
  \DeclareUnicodeCharacter{010E}{\v{D}}%
  \DeclareUnicodeCharacter{010F}{d'}%
  %
  \DeclareUnicodeCharacter{0110}{\DH}%
  \DeclareUnicodeCharacter{0111}{\dh}%
  \DeclareUnicodeCharacter{0112}{\=E}%
  \DeclareUnicodeCharacter{0113}{\=e}%
  \DeclareUnicodeCharacter{0114}{\u{E}}%
  \DeclareUnicodeCharacter{0115}{\u{e}}%
  \DeclareUnicodeCharacter{0116}{\dotaccent{E}}%
  \DeclareUnicodeCharacter{0117}{\dotaccent{e}}%
  \DeclareUnicodeCharacter{0118}{\ogonek{E}}%
  \DeclareUnicodeCharacter{0119}{\ogonek{e}}%
  \DeclareUnicodeCharacter{011A}{\v{E}}%
  \DeclareUnicodeCharacter{011B}{\v{e}}%
  \DeclareUnicodeCharacter{011C}{\^G}%
  \DeclareUnicodeCharacter{011D}{\^g}%
  \DeclareUnicodeCharacter{011E}{\u{G}}%
  \DeclareUnicodeCharacter{011F}{\u{g}}%
  %
  \DeclareUnicodeCharacter{0120}{\dotaccent{G}}%
  \DeclareUnicodeCharacter{0121}{\dotaccent{g}}%
  \DeclareUnicodeCharacter{0122}{\cedilla{G}}%
  \DeclareUnicodeCharacter{0123}{\cedilla{g}}%
  \DeclareUnicodeCharacter{0124}{\^H}%
  \DeclareUnicodeCharacter{0125}{\^h}%
  \DeclareUnicodeCharacter{0126}{\missingcharmsg{H WITH STROKE}}%
  \DeclareUnicodeCharacter{0127}{\missingcharmsg{h WITH STROKE}}%
  \DeclareUnicodeCharacter{0128}{\~I}%
  \DeclareUnicodeCharacter{0129}{\~{\dotless{i}}}%
  \DeclareUnicodeCharacter{012A}{\=I}%
  \DeclareUnicodeCharacter{012B}{\={\dotless{i}}}%
  \DeclareUnicodeCharacter{012C}{\u{I}}%
  \DeclareUnicodeCharacter{012D}{\u{\dotless{i}}}%
  \DeclareUnicodeCharacter{012E}{\ogonek{I}}%
  \DeclareUnicodeCharacter{012F}{\ogonek{i}}%
  %
  \DeclareUnicodeCharacter{0130}{\dotaccent{I}}%
  \DeclareUnicodeCharacter{0131}{\dotless{i}}%
  \DeclareUnicodeCharacter{0132}{IJ}%
  \DeclareUnicodeCharacter{0133}{ij}%
  \DeclareUnicodeCharacter{0134}{\^J}%
  \DeclareUnicodeCharacter{0135}{\^{\dotless{j}}}%
  \DeclareUnicodeCharacter{0136}{\cedilla{K}}%
  \DeclareUnicodeCharacter{0137}{\cedilla{k}}%
  \DeclareUnicodeCharacter{0138}{\ensuremath\kappa}%
  \DeclareUnicodeCharacter{0139}{\'L}%
  \DeclareUnicodeCharacter{013A}{\'l}%
  \DeclareUnicodeCharacter{013B}{\cedilla{L}}%
  \DeclareUnicodeCharacter{013C}{\cedilla{l}}%
  \DeclareUnicodeCharacter{013D}{L'}% should kern
  \DeclareUnicodeCharacter{013E}{l'}% should kern
  \DeclareUnicodeCharacter{013F}{L\U{00B7}}%
  %
  \DeclareUnicodeCharacter{0140}{l\U{00B7}}%
  \DeclareUnicodeCharacter{0141}{\L}%
  \DeclareUnicodeCharacter{0142}{\l}%
  \DeclareUnicodeCharacter{0143}{\'N}%
  \DeclareUnicodeCharacter{0144}{\'n}%
  \DeclareUnicodeCharacter{0145}{\cedilla{N}}%
  \DeclareUnicodeCharacter{0146}{\cedilla{n}}%
  \DeclareUnicodeCharacter{0147}{\v{N}}%
  \DeclareUnicodeCharacter{0148}{\v{n}}%
  \DeclareUnicodeCharacter{0149}{'n}%
  \DeclareUnicodeCharacter{014A}{\missingcharmsg{ENG}}%
  \DeclareUnicodeCharacter{014B}{\missingcharmsg{eng}}%
  \DeclareUnicodeCharacter{014C}{\=O}%
  \DeclareUnicodeCharacter{014D}{\=o}%
  \DeclareUnicodeCharacter{014E}{\u{O}}%
  \DeclareUnicodeCharacter{014F}{\u{o}}%
  %
  \DeclareUnicodeCharacter{0150}{\H{O}}%
  \DeclareUnicodeCharacter{0151}{\H{o}}%
  \DeclareUnicodeCharacter{0152}{\OE}%
  \DeclareUnicodeCharacter{0153}{\oe}%
  \DeclareUnicodeCharacter{0154}{\'R}%
  \DeclareUnicodeCharacter{0155}{\'r}%
  \DeclareUnicodeCharacter{0156}{\cedilla{R}}%
  \DeclareUnicodeCharacter{0157}{\cedilla{r}}%
  \DeclareUnicodeCharacter{0158}{\v{R}}%
  \DeclareUnicodeCharacter{0159}{\v{r}}%
  \DeclareUnicodeCharacter{015A}{\'S}%
  \DeclareUnicodeCharacter{015B}{\'s}%
  \DeclareUnicodeCharacter{015C}{\^S}%
  \DeclareUnicodeCharacter{015D}{\^s}%
  \DeclareUnicodeCharacter{015E}{\cedilla{S}}%
  \DeclareUnicodeCharacter{015F}{\cedilla{s}}%
  %
  \DeclareUnicodeCharacter{0160}{\v{S}}%
  \DeclareUnicodeCharacter{0161}{\v{s}}%
  \DeclareUnicodeCharacter{0162}{\cedilla{T}}%
  \DeclareUnicodeCharacter{0163}{\cedilla{t}}%
  \DeclareUnicodeCharacter{0164}{\v{T}}%
  \DeclareUnicodeCharacter{0165}{\v{t}}%
  \DeclareUnicodeCharacter{0166}{\missingcharmsg{H WITH STROKE}}%
  \DeclareUnicodeCharacter{0167}{\missingcharmsg{h WITH STROKE}}%
  \DeclareUnicodeCharacter{0168}{\~U}%
  \DeclareUnicodeCharacter{0169}{\~u}%
  \DeclareUnicodeCharacter{016A}{\=U}%
  \DeclareUnicodeCharacter{016B}{\=u}%
  \DeclareUnicodeCharacter{016C}{\u{U}}%
  \DeclareUnicodeCharacter{016D}{\u{u}}%
  \DeclareUnicodeCharacter{016E}{\ringaccent{U}}%
  \DeclareUnicodeCharacter{016F}{\ringaccent{u}}%
  %
  \DeclareUnicodeCharacter{0170}{\H{U}}%
  \DeclareUnicodeCharacter{0171}{\H{u}}%
  \DeclareUnicodeCharacter{0172}{\ogonek{U}}%
  \DeclareUnicodeCharacter{0173}{\ogonek{u}}%
  \DeclareUnicodeCharacter{0174}{\^W}%
  \DeclareUnicodeCharacter{0175}{\^w}%
  \DeclareUnicodeCharacter{0176}{\^Y}%
  \DeclareUnicodeCharacter{0177}{\^y}%
  \DeclareUnicodeCharacter{0178}{\"Y}%
  \DeclareUnicodeCharacter{0179}{\'Z}%
  \DeclareUnicodeCharacter{017A}{\'z}%
  \DeclareUnicodeCharacter{017B}{\dotaccent{Z}}%
  \DeclareUnicodeCharacter{017C}{\dotaccent{z}}%
  \DeclareUnicodeCharacter{017D}{\v{Z}}%
  \DeclareUnicodeCharacter{017E}{\v{z}}%
  \DeclareUnicodeCharacter{017F}{\missingcharmsg{LONG S}}%
  %
  \DeclareUnicodeCharacter{01C4}{D\v{Z}}%
  \DeclareUnicodeCharacter{01C5}{D\v{z}}%
  \DeclareUnicodeCharacter{01C6}{d\v{z}}%
  \DeclareUnicodeCharacter{01C7}{LJ}%
  \DeclareUnicodeCharacter{01C8}{Lj}%
  \DeclareUnicodeCharacter{01C9}{lj}%
  \DeclareUnicodeCharacter{01CA}{NJ}%
  \DeclareUnicodeCharacter{01CB}{Nj}%
  \DeclareUnicodeCharacter{01CC}{nj}%
  \DeclareUnicodeCharacter{01CD}{\v{A}}%
  \DeclareUnicodeCharacter{01CE}{\v{a}}%
  \DeclareUnicodeCharacter{01CF}{\v{I}}%
  %
  \DeclareUnicodeCharacter{01D0}{\v{\dotless{i}}}%
  \DeclareUnicodeCharacter{01D1}{\v{O}}%
  \DeclareUnicodeCharacter{01D2}{\v{o}}%
  \DeclareUnicodeCharacter{01D3}{\v{U}}%
  \DeclareUnicodeCharacter{01D4}{\v{u}}%
  %
  \DeclareUnicodeCharacter{01E2}{\={\AE}}%
  \DeclareUnicodeCharacter{01E3}{\={\ae}}%
  \DeclareUnicodeCharacter{01E6}{\v{G}}%
  \DeclareUnicodeCharacter{01E7}{\v{g}}%
  \DeclareUnicodeCharacter{01E8}{\v{K}}%
  \DeclareUnicodeCharacter{01E9}{\v{k}}%
  %
  \DeclareUnicodeCharacter{01F0}{\v{\dotless{j}}}%
  \DeclareUnicodeCharacter{01F1}{DZ}%
  \DeclareUnicodeCharacter{01F2}{Dz}%
  \DeclareUnicodeCharacter{01F3}{dz}%
  \DeclareUnicodeCharacter{01F4}{\'G}%
  \DeclareUnicodeCharacter{01F5}{\'g}%
  \DeclareUnicodeCharacter{01F8}{\`N}%
  \DeclareUnicodeCharacter{01F9}{\`n}%
  \DeclareUnicodeCharacter{01FC}{\'{\AE}}%
  \DeclareUnicodeCharacter{01FD}{\'{\ae}}%
  \DeclareUnicodeCharacter{01FE}{\'{\O}}%
  \DeclareUnicodeCharacter{01FF}{\'{\o}}%
  %
  \DeclareUnicodeCharacter{021E}{\v{H}}%
  \DeclareUnicodeCharacter{021F}{\v{h}}%
  %
  \DeclareUnicodeCharacter{0226}{\dotaccent{A}}%
  \DeclareUnicodeCharacter{0227}{\dotaccent{a}}%
  \DeclareUnicodeCharacter{0228}{\cedilla{E}}%
  \DeclareUnicodeCharacter{0229}{\cedilla{e}}%
  \DeclareUnicodeCharacter{022E}{\dotaccent{O}}%
  \DeclareUnicodeCharacter{022F}{\dotaccent{o}}%
  %
  \DeclareUnicodeCharacter{0232}{\=Y}%
  \DeclareUnicodeCharacter{0233}{\=y}%
  \DeclareUnicodeCharacter{0237}{\dotless{j}}%
  %
  \DeclareUnicodeCharacter{02BC}{'}%
  %
  \DeclareUnicodeCharacter{02DB}{\ogonek{ }}%
  %
  % Greek letters upper case
  \DeclareUnicodeCharacter{0391}{{\it A}}%
  \DeclareUnicodeCharacter{0392}{{\it B}}%
  \DeclareUnicodeCharacter{0393}{\ensuremath{\mit\Gamma}}%
  \DeclareUnicodeCharacter{0394}{\ensuremath{\mit\Delta}}%
  \DeclareUnicodeCharacter{0395}{{\it E}}%
  \DeclareUnicodeCharacter{0396}{{\it Z}}%
  \DeclareUnicodeCharacter{0397}{{\it H}}%
  \DeclareUnicodeCharacter{0398}{\ensuremath{\mit\Theta}}%
  \DeclareUnicodeCharacter{0399}{{\it I}}%
  \DeclareUnicodeCharacter{039A}{{\it K}}%
  \DeclareUnicodeCharacter{039B}{\ensuremath{\mit\Lambda}}%
  \DeclareUnicodeCharacter{039C}{{\it M}}%
  \DeclareUnicodeCharacter{039D}{{\it N}}%
  \DeclareUnicodeCharacter{039E}{\ensuremath{\mit\Xi}}%
  \DeclareUnicodeCharacter{039F}{{\it O}}%
  \DeclareUnicodeCharacter{03A0}{\ensuremath{\mit\Pi}}%
  \DeclareUnicodeCharacter{03A1}{{\it P}}%
  %\DeclareUnicodeCharacter{03A2}{} % none - corresponds to final sigma
  \DeclareUnicodeCharacter{03A3}{\ensuremath{\mit\Sigma}}%
  \DeclareUnicodeCharacter{03A4}{{\it T}}%
  \DeclareUnicodeCharacter{03A5}{\ensuremath{\mit\Upsilon}}%
  \DeclareUnicodeCharacter{03A6}{\ensuremath{\mit\Phi}}%
  \DeclareUnicodeCharacter{03A7}{{\it X}}%
  \DeclareUnicodeCharacter{03A8}{\ensuremath{\mit\Psi}}%
  \DeclareUnicodeCharacter{03A9}{\ensuremath{\mit\Omega}}%
  %
  % Vowels with accents
  \DeclareUnicodeCharacter{0390}{\ensuremath{\ddot{\acute\iota}}}%
  \DeclareUnicodeCharacter{03AC}{\ensuremath{\acute\alpha}}%
  \DeclareUnicodeCharacter{03AD}{\ensuremath{\acute\epsilon}}%
  \DeclareUnicodeCharacter{03AE}{\ensuremath{\acute\eta}}%
  \DeclareUnicodeCharacter{03AF}{\ensuremath{\acute\iota}}%
  \DeclareUnicodeCharacter{03B0}{\ensuremath{\acute{\ddot\upsilon}}}%
  %
  % Standalone accent
  \DeclareUnicodeCharacter{0384}{\ensuremath{\acute{\ }}}%
  %
  % Greek letters lower case
  \DeclareUnicodeCharacter{03B1}{\ensuremath\alpha}%
  \DeclareUnicodeCharacter{03B2}{\ensuremath\beta}%
  \DeclareUnicodeCharacter{03B3}{\ensuremath\gamma}%
  \DeclareUnicodeCharacter{03B4}{\ensuremath\delta}%
  \DeclareUnicodeCharacter{03B5}{\ensuremath\epsilon}%
  \DeclareUnicodeCharacter{03B6}{\ensuremath\zeta}%
  \DeclareUnicodeCharacter{03B7}{\ensuremath\eta}%
  \DeclareUnicodeCharacter{03B8}{\ensuremath\theta}%
  \DeclareUnicodeCharacter{03B9}{\ensuremath\iota}%
  \DeclareUnicodeCharacter{03BA}{\ensuremath\kappa}%
  \DeclareUnicodeCharacter{03BB}{\ensuremath\lambda}%
  \DeclareUnicodeCharacter{03BC}{\ensuremath\mu}%
  \DeclareUnicodeCharacter{03BD}{\ensuremath\nu}%
  \DeclareUnicodeCharacter{03BE}{\ensuremath\xi}%
  \DeclareUnicodeCharacter{03BF}{{\it o}}% omicron
  \DeclareUnicodeCharacter{03C0}{\ensuremath\pi}%
  \DeclareUnicodeCharacter{03C1}{\ensuremath\rho}%
  \DeclareUnicodeCharacter{03C2}{\ensuremath\varsigma}%
  \DeclareUnicodeCharacter{03C3}{\ensuremath\sigma}%
  \DeclareUnicodeCharacter{03C4}{\ensuremath\tau}%
  \DeclareUnicodeCharacter{03C5}{\ensuremath\upsilon}%
  \DeclareUnicodeCharacter{03C6}{\ensuremath\phi}%
  \DeclareUnicodeCharacter{03C7}{\ensuremath\chi}%
  \DeclareUnicodeCharacter{03C8}{\ensuremath\psi}%
  \DeclareUnicodeCharacter{03C9}{\ensuremath\omega}%
  %
  % More Greek vowels with accents
  \DeclareUnicodeCharacter{03CA}{\ensuremath{\ddot\iota}}%
  \DeclareUnicodeCharacter{03CB}{\ensuremath{\ddot\upsilon}}%
  \DeclareUnicodeCharacter{03CC}{\ensuremath{\acute o}}%
  \DeclareUnicodeCharacter{03CD}{\ensuremath{\acute\upsilon}}%
  \DeclareUnicodeCharacter{03CE}{\ensuremath{\acute\omega}}%
  %
  % Variant Greek letters
  \DeclareUnicodeCharacter{03D1}{\ensuremath\vartheta}%
  \DeclareUnicodeCharacter{03D6}{\ensuremath\varpi}%
  \DeclareUnicodeCharacter{03F1}{\ensuremath\varrho}%
  %
  \DeclareUnicodeCharacter{1E02}{\dotaccent{B}}%
  \DeclareUnicodeCharacter{1E03}{\dotaccent{b}}%
  \DeclareUnicodeCharacter{1E04}{\udotaccent{B}}%
  \DeclareUnicodeCharacter{1E05}{\udotaccent{b}}%
  \DeclareUnicodeCharacter{1E06}{\ubaraccent{B}}%
  \DeclareUnicodeCharacter{1E07}{\ubaraccent{b}}%
  \DeclareUnicodeCharacter{1E0A}{\dotaccent{D}}%
  \DeclareUnicodeCharacter{1E0B}{\dotaccent{d}}%
  \DeclareUnicodeCharacter{1E0C}{\udotaccent{D}}%
  \DeclareUnicodeCharacter{1E0D}{\udotaccent{d}}%
  \DeclareUnicodeCharacter{1E0E}{\ubaraccent{D}}%
  \DeclareUnicodeCharacter{1E0F}{\ubaraccent{d}}%
  %
  \DeclareUnicodeCharacter{1E1E}{\dotaccent{F}}%
  \DeclareUnicodeCharacter{1E1F}{\dotaccent{f}}%
  %
  \DeclareUnicodeCharacter{1E20}{\=G}%
  \DeclareUnicodeCharacter{1E21}{\=g}%
  \DeclareUnicodeCharacter{1E22}{\dotaccent{H}}%
  \DeclareUnicodeCharacter{1E23}{\dotaccent{h}}%
  \DeclareUnicodeCharacter{1E24}{\udotaccent{H}}%
  \DeclareUnicodeCharacter{1E25}{\udotaccent{h}}%
  \DeclareUnicodeCharacter{1E26}{\"H}%
  \DeclareUnicodeCharacter{1E27}{\"h}%
  %
  \DeclareUnicodeCharacter{1E30}{\'K}%
  \DeclareUnicodeCharacter{1E31}{\'k}%
  \DeclareUnicodeCharacter{1E32}{\udotaccent{K}}%
  \DeclareUnicodeCharacter{1E33}{\udotaccent{k}}%
  \DeclareUnicodeCharacter{1E34}{\ubaraccent{K}}%
  \DeclareUnicodeCharacter{1E35}{\ubaraccent{k}}%
  \DeclareUnicodeCharacter{1E36}{\udotaccent{L}}%
  \DeclareUnicodeCharacter{1E37}{\udotaccent{l}}%
  \DeclareUnicodeCharacter{1E3A}{\ubaraccent{L}}%
  \DeclareUnicodeCharacter{1E3B}{\ubaraccent{l}}%
  \DeclareUnicodeCharacter{1E3E}{\'M}%
  \DeclareUnicodeCharacter{1E3F}{\'m}%
  %
  \DeclareUnicodeCharacter{1E40}{\dotaccent{M}}%
  \DeclareUnicodeCharacter{1E41}{\dotaccent{m}}%
  \DeclareUnicodeCharacter{1E42}{\udotaccent{M}}%
  \DeclareUnicodeCharacter{1E43}{\udotaccent{m}}%
  \DeclareUnicodeCharacter{1E44}{\dotaccent{N}}%
  \DeclareUnicodeCharacter{1E45}{\dotaccent{n}}%
  \DeclareUnicodeCharacter{1E46}{\udotaccent{N}}%
  \DeclareUnicodeCharacter{1E47}{\udotaccent{n}}%
  \DeclareUnicodeCharacter{1E48}{\ubaraccent{N}}%
  \DeclareUnicodeCharacter{1E49}{\ubaraccent{n}}%
  %
  \DeclareUnicodeCharacter{1E54}{\'P}%
  \DeclareUnicodeCharacter{1E55}{\'p}%
  \DeclareUnicodeCharacter{1E56}{\dotaccent{P}}%
  \DeclareUnicodeCharacter{1E57}{\dotaccent{p}}%
  \DeclareUnicodeCharacter{1E58}{\dotaccent{R}}%
  \DeclareUnicodeCharacter{1E59}{\dotaccent{r}}%
  \DeclareUnicodeCharacter{1E5A}{\udotaccent{R}}%
  \DeclareUnicodeCharacter{1E5B}{\udotaccent{r}}%
  \DeclareUnicodeCharacter{1E5E}{\ubaraccent{R}}%
  \DeclareUnicodeCharacter{1E5F}{\ubaraccent{r}}%
  %
  \DeclareUnicodeCharacter{1E60}{\dotaccent{S}}%
  \DeclareUnicodeCharacter{1E61}{\dotaccent{s}}%
  \DeclareUnicodeCharacter{1E62}{\udotaccent{S}}%
  \DeclareUnicodeCharacter{1E63}{\udotaccent{s}}%
  \DeclareUnicodeCharacter{1E6A}{\dotaccent{T}}%
  \DeclareUnicodeCharacter{1E6B}{\dotaccent{t}}%
  \DeclareUnicodeCharacter{1E6C}{\udotaccent{T}}%
  \DeclareUnicodeCharacter{1E6D}{\udotaccent{t}}%
  \DeclareUnicodeCharacter{1E6E}{\ubaraccent{T}}%
  \DeclareUnicodeCharacter{1E6F}{\ubaraccent{t}}%
  %
  \DeclareUnicodeCharacter{1E7C}{\~V}%
  \DeclareUnicodeCharacter{1E7D}{\~v}%
  \DeclareUnicodeCharacter{1E7E}{\udotaccent{V}}%
  \DeclareUnicodeCharacter{1E7F}{\udotaccent{v}}%
  %
  \DeclareUnicodeCharacter{1E80}{\`W}%
  \DeclareUnicodeCharacter{1E81}{\`w}%
  \DeclareUnicodeCharacter{1E82}{\'W}%
  \DeclareUnicodeCharacter{1E83}{\'w}%
  \DeclareUnicodeCharacter{1E84}{\"W}%
  \DeclareUnicodeCharacter{1E85}{\"w}%
  \DeclareUnicodeCharacter{1E86}{\dotaccent{W}}%
  \DeclareUnicodeCharacter{1E87}{\dotaccent{w}}%
  \DeclareUnicodeCharacter{1E88}{\udotaccent{W}}%
  \DeclareUnicodeCharacter{1E89}{\udotaccent{w}}%
  \DeclareUnicodeCharacter{1E8A}{\dotaccent{X}}%
  \DeclareUnicodeCharacter{1E8B}{\dotaccent{x}}%
  \DeclareUnicodeCharacter{1E8C}{\"X}%
  \DeclareUnicodeCharacter{1E8D}{\"x}%
  \DeclareUnicodeCharacter{1E8E}{\dotaccent{Y}}%
  \DeclareUnicodeCharacter{1E8F}{\dotaccent{y}}%
  %
  \DeclareUnicodeCharacter{1E90}{\^Z}%
  \DeclareUnicodeCharacter{1E91}{\^z}%
  \DeclareUnicodeCharacter{1E92}{\udotaccent{Z}}%
  \DeclareUnicodeCharacter{1E93}{\udotaccent{z}}%
  \DeclareUnicodeCharacter{1E94}{\ubaraccent{Z}}%
  \DeclareUnicodeCharacter{1E95}{\ubaraccent{z}}%
  \DeclareUnicodeCharacter{1E96}{\ubaraccent{h}}%
  \DeclareUnicodeCharacter{1E97}{\"t}%
  \DeclareUnicodeCharacter{1E98}{\ringaccent{w}}%
  \DeclareUnicodeCharacter{1E99}{\ringaccent{y}}%
  %
  \DeclareUnicodeCharacter{1EA0}{\udotaccent{A}}%
  \DeclareUnicodeCharacter{1EA1}{\udotaccent{a}}%
  %
  \DeclareUnicodeCharacter{1EB8}{\udotaccent{E}}%
  \DeclareUnicodeCharacter{1EB9}{\udotaccent{e}}%
  \DeclareUnicodeCharacter{1EBC}{\~E}%
  \DeclareUnicodeCharacter{1EBD}{\~e}%
  %
  \DeclareUnicodeCharacter{1ECA}{\udotaccent{I}}%
  \DeclareUnicodeCharacter{1ECB}{\udotaccent{i}}%
  \DeclareUnicodeCharacter{1ECC}{\udotaccent{O}}%
  \DeclareUnicodeCharacter{1ECD}{\udotaccent{o}}%
  %
  \DeclareUnicodeCharacter{1EE4}{\udotaccent{U}}%
  \DeclareUnicodeCharacter{1EE5}{\udotaccent{u}}%
  %
  \DeclareUnicodeCharacter{1EF2}{\`Y}%
  \DeclareUnicodeCharacter{1EF3}{\`y}%
  \DeclareUnicodeCharacter{1EF4}{\udotaccent{Y}}%
  %
  \DeclareUnicodeCharacter{1EF8}{\~Y}%
  \DeclareUnicodeCharacter{1EF9}{\~y}%
  %
  % Punctuation
  \DeclareUnicodeCharacter{2013}{--}%
  \DeclareUnicodeCharacter{2014}{---}%
  \DeclareUnicodeCharacter{2018}{\quoteleft{}}%
  \DeclareUnicodeCharacter{2019}{\quoteright{}}%
  \DeclareUnicodeCharacter{201A}{\quotesinglbase{}}%
  \DeclareUnicodeCharacter{201C}{\quotedblleft{}}%
  \DeclareUnicodeCharacter{201D}{\quotedblright{}}%
  \DeclareUnicodeCharacter{201E}{\quotedblbase{}}%
  \DeclareUnicodeCharacter{2020}{\ensuremath\dagger}%
  \DeclareUnicodeCharacter{2021}{\ensuremath\ddagger}%
  \DeclareUnicodeCharacter{2022}{\bullet{}}%
  \DeclareUnicodeCharacter{202F}{\thinspace}%
  \DeclareUnicodeCharacter{2026}{\dots{}}%
  \DeclareUnicodeCharacter{2039}{\guilsinglleft{}}%
  \DeclareUnicodeCharacter{203A}{\guilsinglright{}}%
  %
  \DeclareUnicodeCharacter{20AC}{\euro{}}%
  %
  \DeclareUnicodeCharacter{2192}{\expansion{}}%
  \DeclareUnicodeCharacter{21D2}{\result{}}%
  %
  % Mathematical symbols
  \DeclareUnicodeCharacter{2200}{\ensuremath\forall}%
  \DeclareUnicodeCharacter{2203}{\ensuremath\exists}%
  \DeclareUnicodeCharacter{2208}{\ensuremath\in}%
  \DeclareUnicodeCharacter{2212}{\minus{}}%
  \DeclareUnicodeCharacter{2217}{\ast}%
  \DeclareUnicodeCharacter{221E}{\ensuremath\infty}%
  \DeclareUnicodeCharacter{2225}{\ensuremath\parallel}%
  \DeclareUnicodeCharacter{2227}{\ensuremath\wedge}%
  \DeclareUnicodeCharacter{2229}{\ensuremath\cap}%
  \DeclareUnicodeCharacter{2261}{\equiv{}}%
  \DeclareUnicodeCharacter{2264}{\ensuremath\leq}%
  \DeclareUnicodeCharacter{2265}{\ensuremath\geq}%
  \DeclareUnicodeCharacter{2282}{\ensuremath\subset}%
  \DeclareUnicodeCharacter{2287}{\ensuremath\supseteq}%
  %
  \DeclareUnicodeCharacter{2016}{\ensuremath\Vert}%
  \DeclareUnicodeCharacter{2032}{\ensuremath\prime}%
  \DeclareUnicodeCharacter{210F}{\ensuremath\hbar}%
  \DeclareUnicodeCharacter{2111}{\ensuremath\Im}%
  \DeclareUnicodeCharacter{2113}{\ensuremath\ell}%
  \DeclareUnicodeCharacter{2118}{\ensuremath\wp}%
  \DeclareUnicodeCharacter{211C}{\ensuremath\Re}%
  \DeclareUnicodeCharacter{2135}{\ensuremath\aleph}%
  \DeclareUnicodeCharacter{2190}{\ensuremath\leftarrow}%
  \DeclareUnicodeCharacter{2191}{\ensuremath\uparrow}%
  \DeclareUnicodeCharacter{2193}{\ensuremath\downarrow}%
  \DeclareUnicodeCharacter{2194}{\ensuremath\leftrightarrow}%
  \DeclareUnicodeCharacter{2195}{\ensuremath\updownarrow}%
  \DeclareUnicodeCharacter{2196}{\ensuremath\nwarrow}%
  \DeclareUnicodeCharacter{2197}{\ensuremath\nearrow}%
  \DeclareUnicodeCharacter{2198}{\ensuremath\searrow}%
  \DeclareUnicodeCharacter{2199}{\ensuremath\swarrow}%
  \DeclareUnicodeCharacter{21A6}{\ensuremath\mapsto}%
  \DeclareUnicodeCharacter{21A9}{\ensuremath\hookleftarrow}%
  \DeclareUnicodeCharacter{21AA}{\ensuremath\hookrightarrow}%
  \DeclareUnicodeCharacter{21BC}{\ensuremath\leftharpoonup}%
  \DeclareUnicodeCharacter{21BD}{\ensuremath\leftharpoondown}%
  \DeclareUnicodeCharacter{21C0}{\ensuremath\rightharpoonup}%
  \DeclareUnicodeCharacter{21C1}{\ensuremath\rightharpoondown}%
  \DeclareUnicodeCharacter{21CC}{\ensuremath\rightleftharpoons}%
  \DeclareUnicodeCharacter{21D0}{\ensuremath\Leftarrow}%
  \DeclareUnicodeCharacter{21D1}{\ensuremath\Uparrow}%
  \DeclareUnicodeCharacter{21D3}{\ensuremath\Downarrow}%
  \DeclareUnicodeCharacter{21D4}{\ensuremath\Leftrightarrow}%
  \DeclareUnicodeCharacter{21D5}{\ensuremath\Updownarrow}%
  \DeclareUnicodeCharacter{2202}{\ensuremath\partial}%
  \DeclareUnicodeCharacter{2205}{\ensuremath\emptyset}%
  \DeclareUnicodeCharacter{2207}{\ensuremath\nabla}%
  \DeclareUnicodeCharacter{2209}{\ensuremath\notin}%
  \DeclareUnicodeCharacter{220B}{\ensuremath\owns}%
  \DeclareUnicodeCharacter{220F}{\ensuremath\prod}%
  \DeclareUnicodeCharacter{2210}{\ensuremath\coprod}%
  \DeclareUnicodeCharacter{2211}{\ensuremath\sum}%
  \DeclareUnicodeCharacter{2213}{\ensuremath\mp}%
  \DeclareUnicodeCharacter{2218}{\ensuremath\circ}%
  \DeclareUnicodeCharacter{221A}{\ensuremath\surd}%
  \DeclareUnicodeCharacter{221D}{\ensuremath\propto}%
  \DeclareUnicodeCharacter{2220}{\ensuremath\angle}%
  \DeclareUnicodeCharacter{2223}{\ensuremath\mid}%
  \DeclareUnicodeCharacter{2228}{\ensuremath\vee}%
  \DeclareUnicodeCharacter{222A}{\ensuremath\cup}%
  \DeclareUnicodeCharacter{222B}{\ensuremath\smallint}%
  \DeclareUnicodeCharacter{222E}{\ensuremath\oint}%
  \DeclareUnicodeCharacter{223C}{\ensuremath\sim}%
  \DeclareUnicodeCharacter{2240}{\ensuremath\wr}%
  \DeclareUnicodeCharacter{2243}{\ensuremath\simeq}%
  \DeclareUnicodeCharacter{2245}{\ensuremath\cong}%
  \DeclareUnicodeCharacter{2248}{\ensuremath\approx}%
  \DeclareUnicodeCharacter{224D}{\ensuremath\asymp}%
  \DeclareUnicodeCharacter{2250}{\ensuremath\doteq}%
  \DeclareUnicodeCharacter{2260}{\ensuremath\neq}%
  \DeclareUnicodeCharacter{226A}{\ensuremath\ll}%
  \DeclareUnicodeCharacter{226B}{\ensuremath\gg}%
  \DeclareUnicodeCharacter{227A}{\ensuremath\prec}%
  \DeclareUnicodeCharacter{227B}{\ensuremath\succ}%
  \DeclareUnicodeCharacter{2283}{\ensuremath\supset}%
  \DeclareUnicodeCharacter{2286}{\ensuremath\subseteq}%
  \DeclareUnicodeCharacter{228E}{\ensuremath\uplus}%
  \DeclareUnicodeCharacter{2291}{\ensuremath\sqsubseteq}%
  \DeclareUnicodeCharacter{2292}{\ensuremath\sqsupseteq}%
  \DeclareUnicodeCharacter{2293}{\ensuremath\sqcap}%
  \DeclareUnicodeCharacter{2294}{\ensuremath\sqcup}%
  \DeclareUnicodeCharacter{2295}{\ensuremath\oplus}%
  \DeclareUnicodeCharacter{2296}{\ensuremath\ominus}%
  \DeclareUnicodeCharacter{2297}{\ensuremath\otimes}%
  \DeclareUnicodeCharacter{2298}{\ensuremath\oslash}%
  \DeclareUnicodeCharacter{2299}{\ensuremath\odot}%
  \DeclareUnicodeCharacter{22A2}{\ensuremath\vdash}%
  \DeclareUnicodeCharacter{22A3}{\ensuremath\dashv}%
  \DeclareUnicodeCharacter{22A4}{\ensuremath\ptextop}%
  \DeclareUnicodeCharacter{22A5}{\ensuremath\bot}%
  \DeclareUnicodeCharacter{22A8}{\ensuremath\models}%
  \DeclareUnicodeCharacter{22C0}{\ensuremath\bigwedge}%
  \DeclareUnicodeCharacter{22C1}{\ensuremath\bigvee}%
  \DeclareUnicodeCharacter{22C2}{\ensuremath\bigcap}%
  \DeclareUnicodeCharacter{22C3}{\ensuremath\bigcup}%
  \DeclareUnicodeCharacter{22C4}{\ensuremath\diamond}%
  \DeclareUnicodeCharacter{22C5}{\ensuremath\cdot}%
  \DeclareUnicodeCharacter{22C6}{\ensuremath\star}%
  \DeclareUnicodeCharacter{22C8}{\ensuremath\bowtie}%
  \DeclareUnicodeCharacter{2308}{\ensuremath\lceil}%
  \DeclareUnicodeCharacter{2309}{\ensuremath\rceil}%
  \DeclareUnicodeCharacter{230A}{\ensuremath\lfloor}%
  \DeclareUnicodeCharacter{230B}{\ensuremath\rfloor}%
  \DeclareUnicodeCharacter{2322}{\ensuremath\frown}%
  \DeclareUnicodeCharacter{2323}{\ensuremath\smile}%
  %
  \DeclareUnicodeCharacter{25B3}{\ensuremath\triangle}%
  \DeclareUnicodeCharacter{25B7}{\ensuremath\triangleright}%
  \DeclareUnicodeCharacter{25BD}{\ensuremath\bigtriangledown}%
  \DeclareUnicodeCharacter{25C1}{\ensuremath\triangleleft}%
  \DeclareUnicodeCharacter{25C7}{\ensuremath\diamond}%
  \DeclareUnicodeCharacter{2660}{\ensuremath\spadesuit}%
  \DeclareUnicodeCharacter{2661}{\ensuremath\heartsuit}%
  \DeclareUnicodeCharacter{2662}{\ensuremath\diamondsuit}%
  \DeclareUnicodeCharacter{2663}{\ensuremath\clubsuit}%
  \DeclareUnicodeCharacter{266D}{\ensuremath\flat}%
  \DeclareUnicodeCharacter{266E}{\ensuremath\natural}%
  \DeclareUnicodeCharacter{266F}{\ensuremath\sharp}%
  \DeclareUnicodeCharacter{26AA}{\ensuremath\bigcirc}%
  \DeclareUnicodeCharacter{27B9}{\ensuremath\rangle}%
  \DeclareUnicodeCharacter{27C2}{\ensuremath\perp}%
  \DeclareUnicodeCharacter{27E8}{\ensuremath\langle}%
  \DeclareUnicodeCharacter{27F5}{\ensuremath\longleftarrow}%
  \DeclareUnicodeCharacter{27F6}{\ensuremath\longrightarrow}%
  \DeclareUnicodeCharacter{27F7}{\ensuremath\longleftrightarrow}%
  \DeclareUnicodeCharacter{27FC}{\ensuremath\longmapsto}%
  \DeclareUnicodeCharacter{29F5}{\ensuremath\setminus}%
  \DeclareUnicodeCharacter{2A00}{\ensuremath\bigodot}%
  \DeclareUnicodeCharacter{2A01}{\ensuremath\bigoplus}%
  \DeclareUnicodeCharacter{2A02}{\ensuremath\bigotimes}%
  \DeclareUnicodeCharacter{2A04}{\ensuremath\biguplus}%
  \DeclareUnicodeCharacter{2A06}{\ensuremath\bigsqcup}%
  \DeclareUnicodeCharacter{2A3F}{\ensuremath\amalg}%
  \DeclareUnicodeCharacter{2AAF}{\ensuremath\preceq}%
  \DeclareUnicodeCharacter{2AB0}{\ensuremath\succeq}%
  %
  \global\mathchardef\checkmark="1370% actually the square root sign
  \DeclareUnicodeCharacter{2713}{\ensuremath\checkmark}%
}% end of \unicodechardefs

% UTF-8 byte sequence (pdfTeX) definitions (replacing and @U command)
% It makes the setting that replace UTF-8 byte sequence.
\def\utfeightchardefs{%
  \let\DeclareUnicodeCharacter\DeclareUnicodeCharacterUTFviii
  \unicodechardefs
}

% Whether the active definitions of non-ASCII characters expand to
% non-active tokens with the same character code.  This is used to
% write characters literally, instead of using active definitions for
% printing the correct glyphs.
\newif\ifpassthroughchars
\passthroughcharsfalse

% For native Unicode handling (XeTeX and LuaTeX),
% provide a definition macro to replace/pass-through a Unicode character
%
\def\DeclareUnicodeCharacterNative#1#2{%
  \catcode"#1=\active
  \def\dodeclareunicodecharacternative##1##2##3{%
    \begingroup
      \uccode`\~="##2\relax
      \uppercase{\gdef~}{%
        \ifpassthroughchars
          ##1%
        \else
          ##3%
        \fi
      }
    \endgroup
  }
  \begingroup
    \uccode`\.="#1\relax
    \uppercase{\def\UTFNativeTmp{.}}%
    \expandafter\dodeclareunicodecharacternative\UTFNativeTmp{#1}{#2}%
  \endgroup
}

% Native Unicode handling (XeTeX and LuaTeX) character replacing definition.
% It activates the setting that replaces Unicode characters.
\def\nativeunicodechardefs{%
  \let\DeclareUnicodeCharacter\DeclareUnicodeCharacterNative
  \unicodechardefs
}

% For native Unicode handling (XeTeX and LuaTeX),
% make the character token expand
% to the sequences given in \unicodechardefs for printing.
\def\DeclareUnicodeCharacterNativeAtU#1#2{%
  \def\UTFAtUTmp{#2}
  \expandafter\globallet\csname uni:#1\endcsname \UTFAtUTmp
}

% @U command definitions for native Unicode handling (XeTeX and LuaTeX).
\def\nativeunicodechardefsatu{%
  \let\DeclareUnicodeCharacter\DeclareUnicodeCharacterNativeAtU
  \unicodechardefs
}

% US-ASCII character definitions.
\def\asciichardefs{% nothing need be done
   \relax
}

% Define all Unicode characters we know about.  This makes UTF-8 the default
% input encoding and allows @U to work.
\iftxinativeunicodecapable
  \nativeunicodechardefsatu
\else
  \utfeightchardefs
\fi

\message{formatting,}

\newdimen\defaultparindent \defaultparindent = 15pt

\chapheadingskip = 15pt plus 4pt minus 2pt
\secheadingskip = 12pt plus 3pt minus 2pt
\subsecheadingskip = 9pt plus 2pt minus 2pt

% Prevent underfull vbox error messages.
\vbadness = 10000

% Don't be very finicky about underfull hboxes, either.
\hbadness = 6666

% Following George Bush, get rid of widows and orphans.
\widowpenalty=10000
\clubpenalty=10000

% Use TeX 3.0's \emergencystretch to help line breaking, but if we're
% using an old version of TeX, don't do anything.  We want the amount of
% stretch added to depend on the line length, hence the dependence on
% \hsize.  We call this whenever the paper size is set.
%
\def\setemergencystretch{%
  \ifx\emergencystretch\thisisundefined
    % Allow us to assign to \emergencystretch anyway.
    \def\emergencystretch{\dimen0}%
  \else
    \emergencystretch = .15\hsize
  \fi
}

% Parameters in order: 1) textheight; 2) textwidth;
% 3) voffset; 4) hoffset; 5) binding offset; 6) topskip;
% 7) physical page height; 8) physical page width.
%
% We also call \setleading{\textleading}, so the caller should define
% \textleading.  The caller should also set \parskip.
%
\def\internalpagesizes#1#2#3#4#5#6#7#8{%
  \voffset = #3\relax
  \topskip = #6\relax
  \splittopskip = \topskip
  %
  \vsize = #1\relax
  \advance\vsize by \topskip
  \outervsize = \vsize
  \advance\outervsize by 2\topandbottommargin
  \txipageheight = \vsize
  %
  \hsize = #2\relax
  \outerhsize = \hsize
  \advance\outerhsize by 0.5in
  \txipagewidth = \hsize
  %
  \normaloffset = #4\relax
  \bindingoffset = #5\relax
  %
  \ifpdf
    \pdfpageheight #7\relax
    \pdfpagewidth #8\relax
    % if we don't reset these, they will remain at "1 true in" of
    % whatever layout pdftex was dumped with.
    \pdfhorigin = 1 true in
    \pdfvorigin = 1 true in
  \else
    \ifx\XeTeXrevision\thisisundefined
      \special{papersize=#8,#7}%
    \else
      \pdfpageheight #7\relax
      \pdfpagewidth #8\relax
      % XeTeX does not have \pdfhorigin and \pdfvorigin.
    \fi
  \fi
  %
  \setleading{\textleading}
  %
  \parindent = \defaultparindent
  \setemergencystretch
}

% @letterpaper (the default).
\def\letterpaper{{\globaldefs = 1
  \parskip = 3pt plus 2pt minus 1pt
  \textleading = 13.2pt
  %
  % If page is nothing but text, make it come out even.
  \internalpagesizes{607.2pt}{6in}% that's 46 lines
                    {\voffset}{.25in}%
                    {\bindingoffset}{36pt}%
                    {11in}{8.5in}%
}}

% Use @smallbook to reset parameters for 7x9.25 trim size.
\def\smallbook{{\globaldefs = 1
  \parskip = 2pt plus 1pt
  \textleading = 12pt
  %
  \internalpagesizes{7.5in}{5in}%
                    {-.2in}{0in}%
                    {\bindingoffset}{16pt}%
                    {9.25in}{7in}%
  %
  \lispnarrowing = 0.3in
  \tolerance = 700
  \contentsrightmargin = 0pt
  \defbodyindent = .5cm
}}

% Use @smallerbook to reset parameters for 6x9 trim size.
% (Just testing, parameters still in flux.)
\def\smallerbook{{\globaldefs = 1
  \parskip = 1.5pt plus 1pt
  \textleading = 12pt
  %
  \internalpagesizes{7.4in}{4.8in}%
                    {-.2in}{-.4in}%
                    {0pt}{14pt}%
                    {9in}{6in}%
  %
  \lispnarrowing = 0.25in
  \tolerance = 700
  \contentsrightmargin = 0pt
  \defbodyindent = .4cm
}}

% Use @afourpaper to print on European A4 paper.
\def\afourpaper{{\globaldefs = 1
  \parskip = 3pt plus 2pt minus 1pt
  \textleading = 13.2pt
  %
  % Double-side printing via postscript on Laserjet 4050
  % prints double-sided nicely when \bindingoffset=10mm and \hoffset=-6mm.
  % To change the settings for a different printer or situation, adjust
  % \normaloffset until the front-side and back-side texts align.  Then
  % do the same for \bindingoffset.  You can set these for testing in
  % your texinfo source file like this:
  % @tex
  % \global\normaloffset = -6mm
  % \global\bindingoffset = 10mm
  % @end tex
  \internalpagesizes{673.2pt}{160mm}% that's 51 lines
                    {\voffset}{\hoffset}%
                    {\bindingoffset}{44pt}%
                    {297mm}{210mm}%
  %
  \tolerance = 700
  \contentsrightmargin = 0pt
  \defbodyindent = 5mm
}}

% Use @afivepaper to print on European A5 paper.
% From <EMAIL>, 2 July 2000.
% He also recommends making @example and @lisp be small.
\def\afivepaper{{\globaldefs = 1
  \parskip = 2pt plus 1pt minus 0.1pt
  \textleading = 12.5pt
  %
  \internalpagesizes{160mm}{120mm}%
                    {\voffset}{\hoffset}%
                    {\bindingoffset}{8pt}%
                    {210mm}{148mm}%
  %
  \lispnarrowing = 0.2in
  \tolerance = 800
  \contentsrightmargin = 0pt
  \defbodyindent = 2mm
  \tableindent = 12mm
}}

% A specific text layout, 24x15cm overall, intended for A4 paper.
\def\afourlatex{{\globaldefs = 1
  \afourpaper
  \internalpagesizes{237mm}{150mm}%
                    {\voffset}{4.6mm}%
                    {\bindingoffset}{7mm}%
                    {297mm}{210mm}%
  %
  % Must explicitly reset to 0 because we call \afourpaper.
  \globaldefs = 0
}}

% Use @afourwide to print on A4 paper in landscape format.
\def\afourwide{{\globaldefs = 1
  \afourpaper
  \internalpagesizes{241mm}{165mm}%
                    {\voffset}{-2.95mm}%
                    {\bindingoffset}{7mm}%
                    {297mm}{210mm}%
  \globaldefs = 0
}}

\def\bsixpaper{{\globaldefs = 1
  \afourpaper
  \internalpagesizes{140mm}{100mm}%
                    {-6.35mm}{-12.7mm}%
                    {\bindingoffset}{14pt}%
                    {176mm}{125mm}%
  \let\SETdispenvsize=\smallword
  \lispnarrowing = 0.2in
  \globaldefs = 0
}}


% @pagesizes TEXTHEIGHT[,TEXTWIDTH]
% Perhaps we should allow setting the margins, \topskip, \parskip,
% and/or leading, also. Or perhaps we should compute them somehow.
%
\parseargdef\pagesizes{\pagesizesyyy #1,,\finish}
\def\pagesizesyyy#1,#2,#3\finish{{%
  \setbox0 = \hbox{\ignorespaces #2}\ifdim\wd0 > 0pt \hsize=#2\relax \fi
  \globaldefs = 1
  %
  \parskip = 3pt plus 2pt minus 1pt
  \setleading{\textleading}%
  %
  \dimen0 = #1\relax
  \advance\dimen0 by 2.5in % default 1in margin above heading line
                           % and 1.5in to include heading, footing and
                           % bottom margin
  %
  \dimen2 = \hsize
  \advance\dimen2 by 2in % default to 1 inch margin on each side
  %
  \internalpagesizes{#1}{\hsize}%
                    {\voffset}{\normaloffset}%
                    {\bindingoffset}{44pt}%
                    {\dimen0}{\dimen2}%
}}

% Set default to letter.
%
\letterpaper

% Default value of \hfuzz, for suppressing warnings about overfull hboxes.
\hfuzz = 1pt


\message{and turning on texinfo input format.}

\def^^L{\par} % remove \outer, so ^L can appear in an @comment

% DEL is a comment character, in case @c does not suffice.
\catcode`\^^? = 14

% Define macros to output various characters with catcode for normal text.
\catcode`\"=\other \def\normaldoublequote{"}
\catcode`\$=\other \def\normaldollar{$}%$ font-lock fix
\catcode`\+=\other \def\normalplus{+}
\catcode`\<=\other \def\normalless{<}
\catcode`\>=\other \def\normalgreater{>}
\catcode`\^=\other \def\normalcaret{^}
\catcode`\_=\other \def\normalunderscore{_}
\catcode`\|=\other \def\normalverticalbar{|}
\catcode`\~=\other \def\normaltilde{~}

% This macro is used to make a character print one way in \tt
% (where it can probably be output as-is), and another way in other fonts,
% where something hairier probably needs to be done.
%
% #1 is what to print if we are indeed using \tt; #2 is what to print
% otherwise.  Since all the Computer Modern typewriter fonts have zero
% interword stretch (and shrink), and it is reasonable to expect all
% typewriter fonts to have this, we can check that font parameter.
%
\def\ifusingtt#1#2{\ifdim \fontdimen3\font=0pt #1\else #2\fi}

% Same as above, but check for italic font.  Actually this also catches
% non-italic slanted fonts since it is impossible to distinguish them from
% italic fonts.  But since this is only used by $ and it uses \sl anyway
% this is not a problem.
\def\ifusingit#1#2{\ifdim \fontdimen1\font>0pt #1\else #2\fi}

% Set catcodes for Texinfo file

% Active characters for printing the wanted glyph.
% Most of these we simply print from the \tt font, but for some, we can
% use math or other variants that look better in normal text.
%
\catcode`\"=\active
\def\activedoublequote{{\tt\char34}}
\let"=\activedoublequote
\catcode`\~=\active \def\activetilde{{\tt\char126}} \let~ = \activetilde
\chardef\hatchar=`\^
\catcode`\^=\active \def\activehat{{\tt \hatchar}} \let^ = \activehat

\catcode`\_=\active
\def_{\ifusingtt\normalunderscore\_}
\def\_{\leavevmode \kern.07em \vbox{\hrule width.3em height.1ex}\kern .07em }
\let\realunder=_

\catcode`\|=\active \def|{{\tt\char124}}

\chardef \less=`\<
\catcode`\<=\active \def\activeless{{\tt \less}}\let< = \activeless
\chardef \gtr=`\>
\catcode`\>=\active \def\activegtr{{\tt \gtr}}\let> = \activegtr
\catcode`\+=\active \def+{{\tt \char 43}}
\catcode`\$=\active \def${\ifusingit{{\sl\$}}\normaldollar}%$ font-lock fix
\catcode`\-=\active \let-=\normaldash


% used for headline/footline in the output routine, in case the page
% breaks in the middle of an @tex block.
\def\texinfochars{%
  \let< = \activeless
  \let> = \activegtr
  \let~ = \activetilde
  \let^ = \activehat
  \markupsetuplqdefault \markupsetuprqdefault
  \let\b = \strong
  \let\i = \smartitalic
  % in principle, all other definitions in \tex have to be undone too.
}

% Used sometimes to turn off (effectively) the active characters even after
% parsing them.
\def\turnoffactive{%
  \normalturnoffactive
  \otherbackslash
}

\catcode`\@=0

% \backslashcurfont outputs one backslash character in current font,
% as in \char`\\.
\global\chardef\backslashcurfont=`\\

% \realbackslash is an actual character `\' with catcode other.
{\catcode`\\=\other @gdef@realbackslash{\}}

% In Texinfo, backslash is an active character; it prints the backslash
% in fixed width font.
\catcode`\\=\active  % @ for escape char from now on.

% Print a typewriter backslash.  For math mode, we can't simply use
% \backslashcurfont: the story here is that in math mode, the \char
% of \backslashcurfont ends up printing the roman \ from the math symbol
% font (because \char in math mode uses the \mathcode, and plain.tex
% sets \mathcode`\\="026E).  Hence we use an explicit \mathchar,
% which is the decimal equivalent of "715c (class 7, e.g., use \fam;
% ignored family value; char position "5C).  We can't use " for the
% usual hex value because it has already been made active.

@def@ttbackslash{{@tt @ifmmode @mathchar29020 @else @backslashcurfont @fi}}
@let@backslashchar = @ttbackslash % @backslashchar{} is for user documents.

% \otherbackslash defines an active \ to be a literal `\' character with
% catcode other.
@gdef@otherbackslash{@let\=@realbackslash}

% Same as @turnoffactive except outputs \ as {\tt\char`\\} instead of
% the literal character `\'.
%
{@catcode`- = @active
 @gdef@normalturnoffactive{%
   @passthroughcharstrue
   @let-=@normaldash
   @let"=@normaldoublequote
   @let$=@normaldollar %$ font-lock fix
   @let+=@normalplus
   @let<=@normalless
   @let>=@normalgreater
   @let^=@normalcaret
   @let_=@normalunderscore
   @let|=@normalverticalbar
   @let~=@normaltilde
   @let\=@ttbackslash
   @markupsetuplqdefault
   @markupsetuprqdefault
   @unsepspaces
 }
}

% If a .fmt file is being used, characters that might appear in a file
% name cannot be active until we have parsed the command line.
% So turn them off again, and have @fixbackslash turn them back on.
@catcode`+=@other @catcode`@_=@other

% \enablebackslashhack - allow file to begin `\input texinfo'
%
% If a .fmt file is being used, we don't want the `\input texinfo' to show up.
% That is what \eatinput is for; after that, the `\' should revert to printing
% a backslash.
% If the file did not have a `\input texinfo', then it is turned off after
% the first line; otherwise the first `\' in the file would cause an error.
% This is used on the very last line of this file, texinfo.tex.
% We also use @c to call @fixbackslash, in case ends of lines are hidden.
{
@catcode`@^=7
@catcode`@^^M=13@gdef@enablebackslashhack{%
  @global@let\ = @eatinput%
  @catcode`@^^M=13%
  @def@c{@fixbackslash@c}%
  % Definition for the newline at the end of this file.
  @def ^^M{@let^^M@secondlinenl}%
  % Definition for a newline in the main Texinfo file.
  @gdef @secondlinenl{@fixbackslash}%
  % In case the first line has a whole-line command on it
  @let@originalparsearg@parsearg
  @def@parsearg{@fixbackslash@originalparsearg}
}}

{@catcode`@^=7 @catcode`@^^M=13%
@gdef@eatinput input texinfo#1^^M{@fixbackslash}}

% Emergency active definition of newline, in case an active newline token
% appears by mistake.
{@catcode`@^=7 @catcode13=13%
@gdef@enableemergencynewline{%
  @gdef^^M{%
    @par%
    %<warning: active newline>@par%
}}}


@gdef@fixbackslash{%
  @ifx\@eatinput @let\ = @ttbackslash @fi
  @catcode13=5 % regular end of line
  @enableemergencynewline
  @let@c=@comment
  @let@parsearg@originalparsearg
  % Also turn back on active characters that might appear in the input
  % file name, in case not using a pre-dumped format.
  @catcode`+=@active
  @catcode`@_=@active
  %
  % If texinfo.cnf is present on the system, read it.
  % Useful for site-wide @afourpaper, etc.  This macro, @fixbackslash, gets
  % called at the beginning of every Texinfo file.  Not opening texinfo.cnf
  % directly in this file, texinfo.tex, makes it possible to make a format
  % file for Texinfo.
  %
  @openin 1 texinfo.cnf
  @ifeof 1 @else @input texinfo.cnf @fi
  @closein 1
}


% Say @foo, not \foo, in error messages.
@escapechar = `@@

% These (along with & and #) are made active for url-breaking, so need
% active definitions as the normal characters.
@def@normaldot{.}
@def@normalquest{?}
@def@normalslash{/}

% These look ok in all fonts, so just make them not special.
% @hashchar{} gets its own user-level command, because of #line.
@catcode`@& = @other @def@normalamp{&}
@catcode`@# = @other @def@normalhash{#}
@catcode`@% = @other @def@normalpercent{%}

@let @hashchar = @normalhash

@c Finally, make ` and ' active, so that txicodequoteundirected and
@c txicodequotebacktick work right in, e.g., @w{@code{`foo'}}.  If we
@c don't make ` and ' active, @code will not get them as active chars.
@c Do this last of all since we use ` in the previous @catcode assignments.
@catcode`@'=@active
@catcode`@`=@active
@markupsetuplqdefault
@markupsetuprqdefault

@c Local variables:
@c eval: (add-hook 'before-save-hook 'time-stamp)
@c page-delimiter: "^\\\\message\\|emacs-page"
@c time-stamp-start: "def\\\\texinfoversion{"
@c time-stamp-format: "%:y-%02m-%02d.%02H"
@c time-stamp-end: "}"
@c End:

@c vim:sw=2:

@enablebackslashhack
