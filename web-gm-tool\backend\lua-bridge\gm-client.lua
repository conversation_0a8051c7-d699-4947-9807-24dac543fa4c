-- GM工具通信桥接脚本
-- 使用与GM工具完全相同的通信机制

-- 加载必要的模块
local mp = require("msgpack")
local json = require("cjson")

-- 复制GM工具的通信类
local PackClient = {}
PackClient.__index = PackClient

-- 初始化函数（复制自GM工具）
function PackClient:new()
    local obj = {}
    setmetatable(obj, PackClient)
    
    -- 使用与GM工具相同的luahp.client
    obj._new = require("luahp.client")
    obj._hp = obj._new()
    obj._hp:Create_TcpPackClient(obj)
    
    -- 计算包头标识（与GM工具完全相同）
    local Flag = 0
    for i,v in ipairs{string.byte("GGELUA_FLAG", 1, 11)} do
        Flag = Flag+v
    end
    
    obj._hp:SetPackHeaderFlag(Flag)
    print('[Lua桥接] PackClient初始化完成 - 包头标识:', Flag)
    
    return obj
end

-- 连接函数（复制自GM工具）
function PackClient:connect(ip, port)
    local result = self._hp:Start(ip, port, 0)
    if result == 1 then
        print('[Lua桥接] 连接成功 -', ip .. ':' .. port)
        return true
    else
        local errorCode = self._hp:GetLastError()
        local errorDesc = self._hp:GetLastErrorDesc()
        print('[Lua桥接] 连接失败 - 错误码:', errorCode, '描述:', errorDesc)
        return false
    end
end

-- 发送函数（复制自GM工具）
function PackClient:send(data)
    local packed_data = mp.pack{data}
    print('[Lua桥接] 发送MessagePack - 长度:', #packed_data)
    
    -- 显示前16字节
    local hexStr = ""
    for i = 1, math.min(#packed_data, 16) do
        hexStr = hexStr .. string.format("%02X ", string.byte(packed_data, i))
    end
    print('[Lua桥接] 前16字节:', hexStr)
    
    self._hp:SendPack(packed_data)
    return true
end

-- 数据到达回调（复制自GM工具）
function PackClient:OnReceive(pData, iLength)
    print('[Lua桥接] 接收数据包 - 长度:', iLength)
    
    local data1 = mp.unpack(pData)
    if data1 == nil or (data1 ~= nil and data1[1] == nil) then
        print('[Lua桥接] MessagePack解包失败')
        return 1
    end
    
    local content = data1[1]
    -- 这里应该调用jm1解密函数，但为了简化，我们先打印原始数据
    print('[Lua桥接] 接收到加密数据:', string.sub(content, 1, 50) .. '...')
    
    return 1
end

-- 连接成功回调
function PackClient:OnConnect()
    print('[Lua桥接] 连接成功回调')
    return 1
end

-- 连接断开回调
function PackClient:OnClose(so, ec)
    print('[Lua桥接] 连接断开回调 - 错误码:', ec)
    return 1
end

-- 发送完成回调
function PackClient:OnSend(pData, iLength)
    print('[Lua桥接] 数据发送完成 - 长度:', iLength)
    return 1
end

-- 主函数
function main()
    print('=== Lua桥接GM工具通信测试 ===')
    
    -- 创建客户端
    local client = PackClient:new()
    
    -- 连接到服务器
    if not client:connect('127.0.0.1', 6888) then
        print('❌ 连接失败')
        return
    end
    
    -- 等待连接稳定
    os.execute("timeout /t 1 /nobreak > nul")
    
    -- 发送登录数据（使用与GM工具相同的加密数据）
    local encryptedData = 'qL,de,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,es,Yx,vt,xi,Cb,j1,vt,Zu,23,Uc,vt,cK,Cb,xi,Pf,NR,vd,Wa,ET,VP,j1,hY,Cx,Wa,CO,cK,wd,xi,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,PW,pF,j1,hY,VP,dc,Aa,wd,S9,3C,DT,mP,is,3C,Au,yP,A2,2W,JA,yP,A2,2W,JA,hY,aW,3C,Cb,xi,Pf,NR,gZ,VP,dc,DG,yP,JA,dc,DG,es,q6,Au,Ve,es,Aa,Pf,2W,cj,xi,qL,CO,vd,VP,XC,hY,q6,0W,qL,de,Au,CO,23,2W,u2,='
    
    print('[Lua桥接] 发送登录数据...')
    client:send(encryptedData)
    
    -- 等待响应
    print('[Lua桥接] 等待服务器响应...')
    os.execute("timeout /t 3 /nobreak > nul")
    
    print('[Lua桥接] 测试完成')
end

-- 运行主函数
main()
