--======================================================================--
-- @作者: GGE研究群: 342119466
-- @创建时间:   2018-03-03 02:34:19
-- @Last Modified time: 2025-05-22 11:50:22
-- 梦幻西游游戏资源破解 <EMAIL>(313738139) 老毕   和 C++PrimerPlus 717535046 这俩位大神破解所以资源
--======================================================================--
local 活动内容 = class()
function 活动内容:初始化()end
function 活动内容:地图单位对话(连接id,数字id,序列,标识,地图)
          if not 标识  or not 任务数据[标识] then return end
          local 类型 = 任务数据[标识].类型
          if 类型==103 then return end
          if 玩家数据[数字id].战斗~=0 then return  end
          玩家数据[数字id].地图单位={地图=地图,标识=标识,编号=序列}
          local 判断标识 = function(任务,本身)
                local 任务id = 标识
                if 任务 then
                    if not 取队员任务一致(数字id,任务) then
                        return false
                    end
                    任务id = 玩家数据[数字id].角色:取任务(任务)
                    if 本身 and 任务id~=标识 then
                        return false
                    end
                end
                if not 任务id or 任务id==0 or not 任务数据[任务id] then
                    return false
                end
                local 玩家组 = {}
                if 任务数据[任务id].玩家id and 任务数据[任务id].玩家id~=0 then
                    table.insert(玩家组,任务数据[任务id].玩家id)
                end
                if 任务数据[任务id].队伍组 and type(任务数据[任务id].队伍组)=="table" then
                    for i,v in ipairs(任务数据[任务id].队伍组) do
                      table.insert(玩家组,v)
                    end
                end
                local 找到 = false
                for i,v in ipairs(玩家组) do
                      if 数字id ==v then
                        找到 = true
                        break
                      end
                end
                if not 找到 then
                   return false
                end
                return true
          end
          local 判断队伍 = function()
                  if not 玩家数据[数字id].队伍 or 玩家数据[数字id].队伍==0 then
                    常规提示(数字id,"#Y必须组队才能触发该活动")
                    return  false
                  end
                  return true
          end
          local 对话数据={}
          对话数据.模型=任务数据[标识].模型
          对话数据.名称=任务数据[标识].名称
          对话数据.对话="我好像不认识你吧？？？"
          if 任务数据[标识].战斗 then
              玩家数据[数字id].地图单位 = nil
              玩家数据[数字id].最后对话={模型=对话数据.模型,名称=对话数据.名称}
              对话数据.对话="我正在战斗中，请勿打扰。"
              发送数据(连接id,1501,对话数据)
              return
          end
          if 类型==4 and 数字id==任务数据[标识].玩家id then --宝图强盗
                对话数据.对话="看什么看，没见过这么英俊潇洒的强盗？"
                对话数据.选项={"交出宝藏","不好意思，我认错人了"}
          elseif 类型==22 then --摇钱树
                  if 数字id==任务数据[标识].玩家id then
                        对话数据.对话="这是你种下的摇钱树哦，你想要做什么呢？"
                        对话数据.选项={"查看情况","给摇钱树浇水","给摇钱树施肥","给摇钱树除虫","路过,顺便看看的"}
                        if 任务数据[标识].阶段<4 then
                            if 任务数据[标识].操作==false then
                              对话数据.对话="摇钱树正在茁壮的成长#Y需要照料时会有提醒，请注意#56"
                              对话数据.选项={}
                            end
                        else
                            对话数据.对话="我已经长大了，你现在可以轻轻地摇动我获得意外的惊喜哟。（你还可以摇动#R"..任务数据[标识].次数.."#W次)"
                            对话数据.选项={"轻轻摇动","取消"}
                        end
                  else
                      对话数据.对话="这好像不是你的摇钱树哟？？？"
                  end
          elseif 类型==23 then
                if 数字id==任务数据[标识].玩家id then
                    if 任务数据[标识].刷出强盗 then
                        对话数据.对话="强盗就在附近虎视眈眈,还不赶快去杀了他！"
                    else
                        local 添加银子 = 玩家数据[数字id].角色.数据.等级 * 500
                        local 随机参数 = 取随机数(1,100)
                        if 随机参数 <= 50 then
                              local 获得物品={}
                             for i=1,#自定义数据.摇钱树 do
                                 if 取随机数()<=自定义数据.摇钱树[i].概率 then
                                    获得物品[#获得物品+1]=自定义数据.摇钱树[i]
                                 end
                             end
                             获得物品=删除重复(获得物品)
                             if 获得物品~=nil then
                                local 取编号=取随机数(1,#获得物品)
                                if 获得物品[取编号]~=nil and 获得物品[取编号].名称~=nil and 获得物品[取编号].数量~=nil then
                                   玩家数据[数字id].道具:自定义给予道具(数字id,获得物品[取编号].名称,获得物品[取编号].数量,获得物品[取编号].参数)
                                   if 获得物品[取编号].名称=="特殊魔兽要诀" or 获得物品[取编号].名称=="超级魔兽要诀" or 获得物品[取编号].名称=="高级魔兽要诀" or 获得物品[取编号].名称=="制造指南书" or 获得物品[取编号].名称=="灵饰指南书" or 获得物品[取编号].名称=="神兜兜" then
                                      广播消息({内容=string.format("#S(摇钱树)#R/#Y据说#R%s#Y种植摇钱树获得了一个#G/%s",玩家数据[数字id].角色.数据.名称,获得物品[取编号].名称),频道="xt"})
                                   end
                                end
                             end
                        else
                            添加银子 = 添加银子 * 2
                        end
                        玩家数据[数字id].角色:添加银子(添加银子,"摇钱树",1)
                        地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].编号)
                    end
                else
                    对话数据.对话="这好像不是你的物品哟！"
                end
          elseif 类型==24 and 数字id==任务数据[标识].玩家id then
                    对话数据.对话="看什么看，没见过这么英俊潇洒的强盗？"
                    对话数据.选项={"纳命来","不好意思，我认错人了"}
          elseif 类型==209 and 数字id==任务数据[标识].玩家id then --宝图强盗
                  对话数据.对话="哟西，就凭你这点能耐想要捉拿我？那是不可能滴，我劝你收下我的这点银子就此放弃，否则可别怪爷爷教你怎么人#4"
                  对话数据.选项={"我岂是贪污受贿之人","既然如此，就留你一条活路","你让我想想"}
          elseif 类型==101 then
                    对话数据.对话="被压在地下千年了，是谁放我出来的#24我保证留你半条小命#24"
                    对话数据.选项={"少说废话,开打","青山不转，绿水长流，我们后会有期"}
          elseif 类型==102 then
                    对话数据.对话="人家一个小宝宝在这里好怕怕呀#15"
                    对话数据.选项={"我来领养你","回家找你爸妈去"}
          elseif 类型==104 then
                  对话数据.对话="让你找到我不代表你就是有仙缘之人，想要验证你是否有仙缘，必须先经过本星君的考验#36"
                  对话数据.选项={"请星君赐教","我只是路过"}
          elseif 类型==108 then
                if 判断标识(109) then
                    if 任务数据[玩家数据[数字id].角色:取任务(109)].序列== 任务数据[标识].序列 then
                        对话数据.对话="恭喜你们成功抵达，点击我要报道即可。"
                        对话数据.选项={"我要报道","我只是路过"}
                    else
                      对话数据.对话="你现在无法在我这里进行报道，请查看任务说明。"
                    end
                else
                    对话数据.对话="请先前往傲来国报名官处领取任务。"
                end
          elseif 类型==105 then
                  对话数据.对话="在地下呆久了闷得慌，好不容易有机会上来，自然要好好疯狂一番。你可不要打扰我的雅兴，要不然我要了你的小命#4"
                  对话数据.选项={"让我来收拾你","我只是路过"}
          elseif 类型==8 then
                  if 判断标识(8,1) then
                      对话数据.对话="悄悄地告诉你，其实我是从地狱里偷跑出来的#89"
                      对话数据.选项={"回你的地狱去","我只是路过"}
                  else
                      对话数据.对话="我好像不认识你吧？？？"
                  end
          elseif 类型==211 then
                  if 判断标识(211,1) then
                      对话数据.对话="悄悄地告诉你，其实我是从地狱里偷跑出来的#89"
                      对话数据.选项={"回你的地狱去","我只是路过"}
                  else
                      对话数据.对话="我好像不认识你吧？？？"
                  end
          elseif 类型==11 then
                  if 判断标识(11,1)then
                      对话数据.对话="少侠为追查官府被盗一事而来，必定想听听看我是不是说真话。你听好了，我要说的是：#Y"..[[
                        @*&@*&(!&(*@&)@*()*@)(*@()*!)。]]..[[
                        #W现在你知道我是侠客还是大盗了吧。#90]]
                      对话数据.选项={"嗯，你是磊落的侠客，失敬失敬！","哼，你是真的盗贼，我要将你抓捕归案！","让我再想一想"}
                  else
                      对话数据.对话="我好像不认识你吧？？？"
                  end
          elseif 类型==12 then
                    if 判断标识(12,1) then
                      对话数据.对话="虽然我看起来像个贼，名字也跟贼有关。但实际上我是一个好人哟。"
                      对话数据.选项={"将偷盗的宝物交出来","我相信你是个好人"}
                    else
                      对话数据.对话="我好像不认识你吧？？？"
                    end
          elseif 类型==16 then
                  if 判断标识(17) then
                      if 任务数据[玩家数据[数字id].角色:取任务(17)].进程 == 任务数据[标识].进程 then
                          对话数据.对话="小侠客,你准备好了就来挑战我的分身吧！#24"
                          对话数据.选项={"我来试试","我只是路过"}
                      else
                          对话数据.对话="请按照任务指示寻找指定元辰完成挑战！#24"
                      end
                  else
                      对话数据.对话="吾乃十二元辰之一的"..任务数据[标识].名称.."分影,如果你参加了天降辰星活动的话,就可以挑战我！"
                  end
          elseif 类型==18 and 判断标识(18,1) then
                  对话数据.对话="听说你的实力不错,我特意下凡前来考验你一番！#24"
                  对话数据.选项={"接受考验","我还得再修炼修炼"}
          elseif 类型==200 and 判断标识(200,1) then
                  对话数据.对话="又一个想要铃铛的吊毛,找死!"
                  对话数据.选项={"我要降服你","我相信你是个好人"}
          elseif 类型==110 and 数字id==任务数据[标识].玩家id then
                  if 任务数据[标识].分类==1 then
                      对话数据.对话="哟，您是哪冒出来的王八羔子哈，当心大爷一拳打得你找不着南北。"
                      对话数据.选项={"放肆，看我怎么收拾你","不好意思，我认错人了"}
                  elseif 任务数据[标识].分类==2 then
                          对话数据.对话="太好了，你这批物资可真是帮了我们的大忙。"
                          对话数据.选项={"请接收物资","就不给你"}
                  end
          elseif 类型==111 and 数字id==任务数据[标识].玩家id then
                  if 任务数据[标识].分类==5 then
                      对话数据.对话="本门才是天下第一，我不是针对是谁，我是想说除了本门派，其它的都是垃圾#24"
                      对话数据.选项={"放肆，找打","不好意思，我认错人了"}
                  elseif 任务数据[标识].分类==6 then
                          对话数据.对话="太好了，终于等待你的救援了，赶紧帮我把敌人击退吧。"
                          对话数据.选项={"不要怕，我来帮你","您再顶顶"}
                  elseif 任务数据[标识].分类==7 then
                          对话数据.对话="汝等小儿太自不量力了吧，就凭你这三脚猫的功夫，也让当出头鸟？"
                          对话数据.选项={"手底下分高低","您说得对，我走先"}
                  end
          elseif 类型==112 and 数字id==任务数据[标识].玩家id then
                  if 任务数据[标识].分类==5 then
                      对话数据.对话="我才是天下第一，我不是针对是谁，我是想说除了本座，其它的都是垃圾#24"
                      对话数据.选项={"放肆，找打","不好意思，我认错人了"}
                  elseif 任务数据[标识].分类==6 then
                      对话数据.对话="太好了，终于等待你的救援了，赶紧帮我把敌人击退吧。"
                      对话数据.选项={"不要怕，我来帮你","您再顶顶"}
                  elseif 任务数据[标识].分类==7 then
                      对话数据.对话="汝等小儿太自不量力了吧，就凭你这三脚猫的功夫，也让当出头鸟？"
                      对话数据.选项={"手底下分高低","您说得对，我走先"}
                  end
          elseif 类型==201 then
                  if 任务数据[标识].名称=="迷宫土地" then
                      对话数据.对话="我是本层迷宫的土地，我可以帮你使用传送功能离开本层迷宫。除了第一层和第二十层迷宫外，每层迷宫都有两个土地。其中只有一个能帮你传送到下一层迷宫，至于另外一个嘛，你猜猜他会给你传送到哪呢#1"
                      对话数据.选项={"我要离开本层迷宫","你就是那另外一个吧"}
                  elseif 任务数据[标识].名称=="迷宫守卫" then
                          对话数据.对话="恭喜你成功通过了所有迷宫，请领取你的奖励吧。#Y/最先达到的前五个玩家可以获得额外奖励。"
                          对话数据.选项={"领取奖励","不稀罕你那破奖励"}
                  end
          elseif 类型==203 then
                  玩家数据[数字id].道具:开启宝藏山小宝箱(数字id,标识)
                  return
          elseif 类型==204 then
                  玩家数据[数字id].道具:开启宝藏山大宝箱(数字id,标识)
                  return
          elseif 类型==205 then
                  对话数据.对话="是哪个王八羔子在老子头上敲了个大包#4"
                  对话数据.选项={"休得在此放肆","我帮你去打听下"}
          elseif 类型==206 then
                  if 任务数据[标识].等级==60 then
                      对话数据.对话="我是60级世界BOSS万年妖王，本BOSS难度非常高，如您的实力不足，请勿向本BOSS发起挑战。等级大于或小于本世界BOSS等级20以上的玩家无法对我发起挑战。你是否已经准备好对我发起挑战？"
                  elseif 任务数据[标识].等级==100 then
                            对话数据.对话="我是100级世界BOSS万年鬼王，本BOSS难度非常高，如您的实力不足，请勿向本BOSS发起挑战。等级大于或小于本世界BOSS等级20以上的玩家无法对我发起挑战。你是否已经准备好对我发起挑战？"
                  elseif 任务数据[标识].等级==150 then
                            对话数据.对话="我是150级世界BOSS罗刹王，本BOSS难度非常高，如您的实力不足，请勿向本BOSS发起挑战。等级大于或小于本世界BOSS等级20以上的玩家无法对我发起挑战。你是否已经准备好对我发起挑战？"
                  end
                  对话数据.选项={"我们准备好了","我只是路过"}
          elseif 类型==207 and 判断标识(208) then
                  if 玩家数据[数字id].队伍==0 then
                      对话数据.对话="本活动需要组队才能完成。"
                  elseif 取队伍人数(数字id)<3 then
                          对话数据.对话="本活动最少需要五人组队才能完成。"
                  else
                        local 任务id = 玩家数据[数字id].角色:取任务(208)
                        if 任务数据[任务id].序列~=任务数据[标识].序列 then
                            对话数据.对话="你当前不需要在我这里报道。"
                        else
                            战斗准备类:创建战斗(数字id+0,100025,任务id)
                            玩家数据[数字id].地图单位=nil
                            return
                        end
                  end
          elseif 类型==210 then
                  对话数据.对话="看本大王敲破你们的脑袋#24#R（本活动需要五人组队参加且人物等级必须达到60级）"
                  对话数据.选项={"知了还这么嚣张？讨打！","大王好厉害哟"}
          elseif 类型==121 and 判断队伍() then
                  任务数据[标识].战斗=true
                  战斗准备类:创建战斗(数字id+0,100028,标识)
                  玩家数据[数字id].地图单位=nil
                 return
          elseif 类型==122 and 判断队伍() then
                  local 副本id=任务数据[标识].副本id
                  副本数据.乌鸡国.进行[副本id].和尚数量=副本数据.乌鸡国.进行[副本id].和尚数量+1
                  if 副本数据.乌鸡国.进行[副本id].和尚数量>14 then
                    副本数据.乌鸡国.进行[副本id].进程=3
                    任务处理类:设置乌鸡国副本(副本id)
                  end
                  for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                     local 临时id = 队伍数据[玩家数据[数字id].队伍].成员数据[n]
                         玩家数据[临时id].角色:添加经验(100000,"乌鸡国",1)
                         玩家数据[临时id].角色:添加银子(50000,"乌鸡国",1)
                         玩家数据[临时id].角色:刷新任务跟踪()
                         常规提示(临时id,"#Y您的副本进度已经更新")
                  end
                  地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].编号)
                  任务数据[标识]=nil
                  return
          elseif 类型==123 then
                  对话数据.对话="那妖怪正在皇宫内冒充我作恶，还请少侠赶紧进去揭露事情真相。"
                  对话数据.选项={"送我进去","我再想想"}
          elseif 类型==124 and 判断队伍() then
                  对话数据.对话="你是谁？离这里远一点。"
                  对话数据.选项={"哼，少废话，看打！","我就是隔壁老王啊"}
          elseif 类型==125 and 判断队伍() then
                  对话数据.对话="你是谁？离这里远一点。"
                  对话数据.选项={"休得在此作恶","我就是隔壁老王啊"}
          elseif 类型==126 and 判断队伍() then
                对话数据.对话="我就是真的国王，你不信也得信#4。"
                对话数据.选项={"我看你就是假的国王","没错，你就是真的国王"}
          elseif 类型==127 then
                  if 对话数据.模型=="银两" then
                      local wb = {}
                      wb[1] = "哎啊!我什么在这里了#24？"
                      wb[2] = "啊!我是一个刚刚成精的银子,我偷跑出来迷路了#69？"
                      wb[3] = "奇怪!刚刚我还在银堆里面......#45"
                      wb[4] = "这..!都怪那阵死风,什么把我吹这么远了....我要回去!回去!#60"
                      对话数据.对话=wb[取随机数(1,#wb)]
                      对话数据.选项={"捡起来,放口袋里","我只是路过的"}
                  elseif 对话数据.模型=="食物" or 对话数据.模型=="口粮" or 对话数据.模型=="摄妖香" or 对话数据.模型=="药品" then
                          local wb = {}
                          wb[1] = "哎啊!我什么在这里了#24？"
                          wb[2] = "啊!我还是是一个刚刚成精的孩子,我偷跑出来迷路了#69？"
                          wb[3] = "奇怪!刚刚我还在孩子堆里面......#45"
                          wb[4] = "这..!都怪那阵死风,什么把我吹这么远了....我要回去!回去!#60"
                          对话数据.对话=wb[取随机数(1,#wb)]
                          对话数据.选项={"捡物品,放口袋里","我只是路过的"}
                  elseif 对话数据.模型=="海星" or 对话数据.模型=="海毛虫" or 对话数据.模型=="大海龟" or 对话数据.模型=="巨蛙" then
                          local wb = {}
                          wb[1] = "哎啊!我什么在这里了#24？"
                          wb[2] = "啊!我偷跑出来迷路了#69？"
                          wb[3] = "奇怪!刚刚我还在海里游......#45"
                          wb[4] = "这..!都怪那阵死风,什么把我吹这么远了....我要回去!回去!#60"
                          对话数据.对话=wb[取随机数(1,#wb)]
                          对话数据.选项={"抓起来,做宠物","我们后会有期"}
                  end
          elseif 类型==128 then
                  local wb = {}
                  wb[1] = "天天在天庭,都快闷死了".."#"..取随机数(1,110).."？"
                  wb[2] = "偶尔下凡当个山大王也不错".."#"..取随机数(1,110).."？"
                  wb[3] = "诶哦!你是什么发现我的,既然装不下去了,看来不能让你走了".."#"..取随机数(1,110).."。"
                  对话数据.对话=wb[取随机数(1,#wb)]
                  对话数据.选项={"神仙不做,看来我要抓你回天庭","我只是路过"}
          elseif 类型==129 then
                  local wb = {}
                  wb[1] = "天天在烂果园里面,都快闷死了".."#"..取随机数(1,110).."？"
                  wb[2] = "偶尔出来个调皮一下也不错".."#"..取随机数(1,110).."？"
                  wb[3] = "诶哦!你是想干嘛".."#"..取随机数(1,110).."。"
                  wb[4] = "是谁?握住了我命运的后脖颈".."#"..取随机数(1,110).."。"
                  对话数据.对话=wb[取随机数(1,#wb)]
                  对话数据.选项={"派对时刻,要和我一起派对战斗吗?" ,"我只是路过"}
          elseif 类型==131 then
                  local 序列=取随机数(1,#科举题库)
                  local 正确答案=科举题库[序列][4]
                  local 随机答案={}
                  for n=2,4 do
                        随机答案[n-1]={答案=科举题库[序列][n],序列=取随机数(1,9999)}
                  end
                  table.sort(随机答案,function(a,b) return a.序列>b.序列 end )
                  local 显示答案={}
                  for n=1,3 do
                      显示答案[n]=随机答案[n].答案
                  end
                  if 玩家数据[数字id].车迟数据==nil then
                    玩家数据[数字id].车迟数据={题目=0,答案=0,正确答案=0}
                  end
                  玩家数据[数字id].车迟数据={题目=科举题库[序列][1],答案=显示答案,正确答案=正确答案}
                  玩家数据[数字id].车迟对话=true
                  对话数据.对话=string.format("#W/%s", 玩家数据[数字id].车迟数据.题目)
                  对话数据.选项=玩家数据[数字id].车迟数据.答案
          elseif 类型==132 then
                  if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                  local 副本id=任务数据[标识].副本id
                  if 副本数据.车迟斗法.进行[副本id].进程==2 then
                      副本数据.车迟斗法.进行[副本id].进程=3
                      任务处理类:设置车迟斗法副本(副本id)
                      地图处理类:当前消息广播1(6021,"#Y道观内刷新了一批贡品！")
                      对话数据.对话="#Y道观建设完毕，但是还差一些贡品，少侠快帮忙收集下贡品吧！"
                      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                          玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                      end

                  elseif 副本数据.车迟斗法.进行[副本id].进程==3 then
                        if 副本数据.车迟斗法.进行[副本id].车迟贡品 <15 then
                          对话数据.对话="#Y你吃掉了#R"..副本数据.车迟斗法.进行[副本id].车迟贡品.."#Y个贡品(#R需要吃掉15个贡品才行哦#)"
                        else
                          对话数据.对话="#Y多谢少侠帮忙，快去三清殿看看"
                          对话数据.选项={"送我过去","我再转转"}
                        end
                  end
          elseif 类型==133 then
                  对话数据.对话="我就是贡品，你们想做什么？"
                  对话数据.选项={"我来吃掉你","我很饱了，放你一马"}
          elseif 类型==134 then
                  对话数据.对话="吾乃三清，尔等是否来仰慕吾等，还不速速跪拜#4。"
                  对话数据.选项={"妖怪找打","我是来仰慕三清的"}
          elseif 类型==135 and 判断队伍() then
                  local 副本id=任务数据[标识].副本id
                  if 副本数据.车迟斗法.进行[副本id].进程==5 then
                      对话数据.对话="#Y捣乱的三个妖怪跑到了九霄云外去了，少侠赶快去制服它们！"
                      对话数据.选项={"送我过去","我再转转"}
                  end
          elseif 类型==136 and 判断队伍() then
                  local wb={}
                  local 副本id = 任务数据[标识].副本id
                  if 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==6 then
                      副本数据.车迟斗法.进行[副本id].进程=7
                      任务处理类:设置车迟斗法副本(副本id)
                      local 名称={"雷公","电母","雨师"}
                      if 副本数据.车迟斗法.进行[副本id].不动 then
                        名称={"你不动","你不动","我不动"}
                      end
                      local 表述={}
                      for n=1,3 do
                        if 副本数据.车迟斗法.进行[副本id].序列[n]~=true then
                          表述[#表述+1]=名称[n]
                        end
                      end
                      local 名称=""
                      for n=1,#表述 do
                        if n==#表述 then
                          名称=名称..表述[n]
                        else
                          名称=名称..表述[n].."、"
                        end
                      end
                      对话数据.对话 = string.format("少侠能来到这里，代表少侠能力过人，但是我这里还需要考验下少侠的能力，去阻止#R%s",名称)
                      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                            玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                      end
                  else
                      对话数据.对话="#Y若少侠暂时有事，可以暂时离开副本"
                      对话数据.选项={"送我过去","我再转转"}
                  end
          elseif 类型==137 then
                  对话数据.对话="太上老君急急如意令，风兮~雨兮归来兮，电闪雷鸣！！！#4。"
                  对话数据.选项={"上仙莫要助纣为虐","我只是路过的"}
          elseif 类型==138 then
                  对话数据.对话="敌不动，我不动，看谁能奈何谁！！！#4。"
                  对话数据.选项={"妖怪看打","我只是路过的"}
          elseif 类型==139 and 判断队伍() then
                  local 副本id = 任务数据[标识].副本id
                  if 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==8 then
                    对话数据.对话="想要收拾三大妖怪，还得找到齐天大圣变化造型提升实力，少侠去找齐天大圣变化造型吧。"
                    副本数据.车迟斗法.进行[副本id].进程=9
                    任务处理类:设置车迟斗法副本(副本id)
                    for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                        玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                    end
                  elseif 副本数据.车迟斗法.进行[副本id].进程==9 and 玩家数据[数字id].角色.数据.变身数据~=nil then
                          对话数据.对话="既然少侠已经变化造型提升了能力，那就去找它们算帐吧。"
                          副本数据.车迟斗法.进行[副本id].进程=10
                          任务处理类:设置车迟斗法副本(副本id)
                          for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                                玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                          end
                  elseif 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==9 then
                          对话数据.对话="想要收拾三大妖怪，还得找到齐天大圣变化造型提升实力，少侠去找齐天大圣变化造型吧。"
                  elseif 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==10 then
                          对话数据.对话="既然少侠已经变化造型提升了能力，那就去找它们算帐吧。"
                  end
          elseif 类型==140 then
                  local 副本id = 任务数据[标识].副本id
                  if 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==9 then
                      对话数据.对话="七十二变之道，乃非常之道，夺天地制造化，侵日月之玄机，而念念之生灭，又岂止七十二变？心宁气静，六识生灭，瀑流不停，如若变化，了别诸尘……"
                      对话数据.选项={"在下心平气和，准备好变化(每次需要10点体力)","我再想想"}
                  elseif 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==10 then
                          对话数据.对话="既然少侠已经变化造型提升了能力，那就去找它们算帐吧。"
                  end
          elseif 类型==141 then
                  local 副本id = 任务数据[标识].副本id
                  if 任务数据[标识].名称=="羊力" and 副本数据.车迟斗法.进行[副本id].真假[1] and 任务数据[标识].战斗==nil then
                      对话数据.对话="既然让你找到了，那么就让我送你下地狱吧！"
                      对话数据.选项={"不知悔改，看我如何收服你","我只是路过的"}
                  elseif 任务数据[标识].名称=="鹿力" and 副本数据.车迟斗法.进行[副本id].真假[2] and 任务数据[标识].战斗==nil then
                          对话数据.对话="既然让你找到了，那么就让我送你下地狱吧！"
                          对话数据.选项={"不知悔改，看我如何收服你","我只是路过的"}
                  elseif 任务数据[标识].名称=="虎力" and 副本数据.车迟斗法.进行[副本id].真假[3] and 任务数据[标识].战斗==nil then
                          对话数据.对话="既然让你找到了，那么就让我送你下地狱吧！"
                          对话数据.选项={"不知悔改，看我如何收服你","我只是路过的"}
                  else
                      对话数据.对话="你能看得见我吗？确定是找我的吗？"
                  end
          elseif 类型==142 then
                  if 任务数据[标识].绑定id == nil or 任务数据[标识].绑定id ~= 数字id then
                      对话数据.对话="这好像不是你的宝箱哟。"
                  else
                      local 副本id = 任务数据[标识].副本id
                      副本数据.车迟斗法.进行[副本id].宝箱数量 = 副本数据.车迟斗法.进行[副本id].宝箱数量 - 1
                      玩家数据[数字id].道具:开启车迟胜利宝箱(数字id,标识)
                      if 副本数据.车迟斗法.进行[副本id].宝箱数量 <= 0 then
                        副本数据.车迟斗法.进行[副本id].宝箱数量 = nil
                        副本数据.车迟斗法.进行[副本id].进程=12
                        local 副本任务id = 玩家数据[数字id].角色:取任务(130)
                        任务处理类:设置车迟斗法副本(副本id,副本任务id)
                        地图处理类:当前消息广播2(6023,string.format("#G恭喜少侠完成了车迟斗法副本，地图上刷出了更新的奖励宝箱，请少侠快去领取！！！"),副本任务id)
                      end
                      return
                  end
          elseif 类型==143 then
                  玩家数据[数字id].道具:开启车迟福利宝箱(数字id,标识)
                  return
          elseif 类型==151 then
                  local 副本id=任务数据[标识].副本id
                  if 任务数据[标识].名称=="蟠桃树" then
                      对话数据.对话="！！！！！！"
                      if 副本数据.水陆大会.进行[副本id]~=nil and 副本数据.水陆大会.进行[副本id].进程==1 and 玩家数据[数字id].角色:取任务(150)~=0 and 玩家数据[数字id].采摘木材 then
                         对话数据.选项 = {"采摘木材","我就看看"}
                      end
                  end
          elseif 类型==152 and 判断队伍() then
                  local 副本id=任务数据[标识].副本id
                  if 任务数据[标识].名称=="玄奘法师" then
                      对话数据.对话="感谢少侠为水陆大会建设做出的贡献，水陆大会马上要开始了"
                      for i,v in pairs(地图处理类.地图单位[6024]) do
                          if 任务数据[地图处理类.地图单位[6024][i].id].副本id == 副本id then
                              地图处理类:删除单位(6024,i)
                          end
                      end
                      常规提示(数字id,"#Y道场突然狂风大作，少侠快看看发生了什么情况！")
                      副本数据.水陆大会.进行[副本id].进程=3
                      任务处理类:设置水陆大会副本(副本id)
                      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                         玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                      end
                  end
          elseif 类型==153 and 判断队伍() then
                  local 副本id=任务数据[标识].副本id
                  if 任务数据[标识].名称=="受伤的程咬金" then
                      对话数据.对话="呃...这些畜生好是厉害，竟把我伤着。玄奘法师正准备颂经时，突然天色大变，狂风滚滚，杀出十几个妖邪，最可怕的是有一个妖邪半空中闪出，九尺长切有着茶盏大小的双眼，呼啦的张开大嘴，把大家卷了去，黑云好像往寺后院飘去。老夫冒死相拼还是没救下唐王！！快！快！唐王和玄奘法师均被这般妖孽掳去。我伤势太重，有劳诸位了。"
                      地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].编号)
                      副本数据.水陆大会.进行[副本id].进程=4
                      任务处理类:设置水陆大会副本(副本id)
                      for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                         玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                      end
                  end
          elseif 类型 == 156 then
                对话数据.对话="唐王和玄奘法师都被妖怪掳到了妖魔巢穴，少侠是否动身前往呢？"
                对话数据.选项={"快送我过去","我只是路过"}
          elseif 类型 == 157 then
                local 副本id=任务数据[标识].副本id
                if 任务数据[标识].名称=="魑魅" or 任务数据[标识].名称=="魍魉" then
                    if 副本数据.水陆大会.进行[副本id].击败小妖==3 and 副本数据.水陆大会.进行[副本id].击败头领==2 and 副本数据.水陆大会.进行[副本id].击败将军==2 then
                      对话数据.对话="早就听说吃了唐僧肉可以长生不老，终于让我们逮到了，哈哈哈！！！"
                      对话数据.选项={"妖孽找死","我只是路过"}
                    else
                      对话数据.对话="想要降服我，先跟我的手下过过招吧！！！"
                    end
                else
                      if 任务数据[标识].战斗~=nil then 常规提示(数字id,"#Y/对方正在战斗中") return  end
                      if 玩家数据[数字id].队伍==0 then 常规提示(数字id,"#Y必须组队才能触发该活动") return end
                      if 取队伍人数(数字id)<3 then 常规提示(数字id,"#Y队伍人数低于3人，无法进入战斗") return  end
                      任务数据[标识].战斗=true
                      战斗准备类:创建战斗(数字id+0,100118,标识)
                      return
                end
          elseif 类型 == 158 and 判断队伍() then
                  对话数据.对话="感谢少侠助我们脱困，这里有些小小的礼物，不成敬意！"
                  对话数据.选项={"除魔卫道是我们应尽的职责","我只是路过"}
          elseif 类型==161 and 判断队伍() then
                  对话数据.对话="老师今天教新书了，考考你，答对了就告诉你陈关保和一秤金在哪。"
                  对话数据.选项={"好啊，快出题吧小考官。","我可没功夫跟你玩。"}
          elseif 类型==162 and 判断队伍() then
                对话数据.对话="爹爹说今天可以随便玩，好高兴哦！"
                对话数据.选项={"好可爱的小朋友，看的我，变！（会取消原有的变身效果）","我还是喜欢我这成熟的造型。"}
          elseif 类型==163 and 判断队伍() then
                  对话数据.对话="准备好了就触摸灵灯吧。"
                  对话数据.选项={"触摸灵灯","稍等一会"}
          elseif 类型==164 and 判断队伍() then
                  if 任务数据[标识].名称=="唐僧" and 副本数据.通天河.进行[数字id].进程==4  then
                      对话数据.对话="灵感大王战败逃脱，然而恼羞成怒，派出手下河妖大军，浩浩荡荡进攻陈家庄。少侠快去阻止河妖保卫村庄。"
                      对话数据.选项={"我这就前往","稍等一会"}
                  else
                      对话数据.对话="快去除妖"
                  end
        elseif 类型==165 and 判断队伍() then
                对话数据.对话="大王叫我来巡山喽#24美味的食物我来了"
                对话数据.选项={"小小河妖还这么嚣张？讨打！","我只是路过"}
        elseif 类型==166 and 判断队伍() then
                对话数据.对话="想要见到菩萨，先过我这一关"
                对话数据.选项={"那就得罪了","我只是路过"}
        elseif 类型==167 and 判断队伍() then
                if 副本数据.通天河.进行[数字id].进程==7 then
                   对话数据.对话="灵感大王原是潮音洞的鲤鱼所变。众人帮忙采集五色竹条，编织成降魔鱼蓝对付妖怪。"
                    local 副本id=任务数据[标识].副本id
                    副本数据.通天河.进行[副本id].进程=8
                    任务处理类:设置通天河副本(副本id)
                    for n=1,#队伍数据[玩家数据[数字id].队伍].成员数据 do
                     玩家数据[队伍数据[玩家数据[数字id].队伍].成员数据[n]].角色:刷新任务跟踪()
                    end
                else
                    对话数据.对话="快去除妖"
                end
        elseif 类型==168 and 判断队伍() then
                  if 取队伍人数(数字id)<3 then 常规提示(数字id,"#Y/挑战通天河最少要有3人") return  end
                  任务数据[标识].战斗=true
                  战斗准备类:创建战斗(数字id+0,100129,标识)
        elseif 类型==169 then
                对话数据.对话="想要见到大王，先过我这一关"
                对话数据.选项={"妖孽受死","我只是路过"}
        elseif 类型==170 then
                对话数据.对话="只要吃了唐僧的肉就可以长生不老，哈哈哈"
                对话数据.选项={"妖孽受死","我只是路过"}

    ---------摩托新增齐天大圣副本
        elseif 类型==192 then
                local 副本id=任务数据[标识].副本id
                if 任务数据[标识].名称=="伤心的小猴" and 副本数据.齐天大圣.进行[副本id].进程 == 1
                  and 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id and 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 ~= nil
                  and 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴>0 then
                       对话数据.对话="猴爷爷快要不行了,呜呜呜。"
                       if 玩家数据[数字id].队伍==0 then
                            玩家数据[数字id].角色:添加经验(100000,"伤心的小猴",1)
                            玩家数据[数字id].角色:添加储备(100000,"伤心的小猴",1)
                       else
                            local 队伍id=玩家数据[数字id].队伍
                            for n=1,#队伍数据[队伍id].成员数据 do
                                local 队员id=队伍数据[队伍id].成员数据[n]
                               玩家数据[队员id].角色:添加经验(100000,"伤心的小猴",1)
                               玩家数据[队员id].角色:添加储备(100000,"伤心的小猴",1)
                            end
                       end
                      地图处理类:删除单位(6036,任务数据[标识].编号)
                      副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 - 1
                      任务数据[标识]=nil
                elseif 任务数据[标识].名称=="临死的老猴" and 副本数据.齐天大圣.进行[副本id].进程 == 1
                      and 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 ~= nil
                      and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 > 0 then
                      对话数据.对话="大王,我不行了,以后无法再陪伴大王了。"
                      if 玩家数据[数字id].队伍==0 then
                          玩家数据[数字id].角色:添加经验(100000,"临死的老猴",1)
                          玩家数据[数字id].角色:添加储备(100000,"临死的老猴",1)
                       else
                          local 队伍id=玩家数据[数字id].队伍
                          for n=1,#队伍数据[队伍id].成员数据 do
                            local 队员id=队伍数据[队伍id].成员数据[n]
                             玩家数据[队员id].角色:添加经验(100000,"临死的老猴",1)
                             玩家数据[队员id].角色:添加储备(100000,"临死的老猴",1)
                          end
                       end
                      地图处理类:删除单位(6036,任务数据[标识].编号)
                      副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 = 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 - 1
                      任务数据[标识]=nil
                else
                      对话数据.对话="我好像不是你副本里的人物。"
                end
                if 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id and 副本数据.齐天大圣.进行[副本id].进程 == 1
                    and 副本数据.齐天大圣.进行[副本id].小猴老猴.临死的老猴 <= 0
                    and 副本数据.齐天大圣.进行[副本id].小猴老猴.伤心的小猴 <= 0 then
                    副本数据.齐天大圣.进行[副本id].小猴老猴 = nil
                    副本数据.齐天大圣.进行[副本id].进程=2
                    任务处理类:设置齐天大圣副本(副本id)
                end
                玩家数据[数字id].角色:刷新任务跟踪()
        elseif 类型==193 then
                local 副本id=任务数据[标识].副本id
                if 副本数据.齐天大圣.进行[副本id].进程 == 2 and 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id then
                    对话数据.对话="我等要收走死亡老猴的魂魄,尔等不要挡路！"
                    对话数据.选项={"我看谁敢","我只是路过"}
                else
                    对话数据.对话="我好像不是你副本里的人物。"
                end
        elseif 类型==194 then
                local 副本id=任务数据[标识].副本id
                if 副本数据.齐天大圣.进行[副本id].进程==5 and 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id then
                    对话数据.对话="玉帝天恩浩荡,赦免了您大闹地府之罪,还要授予你官职,且与我一同前往天宫授封吧。"
                    地图处理类:删除单位(6036,任务数据[标识].编号)
                    副本数据.齐天大圣.进行[副本id].进程=6
                    任务数据[标识]=nil
                    地图处理类:跳转地图(数字id,6038,203,133)
                else
                  对话数据.对话="我好像不是你副本里的人物。"
                end
                玩家数据[数字id].角色:刷新任务跟踪()
        elseif 类型==195 then
                对话数据.对话="天庭的马儿真是好啊！"
                对话数据.选项={"找死","我只是路过"}
        elseif 类型==196 then
                local 副本id=任务数据[标识].副本id
                if  副本数据.齐天大圣.进行[副本id].进程==9 and 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id  then
                      对话数据.对话="调皮的小马,快点跟着我回去吧！"
                      地图处理类:删除单位(6038,任务数据[标识].编号)
                      副本数据.齐天大圣.进行[副本id].进程=10
                      任务数据[标识]=nil
                end
                玩家数据[数字id].角色:刷新任务跟踪()
        elseif 类型==197 then
                local 副本id=任务数据[标识].副本id
                if 副本id == 任务数据[玩家数据[数字id].角色:取任务(191)].副本id and 副本数据.齐天大圣.进行[副本id].进程 == 11 then
                      if 任务数据[标识].名称 == "百万天兵" then
                          if 任务数据[标识].战斗==nil then
                            对话数据.对话="大胆泼猴,竟敢违逆天命,还不拿命来！"
                            对话数据.选项={"口出狂言","我只是路过"}
                          else
                            对话数据.对话="我正在战斗中，请勿打扰。"
                          end
                      elseif 任务数据[标识].名称 == "巨灵神" then
                            if 任务数据[标识].战斗==nil then
                              对话数据.对话="大胆泼猴,竟敢违逆天命,还不拿命来！"
                              对话数据.选项={"比划比划","我只是路过"}
                            else
                              对话数据.对话="我正在战斗中，请勿打扰。"
                            end
                      elseif 任务数据[标识].名称 == "李靖" then
                            if 副本数据.齐天大圣.进行[副本id].百万天兵.百万天兵 and 副本数据.齐天大圣.进行[副本id].百万天兵.巨灵神 then
                              副本数据.齐天大圣.进行[副本id].百万天兵 = nil
                              地图处理类:删除单位(任务数据[标识].地图编号,任务数据[标识].编号)
                              任务数据[标识]=nil
                              副本数据.齐天大圣.进行[副本id].进程=12
                              地图处理类:跳转地图(数字id,6039,30,38)
                              玩家数据[数字id].角色:刷新任务跟踪()
                            else
                              对话数据.对话="哼,连我的手下都没有消灭,还想来战我！"
                            end
                      end
                else
                      对话数据.对话="我正在战斗中，请勿打扰。"
                end
        elseif 类型==304 then
                对话数据.对话="如果你的实力已经达到登峰造极的地步，可以尝试挑战我。我乃#R"..任务数据[标识].等级.."级#W，等级大于或小于本地煞星等级20以上的玩家无法对我发起挑战。你是否已经准备好对我发起挑战？"
                对话数据.选项={"我们准备好了","我只是路过"}
        elseif 类型==305 then
                    对话数据.对话="大王叫我来巡山喽#24#R（本活动需要五人组队参加且人物等级必须达到60级）"
                    对话数据.选项={"小小先锋还这么嚣张？讨打！","我只是路过"}
        elseif 类型==306 then
                对话数据.对话="小子你看啥？？"
                对话数据.选项={"我来阻止你","我只是路过"}
        elseif 类型==310 then
                对话数据.对话="我佛慈悲，放下屠刀，立地成佛"
                对话数据.选项={"我放你妹","我只是路过"}
        elseif 类型==315 then
                对话数据.对话="尔等报上名号,今天你将死在你所在的门派"
                对话数据.选项={"你觉得你能保护你所在的门派?找死!","我只是路过"}
        elseif 类型==309 and 数字id==任务数据[标识].玩家id then
                  对话数据.对话="看什么看，信不信我吃了你#24"
                  对话数据.选项={"邪魔休要猖狂","我只是路过"}
        elseif 类型==314 then
                对话数据.对话="奉玉帝旨意，下凡来指点尔等，我乃#R"..任务数据[标识].等级.."级#W，等级大于或小于本天罡星等级20以上的玩家无法对我发起挑战。你是否已经准备好对我发起挑战？"
                对话数据.选项={"请星君赐教","我只是路过"}
        elseif 类型==318 then
                对话数据.对话="四墓灵鼠，不想死的话就回你的鼠洞里!"
                对话数据.选项={"让我送你回鼠洞","我只是路过"}
        elseif 类型==336 then
                对话数据.对话="少侠.我用双手成就你的梦想！"
                对话数据.选项={"瞎子看招","我只是路过"}
        elseif 类型==347 then
                if 取随机数()<=90 then
                    玩家数据[数字id].道具:开启福利宝箱(数字id,标识)
                    return
                else
                    任务数据[标识].战斗=true
                    战斗准备类:创建战斗(数字id+0,100105,标识)
                    return
                end
        elseif 类型==348 then
                对话数据.对话="少侠再来一杯、来嘛、来嘛！"
                对话数据.选项={"喝就喝、谁怕谁","不要嘛、不要嘛、亚麻蝶"}
        elseif 类型==349 then
                对话数据.对话="好吃好吃，快把美食交出来！"
                对话数据.选项={"吃你个猪头啊，看打！","大爷，您稍等，我这就给您买吃的去。"}
        elseif 类型==350 then
                对话数据.对话="@#…￥%#…*&@#）（*）？"
                对话数据.选项={"会不会说人话","没事，我就问问，您忙您的"}
        elseif 类型==351 then
                对话数据.对话="终于可以出来透透气了"
                对话数据.选项={"妖兽看招","路过，路过"}
        elseif 类型==352 and 任务数据[标识].玩家id==数字id and 玩家数据[数字id].驱逐泼猴 then
                对话数据.对话="这里的蟠桃真好吃，多吃点！"
                对话数据.选项={"泼猴看打","路过，路过"}
        elseif 类型==354 and 判断队伍() then
              if 活动次数查询(数字id,"水陆大会")==false then return end
              任务数据[标识].战斗=true
              战斗准备类:创建战斗(数字id+0,100119,标识)
              return
        elseif 类型==355 then
                对话数据.对话="我就是当今病毒的初体...哈哈哈？"
                对话数据.选项={"让我来消灭你","我只是路过"}
        elseif 类型==356 then
                对话数据.对话="终于可以出来透透气了，美味的食物，我来了"
                对话数据.选项={"小小年兽","路过~路过"}
        elseif 类型==357 then
                对话数据.对话="终于可以出来透透气了，美味的食物，我来了"
                对话数据.选项={"打倒年兽","路过~路过"}
        elseif 类型==358 then
                对话数据.对话="是你们肆意的猎杀我的伙伴的，杀~杀~杀"
                对话数据.选项={"邪恶年兽休要猖狂","路过~路过"}
        elseif 类型==359 then
                对话数据.对话="这里可是好地方哇,以后我就长居凡界了#24。"
                对话数据.选项={"我来瞧瞧你的啥","我只是路过"}
        elseif 类型==360 then
                对话数据.对话="看什么看，没见过这么嚣张的么！#24#R（本活动需要3-5人组队参加且人物等级必须达到50级）"
                对话数据.选项={"我看你是找打","我只是路过"}
        elseif 类型==212 and 判断标识(212,1) then
                对话数据.对话="新春快乐,这里好好玩一起来玩啊"
                对话数据.选项={"小乖乖赶紧回家","我只是路过"}
        elseif 类型>=361 and 类型<=366 then
                对话数据.对话="可恶啊~竟然被逮到了#24#R（本活动需要3-5人组队参加且人物等级必须达到69级）"
                对话数据.选项={"对对对。","我只是路过"}
        elseif 类型==367 then
                对话数据.对话="玉帝派我等下凡赐福，若你是我所寻之人，我便赐你仙缘助你一臂之力。"
                对话数据.选项={"请星官赐教","我只是路过"}
        elseif 类型==368 then
               对话数据.对话="大胆少年，竟敢偷我宝贝？#24#R（本活动需要等级要求为60至89级）"
               对话数据.选项={"影青龙，我这就来干死你！","错了错了，我溜。"}
        elseif 类型==369 then
               对话数据.对话="大胆少年，竟敢偷我宝贝？#24#R（本活动需要等级要求为90至129级）"
               对话数据.选项={"影朱雀，我这就来干死你！","错了错了，我溜。"}
        elseif 类型==370 then
               对话数据.对话="大胆少年，竟敢偷我宝贝？#24#R（本活动需要等级要求为130至145级）"
               对话数据.选项={"影白虎，我这就来干死你！","错了错了，我溜。"}
        elseif 类型==371 then
               对话数据.对话="大胆少年，竟敢偷我宝贝？#24#R（本活动需要等级要求为146至155级）"
               对话数据.选项={"影玄武，我这就来干死你！","错了错了，我溜。"}
        elseif 类型==372 then
               对话数据.对话="大胆少年，竟敢偷我宝贝？#24#R（本活动需要等级要求为155至175级）"
               对话数据.选项={"影麒麟，我这就来干死你！","错了错了，我溜。"}
        elseif 类型==373 then
                对话数据.对话="我躲吖躲...咿...少侠是怎么找到我的？"
                对话数据.选项={"宝物给我拿过来","我只是路过"}
        elseif 类型==374 and 判断标识(374,1) then
                对话数据.对话="三千浮屠，六千烦恼，且看我，醉得它平生自在逍遥，少侠可与我痛饮一番？"
                对话数据.选项={"乐意之极","抱歉，我还有要事在身"}
        elseif 类型>=375 and 类型<=384 and 数字id==任务数据[标识].玩家id then --宝图强盗
                对话数据.对话="酒里有乾坤，酒里做神仙，难得糊涂，难得糊涂。#89"
                对话数据.选项={"交出仙缘(战斗)","交出银两放你一马！","你让我想想"}
        elseif 类型==385 then
                对话数据.对话="龙行飘忽，所谓神龙见首不见尾，龙脉之地也是神秘万分，少侠可愿前取寻找龙脉所在？"
                对话数据.选项={"让我来收服你","我只是路过"}
        elseif 类型==386 then
              对话数据.对话="此山是我开，此路是我栽，要想从此过，留下买路财！"
              对话数据.选项={"桐人，挡我一下试试！","对不起，打扰了。"}
        elseif 类型==387 then
              对话数据.对话="此山是我开，此路是我栽，要想从此过，留下买路财！"
              对话数据.选项={"魔化铜人，挡我一下试试！","对不起，打扰了。"}
        elseif 类型==388 then
              对话数据.对话="此山是我开，此路是我栽，要想从此过，留下买路财！"
              对话数据.选项={"混世魔王，挡我一下试试！","对不起，打扰了。"}
        elseif 类型==389 then
                对话数据.对话="有能力你就来抓我啊...哈哈哈？"
                对话数据.选项={"我看你是找打","我只是路过"}
        elseif 类型 >= 390 and 类型<= 397 then ---嘉年华
              对话数据 = 嘉年华:怪物对话内容(数字id,类型,标识,地图)
        elseif 类型 ==6674 or 类型 ==1159 then
               对话数据 = 彩虹争霸:怪物的对话(数字id,序列,标识,地图)
        elseif 类型 >= 1312 and 类型<= 1315 then
              对话数据 =  长安保卫战:怪物对话内容(数字id,类型,标识,地图)
        elseif 类型==398 then
                对话数据 = 归墟活动:对话内容(数字id,类型,标识,地图)
        elseif 类型==399 then
                对话数据.对话="2025新春快乐，我是新年使者.遇到我祝你2025年新春快乐！蛇年大吉！平安顺利！"
                对话数据.选项={"新年快乐！蛇年大吉！","我只是路过"}
        elseif 类型==5594 and 判断队伍() then
                对话数据.对话="帮战时间到了,需要进入准备区域么"
                对话数据.选项={"进入战备区域","路过~路过"}
        elseif 类型==5595 and 判断队伍() then
                对话数据.对话="杀戮时间到了,你需要进入战场么"
                对话数据.选项={"进入帮战战场","路过~路过"}
        elseif 类型==5596 then
                if 玩家数据[数字id].拾取帮战宝箱==nil then 玩家数据[数字id].拾取帮战宝箱 = 0 end
                if 自定义数据.帮战宝箱拾取数量==nil or 自定义数据.帮战宝箱拾取数量<=1 then 自定义数据.帮战宝箱拾取数量=1 end
                if 玩家数据[数字id].拾取帮战宝箱 >=自定义数据.帮战宝箱拾取数量 then
                    常规提示(数字id,"#Y每人最多拾取"..自定义数据.帮战宝箱拾取数量.."个宝箱")
                else
                    帮战活动类:开启福利宝箱(数字id,标识)
                    玩家数据[数字id].拾取帮战宝箱 = 玩家数据[数字id].拾取帮战宝箱 + 1
                end
                return
        elseif 类型==5597 then
              对话数据.对话="帮战结束了,是要出去么"
              对话数据.选项={"送我出去","路过~路过"}
        elseif 类型>=7000 and 类型<=7500 then --7000-7500是留给副本的ID，每个副本给50个ID
              副本处理类:地图单位对话(连接id,数字id,标识)
              return
        end
        if 对话数据 and type(对话数据)=="table" and 对话数据.模型 and 对话数据.名称 then
            玩家数据[数字id].地图单位.对话={模型=对话数据.模型,名称=对话数据.名称}
            玩家数据[数字id].最后对话={模型=对话数据.模型,名称=对话数据.名称}
            发送数据(连接id,1501,对话数据)
        end
end


return 活动内容




