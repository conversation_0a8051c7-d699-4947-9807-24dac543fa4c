--======================================================================--
local 对话处理类 = class()
local 活动内容= require("script/对话处理类/活动内容")()
function 对话处理类:初始化()end

function 对话处理类:索取对话内容(id,数字id,序号,内容)
	local 临时清空={"科举对话","解除师徒","喂养坐骑","制作家具","好友分组","抽中编号","升级角色","仓库操作",
					"提取词条","查看辰星","宝宝降级","补充灵气id","神兽天生操作","遗忘剧情技能","角色经脉操作","神兽成长提升",
					"宝宝认证技能","更换宝宝五行","内丹自选礼包","神兽自选礼包","更换模型进阶","设置飞行快捷键",
					"神兽成长资质提升","变异召唤兽添加格子"}
    for i,v in ipairs(临时清空) do
    	玩家数据[数字id][v] = nil
    end
	神秘宝箱[数字id] = nil
	if 玩家数据[数字id].自动遇怪  or 玩家数据[数字id].摊位数据 or 玩家数据[数字id].交易信息 or 交易数据[数字id] then
		发送数据(id,7,"#Y/当前状态不可使用该功能")
		return 0
	end
    if not 玩家数据[数字id].角色.数据.地图数据 or 玩家数据[数字id].角色.数据.地图数据.编号==1003 then
		常规提示(数字id,"#Y该地图禁止使用该功能")
		return
	end
	local 地图 = 玩家数据[数字id].角色.数据.地图数据.编号
	if 内容.地图+0~=地图 then
		常规提示(数字id,"#Y/您距离这个npc太远了")
		return
	end
	if not 内容.编号 or not tonumber(内容.编号) then return end
	local 编号 = 内容.编号+0
	local 标识 = 内容.标识
	if not 地图处理类.NPC列表[地图] and not 地图处理类.地图单位[地图] then return end
	if not 地图处理类.NPC列表[地图][编号] and not 地图处理类.地图单位[地图][编号] then return end
	local NPC数据 = {}
	local 活动单位 = false
	if 地图处理类.NPC列表[地图][编号] then
		NPC数据 =地图处理类.NPC列表[地图][编号]
	elseif 地图处理类.地图单位[地图][编号] then
			if not 标识 or 地图处理类.地图单位[地图][编号].id~=标识 then
				-- print("依然报错地图编号"..地图.."NPC编号"..内容.编号.."操作玩家ID"..数字id)
				-- 发送数据(id,998,玩家数据[数字id].账号)
				-- 发送数据(id,999,玩家数据[数字id].账号)
				return
		    end
		    if not 任务数据[标识] then return end
			NPC数据 = 地图处理类.地图单位[地图][编号]
			活动单位 = true
	end
	if not NPC数据 or not NPC数据.名称 or not NPC数据.x or not NPC数据.y then return end
	local 角色xy ={x=0,y=0}
	local 对方xy ={x=0,y=0}
	对方xy.x,对方xy.y=NPC数据.x,NPC数据.y
    角色xy.x,角色xy.y=玩家数据[数字id].角色.数据.地图数据.x/20,玩家数据[数字id].角色.数据.地图数据.y/20
    if 取两点距离(角色xy,对方xy)>=500 then
		常规提示(数字id,"#Y/您距离这个npc太远了")
		return
	end
	if 活动单位 then
		活动内容:地图单位对话(id,数字id,编号,标识,地图)
	    return
	else
		玩家数据[数字id].地图单位=nil
		if 玩家数据[数字id].角色:取任务(212)~=0 then
			local 任务id = 玩家数据[数字id].角色:取任务(212)
			if 任务数据[任务id].分类==3 and NPC数据.名称==任务数据[任务id].名称 and 地图==任务数据[任务id].地图 then
				if not 取队长权限(数字id) then 常规提示(数字id,"#Y/你不是队长") return end
				任务处理类:完成春节任务(数字id)
				玩家数据[数字id].最后对话={模型=NPC数据.模型,名称=NPC数据.名称,编号=编号}
                发送数据(id,1501,{模型=NPC数据.模型,名称=NPC数据.名称,对话="你的来意我已收到。"})
				return
			end
        end
         if 玩家数据[数字id].角色:取任务(390)~=0  then---嘉年华
         		local 对话数据 = 嘉年华:NPC对话内容(数字id,NPC数据.名称,地图,玩家数据[数字id].角色:取任务(390))
         		if 对话数据.对话 then
         		    对话数据.模型 = NPC数据.模型
         		    对话数据.名称 = NPC数据.名称
         		    玩家数据[数字id].最后对话={模型=NPC数据.模型,名称=NPC数据.名称,编号=编号}
                    发送数据(id,1501,对话数据)
                    return
                end
        end
        local 返回对话 = self:取对话内容(地图,编号,数字id)
        if not 返回对话 then
         	发送数据(id,1501,{模型=NPC数据.模型,名称=NPC数据.名称,对话="你是谁!你要干什么"})
         	return
        elseif type(返回对话)=="table" then
         	if not 返回对话[1] then 返回对话[1] = NPC数据.模型 end
         	if not 返回对话[2] then 返回对话[2] = NPC数据.名称 end
         	if not 返回对话[3] then 返回对话[3] = "你是谁!你要干什么" end
         	if not 返回对话[4] then 返回对话[4] = {} end
         	self:增加对话处理(返回对话,数字id,地图)
         	玩家数据[数字id].最后对话={模型=返回对话[1],名称=返回对话[2],编号=编号}
         	发送数据(id,1501,{模型=返回对话[1],名称=返回对话[2],对话=返回对话[3],选项=返回对话[4]})
         	if 玩家数据[数字id].队伍 and 队伍数据[玩家数据[数字id].队伍] and 玩家数据[数字id].队长 and (返回对话[2]=="马副将" or 返回对话[2]=="御林军" or 返回对话[2]=="赵捕头" or 返回对话[2]=="酒店老板" or 返回对话[2]=="袁天罡") then
         	   for i,v in ipairs(队伍数据[玩家数据[数字id].队伍].成员数据) do
         	   		if v~=数字id and 玩家数据[v] then
         	   			玩家数据[v].最后对话={模型=返回对话[1],名称=返回对话[2],编号=编号}
		            	发送数据(玩家数据[v].连接id,1501,返回对话)
         	   		end
         	   end
         	end
        else
         	发送数据(id,1501,{模型=NPC数据.模型,名称=NPC数据.名称,对话="你是谁!你要干什么"})
        end
	end

end


function 对话处理类:增加对话处理(返回对话,数字id,地图)
		local 临时任务={13,110,212,346,111,112,307,301,302,308,150}
		for i,v in ipairs(临时任务) do
			local 任务id = 玩家数据[数字id].角色:取任务(v)
			if 任务id and 任务id ~=0 and 任务数据[任务id] then
				if v==13 and 返回对话[2]==任务数据[任务id].名称 and 地图==任务数据[任务id].地图编号 then
					if 任务数据[任务id].分类 and 任务数据[任务id].分类>=11 and 任务数据[任务id].分类<=15 then
						table.insert(返回对话[4],"宠物修炼任务")
						玩家数据[数字id].最后操作="宠修操作"
					end
				elseif v==110 and 返回对话[2]=="李将军" and (任务数据[任务id].分类==3 or 任务数据[任务id].分类==4) then
					table.insert(返回对话[4],"上交物品")
					玩家数据[数字id].最后操作="官职物品"
				elseif v==212 and 返回对话[2]=="福禄童子" and 任务数据[任务id].分类==2 then
						table.insert(返回对话[4],"上交物品")
						玩家数据[数字id].最后操作="新春任务"
			    elseif v==346 and 返回对话[2]=="袁天罡" then
			    		table.insert(返回对话[4],"给予侠义任务物品")
			    elseif v==111 then
			          if 任务数据[任务id].分类==1 and 返回对话[2]==任务数据[任务id].人物 and  地图==任务数据[任务id].人物地图 then
			    		 任务处理类:完成门派任务(数字id,1)
			    		 返回对话[3]="你师傅的信我已经收到了，真是太谢谢你了。"
			    		 返回对话[4]={}
			    	  elseif 任务数据[任务id].分类==3 and 任务数据[任务id].门派师傅==返回对话[2] then
			    	  			table.insert(返回对话[4],"上交召唤兽")
								玩家数据[数字id].最后操作="师门召唤兽"
					  elseif 任务数据[任务id].分类==4 and 任务数据[任务id].门派师傅==返回对话[2] then
			    	  			table.insert(返回对话[4],"上交物品")
								玩家数据[数字id].最后操作="师门物品"
					  elseif 任务数据[任务id].分类==7 and 任务数据[任务id].乾坤袋 and 任务数据[任务id].门派师傅==返回对话[2] then
			    	  			table.insert(返回对话[4],"上交乾坤袋")
								玩家数据[数字id].最后操作="师门乾坤袋"
					  end
					  if 任务数据[任务id] and 任务数据[任务id].门派师傅==返回对话[2] then
					  		table.insert(返回对话[4],"取消师门任务")
					  end
                elseif v==112 then
                 		  if 任务数据[任务id].分类==1 and 返回对话[2]==任务数据[任务id].人物 and  地图==任务数据[任务id].人物地图 then
				    		 任务处理类:完成文韵任务(数字id,1)
				    		 返回对话[3]="文韵使者的信我已经收到了，真是太谢谢你了。"
				    		 返回对话[4]={}
				    	  elseif 任务数据[任务id].分类==3 and 任务数据[任务id].门派师傅==返回对话[2] then
				    	  			table.insert(返回对话[4],"上交文韵墨香召唤兽")
									玩家数据[数字id].最后操作="文韵召唤兽"
						  elseif 任务数据[任务id].分类==4 and 任务数据[任务id].门派师傅==返回对话[2] then
				    	  			table.insert(返回对话[4],"上交文韵墨香物品")
									玩家数据[数字id].最后操作="文韵物品"
						  elseif 任务数据[任务id].分类==7 and 任务数据[任务id].乾坤袋 and 任务数据[任务id].门派师傅==返回对话[2] then
				    	  			table.insert(返回对话[4],"上交文韵墨香乾坤袋")
									玩家数据[数字id].最后操作="文韵乾坤袋"
						  end
       			elseif v==307 then
       					if 任务数据[任务id].分类==2 and 返回对话[2]=="太白金星" then
       						table.insert(返回对话[4],"给予烹饪")
							玩家数据[数字id].最后操作="坐骑任务烹饪"
 						elseif 任务数据[任务id].分类==11 and 返回对话[2]=="大大王" then
 								table.insert(返回对话[4],"给予药品")
								玩家数据[数字id].最后操作="坐骑任务药品"
						end
				elseif v==301  then
						if 任务数据[任务id].分类==1 and 返回对话[2]==任务数据[任务id].人物 and 地图==任务数据[任务id].人物地图 then
					        返回对话[3]="贵帮的青龙堂主的信我已经收到了，真是太谢谢你了。"
					        任务处理类:完成青龙任务(任务id,数字id,1)
					    elseif 任务数据[任务id].分类==2 and 任务数据[任务id].帮派总管==返回对话[2] then
						        table.insert(返回对话[4],"给予药品")
						        玩家数据[数字id].最后操作="帮派青龙药品"
					    elseif 任务数据[任务id].分类==3 and 任务数据[任务id].帮派总管==返回对话[2] then
						        table.insert(返回对话[4],"给予烹饪")
						        玩家数据[数字id].最后操作="帮派青龙烹饪"
					    end
				elseif v==302 then
						if 任务数据[任务id].分类==1 and 返回对话[2]==任务数据[任务id].人物 and 地图==任务数据[任务id].人物地图 then
					        返回对话[3]="贵帮的玄武堂主的信我已经收到了，真是太谢谢你了。"
		        			任务处理类:完成玄武任务(任务id,取id组(数字id),1)
					    elseif 任务数据[任务id].分类==2 and 任务数据[任务id].帮派总管==返回对话[2] then
					        table.insert(返回对话[4],"给予药品")
					        玩家数据[数字id].最后操作="帮派玄武药品"
					    elseif 任务数据[任务id].分类==3 and 任务数据[任务id].帮派总管==返回对话[2] then
					        table.insert(返回对话[4],"给予烹饪")
					        玩家数据[数字id].最后操作="帮派玄武烹饪"
					    end
				elseif v==308 then
						if 任务数据[任务id].分类==1 and 返回对话[2]==任务数据[任务id].人物 and 地图==任务数据[任务id].人物地图 then
					        返回对话[3]="少侠是不是在为了合成法宝的材料而烦恼了，我这里正好有你需要的东西，送给你了。"
		        			任务处理类:完成法宝任务(任务id,数字id,1)
		        			if 玩家数据[数字id].角色.法宝进程==1 then
		        				返回对话[1] ="男人_道童"
		        				返回对话[2] ="金童子"
		        				返回对话[3] = "少侠已经集齐法宝合成的材料了，可以来天宫找我领取内丹任务进行法宝合成哦"
		        				返回对话[3] = {}
		        			end
					    elseif 任务数据[任务id].分类==2 and 任务数据[任务id].法宝NPC==返回对话[2] then
					        table.insert(返回对话[4],"给予法宝需求药品")
					        玩家数据[数字id].最后操作="法宝合成药品"
					    elseif 任务数据[任务id].分类==3 and 任务数据[任务id].法宝NPC==返回对话[2] then
					        table.insert(返回对话[4],"给予法宝需求烹饪")
					        玩家数据[数字id].最后操作="法宝合成烹饪"
					    elseif 任务数据[任务id].分类==4 and 返回对话[2]==任务数据[任务id].人物 and 地图==任务数据[任务id].人物地图 then
					    		返回对话[3]="看什么看，再看小心我撞你。"
		       					返回对话[4]={"欠收拾是吧！","只是路过"}
					    end
                elseif v==150 then
                		local 副本id = 任务数据[任务id].副本id
                		if 玩家数据[数字id].角色:取任务(353)~=0 then
                			local 任务id1 = 玩家数据[数字id].角色:取任务(353)
                			if 返回对话[2]==任务数据[任务id1].人物 and 地图==任务数据[任务id1].人物地图 then
                				返回对话[3]="水陆大会的邀帖我已经收到了，我一定会准时去参加的，真是太谢谢你了。"
                				任务数据[任务id].邀请=任务数据[任务id].邀请+5
					            副本数据.水陆大会.进行[副本id].邀请=副本数据.水陆大会.进行[副本id].邀请+5
					            常规提示(数字id,"#Y完成了邀请，邀请任务进度+5")
						        玩家数据[数字id].角色:取消任务(任务id1)
						        if 副本数据.水陆大会.进行[副本id].邀请>=10 then
						        	for i,v in pairs(地图处理类.地图单位[6024]) do
						        		if v.名称 == "蟠桃树" and 任务数据[v.id].副本id == 副本id then
						        			地图处理类:删除单位(6024,i)
						        			break
						        		end
						        	end
							        副本数据.水陆大会.进行[副本id].进程=2
							        任务处理类:设置水陆大会副本(副本id)
							        返回对话[1] ="男人_方丈"
		        					返回对话[2] ="道场督僧"
							        返回对话[3]="感谢少侠为水陆大会建设做出的贡献，道场已经建设完毕"
		       						返回对话[4]={}
							    end
						        玩家数据[数字id].角色:刷新任务跟踪()
                			end
                			return
                		end
                		if 副本数据.水陆大会.进行[副本id].进程==5 and 地图==6025 then
                			if 返回对话[2]==副本数据.水陆大会.进行[副本id].人物[1].名称 and not 副本数据.水陆大会.进行[副本id].人物[1].找到 then
                				返回对话[3]="南无阿弥陀佛。施主能识得我的真身，看来与我佛有缘啊。"
			        			返回对话[4]={"我来领取佛祖法宝<领取袈裟>","拜见观音菩萨"}
                			elseif 返回对话[2]==副本数据.水陆大会.进行[副本id].人物[2].名称 and not 副本数据.水陆大会.进行[副本id].人物[2].找到 then
                					返回对话[3]="南无阿弥陀佛。施主能识得我的真身，看来与我佛有缘啊。"
			        				返回对话[4]={"我来领取佛祖法宝<领取锡杖>","拜见观音菩萨"}
                			end
                		end
				end
			end
		end

end




function 对话处理类:取对话内容(ID,编号,数字id)
	if ID>=10000 and ID<=10018 then
	    return 彩虹争霸:NPC对话内容(ID,编号,数字id)
	end
	local wb = {}
	local xx = {}
	if ID == 1003 then--桃园
		-- if 编号 == 20 then
		-- 	-- if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 >= 1 or 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 >= 1 or 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 >= 1 then
		-- 	wb[1] = "欢迎来到#P梦幻吊游#W这是你没有玩过的#S全新版本,仿官方设置经典耐玩,#Y不伤肝\n全网#R战斗细节#W最好,#G打击感#W超棒,独家补全了所有#G音效,#Y特效,#S造型\n全官方#Z剧情流程#W好玩到爆炸,更多游戏内容,还请您深入体验"
		-- 	--xx = {"我想要领取新手福利","我要领法系宝宝（无限领取）","我要领物理系宝宝（无限领取）","重新剧情任务需点一下","取消桃园剧情系列任务","取消建邺剧情系列任务","取消建玄奘情系列任务"}
		-- 	--xx = {"我想要领取新手福利","领取商人鬼魂剧情系列任务","取消商人鬼魂剧情系列任务","取消玄奘剧情系列任务"}
		-- 	--xx = {"我想要领取新手福利","领取玄奘剧情系列任务","取消商人鬼魂剧情系列任务","取消玄奘剧情系列任务"}
		-- 	xx = {"我想要领取新手福利","我要领新手宝宝（无限领取）",}
		-- 	return{"进阶芙蓉仙子","新手接待师",wb[取随机数(1,#wb)],xx}
		-- --end
		-- -- wb[1] = "欢迎来到#P梦幻吊游#W这是你没有玩过的#S全新版本\n仿官方设置经典耐玩,#Y不伤肝\n全网#R战斗细节#W最好,#G打击感#W超棒\n独家补全了所有#G音效,#Y特效,#S造型\n全官方#Z剧情流程#W好玩到爆炸\n更多游戏内容,还请您深入体验"
		-- -- 	xx = {"剧情任务补领"}
		-- 	--return{"猫灵人形","新手接待师",wb[取随机数(1,#wb)],xx}
		-- if 编号 == 1 then
		-- 	wb[1] = "少不入川，老不出蜀。桃源村可绝对不比那里差多少。少侠年纪轻轻，还是应该闯荡一番。"
		-- 	return{"男人_苦力","夏大叔",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 2 then
		-- 	wb[1] = "厦大叔说很久很久以前，后山下面压了一个猴子好多好多年。后来，后来的事我忘记了。"
		-- 	return{"女人_绿儿","窑窑",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 3 then
		-- 	wb[1] = "什么时候才能像姐姐那么大啊。"
		-- 	return{"女人_绿儿","彤彤",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 4 then
		-- 	wb[1] = "熙熙攘攘都是过客，来来往往都是归人。"
		-- 	return{"女人_赵姨娘","萍儿",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 5 then
		-- 	wb[1] = "桃源村的桃花潭，潭水清澈，美丽动人，这桃花潭，是我最爱的游湖之地，少侠若是有兴趣，可以在这桃花潭游玩一番。"
		-- 	return{"花妖","桃园仙女",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 6 then
  --       	if 玩家数据[数字id].角色:取任务(401) == 0 and (支线奖励[数字id]==nil or 支线奖励[数字id].狸猫奖励==nil) then
		-- 	wb[1] = "少侠可愿帮我把这3只狸赶走呢？"
		-- 	xx = {"帮帮郭大哥","没空"}
		-- 	return{"男人_店小二","郭大哥",wb[取随机数(1,#wb)],xx}
		-- 	end
		-- 	if 玩家数据[数字id].角色:取任务(401) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(401)].进程 == 1 then
		-- 	wb[1] = "少侠救命啊~帮我教训一下3只狸猫"
  --           return{"男人_店小二","郭大哥",wb[取随机数(1,#wb)],xx}
		-- 	end
		-- 	if 支线奖励[数字id].狸猫奖励==true then
		-- 	wb[1] = "多谢少侠相助"
		-- 	xx = {"领取报酬","再见"}
  --           return{"男人_店小二","郭大哥",wb[取随机数(1,#wb)],xx}
		-- 	end
		-- 	wb[1] = "多谢少侠相助"
  --           return{"男人_店小二","郭大哥",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 7 then
  --       	if 玩家数据[数字id].角色:取任务(401) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(401)].进程 == 1 then
		-- 	wb[1] = "你傻逼吗,要打架去找我老大"
		--     else
		--     wb[1] = "#119"
		--     end
		-- 	return{"狸","狸",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 8 then
		-- 	if 玩家数据[数字id].角色:取任务(401) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(401)].进程 == 1 then
		-- 	wb[1] = "你傻逼吗,要打架去找我老大"
		--     else
		--     wb[1] = "#119"
		--     end
		-- 	return{"狸","狸",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 9 then
		-- 	if 玩家数据[数字id].角色:取任务(401) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(401)].进程 == 1 then
		-- 	wb[1] = "我就是狸老大,要干架,就来吧!#4"
		-- 	xx = {"开启剧情战斗"}
		--     else
		--     wb[1] = "#119"
		--     end
		-- 	return{"狸","狸",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 10 then
		-- if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 6 then
		-- wb[1] = "不用说了,我知道你的来意\n不就是想要把武器嘛,没问题\n这是我#Y祖传兵器#拿去用吧\n听说这吧兵器当年可是\n#G上古大神#使用过\n封印#R蚩尤#的神器哦\n不用谢我,我是活雷锋#1"
		-- xx = {"传家宝还外送的?"}
		-- return{"男人_村长","谭村长",wb[取随机数(1,#wb)],xx}
		-- else
		-- wb[1] = "桃源村随时欢迎你回来。"
		-- return{"男人_村长","谭村长",wb[取随机数(1,#wb)],xx}
		-- end
  --       elseif 编号 == 11 then
		-- if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 5 then
		-- wb[1] = "这位吊毛,看你面色难看,应堂发黑\n你应该#R命不久矣#呀!\n不过没事我这里有点#G延年续命的药物\n拿去食用吧,保证不会拉肚子的#1"
		-- xx = {"这尼玛不会是兽医吧"}
		-- return{"男人_药店老板","玄大夫",wb[取随机数(1,#wb)],xx}
		-- else
		-- wb[1] = "早睡早起，方能养生，这句老掉牙的话，真的很有用的。"
		-- return{"男人_药店老板","玄大夫",wb[取随机数(1,#wb)],xx}
		-- end
  --       elseif 编号 == 12 then
		-- 	wb[1] = "这三节的山水奇景，还没有我雨画师没画过的。"
		-- 	return{"男人_老书生","雨画师",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 13 then
		-- if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 4 then
		-- wb[1] = "这位客官看起来挺精致的嘛\n今天有免费的#G包子#可以领取哦\n只需要对我说一声:\n#S孙厨娘你好漂亮,我好喜欢你"
		-- xx = {"我才不会说呢"}
		-- return{"女人_染色师","孙厨娘",wb[取随机数(1,#wb)],xx}
		-- else
		-- wb[1] = "烹饪这种东西，要用心去体会，其实不体会也没什么关系，烹饪技能修炼好就行了。"
		-- return{"女人_染色师","孙厨娘",wb[取随机数(1,#wb)],xx}
		-- end
  --       elseif 编号 == 14 then
  --       	if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 3 then
  --       	wb[1] = "回忆看完了吧,现在的你已经失去#Y神力\n和凡人一样了,你需要自己#G重新修炼\n方可重获力量,出门在外的没有一点防具怎么行呢\n送你一套#S装备#W吧\n#G温馨小提示:#RF9#W可以屏蔽其他玩家哦"
		--     xx = {"好的谢谢"}
		--     return{"女人_翠花","刘大婶",wb[取随机数(1,#wb)],xx}
  --       		else
		-- 	wb[1] = "女大不中留，现在心思都不和我说了。"
		-- 	return{"女人_翠花","刘大婶",wb[取随机数(1,#wb)],xx}
		-- end
  --       elseif 编号 == 15 then
		-- 	wb[1] = "嘘，我们在玩捉迷藏呢。"
		-- 	return{"小毛头","小绿",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 16 then
		-- 	wb[1] = "爷爷说他一辈子也没出国村子，其实我是很想出去的，只是可惜我是个女儿家。"
		-- 	return{"小丫丫","清清",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 17 then
  --       if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 7 then
		-- wb[1] = "你不是想找人干架吗?\n我这里有一只#G大野猪\n专门给你这种想干架的人练手的\n不过如果你被野猪#S打死了\n我可不管哦#17"
		-- xx = {"开启剧情战斗"}
		-- return{"男人_武器店老板","孙猎户",wb[取随机数(1,#wb)],xx}
		-- else
		-- wb[1] = "在卖一车兽皮，就可以换一把光武了。"
		-- return{"男人_武器店老板","孙猎户",wb[取随机数(1,#wb)],xx}
		-- end
  --       elseif 编号 == 18 then
		-- 	wb[1] = "看什么看，再看小心我撞你\n休想打败我!"
		-- 	return{"野猪","野猪",wb[取随机数(1,#wb)],xx}
  --       elseif 编号 == 19 then
		-- 	if 玩家数据[数字id].角色:取任务(999) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(999)].进程 == 2 then
		-- 	wb[1] = "哎呀这不是#S/小吊毛#W/吗？我在这里恭候你多时了\n五百年了你终于从#G其他梦幻游戏#W跑来玩#P吊游#W了\n五百年前你曾经是#P吊游#W的#Y上古大神#W因封印蚩尤耗尽神力(#S肝力#W)\n跑去玩了其他梦幻,不过现在你又回来了!\n让我来帮你回忆一下五百年前那场惊心动魄#X封印蚩尤之战吧\n#Y建议使用#G1280*800#W的分辨率进行这次战斗!#1"
		--     xx = {"观看回忆","跳过观看回忆(建议别跳过,非常精彩的战斗)"}
		--     return{"女人_丫鬟","霞姑娘",wb[取随机数(1,#wb)],xx}
		--     else
		--     wb[1] = "常来看看吧，小桥流水炊烟都不会变的，可是我想知道你以后会变成什么样。"
		-- 	return{"女人_丫鬟","霞姑娘",wb[取随机数(1,#wb)],xx}
		--   end

		-- end
	elseif ID == 1501 then--建邺城
		if 编号 == 1 then
		--	xx = {"我要更换当前的宠物","我想要领取新手福利"}
			xx = {"领取新手礼包","我要更换当前的宠物"}
			return{"普陀_接引仙女","宠物仙子","每个宠物都有它特定的属性，有时候宠物甚至决定一只召唤兽的好坏",xx}
		elseif 编号 == 2 then
			wb[1] = "好消息！最近老夫请到了京城梨园的名角来献艺，机会难得，各位千万不要错过了！"
			wb[2] = "听戏可是人生的一大乐趣啊~#51"
			wb[3] = "我们是远近闻名的草台班子，南腔北调昆腔越剧流行歌曲你想听什么都有"
			wb[4] = "真是太平盛世啊，老百姓衣食无忧，闲暇时间还是多听听戏吧。"
			wb[5] = "一到戏台开演的日子，建邺城总是万人空巷。"
			xx = {"购买戏票（100副本积分）","查询剩余看戏次数","我要听一听一斛珠"}
			if 玩家数据[数字id].角色:取任务(7001) ~= 0 then
				xx = {"购买戏票（100副本积分）","查询剩余看戏次数","我要听一听一斛珠","送我进去听戏吧"}
			end
			return{"男人_老伯","戏班老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 1 then
			wb[1] = "如果说我现在说的是假话，你相信么？"
			wb[2] = "周猎户最喜欢和我拉家常了，他家就住在药店边上。"
			wb[3] = "嘘——千万不要告诉他们我爱吹牛啊，这会影响我的形象的！"
			wb[4] = "人生最痛快的事情莫过于和周猎户一起喝点小酒了！"
			wb[5] = "天上有只牛在飞，一定是我在地上吹。"
			xx ={"询问虎子下落","取消"}
			return{"男人_苦力","吹牛王",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "如果说我现在说的是假话，你相信么？"
			wb[2] = "周猎户最喜欢和我拉家常了，他家就住在药店边上。"
			wb[3] = "嘘——千万不要告诉他们我爱吹牛啊，这会影响我的形象的！"
			wb[4] = "人生最痛快的事情莫过于和周猎户一起喝点小酒了！"
			wb[5] = "天上有只牛在飞，一定是我在地上吹。"
			return{"男人_苦力","吹牛王",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			return{"剑侠客",服务端参数.名称,"你可,以在我这里领取推广奖励哟。同时由管理员赠送的银子和储备也可以在我这里领取哟",
			{"领取推广奖励","领取银子","领取储备","领取60级新手奖励","领取90级新手奖励"}}

		elseif 编号 == 5 then
			wb[1] = "上面粉蒸出来的包子，客官想要几个。"
			wb[2] = "建邺虽小，可样样齐全，什么都有#2货栈边上那个王大嫂做的烤鸭真是好吃。"
			wb[2] = "包子有肉不在褶上,肚里有货不在嘴上。"
			xx = {"购买","我什么也不想做"}
			return{"小孩_飞儿","飞儿",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "我的工作就是收取厌倦尘世生活的玩家性命，如果你不再留恋这个美好的世界，你的一切都将在我这里终结，请认真做出你的选择，你的朋友、亲人将再也见不到你，你永远只存在他们的记忆当中"
			xx = {"安全起见不给删号了"}
			--xx = {"我已经想清楚了","我保留意见"}
			return{"马面","勾魂马面",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 3 then
		wb[1] = "你想要我做的烤鸭?\n我的烤鸭可是#G花钱也买不到的\n不过你可以帮我一个吗?我免费给你做一只\n我的孩子病了,需要#Y熊胆#做药引子\n如果你帮我找来熊胆,我可以把#S烤鸭#给你#3"
		xx = {"妈的熊胆可是要2500两银子"}
		return{"女人_王大嫂","王大嫂",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 4 then
		wb[1] = "你找到#Y熊胆#了吗,如果找到了就交予我吧#1"
		xx = {"上交熊胆"}
		return{"女人_王大嫂","王大嫂",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(400) == 0 and (支线奖励[数字id]==nil or 支线奖励[数字id].虎子奖励==nil) then
		wb[1] = "城北的海岸常有大风浪，岸边那艘沉船，也不知道是哪个年月遇到大风浪沉在那里的"
		xx = {"新枯萎的金莲(支线)"}
		return{"女人_王大嫂","王大嫂",wb[取随机数(1,#wb)],xx}
        end
        if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 12 then
		wb[1] = "虎子没事啦!!!"
		xx = {"给王大嫂讲讲事情的经过"}
		return{"女人_王大嫂","王大嫂",wb[取随机数(1,#wb)],xx}
        end
		wb[1] = "城北的海岸常有大风浪，岸边那艘沉船，也不知道是哪个年月遇到大风浪沉在那里的"
		wb[2] = "我丈夫出海死了，现在我也只好出来摆摊赚钱养活我那个两个孩子"
		wb[3] = "飞儿可真是个懂事的孩子，年纪轻轻就挑起了全家的重担。"
		wb[4] = "瞧一瞧！看一看！新鲜出炉的烤鸭！#51"
		wb[5] = "听过京城的人说，城里到处是亭台楼阁，红砖绿瓦，连皇上都住在那儿，真想去见识一下"
		wb[6] = "在我这里你可以学习烤鸭技巧，学会如何烹饪烤鸭之后你购买一只未煮熟的烤鸭，进行烹饪，烹饪之后的烤鸭不仅可以食用还可以500两银子出售给我"
		wb[7] = "无鸭不成席。来到建邺，你可一定得尝尝我家的烤鸭！"
		return{"女人_王大嫂","王大嫂",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "我在收购附近的海产，什么大海龟、巨蛙、海毛虫之类的都收#R/（警告：请将不出售的海产召唤兽设置为参战状态）"
			xx = {"我卖大海龟（250两银子/只或者300两储备金/只）给你","我卖巨蛙（350两银子/只或者400两储备金/只）给你","我卖海毛虫（500两银子/只或者600两储备金/只）给你","没什么，我只是看看"}
			return{"男人_钓鱼","海产收购商",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 9 then
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 1 then
		wb[1] = "最近海啸连连让不少#R渔民死在海上\n还夜夜报梦请求给他们做场#Y法事\n不知少侠可否帮忙!\n城里的#G牛大胆#是个道士,他应该知道法事的详情#1"
		xx = {"我愿意帮忙"}
		return{"男人_老孙头","老孙头",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 6 then
		wb[1] = "感谢你少侠,有了#G安魂草#我终于可以睡个好觉了\n但是#Y海底沉船闹鬼#的事情还得解决\n每年#G李善人#都会出资金让道士们解决该事\n但是每次都没什么用\n每年还是会有很多人死于海难\n你在帮我跑一趟吧去找#S李善人的管家#问问"
		xx = {"行吧我去找管家"}
		return{"男人_老孙头","老孙头",wb[取随机数(1,#wb)],xx}
		end
		wb[1] = "几年前一次台风，这里沉了不少船，东海湾那里常年有个旋涡，沉掉的船都在下面。要下沉船，从海边的旋涡那潜下去即可。"
		wb[2] = "要下沉船，从海边的旋涡那潜下去即可。"
		wb[3] = "这几年风调雨顺，老百姓的日子过得都很富足，只是城外野兽横行，常有外出的百姓受到骚扰。"
		return{"男人_老孙头","老孙头",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 10 then
			wb[1] = "这建邺小城真是风景如画。"
			wb[2] = "十级以下的新人我可以为你免费治疗伤势。"
			wb[3] = "我是行走江湖的郎中，治病救人是我份内之事。"
			wb[4] = "我是建邺城最精通医药之术的人。"
			wb[5] = "少肉多菜，少烦多眠，少欲多施，方能长寿。"
			xx = {"快些治疗我吧","我点错了"}
			return{"男人_药店老板","陈长寿",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 11 then
			wb[1] = "我没事就去长安书店给颜如玉打工，出体力赚钱一点都不难。"
			wb[2] = "昨天我去长安商会总管那，妈呀，东西多到开天眼都看不过来了"
			return{"小孩_飞儿","罗招弟",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 12 then
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 2 then
		wb[1] = "法事我可以帮你做,但我现在#G肚子很饿\n听说#S王大嫂#的#Y烤鸭#在建邺城那可是出了名的味美\n你能帮我搞一只来吗?吃饱了我才有力气做法事!#2"
		xx = {"我这就去帮你找烤鸭"}
		return{"男人_道士","牛大胆",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 5 then
		wb[1] = "你找到#烤鸭#了吗,如果找到了就交予我吧#1"
		xx = {"上交烤鸭"}
		return{"男人_道士","牛大胆",wb[取随机数(1,#wb)],xx}
		end
		wb[1] = "驱鬼除妖测字算命啦，前算五百年，后算五百年。"
		wb[2] = "这位朋友面带福相，印堂发亮，想是遇到什么喜事了吧。"
		wb[3] = "我就是英俊潇洒玉树临风嫉恶如仇斩妖除魔的方寸第一道士牛大胆！#51"
		wb[4] = "城里的渔夫出海打渔之前，都爱到我这里算上一卦。"
		wb[5] = "求神问卦，看人说话。这位你可要来让我瞧瞧？"
		return{"男人_道士","牛大胆",wb[取随机数(1,#wb)],nil}
		elseif 编号 == 13 then
			wb[1] = "我收购一切装备，直接给我就可以估价了。"
			xx = {"出售","我什么都不想做"}
			return{"男人_苦力","装备收购商",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 14 then
			wb[1] = "我鉴定一切武器，按物品收费，你自己给予给我就好了。（#R/鉴定价格为正常价格的10%#W/）"
			xx = {"给予","我什么都不想做"}
			return{"男人_苦力","装备鉴定商",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 15 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return {"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 16 then
			wb[1] = "建邺虽然小，可是什么都不缺。"
			wb[2] = "前几天去了趟长安城，城里的过往客商可真多。"
			wb[3] = "建邺的风景还不错吧。"
			wb[4] = "老孙头最近好象有什么心事的样子，整天皱着个眉头在自言自语。"
			wb[5] = "俗话说无奸不商，如今像我这样清白做生意的商人可真是少见了啊#17"
			wb[6] = "建邺城可谓麻雀虽小，五脏俱全。长安城有的，你在这都能找到#43"
			return{"男人_老伯","赵元宝",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 17 then

				if 玩家数据[数字id].角色:取任务(400)~=0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程==4 then
			    wb[1] = "面对世俗偏见，有情人终成眷属真的不容易#52"
				xx = {"小花姑娘,我来找旺财","取消"}
				return{"普陀_接引仙女","小花",wb[取随机数(1,#wb)],xx}
				end
				if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 10 then
				wb[1] = "面对世俗偏见，有情人终成眷属真的不容易#52"
				xx = {"美女,你见过金色莲花吗?","再见"}
				return{"普陀_接引仙女","小花",wb[取随机数(1,#wb)],xx}
				end
				wb[1] = "面对世俗偏见，有情人终成眷属真的不容易#52"
				wb[2] = "货栈边上那个王大嫂做的烤鸭真是好吃"
				wb[3] = "又过冬了，得为咱家孩子添置几件棉袄了"
				wb[4] = "听说最近长安城集市热闹的很，过几天可要去看看"
				wb[5] = "听说东海里有数不尽的宝贝……"
			    return{"普陀_接引仙女","小花",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 18 then
			wb[1] = "最近有些事情忙不过来了，你愿意帮我分担一点吗？"
			xx = {"我来领取新手任务","我只是看看"}
			--xx = {"我只是看看"}
			return{"男人_衙役","赵捕头",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 19 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 20 then
			wb[1] = "最近常有鬼鬼祟祟的家伙在门口探头探脑#35，我们镖局可不要这种来路不正的生意。"
			return{"男人_镖头","张来福",wb[取随机数(1,#wb)],xx}
		elseif  编号 == 21 then
			wb[1] = "找我干嘛呢？"
			xx = {"交易","只是路过"}
			return{"男人_特产商人","建邺特产商人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 22 then
			if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 8 then
			wb[1] = "又是一个想要#Y地狱灵芝#的\n想要其实很简单,帮我抓一只#G海毛虫#交予我\n我就把#Y地狱灵芝#给你\n是不是很简单?\n为什么还有很多人不愿意呢#14"
			xx = {"好的我去给你抓"}
			return{"男人_武器店老板","马全有",wb[取随机数(1,#wb)],xx}
		    end
		    if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 9 then
			wb[1] = "#Y海毛虫#你给我抓来了吗?想要#G地狱灵芝#的话,就感觉去抓吧!"
			xx = {"海毛虫我给你抓来了"}
			return{"男人_武器店老板","马全有",wb[取随机数(1,#wb)],xx}
		    end
		    if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 2 then
			wb[1] = "求老天保佑，早日除去城外的野兽，免得我们出城还要提心吊胆。"
            xx = {"询问虎子下落","取消"}
			return{"男人_武器店老板","马全有",wb[取随机数(1,#wb)],xx}
		    end

		    wb[1] = "听说这里有动物的毛能治小儿惊风，此次我是特地来这收购货物的。"
			wb[2] = "我是专门走南闯北倒卖货物的商人。"
			wb[3] = "求老天保佑，早日除去城外的野兽，免得我们出城还要提心吊胆。"
			wb[4] = "衙门里的简师爷平日深居简出，低调得很。"
			wb[5] = "您要点什么，下次我给您带来。"
			wb[6] = "衙天南地北的特产我这都有，就连地府的东西我也能搞到。"
			return{"男人_武器店老板","马全有",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 23 then
			wb[1] = "善哉善哉，佛祖有云：“一人出家，全家光荣”，这位施主看似颇有慧根，应该及早皈依我佛门才是啊。"
			wb[2] = "不知上次赵捕头有没有记得帮我替我带香烛回来。"
			wb[3] = "师傅常说，“心静则万事静，心清则万事清”，我特地到这个清静的小城来体验生活。"
			wb[4] = "不要问我从何而来，我站在这就是神仙一样的存在。"
			wb[5] = "和尚越老越值钱#18"
			return{"男人_胖和尚","迎客僧",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 24 then
			if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 7 then
			wb[1] = "少侠你来的真不是时候\n我家主人得了奇怪的#Y恶疾\n需要#G地狱灵芝#来医治\n可是这附近就只有#S马全有#有\n我找了#S马全有#数次都未果\n正发愁呢,如果你能帮忙弄到#G地狱灵芝\n治好了主人的病,说不定他会#Y给你出资,让你去解决#S闹鬼之事\n谁会跟#R钱#过不去你说是吧?"
			xx = {"我试试看吧"}
			return{"男人_店小二","管家",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "这是李老爷的府上，在这个小城里，我们老爷可是知名度最高的人了。"
			wb[2] = "我家老爷最近病的厉害，医生怎么查也查不出是什么原因。"
			wb[3] = "当个管家也不容易呀，每天忙里忙外，什么杂七杂八的事情都得打点。"
			wb[4] = "人说做一次善事容易，做一辈子善事就难咯，在这点上我是万分钦佩我家老爷的。"
			wb[5] = "建邺城啥时才能开个长安城那样的赌坊呢#80"
			return{"男人_店小二","管家",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 25 then
			wb[1] = "最近天气很好，海上没有大风浪。"
			wb[2] = "城外东海湾附近常有怪物出没，路过那里可要小心。"
			wb[3] = "咱家李善人老爷最近身体不适，我得去药店寻个方子。"
			wb[4] = "听说戏班老板请了京城的梨园弟子前来献艺，一定得去捧个场。"
			return{"男人_兰虎","符全",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 26 then
			wb[1] = "天天在这边看门真是没意思，我也渴望刺激的生活。"
			wb[2] = "前面就是江南野外了，没有实力可不要硬闯，小心被野兽吃的骨头都不剩！"
			xx = {"传送江南野外","我只是路过"}
			return{"男人_衙役","建邺守卫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 27 then
			if 玩家数据[数字id].角色:取任务(898) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(898)].进程 == 1 then
			wb[1] = "居然被你发现了,没错我就是#S妖风\n你超度的#G商人鬼魂#,就是我控制的\n这几年它帮了我不少的忙\n让我可以安安心心吸取#Y海难者的精气\n让我的#R修为大涨#,哈哈\n真正的#G雷黑子#已经被我吃了\n你又能把我怎么样呢?#16"
			xx = {"可恶,我一定要干吊你!"}
			return{"小孩_雷黑子","雷黑子",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 3 then
            wb[1] = "我经常去找宠物仙子姐姐玩，她那里有好多可爱的小动物，上次还送了我一只做宠物呢#43"
            xx = {"黑子哥,借狗一用"}
			return{"小孩_雷黑子","雷黑子",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 9 then
			wb[1] = "旺财你回来了吗?旺财!"
			xx = {"谢谢黑子哥,旺财还你","再见"}
			return{"小孩_雷黑子","雷黑子",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "城里有位好心的郎中陈长寿，给新人看病从不收银子。他一般都在去东海湾的城门口附近摆摊看病。"
			wb[2] = "我经常去找宠物仙子姐姐玩，她那里有好多可爱的小动物，上次还送了我一只做宠物呢#43"
			wb[3] = "城里什么都好，就是小伙伴太少。想去江南野外玩，妈妈又不让，说那里的怪物太凶，没一定的修为去了很危险。"
			wb[4] = "人之初，性本善，性相近，习相远。教书先生今天教的三字经，我一定得背熟。 "
			wb[5] = "建邺城里最好吃的就是马氏酸枣了#89"
			return{"小孩_雷黑子","雷黑子",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 28 then
			wb[1] = "我是城里的教书先生，如果你不清楚接下来做些什么，我可以给你一些建议。"
			wb[2] = "黑发不知勤学早，白首方悔读书迟。少侠可不要荒废了学业！"
			wb[3] = "在我教过的学生里，雷黑子是最刻苦上进的一个。"
			return{"男人_书生","教书先生",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 29 then
            wb[1] = "行走江湖难免会得到不少财物，将这些财物放到仓库里，实在是个最保险的方法。"
            xx={"打开仓库","暂时不用了"}
      return {"仓库保管员","仓库管理员",wb[取随机数(1,#wb)],xx}


    end

  	elseif ID == 1505 then
		if 编号 == 1 then
			wb[1] = "建邺里一年四季温暖如春。"
			wb[2] = "客官想买其他杂货可以去城里看看。"
			wb[3] = "洞冥草可以解除摄妖香的效果，你记住了吗#1"
			wb[4] = "宠物口粮只能通过怪物获得"
			xx = {"购买","我什么也不想做"}
			return{"男人_巫医","杂货店老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1537 then
		if 编号 == 1 then
			wb[1] = "不能体恤民情的官不是好官。"
			wb[2] = "公堂之上，岂容喧哗"
			wb[3] = "公正公平，廉洁高效，这词不错，明儿粉到墙上去"
			return{"男人_老书生","建邺县令",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "公堂禁地，闲杂人等不许乱闯。"
			wb[2] = "告状的话先把状纸准备好了到堂外排队去，别乱闯。"
			wb[3] = "作奸犯科可是要下大狱的。"
			wb[4] = "最近大唐国境总有强盗山贼出没，衙门里人手快不够用了。"
			wb[5] = "虽有石狮把门，安全还得靠人。"
			return{"男人_衙役","衙役1",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "公堂禁地，闲杂人等不许乱闯。"
			wb[2] = "告状的话先把状纸准备好了到堂外排队去，别乱闯。"
			wb[3] = "作奸犯科可是要下大狱的。"
			wb[4] = "最近大唐国境总有强盗山贼出没，衙门里人手快不够用了。"
			wb[5] = "虽有石狮把门，安全还得靠人。"
			return{"男人_衙役","衙役2",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "公堂禁地，闲杂人等不许乱闯。"
			wb[2] = "告状的话先把状纸准备好了到堂外排队去，别乱闯。"
			wb[3] = "作奸犯科可是要下大狱的。"
			wb[4] = "最近大唐国境总有强盗山贼出没，衙门里人手快不够用了。"
			wb[5] = "虽有石狮把门，安全还得靠人。"
			return{"男人_衙役","衙役3",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "公堂禁地，闲杂人等不许乱闯。"
			wb[2] = "告状的话先把状纸准备好了到堂外排队去，别乱闯。"
			wb[3] = "作奸犯科可是要下大狱的。"
			wb[4] = "最近大唐国境总有强盗山贼出没，衙门里人手快不够用了。"
			wb[5] = "虽有石狮把门，安全还得靠人。"
			return{"男人_衙役","衙役4",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "这日子有点太平过头了，半个月来一个告状的都没有。"
			wb[2] = "我们建邺城北门附近，有个有名的郎中叫陈长寿，他可以帮新朋友进行免费治疗。"
			wb[3] = "我们这里可是清水衙门，不收礼的。"
			wb[4] = "作奸犯科之事，衙门绝不会放过。"
			wb[5] = "清廉方正，一心为民，为官之道也。"
			return{"男人_师爷","简师爷",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1502 then
		if 编号 == 1 then
			wb[1] = "客官想要什么兵器？"
			wb[2] = "行走江湖不能两手空空，来挑一件趁手的兵器吧。"
			wb[3] = "这里的风景还不错吧。"
			wb[4] = "少侠是来选购兵器的吧？请慢慢挑选，务必看清楚名称哦！"
			xx = {"购买","我只是来看看"}
			return{"男人_武器店老板","武器店老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "长安武器店专卖0级和20级武器，0级和10级的武器则在建邺城出售。少侠记住了没#1"
			wb[2] = "瞧一瞧看一看了，我这里的武器最适合新人使用了。边上的老板出售高级一点的兵器，购买时请看清楚物品等级，别买错了哦#2"
			xx = {"购买","我只是来看看"}
			return{"男人_老孙头","武器店掌柜",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "（这是一个燃着暗红色火光的打铁炉，使用的时候请小心，烫到手就不好了。）"
			--xx = {"查看熟练度","打造","合成","修理","分解","熔炼"}
			xx =  {}
			return{nil,"打铁炉",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1503 then
		if 编号 == 1 then
			wb[1] = "小店的服饰都是请城里的裁缝精心裁剪的，做工绝对没有问题。"
			wb[2] = "城里的老少都穿我卖的衣服。"
			wb[3] = "小店布匹又快用光了，过两天得去城里进货。"
			wb[4] = "本店服装纯手工制作，随便看看吧。"
			xx = {"购买","我只是来看看"}
			return{"男人_服装店老板","服装店老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "（缝纫台上摆放着布匹、剪刀、尺子等物品，欢迎使用）"
			--xx = {"查看熟练度","打造","合成","认证"}
			xx =  {}
			return{nil,"缝纫台",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1504 then
		if 编号 == 1 then
			wb[1] = "城里有位好心的郎中陈长寿，看病从不收银子，他一般都在去东海湾的城门口附近摆摊看病。"
			wb[2] = "拉肚子，选好药，选药也要有诀窍。"
			wb[3] = "客官需要什么药？"
			wb[4] = "俗话说“对症下药”，这药可是不能乱吃的。"
			wb[5] = "佛手可以去长安、西梁女国和朱紫国的药店买哦。"
			wb[6] = "药材好，药才好。"
			xx = {"购买","我只是来看看"}
			return{"男人_药店老板","药店老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1526 then
		if 编号 == 1 then
			wb[1] = "最近都打不到什么猎物，日子还怎么过啊，老婆说再这样下去，就得考虑让我进京城打工了。"
			wb[2] = "城外的野兽倒不少，可都是凶猛无比，真是恐怖啊！"
			wb[3] = "如今外出打猎要带一包袱的草药，都搞不清是谁打谁了。"
			wb[4] = "进山不怕虎伤人，下海不怕龙卷身。没有胆量是做不得猎人的。"
			wb[5] = "自从建邺城开了新城门，经由我家门口去东海确实方便了许多。"
			xx={}
			local 任务id = 玩家数据[数字id].角色:取任务(307)
        	if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==12 then
	        	    wb[1] = "马儿？什么马儿，我没看见过什么马儿啊。"
	        	    xx = {"看来不给你点颜色看看是不会交代的","路过"}
        		end
        	end
        	if 玩家数据[数字id].角色:取任务(307)~=0 then
			    return {"男人_兰虎","周猎户",wb[1],xx}
			else
				return{"男人_兰虎","周猎户",wb[取随机数(1,#wb)],xx}
			end
		end
	elseif ID == 1534 then
		if 编号 == 1 then
			if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 10 then
			wb[1] = "哎呀我不行啦,我快死了,只有#Y地狱灵芝#才可以救活我#15"
			xx = {"上交地狱灵芝"}
			return{"男人_老财","李善人",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 12 then
			wb[1] = "原来如此,我一直错怪它了\n#Y幕后黑手#可真是可恶至极\n不过事情已经告一段落了\n看少侠骨骼惊奇万中无一\n为什么不选择一个#G门派#好好发展呢\n你可以通过升级传送,到各门派#Y拜师#!等级到达#G10级#就行了\n(#R温馨小提示:#在当前聊天框里可以输入 #S退出战斗 #可快捷退出战斗)"
			xx = {"领取酬劳"}
			return{"男人_老财","李善人",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "日行一善，积善成德。"
			wb[2] = "钱财是身外之物，能用来济世行善是最好不过。"
			wb[3] = "老夫虽膝下无子，但这万贯家财我已经找到了继承人。"
			wb[4] = "我已经不再年轻了，但我喜欢帮助有志向有作为的年轻人"
			return{"男人_老财","李善人",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1523 then
		if 编号 == 1 then
			wb[1] = "你要把物品典当给我吗？如果是#Y/古董#W/的话我的出价会高些，但是如果是普通的物品那么典当价格为正常价格的30%"
			xx = {"我有物品需要典当","我只是随便逛逛 打扰了"}
			return{"男人_特产商人","当铺老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1524 then
		if 编号 == 1 then
			wb[1] = "你是要办理什么业务？"
			xx = {"我要存钱","我要取钱","我只是来看看"}
			return{"男人_老财","钱庄老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1013 then
		if 编号 == 1 then
			wb[1] = "你是要办理什么业务？"
			xx = {"我要存钱","我要取钱","我只是来看看"}
			return{"男人_老财","钱庄老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1081 then
		if 编号 == 1 then
			wb[1] = "你是要办理什么业务？"
			xx = {"我要存钱","我要取钱","我只是来看看"}
			return{"男人_老财","钱庄老板",wb[取随机数(1,#wb)],xx}
		end
   	elseif ID == 1084 then
		if 编号 == 1 then
			wb[1] = "人家想有个漂亮的家,学点做家具的手艺也不错"
			xx = {"我要制作家具设计图","听说这里可以制作如意符","我想合成家具","我只是来看看"}
			return{"男人_店小二","鲁成",wb[取随机数(1,#wb)],xx}
		end

	elseif ID == 1099 then
		if 编号 == 1 then
			wb[1] = "你是要办理什么业务？"
			xx = {"我要存钱","我要取钱","我只是来看看"}
			return{"男人_老财","钱庄老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1506 then
		if 编号 == 1 then
			wb[1] = "我可以送你去#R/傲来国#W/你要不要去呢？"
			xx = {"是的我要去","我还要逛逛"}
			return{"男人_驿站老板","船夫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 5 then
				wb[1] = "我只为10级以下的新人治疗，而且是免费的，有什么可以帮你的吗？"
				xx = {"请问虎子在哪?","再见"}
				return{"男人_药店老板","云游神医",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 6 then
				wb[1] = "虎子变成僵尸了!#4"
				xx = {"开启虎子战斗","我还没准备好"}
				return{"男人_药店老板","云游神医",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 7 then
				wb[1] = "我知道你想问虎子为啥会这样\n不用急，虎子虽身中了尸毒，但还有一息尚存\n从我身后的小路往北走有位#G楚恋依#姑娘，颇有神通\n大侠可把虎子带过去让她看看"
				xx = {"事不宜迟，我这就过去找她","再见"}
				return{"男人_药店老板","云游神医",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "我只为10级以下的新人治疗，而且是免费的，有什么可以帮你的吗？"
			xx = {"请帮我治疗","我随便看看,打扰了"}
			return{"男人_药店老板","云游神医",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "这附近有个岩洞，进去过的人都说里面有很厉害的怪物，最好找一些伙伴一起进去比较安全，你也想进去冒险吗？"
			xx = {"是的，我想进去探个究竟","我怕黑，还是不进去了"}
			return{"男人_村长","林老汉",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "我可以送你去#R/龙宫#W/，你要不要去呢？"
			xx = {"是的我要去","我还有逛逛"}
			return{"虾兵","老虾",wb[取随机数(1,#wb)],xx}
			elseif 编号 == 6 then
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 8 then
			wb[1] = "东海之水，载不动我沉沉的依恋。"
			xx = {"还请恋依姐姐救救虎子","再见"}
			return{"普陀_接引仙女","楚恋依",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(400) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(400)].进程 == 11 then
			wb[1] = "东海之水，载不动我沉沉的依恋。"
			xx = {"上交枯萎的金莲","再见"}
			return{"普陀_接引仙女","楚恋依",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "从这里过去可以到建邺，那里的风景很不错的。"
			wb[2] = "东海湾常有怪物出没，可要小心。"
			wb[3] = "长安城里的集市很热闹的，有机会一定要去看看。"
			wb[4] = "东海边的林老汉可以带你去东海岩洞，那里能遇到海毛虫哦#40"
			wb[5] = "东海之水，载不动我沉沉的依恋。"
			return{"普陀_接引仙女","楚恋依",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
			wb[1] = "想要做个合格的海盗，不会游泳可不行。"
			return{"强盗","海盗头子",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "前些年这里沉了条船，从海边的漩涡下去就能看到那条沉船。"
			return{"男人_老伯","牛二",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 9 then
			wb[1] = "东海湾这风景真不错，是个散心的好地方。"
			return{"狐狸精","玉面公主",wb[取随机数(1,#wb)],xx}
		end
  elseif ID == 1507 then
    if 编号 == 1 then
      wb[1] = "我可以送你去#R/建邺城#W/你要不要去呢？"
      xx = {"是的我要去","我还要逛逛"}
      return{"蟹将","螃蟹精",wb[取随机数(1,#wb)],xx}
      elseif 编号 == 2 then
      	wb[1] = "我认识你吗?滚开!"
      	return{"蛤蟆精","蛤蟆精",wb[取随机数(1,#wb)],xx}
    end
  elseif ID == 1508 then
    if 编号 == 1 then
      wb[1] = "我可以送你去#R/建邺城#W/你要不要去呢？"
      xx = {"是的我要去","我还要逛逛"}
      return{"虾兵","虾精",wb[取随机数(1,#wb)],xx}
      elseif 编号 == 2 then
      	local 首杀名称=首杀记录.妖风
      	if 玩家数据[数字id].角色:取任务(898) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(898)].进程 == 2 then
      	wb[1] = "别不自量力了,你是打不过我的!#4\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
      	xx = {"开启妖风战斗","打不过先溜了"}
      	return{"吸血鬼","妖风",wb[取随机数(1,#wb)],xx}
        end
      	wb[1] = "我要食其肉,啖其血\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
      	return{"吸血鬼","妖风",wb[取随机数(1,#wb)],xx}
      end
  elseif ID == 1509 then
    if 编号 == 1 then
    local 首杀名称=首杀记录.商人的鬼魂
    if 玩家数据[数字id].角色:取任务(998) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(998)].进程 == 11 then
      wb[1] = "又来一个送死的吗?和我一样变成鬼魂吧!\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
      xx = {"开启剧情战斗","打不过先溜了"}
      return{"野鬼","商人的鬼魂",wb[取随机数(1,#wb)],xx}
      end
      --xx = {"开启剧情战斗","打不过先溜了"}
      wb[1] = "又来一个送死的吗?和我一样变成鬼魂吧!\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
      return{"野鬼","商人的鬼魂",wb[取随机数(1,#wb)],xx}
    end
    elseif ID == 1525 then
    if 编号 == 1 then
      wb[1] = "这里有一个宝箱,好像需要给他什么东西才能打开它\n这个宝箱还时不时的#Y散发出恶臭"
      xx = {"真是他妈太臭了","彩蛋什么的不存在的"}
      return{"宝箱","小宝箱",wb[取随机数(1,#wb)],xx}
    end
   	elseif ID == 1514 then
		if 编号 == 1 then
			wb[1] = "我这里帮助召唤兽进行法术认证，少侠是否需要帮助你的召唤兽进行认证呢？"
			xx = {"我要进行法术认证","我要取消法术认证","我随便逛逛"}
			return{"马猴","老马猴",wb[取随机数(1,#wb)],xx}
			elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 10 then
			wb[1] = "你想要我做的#R定神香#?\n救死扶伤,本就是我们医者的本分\n你想要我肯定不会不给\n再加上法明长老又是我的#Y好基友\n我肯定会给的,#G但是\n就是这么不巧,制作定神香的药引子\n#S餐风饮露#刚好用完了（朱紫国药店有卖）\n还是老样子,取找来交给我\n我就给你#G定神香"
			xx = {"妈的比唐僧还罗嗦"}
			return{"长眉灵猴","猴医仙",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 11 then
			wb[1] = "你带来#G餐风饮露#了吗?如果带来了赶紧交给我吧#!"
			xx = {"上交餐风饮露"}
			return{"长眉灵猴","猴医仙",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "救死扶伤,妙手回春,那可是我的本事"
			return{"长眉灵猴","猴医仙",wb[取随机数(1,#wb)],xx}

			elseif 编号 == 3 then
			wb[1] = "少侠有何贵干呢#55？"
			xx = {"关于潜能点","我随便逛逛"}
			return{"马猴","老猕猴",wb[取随机数(1,#wb)],xx}
	end
  elseif ID == 1001 then
  		if 编号 == 1 then
  			if 玩家数据[数字id].角色:取任务(402) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(402)].进程 == 2 and 玩家数据[数字id].角色.数据.等级>=20 then
			wb[1] = "你在长安城瞎转,不需要一个导游吗?"
			xx = {"开启新手指引","我随便逛逛"}
			return{"男人_书生","长安导游",wb[取随机数(1,#wb)],xx}
		    end
		     if 玩家数据[数字id].角色:取任务(402) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(402)].进程 == 7 then
			wb[1] = "你在长安城瞎转,不需要一个导游吗?"
			xx = {"领取奖励","我随便逛逛"}
			return{"男人_书生","长安导游",wb[取随机数(1,#wb)],xx}
		    end
		    wb[1] = "当你达到30级后,可以通过\n#G抓鬼,押镖,江湖任务,官职任务,藏宝图,科举,封妖,三界悬赏令,帮派任务#进行提升\n还有更多限时活动,请打开#Y梦幻指引界面#进行查看!"
			return{"男人_书生","长安导游",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "你这个人看起来好奇怪!"
			return{"女人_王大嫂","王夫人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "平日里多逛逛街，常会遇到些物美价廉的东西#89"
			--xx={"购买召唤兽","购买变异召唤兽","道具交易中心","我只是路过"}
			xx={"购买召唤兽","购买变异召唤兽","购买三级药品","购买烹饪物品","我只是路过"}
			return{"仓库保管员","商会总管",wb[取随机数(1,#wb)],xx}
     	elseif 编号 == 4 then
			wb[1] = "这位少侠，可否替老夫捎一封书信给长寿村的凤凰姑娘#17？"
			wb[2] = "这位朋友是来京城做生意吗，最生意的方式很多，可以摆摊或者开店，开店的话可以找当铺门口的商会总管申请。"
			return{"仓库保管员","陈员外",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "你看到过我的小花吗?"
			return{"普陀_接引仙女","花香香",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "我是30年的老花农了,有什么可以帮到你?"
			return{"男人_村长","老花农",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
			wb[1] = "我这里有一些奇珍异宝，你要买吗？"
			xx = {"购买","我点错了"}
			return{"男人_道士","罗道人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "每次进入染色界面需要消耗一颗彩果，你确定吗？"
			xx = {"我想为人物染色","我点错了"}
			return{"女人_染色师","染色师",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 9 then
			wb[1] = "我可以送你去大唐国境，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return{"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 10 then
			wb[1] = "平日里多逛逛街，常会遇到些物美价廉的东西#89"
			wb[2] = "那边的威震天真是个大嗓门，得想个办法管管他#54"
			return{"男人_老财","张老财",wb[取随机数(1,#wb)],xx}
	   	elseif 编号 == 11 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 12 then
			wb[1] = "少侠请选择要为你传送的门派"
			xx = {"方寸山","女儿村","神木林","化生寺","大唐官府","盘丝洞","阴曹地府","无底洞","魔王寨","狮驼岭","天宫","普陀山","凌波城","五庄观","龙宫"}
      		if 玩家数据[数字id].角色.数据.等级>=30 then
	        	if 玩家数据[数字id].角色.数据.门派=="无门派" then
	           		wb[1] = "你的等级已经超过了30级，我无法再为你提供此类型服务。"
	           		xx=nil
	        	else
	        		if 玩家数据[数字id].角色:取任务(300)~=0 or 玩家数据[数字id].角色:取任务(208)~=0 then
	        		    wb[1] = "少侠身上有押镖的任务，还是自己护送过去吧！"
	        		    xx = {}
	        		else
	        			wb[1] = "请选择你要传送的门派(等级超过30级仅可以传送至本门派)："
	          			xx={玩家数据[数字id].角色.数据.门派,"我只是路过"}
	        		end
	        	end
      		end
			return{"男人_镖头","门派传送人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 13 then
			wb[1] = "我可以送你去战神山，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return{"男人_太上老君","圣山传送人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 14 then
			xx={}
      		if 闯关参数.开关 then
  				wb[1] = "欢迎参加门派闯关竞赛活动，你是否已经叫齐伙伴准备闯关了？"
  				xx = {"准备好了，请告诉我们第一关的挑战地点","我要取消任务","我只是来凑凑热闹"}
		  	else
		  		wb[1] = "当前不是活动时间。"
			end
			return{"男人_马副将","门派闯关使者",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 15 then
			--xx = {"开始四季活动","结束四季活动","查看四季活动积分","兑换四季活动奖励","没有什么想玩的"}
			wb[1] = " 听人说大唐官府的程咬金将军在广招徒弟，不知道谁能有幸拜在他的门下。大唐官府的入口在长安天台的后面。"
			wb[2] = " 化生寺的空度禅师也在招收徒弟，有兴趣的可以去试试。化生寺的入口就在长安大雁塔后面。"
			return{"男人_马副将","刘副将",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 16 then
			wb[1] = " 是个男人就要刚，谁都不要怕#4"
			--xx={"学习调息","学习打坐","学习翱翔","学习变化之术","学习奇门遁甲","学习妙手空空","学习宝石工艺","学习仙灵店铺","购买五色旗盒","遗忘剧情技能","兑换进阶变身卡(50W银子一次)"}
			xx={"学习调息","学习打坐","学习变化之术","学习奇门遁甲","学习妙手空空","学习宝石工艺","学习仙灵店铺","购买五色旗盒","遗忘剧情技能","兑换进阶变身卡(50W银子一次)"}
			return{"男人_兰虎","兰虎",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 17 then
	      	if 双倍数据[数字id]==nil then
	        	双倍数据[数字id]={可领=4,冻结=0,间隔=os.time()}
	      	end
			wb[1] = "奉唐王圣喻，招募外出杀敌的勇士。凡有心效力朝廷者可以在我这里领取雇佣时间，得到双倍经验的奖励，每人每日可以领取#R/4#W/小时。你本日还可领取#R/"..双倍数据[数字id].可领.."#W/小时，当前已冻结双倍时间#R/"..双倍数据[数字id].冻结.."#W/分钟。双倍时间将在每日24点整刷新，已冻结的双倍时间将会直接清空。"
			xx = {"领取一小时","领取两小时","领取四小时","冻结双倍时间","恢复双倍时间","什么也不做"}
			 -- if 嘉年华.活动开关 then
			 -- 	xx = {"领取一小时","领取两小时","领取四小时","冻结双倍时间","恢复双倍时间","开启嘉年华副本","什么也不做"}
			 -- end
			return{"男人_马副将","马副将",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 18 then
      		if 三倍数据[数字id]==nil then
        		三倍数据[数字id]={可领=2,冻结=0,间隔=os.time()}
      		end
			wb[1] = "奉唐王圣喻，为嘉奖表现卓越的勇士(≥60级)每人每日可以领取#R/2#W/小时的精修时间。你本日还可领取#R/"..三倍数据[数字id].可领.."#W/小时的精修时间。精修时间将在每日24点整刷新，精修时间一旦领取将无法冻结。"
			xx = {"领取一小时精修时间","我只是随便瞧瞧"}
			return{"男人_马副将","御林军",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 19 then
			wb[1] = "完成日常任务\n将会获得#G心魔宝珠#W集齐#R50颗#W心魔宝珠后\n可以来我这里换取奖励\n每个角色每天最多可以换取20次奖励。"
			xx = {"上交心魔宝珠","我来领取赏金任务","我马上去搜集"}
			return{"护卫","皇宫护卫",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 20 then
      		if 玩家数据[数字id].角色:取任务(7)~=0 then
        		玩家数据[数字id].科举对话=true
        		return{"考官2","礼部侍郎",string.format("#Y/第%s题：#W/%s",科举数据[数字id].总数, 玩家数据[数字id].科举数据.题目),玩家数据[数字id].科举数据.答案}
      		end
			wb[1] = "等级达到30级的玩家每日可以在我这里参加两次科举活动。每轮都会有二十道题目，每道题有十五秒的答题时间。只要在答题时间内正确作答即可获得奖励。如果回答错误数量达到十次，将直接被淘汰掉。"
			xx = {"我要参加科举","我先回家再看几年书"}
			return{"考官2","礼部侍郎",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 21 then
			wb[1] = "天台的边上就是大雁塔，里面镇压着十万妖魔，是四十级前玩家升级打怪的好去处。"
			--xx={"勇闯通天塔","人物洗点","角色转换","装备转换","武器染色","角色改名","补充法宝灵气","关于高级兽决","关于召唤兽内丹","神兽造型相关操作","神兜兜召唤兽相关操作"}

			if 自定义数据.神兽提升资质~=nil and 自定义数据.神兽提升资质 then
				xx={"领取侠义任务","人物洗点","武器染色","角色改名","门派转换","法术认证","取消法术认证","关于神兽兑换","关于兽决兑换","关于召唤兽内丹","神兽造型相关操作","神兽天生技能更换","神兽成长资质提升","变异召唤兽添加技能格子"}
            else
                xx={"领取侠义任务","人物洗点","武器染色","角色改名","门派转换","法术认证","取消法术认证","关于神兽兑换","关于兽决兑换","关于召唤兽内丹","神兽造型相关操作","神兽天生技能更换","变异召唤兽添加技能格子"}
            end

			return{"袁天罡","袁天罡",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 22 then
      		wb[1] = "宫廷内外有太多事物要处理，你是否愿意帮我处理这些事物？"
      		xx = {"我来帮你","取消任务","领取俸禄","我可没那闲功夫"}
      		return{"男人_马副将","李将军",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 23 then
      		wb[1] = "商城直接买附魔宝珠进行套装点化附魔"
      		xx = {"请帮我点化装备","请帮我更换宝宝五行","请帮我更换装备五行","请帮我更换法宝五行","我只是路过"}
      		return{"五行大师","五行大师",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 24 then
      		wb[1] = "我受朝廷所托，在此召集十五门派的新入门派弟子,帮忙处理江湖之事"
      		xx = {"领取任务","我要取消任务"}
      		return{"男人_店小二","杜少海",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 25 then
      		xx={}
	      	if 皇宫飞贼.开关 then
	        	wb[1] = "皇宫之内，禁卫森严！竟然有人敢来皇宫盗宝，这群飞贼必然来头不小。我们禁卫军跟他们一场恶战，也伤了他们几个贼头，可惜我们以保护唐王为第一要务，这才让他们趁乱逃跑，少侠可愿意帮助我抓这些飞贼归案？"
	        	xx = {"真是无法无天！我这就去帮你把他们抓回来","我打算去抓住这次活动的幕后黑手！","我来取消任务"}-----"我来取消任务",
	      	else
	        	wb[1]="皇宫飞贼活动在每日中午12-14点才开放。"
	      	end
      		return{"护卫","御林军左统领",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 26 then
      		xx={}
      		wb[1]="皇城重地、天子脚下，我劝你为人要善良些。"
      		return{"护卫","御林军右统领",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 27 then
      		wb[1] = "少侠，我这里可是有些稀罕玩意，不知道你是否愿意出高价购买呢？"
      		xx = {"购买","我没钱"}
      		return{"珍品商人","长安珍品商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 28 then
      		wb[1] = "我收购一切装备，直接给我就可以估价了。"
      		xx = {"出售","我什么都不想做"}
      		return{"男人_苦力","装备收购商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 29 then
      		wb[1] = "我鉴定一切武器，按物品收费，你自己给予给我就好了。（#R/鉴定价格为正常价格的10%#W/）"
      		xx = {"给予","我什么都不想做"}
     		return{"男人_苦力","装备鉴定商",wb[取随机数(1,#wb)],xx}
    	elseif 编号==30 then
        	wb[1] = "行走江湖难免会得到不少财物，将这些财物放到仓库里，实在是个最保险的方法。"
        	xx={"打开仓库","暂时不用了"}
        	return {"仓库保管员","仓库管理员",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 31 then
    		wb[1] = "装备需要通过开运增加开孔孔数才能进行符石镶嵌，而点化此乃上古秘传道术。贫道近期正修炼此法，你有兴趣的话可以来试试。我可考虑清楚，贫道虽习得此法，但道行修行尚浅，且需运筹乾坤，难免开运失败。"
    		xx = {"我来给装备开运","点化装备星位","我来合成符石","我可不想给装备开运"}
    		return {"男人_道童","符石道人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 32 then
    		wb[1] = "在我这里可以缴纳一定的银子创建帮派。"
    		xx = {"回到帮派","加入帮派","创建帮派","我什么都不想做"}
    		return {"男人_兰虎","帮派管理员",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 33 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    			xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_特产商人","长安商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 34 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_老财","长安货商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 35 then
    		if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 4 then
			wb[1] = "你好少侠"
			xx = {"触发剧情"}
			return{"男人_太上老君","袁守城",wb[取随机数(1,#wb)],xx}
		    end
		    if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 14 then
			wb[1] = "你好少侠"
			xx = {"触发后续"}
			return{"男人_太上老君","袁守城",wb[取随机数(1,#wb)],xx}
		    end
    		wb[1] = "老夫潜心修炼，悟得一通天之术"
    		return {"男人_太上老君","袁守城",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 36 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 13 then
			wb[1] = "我一定会将龙宫秘法修炼好，誓为爷爷报仇。"
			xx = {"开启剧情战斗"}
			return {"男人_小白龙","龙孙",wb[取随机数(1,#wb)],xx}
			end
    		wb[1] = "我一定会将龙宫秘法修炼好，誓为爷爷报仇。"
    		xx = {}
    		return {"男人_小白龙","龙孙",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 37 then
			wb[1] = "需要建造房子在我这里购买，少侠需要操作什么?"
			 xx = {"我想进行房屋相关事宜","我只是来看看你"}
		   return{"男人_衙役","建房史",wb[取随机数(1,#wb)],xx}


		elseif 编号 == 38 then
			wb[1] = "我这里可以帮助你取消一些效果和修复#G卡装备不能穿戴#的问题"
            xx={"取消人物变身卡","取消罗羹效果"}
			return{"召唤兽造型进阶","效果取消熊猫",wb[取随机数(1,#wb)],xx}

		elseif 编号 == 39 then
			wb[1] = "现在买宝石可以去商城看看了"
			xx={"我想购买你那些珍品","我只是来看看"}
			return{"男人_老财","宝石商人",wb[取随机数(1,#wb)],xx}
	  	elseif 编号 == 42 then
			wb[1] = "每逢佳节少侠可在我这里领取节日礼物哟。"
			xx={"我要领取礼物","不，我只是来看看"}
			return{"兔子怪","节日礼物使者",wb[取随机数(1,#wb)],xx}

		elseif 编号 == 43 then
			wb[1] = "科举考试在国子监处,还不快去!"
			--xx={"我准备好了","过会再来","内部测试中"}
			return{"男人_马副将","房都尉",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 44 then
			wb[1] = "长安城真热闹！"
			return{"女人_丫鬟","怜儿姑娘",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 45 then
			wb[1] = "长安城真热闹！"
			return{"大宝","小宝",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 46 then
            wb[1] = "剑会天下大赛分为:1v1单人模式 3v3三人模式 5v5多人模式\n#Y如果匹配不到玩家会自动匹配假人NPC进行战斗"
			xx = {"我要打听剑会天下的消息","我要领取赛季奖励","我要查询赛季排行榜","我只是路过"}
		    return{"男人_马副将","剑会天下主持人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 47 then--比武NPC
			 wb[1] = "我是负责#R英雄比武大会#W的主持人，你找我有什么事呢？"
			 xx={"查看排行","我要领取比赛奖励","我要打听天下英雄会消息"}
             if 英雄大会.开关 then
	             if not 英雄大会.准备 and not 英雄大会.开始 then
	             	if not 英雄大会.报名[数字id] then
				        xx={"单人报名","组队报名","我要打听天下英雄会消息"}
				     else
				         if 英雄大会.报名[数字id].组队 then
				         	if 玩家数据[数字id].队长 then
				         	    xx={"添加新队友","查看队友","我要打听天下英雄会消息"}
				         	 else
				         	    xx={"单人模式","查看队友","我要打听天下英雄会消息"}
				         	 end
				         else
				         	xx={"多人模式","我要打听天下英雄会消息"}
				         end
				     end
				  else
				  	  if 英雄大会.报名[数字id] then
				  	      xx={"进入比武","我要打听天下英雄会消息"}
				  	  else
				  	  	wb[1] = "英雄比武大会正在进行,你未报名无法进入赛场？"
				  	  	xx={"我要打听天下英雄会消息"}
				  	  end
                  end
			 end
			 return{"男人_将军","比武大会主持人",wb[取随机数(1,#wb)],xx}

    	elseif 编号 == 48 then
			wb[1] = "红绳一牵，逃不过三世宿缘,结婚需要花费5W仙玉,离婚也要扣除2E银子。"
    		--xx = {"我们结婚啦","我要协议离婚","我要强制离婚","我只是来看看"}
    		return{"男人_太上老君","月老",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 49 then
    		wb[1] = "坐轿子回家吧，既快速又舒适每次收取费用#R/500#W/两。"
    		 xx = {"马上送我回家","我要拜访别人的家","我只是来看看你的轿子，别那么紧张"}
    		--xx = {"我只是来看看你的轿子，别那么紧张"}
			return{"男人_兰虎","轿夫",wb[取随机数(1,#wb)],xx}


		elseif 编号 == 50 then
				wb[1] = "异兽录上记载了各式神怪之物的修炼之道，不知少侠可有耳闻？小仙受玉帝所托，将此道传授给有缘之人。"
				--xx = {"我来给召唤兽换进阶造型","给坐骑染色","给坐骑饰品染色","还原宠物染色数据","我先告辞"}
					xx = {"我来给召唤兽换进阶造型","还原宠物染色数据","我先告辞"}
				return{"召唤兽造型进阶","召唤兽进阶熊猫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 51 then
			wb[1] = "师者，传道解惑也。等级达到69级的玩家可以在我与等级在50-59级的玩家建立师徒关系"
			xx={"请帮我们建立师徒关系","查询我的教育记录","我是来解除师徒关系的","我们是来出师的","我只是路过"}
			return{"考官2","国子监祭酒",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 52 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 38 then
			wb[1] = "什么这是小女的书信?#24\n没想到十几年间居然出了这么大的变故#15\n我定要让这个#Y刘洪#死无葬身之地\n你速速去通知#G魏征#大人\n让他赶紧#S出兵剿贼#,弄死这个鳖孙儿"
			xx = {"对!必须干死他!"}
			return {"考官2","殷丞相",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "你好!"
			return{"考官2","殷丞相",wb[取随机数(1,#wb)],xx}
       elseif 编号 == 53 then
			if 镇妖塔数据[数字id] == nil then
            	镇妖塔数据[数字id] = {名称 = 玩家数据[数字id].角色.数据.名称,层数 = 0}
          	end
		 --    wb[1] = "少侠是否是来挑战通天塔，效验自身实力的呢？少侠当前通天塔层数为：#R第 "..通天塔数据[数字id].层数.." 层"
			-- xx = {"挑战通天塔","我点错了"}
			wb[1] = "在我这边可以挑战爬塔，根据自己的实力层层往上每10层难度递增!每周可以重置1次哦，塔层数重置为层。少侠当前塔层数为：#Y第 #R"..镇妖塔数据[数字id].层数.." #Y层"
			xx = {"开启挑战","重置层数(2E)","重置层数(2000仙玉)","我就是来看看"}
			if 镇妖塔数据[数字id] and 镇妖塔数据[数字id].层数>20 then
					local 当前层数 = math.floor(镇妖塔数据[数字id].层数/10)*10-1
					local 当前选项 = "重置到"..当前层数.."层"
					xx = {"开启挑战","重置层数(2E)","重置层数(2000仙玉)",当前选项.."(10E)",当前选项.."(1万仙玉)","我就是来看看"}
			end
			return {"毗舍童子","镇塔童子",wb[取随机数(1,#wb)],xx}

       elseif 编号 == 54 then
			-- if 玩家数据[数字id].角色:取任务(7760) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(7760)].分类 == 2 then
   --      		xx[#xx+1] = "你要的物品我找来了"
   --      	end
			wb[1] = "金鼠捧金辞旧岁，福牛纳福开新章。祝少侠新的一年阖家团圆，心想事成，万事如意！少侠想要做点什么呢?"
			--xx = {"喜迎新春到(环任务)","上交春节物品(环任务)","爆竹响三界(驱赶年兽)","侠士降兽王(年兽统领)","我就是来看看"}
			xx = {"喜迎新春到","我就是来看看"}
		    return{"小仙女","福禄童子",wb[取随机数(1,#wb)],xx}

-----------------------
       elseif 编号 == 55 then
	        wb[1] = "“千丈虹桥望入微，天光云影共楼飞。”唐王为天下英雄设下闯关竞技平台，欢迎能人志士一展拳脚！"
	  		xx = {"我来报名","我要参加活动，请送我入场","哎呀，肚子疼，先上个茅房","我想了解这个活动","我来取消任务"}
	  		return {"男人_兰虎","彩虹大使",wb[取随机数(1,#wb)],xx}
       elseif 编号 == 56 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级青鸾","超级青鸾" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 57 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级腾蛇","超级腾蛇" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 58 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级赤焰兽","超级赤焰兽" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 59 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级神牛","超级神牛" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 60 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级泡泡","超级泡泡" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 61 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级神虎","超级神虎" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 62 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级海豚","超级海豚" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 == 63 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级白泽","超级白泽",wb[取随机数(1,#wb)],xx}
       elseif 编号 == 64 then
            wb[1] = "你可以在仙玉商城购买我哟!"
            return {"超级玉兔","超级玉兔" ,wb[取随机数(1,#wb)],xx}
       elseif 编号 ==65 then
        	wb[1] = "行走江湖难免会得到不少财物，将这些财物放到仓库里，实在是个最保险的方法。"
        	xx={"打开仓库","暂时不用了"}
        	return {"仓库保管员","仓库管理员",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 66 then
      		wb[1] = "哎哟不错,很吊哦!!"
      		xx = {"人物伤害测试","暂时不用了"}
       		return {"木桩",服务端参数.名称.."伤害测试",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 67 then
      		wb[1] = "少侠,很吊哦!你敢挑战门派?让我见识下你的实力!"
      		xx = {"用实力证明一下",  "暂时不用了"}
       		return {"剑侠客","挑战门派师傅",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 68 then
        	wb[1] = "少侠，你有实力吗？"
        	xx={"我来挑战(60-69)","我来挑战(80-89)","我来挑战(100-109)","我来挑战(120-145)","我来挑战(150以上)","拜拜！"}
        	return {"剑侠客","一副欠揍的样子",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 69 then
			wb[1] = "#Y世界B0SS来袭，大家一起来组队，阻止世界BOSS的步伐啊"
			xx = {"查看世界BOSS信息","我要领取奖励","我只是路过"}
		    return{"男人_将军","世界BOSS传送员",wb[取随机数(1,#wb)],xx}
    	 elseif 编号 == 70 then
			wb[1] = "三千红尘历练，各人自有仙缘。三界之间，每一个不起眼的地方，都有可能隐藏着巨大机缘，等待有缘之人前往得之。"
			xx = {"领取仙缘","仙缘商城","我要取消任务","路过"}
		    return{"男人_书生","书生",wb[取随机数(1,#wb)],xx}
	     elseif 编号==71 then
				wb[1] = "我曾经做错了很多，所以我会用永远来赎罪。"
				if 玩家数据[数字id].神器 and 玩家数据[数字id].神器.数据.神器技能 then
					xx = {"更换神器技能","更换神器五行","炼制灵犀之屑","合成灵犀玉","我要做其他事情","我点错了"}
				else
				    xx = {"神器任务序章","我要做其他事情","我点错了"}
				end

		    	return {"飞燕女","一副可爱的样子",wb[取随机数(1,#wb)],xx}
         elseif 编号 == 72 then
			    wb[1] = "人间昌隆，玉帝闻此甚是欣喜，故派十二元辰分影降临人间，本官特召集各方侠士前往挑战十二元辰分影。"
			    xx={"我欲前往收服十二元辰","此行甚是疲惫，我要取消任务","我想了解玩法","我要查看排名","查看我的排名","我要领取排行奖励","我只是路过"}--"我欲前往收服十二元辰","此行甚是疲惫","我要取消任务","我想了解玩法","我要查看排名","我要领取排行奖励",
			    return{"天兵","游奕灵官",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 73 then
			wb[1] = "地宫内漆黑一片，且每一层都有不同类型的怪物尚未清剿完毕，你若是想要下去探查，必须携带火把方可！#R/(注:第一个达到100层并且成功封印蚩尤后重置地宫)"
			xx = {"前往参加","过来看看热闹"}
			return{"男人_马副将","雁塔地宫",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 74 then
             wb[1] = "少侠,我这里提供各门派强化符哦!"
			xx = {"购买","我就是来看看"}
            return{"召唤兽造型进阶","强化符熊猫",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 75 then
             wb[1] = "少侠,你敢挑战轮回境?"
			xx = {"我来挑战第一层","我来挑战第二层","我来挑战第三层","我来挑战第四层","我来挑战第五层","我来挑战第六层","我来挑战第七层","我就是来看看"}
            return{"男人_书生","轮回境",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 76 then
             wb[1] = "每个角色每天挑战3次,最少5人进行战斗"
			xx = {"我来参加活动","我就是来看看"}
            return{"毗舍童子","童子之力",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 77 then
             wb[1] = "每个角色每天挑战3次,连续3场战斗,最少5人进行战斗"
			xx = {"我来参加活动","我就是来看看"}
            return{"进阶地狱战神","副本BOSS挑战",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 78 then
             wb[1] = "少侠,有实力挑战GM?每个角色每天挑战1次,,最少5人进行战斗"
			xx = {"我来挑战GM","我就是来看看"}
            return{"剑侠客","挑战"..服务端参数.名称.."GM",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 79 then
			 wb[1] = "少侠,确定要去跨服战场吗?(每个账号只能进入一个进入跨服)"
			-- --xx = {"喜迎新春到(环任务)","上交春节物品(环任务)","爆竹响三界(驱赶年兽)","侠士降兽王(年兽统领)","我就是来看看"}
			 xx = {"跨服报名","开启跨服","取消跨服报名","我就是来看看"}
			-- if 跨服排名[数字id] and not 跨服排名[数字id].领取 then
   --              xx = {"领取奖励","我就是来看看"}
			-- end
		     return{"男人_马副将","跨服争霸主持人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 80 then
					wb[1] = "正是举国科举期间，今年考生众多，一时间忙不过来了，想请少侠帮助一二，少侠是否愿意？"
					xx = {"正闲着呢，请吩咐","我想了解本次活动","我来使用积分","取消文韵墨香任务"}
					return{"考官2","文韵墨香使者",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 81 then
					wb[1] = "超级魔兽要诀的力量强大而凶暴。贸然学习十分危险。我可以帮助少馍的召换兽掌猩这股强大的力量,但总要给我点好处才行。"
					xx = {"我的召唤兽需要你的赐福.","我想了解下赐福","我只是来找你玩的"}
					return{"超级神柚","超级神柚",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 82 then
					 wb[1] = "找我有事吗?嘉年华活动开启过后可以来找我哦!获得的积分可抽奖"
					 xx = {"嘉年华抽奖","我想了解下","我只是来找你玩的"}
				    if 嘉年华时间 then
						xx = {"开启嘉年华副本","嘉年华抽奖","我想了解下","我只是来找你玩的"}
				    end
					return{"超级红孩儿","超级红孩儿",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 83 then
					 wb[1] = "我是罗刹鬼将！你有那本事？挑战我？"
					 xx = {"我来挑战罗刹鬼将","我就是来看看"}
					return{"进阶迭代鬼将","罗刹鬼将",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 84 then
					 wb[1] = "我命由我不由天！是魔是仙，我自己说了算！"
					 xx = {"我来和你一较高下，看谁说了算","我就是来看看"}
					return{"男人_哪吒","哪吒",wb[取随机数(1,#wb)],xx}




    	end
	elseif ID == 1022 then
		if 编号 == 1 then
			wb[1] = "人靠衣装马靠鞍，本店为您提供各种新款服装，就算不买也来看看吧。"
			wb[2] = "这里各种绸缎一应俱全，肯定有你想要的。只有大唐官府的玩家才能学会鉴定衣服的技能，而项链腰带的鉴定技能只有地府的玩家才能学。"
			wb[3] = "本店的服饰选用的都是上等丝绸布匹，做工细致，包您满意"
			wb[4] = "三分长相，七分打扮，挑件合身的衣服吧。店里的张裁缝可以让你提高裁缝熟练度。"
			wb[5] = "要自己做衣服请到那边的裁缝台吧，只收取您少量的加工费。"
			xx = {"购买","我只是来看看"}
			return{"男人_服装店老板","服装店老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "我裁剪的衣服，穿过的人没有说不好的！我一眼看过去，不用尺，就知道你的三围是多少！"
			--xx = {"普通打造","强化打造","关于打造方式的介绍","有什么需要帮忙的（打工增加熟练度）","查看熟练度","路过"}
			--xx = {"查看熟练度","普通打造","强化打造","元身打造","关于打造方式的介绍","路过"}

			xx = {"路过"}
			return{"男人_服装店老板","张裁缝",wb[取随机数(1,#wb)],xx}
		end
  	elseif ID == 1024 then
    	if 编号 == 1 then
      		wb[1] = "镖王活动可以随时参加,普通押镖也可以进行！"
      		xx = {"我要报名参赛(镖王活动)","普通押镖任务","请帮我取消任务","请帮我取消任务(镖王活动)"}
            --xx = {"我要报名参赛(镖王活动)","一级镖银（30级能运 奖5W银子）","二级镖银（50级能运 奖10W银子）","三级镖银（70级能运 奖15W银子）","四级镖银（90级能运 奖20W银子）","五级镖银（110级能运 奖100仙玉）","请帮我取消任务","请帮我取消任务(镖王活动)"}
      		return{"男人_镖头","郑镖头",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1026 then
    	if 编号 == 1 then
      		wb[1] = "副本活动,找我开启"
      		xx = {"我要开启乌鸡国副本","我只是一个路过的"}
      		if 玩家数据[数字id].角色:取任务(120)~=0 then
        		wb[1]="你的副本已经开启了，是否需要我帮你传送进去？"
        		xx={"请送我进去","取消乌鸡副本","我等会再进去"}
      		end
      		return{"男人_书生","吴举人",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1020 then
		if 编号 == 1 then
			wb[1] = "长安武器店专卖10级和20级武器，0级和10级的武器则在建邺城出售。少侠记住了没#1"
			wb[2] = "瞧一瞧看一看了，我这里的武器最适合新人使用了。边上的老板出售高级一点的兵器，购买时请看清楚物品等级，别买错了哦#2"
			xx = {"购买","我只是来看看"}
			return{"男人_老孙头","武器店掌柜",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "客官想要什么兵器？"
			wb[2] = "行走江湖不能两手空空，来挑一件趁手的兵器吧。"
			wb[3] = "这里的风景还不错吧。"
			wb[4] = "少侠是来选购兵器的吧？请慢慢挑选，务必看清楚名称哦！"
			xx = {"购买","我只是来看看"}
			return{"男人_武器店老板","武器店老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1193 then
		if 编号 == 1 then
			wb[1] = "我戴这桃花好看吗？"
			return{"女人_丫鬟","罗纤纤",wb[sj(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "我这里的商品琳琅满目应有尽有，年轻人想要什么尽管开口。"
			return{"仓库保管员","江湖奸商",wb[sj(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "我不过是想夫妻两人好好过日子，谁知道这头猪居然喜欢上别的女人！"
			return{"女人_丫鬟","卵二姐",wb[sj(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "生态环境的恶化与滥砍滥伐有着密切的关联，保护大塘绿化事业人人有责啊！"
			return{"樵夫","樵夫",wb[sj(1,#wb)],xx}

		end




	elseif ID == 1198 then
		if 编号 == 2 then
			wb[1] = "我家老程的三板斧据说是在梦中学会的，我才不信呢。"
			wb[2] = "现在是太平盛世，百姓安居乐业。"
			wb[3] = "我家兄弟当初与老爷并肩作战，现如今也不知身在何处？"
			wb[4] = "听说老爷最近要远征歼杀突厥，真担心他的身子。"
			return{"女人_程夫人","程夫人",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "心比天高，命比纸薄，我的人生也会如此么？"
			wb[2] = "夫人什么时候再上街呢……好想再见到那个小哥一面#17"
			wb[3] = "我家老爷是开国功臣，现在正在广招门徒。"
			return{"女人_丫鬟","丫鬟",wb[取随机数(1,#wb)],xx}
		elseif 编号 >= 4 and 编号 < 8 then
			wb[1] = "程老爷三板斧，你学到第几斧啦？"
			wb[2] = "能拜我们程老爷为师可是一种荣幸呢！"
			wb[3] = "府衙禁地，闲杂人等不得入内。"
			wb[4] = "这里是天子脚下，太平得很"
			wb[5] = "大唐首席弟子之争日趋白热。"
			return{"护卫","程府护卫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 1 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return{"护卫","传送护卫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
			local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="大唐官府" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"剑侠客","大唐官府护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==9 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "大唐官府" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.大唐官府.名称 and 首席争霸.大唐官府.模型 then
		    	return{首席争霸.大唐官府.模型,首席争霸.大唐官府.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"剑侠客","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end

		end
	elseif ID == 1054 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="大唐官府" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "程咬金" and 任务数据[任务id].人物地图 == 1054 then
				    wb[1] = "原来是你给俺送东西来了。怎么改行当镖师了？也罢，就让俺跟你讲讲怎么劫镖吧，当年俺可是瓦岗寨劫镖的第一先锋，劫了多少镖都没有失手过。哦？你现在在运镖？俺搞错了。俺当年劫的可都是贪官污吏的脏银。现在天下太平，你好好做吧。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "俺的武艺不想带进棺材里，是该找个传人的时候了"
				wb[2] = "一屋不扫何以天下？修身与治国平天下同等重要。"
				wb[3] = "俺老程的三板斧可是天下有名的。"
				wb[4] = "大唐武艺，天下无双。弟子们江湖行走可别坏了师门的名声"
				wb[5] = "做了官还要天天上朝，真是麻烦。"
				wb[6] = "学本领要虚心，可不能浮躁自满。"
				wb[7] = "学会俺的一身本领，闯荡江湖绰绰有余。"
			end
			return{"程咬金","程咬金",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID == 1204 then
	  if 编号 == 1 then
			  wb[1] = "小仙有一宝画，乃是上古流传下来的珍品，名曰《乾坤九曜图》。此画中有雾楼云阁，亭台水榭，青山秀水，蕊珠宫阙，只要轻轻将画卷展开，便可身临其境。各路神魔仙怪因为厌倦尘世杀戮而隐居在此，再也不问时世。天帝命丹青生将此画卷谨慎收藏，不再沾染红尘血腥。只可惜近日魔神将要现世，画中诸雄无不感到了他的怨念与仇恨而杀意萌动。他们忘却了修身养性的要诀，却没有忘记运用盖世的神功手段来杀戮他们看到的一切……于是，丹青生手执画卷，在此等待有缘的侠义之士，来化解他们的戾气。"
			  wb[2]= "侠士准备要做什么?"
			   xx = {"准备好了，你就等我们的好消息吧","我来一观《天地七元图》","我突然想起今天体力还没用完，等会再来"}
			   return{"男人_书生","丹青生",wb[取随机数(1,#wb)],xx}
			elseif 玩家数据[数字id].角色:取任务(630)~=0 then
			 	wb[1]="你的副本轮回境已经开启了，是否需要我帮你传送进去？"
				xx={"请送我进去","我等会再进去"}
			 return{"男人_书生","丹青生",wb[取随机数(1,#wb)],xx}
              end

	elseif ID == 1002 then
		if 编号 == 3 then
			wb[1] = "阁下我送你去长安吧"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_胖和尚","接引僧",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 9 then
			wb[1] = "你想帮助我们#Y化生寺#?想治好#G法明长老的伤#?\n如果你真的愿意帮忙那真是太好了\n听闻#S花果山#的#Y猴医仙#有妙手回春之法\n他配置的特效药#R定神香#一定可以治好#P法明长老"
			xx = {"我这就去问问"}
			return {"男人_胖和尚","慧海",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "得即是失，失即是得，世事本是过往烟云，不必太过看重"
			wb[2] = "不可说，不可说"
			wb[3] = "苦海无边，回头是岸"
			wb[4] = "无色无相，无嗔无狂"
			return {"男人_胖和尚","慧海",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "方丈又去碑林里净修了。"
			wb[2] = "不知道我什么时候才能熬到住持的位置。"
			wb[3] = "化瘀，还是疗伤？这是个问题。"
			wb[4] = "大悲无泪，大悟无言，大笑无声。"
			return {"男人_胖和尚","慧悲",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "少侠捐献银两可以增加人气。你需要捐献吗？"
			xx = {}
			return {"空度禅师","空慈方丈",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 1 then
			wb[1] = "心静则万事静，心清则万事清。"
			wb[2] = "这位施主是来拜师学艺的吗？请到藏经阁找空度禅师。"
			wb[3] = "谁能告诉我小乘佛法与大乘佛法到底有何差异#47？"
			wb[4] = "宁静而致远。"
			return {"男人_胖和尚","慧静",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "副本活动,找我开启"
		    xx = {"我要来帮忙","啊..我有些事先走"}
			if 玩家数据[数字id].角色:取任务(150)~=0 then
			    xx = {"送我过去帮忙","取消水陆副本任务","我有事先走一步"}
			    玩家数据[数字id].角色:刷新任务跟踪()
			end
			return {"男人_胖和尚","疥癞和尚",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="化生寺" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"逍遥生","化生寺护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==8 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "化生寺" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.化生寺.名称 and 首席争霸.化生寺.模型 then
		    	return{首席争霸.化生寺.模型,首席争霸.化生寺.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"逍遥生","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
    elseif ID == 1004 then
		    xx = {}
			if 编号 == 1 then
				if 玩家数据[数字id].角色:取任务(311) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(311)].进程 == 1 then
					  wb[1] = "嗝……徒儿啊，这宝莲法阵……怎的越补越乱？莫不是为师酒喝多了？"
					  xx = {"师父！快醒醒！你召的豆兵全被魔气侵染了！","我点错了"}
					  return {"雨师","太乙真人",wb[取随机数(1,#wb)],xx}
				else
					  return {"雨师","太乙真人","哎哟哟，这娃儿还可以哒",{}}
			    end
		    end

	elseif ID == 1005 then
		    xx = {}
			if 编号 == 1 then
				if 玩家数据[数字id].角色:取任务(311) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(311)].进程 == 2 then
					wb[1] = "哪吒……我若退让，龙族永世为囚；我若前进，你我将成死敌……"
					xx = {"灵珠之力，岂容你优柔寡断！","我点错了"}
			   		return {"龙太子","敖丙",wb[取随机数(1,#wb)],xx}
		        else
					return {"龙太子","敖丙","吾乃龙族三太子",{}}
			    end
		    end
	elseif ID == 1006 then
		    xx = {}
			if 编号 == 1 then
				if 玩家数据[数字id].角色:取任务(311) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(311)].进程 == 3 then
					wb[1] = "灵珠本应属我！尔等蝼蚁，也敢阻我逆天改命？！"
					xx = {"你的执念，早已化作心魔！","我点错了"}
					return {"狂豹人形","申公豹",wb[取随机数(1,#wb)],xx}
		        else
					return {"狂豹人形","申公豹","道友请留步",{}}
			    end
		    end
	elseif ID == 1007 then
		    xx = {}
			if 编号 == 1 then
				if 玩家数据[数字id].角色:取任务(311) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(311)].进程 == 4 then
					wb[1] = "陈塘关必须覆灭！这滔天巨浪，便是龙族的怒吼！"
					xx = {"老泥鳅，小爷的命硬得很，你淹不死！","我点错了"}
					return {"东海龙王","龙王",wb[取随机数(1,#wb)],xx}
		        else
					return {"东海龙王","龙王","吾乃龙族东海龙王",{}}
			    end
		    end
	elseif ID == 1008 then
		    xx = {}
			if 编号 == 1 then
				if 玩家数据[数字id].角色:取任务(311) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(311)].进程 == 5 then
				   	wb[1] = "我命由我？哈哈哈！天要灭我，我便焚了这天！"
				   	xx = {"哪吒！敖丙还在等你","我点错了"}
					return {"超级红孩儿","哪吒",wb[取随机数(1,#wb)],xx}
		        else
					return {"超级红孩儿","哪吒","吾乃天庭三大反骨仔",{}}
			    end
			end
	elseif ID == 1090 then

	elseif ID == 1009 then
		if 编号 == 1 then
		    wb[1] = "地宫内漆黑一片，且每一层都有不同类型的怪物尚未清剿完毕，你若是想要下去探查，必须携带火把方可！#R/(注:第一个达到100层并且成功封印蚩尤后重置地宫)"
			xx = {"开始探查","查看排行","玩法介绍","火把相关","购买火把","我要封印蚩尤","我点错了"}
			return {"男人_将军","雁塔地宫使者",wb[取随机数(1,#wb)],xx}
		    end
 	elseif ID == 1528 then
		if  编号 == 1 then
			wb[1] = "这法明长老是从金山寺过来的，你若有诉求，可直接找他。"
			return {"男人_胖和尚","慧明",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
		xx ={}
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 15 then
		wb[1] = "少侠,想必你已经降服#G白琉璃#了吧\n那请你归还#Y佛光舍利子#,我也好为你解开\n玄奘法师的#P身世之谜#11"
		xx = {"上交佛光舍利子"}
		return {"空度禅师","法明长老",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 8 then
		wb[1] = "少侠现在不是打听事情的时候\n不久之前有一个叫#G白琉璃#的女子\n抢走了化生寺的#Y佛光舍利子#,还打伤了我\n我现在需要休息,不想见客#66"
		xx = {"你灰溜溜的离开了"}
		return {"空度禅师","法明长老",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 12 then
		wb[1] = "这是#R定神香#吗?\n有了它我的#Y伤势#就可以马上恢复了\n真的很谢谢你,请把#S它交予我#吧#1"
		xx = {"上交定神香"}
		return {"空度禅师","法明长老",wb[取随机数(1,#wb)],xx}
		end
		wb[1] = "得即是失，失即是得，世事本是过往烟云，不必太过看重"
		wb[2] = "不可说，不可说"
		wb[3] = "施主是来上香的吗？"
		wb[4] = "随心、随缘、随性。"
		wb[5] = "一念愚即般若绝，一念智即般若生。"
		xx = {"我要学习打坐","我只是路过"}
		return {"空度禅师","法明长老",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1043 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
		    elseif 玩家数据[数字id].角色.数据.门派~="化生寺" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "空度禅师" and 任务数据[任务id].人物地图 == 1043 then
				    wb[1] = "这位檀越，既入寒寺，便与佛法是有缘之人。想不想停老衲讲讲佛经，老衲曾经受唐皇欶命，在长安街上开佛会当主讲，不饮不食，不眠不休，整整讲了七天七夜，至今这项记录还无人打破。一天的出场费就是几十万两银子，还是免税的。听我佛经，虎豹不伤，鬼怪不害，死者超生，福禄永在，不堕轮回，金身不坏……什么？你不想听只要钱赶快走？哎！看啦你跟佛法无缘。尘世中人心都被金钱迷惑了。真是可悲呀。阿弥陀佛！"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			else
				wb[1] = "我佛门心法只传授诚心向佛之士。"
				wb[2] = "学道有成，根本在悟。"
				wb[3] = "得即是失，失即是得，世事本是过眼烟云，不必太过看重。"
				wb[4] = "人生本在是非场，一生难免会有过。修真正道先修心，悟玄讲道渡世人。"
				wb[5] = "救人一命，胜造七级浮屠，这才是化生寺弟子天职所在。"
				wb[6] = "出家人要以慈悲为怀，善哉善哉。"
				wb[7] = "医生难医命终之人，佛陀难渡无缘的众生。"
			end
			return {"空度禅师","空度禅师",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID == 2008 then  -----九黎
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
		    elseif 玩家数据[数字id].角色.数据.门派~="九黎城" then
		    	xx={}
			end
			if not 玩家数据[数字id].角色.数据.月卡 or not 玩家数据[数字id].角色.数据.月卡.开通 then
				xx={}
			end
			wb[1] = "九黎城没有师傅，唯认兵主，九黎城亦无弟子，只有战士。"
			wb[2] = 玩家数据[数字id].角色.数据.名称 .. "，九黎城的未来系于尔身，切不可荒废任性。"
			wb[3] = "吾非魔域所生，九黎城赐予吾新生。刑天必使九黎荣耀再现，恢弘三界。"
			return {"刑天","刑天",wb[取随机数(1,#wb)],xx,"门派师傅"}
		 elseif 编号 == 2 then
			wb[1] = "让我用风送阁下前往长安吧。"
			xx = {"有劳风祖","暂且不用"}
			return {"风祖飞廉","风祖飞廉",wb[取随机数(1, #wb)],xx}
		elseif 编号 == 3  then
			wb[1] = "九黎城没有师傅，唯认兵主，九黎城亦无弟子，只有战士。"
			xx={}
			if 玩家数据[数字id].角色.数据.门派=="九黎城" then
				wb[1] = 玩家数据[数字id].角色.数据.名称 .. "，你可算来了，快来看看我新研究的铸兵术。"
			 	wb[2] = "我出生时还是个瘦小孱弱的魔物，要不是兵主，我早就葬身魔域了呜……"
				xx = {"武器转换铸斧(乾)","武器转换铸斧(坤)"}
			end
			if not 玩家数据[数字id].角色.数据.原始模型 and (玩家数据[数字id].角色.数据.门派=="" or 玩家数据[数字id].角色.数据.门派=="无"
				or  玩家数据[数字id].角色.数据.门派=="无门派" or 玩家数据[数字id].角色.数据.门派=="九黎城") then
				xx[#xx+1]="我要变成影精灵"
			elseif 玩家数据[数字id].角色.数据.原始模型 then
				xx[#xx+1]="我要换回原型"
			end
			xx[#xx+1]="铸斧还原武器"
			xx[#xx+1]="我就是来看看"
			if not 玩家数据[数字id].角色.数据.月卡 or not 玩家数据[数字id].角色.数据.月卡.开通 then
				wb[1] = "九黎城没有师傅，唯认兵主，九黎城亦无弟子，只有战士。"
				xx={}
			end
		 	return {"食铁兽","食铁兽",wb[取随机数(1, #wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="九黎城" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"影精灵","九黎城护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==5 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "九黎城" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.九黎城.名称 and 首席争霸.九黎城.模型 then
		    	return{首席争霸.九黎城.模型,首席争霸.九黎城.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"影精灵","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end

 	elseif ID == 1135 then
		if 编号 == 2 then
			wb[1] = "这里就是灵台方寸山，斜月三星洞。"
			wb[2] = "当年大闹天宫的齐天大圣就是在这里学艺的。"
			wb[3] = "菩提祖师管教很严厉，如果拜在他的门下，可要严于律己。"
			wb[4] = "金木水火土,急急如律令！"
			return {"男人_道士","觉明",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "听说菩提祖师在广招门徒，常有能人异士来拜师。"
			wb[2] = "若不回头，为何不忘？若已无缘，何必誓言！"
			wb[3] = "别离仿佛昨日，转眼却已经年。旧事依然，物是人非。"
			return {"女人_丫鬟","灵儿",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "方寸山远离红尘，清净雅致，正适合修道炼丹。"
			wb[2] = "方寸山以用符出名，方寸山的弟子人人都会画两手符。"
			wb[3] = "修道贵在专心，一心向道才能学有所成。"
			wb[4] = "由迷茫到觉悟的境界即是觉岸。可是苦海无边，哪里才是岸啊？"
			wb[5] = "是谁在此喧哗？打扰了我的清修#4"
			xx={"我想学习奇门遁甲","购买五色旗盒","我只是路过"}
			return {"男人_道士","觉岸",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 1 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_道童","接引道童",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="方寸山" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"飞燕女","方寸山护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==6 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "方寸山" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.方寸山.名称 and 首席争霸.方寸山.模型 then
		    	return{首席争霸.方寸山.模型,首席争霸.方寸山.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"偃无师","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
  	elseif ID == 1137 and 编号 == 1 then
  		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
		    elseif 玩家数据[数字id].角色.数据.门派~="方寸山" then
			   	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "菩提老祖" and 任务数据[任务id].人物地图 == 1137 then
				    wb[1] = "贫道稽首，从来处来，到去处去。施主的来意贫道已经知晓了。荒山小观，无以为谢。但有香茗一盏，鲜果数枚，施主可在此稍稍歇息。觉明、觉岸，看茶。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			else
				wb[1] = "难！难！难！道最玄，莫把金丹做等闲。不遇至人传妙诀，空言口困舌头干！"
				wb[2] = "天地玄黄修道德，宇宙洪荒炼元神；虎龙啸聚风云鼎，乌兔周旋卯酉晨。"
				wb[3] = "我方寸山的技艺只传授有缘之人"
				wb[4] = "修行贵在用心领悟，切忌轻浮自满。"
				wb[5] = "方寸何意，三星又何意，徒儿你可明白？"
				wb[6] = "修习之路没有捷径，踏平坎坷方能成大道。"
			end
			return {"菩提祖师","菩提老祖",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
  	elseif ID == 1142 then
		if 编号 == 2 then
			wb[1] = "这里的孙婆婆很和蔼的，弟子们有不明白的地方，她总是耐心教导。"
			wb[2] = "长安城真的像姑娘们说得那样繁花似锦么？真想哪天去看一看。"
			wb[3] = "这里就是远近闻名的女儿村，拜师的话请找孙婆婆。"
			wb[4] = "鸳鸯双栖蝶双飞，满眼春色惹人醉。"
			return {"女人_翠花","翠花",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "女儿村的姑娘们个个能歌善舞，我家女儿也一定要好好学学。"
			wb[2] = "最近村子里不太平啊，好多姑娘失踪，我女儿前天也不见了，真是急死了。"
			wb[3] = "女儿村的姑娘们个个生的俊俏，看着就让人喜爱啊！"
			wb[4] = "我的女儿是我唯一的依靠，我就盼着她能平平安安的生活，以后嫁个好人家。"
			return {"女人_王大嫂","栗栗娘",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "青琉璃女侠是我的恩人，我一直在想着报答她。"
			wb[2] = "最近村子里失踪了好几个姑娘，家里人都担心死了，特别是那个栗栗儿的娘，天天在村子附近寻找女儿，真是作孽啊。"
			wb[3] = "婆婆传授的法术是用来防身用的，可不是用来逞强的。"
			return {"普陀_接引仙女","柳飞絮",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "上次红线童子送我了个木偶，真好玩，不过只有一个是不是太孤独了。下次再叫他送我个，两个人就不孤独了。"
			wb[2] = "你有什么新奇的玩具啊，绿儿一个人好无聊哦！"
			wb[3] = "我的意中人是个盖世英雄，他要陪我玩丢手绢，嗯，还要把他的糖糖分给我吃！"
			return {"女人_绿儿","绿儿",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 1 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"女人_丫鬟","接引女使",wb[取随机数(1,#wb)],xx}
		 elseif 编号 == 7 then
		 	wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="女儿村" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"英女侠","女儿村护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==8 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "女儿村" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.女儿村.名称 and 首席争霸.女儿村.模型 then
		    	return{首席争霸.女儿村.模型,首席争霸.女儿村.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"英女侠","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		elseif 编号 == 6 then
			wb[1] = "上次红线童子送我了个木偶，真好玩，不过只有一个是不是太孤独了。下次再叫他送我个，两个人就不孤独了。"
			wb[2] = "你有什么新奇的玩具啊，绿儿一个人好无聊哦！"
			wb[3] = "我的意中人是个盖世英雄，他要陪我玩丢手绢，嗯，还要把他的糖糖分给我吃！"
			return {"女人_绿儿","翠儿",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1143 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="女儿村" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "孙婆婆" and 任务数据[任务id].人物地图 == 1143 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			else
				wb[1] = "我女儿村的技能只适合女弟子学习。"
				wb[2] = "不知道有多少弟子掌握了制出淬毒暗器的秘法？"
				wb[3] = "前几天教了村里的姑娘们一套法术，不知道修习的如何了。"
				wb[4] = "村里人口虽然不多，却个个都是貌美如花的绝世高手。"
				wb[5] = "修习贵在持之以恒，专心如始方能有所领悟。"
			end
			return {"孙婆婆","孙婆婆",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
   	elseif ID == 1138 then
		if 编号 == 2 then
			wb[1] = "远古的祖先留下训言，神木族将有三次灾祸，现如今巫神女之乱和虎魄之乱均已灵验，还有一次……"
			wb[2] = "咳咳，俺们神木林千百年来遵守于黄帝大人的约定，如今灾乱四起，不得不踏进江湖，这究竟是福还是祸……"
			return {"巫师","云中月",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "小奴好想去外面的世界看看，可是族长叔叔说小奴还太小，不能踏出神木林#52小奴好想快点长大！"
			return {"女人_云小奴","云小奴",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "#17三界如此多英雄侠士，不知道族长大哥是否允许我们对外联姻呢？"
			return {"女人_满天星","满天星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 1 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"巫师","引路族民",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="神木林" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"巫蛮儿","神木林护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==6 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "神木林" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.神木林.名称 and 首席争霸.神木林.模型 then
		    	return{首席争霸.神木林.模型,首席争霸.神木林.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"巫蛮儿","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
	elseif ID == 1154 then
		if 编号 == 1 then
			wb[1] = "大家往来神木之上，切记这里的一草一木，均有神灵在，不可以随便踩到花花草草哦！"
			wb[2] = "我神木林一派擅长操控自然之灵，天地万物均可化为己用，但谨记必须对神灵心存敬畏，方能运用自如。"
			wb[3] = "神木林千百年幽闭在武神坛之上，现在打开门户广纳门派，来来往往热闹了许多，突然有点不习惯#17"
			wb[4] = "我违背先祖遗训，打开了神木林的大门，让我族法传承出去，这究竟是对是错，全看徒儿你们是否真能为三界安危尽一份力了……"
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
  			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="神木林" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "巫奎虎" and 任务数据[任务id].人物地图 == 1154 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    return {"巫奎虎","巫奎虎",wb[1],xx,"门派师傅"}
			else
				return {"巫奎虎","巫奎虎",wb[取随机数(1,#wb)],xx,"门派师傅"}
			end

		end
   	elseif ID == 1513 then
		if 编号 == 1 then
			wb[1] = "盘丝洞几百年的规矩，不许男人入住，连收徒也只收女弟子。"
			wb[2] = "晶晶姑娘又发脾气了，把犯了门规的小妖挂在洞外七天七夜，差点咽了气。"
			wb[3] = "金琉璃最近老带着些人类女孩进进出出的，不知道在搞什么名堂。"
			wb[4] = "我已修行了千年，为何还未成仙？"
			wb[5] = "濯垢泉乃天然温泉，是姐妹们美容养颜的好去处#97"
			return {"芙蓉仙子","女妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "我一直想验证一下，佛光中炼成的宝物，是不是也可以用恶魔之心加以感化呢？"
			wb[2] = "我从何处而生？又要去往何处？"
			wb[3] = "为什么，为什么我总感觉冥冥之中有几个声音在耳边，而那声音又那么象我自己？"
			wb[4] = "什么？你找女儿村的小姑娘？不认识！我从未去过女儿村！！"
			return {"如意仙子","金琉璃",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "雨花桥边的芦花，不知是否已经飘飞？"
			wb[2] = "我娘找不到我一定担心死了。"
			wb[3] = "这是什么地方？我怎么会在这里？"
			wb[4] = "当我站在雨花桥边，觉得非常难过，我总觉得，应该是两个人站在这里。"
			wb[5] = "村里的姐妹都被妖怪抓去害死了，我好害怕"
			return {"女人_丫鬟","栗栗儿",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 or 编号 == 5 then
			wb[1] = "我们洞主就是人称魔界一枝花的性感又感性的美女～春十三娘"
			wb[2] = "最近总在附近发现一些人类的骸骨，不知道又是谁吃东西没清理好。"
			wb[3] = "金姑娘就喜欢在亭子那里用餐，说是那里风景好，吃东西就吃东西，还用选什么风景。"
			wb[4] = "想拜见我们洞主么？找我就对了！"
			wb[5] = "盘丝岭的夕阳远望可谓梦幻中最美的风景。"
			return {"树怪","看门小妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"蝴蝶仙子","引路小妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="盘丝洞" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"狐美人","盘丝洞护法",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1144 then
		if 编号 == 1 then
			xx={}
			wb[1] = "桃花过处，寸草不生。很多年之后,我有个绰号，叫做桃花娘子……"
			wb[2] = "我有一项不传绝技，叫作“催情大法”。"
			wb[3] = "师妹对那个臭猴子还是念念不忘。"
			wb[4] = "在出道的时候，我认识一个人，他叫孙悟空，他后来有个绰号，叫齐天大圣。"
			wb[5] = "我盘丝洞的法术可不是轻易能学到手的。"
			wb[6] = "现在的孙猴子早就不是五百年前那个孙猴子了！"
			wb[7] = "往前算五百年，往后算五百年，没人的美貌能超越我#99"
			return {"春十三娘","春十三娘",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "盘丝岭从不相信眼泪。"
			wb[2] = "师姐一直好像有什么心事。"
			wb[3] = "师妹对那个臭猴子还是念念不忘。"
			wb[4] = "要想在魔界扬名，还要多加历练才是啊。"
			wb[5] = "学本领要虚心，不可轻浮自满。"
			wb[6] = "今晚的月亮好亮，不知那猴子身在何处，可有想我？"
			wb[7] = "这里就是当年盘丝大仙修炼的地方。"
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="盘丝洞" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "白晶晶" and 任务数据[任务id].人物地图 == 1144 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    return {"白晶晶","白晶晶",wb[1],xx,"门派师傅"}
			else
				return {"白晶晶","白晶晶",wb[取随机数(1,#wb)],xx,"门派师傅"}
			end
		elseif 编号==3 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "盘丝洞" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.盘丝洞.名称 and 首席争霸.盘丝洞.模型 then
		    	return{首席争霸.盘丝洞.模型,首席争霸.盘丝洞.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"狐美人","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end

		end
 	elseif ID == 1131 then
		if (编号 == 1 or 编号 == 2) then
			wb[1] = "这里的三位大王各有一手看家本领，说出来怕会吓死你。"
			wb[2] = "我家的三个大王分别住在三个山洞里。"
			wb[3] = "加入我们狮驼岭，保证你有吃有喝，前途无量。"
			wb[4] = "我们狮驼岭的小妖，光是有名有姓的就有四万七八千。"
			wb[5] = "我家的三个大王神通广大，就是神仙来也得让着三分。"
			return {"雷鸟人","守山小妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"雷鸟人","传送小妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="狮驼岭" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"虎头怪","狮驼岭护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==5 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "狮驼岭" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.狮驼岭.名称 and 首席争霸.狮驼岭.模型 then
		    	return{首席争霸.狮驼岭.模型,首席争霸.狮驼岭.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"虎头怪","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
	-- 大象洞
	elseif ID == 1132 then
		if 编号 == 1 then
			wb[1] = "老牛又叫我去喝酒，好象九头虫也去了，我可不能让这酒虫把酒都喝光了才到。"
			wb[2] = "近来好象又招收了不少门徒，看来得扩充山头了。"
			wb[3] = "狮驼岭的武功博大精深，不是一两天就能领悟的，想要出人头地还需用心苦练。"
			wb[4] = "高级反震什么的，最讨厌了#47"
			return {"二大王","二大王",wb[取随机数(1,#wb)],xx}
		end
	-- 老雕洞
	elseif ID == 1133 then
		if 编号 == 1 then
			wb[1] = "听说佛祖挑选的天命取经人其中有一个是金蝉子转世，吃他一块肉能长生不老，真想尝尝啊。"
			wb[2] = "近来好象又招收了不少门徒，看来得扩充山头了。"
			wb[3] = "狮驼岭的武功博大精深，不是一两天就能领悟的，想要出人头地还需用心苦练。"
			wb[4] = "鹰击长空，能破敌军六将，狮驼弟子务必好好修习！"
			return {"三大王","三大王",wb[取随机数(1,#wb)],xx}
		end
	-- 狮王洞
	elseif ID == 1134 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
		    elseif 玩家数据[数字id].角色.数据.门派~="狮驼岭" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300)~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "大大王" and 任务数据[任务id].人物地图 == 1134 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			elseif 玩家数据[数字id].角色:取任务(307) ~=0 then
				local 任务id=玩家数据[数字id].角色:取任务(307)
				if 任务数据[任务id].分类==10 then
	        		local 随机三药 = Q_随机三级药[取随机数(1,#Q_随机三级药)]
	        		任务数据[任务id].药品=随机三药
	        		任务数据[任务id].分类=11
	        	    wb[1] = "我的事情太多了，忙不过来，少侠先帮我准备个#R"..随机三药.."#W回来，我再告诉你下一步任务。"
	        	    xx = {"好的，我这就去","路过"}
	        	elseif 任务数据[任务id].分类==11 then
	        		wb[1] = "少侠我需要的#R"..任务数据[任务id].药品.."#W找回来了吗？"
	        	    xx = {"我这就去","路过"}
	        	elseif 任务数据[任务id].分类~=nil then
	        		wb[1] = "你身上有坐骑任务,请先完成坐骑任务"
	        	    xx = {"我这就去","路过"}
	    		end
			else
				wb[1] = "我们这里神仙都不敢来的。"
				wb[2] = "最近来投靠的人越来越多，得想法子扩大山头了。"
				wb[3] = "学本领要专一，不能三心二意。"
				wb[4] = "要想在魔界扬名，还要多下苦功夫才行#2"
				wb[5] = "人不可貌相。别看我的弟子们相貌不够英俊，他们可都是温柔贴贴的好男人呢。"
				wb[6] = "要做得山大王，空有一身蛮力是不够的。"
			end
			return {"大大王","大大王",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
   	elseif ID == 1512 then
		if 编号 == 1 or 编号 == 2 then
			wb[1] = "我们寨主就是人称平天大圣的牛魔王！"
			wb[2] = "别看齐天大圣厉害，见到我们大王还不一样得叫大哥！"
			wb[3] = "我们大王正在和九头虫喝酒呢。"
			wb[4] = "加入魔王寨，保你有吃有喝，没人敢再欺负你。"
			wb[5] = "自从大王修炼出了五火神焰印，我们寨子一下就人丁兴旺起来了#89"
			return {"牛妖","守门牛妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"牛妖","传送牛妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="魔王寨" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"巨魔王","魔王寨护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==5 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "魔王寨" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.魔王寨.名称 and 首席争霸.魔王寨.模型 then
		    	return{首席争霸.魔王寨.模型,首席争霸.魔王寨.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"巨魔王","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
	elseif ID == 1145 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="魔王寨" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "牛魔王" and 任务数据[任务id].人物地图 == 1145 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx = {}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx = {}
				end
			else
				wb[1] = "家家有本难念的经，本王一世英明，如今却家事缠身，可叹啊！"
				wb[2] = "你们谁瞧见了本王的避水金睛兽？"
				wb[3] = "学本领要专心，不能三天打渔，两天晒网。"
				wb[4] = "想称霸江湖不是那么容易，要专心修行才行。"
			end
			return {"牛魔王","牛魔王",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
 	elseif ID == 1122 then
		if 编号 == 1 then
			wb[1] = "现在做鬼的也不安分，老是有出去闲逛的，你能帮我抓他们回来吗？"
			xx = {"好的 我帮你","取消 抓鬼任务","不，我没有空"}
			return {"男人_钟馗","钟馗",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "要想人不知，除非己莫为。做坏事的人可是要下地狱的。"
			wb[2] = "地府有十八层地狱，关押那些生前做尽坏事的家伙。"
			wb[3] = "想要尝尝我这跟铁链子的威力么？"
			return {"马面","马面",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "生是追梦人，死为追梦鬼。还记得当初的梦想么？"
			wb[2] = "地府有十八层地狱，关押那些前生坏事做尽的恶人。"
			return {"兔子怪","追梦鬼",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"僵尸","地遁鬼",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 19 then
			wb[1] = "你想要#Y孟婆汤#?你以为这玩意儿说给就给的吗?\n你不#R帮我做点事#,哪里会给你#S白嫖#呀\n地府有一个#G幽冥鬼#死活不肯投胎转世\n这尼玛不是难为我孟婆么,所以你得去帮我#P超度它"
			xx = {"当主角真累啊,都是跑腿的命"}
			return {"女人_孟婆","孟婆",wb[取随机数(1,#wb)],xx}
			end
		    if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 23 then
    		wb[1] = "少侠好本事,帮我解决了一#G大难题\n我这就兑现承诺,把#Y孟婆汤#给你\n拿着它赶紧去给#S玄奘#解毒吧#1"
    		xx = {"你接过孟婆汤"}
    		return {"女人_孟婆","孟婆",wb[取随机数(1,#wb)],xx}
    		end
			wb[1] = "相见不如怀念，怀念不如忘记。"
			wb[2] = "喝下孟婆汤，过了奈何桥，前生的事就再与你无缘。"
			wb[3] = "孟婆汤有甘、苦、辛、酸、咸五种口味，少侠想要哪一种？"
			wb[4] = "听说地藏菩萨在广招门徒，年轻人想不想去学些本领？"
			return {"女人_孟婆","孟婆",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_特产商人","地府商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 7 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_老财","地府货商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 8 then
    		wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="阴曹地府" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"骨精灵","阴曹地府护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==9 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "阴曹地府" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.阴曹地府.名称 and 首席争霸.阴曹地府.模型 then
		    	return{首席争霸.阴曹地府.模型,首席争霸.阴曹地府.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"骨精灵","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end

		end
	-- 森罗
	elseif ID == 1123  then
		if 编号 == 1 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 11 then
			wb[1] = "天英星并没有被贬下凡去,不过他也被天蓬连累,仙身都被#G杨戬#没收了!"
			xx = {"好的"}
			return {"男人_判官","判官",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "生死有命，富贵在天。拜师的请进内室，地藏菩萨正在招收门徒。"
			wb[2] = "阎王要你三更死，不敢留你过五更。"
			wb[3] = "人生切莫把心欺，神鬼昭彰放过谁？"
			wb[4] = "左执生死簿，右拿勾魂笔，赏善罚恶，管人生死——这说的就是老夫我啦#17"
			return {"男人_判官","判官",wb[取随机数(1,#wb)],xx}
		else
			local name = ""
		    if 编号 == 2 then
		    	wb[1] = "我掌第五殿，司掌叫唤大地狱。凡押解到此殿者，押赴望乡台，令之闻见世上本家因罪恶遭殃各事。"
		    	name = "阎罗王"
		    elseif 编号 == 3 then
		    	wb[1] = "我掌第十殿，专司各殿解到鬼魂。辨明善恶，核定等级，发往转生。"
		    	name = "转轮王"
		    elseif 编号 == 4 then
		    	wb[1] = "我掌第一殿，司人世天寿生死，统管幽冥吉凶。"
		    	name = "秦广王"
		    elseif 编号 == 5 then
		    	wb[1] = "我掌第二殿，司掌活大地狱。凡在阳间伤人肢体，奸盗杀生者，推入此狱。"
		    	name = "初江王"
		    elseif 编号 == 6 then
		    	wb[1] = "我掌第三殿，司掌黑蝇大地狱。凡是人抗粮赖租，交易狡诈者，推入此狱。"
		    	name = "宋帝王"
		    elseif 编号 == 7 then
		    	wb[1] = "我掌第六殿，司掌打叫唤大地狱及枉死城。凡世人怨天尤地，对北溺变涕泣者，推入此狱。"
		    	name = "卞城王"
			elseif 编号 == 8 then
		    	wb[1] = "我掌第九殿，司掌打叫酆都城铁网阿鼻地狱。凡阳世杀人放火，战绞正法者，解到本殿。"
		    	name = "平等王"
			elseif 编号 == 9 then
		    	wb[1] = "我掌第七殿，司掌热恼地狱。凡阳世取骸合药，离人至戚者，推入此狱。"
		    	name = "泰山王"
		    elseif 编号 == 10 then
		    	wb[1] = "我掌第八殿，司掌大热恼大地狱，凡谢世不孝，使父母翁姑愁闷懊恼者，发入此狱。"
		    	name = "都市王"
		    elseif 编号 == 11 then
		    	wb[1] = "我掌第四殿，司掌合大地狱。凡阳世忤逆尊长，教唆兴讼者，推入此狱。"
		    	name = "忤官王"
		    end
		    wb[2] = "人世沉浮，根本寂灭。"
		    wb[3] = "天网恢恢，疏而不漏。"
		    wb[4] = "善恶到头，神鬼难欺。"
		    wb[5] = "天理昭彰，报应不爽。"
		    wb[6] = "因果循环，六道轮回。"
		    wb[7] = "临阵无退，杀身有择。"
		    return {"阎罗王",name,wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1124 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="阴曹地府" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "地藏王" and 任务数据[任务id].人物地图 == 1124 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "地狱不空，誓不成佛。"
				wb[2] = "修行贵在持之以恒，切忌浮躁自满。"
				wb[3] = "地府法术诡异玄妙，只传授有缘之人。"
				wb[4] = "地府弟子学有所成是师父我最大的心愿。"
				wb[5] = "要想在魔界扬名，还要多加历练才是啊。"
				wb[6] = "恶业将盈，地狱相现。"
			end
			return {"地藏王","地藏王",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
  	elseif ID == 1125 then
    	if 编号 == 1 then
      		wb[1] = "我可以帮你传送到长安城，你是否需要我对你进行传送？"
      		xx={"请送我过去","我暂时不想去"}
      		return {"白无常","白无常",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 2 then
      		wb[1] = "地狱里头的那些鬼王已经无法镇压了，现在正在四处祸害人间。可惜仅凭我一己之力无法将他们全都收服。"
     		--xx={"我怕鬼，再见"}
     		xx={"我们来帮你","我怕鬼，再见"}
      		return {"黑无常","黑无常",wb[取随机数(1,#wb)],xx}
     	end
  	elseif ID == 1139 then
		if 编号 == 1 then
			wb[1] = "欢迎你来到神秘而美丽的无底洞#86"
			wb[2] = "我们无底洞里还有很多神秘的故事呢#86"
			wb[3] = "我最喜欢这些红花了，闻起来香喷喷的~~"
			return {"幽萤娃娃","璎珞",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "我们似乎已经在此处待了很久很久了。"
			wb[2] = "红莲那丫头的性子，还是那么莽撞冲动，真是伤脑筋啊#83"
			wb[3] = "能来到这里，真是一段难得的缘分呢……"
			return {"修罗傀儡妖","墨衣",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "我这么年轻美丽，一定是妹妹啦，你们说对不对#86"
			wb[2] = "你猜墨衣是我的姐姐还是我的妹妹#110"
			wb[3] = "其实我有点害怕姐姐呢，这么多年了，她一直都是那么严厉，比地涌夫人还凶#17"
			return {"修罗傀儡妖","红莲",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"幽萤娃娃","接引小妖",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="无底洞" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"杀破狼","无底洞护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==6 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "无底洞" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.无底洞.名称 and 首席争霸.无底洞.模型 then
		    	return{首席争霸.无底洞.模型,首席争霸.无底洞.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"鬼潇潇","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
	elseif ID == 1156 then
		if 编号 == 1 then
			wb[1] = "不要叫我师父，请叫我女王大人。"
			wb[2] = "要想领悟我无底洞技能，可得勤学苦练。"
			wb[3] = "我的徒儿们，每一个都那么聪明伶俐。"
			wb[4] = "如今，像我这么美貌与智慧并重的好师傅可不多了"
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="无底洞" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "地涌夫人" and 任务数据[任务id].人物地图 == 1156 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    return {"地涌夫人","地涌夫人",wb[1],xx,"门派师傅"}
			else
				return {"地涌夫人","地涌夫人",wb[取随机数(1,#wb)],xx,"门派师傅"}
			end

		elseif 编号 == 2 then
			wb[1] = "地涌姐姐说人家和她小时候长得很像呢#86"
			wb[2] = "有时候我也觉得怪孤单的，就去外面找璎珞一起玩儿#90"
			return {"女人_灵鼠娃娃","灵鼠娃娃",wb[取随机数(1,#wb)],xx}
		end
   elseif ID == 1117 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
      			xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
    		elseif 玩家数据[数字id].角色.数据.门派~="龙宫" then
      			xx={}
    		end
        	if 玩家数据[数字id].角色:取任务(300) ~=0 then
        	    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "东海龙王" and 任务数据[任务id].人物地图 == 1117 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
        	else
        		wb[1] = "龙宫里有数不尽的宝贝，有机缘之人方能得到。"
				wb[2] = "龙族的法术玄妙精深，要苦心修习方能领悟。"
				wb[3] = "修行贵在用心领悟，切忌轻浮自满。"
				wb[4] = "要想成为仙界的精英，是要下一番苦功夫的。"
				wb[5] = "遇到大唐弟子可千万别跟他们蛮干，切记。"
				wb[6] = "为人间降雨是老夫的职责所在。"
        	end
        	return {"东海龙王","东海龙王",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID == 1116 then
		if 编号 == 1 then
			wb[1] = "我可以送你去#R/东海湾#W/，你要不要去呢？"
			xx = {"是的 我要去","我还有逛逛"}
			return {"虾兵","虾兵",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			local 首杀名称=首杀记录.蟹将军
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 32 then
			wb[1] = "这货肯定是偷#S定颜珠#的#R贼人#,虾兵蟹将们#Y给我拿下!#4\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			xx = {"卧槽不分青红皂白就打我啊!"}
			return {"蟹将","蟹将军",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "有志的仙族青年应该投奔我龙宫，及早谋个好前程啊。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[2] = "最近宫里丢了颗定颜珠，千岁正发愁呢。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[3] = "海底地形复杂，当心可别迷了路。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[4] = "一只羊在吃草，一只狼在旁边过，但没吃羊，少侠可知这个谜语说的是谁#40\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[5] = "前几日一只螃蟹爬出蒸锅说“我热”，我愤怒的教导他，想红就给我忍着点#4\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
      		return {"蟹将","蟹将军",wb[取随机数(1,#wb)],xx}
		elseif (编号 == 3 or 编号== 7 or 编号 == 9 or 编号 == 10 or 编号 == 12 or 编号 == 13 or 编号 == 15) then
			wb[1] = "龙宫的宝贝真是多啊，随便偷得一两件拿到外面来卖都能发笔横财。"
			wb[2] = "瞧一瞧看一看了，东海龙宫宝贝大展销啦#51"
			wb[3] = "三界之间的恩怨情仇，从来就和我们这种小角色无关#109"
      		return {"虾兵","虾兵",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 33 then
			wb[1] = "非常感谢,你带回了#S定颜珠\n你所问之人#Y陈光蕊#,十八年前我们确实有救过\n但他伤好之后,自己就#G回到岸上去了\n具体去向我们也不知#17"
			xx = {"好吧,我先回去了"}
			return {"龟丞相","龟千岁",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "大家叫我龟千岁，可我看起来还是那么年轻吧？"
			wb[2] = "在海里呆得久了，想到陆地上走走。"
			wb[3] = "龙族的法术玄妙无比，要苦心修习方能领悟。"
			wb[4] = "龙宫里有数不尽的宝贝，有机缘之人才能得到。"
			wb[5] = "生蚝肉怎么这么好吃#89 "
			wb[6] = "年纪越大，越容易犯低级错误#76"
      		return {"龟丞相","龟千岁",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "我爹爹只不过改了下雨的时辰点数就被斩首了，真不甘心！"
			wb[2] = "我一定要用袁守诚的人头祭祀我父王。"
      		return {"小龙女","小龙女",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "有志青年应该投奔我龙宫，及早谋个好前程啊。"
			wb[2] = "整天就是在这里看门，真是没什么意思，不过真让我休息的话我也还是找个地方待着睡觉。"
			wb[3] = "千年王八万年龟，我今年已经一万零八岁了。"
			wb[4] = "学游泳，找我就对了，价格实惠，包学包会#39"
			wb[5] = "这太尉不过是个有名无实的闲职罢了！"
    		return {"龟丞相","龟太尉",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 11 then
			wb[1] = "有志青年应该投奔我龙宫，及早谋个好前程啊。"
			wb[2] = "我这出去就是海底的迷宫了，里面有不少怪物，最好结伴而行。"
			wb[3] = "没事别老找我说话，找那老龟吧，他喜欢侃。"
			wb[4] = "嘿哟嘿哟！强身健体，为我龙宫健康工作一万年！"
			wb[5] = "真的蛤蟆勇士，敢于直面先天的缺陷。"
    		return {"蛤蟆精","蛤蟆勇士",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 14 then
			wb[1] = "有志青年应该投奔我龙宫，及早谋个好前程啊。"
			wb[2] = "海底地形复杂，当心可别迷了路。"
			wb[3] = "最近宫里丢了颗定颜珠，千岁正发愁呢。"
			wb[4] = "论智慧我比蟹将军高那么一点点，可是他比我多了好多手脚，所以比我升的快。"
			wb[5] = "八只脚，抬面鼓，两把剪刀鼓前舞，生来横行又霸道，嘴里常把泡沫吐。少侠猜猜是谁？#86"
			wb[6] = "不想当龙虾的虾不是好虾#40"
	      	return {"虾兵","虾将军",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 16 then
			wb[1] = "小白龙真是个傻瓜，本公主略施小计就把他整的一点脾气也没有。"
			wb[2] = "九头虫怎么还不回来，不知道事情办妥了没有。"
			wb[3] = "龙族的法术玄妙无比，要苦心修习方能领悟。"
			wb[4] = "龙宫纵有千万般好处，可还是比不上我的乱石山碧波潭。"
    		return {"女人_万圣公主","万圣公主",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
    		return {"蟹将","传送蟹将",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 17 then
    		wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="龙宫" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"龙太子","龙宫护法",wb[取随机数(1,#wb)],xx}

		elseif 编号==18 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "龙宫" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.龙宫.名称 and 首席争霸.龙宫.模型 then
		    	return{首席争霸.龙宫.模型,首席争霸.龙宫.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"龙太子","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
  	elseif ID == 1140 then
		if 编号 == 2 then
			wb[1] = "这里就是普陀山紫竹林了。"
			wb[2] = "你也是来拜见大慈大悲观世音菩萨的吗？#18"
			wb[3] = "菩萨最近正在招收徒，只有女性的仙族才收哦#0"
			wb[4] = "元宵节要到了，谁能陪我去长安城观灯？"
			wb[5] = "紫竹林风光无限好，我再也不愿回到水晶宫了。"
			return {"小龙女","龙女宝宝",wb[取随机数(1,#wb)],xx}
		elseif  编号 == 3 then
			wb[1] = "这里是观音大士清修之地，闲杂人等不得乱闯。"
			wb[2] = "比起暗无天日的黑风山，这里可真是清明自在之地啊。#18"
			wb[3] = "跟着观音姐姐，我总会有成仙的那天#89"
			return {"黑熊精","黑熊怪",wb[取随机数(1,#wb)],xx}
		elseif  编号 == 1 then
			wb[1] = "我可以送你去长安，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"普陀_接引仙女","接引仙女",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="普陀山" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"玄彩娥","普陀山护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==5 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "普陀山" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.普陀山.名称 and 首席争霸.普陀山.模型 then
		    	return{首席争霸.普陀山.模型,首席争霸.普陀山.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"玄彩娥","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
	-- 潮音洞
	elseif ID == 1141 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
			  xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
		    elseif 玩家数据[数字id].角色.数据.门派~="普陀山" then
		    	xx={}
  			end
  			if 玩家数据[数字id].角色:取任务(300) ~=0 then
  			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "观音姐姐" and 任务数据[任务id].人物地图 == 1141 then
				    wb[1] = "这位施主，既入我寒山，便是有缘之人。我这里风景还算清幽，可下去看看吧。（自言自语）哎。这些百姓，许愿也多，布施又少，谁都知道我是救苦救难的大观世音菩萨，穷苦人的保护神，做这些事情功德也大，但是棘手呀。这些人呀，没事情老上什么访！"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
  			else
  				wb[1] = "观世间疾苦繁华，身身入耳，一一在心。"
				wb[2] = "我南海普陀山有救世正心之法，只传授给有缘之人。"
				wb[3] = "心欲若除，则万事可成，心无杂念，非外事可扰。"
				wb[4] = "佛祖有真经三藏，乃是修真之经，正善之门，可劝人为善。"
				wb[5] = "修行贵在持之以恒，切忌浮躁自满。"
  			end
  			return {"观音姐姐","观音姐姐",wb[取随机数(1,#wb)],xx,"门派师傅"}
		elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 18 then
			wb[1] = "少侠,#Y观音姐姐#早就知道你的来意了,让我在此恭候多时了\n这是#S九转回魂丹#你拿好,不过要想解#G玄奘的毒\n必须得和#P孟婆汤#一起服用,看来你得跑#R地府#一趟了!"
			xx = {"多谢美女姐姐"}
			return {"普陀_接引仙女","青莲仙女",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 6 then
			wb[1] = "少侠我们又见面了!"
			xx = {"触发剧情"}
			return {"普陀_接引仙女","青莲仙女",wb[取随机数(1,#wb)],xx}
			end
		    if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 7 then
			wb[1] = "少侠我们又见面了!"
			xx = {"上交火凤之睛"}
			return {"普陀_接引仙女","青莲仙女",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "普陀山是观音姐姐清修之地，紫气蒸腾，烟围雾笼，景色可是十分奇秀。"
			wb[2] = "大慈与一切众生乐，大悲与一切众生苦。"
			wb[3] = "很多怪病奇毒只有仙家灵丹可以医治。"
			wb[4] = "欲朝普陀山，必度莲花池，穿过前面的莲池，便可见到观音姐姐了。"
			return {"普陀_接引仙女","青莲仙女",wb[取随机数(1,#wb)],xx}
		end
 	elseif ID == 1111 then
		if 编号 == 1 then
			wb[1] = "我可以送你去西牛贺洲，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"天兵","守门天将",wb[1],xx}
		elseif 编号 == 2 then
			local 首杀名称=首杀记录.守门天兵
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 13 then
			wb[1] = "你是何人?竟敢擅闯#Y南天门#,还不速速#G伏法#!\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			xx = {"卧槽,这就开干了?"}
			return {"天兵","执法天兵",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "天天在这里守门真是无聊死了，我也渴望冒险刺激的生活。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[2] = "整天有人来打听这打听那的，什么红琉璃白琉璃，当心我火了打你一顿。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[3] = "又要开蟠桃会了，我算算我守了多少年的门，自从我守门开始，已经开了2次蟠桃会了。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[4] = "天宫可是众仙云集之地，不是你想来就来，想走就走的！\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			wb[5] = "天宫重地，严禁喧哗！！\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			return {"天兵","执法天兵",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 then
			wb[1] = "天天在这里守门真是无聊死了，我也渴望冒险刺激的生活。"
			wb[2] = "整天有人来打听这打听那的，什么红琉璃白琉璃，当心我火了打你一顿。"
			wb[3] = "又要开蟠桃会了，我算算我守了多少年的门，自从我守门开始，已经开了2次蟠桃会了。"
			wb[4] = "天宫可是众仙云集之地，不是你想来就来，想走就走的！"
			wb[5] = "天宫重地，严禁喧哗！！"
			return {"天兵","守门天兵",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "我可以送你去长安，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"芙蓉仙子","接引仙女",wb[1],xx}
		elseif 编号 == 5 then
			wb[1] = "我是三界闻名的千里眼，什么事情都逃不过我的眼睛。"
			xx ={}
			local 任务id = 玩家数据[数字id].角色:取任务(307)
        	if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==3 then
	        	    wb[1] = "好啦，看你这么善良正直。不开玩笑了，你帮我捎个信给大力神灵，我去帮你调查调查。"
	        	    xx = {"好的，我这就去","路过"}
	        	elseif 任务数据[任务id].分类==5 then
	        		wb[1] = "朱紫国一带有一座麒麟山，寻那附近的土地公公查探查探。"
	        	    xx = {"好的，我这就去","路过"}
        		end
        	end
			return {"天兵","千里眼",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 6 or 编号==7 then
			wb[1] = "我就一看门的小屁孩#24"
			return {"男人_道童","守门道童",wb[取随机数(1,#wb)],{"我点错了"}}
    	elseif 编号 == 8 then
			wb[1] = "我是三界闻名的顺风耳，什么事情都逃不过我的耳朵。"
			return {"天兵","顺风耳",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 9 then
			wb[1] = " 请选择你要进行的操作："
			xx={"领取宠物修炼任务","跳过宠物修炼任务","取消宠物修炼任务","我只是路过"}
			return {"男人_道士","马真人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 10 then
			wb[1] = "天牢里关的都是十恶不赦的坏人。"
			return {"天将","天牢守卫",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 11 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 10 then
			wb[1] = "掌管十万水兵虽然威风，但不知什么时候我也会被玉帝找个理由打入天牢，真是伴君如伴虎。"
			xx = {"触发剧情"}
			return {"男人_将军","水兵统领",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "掌管十万水兵虽然威风，但不知什么时候我也会被玉帝找个理由打入天牢，真是伴君如伴虎。"
			return {"男人_将军","水兵统领",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 12 then
			wb[1] = "明天就要行刑了，我居然把玄天铁鞭丢了……这可是大罪啊，如果被玉帝知道说不定会说我包庇罪犯贬下凡间像天蓬元帅一样投个猪胎说不定连猪都不如~~~~~~~啊~~~~"
			xx = {}
			local 任务id = 玩家数据[数字id].角色:取任务(307)
        	if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==4 then
	        	    wb[1] = "#04什么？千里眼，那丫的还欠我钱呢，火大！让你走不走，打你走！"
	        	    xx = {"看我不好好的收拾你！","路过"}
        		end
        	end
        	return{"风伯","大力神灵",wb[取随机数(1,#wb)],xx}
         elseif 编号 == 13 then
        	wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="天宫" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"舞天姬","天宫护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==14 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "天宫" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.天宫.名称 and 首席争霸.天宫.模型 then
		    	return{首席争霸.天宫.模型,首席争霸.天宫.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"舞天姬","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end

		elseif ID== 6035 then --梦战兜率宫
		if 编号 == 1 then
	    wb[1] = "111"
		xx = {"接桃园任务","接建邺任务","接玄奘身世","接取新手任务","取消桃园任务","取消建邺任务","取消玄奘身世","取消新手指引"}
		return{"龙太子","秋风的回忆",wb[取随机数(1,#wb)],xx}
		end



  	elseif ID == 1112 then
		if 编号 == 1 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 12 then
			wb[1] = "等等,别打断本王思路,你,嗯嗯,看你骨骼精奇,想必也是武学奇才,来来来,和犬子切磋切磋,赢了我就将#G天英星#给你!"
			xx = {"开启剧情战斗"}
			return {"男人_二郎神","杨戬",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "下界有妖魔作乱，要多加防范才是。"
			wb[2] = "不知道嫦娥仙子如何喜欢我，唉，真是苦恼啊#14"
			wb[3] = "天庭也非清净之地，啥时能再回到我的灌江口？"
			wb[4] = "我就是英俊潇洒玉树临风天庭第一美男子二郎神#17"
			return {"男人_二郎神","杨戬",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
	        if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
	          xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	        elseif 玩家数据[数字id].角色.数据.门派~="天宫" then
	          xx={}
	        end
		    if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "李靖" and 任务数据[任务id].人物地图 == 1112 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "下界凡间遇到纷争可不要与他们一般见识，他们可是不讲理的。"
				wb[2] = "下界有妖魔作乱，要多加防范才是。"
				wb[3] = "天庭冷落，真想再回到人间。"
				wb[4] = "想成为天界的精英，是要下一番苦功夫的。"
				wb[5] = "修行贵在持之以恒，切记浮躁自满。"
			end
			return {"李靖","李靖",wb[取随机数(1,#wb)],xx,"门派师傅"}
		elseif 编号 == 3 then
			xx = {}
			wb[1] = "作皇帝难，作玉皇大帝更难啊。"
			wb[2] = "那个什么九头虫献上的宝贝还真不错。"
			wb[3] = "其实我很怕我老婆的，很多事情都是她做主，但是话说回来，怕她，说明我爱她嘛#17你说是不是。"
			wb[4] = "那猴子自从跟了唐三藏取经，好久都没消息了，也不知道现在行至何处？"
			return {"男人_玉帝","玉皇大帝",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 4 then
			wb[1] = "我蟠桃园里的桃子可都是稀世珍品。"
			wb[2] = "谁这么大胆，偷走了我的九叶灵芝草#51"
			wb[3] = "蟠桃园的桃子长势喜人，今年又可以开一场盛大的蟠桃宴会了。"
			wb[4] = "猴子被佛祖降服以后，天界的日子总算太平了。"
			wb[5] = "仙魔两界向来水火不容，说什么神仙妖魔自由恋爱，哼，除非神仙都死光了。"
			return {"女人_王母","王母娘娘",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "居然把火尖枪输给那个看门小神，那是师傅送我的枪啊#52我有何颜面去见师傅和父亲"
			wb[2] = "作为三坛海会大神，率领天兵去收降那猴子，却屡战屡败，真是惭愧！"
			wb[3] = "下界有妖魔作乱，要多加防范才是。"
			wb[4] = "天上一日，人间一年。下界许久没什么大动静，我待在这都快生锈了。"
			wb[5] = "莲藕为骨，荷叶为衣，吾乃刀枪不入之躯。"
			return {"男人_哪吒","哪吒",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1113 then
		if 编号 == 1 then
			wb[1] = "我的事情太多了，忙不过来，请优先选择一件你要做的事情吧。"
			xx = {}
			return {"男人_太上老君","太上老君",wb[取随机数(1,#wb)],xx}
    elseif 编号 == 2 then
			wb[1] = "仙丹能治百病，却治不了贪婪的人心。"
			wb[2] = "当年有志学长生，今日方知道行精。运动乾坤颠倒理，转移日月互为明。"
			xx = {}
			return {"男人_道士","炼丹道士",wb[取随机数(1,#wb)],xx}
    elseif 编号 == 3 then
			wb[1] = "我这里可以进行法宝合成，也可以花费银子快速补回法宝灵气，法宝收费200万银子，你需要使用这项功能吗？"
			xx = {"开启法宝任务","请帮我进行法宝合成","请帮我取消法宝任务","请告诉我如何获得内丹","取消法宝内丹任务","完成法宝任务"}
			return {"男人_道童","金童子",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1114 then
		if 编号 == 1 then
			wb[1] = "目前来说以你的修为已经到头了，如果想百尺竿头，更上一步的话，除非能入于化境！"

			xx = {"什么是化境！","我想入此境，请上仙指点一二","我只是路过"}

			return {"吴刚","吴刚",wb[取随机数(1,#wb)],xx}
    elseif 编号 == 2 then
			wb[1] = "纵有端正没人姿，又有谁能知道广寒宫的寂寞？"
			wb[2] = "做错的事，能否重来呢？"
			xx = {}
			return {"女人_满天星","嫦娥",wb[取随机数(1,#wb)],xx}
    elseif 编号 == 3 then
			wb[1] = "我们老大就是人称玉面无敌的多情公子二郎神，怎么样，怕了吧！"
			xx = {}
			return {"天兵","康太尉",wb[取随机数(1,#wb)],xx}
		end
  	elseif ID == 1150 then
		if 编号 == 1 then
			wb[1] = "能给我杨戬当徒儿的，都是人中龙凤。"
			wb[2] = "想听故事找你们那六个师叔去，为师忙得很！"
			wb[3] = "好久没和孙悟空那小子比试比试了，真是寂寞如雪啊。"
			wb[4] = "为师教给你的东西可都学会了？"
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
        		xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
      		elseif 玩家数据[数字id].角色.数据.门派~="凌波城" then
        		xx={}
      		end
      		if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "二郎神" and 任务数据[任务id].人物地图 == 1150 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
	      	end
	      	if 玩家数据[数字id].角色:取任务(300) ~=0 then
				return {"二郎神","二郎神",wb[1],xx,"门派师傅"}
			else
				return {"二郎神","二郎神",wb[取随机数(1,#wb)],xx,"门派师傅"}
			end
		elseif 编号 == 2 then
			wb[1] = "啊呜啊呜……汪汪汪！"
			wb[2] = "天庭里那个二郎神长得好像和主人不是很像……"
			wb[3] = "跟随主人出生入死这么多年，我们一起经历了许多事情，不知道你想听我从那一件讲起呢？"
			return {"哮天犬","哮天犬",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 3 or 编号 == 4 then
			wb[1] = "你整理一下形象再来进门，你这蓬头垢面的成何体统#4"
			wb[2] = "少侠，你没走错，这就大名鼎鼎，鼎鼎大名的凌波城了。"
			wb[3] = "喂……都说了没走错了，你怎么还不进门啊#4"
			return {"天兵","守门天将",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
			wb[1] = "当年，真没想到跟大哥混还这么有前途#17"
			wb[2] = "哮天犬这小子最不厚道了，每次都是他给大哥惹麻烦#83"
			wb[3] = "我觉得大哥的优点就是长得帅#17"
			return {"狂豹人形","荒芜星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
			wb[1] = "真不明白大哥为什么和那个死猴子称兄道弟的#4"
			wb[2] = "天庭里的繁文缛节实在令人生厌#4"
			wb[3] = "天庭里的繁文缛节实在令人生厌#4"
			return {"鲛人","刀砧星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 9 then
			wb[1] = "我最喜欢的就是灌江口的涛声。"
			wb[2] = "小螃蟹爬来爬去，好像也挺开心的呢#86"
			wb[3] = "一晃好像过了很多年了，但是三界还是乱糟糟的……"
			return {"羊头怪","反吟星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 10 then
			wb[1] = "#75知不知道，能给大哥当徒弟，是你三生有幸！"
			wb[2] = "能跟大哥闯荡，真是畅快！"
			wb[3] = "天下之大，大哥要是称第二，就没人称第一#89"
			return {"犀牛将军人形","天瘟星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 11 then
			wb[1] = "这江水哗啦啦的声音最是扰人清梦#83"
			wb[2] = "小螃蟹爬来爬去真是烦死人了。"
			wb[3] = "好久都没去看外面的世界了，感觉有点寂寞啊#52"
			return {"野猪精","伏断星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 6 then
			wb[1] = "龙宫家的姑娘，长的还是挺好看的#17"
			wb[2] = "想知道你师父和龙宫家姑娘的故事么？问我就对啦！"
			wb[3] = "你要是真问我发生了什么，我也只能说个大概……"
			return {"百足将军","破碎星",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 5 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"天兵","传送天将",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 12 then
			wb[1] = "请先前往长安城门派闯关使者处领取任务。"
			xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="凌波城" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"羽灵神","凌波城护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==13 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "凌波城" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
			if 首席争霸.凌波城.名称 and 首席争霸.凌波城.模型 then
		    	return{首席争霸.凌波城.模型,首席争霸.凌波城.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"羽灵神","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end
		end
  	elseif ID == 1146 then
		if 编号 == 1 then
			wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_道童","传送道童",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 5 then
			wb[1] = "你找我有何事?"
			xx = {"触发剧情"}
			return {"男人_道童","清风",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 8 then
			wb[1] = "你找我有何事?"
			xx = {"触发后续"}
			return {"男人_道童","清风",wb[取随机数(1,#wb)],xx}
			end
			wb[1] = "你找我有何事?"
			return {"男人_道童","清风",wb[取随机数(1,#wb)],xx}
         elseif 编号 == 3 then
            wb[1] = "请先前往长安城门派闯关使者处领取任务。"
		    xx = {}
		    local 任务id = 玩家数据[数字id].角色:取任务(107)
			if 任务id~=0 then
				 local 门派名称 = 任务数据[任务id].闯关序列[任务数据[任务id].当前序列]
				 if 门派名称=="五庄观" then
				       wb[1] ="你们是否已经做好了接受考验的准备？"
				       xx={"请出招吧","我再准备准备"}
				  else
				      wb[1]="你当前应该前往#G/"..门派名称.."#W/接受考验。"
				  end
			 end
		    return{"神天兵","五庄观护法",wb[取随机数(1,#wb)],xx}
		elseif 编号==4 then
			xx = {}
			if 玩家数据[数字id].角色.数据.门派 ~= "五庄观" then
		        wb[1] = "你不是本门弟子，是否有要紧的事情。"
		    else
		        wb[1] = "我是首席弟子，你找我什么事情？"
		        wb[2] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
		        xx = {"我要挑战你","我只是来看看"}
		    end
		    if 首席争霸.五庄观.名称 and 首席争霸.五庄观.模型 then
		    	return{首席争霸.五庄观.模型,首席争霸.五庄观.名称,wb[取随机数(1,#wb)],xx}
		    else
		   	 	return{"神天兵","首席大弟子",wb[取随机数(1,#wb)],xx}
		   	end

		end
	elseif ID == 1147 then
		if 编号 == 1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
	        	xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
	      	elseif 玩家数据[数字id].角色.数据.门派~="五庄观" then
	        	xx={}
	      	end
		    if 玩家数据[数字id].角色:取任务(300) ~=0 then
				local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "镇元子" and 任务数据[任务id].人物地图 == 1147 then
				    wb[1] = "少侠幸苦了，这是酬劳请收下。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "学了本领要用在正途，不许恃强凌弱。"
				wb[2] = "修行要注重基础，持之以恒，切忌好高骛远。"
				wb[3] = "想成为仙界的精英，还要下一番苦功夫啊～"
				wb[4] = "我观中的人参果树乃是混沌初分，鸿蒙初判，天地未开之际产成的灵根。"
				wb[5] = "师傅领进门，修行在个人。本门法术之精妙还望各位多多领悟。"
			end
			return {"镇元子","镇元子",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID == 1028 then
		if 编号 == 1 then
			wb[1] = "别看我只是一个小二，长安消息可就数我最灵通了，最近在这里住店过往商人经常提起有强盗的事情，想不想听听？"
			xx = {"听听无妨(消耗2000两银子)","还是别多管闲事了。。。"}
			return {"男人_店小二","店小二",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "客官远道而来辛苦了，我们酒店有干净舒适的客房，您休息后可完全回复气血和魔法。休息一次需要500两银子和消耗当前10%的活力，对于20级以下的玩家，我们不收费"
			xx = {"我要住店休息","我要制作仙露丸子","我精神很好，不想住店"}
			return {"男人_酒店老板","酒店老板",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1092 then
		if 编号 == 1 then
			wb[1] = "我可以送你去东海湾你要不要去呢？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_驿站老板","船夫",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "我可以送你去蓬莱仙岛你要不要去呢？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_诗中仙","仙岛引路人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 3 then
			wb[1] = "我可以送你去长安城你要不要去呢？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 4 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 5 then
			wb[1] = "掌上星初满，盘中月正弧~我就是精通宝石工艺的蝴蝶妹妹#2"
			xx = {"我要学习宝石工艺","我只是路过"}
			return {"蝴蝶仙子","蝴蝶妹妹",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 6 then
			wb[1] = "魔族的生活本该逍遥自在，但是总有些魔族弟子不守本分，还有些神仙没事就来找茬，你去调查下究竟是个什么情况，必要时使用武力解决问题。"
			xx = {"好啊，我也正想教训他们呢。","我不想去"}
			return {"九头精怪","九头精怪",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 7 then
	      	wb[1] = "比赛时间内只要等级达到30级的玩家可以在我这里报名参赛"
	      	xx = {"我要报名参赛","我要取消任务","我不想淹死"}
	      	return {"雨师","报名官",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 8 then
	      	wb[1] = "只要你在我这里集齐了五宝，我会给你一个神秘物品哟。"
	      	xx = {"滚"}
	      	return {"兔子怪","偷偷怪",wb[取随机数(1,#wb)],xx}

    	elseif 编号 == 9 then
      		wb[1] = "每晚21-22点你可以在我这里参加迷宫活动哟。成功抵达第20层迷宫并找到迷宫守卫报道后可以获得丰厚奖励哟。"
      		xx = {}
      		if 迷宫数据.开关 then
        		wb[1] = "幻域迷宫活动已经开启，你需要我送你进去吗？"
        		xx = {"请送我进去","不去,我怕迷路"}
      		end
      		return {"马猴","金毛猿",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 10 then
      		wb[1] = "少侠，我这里可是有些稀罕玩意，不知道你是否愿意出高价购买呢？"
      		xx = {"购买","我没钱"}
      		return{"珍品商人","傲来珍品商人",wb[取随机数(1,#wb)],xx}

      	elseif 编号 == 11 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}
    		end
    		return {"男人_特产商人","傲来商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 12 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}
    		end
    		return {"男人_老财","傲来货商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 13 then
			wb[1] = "东海广阔无边，巡海夜叉无数，少侠出门千万要小心。"
			xx={"我要开启齐天大圣","我只是看看"}

			if 玩家数据[数字id].角色:取任务(191)~=0 then
				wb[1]="你的副本已经开启了，是否需要我帮你传送进去？"
        		xx={"我这就前往","我等会再进去"}
			end
			return {"马猴","红毛猿",wb[取随机数(1,#wb)],xx}
	     elseif 编号 == 14 then
			wb[1] = "我这里可以用#G钓鱼积分#兑换海产：\n强化石，彩果，金柳露，点化石，附魔宝珠，魔兽要诀，神兜兜\n你目前有"..玩家数据[数字id].角色.数据.钓鱼积分.."积分"
			xx = {"花费100万两购买鱼竿","兑换海产"}
			return {"男人_钓鱼","渔夫",wb[取随机数(1,#wb)],xx}

		-- elseif 编号 == 15 then
  --   			wb[1] = "少侠可是想要炼制极品丹药？带上你的东西到我这来炼丹吧！运气好的话你会获得价值不菲的灵丹妙药！"
  --   			xx = {"我要炼丹","买点炼丹材料","领取寄存丹药","我什么都不想做"}
  --   		return {"男人_道士","上古练气士",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 15 then
      		wb[1] = "贫道游走四方搜集了不少珍宝，现想凑点钱财修建一座道观，逼不得已只能将这些珍宝出售了。"
      		xx = {"购买","我没钱"}
      		return{"男人_道士","云游道人",wb[取随机数(1,#wb)],xx}
		 elseif 编号 == 16 then
     			wb[1] = "少侠可是想要炼制极品丹药？带上你的东西到我这来炼丹吧！运气好的话你会获得价值不菲的灵丹妙药！"
    			xx = {"我要炼丹","买点炼丹材料","领取寄存丹药","我什么都不想做"}
     		return {"炼丹炉","八卦炼丹炉",wb[取随机数(1,#wb)],xx}
		end

 	elseif ID == 1101 then
		if 编号 == 1 then
			wb[1] = "我这里出售各种30级武器，你是否需要买一把武器防身呢？"
			xx = {"购买","我只是路过"}
			return {"男人_兰虎","杜天",wb[取随机数(1,#wb)],xx}
		end
 	elseif ID == 1095 then
		if 编号 == 1 then
			wb[1] = "我这里出售各种30级防具，你是否需要买点防具呢？"
			xx = {"购买","我只是路过"}
			return {"男人_服装店老板","牛师傅",wb[取随机数(1,#wb)],xx}
		end
  	elseif ID == 1104 then
		if 编号 == 1 then
			wb[1] = "客官需要什么药，治病的灵药，大保健的补药，小店都有。药有等级之分，不同的等级在道具栏内能叠加的数量也不一样。"
			xx = {"购买","我什么都不想做"}
			return {"男人_药店老板","沈妙衣",wb[取随机数(1,#wb)],xx}
		end
  	elseif ID == 1016 then
    	if 编号 == 1 then
      		wb[1] = "客官需要什么药，治病的灵药，大保健的补药，小店都有。药有等级之分，不同的等级在道具栏内能叠加的数量也不一样。"
      		xx = {"购买","我什么都不想做"}
      		return {"男人_药店老板","药店老板",wb[取随机数(1,#wb)],xx}
    	end
  elseif ID == 1017 then
    	if 编号 == 1 then
      		wb[1] = "送些精致饰品给意中人，表示一下心意嘛。"
      		xx = {"购买","我什么都不想做"}
      		return {"女人_赵姨娘","饰品店老板",wb[取随机数(1,#wb)],xx}
    	end
  elseif ID == 1105 then
		if 编号 == 1 then
			wb[1] = "外面的家具市场真是人山人海，摩肩接踵。少侠就算不买东西，也可以进本店休憩片刻，寻个清静。#40"
			xx = {"购买","我什么都不想做"}
			return {"男人_巫医","杂货店老板",wb[取随机数(1,#wb)],xx}
		end
  elseif ID == 1093 then
		if 编号 == 1 then
			wb[1] = "我的店里有好酒，保准让客官饮后忘却一切烦恼！"
			xx = {"购买","我什么都不想做"}
			return {"男人_酒店老板","王福来",wb[取随机数(1,#wb)],xx}
       	elseif 编号 == 2 then
			wb[1] = "春茶苦，夏茶涩，品茶当品秋白露。"
			wb[2] = "认人如同品茶，观色闻香尚不够，还需切身细细体会和感受。"
			wb[3] = "这位朋友喜欢红茶，绿茶，还是乌龙茶？"
			wb[4] = "夏季宜饮绿，冬季宜饮红，春秋两季皆饮花，此乃养生之道也。"
			wb[5] = "这里就是东胜神洲傲来国了，听说齐天大圣就住在附近。"
			wb[6] = "我这里有上好的龙井茶，你想不想尝尝？"
			return {"男人_老书生","慕容先生",wb[取随机数(1,#wb)],xx}
		end
  elseif ID == 1174 then
		if 编号 == 1 then
			wb[1] = "我可以送你去长寿郊外，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"兔子怪","地遁鬼",wb[1],xx}
		elseif 编号 == 2 then
			wb[1] = "我可以送你去女娲神迹，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"净瓶女娲","女娲神迹传送人",wb[1],xx}
    	elseif 编号 == 3 then
			wb[1] = "北俱芦洲的风雪千年不变，这块冰封的大路上也有无数的宝藏。"
			wb[2] = "行走江湖这么久，哪里才是我的栖身之所呢……"
			wb[3] = "少侠你打南边来，可曾见到翻天怪的行踪？"
			wb[4] = "为什么，为什么我总是感觉冥冥之中有几个声音在耳边，而那声音又那么像我自己。"
			return {"星灵仙子","青琉璃",wb[取随机数(1,#wb)],xx}
     --[[ elseif 编号 == 4 then
			wb[1] = "我这里的商品琳琅满目应有尽有，年轻人想要什么尽管开口。"
			wb[2] = "作买卖要讲究变通，眼明手快，商场如战场，时机不等人啊。"
			return {"仓库保管员","江湖奸商",wb[取随机数(1,#wb)],xx} --]]
    	elseif 编号 == 5 then
			wb[1] = "这里的龙窟凤巢是升级的好地方，但是没有些本事可千万不要去冒险，最好找些同伴一起去。"
			wb[2] = "这里到处是冰天雪地，没有人家的。"
			wb[3] = "若是没有真本事，还是不要去惹恼这里的翻天怪。"
			wb[4] = "冰是睡着的冰，这厚厚冰层之下的水，连接着父王的水晶宫。"
			return {"小龙女","龙女妹妹",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 4 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养！召唤兽降级：每次可降级10级需要50万银子"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
	  	elseif 编号 == 6 then
			wb[1] = "北俱一年四季都被冰雪覆盖。"
			wb[2] = "这里终年冰封，寸草不生。"
			wb[3] = "方圆几百里内没有人家，倒是常有不少凶猛的野兽出没。"
			wb[4] = "北俱龙窟凤巢，是江湖探险者的好去处。"
			return {"山贼","莽汉",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 7 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_特产商人","北俱商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 8 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}

    		end
    		return {"男人_老财","北俱货商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 9 then
			wb[1] = "我可以送你去长安城你要不要去呢？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 10 then
			wb[1] = "北俱一年四季都被冰雪覆盖。"
			wb[2] = "这里终年冰封，寸草不生。"
			wb[3] = "方圆几百里内没有人家，倒是常有不少凶猛的野兽出没。"
			wb[4] = "北俱龙窟凤巢，是江湖探险者的好去处。"
			return {"雷鸟人","雷鸟精",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 11 then
			wb[1] = "北俱一年四季都被冰雪覆盖。"
			wb[2] = "这里终年冰封，寸草不生。"
			wb[3] = "方圆几百里内没有人家，倒是常有不少凶猛的野兽出没。"
			wb[4] = "北俱龙窟凤巢，是江湖探险者的好去处。"
			return {"白熊","白熊怪",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 12 then
			wb[1] = "北俱一年四季都被冰雪覆盖。"
			wb[2] = "这里终年冰封，寸草不生。"
			wb[3] = "方圆几百里内没有人家，倒是常有不少凶猛的野兽出没。"
			wb[4] = "北俱龙窟凤巢，是江湖探险者的好去处。"
			return {"地狱战神","翻天怪",wb[取随机数(1,#wb)],xx}
		end
 	elseif ID == 1091 then
		if 编号 == 1 then
			wb[1] = "我可以送你去北俱芦洲，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_驿站老板","驿站老板",wb[1],xx}
		elseif 编号 == 2 then
			wb[1] = "我可以送你去天宫，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_将军","传送天将",wb[1],xx}
		elseif 编号 == 3 then
			wb[1] = "我可以送你去大唐境外，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_土地","西牛贺洲土地",wb[1],xx}
		elseif 编号 == 4 then
			wb[1] = "降妖伏魔活动"
			xx = {"领取任务","取消任务","路过"}
			return {"男人_道士","鬼谷道人",wb[1],xx}
		elseif 编号 == 5 then
		    if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 9 then
			wb[1] = "你说给你就给你啊,我说皇宫是我的皇上会给我吗?这是我的,真是,赤裸裸的打劫!"
			xx = {"不给,打你啊!"}
			return {"赌徒","路人甲",wb[1],xx}
			end
			wb[1] = "我是路人甲"
			return {"赌徒","路人甲",wb[1],xx}
		end
	elseif ID == 1168 then
		if 编号 == 1 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 40 then
			wb[1] = "这一天终于到来了,我们的#Y刘洪大人#早就料到有今日\n所以叫我#G假扮他#,在这里和你们周旋\n能拖一会是一会,给刘洪大人争取时间\n看来不是#R你死就是我亡#了"
			xx = {"开打开打"}
			return {"护卫","刘洪",wb[1],xx}
			end
			wb[1] = "你是何人,到江州府干吊?#4"
			return {"护卫","刘洪",wb[1],xx}
		elseif 编号 == 2 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 28 then
			wb[1] = "这就是当年的#R血书#吗?\n当时夫君#G陈光蕊#考上了状元,恰巧父亲殷丞相,替我抛#Y绣球招亲\n那绣球恰好打到光蕊的乌纱帽,我二人由此成就了#P一段姻缘\n而后光蕊赴任江州到了洪江渡口,船公#S刘洪#,#S李彪#见色起意\n杀了光蕊和家僮,逼我顺从,当时洪江上波动汹涌,似有#Y水族#经过\n事后不见#G夫君尸首#,我寻思无计,只得顺了刘洪\n刘洪穿了#S光蕊衣冠#,#G带了官凭文书#,同我往江州上任去了\n刘洪利用职位之便,#R买通#一些官府和衙门上下,我#Y伸冤#至今没有回应"
			xx = {"请你继续说"}
			return {"陈妈妈","殷温娇",wb[1],xx}
			end
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 34 then
			wb[1] = "非常感谢你少侠,我的#Y夫君居然还活着\n虽然不知道他现在身在何处,但只要他在世,我相信我们#R一定会再见#的\n#S对了,少侠!\n我还有#P一位婆婆#和我走散了多年\n麻烦你再帮我一个忙,找到我的婆婆#15\n你可以在#Y国境问问#,看有人知道她的下落没"
			xx = {"帮人帮到底,送佛送到西"}
			return {"陈妈妈","殷温娇",wb[1],xx}
			end
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 37 then
			wb[1] = "再次非常感谢你少侠,我十几年了,终于可以和一个家人团聚了#14\n最后麻烦你一件事情,将我的#G书信#带个我的父亲:#Y殷丞相\n让他帮忙解#S决刘洪假冒#我夫君之事!"
			xx = {"好的,这种事情我愿意帮忙"}
			return {"陈妈妈","殷温娇",wb[1],xx}
			end
			wb[1] = "我真正的夫君,可不是刘洪这个大流氓#16"
			return {"陈妈妈","殷温娇",wb[1],xx}
		    elseif 编号 == 3 then
			wb[1] = "..."
			return {"男人_老书生","江州县令",wb[1],xx}
		end
  	elseif ID == 1110 then
		if 编号 == 4 then
			wb[1] = "我可以送你去长安城，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_驿站老板","驿站老板",wb[1],xx}
    	elseif 编号 == 1 then
			wb[1] = "我可以送你去凌波城，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_土地","大唐国境土地",wb[1],xx}
    	elseif 编号 == 2 then
			wb[1] = "我可以送你去普陀山，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"普陀_接引仙女","普陀山接引仙女",wb[1],xx}
	    elseif 编号==3 then
	    	if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 35 then
			wb[1] = "你要找的#S婆婆#就在附近,她已经在#Y洪州#乞讨很多年了\n今天她好像#G饿晕了#,有点不省人事\n你最好是给他#P带点吃的#,让她吃饱了,#S才有力气和你回去!"
			xx = {"我这就去准备吃的"}
			return {"男人_店小二","小二",wb[1],xx}
			end
	      	wb[1] = "别看我们店小，回锅肉，十香素锦、桂花鸡样样齐全。"
	      	wb[2] = "客官你是要打尖还是住店？"
	      	wb[3] = "物价飞涨，小店生意不好做啊，老板迫不得已把酒钱都给涨了#14"
	      	xx ={}
	      	return {"男人_店小二","小二",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 5 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
    	elseif 编号==6 then
      		wb[1] = "清净之心可除烦恼，至诚之心可入佛号。"
      		wb[2] = "本寺的法明长老最近去长安城会朋友去了，你若是有急事可以到化生寺找他。"
      		wb[3] = "师傅为我取法号“者释和尚”，意思是说，这是个和尚。"
      		return {"男人_胖和尚","者释和尚",wb[取随机数(1,#wb)],xx}
    	elseif 编号==7 then
      		wb[1] = "酒肉和尚老是欺负玄奘，我们都看不过去，但是那酒肉和尚有几分蛮力，我们也不敢说什么。"
      		wb[2] = "到庙里上香要怀着一颗虔诚之心。"
      		wb[3] = "修行佛法贵在调心，“心生则法生，法生则心生”，只要心净，佛土自然清静。"
      		return {"男人_胖和尚","业释和尚",wb[取随机数(1,#wb)],xx}
    	elseif 编号==8 then
      		wb[1] = "施主是来金山寺进香的吗？"
      		wb[2] = "须菩提！以要言之，是经有不可思议，不可称量，无边功德……。"
      		wb[3] = "本寺的法明长老最近去长安城会朋友去了，你若是有急事可以到化生寺找他。"
      		return {"男人_胖和尚","海释和尚",wb[取随机数(1,#wb)],xx}
    	elseif 编号==9 then
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 14 then
			wb[1] = "法明那老头子还没死吗?#G化生寺#看来是没人了\n派你这个#Y毛都没长齐#的小东西,来我这里要东西\n好吧!#S想要也可以#,看你有没有本事了#25"
			xx = {"得罪了,小琉璃"}
			return {"星灵仙子","白琉璃",wb[取随机数(1,#wb)],xx}
			end
      		wb[1] = "没了，终于知道什么是没了……"
      		wb[2] = "发明那老头明明知道些什么，却那么嘴硬，不给点教训他还不知道本姑娘的厉害。"
      		wb[3] = "为什么，为什么我总感觉冥冥之中有几个声音在耳边，而那声音又那么像我自己？"
      		return {"星灵仙子","白琉璃",wb[取随机数(1,#wb)],xx}
    	elseif 编号==10 then
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 14 then
    		wb[1] = "听说白琉璃有个#G弱点#,她非常怕#Y细小的棍状物体#1"
    		return {"雨师","山神",wb[取随机数(1,#wb)],xx}
    		end
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 30 then
    		wb[1] = "想要#S避水珠?\n天下没有免费的午餐,如果你给我带来#G美味的食物\n我可以把#S避水珠#给你,不然的话你就别想去#Y龙宫#咯\n随便告诉你一下,我喜欢的东西比较#R特别#哦"
    		xx = {"好的,我这就去给你找美食"}
    		return {"雨师","山神",wb[取随机数(1,#wb)],xx}
    		end
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 31 then
    		wb[1] = "我想吃的东西,你给我找来了吗?"
    		xx = {"上交特别的美味"}
    		return {"雨师","山神",wb[取随机数(1,#wb)],xx}
    		end
      		wb[1] = "丛山峻岭中呆久了，难免想到海边走走。"
      		wb[2] = "山外有山，人外有人。这山神也是分三六九等的。"
      		wb[3] = "从前有座山，山上有个庙，庙里有个神仙讲故事，他说从前有座山，山上有个庙，少侠你还想继续听么？"
      		return {"雨师","山神",wb[取随机数(1,#wb)],xx}
    	elseif 编号==11 then
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 29 then
			wb[1] = "你打听十八年前的事儿?这我哪知道\n不过我这里有刚从#G龙宫#顺出来的宝贝#Y定颜珠\n你要的话,就便宜点卖给你\n收你#S50万#银子不过分吧#25"
			xx = {"50万银子确实不贵,因为我TM要发展接下来的剧情,必须买!","我没钱,不用了!"}
			return {"虾兵","虾兵",wb[取随机数(1,#wb)],xx}
			end
      		wb[1] = "三界之间的恩怨情仇，从来就和我们这种小角色无关#69"
     	 	wb[2] = "瞧一瞧看一看了，东海龙宫宝贝大展销啦#51"
      		wb[3] = "龙宫的宝贝真是多啊，随便偷得一两件拿到外面来卖都能发笔横财。"
      		return {"虾兵","虾兵",wb[取随机数(1,#wb)],xx}
      	elseif 编号==12 then
      		wb[1] = "三界之间的恩怨情仇，从来就和我们这种小角色无关#69"
      		return {"男人_村长","吴老爹",wb[取随机数(1,#wb)],xx}
      	elseif 编号==13 then
      		wb[1] = "不知道吴大哥现在过的怎么样了！#69"
      		return {"女人_丫鬟","小芸芸",wb[取随机数(1,#wb)],xx}
      	elseif 编号==14 then
      		wb[1] = "丛山峻岭中呆久了，难免想到海边走走。#69"
      		return {"男人_书生","吴文彩",wb[取随机数(1,#wb)],xx}
      	elseif 编号==15 then
      		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 36 then
			wb[1] = "我好饿啊~~~我想吃#Y有肉#的东西"
			xx = {"上交吃的"}
			return {"女人_孟婆","婆婆",wb[1],xx}
			end
      		wb[1] = "我可怜的儿子媳妇！#69"
      		return {"女人_孟婆","婆婆",wb[取随机数(1,#wb)],xx}
      	elseif 编号==16 then
      		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 21 then
    		wb[1] = "是的,我就是#G文秀\n胖哥去世了这么多年了,居然为了我还没有#P转世投胎\n当年我们遇到土匪,#S他为了救我#,自己挡住了土匪\n给我留出了更多时间让我好#Y逃命\n后来得知他和土匪搏斗死了,我伤心欲绝\n不过这么多年过去了,现在的我已经#Y改嫁#了\n这是我的#Y信物#交给他,让他放心#R凡事红尘#安心投胎吧"
    		xx = {"原来如此"}
    		return {"女人_丫鬟","文秀",wb[取随机数(1,#wb)],xx}
    		end
      		wb[1] = "今天天气不错哦~"
      		return {"女人_丫鬟","文秀",wb[取随机数(1,#wb)],xx}
      	elseif 编号==17 then
      		wb[1] = "江州府,闲杂人等请勿靠近"
      		xx = {"我要去江州府"}
      		if  玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)]~=nil then
      			local 任务id = 玩家数据[数字id].角色:取任务(997)
      			if 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 25 then
      				wb[1] = "那一夜,留香阁的#Y小桃红#,真的很销魂啊\n要是能把她娶回家该多好呀,虽然是个#P妓女\n但总比#G单身#强吧#80"
		    		xx = {"真是个老色批"}
		    	elseif 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 27 then
		    		wb[1] = "什么这是#P小桃红的手帕#?你说她对我有意思?\n我简直就是在做梦,没想到她还给我信物,这#Y香味#一定错不了\n就是她的东西!\n这位少侠,我真是太高兴了,有什么事儿你尽管吩咐!"
		    	elseif 任务数据[玩家数据[数字id].角色:取任务(997)].进程 > 27 then
		    	    wb[1] = "少侠里边儿请#1"
		    	end
      		end
      		return {"男人_衙役","衙役",wb[取随机数(1,#wb)],xx}
		end
  	elseif ID == 1173 then
	    if 编号==1 then
	    	wb[1] = "我可以送你去西牛贺洲，你要去吗？"
			xx = {"是的我要去","我还要逛逛"}
			return {"男人_土地","南瞻部洲土地",wb[1],xx}
	    elseif 编号==2 then
    		wb[1] = "我可以送你去碗子山，你要去吗？"
			xx = {"送我过去","我还要逛逛"}
			return {"男人_驿站老板","驿站老板",wb[1],xx}
    	elseif 编号 == 3 or 编号==13 then
			wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
			xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
			return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
    	elseif 编号==4 then
	      	wb[1] = "山贼不是贼，我和兄弟们都是绿林好汉。"
	      	wb[2] = "行走江湖靠的是拳头和刀枪，没点真本事是闯不出名堂的。"
	      	wb[3] = "这年头江湖凶险啊，不如投奔我们吧，保证有吃有喝。"
	      	wb[4] = "近日西北方向来了个奇怪的僧人，少侠去打探下，看看有何玄机。"
	      	wb[5] = "这里山林密布，靠近国境，是个立山头的好地方。"
      		return {"山贼","山贼头子",wb[取随机数(1,#wb)],xx}
    	elseif 编号==5 then
	      	wb[1] = "少侠可是去墨家村？往东北方向一直走便是。"
	      	wb[2] = "我家大王最近正在广招门徒，欢迎魔族的有志青年前去，男女不限#51。"
	      	wb[3] = "听说九头精怪要来拜访我家大王，大王特派我在此迎接43"
	      	wb[4] = "我就是牛魔王座下巡山将，怎么样，投入我们大王门下吧，绝对没有人敢欺负你。"
	     	wb[5] = "南瞻部洲常有野兽出没，路过可要当心了。"
	      	return {"牛妖","牛将军",wb[取随机数(1,#wb)],xx}
	    elseif 编号==6 then
	      	wb[1] = "我可以送你去长安城，你要去吗？"
	      	xx = {"是的我要去","我还要逛逛"}
	      	return {"男人_驿站老板","驿站老板",wb[1],xx}
	    elseif 编号==7 then
	      	wb[1] = "道，可道，非恒道。名，可名，非恒名。"
	      	wb[2] = "给我一双慧眼，让我看清这个纷繁复杂的世界吧！"
	      	wb[3] = "人到魔的转变往往就在一念之间。"
	      	wb[4] = "没事的话别在这一带逛，这附近…咳…咳…"
	      	wb[5] = "人法地，地法天，天法道，道法自然。"
	      	return {"逍遥生","白衣人",wb[取随机数(1,#wb)],xx}
	    elseif 编号==8 then
	      	wb[1] = "有些人一辈子都在骗人，而有些人用一辈子去骗一个人！"
	      	wb[2] = "那么久没见面我真的好想念他，不知道他是否也这样想念着我呢？"
	      	wb[3] = "天下最远的距离，不是相隔天涯，而是我就在他身边，他却不知道……"
	      	wb[4] = "我已经心有所属，怎么可能再去喜欢别人呢？"
	      	wb[5] = "为什么，为什么我总感觉冥冥之中有几个声音在耳边，而那声音又那么像我自己？"
	      	return {"星灵仙子","阿紫",wb[取随机数(1,#wb)],xx}
	    elseif 编号==9 then
	      	wb[1] = "二郎神的庙宇位于灌江口，那里可是块风水宝地。"
	      	wb[2] = "我们康张姚李四太尉是二郎神手下的四大天王。"
	      	wb[3] = "人称风度翩翩风流倜傥风靡天下少女的天下第一帅哥就是我们二郎神君。"
	      	return {"天兵","姚太尉",wb[取随机数(1,#wb)],xx}
    	elseif 编号==10 then
    		wb[1] = "再往西走就是荒芜凶险之地，少侠可要一路多加保重。"
      		wb[2] = "仁者乐山，智者乐水，游历可以怡情养性，豁达心情。"
      		wb[3] = "下面田里的那对小夫妻带着一身妖气，好像是妖怪。"
      		return {"空度禅师","云游僧",wb[取随机数(1,#wb)],xx}
	    elseif 编号==11 then
	    	local 首杀名称=首杀记录.白鹿精
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 2 then
			wb[1] = "淦尼酿!我是不会和你回去的\n那个死老头#G天天骑我#,有什么好的\n我还是喜欢天天(#Y骑#)我的#S小狐狸\n你就别来打扰我们#P鸳鸯戏水#了\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			xx = {"不和我回去是吧?那只有把你打趴了!"}
			return {"赌徒","白鹿精",wb[取随机数(1,#wb)],xx}
			end
	      	wb[1] = "总算是摆脱了寿星老头的控制，可以自由自在的生活了。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	wb[2] = "我有个远方亲戚，他可是三界的神兽，在长安城天台你就能看见他。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	     	wb[3] = "南极仙翁那里包吃包住，其实生活也算不错。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	return {"赌徒","白鹿精",wb[取随机数(1,#wb)],xx}
	    elseif 编号==12 then
	      	wb[1] = "在世为妖是多么自由啊，为什么要被那些神仙呼来喝去呢？"
	      	wb[2] = "我表姐可是个公主，听说最近爱上了牛魔王#35"
	      	wb[3] = "边上这就是我的夫君，别看他现在其貌不扬的，以前可是南极仙翁的坐骑。"
	      	return {"狐狸精","玉面狐狸",wb[取随机数(1,#wb)],xx}
	    elseif 编号==14 then
	      	wb[1] = "大唐境外那一家人看起来可不像凡夫俗子，是哪个妖怪变的？"
	      	wb[2] = "前面不远就是高老庄"
	      	wb[3] = "南瞻部洲地域辽阔，有居家百姓也有草莽野兽，有妖魔鬼怪也有佛道神仙。"
	      	return {"野猪","野猪王",wb[取随机数(1,#wb)],xx}
	    elseif 编号==15 then
	      	wb[1] = "你见到鬼不害怕吗？"
	      	wb[2] = "盘丝岭近在眼前。可惜她们不招收男弟子#15"
	      	wb[3] = "五庄观就在附近，听说镇元大仙正在广招仙族弟子，不过只收男的。"
	      	return {"骷髅怪","偷尸鬼",wb[取随机数(1,#wb)],xx}
	    elseif 编号==16 then
	      	wb[1] = "严肃点，正打劫呢！"
	      	wb[2] = "你看上去太搜了，再怎么有诚意地看着我我也不会吃你的！"
	      	wb[3] = "好多天没吃人肉了，你就委屈下做我的午餐吧。"
	      	return {"强盗","李彪",wb[取随机数(1,#wb)],xx}
	    elseif 编号==17 then
            local 首杀名称=首杀记录.真刘洪
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 41 then
			wb[1] = "善恶到头终有报，我的#Y报应#是快到了\n逃避总不是办法,最终还是得面对\n来吧,#R杀了我#!\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			xx = {"这会儿后悔已经晚了"}
			return {"护卫","刘洪",wb[1],xx}
			end
	      	wb[1] = "俺老娘也是的，好吃好喝伺候着，还总是想东想西。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	wb[2] = "善恶到头终有报，我的报应是快到了。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	wb[3] = "做官就是舒服，好过平头百姓奔波操劳。\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	return {"男人_马副将","刘洪",wb[取随机数(1,#wb)],xx}
	    elseif 编号==18 then
	      	wb[1] = "行走江湖靠的是拳头和刀枪，没点真本事是闯不出名堂的。"
	      	xx={"学习技能","我点错了"}
	      	return {"强盗","强盗头子",wb[取随机数(1,#wb)],xx}
	    elseif 编号==19 then
	      	wb[1] = "冤冤相报何时了？连做鬼都不得清静……"
	      	wb[2] = "那个土地老头真好欺负，下次再去和他玩#43"
	      	wb[3] = "生前再怎么风光，死后还不是一样堕入轮回……"
	      	return {"僵尸","冤魂",wb[取随机数(1,#wb)],xx}
	    elseif 编号==20 then
	    	wb[1] = "打劫脚底板！严肃点，正打劫呢！"
	    	return {"至尊宝","至尊宝",wb[取随机数(1,#wb)],xx}
	    elseif 编号==21 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 1 then
			wb[1] = "他打碎王母娘娘爱物,理应受罚,若非观音说他什么天命取经,他小命都难保!我只是奉了王母之命行事.想救他,我看看你还是省省吧,否则我手中飞剑可不长眼睛!"
			xx = {"开启剧情战斗"}
			return {"天兵","天兵飞剑",wb[取随机数(1,#wb)],xx}
			end
	    	if 玩家数据[数字id].角色.数据.等级>=69 then
	    	wb[1] = "他打碎王母娘娘爱物,理应受罚,若非观音说他什么天命取经,他小命都难保!我只是奉了王母之命行事.想救他,我看看你还是省省吧,否则我手中飞剑可不长眼睛!"
			xx = {"天命取经人你都敢打?尽管放马过来,这件事我管定了!"}
			return {"天兵","天兵飞剑",wb[取随机数(1,#wb)],xx}
			end
	    	wb[1] = "奉玉帝旨意在此惩罚卷帘大将！"
	    	return {"天兵","天兵飞剑",wb[取随机数(1,#wb)],xx}
	    elseif 编号==22 then
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 2 then
			wb[1] = "你...你闯下大祸了!你得罪了天兵飞剑,他必去禀告王母,若王母怪罪下来,让我以后怎么回去做天神!"
			xx = {"触发剧情"}
			return {"沙僧","卷帘大将",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 3 then
			wb[1] = "就因为打碎了一个琉璃盏，就要受万剑穿心之痛！"
			xx = {"触发后续"}
			return {"沙僧","卷帘大将",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(996) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(996)].进程 == 15 then
			wb[1] = "就因为打碎了一个琉璃盏，就要受万剑穿心之痛！"
			xx = {"开启剧情战斗2"}
			return {"沙僧","卷帘大将",wb[取随机数(1,#wb)],xx}
			end
	    	wb[1] = "就因为打碎了一个琉璃盏，就要受万剑穿心之痛！"
	    	return {"沙僧","卷帘大将",wb[取随机数(1,#wb)],xx}
	    elseif 编号==23 then
	    	wb[1] = "嫦娥，我还会回去找你的！"
	    	return {"猪八戒","天蓬元帅",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1228 then
    	if 编号==1 then
     		wb[1] = "我可以送你去无底洞，你要去吗？"
      		xx = {"是的我要去","我还要逛逛"}
      		return {"男人_土地","碗子山土地",wb[1],xx}
    	end
  	elseif ID == 1033 then
    	if 编号==1 then
      		wb[1] = "今宵有酒今宵醉，就让俺醉这一回吧！"
     		wb[2] = "今天点谁来给我唱曲呢？"
      		wb[3] = "人生在世不称意，可有哪位姑娘懂俺的心意？"
      		return {"罗百万","罗百万",wb[取随机数(1,#wb)],xx}
    	elseif 编号==2 then
    		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 26 then
			wb[1] = "你悄悄的,盗取了#Y小桃红#的#S手帕\n顺便摸了摸她的#R屁股\n真是有弹性啊,不过#G你被发现了\n为了不被打死,你还是赶紧跑吧!"
			xx = {"溜了溜了"}
			return {"少女","小桃红",wb[取随机数(1,#wb)],xx}
			end
      		wb[1] = "人面桃花相映红。客官请与小女桃红满饮此杯。"
      		wb[2] = "小女子擅弹琵琶，客官可要来上一曲？"
      		wb[3] = "要不要小桃红唱首新学的小曲儿给您听听？"
      		return {"少女","小桃红",wb[取随机数(1,#wb)],xx}
    	elseif 编号==3 then
      		wb[1] = "这位大爷一看就知道是怜香惜玉之人，我这儿的怜香惜玉两位姑娘能歌善舞，您去楼上看看？"
      		wb[2] = "良辰夜景，花好月圆，这位客官喜欢哪位姑娘呀？我这儿的怜香惜玉两位姑娘能歌善舞，您去楼上看看？"
      		wb[3] = "本楼可是正规娱乐场所#90客官您是要品茶还是饮酒啊？"
      		return {"陈妈妈","陈妈妈",wb[取随机数(1,#wb)],xx}
    	end
    elseif ID == 1213 then
    	if 编号==1 then
    		wb[1] = "你可以对我进行挑战,成功后我会给予你一定的奖励哟！"
    		xx = {"挑战子鼠","我只是一个路过的"}
    		return {"鼠先锋","子鼠",wb[取随机数(1,#wb)],xx}
    	elseif 编号==2 then
    		wb[1] = "你可以对我进行挑战,成功后我会给予你一定的奖励哟！"
    		xx = {"挑战丑牛","我只是一个路过的"}
    		return {"超级神牛","丑牛",wb[取随机数(1,#wb)],xx}
    	elseif 编号==3 then
    		wb[1] = "你可以对我进行挑战,成功后我会给予你一定的奖励哟！"
    		xx = {"挑战寅虎","我只是一个路过的"}
    		return {"超级神虎","寅虎",wb[取随机数(1,#wb)],xx}
    	elseif 编号==4 then
    		wb[1] = "你可以对我进行挑战,成功后我会给予你一定的奖励哟！"
    		xx = {"挑战卯兔","我只是一个路过的"}
    		return {"超级神兔","卯兔",wb[取随机数(1,#wb)],xx}
    	end
    elseif ID == 1070 then
      	if 编号==1 then
        	wb[1] = "副本活动,找我开启"
      		xx = {"我要开启车迟斗法副本","我只是一个路过的"}
      		if 玩家数据[数字id].角色:取任务(130)~=0 then
        		wb[1]="你的副本已经开启了，是否需要我帮你传送进去？"
        		xx={"请送我进去","我要取消车迟斗法任务","我等会再进去"}
      		end
        	return {"男人_胖和尚","慧觉和尚",wb[取随机数(1,#wb)],xx}
      	elseif 编号==2 then
        	wb[1] = "任务链任务目前尚未开放。"
        	return {"男人_老书生","陆萧然",wb[取随机数(1,#wb)],xx}
      	elseif 编号==3 then
            wb[1] = "副本活动,找我开启"
        	xx = {"逢山开路，遇水架桥，这有何难，待我渡来。","这经取不得了，各回各家吧。"}
			if 玩家数据[数字id].角色:取任务(160)~=0 then
			wb[1]="你的副本已经开启了，是否需要我帮你传送进去？"
			xx={"送我进去","取消通天河副本","我等会再进去"}
			end
        	return {"蝴蝶仙子","蝴蝶女",wb[取随机数(1,#wb)],xx}
      	elseif 编号==4 then
        	wb[1] = "种族任务目前尚未开放。"
   --      	xx ={"开启大闹天宫","路过"}
   --      	if 玩家数据[数字id].角色:取任务(180)~=0 then
			-- 	wb[1]="你的副本已经开启了，是否需要我帮你传送进去？"
   --      		xx={"我这就前往","我等会再进去"}
			-- end
			xx ={}


        	local 任务id = 玩家数据[数字id].角色:取任务(307)
        	if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==1 then
	        		local 随机烹饪 = Q_随机烹饪[取随机数(1,#Q_随机烹饪)]
	        		任务数据[任务id].烹饪=随机烹饪
	        		任务数据[任务id].分类=2
	        	    wb[1] = "我的事情太多了，忙不过来，少侠先帮我准备个#R"..随机烹饪.."#W回来，我再告诉你下一步任务。"
	        	    xx = {"好的，我这就去","路过"}
	        	elseif 任务数据[任务id].分类==2 then
	        		wb[1] = "少侠我需要的#R"..任务数据[任务id].烹饪.."#W找回来了吗？"
	        	    xx = {"我这就去","路过"}
	        	elseif 任务数据[任务id].分类==9 then
	        		wb[1] = "没找到马儿吗？天下间的动物何去何从都逃不过狮驼岭的大大王的眼睛，去调查一番。"
	        	    xx = {"我去看看","路过"}
        		end
        	end
        	return {"太白金星","太白金星",wb[取随机数(1,#wb)],xx}
      	elseif 编号==5 then
        	wb[1] = "前些天从天上掉下个东西，好像把慧觉和尚的头砸了个包，他还真是福大命大，要是砸在我老头子身上我可受不了。"
        	wb[2] = "年轻人要心胸宽广，乐观豁达才有益健康。"
       		wb[3] = "前几天村里赶毛驴的老张好像捡了根铁鞭子，他还拿着到处炫耀呢。"
        	return {"男人_村长","海老先生",wb[取随机数(1,#wb)],xx}
      	elseif 编号==6 then
        	wb[1] = "前几天真是吓坏了，就听“轰”地一声，天上掉下个鞭子，差一点砸到我身上。"
        	wb[2] = "三更灯火五更鸡，正是男儿读书时。"
        	wb[3] = "“百无一用是书生”，那是市井粗人的调侃之词。"
        	return {"男人_书生","钟书生",wb[取随机数(1,#wb)],xx}
      	elseif 编号==7 then
        	wb[1] = "逝者如斯，何日君再来？"
        	wb[2] = "长寿村远近闻名，人杰地灵。"
        	wb[3] = "“北面上去就是方寸山，听说菩提祖师就在那里。"
        	return {"少女","许姑娘",wb[取随机数(1,#wb)],xx}
      	elseif 编号==8 then
        	wb[1] = "行走江湖难免会得到不少财物，将这些财物放到仓库里，实在是个最保险的方法。"
        	xx={"打开仓库","暂时不用了"}
        	return {"仓库保管员","仓库管理员",wb[取随机数(1,#wb)],xx}
     	elseif 编号==9 then
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 1 and 玩家数据[数字id].角色.数据.等级>=25 then
			wb[1] = "这位少侠,你能帮老夫一个忙吗?\n我的坐骑:#Y一只小白鹿\n近些日子走丢了\n你能帮我找到它吗?\n如果你帮我找回来的话,我定有重谢\n最后一次看见它,好像是在#G大唐境外#丢失的"
			xx = {"没问题包在我身上吧"}
			return {"南极仙翁","南极仙翁",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 3 then
			wb[1] = "卧槽,你怎么把我的#Y小白鹿#打伤了\n它虽说有些不听话,#P好色了点\n但你下手也太重了吧\n赶紧去找#G百色花\n带回来给我的鹿儿医治#4"
			xx = {"打了还赔医药费,真倒霉"}
			return {"南极仙翁","南极仙翁",wb[取随机数(1,#wb)],xx}
			end
			if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 4 then
			wb[1] = "你把#Y百色花#带来了吗?如果带来了赶紧交给我吧#1"
			xx = {"上交百色花"}
			return {"南极仙翁","南极仙翁",wb[取随机数(1,#wb)],xx}
			end
        	wb[1] = "你可以在我这里学习剧情技能仙灵店铺。每级消耗3点剧情点，最高可以学习到5级。"
        	xx={"学习仙灵店铺","我考虑考虑"}
        	return {"南极仙翁","南极仙翁",wb[取随机数(1,#wb)],xx}
   		elseif 编号 == 10 then
      		wb[1] = "少侠，我这里可是有些稀罕玩意，不知道你是否愿意出高价购买呢？"
      		xx = {"购买","我没钱"}
      		return{"珍品商人","长寿珍品商人",wb[取随机数(1,#wb)],xx}
      	elseif 编号 == 11 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}
    		end
    		return {"男人_特产商人","长寿商人",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 12 then
    		wb[1] = "来！来！来！瞧一瞧看一看了！"
    		xx = {}
    		if 玩家数据[数字id].角色.数据.跑商 then
    		    xx = {"购买商品","我什么都不想做"}
    		end
    		return {"男人_老财","长寿货商",wb[取随机数(1,#wb)],xx}
    	elseif 编号 == 13 then
		    wb[1] = "我那女儿都19岁了还没找人家，我看村里的钟书生挺上进，就是不知女儿对他有没有意思"
		    return{"女人_王大嫂","许大娘",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 999 then
	      	wb[1] = "想买到真正的古董，一定要像我一样练就一双火眼金睛。"
	      	xx = {"存款","取款","我只是来看看"}
	      	return{"男人_财主","钱老板",wb[取随机数(1,#wb)],xx}
	    elseif 编号==14 then
	      	wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
	      	xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要同时补满召唤兽的气血、魔法和忠诚","我只是看看"}
	      	return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	     	wb[1] = "人生若有几名知己，几壶美酒，夫复何求。"
	      	xx = {"我要成立家族","家族列表","加入家族","活动暂未开放"}
	      	return{"男人_村长","李药师",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	      	wb[1] = "想结拜成为梦幻人人羡慕的结义金兰吗？我可以帮你达成愿望。"
	      	xx = {"我们是来义结金兰的","我们是来领取称谓的","活动暂未开放"}
	      	return{"男人_武器店老板","虬髯客",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	      	wb[1] = "岂日无依，与子同袍，一起经历患难，同生死的好兄弟，好姐妹，可以来我这里结成同袍。"
	      	xx = {"我们来结成同袍","我们是来领同袍称谓的","我们来割袍断义","活动暂未开放"}
	      	return{"女人_万圣公主","红拂女",wb[取随机数(1,#wb)],xx}
	    elseif 编号==15 then
	     	wb[1] = "长寿村远近闻名，人杰地灵。"
	      	return{"男人_老伯","毛驴张",wb[取随机数(1,#wb)],xx}
	    elseif 编号==16 then
	    	wb[1] = "长寿村之所以风景秀丽，民风淳朴。是长久以来受到十二生肖的庇佑,您要前往生效所在地么？"
	    	xx = {"我只是来看看"}
	    	--xx = {"生肖家园","我只是来看看"}
	      	return{"普陀_接引仙女","凤凰姑娘",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	      	wb[1] = "走过路过不要错过。"
	      	xx = {"我想购买你那些用品","活动暂未开放"}
	      	return{"珍品商人","童趣商人",wb[取随机数(1,#wb)],xx}
	    elseif 编号==17 then
	      	wb[1] = "这些都是可怜的孩子，希望你能爱惜他们(#G消耗10万仙玉领取孩子#)"
	      	--xx = {"随机领养孩子","路过看看"}
	      	xx = {"路过看看"}
	      	return{"女人_孟婆","马婆婆",wb[取随机数(1,#wb)],xx}
	    elseif 编号==18 then
	      	wb[1] = "这里是远近闻名的长寿村，任务链在村子左下的“陆萧然”领取。"
	      	return{"男人_村长","长寿村村长",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	      	wb[1] = "人们都想有个漂亮的家。"
	      	xx = {"购买家具","活动暂未开放"}
	      	return{"吴刚","鲁成",wb[取随机数(1,#wb)],xx}
	    elseif 编号==999 then
	      	wb[1] = "老夫种了一辈子菜了。"
	      	xx = {"购买菜种","活动暂未开放"}
	      	return{"男人_老伯","蔡爷爷",wb[取随机数(1,#wb)],xx}
	    elseif 编号==19 then
	      	wb[1] = "开启PK开关,即表示自愿与别人进行生死决斗。每次开启后24小时内不可关闭。期间可与其他开启开关的玩家进行PK，战斗失败的方会受到PK死亡的损失，获胜方不会受到PK惩罚。"
	      	--xx = {"开启PK开关","关闭PK开关","开启强P开关"}
	      	xx = {"开启强P开关","关闭强P开关"}
	      	return{"男人_兰虎","PK申请人",wb[取随机数(1,#wb)],xx}
	    elseif 编号==20 then
	      	wb[1] = "卖药啦,快来买呀"
	      	xx = {"购买","取消"}
	      	return{"男人_药店老板","药店老板",wb[取随机数(1,#wb)],xx}
	    end
	elseif ID == 1153 then
		local 首杀名称=首杀记录.酒肉和尚
		if 编号==1 then
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 6 then
		wb[1] = "人生在世好吃好喝才是王道\n最近这#Y玄奘和尚#坏我好事\n供养都去找他了,都没钱买肉了\n去#G揍他一顿#出出气"
		xx={"你还敢揍他,找死!"}
		return {"雨师","酒肉和尚",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 17 then
		wb[1] = "不用看了,#S玄奘#是我把他#G下毒之后#变成这样子的\n我就是看不惯他们这种#Y教条的方式#,整体唧唧歪歪的\n烦都烦死了,当和尚还是要#R随心所欲#的好!#1\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
		xx={"快把解药拿出来,不然别怪我不客气!"}
		return {"雨师","酒肉和尚",wb[取随机数(1,#wb)],xx}
		end
		wb[1] = "大口吃肉,大口喝酒,有个花姑凉陪陪更好#1\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
		return {"雨师","酒肉和尚",wb[取随机数(1,#wb)],xx}
		elseif 编号==2 then
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 16 then
		wb[1] = "啊啊啊~,我不是法明长老的#Y野种#,我不是,不是,啊啊啊啊~"
		xx = {"玄奘法师怎么有点疯疯癫癫的,问问旁边酒肉和尚情况"}
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 5 then
		wb[1] = "#Y酒肉和尚#因为讲经不如我\n#R恼羞成怒#之下\n嘲讽#G玄奘身世#\n还欲打我!#4"
		xx = {"没事,我帮你教训他"}
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 7 then
		wb[1] = "一切有为法,如梦幻泡影,如露亦如电,应作如是观\n可我的#Y身世#到底是什么呢?\n缘起缘灭,我主持讲经大会#G走不开#呀\n还请少侠帮我去问问#S化生寺#的#Y法明长老#!"
		xx = {"行,我帮你跑一趟吧"}
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 24 then
		wb[1] = "啊啊啊~,我不是法明长老的#Y野种#,我不是,不是,啊啊啊啊~"
		xx = {"上交九转回魂丹"}
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
		end
		if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 42 then
		wb[1] = "玄奘家仇得报,复知#G父母平安#,更欲何求?\n从今以后发愿立意#Y安禅:\n愿以此功德,庄严净土,上报四重恩,下济三途苦\n若有见闻者,悉发菩提心,同生极乐国,#S尽把此一身#!"
		xx = {"终于告一段落了"}
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
		end
		wb[1] = "妖是妖他妈生的,人是人他妈生的,所以你是人妖!"
		return {"唐僧","玄奘",wb[取随机数(1,#wb)],xx}
	end
  	elseif ID == 1083 then
		wb[1] = "三分长相，七分打扮，挑件合身的衣服吧。店里的张裁缝可以让你提高裁缝熟练度。"
		wb[2] = "人靠衣装马靠鞍，本店为您提供各种新款服装，就算不买也来看看吧。"
		wb[3] = "这里各色绸缎一应俱全，肯定有你想要的。只有大唐官府的玩家才能学会鉴定衣服的技能，而项链腰带的鉴定技能只有地府的玩家才能学。"
		return {"男人_服装店老板","裁缝张",wb[取随机数(1,#wb)],{"购买","我只是来看看"}}
	elseif ID == 1085 then
		wb[1] = "这里的风景还不错吧。"
		wb[2] = "行走江湖不能两手空空，来挑一件趁手的兵器吧。"
		wb[3] = "少侠是来选购兵器的吧？请慢慢挑选，务必看清楚名称哦！"
		return {"男人_武器店老板","武器店老板",wb[取随机数(1,#wb)],{"购买","我只是来看看"}}
  	elseif ID == 1202 then
  		if 编号==1 then
  			xx={}
  		    wb[1] = "少侠是想要我这里的东西的吗，不过你好像没资格拿哦#18"
  		    return {"宝箱","小宝箱",wb[取随机数(1,#wb)],xx}
  		end
  	elseif ID == 1208 then
    	if 编号==1 then
      		wb[1] = "正赶上这太平盛世，我看手下那几个兵挺闲的，哪天让它们背诵抄写下那些军令条例#18"
      		wb[2] = "前日国王说想把金圣宫娘娘住的昭阳宫修缮一下，我得查查黄道良辰，好择日开工。"
      		wb[3] = "自打国王失去金圣宫娘娘，连这朱紫国的天下都不要了#14"
      		return {"校尉","朱紫校尉",wb[取随机数(1,#wb)],xx}
    	elseif 编号==2 then
      		wb[1] = "是药三分毒，身体有恙，不可不吃药，也不可乱吃，一定要对症下药，方能药到病除。"
      		xx={"购买","我什么都不想做"}
      		return {"男人_苦力","药店伙计",wb[取随机数(1,#wb)],xx}
    	elseif 编号==3 then
      		wb[1] = "朱紫国旁边就是麒麟山，山势险峻，怪石嶙峋，据说还有妖怪出没#35不过那里盛产珍奇药草#80"
      		wb[2] = "神仙看病也须得先望闻问切，然后再配上老夫的灵丹妙药，保准药到病除。"
      		wb[3] = "冬吃萝卜夏吃姜，不劳医生开药方。这位少侠请多多学习下养生之道！	"
      		return {"男人_药店老板","紫阳药师",wb[取随机数(1,#wb)],xx}
    	elseif 编号==4 then
      		wb[1] = "听说西梁女国开了个挂毯商店，吸引了那些图新鲜的人。怪不得我的生意每况愈下#52"
      		wb[2] = "如今人人家里都用上白虎皮了，我这手编的波斯地毯如何才能卖得出去啊#47"
      		wb[3] = "苦恨年年压金线，为他人作嫁衣裳#24"
      		return {"女人_王大嫂","端木娘子",wb[取随机数(1,#wb)],xx}
    	elseif 编号==5 or 编号==6 then
      		wb[1] = "行走江湖，安全第一。少侠到了大唐境外可要小心别被老虎给吃了#47"
      		wb[2] = "等咱当了校尉，想吃香的吃香的，想喝辣的喝辣的。豆浆买两碗，喝一碗，倒一碗！#28"
      		wb[3] = "近日校尉让我们严加防范，说是有可疑人在附近出没。可是我一个眼睛瞪成两个大，都没有看到#24"
      		return {"校尉","朱紫侍卫",wb[取随机数(1,#wb)],xx}
    	elseif 编号==7 then
      		wb[1] = "我是专门治疗和调训召唤兽的医生，10级以下免费治疗驯养，选择驯养或治疗之前请注意：我每次都是把你身上携带的所有召唤兽进行统一治疗和驯养"
      		xx = {"我的召唤兽受伤了，请帮我救治一下吧","我的召唤兽忠诚度降低了，请帮我驯养一下吧","我要给召唤兽降低等级，请帮我降低一下吧","我要同时补满召唤兽的气血、魔法和忠诚","召唤兽洗点","我只是看看"}
      		return{"男人_巫医","超级巫医",wb[取随机数(1,#wb)],xx}
    	elseif 编号==8 then
    		wb[1] = "仙界神兵已冲破魔界，各位快快进入仙源洞天调查一番。"
      		xx = {"我要进入仙源洞天","等下再说"}
      		return {"男人_村长","申太公",wb[取随机数(1,#wb)],xx}
      	elseif 编号==9 then
      		wb[1] = "大王叫我来巡山，我把人间转一转！打起我的鼓，敲起我的锣，生活充满节奏感！"
      		xx = {}
      		local 任务id=玩家数据[数字id].角色:取任务(307)
      		if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==7 then
	        	    wb[1] = "哪里来的小子，胆敢在我的地盘撒野，待我先把你收拾了。"
	        	    xx = {"妖怪，终于让我逮到你了，速速带我去找你老大，饶你不死！","这就走，这就走……"}
	        	    elseif 任务数据[任务id].分类==8 then
	        	wb[1] = "少侠我都已经错了,别打我了,我的老大就在里边#17。"
	        	 xx = {"速速送我过去"}
        		end
        	end
      		return {"蝴蝶仙子","妖魔亲信",wb[取随机数(1,#wb)],xx}
      	elseif 编号==10 then
      		wb[1] = "再往西走就是荒芜凶险之地，少侠可要一路多加保重。"
      		xx = {}
      		local 任务id = 玩家数据[数字id].角色:取任务(307)
      		if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==6 then
	        	    wb[1] = "最近一段时间朱紫国皇宫附近来了一个鬼鬼祟祟的妖怪，少侠前往皇宫附近调查一番，也许有意想不到的收获。"
	        	    xx = {"好的，我这就去","路过"}
        		end
        	end
      		return {"男人_土地","土地公公",wb[取随机数(1,#wb)],xx}


    	end
    elseif ID == 1215 then--蜃妖幻境
    	if 编号==1 then
    	    wb[1] = "看什么看，再看小心我吃了你。"
      		xx = {}
      		local 任务id = 玩家数据[数字id].角色:取任务(307)
      		if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==8 then
	        	    wb[1] = "好不容易抓到只天马，想要拿走，没门！"
	        	    xx = {"妖孽，速速放了天马","路过"}
        		end
        	end
      		return {"炎魔神","蜃妖元神",wb[取随机数(1,#wb)],xx}
    	end
    elseif ID == 1216 then--仙源洞天
    	if 编号==1 then
    	    wb[1] = "异兽录上记载了各式神怪之物的修炼之道，不知少侠可有耳闻？小仙受玉帝所托，将此道传授给有缘之人。"
      		xx = {"我来给召唤兽换进阶造型","我先告辞"}
      		return{"护卫","召唤师",wb[取随机数(1,#wb)],xx}
      	elseif 编号==2 then
      		    wb[1] = "神仙之事自然问神仙，去太白金星老头那里打听打听。至于我这里嘛，放消息给你也要收点钱买点小酒,你确定接任务我就收你银子哦\n(#Y获得任务成功将扣除300W两，同时扣除体力100点#)"
      			xx = {"确定","取消坐骑任务"}
      		local 任务id=玩家数据[数字id].角色:取任务(307)
      		if 玩家数据[数字id].角色:取任务(307)~=0 then
        		if 任务数据[任务id].分类==13 then
	        	    wb[1] = "谢谢少侠帮忙找回马儿！为了感谢少侠，特意为少侠准备了一个礼物。"
	        	    xx = {"感谢兽王的礼物！","路过"}
	        	    任务处理类:完成坐骑任务(任务id,数字id)
        		end
        	end
      		return{"大大王","百兽王",wb[取随机数(1,#wb)],xx}
      	elseif 编号==3 then
      		wb[1] = "想要给坐骑换个颜色吗？可以找我哦，但是要先把坐骑牵出来我看看哦！"
      		--xx = {"给坐骑染色","给坐骑饰品染色","我只是来看看"}
      		xx = {"我只是来看看"}
      		return{"女人_赵姨娘","仙缘染坊主",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1040 then--西凉女国
    	if 编号==1 or 编号==2 or 编号==3 or 编号==4 then
      		wb[1] = "欢迎来到西梁女国，不要被乱花迷了眼哦#17"
     		wb[2] = "喝了子母河的水就能生个孩子，这在方圆五百里也不是什么秘密了#90"
      		wb[3] = "安全第一，预防为主#43"
      		return {"女兵","西梁女兵",wb[取随机数(1,#wb)],xx}
	    elseif 编号== 5 then
	      	wb[1] = "我可以送你去朱紫国，你要去吗？"
	      	xx = {"是的我要去","我还要逛逛"}
	       	return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 6 then
      		wb[1] = "少侠欲前往丝绸之路吗？"
      		xx = {"我去！我去！","我路过瞧瞧而已"}
       		return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
        elseif 编号 == 7 then
		      wb[1] = "少侠，你的修为已经是这片天地巅峰了，如果想踏出这片修炼之地，迈入神界的话，除非能接受天地浩劫的洗礼，若成功，便成神"
		      local xx = {"什么是渡劫！","我想渡劫，请上仙指点一二","我只是路过"}
		      return {"二郎神","二郎神",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 8 then
		      wb[1] = "我来自遥远的深渊宇宙，刚踏入你的这片宇宙，你既然是神界之主，想必知道，神界之主之外还有更高的宇宙之圣！我乃是圣王。如果你击败我，那么你将继承我的圣格，如果失败，神格就是我的"
		      local xx = {"了解圣王！","圣王，得罪了","我只是路过"}
		      return {"九头精怪","超凡入圣",wb[取随机数(1,#wb)],xx}
       	elseif 编号 == 9 then
		    wb[1] = "完成每层挑战必得奖励(5-10级宝石，120-150书铁，120-140灵饰书铁)"
        	xx = {"木桩伤害测试","我已经准备好了，请开始挑战吧"}
        	return {"孙悟空","九生九死",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1226 then
    	if 编号==1 then
      		wb[1] = "走过路过不要错过~骏马骆驼一应俱全，保证安全、舒适、快捷、方便地让客官抵达目的地！那么客官想前往哪里呢？"
      		xx={"我要前往长安城","旅费不够，下次再说"}
      		return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
    	elseif 编号==2  then
      		wb[1] = "押镖可是极其危险的活，如果没有足够的能力，还是不要随意接镖的好。"
      		return {"男人_店小二","镖局学童",wb[取随机数(1,#wb)],xx}
	    elseif 编号==3 then
	      	xx={}
	      	if 宝藏山数据.开关==false then
	        	wb[1] = "宝藏山活动在每日11-12点、19-20点期间开放。当前不是活动时间."
	      	else
	        	wb[1] = "宝藏山活动已经开启，你是否需要进宝藏山里头寻宝呢？"
	        	xx={"请送我进去","我只是路过"}
	      	end
	      	return {"男人_土地","土地公公",wb[取随机数(1,#wb)],xx}
	    elseif 编号==4 then
	    	wb[1] = "客官需要什么药，治病的灵药，大保健的补药，小店都有。药有等级之分，不同的等级在道具栏内能叠加的数量也不一样。"
        	xx = {"购买","我什么都不想做"}
        	return {"男人_药店老板","药店老板",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 5001 then
	    if 编号==1  then
	      	wb[1] = "我可以帮助你离开本场景传送至宝象国，你需要我帮你进行传送吗？"
	      	xx={"送我过去","我再转转"}
	      	return {"男人_土地","土地公公",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1235 then -- 丝绸之路
	    if 编号 == 1 then
	      	wb[1] = "我可以送你去西梁女国，你要去吗？"
	      	xx = {"是的我要去","我还要逛逛"}
	      	return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1210 then--麒麟山
	    if 编号==1  then
	      	wb[1] = "你可是需要传送至宝象国？"
	      	xx={"是的送我过去","不了，我还要逛逛"}
	      	return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1207 then
	    if 编号==1  then
	      wb[1] = "你可是需要传送至傲来国？"
	      xx={"是的送我过去","不了，我还要逛逛"}
	      return {"男人_驿站老板","驿站老板",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1127 then
	    if 编号==1  then
	    	local 首杀名称=首杀记录.幽冥鬼
	    	if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 20 and 玩家数据[数字id].角色.数据.等级>=40 then
			wb[1] = "走开,你这个二百五,你根本不知道什么叫#R做爱#15\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
			xx = {"做爱什么的我不知道,但是我知道怎么超度你!"}
			return {"巡游天神","幽冥鬼",wb[取随机数(1,#wb)],xx}
			end
	    if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 22 then
    		wb[1] = "他已经#Y改嫁#了?哎,原来如此,女人呐~~~~\n罢也罢也,我这就去喝了#G孟婆汤#,忘掉这前世俗缘吧#15\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
    		xx = {"幽冥鬼看着文秀的信物,流下了伤心的泪水"}
    		return {"巡游天神","幽冥鬼",wb[取随机数(1,#wb)],xx}
    		end
	      	wb[1] = "我、我死得好冤啊#15\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	wb[2] = "文秀，你现在在哪儿呀，赶紧来陪我啊#54\n#G首次击杀#该剧情BOSS的#S玩家#是:#Y【"..首杀名称.."】"
	      	return {"巡游天神","幽冥鬼",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1103 then
	    if 编号==1  then
	      	wb[1] = "无敌是一种寂寞，无敌是一种孤独。你是来解脱我的吗#55"
	      	xx={"我要学习变化之术","您继续无敌吧"}
	      	return {"孙悟空","美猴王",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1129 then
    	if 编号==1  then
      		wb[1] = "呜呜呜~我什么时候才能逃出这地狱去投胎啊，要是能有个特赦令牌，那该多好啊\n#Y特赦令牌#现在可以#G右键兑换高图#了"
      		xx={"特赦令牌现在可以右键兑换高图了"}
      		return {"野鬼","无名野鬼",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1030 then
    	if 编号==1  then
      		wb[1] = "这位客官，本酒楼有上好的酒菜，您是否需要品尝？"
      		xx={"购买","我只是路过"}
      		return {"男人_酒店老板","酒店老板",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1019 then --书香斋
		if 玩家数据[数字id].角色:取任务(402)~=0 and 任务数据[玩家数据[数字id].角色:取任务(402)].进程 == 5 then
		任务数据[玩家数据[数字id].角色:取任务(402)].进程=6
		玩家数据[数字id].角色:刷新任务跟踪()
		玩家数据[数字id].道具:给予书铁(数字id,{1,1},"精铁")
		玩家数据[数字id].道具:给予书铁(数字id,{1,1},"指南书")
		常规提示(数字id,"#Y你得到了#R10级书铁")
		end
    	if 编号 == 1 then
      		wb[1] = "我这里可以制作装备图鉴,飞行符,打工赚钱"
      		xx = {"是的我要打工一次挣钱","是的我要打工一天","制作神兵图鉴","制作灵宝图鉴","制作飞行符","我还是在考虑考虑"}
      		return {"男人_老书生","颜如玉",wb[取随机数(1,#wb)],xx}
    	end
  	elseif ID == 1025 then    --冯记铁铺
	    if 编号 == 1 then
	      	wb[1] = "我现在忙的很#101,要不要过来帮帮忙呢?我可以付工钱!"
	      	xx = {"是的我要打工一次挣钱","是的我要打工一天","我还是在考虑考虑"}
	      	return {"男人_武器店老板","冯铁匠",wb[取随机数(1,#wb)],xx}
	    -- elseif 编号 == 2 then
	    --   	wb[1] = "你好!请问你要更高级的东西吗?可以给我一样等级的东西,我帮你合成更高级的!#101"
	    --   	xx = {"是的我要合东西","我还是在考虑考虑"}
	    --   	return {"男人_武器店老板","冯冯",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 2 then--物件_打铁炉
			if 玩家数据[数字id].角色:取任务(402)~=0 and 任务数据[玩家数据[数字id].角色:取任务(402)].进程 == 6 then
			任务数据[玩家数据[数字id].角色:取任务(402)].进程=7
			玩家数据[数字id].角色:刷新任务跟踪()
			end
	      	wb[1] = "你好!我能帮你什么忙吗#24？"
	      	wb[2] = "在厉害的大师,打造东西都要靠我#69？"
	      	wb[3] = "打造,打造,打造,都快把我打造坏了......#45"
	      	wb[4] = "天天打造,烦不烦啊........#60"
	      --	xx = {"查看熟练度","普通打造","强化打造","元身打造","修理"}
	      -- xx = {"查看熟练度","购买熟练度","宝石镶嵌","普通打造","强化打造","装备回收","装备修理","熔炼","路过"}
	       xx = {"打造些东西","查看熟练度","购买熟练度","查看额外加成","装备回收","打造任务","神器相关","我们后会有期"}
	      	return {"物件_打铁炉","物件_打铁炉",wb[取随机数(1,#wb)],xx}
	    end
  	elseif ID == 1044 then-- 金銮殿
  		xx = {}
	    if 编号 == 1 then
	    	if 玩家数据[数字id].角色:取任务(997) ~= 0 and 任务数据[玩家数据[数字id].角色:取任务(997)].进程 == 39 then
			wb[1] = "天底下,居然还有如此胆大妄为的#G狂徒#4\n老夫可以全权做主,借给你一些#Y兵马\n助你一臂之力\n斩杀#S刘洪#之事就交予你了#81"
			xx = {"多谢,魏征大人"}
			return {"考官2","魏征",wb[取随机数(1,#wb)],xx}
			end
	      	wb[1] = "盛世唐朝，尔等岂敢来犯。天佑我朝流芳千古！"
	      	return {"考官2","魏征",wb[1],xx}
	    elseif 编号 == 2 then
	      	wb[1] = "我大唐人才辈出，民间有不少能人异士在某些方面为常人所不同。唐王特令我搜寻这些能人异士，并将他们的功绩张榜公示，以鞭策后人。"
	      	--xx = {"查看实力榜","我只是路过"}
	      	return{"考官2","房玄龄",wb[取随机数(1,#wb)],xx}
	    elseif 编号 == 3 then
	      	wb[1] = "为选拔天下贤能之士，特在此举行御前科举大赛，会试入围的才子可以在金銮殿上进行殿试，所有参加殿试的才子都可按成绩获得一定的奖励和称谓"
	      	   --xx = {"领取特殊兽决"}
	      	   xx = {"千亿称号[血]","千亿称号[伤]","千亿称号[法]","千亿称号[防]","千亿称号[速]","更换千亿称号","我就是来看看"}

	      	return {"皇帝","李世民",wb[1],xx}
	    end

	elseif ID == 1621 then
    	   if 编号~=0 then
		        wb[1] = "看什么看.有会员了不起啊?"
		        xx = {"开打开打","我只是来看看"}
			    return{"超级神柚","会员福利",wb[sj(1,#wb)],xx}
    	   end
  	elseif ID == 1875 then -- 聚义堂
	  	if 编号 == 1 then
	  	    local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
	        if 帮派编号~=nil and 帮派编号>0 then
	        	  if 帮派缴纳情况[帮派编号]==nil then
			         帮派缴纳情况[帮派编号] = {}
			         帮派缴纳情况[帮派编号].缴费人数 = 0
			         帮派缴纳情况[帮派编号].缴费总金额 = 0
			         帮派缴纳情况[帮派编号].起始时间 = os.time()
			      end
			      if 帮派数据[帮派编号].成员数据[数字id].职务 == "帮主"  then
				      wb[1] = "我是本帮总管,请问你需要做些什么？\n#Y/请使用ALT+B打开帮派管理！\n本周上交帮贡共："..帮派缴纳情况[帮派编号].缴费总金额.."您可以随时提取哦！"
				  	  xx = {"领取本周帮费","帮费设置","帮派加成领取","查看当前加成","送我回长安","路过，随便看看"}
				  else
				      wb[1] = "我是本帮总管,请问你需要做些什么？"
				  	  xx = {"帮派加成领取","查看当前加成","送我回长安","路过，随便看看"}
				  end
				  if 帮派数据[帮派编号].帮战胜利 and os.time()<=帮派数据[帮派编号].帮战胜利 then
				  		xx[#xx+1]="领取帮战胜利称号"
				  end
			else
				  wb[1] = "你找我干什么？"
			  	  xx = {"路过，随便看看"}
			end
	  		return {"男人_兰虎","帮派总管",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  	 	return {"帮派机关人","黑色机关人",wb[1],xx}
	  	end
  	elseif ID == 1865 then -- 青龙堂
	  	if 编号 == 1 then
	  	    wb[1] = "你找我干什么？"
	  	    local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
	        if 帮派编号~=nil and 帮派编号>0 then
			  	  xx = {"给我些任务","取消任务","路过，随便看看"}
			else
			  	  xx = {"路过，随便看看"}
			end
	  	    return {"男人_兰虎","青龙总管",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  	 	return {"帮派机关人","橙色机关人",wb[1],xx}
	  	end
  	elseif ID == 1855 then -- 药房
	  	if 编号 == 1 then
	  	    wb[1] = "你找我干什么？"
	  	    local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
	        if 帮派编号~=nil and 帮派编号>0 then
			  	  xx = {"提高修炼修为","路过，随便看看"}
			else
			  	  xx = {"路过，随便看看"}
			end
	  	    return {"男人_药店老板","药房总管",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  	 	return {"帮派机关人","红色机关人",wb[1],xx}
	  	end
  	elseif ID == 1845 then -- 厢房
	  	if 编号 == 1 then
	  	    wb[1] = "你找我干什么？"
	  	     local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
	           if 帮派编号~=nil and 帮派编号>0 then
			  	    xx = {"给我些任务","取消任务","路过，随便看看"}
			  	else
			  	    xx = {"路过，随便看看"}
			  	end
	  	    return {"男人_兰虎","厢房总管",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  	 	return {"帮派机关人","绿色机关人",wb[1],xx}
	  	end
  	elseif ID == 1835 then -- 兽室
	  	if 编号 == 1 then
	  	    wb[1] = "状态是帮派守护兽除等级外最重要的属性，初始状态为500点，上限为2500点；除了每个月会固定提升若干状态外，还可以通过培养的方式来主动提升守护兽状态。您想好要做什么了吗？"
	  	    --xx = {"培养召唤兽","路过，随便看看"}

	  	    return {"帮派妖兽","帮派守护兽",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  	 	return {"帮派机关人","青色机关人",wb[1],xx}
	  	end
  	elseif ID == 1825 then -- 书院
	  	if 编号 == 1 then
	  	    wb[1] = "现在帮派研究技能是xx，你想做什么呢？"
	  	    --xx = {"查看帮派内政","停止当前内政","用帮派贡献度换薪水","设置技能研究","设置敌对帮派","学习技能","购买药品","路过，随便看看"}
	  	    local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
           if 帮派编号~=nil and 帮派编号>0 then
		  	    xx = {"学习技能","路过，随便看看"}
		  	else
		  	    xx = {"路过，随便看看"}
		  	end
	  		return {"男人_师爷","帮派师爷",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  		return {"帮派机关人","蓝色机关人",wb[1],xx}
	  	end
  	elseif ID == 1815 then -- 金库
	  	if 编号 == 1 then
	  	    wb[1] = "当帮中资金少于一定资金就无法维护，少于xx跑商会有额外的经验奖励，你加油为帮里出力啊。"
           local 帮派编号 = 玩家数据[数字id].角色.数据.帮派数据.编号
           if 帮派编号~=nil and 帮派编号>0 then
		  	    xx = {"给我些任务","申请成为商人","完成跑商任务","取消跑商任务","上交金银锦盒","路过，随便看看"}
		  	else
		  	    xx = {"路过，随便看看"}
		  	end
	  		return {"男人_兰虎","金库总管",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "收你500块，我可以送您去帮派中的任何一间房间里，告诉我您要去的地方吧。"
	  	 	xx = {"金库","兽室","药房","聚义厅","书院","厢房","仓库","送我回长安","路过，随便看看"}
	  		return {"帮派机关人","紫色机关人",wb[1],xx}
	  	end


	elseif ID == 6021 then
		if 编号 == 1 then
			xx = {}
			local 任务id = 玩家数据[数字id].角色:取任务(130)
			if 任务id == 0 then
				return
			end
			local 副本id=任务数据[任务id].副本id
			if 副本数据.车迟斗法.进行[副本id]~=nil and 副本数据.车迟斗法.进行[副本id].进程==1 and 玩家数据[数字id].角色:取任务(130)~=0 then
			    wb[1] = "通过有个道士的考验，获得木材上交与我，获得建设度，当前建设度："..副本数据.车迟斗法.进行[副本id].车迟木材..":5"
			    xx = {"上交木材","我来看看进度"}
			else
				wb[1] = "当前的建设度已满！！"
			end
			return {"道观","道观",wb[1],xx}
		end
	elseif ID == 6024 then
		if 编号 == 1 then
		    local 任务id = 玩家数据[数字id].角色:取任务(150)
			if 任务id == 0 then
				return
			end
			local 副本id=任务数据[任务id].副本id
			if 副本数据.水陆大会.进行[副本id]~=nil and 副本数据.水陆大会.进行[副本id].进程==1 and 玩家数据[数字id].角色:取任务(150)~=0 then
			    wb[1] = "多谢少侠了，你想帮我做哪个呢？"
			    xx = {"装潢（需要完成"..任务数据[任务id].装潢.."/10个）","邀请（需要完成"..任务数据[任务id].邀请.."/10个）","看流星耶..当我没来过。"}
			else
				wb[1] = "当前的建设度已满！！"
			end
			return {"男人_方丈","道场督僧",wb[1],xx}
		elseif 编号 == 2 then
			wb[1] = "前面就是繁华京城了，少侠是否需要过去呢？"
	  	 	xx = {"快送我过去","路过，随便看看"}
	  		return {"男人_胖和尚","道场童子",wb[1],xx}
       elseif 编号 == 3 then
       	   wb[1] = "唔？哪来的黄毛小儿。走开！今妖爷爷高兴不想抓你"
			xx = {}
       	    local 任务id = 玩家数据[数字id].角色:取任务(150)
			if 任务id ~= 0 then
				local 副本id=任务数据[任务id].副本id
				if 副本数据.水陆大会.进行[副本id]~=nil and (副本数据.水陆大会.进行[副本id].进程==4 or 副本数据.水陆大会.进行[副本id].进程==5) then
		       	    wb[1] = "唔？哪来的黄毛小儿。走开！今妖爷爷高兴不想抓你！(等级>=50,至少三人组队)"
			  	 	xx = {"妖怪看打","路过，随便看看"}
			  	 end
		  	 end
	  		return {"噬天虎","翼虎将军",wb[1],xx}
       elseif 编号 == 4 then
       	    wb[1] = "唔？哪来的黄毛小儿。走开！今妖爷爷高兴不想抓你"
			xx = {}
         	local 任务id = 玩家数据[数字id].角色:取任务(150)
			if 任务id ~= 0 then
				local 副本id=任务数据[任务id].副本id
				if 副本数据.水陆大会.进行[副本id]~=nil and (副本数据.水陆大会.进行[副本id].进程==4 or 副本数据.水陆大会.进行[副本id].进程==5)   then
					wb[1] = "唔？哪来的黄毛小儿。走开！今妖爷爷高兴不想抓你！(等级>=50,至少三人组队)"
			  	 	xx = {"妖怪看打","路过，随便看看"}
			  	 end
		  	 end
	  		return {"律法女娲","蝰蛇将军",wb[1],xx}

		end
	elseif ID == 6025 then
		if 编号 == 1 then
		    wb[1] = "前面就是水陆道场，少侠是否需要过去呢？"
	  	 	xx = {"快送我过去","路过，随便看看"}
	  		return {"男人_马副将","传送侍卫",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_师爷","代笔师爷",wb[1],xx}
	  	elseif 编号 == 3 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_老伯","瓜农",wb[1],xx}
	  	elseif 编号 == 4 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_染色师","绣娘",wb[1],xx}
	  	elseif 编号 == 5 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_兰虎","喜轿轿夫",wb[1],xx}
	  	elseif 编号 == 6 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_苦力","卖艺者",wb[1],xx}
	  	elseif 编号 == 7 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_村长","乞丐乙",wb[1],xx}
	  	elseif 编号 == 8 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_丫鬟","阿咪",wb[1],xx}
	  	elseif 编号 == 9 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"珍品商人","马商",wb[1],xx}
	  	elseif 编号 == 10 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_绿儿","小顽童",wb[1],xx}
	  	elseif 编号 == 11 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_绿儿","卖花童",wb[1],xx}
	  	elseif 编号 == 12 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_丫鬟","丫鬟",wb[1],xx}
	  	elseif 编号 == 13 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"普陀_接引仙女","富家小姐",wb[1],xx}
	  	elseif 编号 == 14 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_苦力","卖鱼人",wb[1],xx}
	  	elseif 编号 == 15 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_苦力","面点师傅",wb[1],xx}
	  	elseif 编号 == 16 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"女人_王大嫂","针线娘子",wb[1],xx}
	  	elseif 编号 == 17 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"樵夫","樵夫",wb[1],xx}
	  	elseif 编号 == 18 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_老财","大财主",wb[1],xx}
	  	elseif 编号 == 19 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_衙役","曾衙役",wb[1],xx}
	  	elseif 编号 == 20 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_村长","布陈太医",wb[1],xx}
	  	elseif 编号 == 21 then
	  		wb[1] = "繁华的京城，热闹极了"
	  		return {"男人_书生","游方郎中",wb[1],xx}
		end
	elseif ID == 1249 then
		if 编号==1 then
		    wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return{"女人_云小奴","传送侍卫",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1250 then
		if 编号==1 then
		    wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return{"女人_云小奴","传送侍卫",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 1251 then
		if 编号==1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				--xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
				xx={"门派暂时没开放"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="花果山" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "齐天大圣" and 任务数据[任务id].人物地图 == 1251 then
				    wb[1] = "原来是你给俺送东西来了。怎么改行当镖师了？也罢，就让俺跟你讲讲怎么劫镖吧，当年俺可是瓦岗寨劫镖的第一先锋，劫了多少镖都没有失手过。哦？你现在在运镖？俺搞错了。俺当年劫的可都是贪官污吏的脏银。现在天下太平，你好好做吧。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "俺的武艺不想带进棺材里，是该找个传人的时候了"
				wb[2] = "一屋不扫何以天下？修身与治国平天下同等重要。"
				wb[3] = "俺老程的三板斧可是天下有名的。"
				wb[4] = "大唐武艺，天下无双。弟子们江湖行走可别坏了师门的名声"
				wb[5] = "做了官还要天天上朝，真是麻烦。"
				wb[6] = "学本领要虚心，可不能浮躁自满。"
				wb[7] = "学会俺的一身本领，闯荡江湖绰绰有余。"
			end
			return{"孙悟空","齐天大圣",wb[取随机数(1,#wb)],xx,"门派师傅"}
		elseif 编号==2 then
		    wb[1] = "我送阁下回长安吧。"
			xx = {"是的我要去","我还要逛逛"}
			return{"女人_云小奴","传送侍卫",wb[取随机数(1,#wb)],xx}
		end
	elseif ID==1252 then
		if 编号==1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				--xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
				xx={"门派暂时没开放"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="女魃墓" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "齐天大圣" and 任务数据[任务id].人物地图 == 1251 then
				    wb[1] = "原来是你给俺送东西来了。怎么改行当镖师了？也罢，就让俺跟你讲讲怎么劫镖吧，当年俺可是瓦岗寨劫镖的第一先锋，劫了多少镖都没有失手过。哦？你现在在运镖？俺搞错了。俺当年劫的可都是贪官污吏的脏银。现在天下太平，你好好做吧。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "俺的武艺不想带进棺材里，是该找个传人的时候了"
				wb[2] = "一屋不扫何以天下？修身与治国平天下同等重要。"
				wb[3] = "俺老程的三板斧可是天下有名的。"
				wb[4] = "大唐武艺，天下无双。弟子们江湖行走可别坏了师门的名声"
				wb[5] = "做了官还要天天上朝，真是麻烦。"
				wb[6] = "学本领要虚心，可不能浮躁自满。"
				wb[7] = "学会俺的一身本领，闯荡江湖绰绰有余。"
			end
			return{"金圣宫","天女魃",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID==1253 then
		if 编号==1 then
			xx = {"交谈","给予","师门任务","学习技能","兑换乾元丹","退出门派","路过，回来看看师傅！"}
			if 玩家数据[数字id].角色.数据.门派 == "无" or 玩家数据[数字id].角色.数据.门派 == "无门派" then
				--xx={"请收我为徒","三脚猫的功夫也想当我师傅？你配吗？"}
				xx={"门派暂时没开放"}
	    	elseif 玩家数据[数字id].角色.数据.门派~="天机城" then
		    	xx={}
			end
			if 玩家数据[数字id].角色:取任务(300) ~=0 then
			    local 任务id = 玩家数据[数字id].角色:取任务(300)
				if 任务数据[任务id].人物 == "齐天大圣" and 任务数据[任务id].人物地图 == 1251 then
				    wb[1] = "原来是你给俺送东西来了。怎么改行当镖师了？也罢，就让俺跟你讲讲怎么劫镖吧，当年俺可是瓦岗寨劫镖的第一先锋，劫了多少镖都没有失手过。哦？你现在在运镖？俺搞错了。俺当年劫的可都是贪官污吏的脏银。现在天下太平，你好好做吧。"
				    任务处理类:完成押镖任务(任务id,数字id,任务数据[任务id].人物地图)
				    xx={}
				else
					wb[1] = "少侠是不是运送错了地方呢，再仔细看看任务提示！"
					xx={}
				end
			else
				wb[1] = "俺的武艺不想带进棺材里，是该找个传人的时候了"
				wb[2] = "一屋不扫何以天下？修身与治国平天下同等重要。"
				wb[3] = "俺老程的三板斧可是天下有名的。"
				wb[4] = "大唐武艺，天下无双。弟子们江湖行走可别坏了师门的名声"
				wb[5] = "做了官还要天天上朝，真是麻烦。"
				wb[6] = "学本领要虚心，可不能浮躁自满。"
				wb[7] = "学会俺的一身本领，闯荡江湖绰绰有余。"
			end
			return{"鲁班","小夫子",wb[取随机数(1,#wb)],xx,"门派师傅"}
		end
	elseif ID == 1332 then
		if 编号 == 1 then
		    wb[1] = "趁女主人不在家快来跟我一起做点啥吧"
	  	 	xx = {"干"}
	  		return {"女人_丫鬟","女佣",wb[1],xx}
	  	elseif 编号 == 2 then
	  		wb[1] = "繁华的京城，热闹极了"
	  	 	xx = {"孩子管理"}
	  		return {"男人_师爷","管家",wb[1],xx}
	  	end


	  	-------------摩托新增齐天大圣副本对话
	elseif ID == 6037 then
		if 编号 == 1 then
			wb[1] = "大仙,生死簿存放在阎王手中,你找小仙也没有用啊！"
			local 任务id = 玩家数据[数字id].角色:取任务(191)
			if 任务id ~= 0 then
				local 副本id  = 任务数据[任务id].副本id
				if 副本数据.齐天大圣.进行[副本id].进程 == 3 then
					副本数据.齐天大圣.进行[副本id].进程 = 4
					玩家数据[数字id].角色:刷新任务跟踪()
				end
			end
			return{"男人_判官","判官",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "生死簿乃是地府重宝,岂是尔等可以索要的！"
			local 任务id = 玩家数据[数字id].角色:取任务(191)
			if 任务id ~= 0 and (玩家数据[数字id].队伍 == 0 or 玩家数据[数字id].队长) then
				local 副本id  = 任务数据[任务id].副本id
				if 副本数据.齐天大圣.进行[副本id].进程 == 4 then
					xx = {"那就别怪我不客气","我再想办法"}
				end
			end
			return{"阎罗王","阎王",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 6038 then
		if 编号 == 1 then
			wb[1] = "要想做官得让朕先看看你的本事！"
			local 任务id = 玩家数据[数字id].角色:取任务(191)
			if 任务id ~= 0 and (玩家数据[数字id].队伍 == 0 or 玩家数据[数字id].队长) then
				local 副本id  = 任务数据[任务id].副本id
				if 副本数据.齐天大圣.进行[副本id].进程 == 7 then
					wb[1] = "你能穿上朝服，管理天上所有的骏马,还能拥有好听的官名“弼马温”怎么样！"
					副本数据.齐天大圣.进行[副本id].进程 = 8
					副本数据.齐天大圣.进行[副本id].盗马贼 = 10
					任务处理类:设置齐天大圣副本(副本id)
					玩家数据[数字id].角色:刷新任务跟踪()
					xx ={}
				elseif 副本数据.齐天大圣.进行[副本id].进程 == 6 then
					xx = {"那老孙就露两手","我考虑考虑"}
				end
			end
			return{"男人_玉帝","玉皇大帝",wb[取随机数(1,#wb)],xx}
		elseif 编号 == 2 then
			wb[1] = "遥望月宫仙子,口水就直流啊！"
			local 任务id = 玩家数据[数字id].角色:取任务(191)
			if 任务id ~= 0 and (玩家数据[数字id].队伍 == 0 or 玩家数据[数字id].队长) then
				local 副本id  = 任务数据[任务id].副本id
				if 副本数据.齐天大圣.进行[副本id].进程 == 10 then
					wb[1] = "你就是一个不入流的弼马温,专门看马的而已,哈哈哈,知道我是谁么,我乃天庭十万水军元帅,天蓬元帅是也！"
					副本数据.齐天大圣.进行[副本id].进程 = 11
					任务处理类:设置齐天大圣副本(副本id)
					玩家数据[数字id].角色:刷新任务跟踪()
					xx ={}
					地图处理类:跳转地图(数字id,6036,12,108)
				end
			end
			return{"猪八戒","天蓬元帅",wb[取随机数(1,#wb)],xx}
		end
	elseif ID == 6039 then
		if 编号 == 1 then
			wb[1] = "我乃塔中神灵！"
			local 任务id = 玩家数据[数字id].角色:取任务(191)
			if 任务id ~= 0 then
				local 副本id  = 任务数据[任务id].副本id
				if 副本数据.齐天大圣.进行[副本id].进程 == 12 then
					wb[1] = "想要出塔,得先过我这一关！"
					xx ={"放马过来","我只是来看看"}
				end
			end
			return{"男人_将军","镇塔之神",wb[取随机数(1,#wb)],xx}
		end




  	end
 	return
end



return 对话处理类