

function 战斗处理类:法宝计算(编号)

    local 指令 = self.参战单位[编号].指令
    local 目标, 格子 = 指令.目标, 指令.参数
    local 玩家id = self.参战单位[编号].玩家id
    local 道具id = 玩家数据[玩家id].角色.数据.法宝[格子]
    if not 道具id then return end
    local 道具 = 玩家数据[玩家id].道具.数据[道具id]
    if not 道具 then return end
    local 名称 = 道具.名称
    if not 战斗技能[名称] or not 战斗技能[名称].法宝 then
        return
    end
    if 道具.回合 and 道具.回合 > self.回合数 then
        self:添加提示(玩家id, 编号, "#Y/该法宝在当前回合无法使用")
        return
    end
    if self.参战单位[目标].气血 <= 0 then
        self:添加提示(玩家id, 编号, "#Y/目标已处于死亡状态，无法对其使用法宝")
        return
    elseif self.参战单位[目标].队伍 == 0 then
        self:添加提示(玩家id, 编号, "#Y/你无法对这样的目标使用法宝")
        return
    end
    if self.参战单位[编号].法术状态.错乱 and self.参战单位[编号].法术状态.错乱.震慑 then
        self:添加提示(玩家id, 编号, "#Y/当前状态不可使用法宝")
        return
    end
    local 物品数据 = 取物品数据(名称)
    self.执行等待 = self.执行等待 + 10
    玩家数据[玩家id].道具.数据[道具id].回合 = self.回合数 + (物品数据[7] or 0)
    玩家数据[玩家id].道具.数据[道具id].魔法 = 玩家数据[玩家id].道具.数据[道具id].魔法 - 1
    发送数据(玩家数据[玩家id].连接id, 38, {内容="你的法宝减少了1点灵气"})
    local 处理函数 = {
        增益 = function()
            self:增益技能计算(编号, 名称,0,nil,玩家数据[玩家id].道具.数据[道具id].气血)
        end,
        减益 = function()
            self:减益技能计算(编号, 名称,0,nil,玩家数据[玩家id].道具.数据[道具id].气血)
        end,
        封印 = function()
            self:封印技能计算(编号, 名称,0,nil,玩家数据[玩家id].道具.数据[道具id].气血)
        end,
        恢复 = function()
            self:治疗技能计算(编号, 名称,0,nil,玩家数据[玩家id].道具.数据[道具id].气血)
        end
    }
    if 战斗技能[名称].冷却 then
        if self.参战单位[编号][名称] then
            self:添加提示(self.参战单位[编号].玩家id,编号, "#Y该技能当前处于冷却中还需："..self.参战单位[编号][名称].."回合后才可使用")
            return
        else
            local 冷却 = 战斗技能[名称].冷却(self,编号)
            if 战斗技能[名称].技能流程 then
                  战斗技能[名称].技能流程(self,编号,self.参战单位[编号].指令.目标,消耗)
            elseif 处理函数[战斗技能[名称].类型] then
                    处理函数[战斗技能[名称].类型]()
            end
            if 冷却 and 冷却.使用 > 0 then
                    self.参战单位[编号][名称] = 冷却.使用
            end
        end
    elseif 战斗技能[名称].技能流程 then
            战斗技能[名称].技能流程(self,编号,self.参战单位[编号].指令.目标,消耗)
    elseif 处理函数[战斗技能[名称].类型] then
            处理函数[战斗技能[名称].类型]()
    end
    if self.参战单位[编号].奇经八脉.神念 then
        self:增加愤怒(编号, 10)
    end
end


function 战斗处理类:灵宝计算(编号)  ------------------------------灵宝
        local 玩家id = self.参战单位[编号].玩家id
        local 道具数据 = self.参战单位[编号].灵宝佩戴[self.参战单位[编号].指令.参数]
        if not 道具数据 then return end
        local 名称 = 道具数据.名称
        local 等级 = self.参战单位[编号].等级
        local 消耗 = 1
         if self.参战单位[编号].灵元.数值 == 0 then
          self:添加提示(self.参战单位[编号].玩家id, 编号, "#Y/灵元不足无法使用灵宝")
          return
         end
        if 名称 == "定神仙琴" or 名称 == "护体灵盾" or 名称 == "惊兽云尺" then
            消耗 =1
        else
            if self.参战单位[编号].灵元.数值 >=7 then
              消耗 =7
            elseif self.参战单位[编号].灵元.数值 >=3 then
              消耗 =3
            elseif self.参战单位[编号].灵元.数值 >=1 then
              消耗 =1
            end
        end
        if not 战斗技能[名称] or not 战斗技能[名称].灵宝 then
            return
        end
        if 消耗>self.参战单位[编号].灵元.数值 then
            self:添加提示(self.参战单位[编号].玩家id, 编号, "#Y/灵元不足无法使用灵宝")
          return
        end
        self.参战单位[编号].灵元.数值  = self.参战单位[编号].灵元.数值-消耗
        local 处理函数 = {
            物伤 = function()
                self:物攻技能计算(编号,名称,道具数据.气血,nil,1,消耗)
            end,
            法伤 = function()
                self:法伤技能计算(编号,名称,道具数据.气血,nil,1,消耗)
            end,
            固伤 = function()
                self:法伤技能计算(编号,名称,道具数据.气血,nil,1,消耗)
            end,
            增益 = function()
                self:增益技能计算(编号, 名称,道具数据.气血, nil,消耗)
            end,
            减益 = function()
                self:减益技能计算(编号, 名称,道具数据.气血, nil,消耗)
            end,
            封印 = function()
                self:封印技能计算(编号, 名称,道具数据.气血, nil,消耗)
            end,
            恢复 = function()
                self:治疗技能计算(编号, 名称,道具数据.气血, nil,消耗)
            end
        }

        if 战斗技能[名称].冷却 then
            if self.参战单位[编号][名称] then
                self:添加提示(self.参战单位[编号].玩家id,编号, "#Y该技能当前处于冷却中还需："..self.参战单位[编号][名称].."回合后才可使用")
                return
            else
                local 冷却 = 战斗技能[名称].冷却(self,编号)
                if 战斗技能[名称].技能流程 then
                    战斗技能[名称].技能流程(self,编号,self.参战单位[编号].指令.目标,消耗)
                elseif 处理函数[战斗技能[名称].类型] then
                      处理函数[战斗技能[名称].类型]()
                end
                if 冷却 and 冷却.使用 > 0 then
                    self.参战单位[编号][名称] = 冷却.使用
                end
            end
        elseif 战斗技能[名称].技能流程 then
                战斗技能[名称].技能流程(self,编号,self.参战单位[编号].指令.目标,消耗)
        elseif 处理函数[战斗技能[名称].类型] then
              处理函数[战斗技能[名称].类型]()
        end

end


function 战斗处理类:药物特性处理(编号,目标,药性,阶品,流程)
          if 药性 and 药性=="藏神" then
              self:添加状态组处理(编号,目标,{目标=目标},"护盾",阶品*8+75,流程,1)
          end
          local 触发 = 0
          if self.参战单位[编号].类型=="角色" and self.参战单位[编号].奇经八脉.烛照 then
                触发 = 26
          end
          if self.参战单位[编号].类型~="角色" and self.参战单位[编号].主人~=nil and self.参战单位[self.参战单位[编号].主人].奇经八脉.烛照 then
                触发 = 26
          end
          if 取随机数()<=触发 then
               self:添加状态组处理(编号,目标,{目标=目标},"护盾",阶品*8+75,流程,1)
          end
end



function 战斗处理类:道具计算(编号)
        if not 编号 or not self.参战单位[编号] or not self.参战单位[编号].玩家id
          or not 玩家数据[self.参战单位[编号].玩家id] then
            return
        end
        if self.参战单位[编号].道具类型=="法宝" then
           self:法宝计算(编号)
            return
        end
        if self.参战单位[编号].道具类型 == "灵宝" then
           self:灵宝计算(编号)
           return
        end
        local 目标=self.参战单位[编号].指令.目标
        local 道具=self.参战单位[编号].指令.参数
        local 玩家id=self.参战单位[编号].玩家id
        local 道具1=玩家数据[玩家id].角色.数据.道具[道具]
        if not 道具1 or not 玩家数据[玩家id].道具.数据[道具1] then
          self:添加提示(self.参战单位[编号].玩家id,编号,"#Y/你没有这样的道具")
          return
        end
        if self.参战单位[编号].灵宝缚仙蛛丝 then
            for i=1,#self.参战单位[编号].灵宝缚仙蛛丝 do
                if 道具==self.参战单位[编号].灵宝缚仙蛛丝[i] then
                    self:添加提示(self.参战单位[编号].玩家id,编号,"#Y/你无法使用该道具")
                    return
                end
            end
        end
        local 道具数据=DeepCopy(玩家数据[玩家id].道具.数据[道具1])
        local 名称=道具数据.名称
        local 使用=false
        local 使用类型
        local 类型映射 = {
            加血 = {"金创药","小还丹","千年保心丹","金香玉","五龙丹","天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑","火凤之睛","四叶花","天青地白","七叶莲","草果","九香虫","水黄莲","山药","八角莲叶","人参"},
            加魔 = {"翡翠豆腐","佛跳墙","蛇蝎美人","风水混元丹","定神香","十香返生丸","丁香水","月星子","仙狐涎","地狱灵芝","麝香","血珊瑚","餐风饮露","白露为霜","天龙水","孔雀红","紫丹罗","佛手","旋复花","龙须草","百色花","香叶","白玉骨头","鬼切草","灵脂","曼陀罗花"},
            复活 = {"佛光舍利子","九转回魂丹"},
            酒类 = {"珍露酒","虎骨酒","女儿红","蛇胆酒","醉生梦死","梅花酒","百味酒"}
        }
        local 使用类型
        for 类型, 列表 in pairs(类型映射) do
            for _, 道具名 in ipairs(列表) do
                if 道具名 == 名称 then
                    使用类型 = 类型
                    break
                end
            end
            if 使用类型 then break end
        end
        if not 使用类型 then
            if 名称 == "乾坤袋" then
                使用类型 = "乾坤袋"
            elseif 道具数据.总类 == 2000 then
                使用类型 = "飞镖"
            end
        end
        if not 使用类型 then
            self:添加提示(玩家id, 编号, "#Y/此类道具无法在战斗中使用")
            return
        end
        self.执行等待=self.执行等待+8
        if 使用类型=="加血" then
              if self.参战单位[目标].法术状态.魔音摄魂 or self.参战单位[目标].气血<=0 then
                    self:添加提示(玩家id,编号,"当前状态无法使用物品")
                    return
              else
                    self.执行等待=self.执行等待+5
                    local 流程 = #self.战斗流程+1
                    self.战斗流程[流程] = {流程=27,攻击方=编号,
                                      挨打方={
                                          {挨打方=目标,特效={"加血"}}
                                          },
                                      提示={允许=true,
                                          类型="加血",
                                          名称=名称
                                          }
                                    }
                    local 基础 = DeepCopy(self.计算属性)
                    基础.初始伤害 = 玩家数据[玩家id].道具:取加血道具1(名称,道具1)
                    local 结果 = self:取基础治疗计算(编号,目标,"回血",self.参战单位[编号].等级,基础)
                    if self.参战单位[目标].识药特性~=nil then
                          结果.气血=math.floor(结果.气血*(1+self.参战单位[目标].识药特性*0.05))
                    end
                    local 药性 = 道具数据.药性
                    local 阶品 = 道具数据.阶品
                    if 药性 and 药性=="倍愈" then
                        结果.气血=结果.气血*2
                    end
                    self:增加气血(目标,结果.气血)
                    self.战斗流程[流程].挨打方[1].伤害 = 结果.气血
                    self.战斗流程[流程].挨打方[1].类型 = 2
                    self:药物特性处理(编号,目标,药性,阶品,流程)
                    使用=true
                    if 名称=="五龙丹" then
                            self.战斗流程[流程].挨打方[1].取消状态=self:解除状态组(编号,目标,self.技能数据.异常,"五龙丹",#self.战斗流程,1)
                            self:解除所有经脉异常(目标)
                            if #self.战斗流程[流程].挨打方[1].取消状态==0 then
                                self:添加状态("催眠符",目标,编号,150)
                                self.参战单位[目标].法术状态.催眠符.回合 = 5
                                self:处理流程状态(self.战斗流程[流程].挨打方[1],"催眠符",目标)
                            end
                            if self.参战单位[目标].奇经八脉.背水 then
                                  self:添加状态("背水",目标,编号,150)
                                  self:处理流程状态(self.战斗流程[流程].挨打方[1],"背水",目标)
                            end
                      elseif 名称=="小还丹" or 名称=="千年保心丹" or 名称=="九转回魂丹" or 名称=="佛光舍利子" or 名称=="山药" or 名称=="八角莲叶" or 名称=="人参"
                            or 名称=="草果" or 名称=="九香虫" or 名称=="水黄莲" then
                                self:恢复伤势(目标,结果.气血)
                                self.战斗流程[流程].挨打方[1].伤势 = 结果.气血
                                self.战斗流程[流程].挨打方[1].伤势类型 = 2
                      end
              end
        elseif 使用类型=="加魔" then
                if self.参战单位[目标].法术状态.魔音摄魂 or self.参战单位[目标].气血<=0 then
                    self:添加提示(玩家id,编号,"当前状态无法使用物品")
                    return
                else
                      self.执行等待=self.执行等待+5
                      local 流程 = #self.战斗流程+1
                      self.战斗流程[流程] = {流程=27,攻击方=编号,
                                              挨打方={
                                                  {挨打方=目标,特效={"加蓝"}}
                                                  },
                                              提示={允许=true,
                                                  类型="加蓝",
                                                  名称=名称
                                                }
                                            }

                      local 临时数值=玩家数据[玩家id].道具:取加魔道具1(名称,道具1)
                      if self.参战单位[目标].识药特性 then
                          临时数值=math.floor(临时数值*(1+self.参战单位[目标].识药特性*0.05))
                      end
                      local 药性 = 道具数据.药性
                      local 阶品 = 道具数据.阶品
                      if 药性 ~= nil and 药性=="倍愈" then
                          临时数值=临时数值*2
                      end
                      self:增加魔法(目标,临时数值)
                      self:药物特性处理(编号,目标,药性,阶品,流程)
                      使用=true
                end
        elseif 使用类型=="复活" then
              if self.参战单位[目标].类型~="角色" or self.参战单位[目标].法术状态.魔音摄魂 or self.参战单位[目标].法术状态.死亡召唤
                or self.参战单位[目标].法术状态.锢魂术 or self.参战单位[目标].气血>0 then
                  self:添加提示(玩家id,编号,"当前状态无法使用物品")
                  return
              else
                    self.执行等待=self.执行等待+5
                    local 流程 = #self.战斗流程+1
                    self.战斗流程[流程] = {流程=27,攻击方=编号,
                                      挨打方={
                                          {挨打方=目标,解除状态={},特效={"加血"}}
                                          },
                                      提示={允许=true,
                                          类型="复活",
                                          名称=名称
                                        }
                                    }
                    local 基础 = DeepCopy(self.计算属性)
                    基础.初始伤害 = 玩家数据[玩家id].道具:取加血道具1(名称,道具1)
                    local 结果=self:取基础治疗计算(编号,目标,"回血",self.参战单位[编号].等级,基础)
                    if self.参战单位[目标].识药特性 then
                          结果.气血=math.floor(结果.气血*(1+self.参战单位[目标].识药特性*0.05))
                    end
                    local 药性 = 道具数据.药性
                    local 阶品 = 道具数据.阶品
                    if 药性 ~= nil and 药性=="倍愈" then
                        结果.气血=结果.气血*2
                    end
                    self:恢复伤势(目标,结果.气血)
                    self.战斗流程[流程].挨打方[1].伤势 = 结果.气血
                    self.战斗流程[流程].挨打方[1].伤势类型=2
                    self:增加气血(目标,结果.气血)
                    self.战斗流程[流程].挨打方[1].伤害 = 结果.气血
                    self.战斗流程[流程].挨打方[1].类型 = 2
                    self.战斗流程[流程].挨打方[1].复活 = true
                    table.insert(self.执行复活,目标)
                    self.回合复活=true
                    self:药物特性处理(编号,目标,药性,阶品,流程)
                    使用=true
              end
      elseif 使用类型=="酒类" then
              if self.参战单位[目标].类型~="角色" or self.参战单位[目标].法术状态.魔音摄魂 or self.参战单位[目标].气血<=0 then
                  self:添加提示(玩家id,编号,"当前状态无法使用物品")
                  return
              else
                    self.执行等待=self.执行等待+5
                    local 流程 = #self.战斗流程+1
                    self.战斗流程[流程] = {流程=27,攻击方=编号,
                                      挨打方={
                                          {挨打方=目标,特效={"加蓝"}}
                                          },
                                      提示={允许=true,
                                          类型="喝酒",
                                          名称=名称
                                        }
                                    }
                    local 临时数值=玩家数据[玩家id].道具:取加魔道具1(名称,道具1)
                    self:增加愤怒(目标,临时数值)
                    使用=true
                    if 名称=="女儿红" or 名称=="梅花酒"  then
                            self:添加状态("催眠符",目标,编号,150)
                            self.参战单位[目标].法术状态.催眠符.回合 = 取随机数(2,3)
                            self:处理流程状态(self.战斗流程[流程].挨打方[1],"催眠符",目标)
                    elseif 名称=="蛇胆酒" then
                            self.参战单位[目标].防御=self.参战单位[目标].防御-math.floor(临时数值*1.5)
                            if   self.参战单位[目标].防御<=0 then
                                  self.参战单位[目标].防御 = 0
                            end
                            if 取随机数()<=10 then
                                  self:添加状态("催眠符",目标,编号,150)
                                  self.参战单位[目标].法术状态.催眠符.回合 = 取随机数(2,3)
                                  self:处理流程状态(self.战斗流程[流程].挨打方[1],"催眠符",目标)
                            elseif 取随机数()<=10 then
                                  self:添加状态("疯狂",目标,编号,150)
                                  self.参战单位[目标].法术状态.疯狂.回合 = 取随机数(2,3)
                                  self:处理流程状态(self.战斗流程[流程].挨打方[1],"疯狂",目标)
                            end
                    elseif 名称=="珍露酒" then
                            if 取随机数()<=20 then
                                self:添加状态("催眠符",目标,编号,150)
                                self.参战单位[目标].法术状态.催眠符.回合 = 取随机数(2,3)
                                self:处理流程状态(self.战斗流程[流程].挨打方[1],"催眠符",目标)
                            elseif 取随机数()<=20 then
                                    self:添加状态("疯狂",目标,编号,150)
                                    self.参战单位[目标].法术状态.疯狂.回合 = 取随机数(2,3)
                                    self:处理流程状态(self.战斗流程[流程].挨打方[1],"疯狂",目标)
                            elseif 取随机数()<=20 then
                                    self:添加状态("暗器毒",目标,编号,150)
                                    self.参战单位[目标].法术状态.暗器毒.回合 = 5
                                    self:处理流程状态(self.战斗流程[流程].挨打方[1],"暗器毒",目标)
                            end
                    elseif 名称=="百味酒" then
                            if 取随机数()<=50 then
                                self:添加状态("催眠符",目标,编号,150)
                                self.参战单位[目标].法术状态.催眠符.回合 = 取随机数(2,3)
                                self:处理流程状态(self.战斗流程[流程].挨打方[1],"催眠符",目标)
                            else
                                self:添加状态("暗器毒",目标,编号,150)
                                self.参战单位[目标].法术状态.暗器毒.回合 = 5
                                self:处理流程状态(self.战斗流程[流程].挨打方[1],"暗器毒",目标)
                            end
                    elseif 名称=="醉生梦死" or 名称=="虎骨酒"  then
                            self:添加状态("疯狂",目标,编号,150)
                            self.参战单位[目标].法术状态.疯狂.回合 = 取随机数(2,3)
                            self:处理流程状态(self.战斗流程[流程].挨打方[1],"疯狂",目标)
                    end
              end
      elseif 使用类型=="乾坤袋" then
              if not self.参战单位[目标].乾坤袋 then
                  self:添加提示(玩家id,编号,"#Y/你无法对此类目标使用乾坤袋")
                  return
              elseif self.参战单位[编号].类型~="角色" then
                  self:添加提示(玩家id,编号,"#Y/只有角色才可以使用此道具")
                  return
              else
                    self.执行等待=self.执行等待+5
                    local 流程 = #self.战斗流程+1
                    self.战斗流程[流程] = {流程=21,攻击方=编号,
                                      挨打方={
                                          {挨打方=目标,特效={"乾坤袋"}}
                                          },
                                      提示={允许=true,
                                          类型="收妖",
                                          名称=名称
                                        }
                                    }

                    local 百分比=math.floor(100-self.参战单位[编号].气血/self.参战单位[编号].最大气血*100)
                    百分比=百分比+20
                    if 百分比>=取随机数() then
                        self.战斗流程[流程].挨打方[1].伤害=self.参战单位[目标].气血
                        self.战斗流程[流程].挨打方[1].类型=1
                        self.战斗流程[流程].挨打方[1].死亡=self:减少气血(目标,self.参战单位[目标].气血,编号)
                        任务数据[self.任务id].乾坤袋=true
                        玩家数据[玩家id].角色:刷新任务跟踪()
                    end
              end
      elseif 使用类型=="飞镖" then
              local 等级 = self:取技能等级(编号,"满天花雨")
              local 人数 = math.floor(等级/25)+1
              local 伤害 = 道具数据.分类
              local 等级伤害 = math.floor(等级/40)*伤害
              local 暗器伤害 = math.floor(玩家数据[玩家id].角色:取生活技能等级("暗器技巧")*2)
              if self.参战单位[编号].奇经八脉.花雨 and self.回合数%2 == 0 and self.参战单位[编号].乐韵 then
                  self.参战单位[编号].乐韵 = self.参战单位[编号].乐韵 + 1
              end
              if self.战斗类型 == 110009 and self.参战单位[目标].模型=="星灵仙子" then
                  伤害=伤害*50
              end
              local 目标组=self:取敌方目标组(编号,目标,人数,名称)
              伤害=伤害*0.7+暗器伤害/2+等级伤害*0.5
              if #目标组>0 then
                  self.执行等待=self.执行等待+5
                  local 流程=#self.战斗流程 + 1
                  self.战斗流程[流程] ={流程=57,攻击方=编号,挨打方={},提示={允许=true,类型="暗器",名称=名称}}
                  for n=1,#目标组 do
                      if self:取目标状态(编号,目标组[n],1) then
                          local 挨打 = #self.战斗流程[流程].挨打方 + 1
                          local 基础 = DeepCopy(self.计算属性)
                          local 数据 = {次数=n,总数=#目标组,流程=流程,挨打=挨打}
                          战斗技能.满天花雨.基础计算(self,编号,目标组[n],等级,数据,基础,"伤害")
                          基础.特效 = {}
                          local 最后伤害 = 伤害 +  self:取基础固伤伤害(编号,目标组[n],"满天花雨",等级,基础)
                          if self.参战单位[编号].奇经八脉.暗刃 and self.参战单位[目标组[n]].法术状态.暗器毒 then
                              最后伤害 = 最后伤害 * 1.18
                          end
                          self.战斗流程[流程].挨打方[挨打] = {挨打方=目标组[n],伤害=最后伤害}
                          self.战斗流程[流程].死亡 = self:减少气血(目标组[n],最后伤害,编号,"飞镖")
                          if 基础.取消状态 then
                              self:解除状态组处理(编号,目标组[n],基础.取消状态,"满天花雨",流程,挨打)
                          end
                          if 基础.添加状态 then
                              self:添加状态组处理(编号,目标组[n],基础.添加状态,"满天花雨",等级,流程,挨打)
                          end
                      end
                  end
                  玩家数据[玩家id].道具.数据[道具1].耐久=玩家数据[玩家id].道具.数据[道具1].耐久-1
                  if 玩家数据[玩家id].道具.数据[道具1].耐久<=0 then
                      玩家数据[玩家id].道具.数据[道具1]=nil
                      玩家数据[玩家id].角色.数据.道具[道具]=nil
                  end
              end
    end
    if 使用 then
        if 名称=="小还丹" or 名称=="千年保心丹" or 名称=="九转回魂丹" or 名称=="佛光舍利子" or 名称=="蛇蝎美人" or 名称=="风水混元丹" or
            名称=="定神香"or 名称=="十香返生丸" or 名称=="佛光舍利子" or 名称=="九转回魂丹" or 名称=="五龙丹" then
             if self.参战单位[编号].奇经八脉.丹香 then
                  self:增加愤怒(编号,2)
             end
             if self.参战单位[编号].奇经八脉.执念 then
                local 随机五行 = {"金","木","水","火","土"}
                local 随机珠子 = 随机五行[取随机数(1,5)]
                self.参战单位[编号].五行珠[随机珠子] = 1
             end
            if self.参战单位[编号].门派=="神木林" and self.参战单位[编号].风灵 and self.参战单位[编号].经脉流派 ~="灵木药宗"
              and self.参战单位[编号].神器技能 and self.参战单位[编号].神器技能.名称=="钟灵" then
                local 临时数额 = self.参战单位[编号].神器技能.等级*20
                if 取随机数()<=临时数额 then
                   self.参战单位[编号].风灵 = self.参战单位[编号].风灵 + 1
                end
             end
        end
        if 道具数据.数量 then
          玩家数据[玩家id].道具.数据[道具1].数量=玩家数据[玩家id].道具.数据[道具1].数量-1
        end
        if not 道具数据.数量 or 玩家数据[玩家id].道具.数据[道具1].数量<=0 then
            玩家数据[玩家id].道具.数据[道具1]=nil
            玩家数据[玩家id].角色.数据.道具[道具]=nil
        end
    end
end



