{"ast": null, "code": "import * as React from 'react';\nfunction Panel(_ref) {\n  var className = _ref.className,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, children);\n}\nexport default Panel;", "map": {"version": 3, "names": ["React", "Panel", "_ref", "className", "children", "createElement"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/rc-table/es/Panel/index.js"], "sourcesContent": ["import * as React from 'react';\nfunction Panel(_ref) {\n  var className = _ref.className,\n    children = _ref.children;\n  return /*#__PURE__*/React.createElement(\"div\", {\n    className: className\n  }, children);\n}\nexport default Panel;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAKA,CAACC,IAAI,EAAE;EACnB,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC5BC,QAAQ,GAAGF,IAAI,CAACE,QAAQ;EAC1B,OAAO,aAAaJ,KAAK,CAACK,aAAa,CAAC,KAAK,EAAE;IAC7CF,SAAS,EAAEA;EACb,CAAC,EAAEC,QAAQ,CAAC;AACd;AACA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}