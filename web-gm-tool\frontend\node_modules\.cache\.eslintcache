[{"C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\contexts\\SocketContext.js": "3", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\pages\\Dashboard.js": "4", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\pages\\Login.js": "5", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\contexts\\AuthContext.js": "6", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\RechargePanel.js": "7", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\ActivityPanel.js": "8", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\AccountPanel.js": "9", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\StatusPanel.js": "10", "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\EquipmentPanel.js": "11"}, {"size": 715, "mtime": *************, "results": "12", "hashOfConfig": "13"}, {"size": 4898, "mtime": *************, "results": "14", "hashOfConfig": "13"}, {"size": 300, "mtime": *************, "results": "15", "hashOfConfig": "13"}, {"size": 5792, "mtime": *************, "results": "16", "hashOfConfig": "13"}, {"size": 4025, "mtime": *************, "results": "17", "hashOfConfig": "13"}, {"size": 285, "mtime": *************, "results": "18", "hashOfConfig": "13"}, {"size": 7645, "mtime": *************, "results": "19", "hashOfConfig": "13"}, {"size": 8008, "mtime": *************, "results": "20", "hashOfConfig": "13"}, {"size": 8888, "mtime": *************, "results": "21", "hashOfConfig": "13"}, {"size": 7154, "mtime": *************, "results": "22", "hashOfConfig": "13"}, {"size": 9310, "mtime": 1752671309424, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "8pwmh1", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\contexts\\SocketContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\pages\\Dashboard.js", ["57", "58", "59", "60"], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\pages\\Login.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\contexts\\AuthContext.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\RechargePanel.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\ActivityPanel.js", [], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\AccountPanel.js", ["61", "62"], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\StatusPanel.js", ["63"], [], "C:\\Users\\<USER>\\Desktop\\mh\\web-gm-tool\\frontend\\src\\components\\EquipmentPanel.js", ["64", "65"], [], {"ruleId": "66", "severity": 1, "message": "67", "line": 1, "column": 27, "nodeType": "68", "messageId": "69", "endLine": 1, "endColumn": 36}, {"ruleId": "66", "severity": 1, "message": "70", "line": 11, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 11, "endColumn": 18}, {"ruleId": "66", "severity": 1, "message": "71", "line": 13, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 13, "endColumn": 15}, {"ruleId": "66", "severity": 1, "message": "72", "line": 14, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 14, "endColumn": 21}, {"ruleId": "66", "severity": 1, "message": "73", "line": 3, "column": 52, "nodeType": "68", "messageId": "69", "endLine": 3, "endColumn": 63}, {"ruleId": "66", "severity": 1, "message": "74", "line": 3, "column": 65, "nodeType": "68", "messageId": "69", "endLine": 3, "endColumn": 84}, {"ruleId": "66", "severity": 1, "message": "75", "line": 21, "column": 9, "nodeType": "68", "messageId": "69", "endLine": 21, "endColumn": 15}, {"ruleId": "66", "severity": 1, "message": "76", "line": 3, "column": 52, "nodeType": "68", "messageId": "69", "endLine": 3, "endColumn": 65}, {"ruleId": "66", "severity": 1, "message": "77", "line": 3, "column": 67, "nodeType": "68", "messageId": "69", "endLine": 3, "endColumn": 81}, "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'SettingOutlined' is defined but never used.", "'WifiOutlined' is defined but never used.", "'DisconnectOutlined' is defined but never used.", "'BanOutlined' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'socket' is assigned a value but never used.", "'SwordOutlined' is defined but never used.", "'ShieldOutlined' is defined but never used."]