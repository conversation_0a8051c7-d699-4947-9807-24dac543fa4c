{"ast": null, "code": "import classNames from 'classnames';\nimport * as React from 'react';\nexport default function Arrow(props) {\n  var prefixCls = props.prefixCls,\n    align = props.align,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos;\n  var _ref = arrow || {},\n    className = _ref.className,\n    content = _ref.content;\n  var _arrowPos$x = arrowPos.x,\n    x = _arrowPos$x === void 0 ? 0 : _arrowPos$x,\n    _arrowPos$y = arrowPos.y,\n    y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;\n  var arrowRef = React.useRef();\n\n  // Skip if no align\n  if (!align || !align.points) {\n    return null;\n  }\n  var alignStyle = {\n    position: 'absolute'\n  };\n\n  // Skip if no need to align\n  if (align.autoArrow !== false) {\n    var popupPoints = align.points[0];\n    var targetPoints = align.points[1];\n    var popupTB = popupPoints[0];\n    var popupLR = popupPoints[1];\n    var targetTB = targetPoints[0];\n    var targetLR = targetPoints[1];\n\n    // Top & Bottom\n    if (popupTB === targetTB || !['t', 'b'].includes(popupTB)) {\n      alignStyle.top = y;\n    } else if (popupTB === 't') {\n      alignStyle.top = 0;\n    } else {\n      alignStyle.bottom = 0;\n    }\n\n    // Left & Right\n    if (popupLR === targetLR || !['l', 'r'].includes(popupLR)) {\n      alignStyle.left = x;\n    } else if (popupLR === 'l') {\n      alignStyle.left = 0;\n    } else {\n      alignStyle.right = 0;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: arrowRef,\n    className: classNames(\"\".concat(prefixCls, \"-arrow\"), className),\n    style: alignStyle\n  }, content);\n}", "map": {"version": 3, "names": ["classNames", "React", "Arrow", "props", "prefixCls", "align", "arrow", "arrowPos", "_ref", "className", "content", "_arrowPos$x", "x", "_arrowPos$y", "y", "arrowRef", "useRef", "points", "alignStyle", "position", "autoArrow", "popupPoints", "targetPoints", "popupTB", "popupLR", "targetTB", "targetLR", "includes", "top", "bottom", "left", "right", "createElement", "ref", "concat", "style"], "sources": ["C:/Users/<USER>/Desktop/mh/web-gm-tool/frontend/node_modules/@rc-component/trigger/es/Popup/Arrow.js"], "sourcesContent": ["import classNames from 'classnames';\nimport * as React from 'react';\nexport default function Arrow(props) {\n  var prefixCls = props.prefixCls,\n    align = props.align,\n    arrow = props.arrow,\n    arrowPos = props.arrowPos;\n  var _ref = arrow || {},\n    className = _ref.className,\n    content = _ref.content;\n  var _arrowPos$x = arrowPos.x,\n    x = _arrowPos$x === void 0 ? 0 : _arrowPos$x,\n    _arrowPos$y = arrowPos.y,\n    y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;\n  var arrowRef = React.useRef();\n\n  // Skip if no align\n  if (!align || !align.points) {\n    return null;\n  }\n  var alignStyle = {\n    position: 'absolute'\n  };\n\n  // Skip if no need to align\n  if (align.autoArrow !== false) {\n    var popupPoints = align.points[0];\n    var targetPoints = align.points[1];\n    var popupTB = popupPoints[0];\n    var popupLR = popupPoints[1];\n    var targetTB = targetPoints[0];\n    var targetLR = targetPoints[1];\n\n    // Top & Bottom\n    if (popupTB === targetTB || !['t', 'b'].includes(popupTB)) {\n      alignStyle.top = y;\n    } else if (popupTB === 't') {\n      alignStyle.top = 0;\n    } else {\n      alignStyle.bottom = 0;\n    }\n\n    // Left & Right\n    if (popupLR === targetLR || !['l', 'r'].includes(popupLR)) {\n      alignStyle.left = x;\n    } else if (popupLR === 'l') {\n      alignStyle.left = 0;\n    } else {\n      alignStyle.right = 0;\n    }\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: arrowRef,\n    className: classNames(\"\".concat(prefixCls, \"-arrow\"), className),\n    style: alignStyle\n  }, content);\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,YAAY;AACnC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,eAAe,SAASC,KAAKA,CAACC,KAAK,EAAE;EACnC,IAAIC,SAAS,GAAGD,KAAK,CAACC,SAAS;IAC7BC,KAAK,GAAGF,KAAK,CAACE,KAAK;IACnBC,KAAK,GAAGH,KAAK,CAACG,KAAK;IACnBC,QAAQ,GAAGJ,KAAK,CAACI,QAAQ;EAC3B,IAAIC,IAAI,GAAGF,KAAK,IAAI,CAAC,CAAC;IACpBG,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;EACxB,IAAIC,WAAW,GAAGJ,QAAQ,CAACK,CAAC;IAC1BA,CAAC,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;IAC5CE,WAAW,GAAGN,QAAQ,CAACO,CAAC;IACxBA,CAAC,GAAGD,WAAW,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,WAAW;EAC9C,IAAIE,QAAQ,GAAGd,KAAK,CAACe,MAAM,CAAC,CAAC;;EAE7B;EACA,IAAI,CAACX,KAAK,IAAI,CAACA,KAAK,CAACY,MAAM,EAAE;IAC3B,OAAO,IAAI;EACb;EACA,IAAIC,UAAU,GAAG;IACfC,QAAQ,EAAE;EACZ,CAAC;;EAED;EACA,IAAId,KAAK,CAACe,SAAS,KAAK,KAAK,EAAE;IAC7B,IAAIC,WAAW,GAAGhB,KAAK,CAACY,MAAM,CAAC,CAAC,CAAC;IACjC,IAAIK,YAAY,GAAGjB,KAAK,CAACY,MAAM,CAAC,CAAC,CAAC;IAClC,IAAIM,OAAO,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC5B,IAAIG,OAAO,GAAGH,WAAW,CAAC,CAAC,CAAC;IAC5B,IAAII,QAAQ,GAAGH,YAAY,CAAC,CAAC,CAAC;IAC9B,IAAII,QAAQ,GAAGJ,YAAY,CAAC,CAAC,CAAC;;IAE9B;IACA,IAAIC,OAAO,KAAKE,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAACE,QAAQ,CAACJ,OAAO,CAAC,EAAE;MACzDL,UAAU,CAACU,GAAG,GAAGd,CAAC;IACpB,CAAC,MAAM,IAAIS,OAAO,KAAK,GAAG,EAAE;MAC1BL,UAAU,CAACU,GAAG,GAAG,CAAC;IACpB,CAAC,MAAM;MACLV,UAAU,CAACW,MAAM,GAAG,CAAC;IACvB;;IAEA;IACA,IAAIL,OAAO,KAAKE,QAAQ,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,QAAQ,CAACH,OAAO,CAAC,EAAE;MACzDN,UAAU,CAACY,IAAI,GAAGlB,CAAC;IACrB,CAAC,MAAM,IAAIY,OAAO,KAAK,GAAG,EAAE;MAC1BN,UAAU,CAACY,IAAI,GAAG,CAAC;IACrB,CAAC,MAAM;MACLZ,UAAU,CAACa,KAAK,GAAG,CAAC;IACtB;EACF;EACA,OAAO,aAAa9B,KAAK,CAAC+B,aAAa,CAAC,KAAK,EAAE;IAC7CC,GAAG,EAAElB,QAAQ;IACbN,SAAS,EAAET,UAAU,CAAC,EAAE,CAACkC,MAAM,CAAC9B,SAAS,EAAE,QAAQ,CAAC,EAAEK,SAAS,CAAC;IAChE0B,KAAK,EAAEjB;EACT,CAAC,EAAER,OAAO,CAAC;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}